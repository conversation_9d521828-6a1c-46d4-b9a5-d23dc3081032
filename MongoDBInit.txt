

db.createCollection("books");
db.getCollection("books").insert({
	"title":"星际龙神",
	"alias":"星际战神",
	"author":"琴天",
	"cover":"bookcover/xjlp.jpg",
	"bookType": "aitranslate",
	"shortIntro": "shortIntro",
	"longIntro": "longIntro",
	"tags": ["male", "white_collar", "age20"],
	"categories": ["fantasy", "male_freq", "revenge"],
	"gender": ["male", "female"],
	"lastChapter": "Final chapter",
	"chaptersCount": 256,
	"isSerial": true,
	"created": ISODate("2025-05-02T13:07:17.012Z"),
	"updated": ISODate("2025-05-02T13:07:17.012Z"),
    "isEnded": false,
    "allowBeanVoucher": true,
    "allowVoucher": true,
    "allowMonthly": true,
    "allowFree": false,
    "allowLimitedFree": true,
    "contentType": "txt",
    "contentLevel": 0 //a:0 b:1 c:2 s:3 e:-1
})


db.getCollection("books").insert({
	"title":"斗罗宇宙",
	"alias":"卷心菜",
	"author":"xx",
	"cover":"bookcover/dlyz.jpg",
	"bookType": "aitranslate",
	"shortIntro": "不得不看",
	"longIntro": "看了上瘾",
	"tags": ["male", "white_collar", "age20"],
	"categories": ["fantasy", "male_freq", "revenge"],
	"gender": ["male", "female"],
	"lastChapter": "Final chapter",
	"chaptersCount": 556,
	"isSerial": true,
	"created": new Date(),
	"updated": new Date(),
    "isEnded": true,
    "allowBeanVoucher": true,
    "allowVoucher": true,
    "allowMonthly": true,
    "allowFree": true,
    "allowLimitedFree": true,
    "contentType": "txt",
    "contentLevel": 0,
})

db.getCollection("books").insert({
	"title":"雨中菜刀行",
	"alias":"",
	"author":"磨刀石",
	"cover":"bookcover/yzcdx.jpg",
	"bookType": "aitranslate",
	"shortIntro": "菜刀的励志故事",
	"longIntro": "不看后悔,看了才后悔",
	"tags": ["male", "white_collar", "age20"],
	"categories": ["fantasy", "male_freq", "revenge"],
	"gender": ["male", "female"],
	"lastChapter": "Final chapter",
	"chaptersCount": 888,
	"isSerial": true,
	"created": new Date(),
	"updated": new Date(),
    "isEnded": false,
    "allowBeanVoucher": true,
    "allowVoucher": true,
    "allowMonthly": true,
    "allowFree": true,
    "allowLimitedFree": true,
    "contentType": "txt",
    "contentLevel": 0,
})

db.getCollection("books").insert({
	"title":"凤舞九天",
	"alias":"",
	"author":"不死鸟",
	"cover":"bookcover/fwjt.jpg",
	"bookType": "aitranslate",
	"shortIntro": "凤凰涅槃",
	"longIntro": "身处绝境, 顽强重生, 再创辉煌",
	"tags": ["male", "white_collar", "age20"],
	"categories": ["fantasy", "male_freq", "revenge"],
	"gender": ["male", "female"],
	"lastChapter": "Final chapter",
	"chaptersCount": 1024,
	"isSerial": true,
	"created": new Date(),
	"updated": new Date(),
    "isEnded": false,
    "allowBeanVoucher": true,
    "allowVoucher": true,
    "allowMonthly": true,
    "allowFree": true,
    "allowLimitedFree": true,
    "contentType": "txt",
    "contentLevel": 0,
})

db.books.createIndex({"title":1}, {name: "books_idx_book_title"});
db.books.createIndex({"updated":1}, {name: "books_idx_update"});


db.createCollection("txtchapters")
db.txtchapters.createIndex({"_id": 1, "bookId":2, "order":3}, {name: "chapters_idx_chapter_book_order"},{unique:true});
db.txtchapters.createIndex({"bookId": 1, "order":2}, {name: "chapters_idx_book_order_In_chapters"},{unique:true});
db.txtchapters.createIndex({"bookId": 1, "title":2}, {name: "chapters_idx_book_title"},{unique:true});

db.getCollection("txtchapters").insert({
    "bookId":ObjectId("68185f28582200008e003f72"),
    "cp": "cptest1",
    "cpChapterId": "cpchapterId00001",
    "title": "序幕",
    "order": 1,
    "contentLength": 2000,
    "created": ISODate("2025-05-13T21:52:17.012Z"),
    "updated": ISODate("2025-05-13T22:52:17.012Z"),
    "publishAt": ISODate("2025-05-13T22:54:17.012Z"),
    "volume":"",
    "key": "testkey1",
    "content": "",
    "currency": 60,
    "version": 1,
    "fileName": "testchapter001.txt",
    "path": "68185f28582200008e003f72",
    "isFree": false,
    "isVip": true,
    "isMonthly": true,
    "IsLimitedFree": false,
    "bytes": 16,
})

db.getCollection("txtchapters").insert({
    "bookId":ObjectId("68185f28582200008e003f72"),
    "cp": "cptest1",
    "cpChapterId": "cpchapterId00002",
    "title": "大雪",
    "order": 2,
    "contentLength": 2143,
    "created": ISODate("2025-05-15T21:52:17.012Z"),
    "updated": ISODate("2025-05-15T22:52:17.012Z"),
    "publishAt": ISODate("2025-05-15T22:54:17.012Z"),
    "volume":"",
    "key": "testkey1",
    "content": "",
    "currency": 60,
    "version": 1,
    "fileName": "testchapter002.txt",
    "path": "68185f28582200008e003f72",
    "isFree": false,
    "isVip": true,
    "isMonthly": true,
    "IsLimitedFree": false,
    "bytes": 16,
})

db.getCollection("txtchapters").insert({
    "bookId":ObjectId("68185f28582200008e003f72"),
    "cp": "cptest1",
    "cpChapterId": "cpchapterId00002",
    "title": "深渊",
    "order": 3,
    "contentLength": 2222,
    "created": ISODate("2025-05-22T21:52:17.012Z"),
    "updated": ISODate("2025-05-22T22:52:17.012Z"),
    "publishAt": ISODate("2025-05-22T22:54:17.012Z"),
    "volume":"",
    "key": "testkey1",
    "content": "",
    "currency": 60,
    "version": 1,
    "fileName": "testchapter003.txt",
    "path": "68185f28582200008e003f72",
    "isFree": false,
    "isVip": true,
    "isMonthly": true,
    "IsLimitedFree": false,
    "bytes": 16,
})

db.getCollection("txtchapters").insert({
    "bookId":ObjectId("682d7aeba231000033004942"),
    "cp": "cptest1",
    "cpChapterId": "cpchapterId00001",
    "title": "序幕",
    "order": 1,
    "contentLength": 2000,
    "created": ISODate("2025-05-13T21:52:17.012Z"),
    "updated": ISODate("2025-05-13T22:52:17.012Z"),
    "publishAt": ISODate("2025-05-13T22:54:17.012Z"),
    "volume":"",
    "key": "testkey1",
    "content": "",
    "currency": 60,
    "version": 1,
    "fileName": "testchapter001.txt",
    "path": "682d7aeba231000033004942",
    "isFree": false,
    "isVip": true,
    "isMonthly": true,
    "IsLimitedFree": false,
    "bytes": 16,
})

db.getCollection("txtchapters").insert({
    "bookId":ObjectId("6828a1f47f520000ff006092"),
    "cp": "cptest1",
    "cpChapterId": "cpchapterId00002",
    "title": "找刀",
    "order": 1,
    "contentLength": 1388,
    "created": new Date(),
    "updated": new Date(),
    "publishAt": new Date(),
    "volume":"",
    "key": "testkey3",
    "content": "",
    "currency": 120,
    "version": 1,
    "fileName": "xzcdx_cp001.txt",
    "path": "6828a1f47f520000ff006092",
    "isFree": true,
    "isVip": true,
    "isMonthly": true,
    "IsLimitedFree": false,
    "bytes": 168,
})



