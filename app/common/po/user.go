package po

import "time"

// User 用户信息
type User struct {
	UserId      uint64     `db:"userId" json:"userId"`
	Phone       string     `db:"phone" json:"phone"`
	Email       string     `db:"email" json:"email"`
	Nickname    string     `db:"nickname" json:"nickname"`
	Avatar      string     `db:"avatar" json:"avatar"`
	Gender      int32      `db:"gender" json:"gender"`
	Birthday    string     `db:"birthday" json:"birthday"`
	Location    string     `db:"location" json:"location"`
	Status      int32      `db:"status" json:"status"`
	LoginType   int32      `db:"loginType" json:"loginType"`
	GoogleId    string     `db:"googleId" json:"googleId"`
	AppleId     string     `db:"appleId" json:"appleId"`
	LastLoginAt *time.Time `db:"lastLoginAt" json:"lastLoginAt"`
	LastLoginIp string     `db:"lastLoginIp" json:"lastLoginIp"`
	CreatedAt   time.Time  `db:"createdAt" json:"createdAt"`
	UpdatedAt   time.Time  `db:"updatedAt" json:"updatedAt"`
}

// LoginLog 登录日志
type LoginLog struct {
	LogId       uint64    `db:"logId" json:"logId"`
	UserId      uint64    `db:"userId" json:"userId"`
	LoginType   int32     `db:"loginType" json:"loginType"`
	LoginIp     string    `db:"loginIp" json:"loginIp"`
	UserAgent   string    `db:"userAgent" json:"userAgent"`
	DeviceId    string    `db:"deviceId" json:"deviceId"`
	LoginResult int32     `db:"loginResult" json:"loginResult"`
	FailReason  string    `db:"failReason" json:"failReason"`
	CreatedAt   time.Time `db:"createdAt" json:"createdAt"`
}

// SmsCode 短信验证码
type SmsCode struct {
	Id        uint64    `db:"id" json:"id"`
	Phone     string    `db:"phone" json:"phone"`
	Code      string    `db:"code" json:"code"`
	CodeType  int32     `db:"codeType" json:"codeType"`
	IsUsed    bool      `db:"isUsed" json:"isUsed"`
	ExpireAt  time.Time `db:"expireAt" json:"expireAt"`
	CreatedAt time.Time `db:"createdAt" json:"createdAt"`
}

// UserSession 用户会话
type UserSession struct {
	SessionId string    `db:"sessionId" json:"sessionId"`
	UserId    uint64    `db:"userId" json:"userId"`
	Token     string    `db:"token" json:"token"`
	DeviceId  string    `db:"deviceId" json:"deviceId"`
	UserAgent string    `db:"userAgent" json:"userAgent"`
	LoginIp   string    `db:"loginIp" json:"loginIp"`
	ExpireAt  time.Time `db:"expireAt" json:"expireAt"`
	CreatedAt time.Time `db:"createdAt" json:"createdAt"`
	UpdatedAt time.Time `db:"updatedAt" json:"updatedAt"`
}
