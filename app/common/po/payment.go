package po

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

// PaymentOrder 支付订单
type PaymentOrder struct {
	ID            primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	OrderId       string             `bson:"orderId" json:"orderId"`
	UserId        uint64             `bson:"userId" json:"userId"`
	PaymentMethod int32              `bson:"paymentMethod" json:"paymentMethod"`
	PaymentType   int32              `bson:"paymentType" json:"paymentType"`
	Amount        int64              `bson:"amount" json:"amount"`
	Currency      string             `bson:"currency" json:"currency"`
	ProductId     string             `bson:"productId" json:"productId"`
	ProductName   string             `bson:"productName" json:"productName"`
	Description   string             `bson:"description" json:"description"`
	Status        int32              `bson:"status" json:"status"`
	TransactionId string             `bson:"transactionId" json:"transactionId"`
	Metadata      map[string]string  `bson:"metadata" json:"metadata"`
	ClientIp      string             `bson:"clientIp" json:"clientIp"`
	UserAgent     string             `bson:"userAgent" json:"userAgent"`
	ReturnUrl     string             `bson:"returnUrl" json:"returnUrl"`
	CancelUrl     string             `bson:"cancelUrl" json:"cancelUrl"`
	PaymentUrl    string             `bson:"paymentUrl" json:"paymentUrl"`
	PaymentData   string             `bson:"paymentData" json:"paymentData"`
	FailureReason string             `bson:"failureReason" json:"failureReason"`
	CreatedAt     time.Time          `bson:"createdAt" json:"createdAt"`
	UpdatedAt     time.Time          `bson:"updatedAt" json:"updatedAt"`
	PaidAt        *time.Time         `bson:"paidAt,omitempty" json:"paidAt,omitempty"`
	ExpiredAt     time.Time          `bson:"expiredAt" json:"expiredAt"`
}

// PaymentCallback 支付回调记录
type PaymentCallback struct {
	ID            primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	OrderId       string             `bson:"orderId" json:"orderId"`
	PaymentMethod int32              `bson:"paymentMethod" json:"paymentMethod"`
	TransactionId string             `bson:"transactionId" json:"transactionId"`
	Status        int32              `bson:"status" json:"status"`
	Amount        int64              `bson:"amount" json:"amount"`
	Currency      string             `bson:"currency" json:"currency"`
	CallbackData  map[string]string  `bson:"callbackData" json:"callbackData"`
	Signature     string             `bson:"signature" json:"signature"`
	Processed     bool               `bson:"processed" json:"processed"`
	ProcessedAt   *time.Time         `bson:"processedAt,omitempty" json:"processedAt,omitempty"`
	CreatedAt     time.Time          `bson:"createdAt" json:"createdAt"`
}

// PaymentRefund 退款记录
type PaymentRefund struct {
	ID            primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	RefundId      string             `bson:"refundId" json:"refundId"`
	OrderId       string             `bson:"orderId" json:"orderId"`
	UserId        uint64             `bson:"userId" json:"userId"`
	PaymentMethod int32              `bson:"paymentMethod" json:"paymentMethod"`
	RefundAmount  int64              `bson:"refundAmount" json:"refundAmount"`
	Currency      string             `bson:"currency" json:"currency"`
	Reason        string             `bson:"reason" json:"reason"`
	Status        int32              `bson:"status" json:"status"`
	TransactionId string             `bson:"transactionId" json:"transactionId"`
	RefundData    map[string]string  `bson:"refundData" json:"refundData"`
	CreatedAt     time.Time          `bson:"createdAt" json:"createdAt"`
	UpdatedAt     time.Time          `bson:"updatedAt" json:"updatedAt"`
	ProcessedAt   *time.Time         `bson:"processedAt,omitempty" json:"processedAt,omitempty"`
}

// PaymentMethod 支付方式配置
type PaymentMethodConfig struct {
	ID                  primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	Method              int32              `bson:"method" json:"method"`
	Name                string             `bson:"name" json:"name"`
	DisplayName         string             `bson:"displayName" json:"displayName"`
	Icon                string             `bson:"icon" json:"icon"`
	Enabled             bool               `bson:"enabled" json:"enabled"`
	SupportedCurrencies []string           `bson:"supportedCurrencies" json:"supportedCurrencies"`
	SupportedPlatforms  []string           `bson:"supportedPlatforms" json:"supportedPlatforms"`
	SupportedRegions    []string           `bson:"supportedRegions" json:"supportedRegions"`
	Config              map[string]string  `bson:"config" json:"config"`
	CreatedAt           time.Time          `bson:"createdAt" json:"createdAt"`
	UpdatedAt           time.Time          `bson:"updatedAt" json:"updatedAt"`
}

// PaymentProduct 支付产品配置
type PaymentProduct struct {
	ID          primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	ProductId   string             `bson:"productId" json:"productId"`
	ProductName string             `bson:"productName" json:"productName"`
	ProductType int32              `bson:"productType" json:"productType"`
	Amount      int64              `bson:"amount" json:"amount"`
	Currency    string             `bson:"currency" json:"currency"`
	CoinAmount  int64              `bson:"coinAmount" json:"coinAmount"`
	VipDays     int32              `bson:"vipDays" json:"vipDays"`
	MonthlyDays int32              `bson:"monthlyDays" json:"monthlyDays"`
	Description string             `bson:"description" json:"description"`
	Enabled     bool               `bson:"enabled" json:"enabled"`
	SortOrder   int32              `bson:"sortOrder" json:"sortOrder"`
	CreatedAt   time.Time          `bson:"createdAt" json:"createdAt"`
	UpdatedAt   time.Time          `bson:"updatedAt" json:"updatedAt"`
}

// PaymentLog 支付日志
type PaymentLog struct {
	ID        primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	OrderId   string             `bson:"orderId" json:"orderId"`
	UserId    uint64             `bson:"userId" json:"userId"`
	Action    string             `bson:"action" json:"action"`
	Status    string             `bson:"status" json:"status"`
	Message   string             `bson:"message" json:"message"`
	Data      map[string]string  `bson:"data" json:"data"`
	CreatedAt time.Time          `bson:"createdAt" json:"createdAt"`
}

// 支付方式常量
const (
	PaymentMethodUnknown   = 0
	PaymentMethodGooglePay = 1
	PaymentMethodApplePay  = 2
	PaymentMethodPaypal    = 3
	PaymentMethodAlipay    = 4
	PaymentMethodWechatPay = 5
)

// 支付类型常量
const (
	PaymentTypeUnknown             = 0
	PaymentTypeVipPurchase         = 1
	PaymentTypeMonthlySubscription = 2
	PaymentTypeCoinRecharge        = 3
)

// 订单状态常量
const (
	OrderStatusUnknown   = 0
	OrderStatusPending   = 1
	OrderStatusPaid      = 2
	OrderStatusCancelled = 3
	OrderStatusRefunded  = 4
	OrderStatusFailed    = 5
)

// 退款状态常量
const (
	RefundStatusPending   = 1
	RefundStatusSuccess   = 2
	RefundStatusFailed    = 3
	RefundStatusCancelled = 4
)
