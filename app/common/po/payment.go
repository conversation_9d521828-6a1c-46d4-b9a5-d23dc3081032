package po

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
	"time"
)

// PaymentOrder 支付订单
type PaymentOrder struct {
	ID            primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	OrderId       string             `bson:"orderId" json:"orderId"`
	UserId        uint64             `bson:"userId" json:"userId"`
	PaymentMethod int32              `bson:"paymentMethod" json:"paymentMethod"`
	PaymentType   int32              `bson:"paymentType" json:"paymentType"`
	Amount        int64              `bson:"amount" json:"amount"`
	Currency      string             `bson:"currency" json:"currency"`
	ProductId     string             `bson:"productId" json:"productId"`
	ProductName   string             `bson:"productName" json:"productName"`
	Description   string             `bson:"description" json:"description"`
	Status        int32              `bson:"status" json:"status"`
	TransactionId string             `bson:"transactionId" json:"transactionId"`
	Metadata      map[string]string  `bson:"metadata" json:"metadata"`
	ClientIp      string             `bson:"clientIp" json:"clientIp"`
	UserAgent     string             `bson:"userAgent" json:"userAgent"`
	ReturnUrl     string             `bson:"returnUrl" json:"returnUrl"`
	CancelUrl     string             `bson:"cancelUrl" json:"cancelUrl"`
	PaymentUrl    string             `bson:"paymentUrl" json:"paymentUrl"`
	PaymentData   string             `bson:"paymentData" json:"paymentData"`
	FailureReason string             `bson:"failureReason" json:"failureReason"`
	CreatedAt     time.Time          `bson:"createdAt" json:"createdAt"`
	UpdatedAt     time.Time          `bson:"updatedAt" json:"updatedAt"`
	PaidAt        *time.Time         `bson:"paidAt,omitempty" json:"paidAt,omitempty"`
	ExpiredAt     time.Time          `bson:"expiredAt" json:"expiredAt"`
}

// PaymentCallback 支付回调记录
type PaymentCallback struct {
	ID            primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	OrderId       string             `bson:"orderId" json:"orderId"`
	PaymentMethod int32              `bson:"paymentMethod" json:"paymentMethod"`
	TransactionId string             `bson:"transactionId" json:"transactionId"`
	Status        int32              `bson:"status" json:"status"`
	Amount        int64              `bson:"amount" json:"amount"`
	Currency      string             `bson:"currency" json:"currency"`
	CallbackData  map[string]string  `bson:"callbackData" json:"callbackData"`
	Signature     string             `bson:"signature" json:"signature"`
	Processed     bool               `bson:"processed" json:"processed"`
	ProcessedAt   *time.Time         `bson:"processedAt,omitempty" json:"processedAt,omitempty"`
	CreatedAt     time.Time          `bson:"createdAt" json:"createdAt"`
}

// PaymentRefund 退款记录
type PaymentRefund struct {
	ID            primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	RefundId      string             `bson:"refundId" json:"refundId"`
	OrderId       string             `bson:"orderId" json:"orderId"`
	UserId        uint64             `bson:"userId" json:"userId"`
	PaymentMethod int32              `bson:"paymentMethod" json:"paymentMethod"`
	RefundAmount  int64              `bson:"refundAmount" json:"refundAmount"`
	Currency      string             `bson:"currency" json:"currency"`
	Reason        string             `bson:"reason" json:"reason"`
	Status        int32              `bson:"status" json:"status"`
	TransactionId string             `bson:"transactionId" json:"transactionId"`
	RefundData    map[string]string  `bson:"refundData" json:"refundData"`
	CreatedAt     time.Time          `bson:"createdAt" json:"createdAt"`
	UpdatedAt     time.Time          `bson:"updatedAt" json:"updatedAt"`
	ProcessedAt   *time.Time         `bson:"processedAt,omitempty" json:"processedAt,omitempty"`
}

// PaymentMethod 支付方式配置
type PaymentMethodConfig struct {
	ID                  int64             `json:"id" db:"id"`
	Method              int32             `json:"method" db:"method"`
	Name                string            `json:"name" db:"name"`
	DisplayName         string            `json:"displayName" db:"display_name"`
	Icon                string            `json:"icon" db:"icon"`
	Enabled             bool              `json:"enabled" db:"enabled"`
	SupportedCurrencies []string          `json:"supportedCurrencies" db:"supported_currencies"`
	SupportedPlatforms  []string          `json:"supportedPlatforms" db:"supported_platforms"`
	SupportedRegions    []string          `json:"supportedRegions" db:"supported_regions"`
	Config              map[string]string `json:"config" db:"config"`
	SortOrder           int32             `json:"sortOrder" db:"sort_order"`
	CreatedAt           time.Time         `json:"createdAt" db:"created_at"`
	UpdatedAt           time.Time         `json:"updatedAt" db:"updated_at"`
}

// PaymentProduct 支付产品配置
type PaymentProduct struct {
	ID          int64     `json:"id" db:"id"`
	ProductId   string    `json:"productId" db:"product_id"`
	ProductName string    `json:"productName" db:"product_name"`
	ProductType int32     `json:"productType" db:"product_type"`
	Price       float32   `json:"price" db:"price"`
	Currency    string    `json:"currency" db:"currency"`
	CoinAmount  float64   `json:"coinAmount" db:"coin_amount"`
	VipDays     int32     `json:"vipDays" db:"vip_days"`
	Description string    `json:"description" db:"description"`
	Enabled     bool      `json:"enabled" db:"enabled"`
	SortOrder   int32     `json:"sortOrder" db:"sort_order"`
	CreatedAt   time.Time `json:"createdAt" db:"created_at"`
	UpdatedAt   time.Time `json:"updatedAt" db:"updated_at"`
}

// PaymentLog 支付日志
type PaymentLog struct {
	ID        primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	OrderId   string             `bson:"orderId" json:"orderId"`
	UserId    uint64             `bson:"userId" json:"userId"`
	Action    string             `bson:"action" json:"action"`
	Status    string             `bson:"status" json:"status"`
	Message   string             `bson:"message" json:"message"`
	Data      map[string]string  `bson:"data" json:"data"`
	CreatedAt time.Time          `bson:"createdAt" json:"createdAt"`
}

// 支付方式常量
const (
	PaymentMethodUnknown   = 0
	PaymentMethodGooglePay = 1
	PaymentMethodApplePay  = 2
	PaymentMethodPaypal    = 3
	PaymentMethodAlipay    = 4
	PaymentMethodWechatPay = 5
)

// 支付类型常量
const (
	PaymentTypeUnknown             = 0
	PaymentTypeVipPurchase         = 1
	PaymentTypeMonthlySubscription = 2
	PaymentTypeCoinRecharge        = 3
)

// 订单状态常量
const (
	OrderStatusUnknown   = 0
	OrderStatusPending   = 1
	OrderStatusPaid      = 2
	OrderStatusCancelled = 3
	OrderStatusRefunded  = 4
	OrderStatusFailed    = 5
)

// 退款状态常量
const (
	RefundStatusPending   = 1
	RefundStatusSuccess   = 2
	RefundStatusFailed    = 3
	RefundStatusCancelled = 4
)
