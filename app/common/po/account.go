package po

import (
	"creativematrix.com/beyondreading/app/common"
	"strconv"
	"strings"
	"time"
)

// Account 账户信息
type Account struct {
	AccountId         uint64     `db:"accountId" json:"accountId"`
	UserId            uint64     `db:"userId" json:"userId"`
	CoinBalance       float64    `db:"coinBalance" json:"coinBalance"`
	TotalRecharged    float64    `db:"totalRecharged" json:"totalRecharged"`
	TotalConsumed     float64    `db:"totalConsumed" json:"totalConsumed"`
	Status            int32      `db:"status" json:"status"`
	UserType          int32      `db:"userType" json:"userType"`
	UserLevel         int32      `db:"userLevel" json:"userLevel"`
	VipExpireTime     *time.Time `db:"vipExpireTime" json:"vipExpireTime"`
	MonthlyExpireTime *time.Time `db:"monthlyExpireTime" json:"monthlyExpireTime"`
	CreatedAt         time.Time  `db:"createdAt" json:"createdAt"`
	UpdatedAt         time.Time  `db:"updatedAt" json:"updatedAt"`
}

// AccountLog 账户日志
type AccountLog struct {
	LogId           uint64    `db:"logId" json:"logId"`
	AccountId       uint64    `db:"accountId" json:"accountId"`
	UserId          uint64    `db:"userId" json:"userId"`
	TransactionType string    `db:"transactionType" json:"transactionType"`
	Amount          float64   `db:"amount" json:"amount"`
	BalanceBefore   float64   `db:"balanceBefore" json:"balanceBefore"`
	BalanceAfter    float64   `db:"balanceAfter" json:"balanceAfter"`
	OrderId         string    `db:"orderId" json:"orderId"`
	BookId          string    `db:"bookId" json:"bookId"`
	ChapterId       string    `db:"chapterId" json:"chapterId"`
	Description     string    `db:"description" json:"description"`
	ExtraData       string    `db:"extraData" json:"extraData"`
	CreatedAt       time.Time `db:"createdAt" json:"createdAt"`
}

// RechargeOrder 充值订单
type RechargeOrder struct {
	OrderId        string     `db:"orderId" json:"orderId"`
	AccountId      uint64     `db:"accountId" json:"accountId"`
	UserId         uint64     `db:"userId" json:"userId"`
	Amount         float64    `db:"amount" json:"amount"`
	CoinAmount     float64    `db:"coinAmount" json:"coinAmount"`
	ExchangeRate   float32    `db:"exchangeRate" json:"exchangeRate"`
	PaymentMethod  string     `db:"paymentMethod" json:"paymentMethod"`
	PaymentOrderId string     `db:"paymentOrderId" json:"paymentOrderId"`
	Status         int32      `db:"status" json:"status"`
	PaidAt         *time.Time `db:"paidAt" json:"paidAt"`
	CreatedAt      time.Time  `db:"createdAt" json:"createdAt"`
	UpdatedAt      time.Time  `db:"updatedAt" json:"updatedAt"`
}

// PurchaseOrder 购买订单（章节）
type PurchaseOrder struct {
	OrderId      string    `db:"orderId" json:"orderId"`
	AccountId    uint64    `db:"accountId" json:"accountId"`
	UserId       uint64    `db:"userId" json:"userId"`
	OrderType    string    `db:"orderType" json:"orderType"`
	BookId       string    `db:"bookId" json:"bookId"`
	BookName     string    `db:"bookName" json:"bookName"`
	ChapterId    string    `db:"chapterId" json:"chapterId"`
	ChapterTitle string    `db:"chapterTitle" json:"chapterTitle"`
	ChapterOrder uint32    `db:"chapterOrder" json:"chapterOrder"`
	CoinAmount   float64   `db:"coinAmount" json:"coinAmount"`
	Status       int32     `db:"status" json:"status"`
	CreatedAt    time.Time `db:"createdAt" json:"createdAt"`
	UpdatedAt    time.Time `db:"updatedAt" json:"updatedAt"`
}

// VipMonthlyOrder VIP/包月订单
type VipMonthlyOrder struct {
	OrderId      string     `db:"orderId" json:"orderId"`
	AccountId    uint64     `db:"accountId" json:"accountId"`
	UserId       uint64     `db:"userId" json:"userId"`
	OrderType    string     `db:"orderType" json:"orderType"`
	CoinAmount   float64    `db:"coinAmount" json:"coinAmount"`
	DurationDays int32      `db:"durationDays" json:"durationDays"`
	StartTime    *time.Time `db:"startTime" json:"startTime"`
	EndTime      *time.Time `db:"endTime" json:"endTime"`
	Status       int32      `db:"status" json:"status"`
	CreatedAt    time.Time  `db:"createdAt" json:"createdAt"`
	UpdatedAt    time.Time  `db:"updatedAt" json:"updatedAt"`
}

// ChapterPurchaseInfo 章节购买信息
type ChapterPurchaseInfo struct {
	OrderId      string    `json:"orderId"`
	ChapterId    string    `json:"chapterId"`
	ChapterTitle string    `json:"chapterTitle"`
	ChapterOrder uint32    `json:"chapterOrder"`
	CoinAmount   float64   `json:"coinAmount"`
	PurchasedAt  time.Time `json:"purchasedAt"`
	IsMonthly    bool      `json:"isMonthly"`
	IsVip        bool      `json:"isVip"`
}

// ChapterPurchaseItem 批量章节购买项
type ChapterPurchaseItem struct {
	ChapterOrder uint32  `json:"chapterOrder"`
	CoinAmount   float64 `json:"coinAmount"`
}

// GetUserLevel 根据消费金额计算用户等级
func GetUserLevel(totalConsumed string) int {
	if totalConsumed == "" || totalConsumed == "0" || totalConsumed == "0.00" {
		return 1 // 默认1级
	}

	// 解析消费金额
	consumed, err := ParseDecimalString(totalConsumed)
	if err != nil {
		return 1 // 解析失败返回1级
	}

	// 计算等级：消费书币数量 / 1000 + 1
	level := int(consumed/common.CoinsPerLevel) + 1

	// 确保等级至少为1
	if level < 1 {
		level = 1
	}

	return level
}

// ParseDecimalString 简单的decimal字符串解析（导出函数）
func ParseDecimalString(s string) (float64, error) {
	// 移除可能的空格
	s = strings.TrimSpace(s)
	if s == "" {
		return 0, nil
	}

	// 解析为float64
	value, err := strconv.ParseFloat(s, 64)
	if err != nil {
		return 0, err
	}

	return value, nil
}

// IsVipActive 检查VIP是否有效
func (a *Account) IsVipActive() bool {
	if a.VipExpireTime == nil {
		return false
	}
	return a.VipExpireTime.After(time.Now())
}

// TableName 返回表名
func (Account) TableName() string {
	return "account"
}

func (AccountLog) TableName() string {
	return "account_log"
}

func (RechargeOrder) TableName() string {
	return "recharge_order"
}

func (PurchaseOrder) TableName() string {
	return "purchase_order"
}
