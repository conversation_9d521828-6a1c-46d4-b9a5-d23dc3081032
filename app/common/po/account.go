package po

import (
	"creativematrix.com/beyondreading/app/common"
	"strconv"
	"strings"
	"time"
)

// Account 账户表
type Account struct {
	AccountId         uint64     `db:"account_id" json:"account_id"`
	UserId            uint64     `db:"user_id" json:"user_id"`
	CoinBalance       float64    `db:"coin_balance" json:"coin_balance"`               // 书币余额
	TotalRecharged    float64    `db:"total_recharged" json:"total_recharged"`         // 总充值金额
	TotalConsumed     float64    `db:"total_consumed" json:"total_consumed"`           // 总消费金额
	Status            int32      `db:"status" json:"status"`                           // 账户状态：1-正常，2-冻结，3-注销
	UserType          int32      `db:"user_type" json:"user_type"`                     // 用户类型：1-普通用户，2-VIP用户，3-包月用户
	UserLevel         int32      `db:"user_level" json:"user_level"`                   // 用户等级（根据消费书币计算，1000书币一级）
	VipExpireTime     *time.Time `db:"vip_expire_time" json:"vip_expire_time"`         // VIP过期时间
	MonthlyExpireTime *time.Time `db:"monthly_expire_time" json:"monthly_expire_time"` // 包月过期时间
	CreatedAt         time.Time  `db:"created_at" json:"created_at"`
	UpdatedAt         time.Time  `db:"updated_at" json:"updated_at"`
}

// AccountLog 账户日志表
type AccountLog struct {
	LogId           uint64    `db:"log_id" json:"log_id"`
	AccountId       uint64    `db:"account_id" json:"account_id"`
	UserId          uint64    `db:"user_id" json:"user_id"`
	TransactionType string    `db:"transaction_type" json:"transaction_type"` // 交易类型
	Amount          float64   `db:"amount" json:"amount"`                     // 变动金额
	BalanceBefore   float64   `db:"balance_before" json:"balance_before"`     // 变动前余额
	BalanceAfter    float64   `db:"balance_after" json:"balance_after"`       // 变动后余额
	OrderId         *string   `db:"order_id" json:"order_id"`                 // 关联订单ID
	BookId          *string   `db:"book_id" json:"book_id"`                   // 书籍ID
	ChapterId       *string   `db:"chapter_id" json:"chapter_id"`             // 章节ID
	Description     *string   `db:"description" json:"description"`           // 描述
	ExtraData       *string   `db:"extra_data" json:"extra_data"`             // 额外数据（JSON格式）
	CreatedAt       time.Time `db:"created_at" json:"created_at"`
}

// RechargeOrder 充值订单表
type RechargeOrder struct {
	OrderId        string     `db:"order_id" json:"order_id"`
	AccountId      uint64     `db:"account_id" json:"account_id"`
	UserId         uint64     `db:"user_id" json:"user_id"`
	Amount         float64    `db:"amount" json:"amount"`                     // 充值金额（人民币）
	CoinAmount     float64    `db:"coin_amount" json:"coin_amount"`           // 获得书币数量
	ExchangeRate   float32    `db:"exchange_rate" json:"exchange_rate"`       // 兑换比例
	PaymentMethod  string     `db:"payment_method" json:"payment_method"`     // 支付方式
	PaymentOrderId *string    `db:"payment_order_id" json:"payment_order_id"` // 第三方支付订单ID
	Status         int32      `db:"status" json:"status"`                     // 订单状态
	PaidAt         *time.Time `db:"paid_at" json:"paid_at"`                   // 支付时间
	CreatedAt      time.Time  `db:"created_at" json:"created_at"`
	UpdatedAt      time.Time  `db:"updated_at" json:"updated_at"`
}

// PurchaseOrder 购买订单表
type PurchaseOrder struct {
	OrderId      string     `db:"order_id" json:"order_id"`
	AccountId    uint64     `db:"account_id" json:"account_id"`
	UserId       uint64     `db:"user_id" json:"user_id"`
	OrderType    string     `db:"order_type" json:"order_type"`       // 订单类型：chapter, monthly, vip
	BookId       *string    `db:"book_id" json:"book_id"`             // 书籍ID
	BookName     *string    `db:"book_name" json:"book_name"`         // 书籍名称
	ChapterId    *string    `db:"chapter_id" json:"chapter_id"`       // 章节ID
	ChapterTitle *string    `db:"chapter_title" json:"chapter_title"` // 章节标题
	ChapterOrder *uint32    `db:"chapter_order" json:"chapter_order"` // 章节序号
	CoinAmount   float64    `db:"coin_amount" json:"coin_amount"`     // 消费书币数量
	DurationDays *int32     `db:"duration_days" json:"duration_days"` // 有效期天数
	StartTime    *time.Time `db:"start_time" json:"start_time"`       // 开始时间
	EndTime      *time.Time `db:"end_time" json:"end_time"`           // 结束时间
	Status       int32      `db:"status" json:"status"`               // 订单状态
	CreatedAt    time.Time  `db:"created_at" json:"created_at"`
	UpdatedAt    time.Time  `db:"updated_at" json:"updated_at"`
}

// VipMonthlyOrder VIP/包月订单
type VipMonthlyOrder struct {
	OrderId      string     `db:"order_id" json:"order_id"`
	AccountId    uint64     `db:"account_id" json:"account_id"`
	UserId       uint64     `db:"user_id" json:"user_id"`
	OrderType    string     `db:"order_type" json:"order_type"`
	CoinAmount   float64    `db:"coin_amount" json:"coin_amount"`
	DurationDays int32      `db:"duration_days" json:"duration_days"`
	StartTime    *time.Time `db:"start_time" json:"start_time"`
	EndTime      *time.Time `db:"end_time" json:"end_time"`
	Status       int32      `db:"status" json:"status"`
	CreatedAt    time.Time  `db:"created_at" json:"created_at"`
	UpdatedAt    time.Time  `db:"updated_at" json:"updated_at"`
}

// ChapterPurchaseInfo 章节购买信息
type ChapterPurchaseInfo struct {
	OrderId      string    `json:"order_id"`
	ChapterId    string    `json:"chapter_id"`
	ChapterTitle string    `json:"chapter_title"`
	ChapterOrder uint32    `json:"chapter_order"`
	CoinAmount   float64   `json:"coin_amount"`
	PurchasedAt  time.Time `json:"purchased_at"`
	IsMonthly    bool      `json:"is_monthly"`
	IsVip        bool      `json:"is_vip"`
}

// ChapterPurchaseItem 批量章节购买项
type ChapterPurchaseItem struct {
	ChapterOrder uint32  `json:"chapter_order"`
	CoinAmount   float64 `json:"coin_amount"`
}

// GetUserLevel 根据消费金额计算用户等级
func GetUserLevel(totalConsumed string) int {
	if totalConsumed == "" || totalConsumed == "0" || totalConsumed == "0.00" {
		return 1 // 默认1级
	}

	// 解析消费金额
	consumed, err := ParseDecimalString(totalConsumed)
	if err != nil {
		return 1 // 解析失败返回1级
	}

	// 计算等级：消费书币数量 / 1000 + 1
	level := int(consumed/common.CoinsPerLevel) + 1

	// 确保等级至少为1
	if level < 1 {
		level = 1
	}

	return level
}

// ParseDecimalString 简单的decimal字符串解析（导出函数）
func ParseDecimalString(s string) (float64, error) {
	// 移除可能的空格
	s = strings.TrimSpace(s)
	if s == "" {
		return 0, nil
	}

	// 解析为float64
	value, err := strconv.ParseFloat(s, 64)
	if err != nil {
		return 0, err
	}

	return value, nil
}

// IsVipActive 检查VIP是否有效
func (a *Account) IsVipActive() bool {
	if a.VipExpireTime == nil {
		return false
	}
	return a.VipExpireTime.After(time.Now())
}

// TableName 返回表名
func (Account) TableName() string {
	return "account"
}

func (AccountLog) TableName() string {
	return "account_log"
}

func (RechargeOrder) TableName() string {
	return "recharge_order"
}

func (PurchaseOrder) TableName() string {
	return "purchase_order"
}
