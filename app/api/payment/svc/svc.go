package svc

import (
	"context"

	"creativematrix.com/beyondreading/app/api/payment/conf"
	"creativematrix.com/beyondreading/app/api/payment/dao"
)

type Svc struct {
	conf *conf.Config
	dao  *dao.Dao
}

func Load(c *conf.Config) *Svc {
	svc := &Svc{
		conf: c,
		dao:  dao.Load(c),
	}
	return svc
}

func (s *Svc) Ping(ctx context.Context) error {
	return s.dao.Ping(ctx)
}

func (s *Svc) Close() {
	s.dao.Close()
}
