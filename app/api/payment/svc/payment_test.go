package svc

import (
	"context"
	"creativematrix.com/beyondreading/app/api/payment/conf"
	"creativematrix.com/beyondreading/app/api/payment/model/vo"
	"creativematrix.com/beyondreading/pkg/logger"
	"fmt"
	"github.com/BurntSushi/toml"
	"gotest.tools/assert"
	"testing"
)

func doInit() *Svc {
	app := "api-payment"
	cf, err := loadConf(app)
	if err != nil {
		panic(err)
	}

	logger.InitLog(app, cf.Log.Level)
	paymentSvc := Load(cf)

	return paymentSvc
}

func loadConf(app string) (*conf.Config, error) {
	cf := &conf.Config{}
	if _, err := toml.DecodeFile("../../../base.toml", cf); err != nil {
		return nil, err
	}
	if _, err := toml.DecodeFile(fmt.Sprintf("../cmd/%s.toml", app), cf); err != nil {
		return nil, err
	}
	return cf, nil
}

func TestSvc_GetProducts(t *testing.T) {
	svc := doInit()

	req := &vo.GetProductReq{
		Platform: "android",
	}

	got, err := svc.GetProducts(context.Background(), req)
	if err != nil {
		logger.LogErrorf("Failed to create payment order: %v", err)
		return
	}

	for _, v := range got.Products {
		logger.LogInfof("Platform:%s , products:%v", req.Platform, v)
	}

	assert.NilError(t, err)
}
