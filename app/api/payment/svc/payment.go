package svc

import (
	"context"
	"creativematrix.com/beyondreading/app/api/payment/model/vo"
	"creativematrix.com/beyondreading/app/common"
	"creativematrix.com/beyondreading/app/common/po"
	"errors"
	"fmt"
	"time"

	"creativematrix.com/beyondreading/pkg/logger"

	"creativematrix.com/beyondreading/pkg/utils"

	"creativematrix.com/beyondreading/app/api/payment/model"
	"creativematrix.com/beyondreading/app/api/payment/pay"
)

func (s *Svc) GetProducts(ctx context.Context, pp *vo.GetProductReq) (*vo.GetProductResp, error) {
	products, err := s.dao.GetPaymentProducts(ctx, &pp.Platform)
	if err != nil {
		if errors.Is(err, model.ErrNoDocuments) {
			return nil, model.ProductNotFound
		}
		return nil, err
	}

	productsVo := make([]*vo.Product, len(products))

	for _, p := range products {
		product := &vo.Product{}
		if err := utils.JsonCopy(p, product); err != nil {
			logger.LogErrorf("failed to copy product:%s", err.Error())
		}
		productsVo = append(productsVo, product)
	}

	resp := &vo.GetProductResp{
		RetCode:  0,
		Msg:      "success",
		Products: productsVo,
	}
	return resp, nil
}

func (s *Svc) CreateOrder(ctx context.Context, req *vo.CreatePaymentOrderReq) (*vo.CreatePaymentOrderResp, error) {
	// 获取支付平台
	payment := pay.Get(req.PaymentMethod)
	if payment == nil {
		return nil, model.PaymentNotFound
	}

	// 获取商品信息
	_, err := s.dao.GetPaymentProductById(ctx, req.ProductId)
	if err != nil {
		return nil, model.ProductNotFound
	}

	// 获取用户信息
	userInfo, err := s.dao.GetUserInfoById(ctx, req.UserId)
	if err != nil {
		return nil, model.AccountNotFound
	}

	// 封禁用户不能生成订单
	if userInfo.Status != common.UserStatusNormal {
		return nil, model.UserError
	}

	//// 预警是否多比订单未支付告警
	//err = s.PayDingTalk(userInfo.NoId, userInfo.Id, op.SpbillCreateIp)
	//if err != nil {
	//	logger.LogErrorf("payDingTalk Err %s (%v)", userInfo.NoId, err)
	//}

	orderId := fmt.Sprintf("PAY%d%d%d", req.UserId, req.ProductId, time.Now().UnixMilli())

	// 创建支付订单
	order := &po.PaymentOrder{
		OrderId:       orderId,
		UserId:        req.UserId,
		PaymentMethod: req.PaymentMethod,
		PaymentType:   int32(req.PaymentType),
		Amount:        req.Amount,
		Currency:      req.Currency,
		ProductId:     req.ProductId,
		ProductName:   req.ProductName,
		Description:   req.Description,
		Status:        po.OrderStatusPending,
		Metadata:      req.Metadata,
		ClientIp:      req.ClientIp,
		UserAgent:     req.UserAgent,
		ReturnUrl:     req.ReturnUrl,
		CancelUrl:     req.CancelUrl,
	}

	// 生成 payment 数据
	payOrder, err := payment.Pay(order)
	if err != nil {
		return nil, err
	}

	// 生成 chargeorder 数据
	err = s.dao.CreatePaymentOrder(ctx, order)
	if err != nil {
		logger.LogErrorf("Failed to create payment order: %v", err)
		return nil, err
	}

	orderRes := &vo.CreatePaymentOrderResp{}
	orderRes.OrderId = order.OrderId
	orderRes.Type = payOrder.Type
	orderRes.Value = payOrder.Value

	return orderRes, nil
}

//
//func (s *Svc) PayDingTalk(noId, accountId, ip string) error {
//	count, err := s.dao.GetNoPayCountByAccount(accountId)
//	if err != nil {
//		return err
//	}
//
//	if count >= s.conf.DingPay.DingTime {
//		id, _ := primitive.ObjectIDFromHex(accountId)
//		var dingToken = []string{s.conf.DingPay.DingPayToken}
//		cli := dingtalk.InitDingTalk(dingToken, s.conf.DingPay.DingPayKey)
//		dm := dingtalk.DingMap()
//		dm.Set(fmt.Sprintf("用户ID %s", noId), dingtalk.N)
//		dm.Set(fmt.Sprintf("注册时间 %s", id.Timestamp().Add(+time.Hour*8).Format("2006-01-02 15:04:05")), dingtalk.N)
//		dm.Set(fmt.Sprintf("发起IP %s", ip), dingtalk.N)
//		dm.Set(fmt.Sprintf("%d 分钟发起 %d 次数未支付", 60, count), dingtalk.N)
//
//		return cli.SendMarkDownMessageBySlice("预警", dm.Slice(), dingtalk.WithAtAll())
//	}
//	return nil
//}

//func (s *Svc) Notify(ctx context.Context, w http.ResponseWriter, req *http.Request, pType model.PaymentMethod) {
//	payment := pay.Get(pType)
//	if payment == nil {
//		msg, _ := json.Marshal(map[string]interface{}{
//			"code": model.PaymentNotFound.Code(),
//			"msg":  model.PaymentNotFound.Message(),
//		})
//		w.Header().Set("Content-Type", "application/json; charset=utf-8")
//		w.Write(msg)
//	}
//	no, err := payment.Notify(req)
//	if err != nil {
//		payment.AckNotify(w, err)
//		return
//	}
//	fmt.Println("[payment.Notify no]", utils.JsonString(no))
//
//	switch no.Type {
//	case pay.NT_TRADE, 0: // 支付完成
//		if payment.Name() == model.CPT_APPSTORE { // 校验沙盒白名单
//			if !s.CheckAppstoreSandbox(ctx, no.ID, no) {
//				msg, _ := json.Marshal(map[string]interface{}{
//					"code": model.AppleSandBoxDisable.Code(),
//					"msg":  model.AppleSandBoxDisable.Message(),
//					"ts":   time.Now().UnixNano() / 1e6,
//					"data": nil,
//				})
//				w.Header().Set("Content-Type", "application/json; charset=utf-8")
//				w.Write(msg)
//				return
//			}
//
//		}
//		err = s.dao.NotifyOrder(ctx, no.ID, no.Data, payment.Table())
//
//		//case pay.NT_TRADE_ERR: // 交易失败
//		//	reason, _ := no.Data["reason"].(string)
//		//	err = s.dao.CancelledOrder(ctx, no.ID, reason)
//	}
//	payment.AckNotify(w, err)
//}
//
//// CheckAppstoreSandbox 校验苹果沙盒白名单
//func (s *Svc) CheckAppstoreSandbox(ctx context.Context, orderId int64, order *pay.NotifyOrder) bool {
//	if order.UseSandbox { // 使用沙盒支付，校验白名单
//		accountID := s.dao.GetAccountIDByOrderID(ctx, orderId)
//		if accountID == "" {
//			return false
//		}
//
//		for _, white := range s.conf.Appstore.SandboxWhiteList {
//			if white == accountID {
//				return true
//			}
//		}
//		return false
//	} else {
//		return true
//	}
//}
//
//func (s *Svc) MQUpdateAccount(data []byte) error {
//	info := new(model.AccountMessage)
//	// 数据出错，该消息丢弃
//	if err := json.Unmarshal(data, info); err != nil {
//		return model.MQAccountMessageUnmarshalErr
//	}
//	fmt.Println(info)
//	return nil
//}
//
//func randNumStr(l int) string {
//	numStr := ""
//	for i := 0; i < l; {
//		numStr += strconv.Itoa(rand.Intn(9))
//		i++
//	}
//	return numStr
//}
//
//func (s *Svc) AccountStatus(ctx context.Context, accountId uint64, ip string) (*model.AccountStatusRes, error) {
//	var accountStatusRes model.AccountStatusRes
//	userInfo, err := s.dao.GetUserInfoById(ctx, accountId)
//	if err != nil || userInfo == nil {
//		return nil, model.AccountNotFound
//	}
//	// 封禁用户不能生成订单
//	if userInfo.Status != {
//		return nil, model.UserError
//	}
//
//	accountStatusRes.Code = ""
//	accountStatusRes.Status = "pass"
//
//	count, err := s.dao.GetNoPayCountByAccount(userInfo.UserId)
//	if err != nil {
//		return nil, err
//	}
//	if count >= s.conf.DingPay.DingAccountTime {
//		var dingToken = []string{s.conf.DingPay.DingPayToken}
//		cli := dingtalk.InitDingTalk(dingToken, s.conf.DingPay.DingPayKey)
//		dm := dingtalk.DingMap()
//		dm.Set(fmt.Sprintf("用户ID %d", userInfo.UserId), dingtalk.N)
//		dm.Set(fmt.Sprintf("注册时间 %s", time.Unix(userInfo.CreatedAt, 0).Format("2006-01-02 15:04:05")), dingtalk.N)
//		dm.Set(fmt.Sprintf("发起IP %s", ip), dingtalk.N)
//		dm.Set(fmt.Sprintf("用户%d 分钟未支付订单%d，触发二次校验", 60, count), dingtalk.N)
//
//		cli.SendMarkDownMessageBySlice("预警", dm.Slice(), dingtalk.WithAtAll())
//
//		accountStatusRes.Code = randNumStr(4)
//		accountStatusRes.Status = "check"
//	}
//	return &accountStatusRes, nil
//}
