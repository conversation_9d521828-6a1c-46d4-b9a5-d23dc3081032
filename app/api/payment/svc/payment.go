package svc

import (
	"context"
	"creativematrix.com/beyondreading/app/common"
	"encoding/json"
	"fmt"
	"math/rand"
	"net/http"
	"strconv"
	"time"

	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/typeconvert"
	"github.com/blinkbean/dingtalk"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"creativematrix.com/beyondreading/pkg/utils"

	"creativematrix.com/beyondreading/app/api/payment/model"
	"creativematrix.com/beyondreading/app/api/payment/pay"
)

func (s *Svc) GetProducts(ctx context.Context, pp *model.ProductParam) ([]*model.PayProductRes, error) {
	product, err := s.dao.GetProducts(ctx, pp)
	if err != nil {
		if err == model.ErrNoDocuments {
			return nil, model.ProductNotFound
		}
		return nil, err
	}
	if len(product) > 0 {
		for _, p := range product {
			p.Num = p.Num + p.GIftNum
		}
	}

	return product, nil
}

func (s *Svc) Unifiedorder(ctx context.Context, op *model.OrderParam) (*model.OrderRes, error) {
	var orderRes model.OrderRes
	// 获取支付平台
	payment := pay.Get(op.PayType)
	if payment == nil {
		return nil, model.PaymentNotFound
	}

	var (
		product *model.PayProductModel
		err     error
	)
	if op.ProductID > model.CustomPayStartNum { // 自定义充值金额
		pid := op.ProductID - model.CustomPayStartNum
		product = &model.PayProductModel{
			Name:      typeconvert.Int32ToString(pid),
			ProductId: op.ProductID,
			Price:     float64(pid),
			Num:       pid * 100,
			GIftNum:   0,
		}
	} else {
		// 获取商品信息
		product, err = s.dao.GetProductByID(ctx, op)
		if err != nil {
			return nil, model.ProductNotFound
		}
	}

	// 获取用户信息
	userInfo, err := s.dao.GetUserInfoById(ctx, op.UserId)
	if err != nil {
		return nil, model.AccountNotFound
	}
	op.Subject = fmt.Sprintf(model.PayTip, userInfo.UserId)

	// 封禁用户不能生成订单
	if userInfo.Status != common.UserStatusNormal {
		return nil, model.UserError
	}
	// 预警是否多比订单未支付告警
	err = s.PayDingTalk(userInfo.NoId, userInfo.Id, op.SpbillCreateIp)
	if err != nil {
		logger.LogErrorf("payDingTalk Err %s (%v)", userInfo.NoId, err)
	}

	// 生成订单 ID
	orderId, payId, err := s.dao.GetUniqueIds(ctx)
	if err != nil {
		return nil, err
	}

	// 生成 chargeorder 数据
	order := s.dao.NewOrder(op, product, orderId, payId)

	// 生成 payment 数据
	payOrder, err := payment.Pay(order, product, op)
	if err != nil {
		return nil, err
	}

	// 创建订单
	if err := s.dao.CreateOrder(ctx, order, payOrder, payment.Table()); err != nil {
		return nil, err
	}

	orderRes.OrderId = order.ID
	orderRes.Type = payOrder.Type
	orderRes.Value = payOrder.Value

	return &orderRes, nil
}

func (s *Svc) PayDingTalk(noId, accountId, ip string) error {
	count, err := s.dao.GetNoPayCountByAccount(accountId)
	if err != nil {
		return err
	}

	if count >= s.conf.DingPay.DingTime {
		id, _ := primitive.ObjectIDFromHex(accountId)
		var dingToken = []string{s.conf.DingPay.DingPayToken}
		cli := dingtalk.InitDingTalk(dingToken, s.conf.DingPay.DingPayKey)
		dm := dingtalk.DingMap()
		dm.Set(fmt.Sprintf("用户ID %s", noId), dingtalk.N)
		dm.Set(fmt.Sprintf("注册时间 %s", id.Timestamp().Add(+time.Hour*8).Format("2006-01-02 15:04:05")), dingtalk.N)
		dm.Set(fmt.Sprintf("发起IP %s", ip), dingtalk.N)
		dm.Set(fmt.Sprintf("%d 分钟发起 %d 次数未支付", 60, count), dingtalk.N)

		return cli.SendMarkDownMessageBySlice("预警", dm.Slice(), dingtalk.WithAtAll())
	}
	return nil
}

func (s *Svc) Notify(ctx context.Context, w http.ResponseWriter, req *http.Request, pType model.PayType) {
	payment := pay.Get(pType)
	if payment == nil {
		msg, _ := json.Marshal(map[string]interface{}{
			"code": model.PaymentNotFound.Code(),
			"msg":  model.PaymentNotFound.Message(),
		})
		w.Header().Set("Content-Type", "application/json; charset=utf-8")
		w.Write(msg)
	}
	no, err := payment.Notify(req)
	if err != nil {
		payment.AckNotify(w, err)
		return
	}
	fmt.Println("[payment.Notify no]", utils.JsonString(no))

	switch no.Type {
	case pay.NT_TRADE, 0: // 支付完成
		if payment.Name() == model.CPT_APPSTORE { // 校验沙盒白名单
			if !s.CheckAppstoreSandbox(ctx, no.ID, no) {
				msg, _ := json.Marshal(map[string]interface{}{
					"code": model.AppleSandBoxDisable.Code(),
					"msg":  model.AppleSandBoxDisable.Message(),
					"ts":   time.Now().UnixNano() / 1e6,
					"data": nil,
				})
				w.Header().Set("Content-Type", "application/json; charset=utf-8")
				w.Write(msg)
				return
			}

		}
		err = s.dao.NotifyOrder(ctx, no.ID, no.Data, payment.Table())

		//case pay.NT_TRADE_ERR: // 交易失败
		//	reason, _ := no.Data["reason"].(string)
		//	err = s.dao.CancelledOrder(ctx, no.ID, reason)
	}
	payment.AckNotify(w, err)
}

// CheckAppstoreSandbox 校验苹果沙盒白名单
func (s *Svc) CheckAppstoreSandbox(ctx context.Context, orderId int64, order *pay.NotifyOrder) bool {
	if order.UseSandbox { // 使用沙盒支付，校验白名单
		accountID := s.dao.GetAccountIDByOrderID(ctx, orderId)
		if accountID == "" {
			return false
		}

		for _, white := range s.conf.Appstore.SandboxWhiteList {
			if white == accountID {
				return true
			}
		}
		return false
	} else {
		return true
	}
}

func (s *Svc) MQUpdateAccount(data []byte) error {
	info := new(model.AccountMessage)
	// 数据出错，该消息丢弃
	if err := json.Unmarshal(data, info); err != nil {
		return model.MQAccountMessageUnmarshalErr
	}
	fmt.Println(info)
	return nil
}

func randNumStr(l int) string {
	numStr := ""
	for i := 0; i < l; {
		numStr += strconv.Itoa(rand.Intn(9))
		i++
	}
	return numStr
}

func (s *Svc) AccountStatus(ctx context.Context, accountId, ip string) (*model.AccountStatusRes, error) {
	var accountStatusRes model.AccountStatusRes
	userInfo, err := s.dao.GetUserByMongoId(ctx, accountId)
	if err != nil || userInfo == nil {
		return nil, model.AccountNotFound
	}
	// 封禁用户不能生成订单
	if userInfo.RegisterStatus == "forbid" {
		return nil, model.UserError
	}

	accountStatusRes.Code = ""
	accountStatusRes.Status = "pass"

	count, err := s.dao.GetNoPayCountByAccount(userInfo.Id)
	if err != nil {
		return nil, err
	}
	if count >= s.conf.DingPay.DingAccountTime {
		id, _ := primitive.ObjectIDFromHex(userInfo.Id)

		var dingToken = []string{s.conf.DingPay.DingPayToken}
		cli := dingtalk.InitDingTalk(dingToken, s.conf.DingPay.DingPayKey)
		dm := dingtalk.DingMap()
		dm.Set(fmt.Sprintf("用户ID %s", userInfo.NoId), dingtalk.N)
		dm.Set(fmt.Sprintf("注册时间 %s", id.Timestamp().Add(+time.Hour*8).Format("2006-01-02 15:04:05")), dingtalk.N)
		dm.Set(fmt.Sprintf("发起IP %s", ip), dingtalk.N)
		dm.Set(fmt.Sprintf("用户%d 分钟未支付订单%d，触发二次校验", 60, count), dingtalk.N)

		cli.SendMarkDownMessageBySlice("预警", dm.Slice(), dingtalk.WithAtAll())

		accountStatusRes.Code = randNumStr(4)
		accountStatusRes.Status = "check"
	}
	return &accountStatusRes, nil
}
