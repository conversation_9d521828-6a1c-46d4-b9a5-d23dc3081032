package dao

import (
	"context"
	userpb "creativematrix.com/beyondreading/app/base/user/api"
)

func (d *Dao) GetUserByMongoId(ctx context.Context, userId string) (*userpb.UserInfo, error) {
	req := &userpb.MongoIdsReq{
		Ids: []string{userId},
	}

	rsp, err := d.uc.GetUsersByMongoIds(ctx, req)
	if err != nil {
		return nil, err
	}

	if rsp != nil && len(rsp.Users) == 1 {
		return rsp.Users[0], nil
	}

	return nil, nil
}
