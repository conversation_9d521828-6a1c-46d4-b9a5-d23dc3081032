package dao

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"creativematrix.com/beyondreading/pkg/logger"

	"creativematrix.com/beyondreading/pkg/typeconvert"
	"creativematrix.com/beyondreading/pkg/utils"

	"github.com/go-xorm/builder"
	"github.com/gomodule/redigo/redis"
	"github.com/jmoiron/sqlx"
	"github.com/jmoiron/sqlx/reflectx"
	"go.mongodb.org/mongo-driver/bson"

	msD "github.com/go-sql-driver/mysql"

	"creativematrix.com/beyondreading/app/api/payment/model"
	"creativematrix.com/beyondreading/app/api/payment/pay"
	"creativematrix.com/beyondreading/pkg/mongo"
	"creativematrix.com/beyondreading/pkg/mysql"
)

func (d *Dao) GetProducts(ctx context.Context, pp *model.ProductParam) ([]*model.PayProductRes, error) {
	products := make([]*model.PayProductRes, 0)
	sort := bson.M{"serial": 1}
	if err := d.schema[model.TablePayProduct].Find(&products, bson.M{"platform": pp.Platform, "status": "published"}, mongo.Sort(sort)); err != nil {
		return nil, err
	}

	return products, nil
}

func (d *Dao) GetProductByID(ctx context.Context, op *model.OrderParam) (*model.PayProductModel, error) {
	var product model.PayProductModel
	if err := d.schema[model.TablePayProduct].FindOne(&product, bson.M{"productId": op.ProductID}); err != nil {
		return nil, err
	}
	return &product, nil
}

//primitive.ObjectIDFromHex(id)

func (d *Dao) GetUniqueIds(ctx context.Context) (int64, int64, error) {
	n, err := redis.Int64(d.cache.RDo(ctx, "INCRBY", model.Redis_OrderUniqueIDKey, 2))
	return n, n - 1, err
}

func (d *Dao) NewOrder(op *model.OrderParam, product *model.PayProductModel, orderId, payId int64) *model.ChargeOrder {
	order := &model.ChargeOrder{
		ID:          orderId,
		PayID:       payId,
		AccountID:   op.AccountID,
		Price:       int32(product.Price * 100),
		PayType:     op.PayType,
		Platform:    op.Platform,
		Diamond:     product.Num,
		GiftDiamond: product.GIftNum,
		ProductID:   product.ProductId,
	}
	return order
}

func (d *Dao) CreateOrder(ctx context.Context, order *model.ChargeOrder, po *pay.PayOrder, table string) error {

	db, err := d.msshard.DB(order.AccountID)
	if err != nil {
		return err
	}

	err = mysql.Transact(db, func(tx *sqlx.Tx) (err error) {
		orderSql, args := order.OrderSql()
		if _, err := tx.ExecContext(ctx, orderSql, args...); err != nil {
			return err
		}

		if table != "" {
			if po.Order == nil {
				return errors.New("no pay order")
			}
			po.Order["_id"] = order.PayID
			if table == "pay_weixin" || table == "pay_ali" {
				po.Order["orderId"] = order.ID
			}

			paySQL, args, err := builder.Insert(builder.Eq(po.Order)).Into(table).ToSQL()
			if err != nil {
				return err
			}
			if _, err := tx.Exec(paySQL, args...); err != nil {
				return err
			}

		}

		//diamonds := order.Diamond + order.GiftDiamond
		//accountSQL := `UPDATE accounts SET diamonds=diamonds+?,totalDiamonds=totalDiamonds+? where _id=?`
		//if _, err := tx.ExecContext(ctx, accountSQL, diamonds, diamonds, order.AccountID); err != nil {
		//	return err
		//}
		//
		//key := fmt.Sprintf("user:diamonds:%s", order.AccountID)
		//d.cache.DelKey(ctx, key)

		if err := d.setAccountIDByOrderID(ctx, order.ID, order.AccountID); err != nil {
			return err
		}

		return nil
	})
	return err
}

func (d *Dao) NotifyOrder(ctx context.Context, orderID int64, paymentUpdate map[string]interface{}, table string) error {
	accountID := d.GetAccountIDByOrderID(ctx, orderID)
	if accountID == "" {
		return model.AccountNotFound
	}

	db, err := d.msshard.DB(accountID)
	if err != nil {
		return err
	}

	order := new(model.ChargeOrder)

	err = mysql.Transact(db, func(tx *sqlx.Tx) error {
		uTx := tx.Unsafe()
		uTx.Mapper = reflectx.NewMapperFunc("json", strings.ToLower)
		if err := uTx.GetContext(ctx, order, "SELECT * FROM chargeorders WHERE _id=? FOR UPDATE", orderID); err != nil {
			return err
		}
		// 订单已完成
		if order.Stat == "completed" {
			return nil
		}

		// 更改订单状态
		if _, err := tx.ExecContext(ctx, `UPDATE chargeorders SET stat="completed",completed=NOW() WHERE _id=?`, orderID); err != nil {
			return err
		}

		if table != "" {
			// 更改 payment 表状态
			if paymentUpdate == nil {
				paymentUpdate = make(map[string]interface{})
			}
			paymentUpdate["paid"] = 1
			paymentUpdate["transfered"] = 1
			query, args, err := builder.Update(paymentUpdate).From(table).Where(builder.Eq{"_id": order.PayID}).ToSQL()
			if err != nil {
				return err
			}
			logger.LogInfoF("[query args paymentUpdate] %s %v %v", query, args, paymentUpdate)
			if _, err := tx.ExecContext(ctx, query, args...); err != nil {
				return err
			}
		}

		// 插入充值完成事件
		if _, err := tx.Exec(`INSERT INTO eventlogs (_id,type,state) VALUES (?,2,0)`, orderID); err != nil {
			return err
		}

		return nil
	})

	if me, ok := err.(*msD.MySQLError); ok && me.Number == 1062 {
		return model.OrderDuplicate
	}

	if err != nil {
		return err
	}

	order.Stat = "completed"
	if order.Completed == nil {
		now := time.Now()
		order.Completed = &now
	}

	var transactionId string
	if val, ok := paymentUpdate["transactionId"]; ok {
		transactionId = val.(string)
	}
	// 过户 要写进mq 然后再给回执
	mess := &model.MqBaseMessage{
		Event: model.EVENT_RECHARGE,
		Ts:    time.Now().Unix(),
		Data: &model.RechargeMessage{
			AccountId:     order.AccountID,
			Platform:      order.Platform,
			RechargeTime:  order.Completed.Format("2006-01-02 15:04:05"),
			PayType:       order.PayType.String(),
			Amount:        typeconvert.Float32ToString(float32(order.Price) / 100),
			Diamonds:      int64(order.Diamond + order.GiftDiamond),
			OrderId:       orderID,
			TransActionId: transactionId,
		},
	}
	fmt.Println("[PUBLISH]", utils.JsonString(mess))
	if err = d.RabbitMQ.Publish(model.MQ_EXPEND_BASE, mess); err != nil {
		fmt.Println(err)
	}

	return nil
}

func (d *Dao) setAccountIDByOrderID(ctx context.Context, orderID int64, accountID string) error {
	_, err := d.cache.RDo(ctx, "SETEX", model.Redis_OrderIDToAccountIDKey, 2*24*3600, accountID)
	return err
}

func (d *Dao) GetAccountIDByOrderID(ctx context.Context, orderID int64) string {

	result, _ := redis.String(d.cache.RDo(ctx, "GET", model.Redis_OrderIDToAccountIDKey, orderID))

	if result == "" {
		var accountID string
		for _, db := range d.msshard.All() {
			_ = db.Get(&accountID, "SELECT accountId FROM chargeorders WHERE _id=?", orderID)
			if accountID != "" {
				break
			}
		}
		result = accountID
	}
	return result
}

func (d *Dao) GetNoPayCountByAccount(accountID string) (int64, error) {
	db, err := d.msshard.DB("")
	if err != nil {
		return 0, err
	}

	var chargeTotalSQL = "select COUNT(*) from chargeorders where accountId='%s' and stat!='completed' and created>='%s'"
	timeS := time.Now().Add(time.Minute * -60).Format(utils.TimeFormat)
	qSql := fmt.Sprintf(chargeTotalSQL, accountID, timeS)

	var total int64
	row := db.QueryRow(qSql)
	if err = row.Scan(&total); err != nil {
		return -1, err
	}
	return total, err
}
