package dao

import (
	"context"
	"creativematrix.com/beyondreading/app/api/payment/model/po"
	"creativematrix.com/beyondreading/pkg/logger"
	"database/sql"
	"encoding/json"
	"fmt"
)

// GetPaymentMethods 获取支付方式列表
func (d *Dao) GetPaymentMethods(ctx context.Context, enabled *bool) ([]*po.PaymentMethodConfig, error) {
	// 先从缓存获取
	if enabled == nil || *enabled {
		methods, err := d.getPaymentMethodsFromCache(ctx)
		if err == nil && methods != nil {
			return methods, nil
		}
	}

	// 构建SQL查询
	query := "SELECT id, method, name, enabled, config, sort_order, created_at, updated_at FROM payment_methods"
	args := []interface{}{}

	if enabled != nil {
		query += " WHERE enabled = ?"
		args = append(args, *enabled)
	}

	query += " ORDER BY method ASC"

	// 获取数据库连接 - payment_methods不分片，使用默认连接
	db, err := d.msshard.DB("0")
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}

	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		logger.LogErrorf("Failed to query payment methods: %v", err)
		return nil, fmt.Errorf("failed to query payment methods: %w", err)
	}
	defer rows.Close()

	var methods []*po.PaymentMethodConfig
	for rows.Next() {
		method := &po.PaymentMethodConfig{}
		var configStr string
		err := rows.Scan(&method.ID, &method.Method, &method.Name, &method.Enabled,
			&configStr, &method.SortOrder, &method.CreatedAt, &method.UpdatedAt)
		if err != nil {
			logger.LogErrorf("Failed to scan payment method: %v", err)
			continue
		}

		// 解析配置JSON
		if configStr != "" {
			if err := json.Unmarshal([]byte(configStr), &method.Config); err != nil {
				logger.LogErrorf("Failed to unmarshal method config: %v", err)
				method.Config = make(map[string]string)
			}
		} else {
			method.Config = make(map[string]string)
		}

		methods = append(methods, method)
	}

	// 设置缓存（仅缓存启用的支付方式）
	if enabled == nil || *enabled {
		err = d.setPaymentMethodsCache(ctx, methods)
		if err != nil {
			logger.LogErrorf("Failed to set payment methods cache: %v", err)
		}
	}

	return methods, nil
}

// GetPaymentMethodByMethod 根据支付方式获取配置
func (d *Dao) GetPaymentMethodByMethod(ctx context.Context, method int32) (*po.PaymentMethodConfig, error) {
	// 先从缓存获取
	methodConfig, err := d.getPaymentMethodFromCache(ctx, method)
	if err == nil && methodConfig != nil {
		return methodConfig, nil
	}

	query := "SELECT id, method, name, enabled, config, sort_order, created_at, updated_at FROM payment_methods WHERE method = ?"

	// 获取数据库连接
	db, err := d.msshard.DB("0")
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}

	var result po.PaymentMethodConfig
	var configStr string
	err = db.QueryRowContext(ctx, query, method).Scan(
		&result.ID, &result.Method, &result.Name, &result.Enabled,
		&configStr, &result.SortOrder, &result.CreatedAt, &result.UpdatedAt)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		logger.LogErrorf("Failed to get payment method: %v", err)
		return nil, fmt.Errorf("failed to get payment method: %w", err)
	}

	// 解析配置JSON
	if configStr != "" {
		if err := json.Unmarshal([]byte(configStr), &result.Config); err != nil {
			logger.LogErrorf("Failed to unmarshal method config: %v", err)
			result.Config = make(map[string]string)
		}
	} else {
		result.Config = make(map[string]string)
	}

	// 设置缓存
	err = d.setPaymentMethodCache(ctx, &result)
	if err != nil {
		logger.LogErrorf("Failed to set payment method cache: %v", err)
	}

	return &result, nil
}
