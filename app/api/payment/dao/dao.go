package dao

import (
	"context"
	"creativematrix.com/beyondreading/app/api/payment/conf"
	"creativematrix.com/beyondreading/app/api/payment/model"
	userApi "creativematrix.com/beyondreading/app/base/user/api"
	"creativematrix.com/beyondreading/pkg/elastic"
	"creativematrix.com/beyondreading/pkg/mongo"
	"creativematrix.com/beyondreading/pkg/mysql"
	"creativematrix.com/beyondreading/pkg/rabbitmq"
	"creativematrix.com/beyondreading/pkg/redis"
	userpb "creativematrix.com/beyondreading/proto/user"
)

type Dao struct {
	msshard mysql.Mysqler
	// pgshard  postgre.Postgrer
	cache    redis.Redis
	schema   map[string]*mongo.Model
	es       *elastic.Elastic
	RabbitMQ *rabbitmq.Broker
	// Kafka    kafka.Ikafka
	uc userpb.UserServiceClient
}

var summerTable = []string{
	model.TablePayProduct,
}

func (d *Dao) Ping(ctx context.Context) (err error) {
	return
}

func (d *Dao) Close() {
	d.es.Close()
	// d.Kafka.Close()
}

func Load(c *conf.Config) *Dao {

	summerConn := mongo.Connect(c.MongodbPayment)
	schema := map[string]*mongo.Model{}

	for _, name := range summerTable {
		schema[name] = summerConn.Model(name)
	}

	es, err := elastic.NewEsPool(c.ElasticSearch)
	if err != nil {
		panic(err)
	}

	uc, err := userApi.NewClient(c.Base)
	if err != nil {
		panic(err)
	}

	// ikafka, err := kafka.Kafka(kafka.KConfig{
	// 	Point:          c.Kafka.Point,
	// 	CommitInterval: time.Second,
	// 	Initial:        sarama.OffsetOldest,
	// 	Username:       c.Kafka.Username,
	// 	Password:       c.Kafka.Password,
	// })
	// if err != nil {
	// 	panic(err)
	// }

	return &Dao{
		// Kafka:    ikafka,
		msshard:  mysql.New(c.Mysql),
		es:       es,
		schema:   schema,
		cache:    redis.Load(c.RedisPayment),
		uc:       uc,
		RabbitMQ: rabbitmq.NewBroker(c.RabbitMQ.URL, c.RabbitMQ.SummerEx),
	}
}
