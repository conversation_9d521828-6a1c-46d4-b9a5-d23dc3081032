package http

import (
	"fmt"

	"creativematrix.com/beyondreading/pkg/rateLimit"

	"github.com/gin-gonic/gin"

	"creativematrix.com/beyondreading/app/api/payment/conf"
	"creativematrix.com/beyondreading/app/api/payment/model"
	"creativematrix.com/beyondreading/app/api/payment/svc"

	bbrouter "creativematrix.com/beyondreading/pkg/router"
)

var (
	service *svc.Svc
)

func Start(c *conf.Config, s *svc.Svc) {

	service = s
	router := bbrouter.Start(c.Base)

	limiter := rateLimit.New(c.RedisPayment).RateLimiter(
		rateLimit.MaxThreads(1),
		rateLimit.IsLimitTime(true),
		rateLimit.ExpireTime(1),
	)

	// 充值支付
	router.GET("/payment/products", getProducts)

	v2 := router.Group("/payment")
	{
		v2.POST("/unifiedorder", router.AuthUser, limiter, unifiedorder)
		v2.POST("/unifiedorder/wxoa", limiter, unifiedorder) // 微信公众号支付下单
		v2.GET("/order/:id", router.AuthUser, getOrder)
		v2.GET("/account/status", limiter, accountStatus)                     // 微信公众号校验
		v2.GET("/account/status/v2", router.AuthUser, limiter, accountStatus) // app校验
	}

	router.POST("/payment/notify/:type", func(c *gin.Context) {
		pType := model.PayType(c.Param("type"))
		if pType == model.CPT_APPSTORE {
			router.AuthUser(c)
			fmt.Println(c.IsAborted())
			if c.IsAborted() {
				return
			}
		}
	}, notify)

	// 充值支付
	router.GET("/paqyv2la7nnwt72qupmpepqtjn", getProducts)
	router.POST("/paf3t69vsbbkeiaqkyuifg7wts", router.AuthUser, limiter, unifiedorder)

	go func() {
		if err := router.Run(c.Port.HTTP); err != nil {
			panic(err)
		}
	}()
}
