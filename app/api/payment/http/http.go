package http

import (
	"creativematrix.com/beyondreading/app/api/payment/conf"
	"creativematrix.com/beyondreading/app/api/payment/svc"
	bbrouter "creativematrix.com/beyondreading/pkg/router"
)

var (
	service *svc.PaymentSvc
)

func Start(c *conf.Config, s *svc.PaymentSvc) (*bbrouter.Router, error) {
	service = s
	server := bbrouter.Start(c.Base)

	v1 := server.Group("/api/v1/payment")
	{
		// 支付订单相关
		v1.POST("/orders", createPaymentOrder)
		v1.GET("/orders/:orderId", getPaymentOrder)
		v1.GET("/orders", getPaymentOrders)
		v1.PUT("/orders/:orderId/cancel", cancelPaymentOrder)

		// 支付回调
		v1.POST("/callback", processPaymentCallback)

		// 退款
		v1.POST("/refund", refundPayment)

		// 支付方式
		v1.GET("/methods", getPaymentMethods)
	}

	// 支付回调路由（不需要认证）
	callback := server.Group("/api/v1/payment/callback")
	{
		callback.POST("/google", processGooglePayCallback)
		callback.POST("/apple", processApplePayCallback)
		callback.POST("/paypal", processPayPalCallback)
		callback.POST("/alipay", processAlipayCallback)
		callback.POST("/wechat", processWechatPayCallback)
	}

	return server, nil
}
