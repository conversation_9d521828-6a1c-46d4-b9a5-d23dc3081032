package http

import (
	"creativematrix.com/beyondreading/app/api/payment/model/vo"
	"fmt"
	"strconv"

	"creativematrix.com/beyondreading/pkg/router"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"

	"creativematrix.com/beyondreading/app/api/payment/model"
	"creativematrix.com/beyondreading/pkg/ecode"
	"creativematrix.com/beyondreading/pkg/utils"
)

var (
	key = "cLspc3m1"
)

func getProducts(c *gin.Context) {
	pp := new(vo.GetProductReq)
	if err := c.BindQuery(pp); err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	list, err := service.GetProducts(c, pp)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(list).Success()
}

func createOrder(c *gin.Context) {
	up := new(vo.CreatePaymentOrderReq)
	if err := c.MustBindWith(up, binding.Form); err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	if up.UserId == 0 {
		id, _ := strconv.Atoi(c.GetString(router.UserKey))
		up.UserId = uint64(id)
	}
	if up.ClientIp == "" {
		up.ClientIp = utils.ClientIP(c.Request)
	}
	orderRes, err := service.CreateOrder(c, up)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	// result := make(map[string]interface{})
	// result["orderId"] = orderRes.OrderId
	// result["orderId"] = orderRes.OrderId

	ecode.Back(c).SetData(orderRes).Success()
}

func getOrder(c *gin.Context) {
	fmt.Println("dsldf")

	// var result map[string]interface{}
	// result["status"] = "complated"

	ecode.Back(c).SetData(true).Success()
}

func notify(c *gin.Context) {
	pType := model.PaymentMethod(c.Param("paymentMethod"))
	fmt.Println(pType)
	//service.Notify(c, c.Writer, c.Request, pType)
}

func accountStatus(c *gin.Context) {
	as := new(model.AccountStatusParam)
	if as.UserId == "" {
		as.UserId = c.GetString(router.UserKey)
	}

	if err := c.BindQuery(as); err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	//uId, err := strconv.Atoi(as.UserId)
	//if err != nil {
	//	ecode.Back(c).Failure(err)
	//	return
	//}
	//
	//accountRes, err := service.AccountStatus(c, uint64(uId), utils.ClientIP(c.Request))
	//if err != nil {
	//	ecode.Back(c).Failure(err)
	//	return
	//}
	//ecode.Back(c).SetData(accountRes).Success()
}
