package model

type MqBaseMessage struct {
	Event string      `json:"event"`
	Ts    int64       `json:"ts"`   // unix时间戳
	Data  interface{} `json:"data"` // 消息体
}

type RechargeMessage struct {
	AccountId     string `json:"accountId"`     // 账户ID
	Platform      string `json:"platform"`      // 系统
	RechargeTime  string `json:"rechargeTime"`  // 充值时间
	PayType       string `json:"payType"`       // 充值方式
	Amount        string `json:"amount"`        // 充值金额
	Diamonds      int64  `json:"diamonds"`      // 获得钻石
	OutTradeNo    string `json:"outTradeNo"`    // 三方交易号
	OrderId       int64  `json:"orderId"`       // 业务订单号
	TransActionId string `json:"transActionId"` // 交易单号
}
