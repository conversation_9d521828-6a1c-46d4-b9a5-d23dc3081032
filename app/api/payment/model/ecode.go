package model

import "creativematrix.com/beyondreading/pkg/ecode"

var (
	BindError      = ecode.New(-3, "body bind 错误")
	ErrNoDocuments = ecode.New(-40006, "会话记录不存在")

	AccountNotFound = ecode.New(-1000, "账户不存在")
	UserError       = ecode.New(-41204, "用户账号封禁中")

	PaymentNotFound = ecode.New(-10001, "支付类型不存在")
	SignError       = ecode.New(-10002, "签名错误")
	OrderDuplicate  = ecode.New(-10003, "订单重复")
	ProductNotFound = ecode.New(-10005, "充值商品不存在")
	OrderIDError    = ecode.New(-10006, "订单ID错误")

	NotifyTypeError = ecode.New(-10014, "通知类型错误")

	WeixinAppInvalid  = ecode.New(-11100, "微信appleId异常")
	AlipayStatusError = ecode.New(-11001, "支付宝status错误")
	AlipayRSAKeyError = ecode.New(-11002, "支付宝RSAKey错误")

	AppleStatusError      = ecode.New(-11200, "苹果status错误")
	AppleTransactionError = ecode.New(-11201, "")
	AppleSandBoxDisable   = ecode.New(-11202, "Sandbox receipt used in production, recharge failed. Use the specified whitelist account for sandbox recharge")

	MQAccountMessageUnmarshalErr = ecode.New(-90001, "account message unmarsh Err")
)
