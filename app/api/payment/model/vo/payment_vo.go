package vo

import (
	"creativematrix.com/beyondreading/app/api/payment/model"
	"time"
)

const (
	Pay_ALI      model.PaymentMethod = "alipay"
	Pay_WEIXIN   model.PaymentMethod = "weixinpay"
	Pay_APPSTORE model.PaymentMethod = "appstore"
	Pay_GOOGLE   model.PaymentMethod = "google"
	Pay_PAYPAL   model.PaymentMethod = "paypal"

	APP    model.TradeType = "APP"
	MWEB   model.TradeType = "MWEB"
	JSAPI  model.TradeType = "JSAPI"
	NATIVE model.TradeType = "NATIVE"
)

// CreatePaymentOrderReq 创建支付订单请求
type CreatePaymentOrderReq struct {
	UserId        uint64              `json:"userId" binding:"required"`
	PaymentMethod model.PaymentMethod `json:"paymentMethod" binding:"required"`
	PaymentType   string              `json:"paymentType" binding:"required"`
	Amount        int64               `json:"amount" binding:"required"`
	Currency      string              `json:"currency" binding:"required"`
	ProductId     uint32              `json:"productId" binding:"required"`
	ProductName   string              `json:"productName" binding:"required"`
	Description   string              `json:"description"`
	Metadata      map[string]string   `json:"metadata"`
	ReturnUrl     string              `json:"returnUrl"`
	CancelUrl     string              `json:"cancelUrl"`
	ClientIp      string              `json:"clientIp"`
	UserAgent     string              `json:"userAgent"`
}

// CreatePaymentOrderResp 创建支付订单响应
type CreatePaymentOrderResp struct {
	OrderId string `json:"orderId" binding:"required"`
	Type    int    `json:"type" binding:"required"`
	Value   string `json:"value" binding:"required"`
}

// PaymentCallbackReq 支付回调请求
type PaymentCallbackReq struct {
	PaymentMethod int32             `json:"paymentMethod"`
	OrderId       string            `json:"orderId"`
	TransactionId string            `json:"transactionId"`
	Status        int32             `json:"status"`
	Amount        int64             `json:"amount"`
	Currency      string            `json:"currency"`
	CallbackData  map[string]string `json:"callbackData"`
	Signature     string            `json:"signature"`
}

// PaymentCallbackResp 支付回调响应
type PaymentCallbackResp struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// GetPaymentOrderReq 获取支付订单请求
type GetPaymentOrderReq struct {
	OrderId string `json:"orderId" binding:"required"`
}

// GetPaymentOrderResp 获取支付订单响应
type GetPaymentOrderResp struct {
	Order *PaymentOrderInfo `json:"order"`
}

// GetPaymentOrdersReq 获取支付订单列表请求
type GetPaymentOrdersReq struct {
	PaymentMethod int32 `json:"paymentMethod"`
	PaymentType   int32 `json:"paymentType"`
	Status        int32 `json:"status"`
	Page          int32 `json:"page"`
	PageSize      int32 `json:"pageSize"`
	StartTime     int64 `json:"startTime"`
	EndTime       int64 `json:"endTime"`
}

// GetPaymentOrdersResp 获取支付订单列表响应
type GetPaymentOrdersResp struct {
	Orders []*PaymentOrderInfo `json:"orders"`
	Total  int64               `json:"total"`
}

// CancelPaymentOrderReq 取消支付订单请求
type CancelPaymentOrderReq struct {
	OrderId string `json:"orderId" binding:"required"`
	Reason  string `json:"reason"`
}

// CancelPaymentOrderResp 取消支付订单响应
type CancelPaymentOrderResp struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

// RefundPaymentReq 退款请求
type RefundPaymentReq struct {
	OrderId      string `json:"orderId" binding:"required"`
	RefundAmount int64  `json:"refundAmount" binding:"required"`
	Reason       string `json:"reason" binding:"required"`
	RefundId     string `json:"refundId"`
}

// RefundPaymentResp 退款响应
type RefundPaymentResp struct {
	RefundId string `json:"refundId"`
	Success  bool   `json:"success"`
	Message  string `json:"message"`
}

// GetPaymentMethodsReq 获取支付方式列表请求
type GetPaymentMethodsReq struct {
	Platform string `json:"platform"`
	Region   string `json:"region"`
}

// GetPaymentMethodsResp 获取支付方式列表响应
type GetPaymentMethodsResp struct {
	Methods []*PaymentMethodInfo `json:"methods"`
}

// PaymentOrderInfo 支付订单信息
type PaymentOrderInfo struct {
	OrderId       string            `json:"orderId"`
	UserId        uint64            `json:"userId"`
	PaymentMethod int32             `json:"paymentMethod"`
	PaymentType   int32             `json:"paymentType"`
	Amount        int64             `json:"amount"`
	Currency      string            `json:"currency"`
	ProductId     string            `json:"productId"`
	ProductName   string            `json:"productName"`
	Description   string            `json:"description"`
	Status        int32             `json:"status"`
	TransactionId string            `json:"transactionId"`
	Metadata      map[string]string `json:"metadata"`
	CreatedAt     time.Time         `json:"createdAt"`
	UpdatedAt     time.Time         `json:"updatedAt"`
	PaidAt        *time.Time        `json:"paidAt,omitempty"`
	ExpiredAt     time.Time         `json:"expiredAt"`
	FailureReason string            `json:"failureReason"`
}

// PaymentMethodInfo 支付方式信息
type PaymentMethodInfo struct {
	Method              int32             `json:"method"`
	Name                string            `json:"name"`
	DisplayName         string            `json:"displayName"`
	Icon                string            `json:"icon"`
	Enabled             bool              `json:"enabled"`
	SupportedCurrencies []string          `json:"supportedCurrencies"`
	Config              map[string]string `json:"config"`
}
