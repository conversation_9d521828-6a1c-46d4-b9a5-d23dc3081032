package model

import "go.mongodb.org/mongo-driver/bson/primitive"

const TablePayProduct = "pay_product"

type ProductParam struct {
	Platform string `form:"platform" binding:"required"` // 平台(android,ios)
}

type AccountStatusParam struct {
	AccountId string `form:"accountId" binding:"required"` // 用户noId
}

type PayProductModel struct {
	ID         primitive.ObjectID `bson:"_id,omitempty" json:"_id"`                        // 商品Id
	Platform   string             `bson:"platform" json:"platform" binding:"required"`     // android, ios
	Serial     int                `bson:"serial" json:"serial" binding:"required"`         // 序号
	Name       string             `bson:"name" json:"name" binding:"required"`             // 商品名称
	ProductId  int32              `bson:"productId" json:"productId" binding:"required"`   // 商品Id
	Price      float64            `bson:"price" json:"price" binding:"required"`           // RMB以分为单位 6
	Num        int32              `bson:"num" json:"num" binding:"required"`               // 充值获取数量 600
	GIftNum    int32              `bson:"giftNum" json:"giftNum" binding:"required"`       // 赠送金额 600
	Status     string             `bson:"status" json:"status" binding:"required"`         // 状态 published 上架  unPublished 下架 delete 删除
	DescStatus bool               `bson:"descStatus" json:"descStatus" binding:"required"` // 活动文案 true:有 false:无
	Desc       string             `bson:"desc" json:"desc" binding:"required"`             // 介绍，宣传语 【特惠】送600豆，新用户加送龙岩座驾
}

type PayProductRes struct {
	Name       string  `bson:"name" json:"name" binding:"required"`             // 商品名称
	ProductId  int32   `bson:"productId" json:"productId" binding:"required"`   // 商品Id
	Price      float64 `bson:"price" json:"price" binding:"required"`           // RMB以分为单位 6
	Num        int32   `bson:"num" json:"num" binding:"required"`               // 充值获取数量 600
	GIftNum    int32   `bson:"giftNum" json:"giftNum" binding:"required"`       // 赠送金额 600
	DescStatus bool    `bson:"descStatus" json:"descStatus" binding:"required"` // 活动文案 true:有 false:无
	Desc       string  `bson:"desc" json:"desc" binding:"required"`             // 介绍，宣传语 【特惠】送600豆，新用户加送龙岩座驾
}
