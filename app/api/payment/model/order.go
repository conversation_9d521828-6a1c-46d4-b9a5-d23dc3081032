package model

import (
	"time"
)

// PayType 订单支付类型
type PayType string

// TradeType 订单交易类型
type TradeType string

const (
	CPT_ALI      PayType = "alipay"
	CPT_WEIXIN   PayType = "weixinpay"
	CPT_APPSTORE PayType = "appstore"

	APP    TradeType = "APP"
	MWEB   TradeType = "MWEB"
	JSAPI  TradeType = "JSAPI"
	NATIVE TradeType = "NATIVE"
)

type OrderParam struct {
	//ProductID primitive.ObjectID `form:"productId" binding:"required"` // 商品Id
	ProductID      int32   `form:"productId" binding:"required"` // 商品Id
	PayType        PayType `form:"payType" binding:"required"`   // 支付方式 alipay,weixinpay,apply,appstore,backpay
	Platform       string  `form:"platform" binding:"required"`  // android, ios, h5
	TradeType      string  `form:"tradeType" binding:"required"` // 支付类型 APP,MWEB,JSAPI,NATIVE
	Ts             int64   `form:"ts" binding:"required"`        // 当前时间戳
	Sign           string  `form:"sign" binding:"required"`      // 签名：设所有发送数据（排除 sign）为集合 M，再添加密钥 key=xxx, 将集合 M 内非空参数值的参数按照参数名ASCII码从小到大排序，使用 URL 键值对格式（即 key1=value1&key2=value2… ）拼接字符串，进行 MD5 运算，得到结果即为本次请求签名值。
	SpbillCreateIp string  `form:"-"`                            // client ip
	OpenID         string  `form:"openId"`                       // JSAPI支付，此参数必传(微信使用)
	AppleID        string  `form:"appleId"`                      // ios 当无法获取商品id时，ProductID传 -1，且传入appleId
	AccountID      uint64  `form:"AccountId"`                    // 从header token里解析获得
	ReturnPath     string  `form:"returnPath"`                   // 支付宝跳转用
	Subject        string  `form:"-"`                            // 支付提示语
	Channel        string  `form:"channel"`                      // 渠道 苏州星炫xingxuan 上海一见yijian
}

type OrderRes struct {
	OrderId int64  `json:"orderId" binding:"required"`
	Type    int    `json:"type" binding:"required"`
	Value   string `json:"value" binding:"required"`
}

type AccountStatusRes struct {
	Status string `json:"status" binding:"required"`
	Code   string `json:"code" binding:"required"`
}
type ChargeOrder struct {
	ID          int64      `json:"_id,omitempty" db:"_id"`                 // 订单ID
	PayID       int64      `json:"payId,omitempty" db:"payId"`             // 支付类型ID
	AccountID   uint64     `json:"accountId,omitempty" db:"accountId"`     // 用户ID
	PayType     PayType    `json:"payType,omitempty" db:"payType"`         // 支付方式 alipay,weixinpay,apply,appstore,backpay
	Platform    string     `json:"platform,omitempty" db:"platform"`       // 渠道 android, ios, h5
	ProductID   int32      `json:"productId,omitempty" db:"productId"`     // 商品ID
	Price       int32      `json:"price,omitempty" db:"price"`             // 价格
	Diamond     int32      `json:"diamond,omitempty" db:"diamond"`         // 砖石数量
	GiftDiamond int32      `json:"giftDiamond,omitempty" db:"giftDiamond"` // 赠送砖石数量
	Stat        string     `json:"stat,omitempty" db:"stat"`               // 状态
	Created     *time.Time `json:"created,omitempty" db:"created"`         // 创建时间
	Completed   *time.Time `json:"completed,omitempty" db:"completed"`     // 完成时间
}

func (p PayType) String() string {
	return string(p)
}

func (c *ChargeOrder) OrderSql() (string, []interface{}) {
	orderSql := `INSERT INTO chargeorders (_id, payId, accountId, payType, platform, productId, price, diamond, giftDiamond, stat, created)
	VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

	return orderSql, []interface{}{
		c.ID, c.PayID, c.AccountID, c.PayType, c.Platform, c.ProductID, c.Price, c.Diamond, c.GiftDiamond, "pending", time.Now(),
	}
}
