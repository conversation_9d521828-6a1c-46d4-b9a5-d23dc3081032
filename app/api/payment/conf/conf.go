package conf

import (
	"fmt"

	"creativematrix.com/beyondreading/pkg/redis"

	"creativematrix.com/beyondreading/pkg/config"
	"creativematrix.com/beyondreading/pkg/rabbitmq"
)

type Config struct {
	config.Base

	MongodbPayment string

	RedisPayment *redis.Config

	Log struct {
		Level string
	}

	NotifyURL    string
	ReturnDomain string

	Weixinpay map[string]*WxConf

	Appstore struct {
		IsProduction     bool
		SandboxWhiteList []string
		CanUseSanboxIds  []string
		Passwords        map[string]string
	}

	Alipay struct {
		Partner      string
		SellerId     string
		AppId        string
		PrivateKey   string
		AliPublicKey string
	}

	RabbitMQ struct {
		URL      string
		SummerEx *rabbitmq.ExchangeConfig
	}

	DingPay struct {
		DingPayToken    string
		DingPayKey      string
		DingTime        int64
		DingAccountTime int64
	}
}

type WxConf struct {
	AppId  string
	MchId  string
	Key    string
	PlanId int
	Domain string
}

func Load(app string) *Config {
	var conf = new(Config)
	if err := config.Load(app, conf); err != nil {
		panic(fmt.Sprintf("config load failed: %v", err))
	}
	return conf
}
