package main

import (
	"os"
	"os/signal"
	"syscall"
	"time"

	"creativematrix.com/beyondreading/app/api/payment/conf"
	"creativematrix.com/beyondreading/app/api/payment/http"
	"creativematrix.com/beyondreading/app/api/payment/pay"
	"creativematrix.com/beyondreading/app/api/payment/svc"
	"creativematrix.com/beyondreading/pkg/debug"
	"creativematrix.com/beyondreading/pkg/ecode"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/middlewares"
	"creativematrix.com/beyondreading/pkg/tracer"

	_ "creativematrix.com/beyondreading/app/api/payment/pay/alipay"
	_ "creativematrix.com/beyondreading/app/api/payment/pay/apple"
	_ "creativematrix.com/beyondreading/app/api/payment/pay/weixin"
)

const (
	app = "api-payment"
)

func main() {
	config := conf.Load(app)
	pay.Load(config)

	logger.InitLog(app, config.Log.Level)
	tracer.InitTracing(config.Base, app)
	service := svc.Load(config)
	ecode.Init(&config.Base)
	middlewares.Init(&config.Base)

	http.Start(config, service)
	debug.Start(config.Base, service)
	logger.LogInfo(app, "service started listen on ", config.Port.HTTP)

	c := make(chan os.Signal, 1)
	signal.Notify(c, syscall.SIGHUP, syscall.SIGQUIT, syscall.SIGTERM, syscall.SIGINT)
	for {
		s := <-c
		switch s {
		case syscall.SIGQUIT, syscall.SIGTERM, syscall.SIGINT:
			logger.LogWarnw("api payment service exit")
			service.Close()

			time.Sleep(time.Second * 5)
			return
		case syscall.SIGHUP:
		default:
			return
		}
	}
}
