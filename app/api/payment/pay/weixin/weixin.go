package weixin

import (
	"encoding/json"
	"encoding/xml"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
	"time"

	"creativematrix.com/beyondreading/app/api/payment/conf"
	"creativematrix.com/beyondreading/app/api/payment/model"
	"creativematrix.com/beyondreading/app/api/payment/pay"
	"creativematrix.com/beyondreading/pkg/logger"
)

func init() {
	pay.Register(&Weixin{})
}

type Weixin struct {
	apps         map[string]*conf.WxConf
	notifyURL    string
	returnDomain string
	client       *http.Client
}

func (w *Weixin) Name() model.PayType {
	return model.CPT_WEIXIN
}

func (w *Weixin) Table() string {
	return pay.TABLE_WEIXIN
}

func (w *Weixin) Init(cfg *conf.Config) (err error) {
	w.apps = cfg.Weixinpay
	w.notifyURL = cfg.NotifyURL + w.Name().String()
	w.returnDomain = cfg.ReturnDomain
	w.client = http.DefaultClient

	return nil
}

func (w *Weixin) Pay(order *model.ChargeOrder, product *model.PayProductModel, op *model.OrderParam) (*pay.PayOrder, error) {
	var app *conf.WxConf
	if op.Channel != "" {
		app = w.apps[op.Channel]
	} else {
		app = w.apps[op.Platform]
	}
	if app == nil {
		return nil, model.WeixinAppInvalid
	}

	var param Param
	//body := "心恋:" + product.Name
	body := op.Subject

	uo := UnifiedOrder{
		Body:           body,
		OutTradeNo:     pay.Scramble(order.ID),
		TotalFee:       int(order.Price),
		SpbillCreateIP: op.SpbillCreateIp,
		TimeStart:      time.Now().Format(timeFormat),
		TimeExpire:     time.Now().Add(15 * time.Minute).Format(timeFormat),
		NotifyURL:      w.notifyURL,
		NonceStr:       GetNonceStr(),
		TradeType:      op.TradeType,
		Openid:         op.OpenID,
	}
	param = uo

	param.Payload()

	var agreement map[string]interface{}
	var res PreOrderRes
	if err := w.doRequest(&res, param, app); err != nil {
		return nil, err
	}

	var returnDomain string
	if app.Domain != "" {
		returnDomain = app.Domain
	} else {
		returnDomain = w.returnDomain
	}

	var info = pay.Payload{}

	switch op.TradeType {
	case pay.TRADE_TYPE_APP:
		if res.PreEntrustwebID != "" {
			info.Set("pre_entrustweb_id", res.PreEntrustwebID)
			info.Set("appid", app.AppId)
		} else {
			info.Set("appid", app.AppId)
			info.Set("noncestr", GetNonceStr())
			info.Set("partnerid", app.MchId)
			info.Set("prepayid", res.PrepayID)
			info.Set("package", "Sign=WXPay")
			info.Set("timestamp", fmt.Sprintf("%d", time.Now().Unix()))
			info.Set("sign", md5Sign(info, app.Key))
		}
	case pay.TRADE_TYPE_JSAPI:
		info.Set("appId", app.AppId)
		info.Set("timeStamp", fmt.Sprintf("%d", time.Now().Unix()))
		info.Set("nonceStr", GetNonceStr())
		info.Set("package", "prepay_id="+res.PrepayID)
		info.Set("signType", "MD5")
		info.Set("sign", md5Sign(info, app.Key))
	case pay.TRADE_TYPE_MWEB:
		// 此处拼接上redirect_url用于支付完成跳转
		if op.ReturnPath != "" {
			u := url.Values{}
			u.Set("redirect_url", returnDomain+op.ReturnPath)
			res.MWebURL = res.MWebURL + "&" + u.Encode()
		}
		info.Set("mweb_url", res.MWebURL)
	case pay.TRADE_TYPE_NATIVE:
		info.Set("code_url", res.CodeURL)
	}
	infoStr, _ := json.Marshal(info)

	po := pay.PayOrder{
		Value:     string(infoStr),
		Order:     w.presist(uo.Payload(), res, app),
		Agreement: agreement,
		Type:      0,
	}

	return &po, nil
}

func (w *Weixin) presist(pl pay.Payload, res PreOrderRes, app *conf.WxConf) map[string]interface{} {
	return map[string]interface{}{
		"appid":          app.AppId,
		"mchId":          app.MchId,
		"body":           pl.Get("body"),
		"outTradeNo":     pl.Get("out_trade_no"),
		"totalFee":       pl.Get("total_fee"),
		"spbillCreateIp": pl.Get("spbill_create_ip"),
		"timeStart":      pl.Get("time_start"),
		"timeExpire":     pl.Get("time_expire"),
		"tradeType":      pl.Get("trade_type"),
		"notifyUrl":      pl.Get("notify_url"),
		"prepayId":       res.PrepayID,
	}
}

func (w *Weixin) doRequest(result interface{}, param Param, app *conf.WxConf) error {
	p := param.Payload()

	p.Set("appid", app.AppId)
	p.Set("mch_id", app.MchId)
	p.Set("sign", md5Sign(p, app.Key))

	fmt.Println("[微信 POST XML]", p.ToXML())
	req, err := http.NewRequest("POST", param.APIURL(), strings.NewReader(p.ToXML()))
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/xml;charset=utf-8")

	resp, err := w.client.Do(req)
	if resp != nil {
		defer resp.Body.Close()
	}
	if err != nil {
		return err
	}

	data, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	logger.LogInfow("pay unifiedorder", "message", string(data))

	//if err := w.verifyResponse(data, app.Key); err != nil {
	//	return err
	//}

	return xml.Unmarshal(data, result)
}

func (w *Weixin) Notify(req *http.Request) (*pay.NotifyOrder, error) {
	body, err := ioutil.ReadAll(req.Body)
	if err != nil {
		return nil, err
	}
	fmt.Println(">>>>>>Notify body", string(body))
	if err := w.verifyResponse(body, ""); err != nil {
		return nil, err
	}

	var notify TradeNotification
	if err := xml.Unmarshal(body, &notify); err != nil {
		return nil, err
	}

	if notify.ResultCode == "FAIL" {
		return &pay.NotifyOrder{
			Type: pay.NT_TRADE_ERR,
			ID:   pay.DeScramble(notify.OutTradeNo),
			Data: map[string]interface{}{
				"reason": fmt.Sprintf("%s:%s", notify.ErrCode, notify.ErrCodeDes),
			},
		}, nil
	}

	if notify.TradeType != "" || notify.TradeState == "SUCCESS" {
		return &pay.NotifyOrder{
			Type: pay.NT_TRADE,
			ID:   pay.DeScramble(notify.OutTradeNo),
			Data: map[string]interface{}{
				"transactionId": notify.TransactionID,
			},
		}, nil
	}

	var cNotify ContractNotification
	if err := xml.Unmarshal(body, &cNotify); err != nil {
		return nil, err
	}

	no := new(pay.NotifyOrder)

	switch cNotify.ChangeType {
	case "ADD":
		no.AccountID = cNotify.ContractCode
		no.Data = map[string]interface{}{
			"agNo":    cNotify.ContractID,
			"payType": w.Name(),
		}
		no.Type = pay.NT_SIGN
	case "DELETE":
		no.AccountID = cNotify.ContractCode
		no.Type = pay.NT_UNSIGN
	default:
		return nil, model.NotifyTypeError
	}
	return no, nil
}

func (w *Weixin) AckNotify(wr http.ResponseWriter, err error) {
	var p = pay.Payload{}
	if err != nil {
		logger.LogErrorw("weixin notify", "err", err)
		p.Set("return_code", "FAIL")
		p.Set("return_msg", err.Error())
	} else {
		p.Set("return_code", "SUCCESS")
		p.Set("return_msg", "OK")
	}
	wr.Write([]byte(p.ToXML()))
}

func (w *Weixin) verifyResponse(data []byte, key string) error {
	var p = make(pay.Payload)
	if err := xml.Unmarshal(data, &p); err != nil {
		return err
	}
	code := p.Get("return_code")
	if code == "FAIL" {
		return errors.New(p.Get("return_msg"))
	}
	// code = p.Get("result_code")
	// if code == "FAIL" {
	// 	return errors.New(p.Get("err_code"))
	// }

	if key == "" {
		appID := p.Get("appid")
		mchID := p.Get("mch_id")

		for _, app := range w.apps {
			if app.AppId == appID && app.MchId == mchID {
				key = app.Key
				break
			}

		}

		if key == "" && appID != "" {
			for _, app := range w.apps {
				if app.AppId == appID {
					key = app.Key
					break
				}
			}
		}
		if key == "" && mchID != "" {
			for _, app := range w.apps {
				if app.MchId == mchID {
					key = app.Key
					break
				}
			}
		}
	}

	// check sign
	sign := p.Get("sign")
	delete(p, "sign")

	if sign == "" {
		return model.SignError
	}
	if sign2 := md5Sign(p, key); sign != sign2 {
		return model.SignError
	}
	return nil
}
