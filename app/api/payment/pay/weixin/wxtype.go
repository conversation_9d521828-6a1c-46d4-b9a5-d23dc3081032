package weixin

import (
	"math/rand"
	"time"

	"creativematrix.com/beyondreading/app/api/payment/pay"
)

const (
	timeFormat = "20060102150405"
)

type Param interface {
	APIURL() string
	Payload() pay.Payload
}

func GetNonceStr() (nonceStr string) {
	chars := "abcdefghijklmnopqrstuvwxyz0123456789"
	var r = rand.New(rand.NewSource(time.Now().UnixNano()))
	for i := 0; i < 32; i++ {
		idx := r.Intn(len(chars) - 1)
		nonceStr += chars[idx : idx+1]
	}
	return nonceStr
}
