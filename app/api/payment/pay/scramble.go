package pay

import (
	"github.com/speps/go-hashids"
)

const (
	salt     = "4081d23d494dc9258c4fdf32162e26e03e831806b50f771c6f1712f575b1732d"
	scramble = 12
)

var hid *hashids.HashID

func init() {
	hd := hashids.NewData()
	hd.Salt = salt
	hid, _ = hashids.NewWithData(hd)
}

func Scramble(id int64) string {

	hash, _ := hid.EncodeInt64([]int64{id, scramble})

	return hash
}

func DeScramble(hash string) int64 {
	ids := hid.DecodeInt64(hash)

	if len(ids) > 0 {
		return ids[0]
	}
	return -1
}
