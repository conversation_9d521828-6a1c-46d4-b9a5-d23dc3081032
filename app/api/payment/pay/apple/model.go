package apple

type Environment string

const (
	Sandbox    Environment = "Sandbox"
	Production Environment = "Production"
)

type (
	IAPRequest struct {
		ReceiptData string `json:"receipt-data"`
		//Password               string `json:"password,omitempty"`
		//ExcludeOldTransactions bool   `json:"exclude-old-transactions"`
	}

	InApp struct {
		Quantity              string `json:"quantity"`
		ProductID             string `json:"product_id"`
		TransactionID         string `json:"transaction_id"`
		OriginalTransactionID string `json:"original_transaction_id"`
		WebOrderLineItemID    string `json:"web_order_line_item_id,omitempty"`
		IsTrialPeriod         string `json:"is_trial_period"`
		IsInIntroOfferPeriod  string `json:"is_in_intro_offer_period,omitempty"`
		ExpiresDate           string `json:"expires_date"`
		// 大于当前时间，订阅处于活跃状态。
		ExpiresDateMs  string `json:"expires_date_ms"`
		PurchaseDateMs string `json:"purchase_date_ms"`
		// 如果存在，订阅被取消（只有当用户联系 Apple 客户支持立即取消订阅退款，才会出现）
		CancellationDateMs     string `json:"cancellation_date_ms"`
		OriginalPurchaseDateMs string `json:"original_purchase_date_ms"`
	}

	Receipt struct {
		ReceiptType            string  `json:"receipt_type"`
		AdamID                 int64   `json:"adam_id"`
		BundleID               string  `json:"bundle_id"`
		ApplicationVersion     string  `json:"application_version"`
		DownloadID             int64   `json:"download_id"`
		InApp                  []InApp `json:"in_app"`
		CreationDateMs         string  `json:"receipt_creation_date_ms"`
		RequestDateMs          string  `json:"request_date_ms"`
		OriginalPurchaseDateMs string  `json:"original_purchase_date_ms"`
	}

	PendingRenewalInfo struct {
		ExpirationIntent   string `json:"expiration_intent"`
		AutoRenewProductID string `json:"auto_renew_product_id"`
		// 0:只在失效的或者已取消的订阅中出现。1:尝试扣费，至60天，订阅可能不会失效
		RetryFlag             string `json:"is_in_billing_retry_period"`
		AutoRenewStatus       string `json:"auto_renew_status"`
		PriceConsentStatus    string `json:"price_consent_status"`
		ProductID             string `json:"product_id"`
		OriginalTransactionID string `json:"original_transaction_id"`
	}

	IAPResponse struct {
		Status             int                  `json:"status"`
		Environment        Environment          `json:"environment"`
		Receipt            Receipt              `json:"receipt"`
		LatestReceiptInfo  []InApp              `json:"latest_receipt_info,omitempty"`
		LatestReceipt      string               `json:"latest_receipt,omitempty"`
		PendingRenewalInfo []PendingRenewalInfo `json:"pending_renewal_info,omitempty"`
		IsRetryable        bool                 `json:"is-retryable,omitempty"`
	}

	Notification struct {
		Environment              Environment `json:"environment"`
		Type                     string      `json:"notification_type"`
		Password                 string      `json:"password"`
		CancellationDateMs       string      `json:"cancellation_date_ms"`
		LatestReceiptInfo        InApp       `json:"latest_receipt_info"`
		LatestReceipt            string      `json:"latest_receipt"`
		LatestExpiredReceiptInfo InApp       `json:"latest_expired_receipt_info"`
		LatestExpiredReceipt     string      `json:"latest_expired_receipt"`
		AutoRenewStatus          string      `json:"auto_renew_status"`
	}
)

func findInApp(inapps []InApp, id string) InApp {
	for _, ia := range inapps {
		if ia.TransactionID == id {
			return ia
		}
	}
	return InApp{}
}

//func filterInAppByOriginal(inapps []InApp, id string) []InApp {
//	ias := []InApp{}
//	for _, ia := range inapps {
//		if ia.OriginalTransactionID == id {
//			ias = append(ias, ia)
//		}
//	}
//	return ias
//}
