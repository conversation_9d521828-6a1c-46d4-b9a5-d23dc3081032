package apple

import (
	"fmt"

	"creativematrix.com/beyondreading/app/api/payment/model"
)

const (
	productVerifyURL = "https://buy.itunes.apple.com/verifyReceipt"
	sandboxVerifyURL = "https://sandbox.itunes.apple.com/verifyReceipt"
)

const (
	NOTIFY_TYPE_TRADE  = "trade"
	NOTIFY_TYPE_SIGN   = "sign"
	NOTIFY_TYPE_UNSIGN = "unsign"

	Product_Prefix = "kl_coin_%d"
)

func GenerateAppleID(order *model.ChargeOrder, product *model.PayProductModel) string {
	//return fmt.Sprintf("%s_%d_%d_%.2f", order.Platform, order.Price, 1, float32(product.Price))
	return fmt.Sprintf(Product_Prefix, product.ProductId)
}
