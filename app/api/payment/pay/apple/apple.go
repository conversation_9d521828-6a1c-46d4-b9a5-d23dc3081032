package apple

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strconv"
	"time"

	"creativematrix.com/beyondreading/app/api/payment/conf"
	"creativematrix.com/beyondreading/app/api/payment/model"
	"creativematrix.com/beyondreading/app/api/payment/pay"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/utils"
)

func init() {
	pay.Register(&Apple{})
}

type Apple struct {
	pwds             map[string]string
	isProduction     bool
	sandboxWhiteList []string
	canUseSanboxIds  []string
}

func (a *Apple) Name() model.PayType {
	return model.CPT_APPSTORE
}

func (a *Apple) Table() string {
	return pay.TABLE_APPSTORE
}

func (a *Apple) Init(cfg *conf.Config) (err error) {
	a.pwds = cfg.Appstore.Passwords
	a.isProduction = cfg.Appstore.IsProduction
	a.sandboxWhiteList = cfg.Appstore.SandboxWhiteList
	a.canUseSanboxIds = cfg.Appstore.CanUseSanboxIds
	return nil
}

func (a *Apple) Pay(order *model.ChargeOrder, product *model.PayProductModel, up *model.OrderParam) (*pay.PayOrder, error) {
	nano := time.Now().UnixNano() / 1000
	this := []byte(strconv.FormatInt(nano, 10))
	transactionID := fmt.Sprintf("temp_%v_%v", string(this[:13]), string(this[14:]))

	info := map[string]interface{}{
		"appleId": GenerateAppleID(order, product),
	}
	value, _ := json.Marshal(info)

	po := pay.PayOrder{
		Value: string(value),
		Order: map[string]interface{}{
			"receipt":       "temp_receipt",
			"transactionId": transactionID,
		},
	}

	return &po, nil
}

func (a *Apple) Notify(req *http.Request) (*pay.NotifyOrder, error) {
	if err := req.ParseForm(); err != nil {
		return nil, err
	}
	var (
		orderID       = req.Form.Get("orderId")
		receipt       = req.Form.Get("receipt")
		transactionID = req.Form.Get("transactionId")
		notifyType    = req.Form.Get("notifyType")
	)
	if orderID == "" || receipt == "" || transactionID == "" || notifyType == "" {
		return nil, model.BindError
	}
	if notifyType != NOTIFY_TYPE_TRADE {
		return nil, model.NotifyTypeError
	}

	fmt.Println(receipt)
	body := IAPRequest{
		ReceiptData: receipt,
	}
	iapRes, useSandbox, err := a.verify(body)
	if err != nil {
		return nil, err
	}
	if iapRes.Status != 0 {
		return nil, model.AppleStatusError
	}

	return a.iapRes(iapRes, orderID, receipt, transactionID, useSandbox)
}

func (a Apple) iapRes(iapRes *IAPResponse, orderID, receipt, transactionID string, useSandbox bool) (*pay.NotifyOrder, error) {
	id, err := strconv.ParseInt(orderID, 10, 64)
	if err != nil {
		return nil, model.OrderIDError
	}

	no := &pay.NotifyOrder{ID: id}

	ia := findInApp(iapRes.Receipt.InApp, transactionID)
	if ia == (InApp{}) {
		return nil, model.AppleTransactionError
	}
	if a.isProduction && iapRes.Environment == Sandbox && !utils.Contains(a.canUseSanboxIds, ia.ProductID) {
		return nil, model.AppleStatusError
	}
	no.Data = map[string]interface{}{
		"transactionId": ia.TransactionID,
		"receipt":       receipt,
	}
	no.Type = pay.NT_TRADE
	no.UseSandbox = useSandbox
	return no, nil
}

func (a *Apple) AckNotify(w http.ResponseWriter, err error) {
	data := map[string]interface{}{
		"code": 200,
		"msg":  "执行成功",
		"ts":   time.Now().UnixNano() / 1e6,
		"data": true,
	}
	if err != nil && err != model.OrderDuplicate {
		data["msg"] = err
		data["code"] = -99999
		logger.LogErrorw("apple notify error", "err", err)
	}
	b, _ := json.Marshal(data)
	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.Write(b)
}

func (a *Apple) verify(body interface{}) (*IAPResponse, bool, error) {
	result := new(IAPResponse)
	useSandboxUrl := false

	if err := a.doRequest(body, result, productVerifyURL); err != nil {
		return nil, false, err
	}

	if result.Status == 21007 || result.Status == 21004 {
		if err := a.doRequest(body, result, sandboxVerifyURL); err != nil {
			return nil, false, err
		}
		useSandboxUrl = true
	}

	return result, useSandboxUrl, nil
}

func (a *Apple) doRequest(body, result interface{}, url string) error {
	var b []byte
	if iap, ok := body.(IAPRequest); ok {
		receiptDat := fmt.Sprintf(`{"receipt-data": "%s"}`, iap.ReceiptData)
		b = []byte(receiptDat)
	} else {
		return nil
	}
	fmt.Println(string(b))
	//b, err := json.Marshal(body)
	//fmt.Println(string(b))
	//if err != nil {
	//	return err
	//}
	//b1 := []byte(`{
	//   "receipt-data":"MIIT5AYJKoZIhvcNAQcCoIIT1TCCE9ECAQExCzAJBgUrDgMCGgUAMIIDhQYJKoZIhvcNAQcBoIIDdgSCA3IxggNuMAoCAQgCAQEEAhYAMAoCARQCAQEEAgwAMAsCAQECAQEEAwIBADALAgEDAgEBBAMMATEwCwIBCwIBAQQDAgEAMAsCAQ8CAQEEAwIBADALAgEQAgEBBAMCAQAwCwIBGQIBAQQDAgEDMAwCAQoCAQEEBBYCNCswDAIBDgIBAQQEAgIAyzANAgENAgEBBAUCAwIlODANAgETAgEBBAUMAzEuMDAOAgEJAgEBBAYCBFAyNTYwFgIBAgIBAQQODAxjb20uaml4aXUuenQwGAIBBAIBAgQQHuFajFzAMBs6DjOzvxbivzAbAgEAAgEBBBMMEVByb2R1Y3Rpb25TYW5kYm94MBwCAQUCAQEEFFeuQdokFSZNoFxPlm6inu497GSLMB4CAQwCAQEEFhYUMjAyMS0wOC0yNVQxMzo1MjoxMVowHgIBEgIBAQQWFhQyMDEzLTA4LTAxVDA3OjAwOjAwWjBGAgEHAgEBBD5BfOG6xA5dyvEeH0iZoi+IblVVQpEynUSAHv7Z88vTuJXDDuNCOPW\/aFLdvrOZ3IUKj5IQogoIhbxXTIgVFTBhAgEGAgEBBFl7ED40PXiq+G0A8XlPx+NvA46rpwrBEojLrCQdQYDahIDHE1AwXTe+RX7MCF\/6fbUcDQ2PJ+Krgt6lNn8SayVC2wbevDh7YXsJzEzJvClKwnQRdGenGA1s3DCCAWICARECAQEEggFYMYIBVDALAgIGrAIBAQQCFgAwCwICBq0CAQEEAgwAMAsCAgawAgEBBAIWADALAgIGsgIBAQQCDAAwCwICBrMCAQEEAgwAMAsCAga0AgEBBAIMADALAgIGtQIBAQQCDAAwCwICBrYCAQEEAgwAMAwCAgalAgEBBAMCAQEwDAICBqsCAQEEAwIBATAMAgIGrgIBAQQDAgEAMAwCAgavAgEBBAMCAQAwDAICBrECAQEEAwIBADAMAgIGugIBAQQDAgEAMBoCAgamAgEBBBEMD2NvbS5qaXhpdS56dDU1NjAbAgIGpwIBAQQSDBAxMDAwMDAwODY1OTk5NzMxMBsCAgapAgEBBBIMEDEwMDAwMDA4NjU5OTk3MzEwHwICBqgCAQEEFhYUMjAyMS0wOC0yNVQxMzo1MjoxMVowHwICBqoCAQEEFhYUMjAyMS0wOC0yNVQxMzo1MjoxMVqggg5lMIIFfDCCBGSgAwIBAgIIDutXh+eeCY0wDQYJKoZIhvcNAQEFBQAwgZYxCzAJBgNVBAYTAlVTMRMwEQYDVQQKDApBcHBsZSBJbmMuMSwwKgYDVQQLDCNBcHBsZSBXb3JsZHdpZGUgRGV2ZWxvcGVyIFJlbGF0aW9uczFEMEIGA1UEAww7QXBwbGUgV29ybGR3aWRlIERldmVsb3BlciBSZWxhdGlvbnMgQ2VydGlmaWNhdGlvbiBBdXRob3JpdHkwHhcNMTUxMTEzMDIxNTA5WhcNMjMwMjA3MjE0ODQ3WjCBiTE3MDUGA1UEAwwuTWFjIEFwcCBTdG9yZSBhbmQgaVR1bmVzIFN0b3JlIFJlY2VpcHQgU2lnbmluZzEsMCoGA1UECwwjQXBwbGUgV29ybGR3aWRlIERldmVsb3BlciBSZWxhdGlvbnMxEzARBgNVBAoMCkFwcGxlIEluYy4xCzAJBgNVBAYTAlVTMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApc+B\/SWigVvWh+0j2jMcjuIjwKXEJss9xp\/sSg1Vhv+kAteXyjlUbX1\/slQYncQsUnGOZHuCzom6SdYI5bSIcc8\/W0YuxsQduAOpWKIEPiF41du30I4SjYNMWypoN5PC8r0exNKhDEpYUqsS4+3dH5gVkDUtwswSyo1IgfdYeFRr6IwxNh9KBgxHVPM3kLiykol9X6SFSuHAnOC6pLuCl2P0K5PB\/T5vysH1PKmPUhrAJQp2Dt7+mf7\/wmv1W16sc1FJCFaJzEOQzI6BAtCgl7ZcsaFpaYeQEGgmJjm4HRBzsApdxXPQ33Y72C3ZiB7j7AfP4o7Q0\/omVYHv4gNJIwIDAQABo4IB1zCCAdMwPwYIKwYBBQUHAQEEMzAxMC8GCCsGAQUFBzABhiNodHRwOi8vb2NzcC5hcHBsZS5jb20vb2NzcDAzLXd3ZHIwNDAdBgNVHQ4EFgQUkaSc\/MR2t5+givRN9Y82Xe0rBIUwDAYDVR0TAQH\/BAIwADAfBgNVHSMEGDAWgBSIJxcJqbYYYIvs67r2R1nFUlSjtzCCAR4GA1UdIASCARUwggERMIIBDQYKKoZIhvdjZAUGATCB\/jCBwwYIKwYBBQUHAgIwgbYMgbNSZWxpYW5jZSBvbiB0aGlzIGNlcnRpZmljYXRlIGJ5IGFueSBwYXJ0eSBhc3N1bWVzIGFjY2VwdGFuY2Ugb2YgdGhlIHRoZW4gYXBwbGljYWJsZSBzdGFuZGFyZCB0ZXJtcyBhbmQgY29uZGl0aW9ucyBvZiB1c2UsIGNlcnRpZmljYXRlIHBvbGljeSBhbmQgY2VydGlmaWNhdGlvbiBwcmFjdGljZSBzdGF0ZW1lbnRzLjA2BggrBgEFBQcCARYqaHR0cDovL3d3dy5hcHBsZS5jb20vY2VydGlmaWNhdGVhdXRob3JpdHkvMA4GA1UdDwEB\/wQEAwIHgDAQBgoqhkiG92NkBgsBBAIFADANBgkqhkiG9w0BAQUFAAOCAQEADaYb0y4941srB25ClmzT6IxDMIJf4FzRjb69D70a\/CWS24yFw4BZ3+Pi1y4FFKwN27a4\/vw1LnzLrRdrjn8f5He5sWeVtBNephmGdvhaIJXnY4wPc\/zo7cYfrpn4ZUhcoOAoOsAQNy25oAQ5H3O5yAX98t5\/GioqbisB\/KAgXNnrfSemM\/j1mOC+RNuxTGf8bgpPyeIGqNKX86eOa1GiWoR1ZdEWBGLjwV\/1CKnPaNmSAMnBjLP4jQBkulhgwHyvj3XKablbKtYdaG6YQvVMpzcZm8w7HHoZQ\/Ojbb9IYAYMNpIr7N4YtRHaLSPQjvygaZwXG56AezlHRTBhL8cTqDCCBCIwggMKoAMCAQICCAHevMQ5baAQMA0GCSqGSIb3DQEBBQUAMGIxCzAJBgNVBAYTAlVTMRMwEQYDVQQKEwpBcHBsZSBJbmMuMSYwJAYDVQQLEx1BcHBsZSBDZXJ0aWZpY2F0aW9uIEF1dGhvcml0eTEWMBQGA1UEAxMNQXBwbGUgUm9vdCBDQTAeFw0xMzAyMDcyMTQ4NDdaFw0yMzAyMDcyMTQ4NDdaMIGWMQswCQYDVQQGEwJVUzETMBEGA1UECgwKQXBwbGUgSW5jLjEsMCoGA1UECwwjQXBwbGUgV29ybGR3aWRlIERldmVsb3BlciBSZWxhdGlvbnMxRDBCBgNVBAMMO0FwcGxlIFdvcmxkd2lkZSBEZXZlbG9wZXIgUmVsYXRpb25zIENlcnRpZmljYXRpb24gQXV0aG9yaXR5MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAyjhUpstWqsgkOUjpjO7sX7h\/JpG8NFN6znxjgGF3ZF6lByO2Of5QLRVWWHAtfsRuwUqFPi\/w3oQaoVfJr3sY\/2r6FRJJFQgZrKrbKjLtlmNoUhU9jIrsv2sYleADrAF9lwVnzg6FlTdq7Qm2rmfNUWSfxlzRvFduZzWAdjakh4FuOI\/YKxVOeyXYWr9Og8GN0pPVGnG1YJydM05V+RJYDIa4Fg3B5XdFjVBIuist5JSF4ejEncZopbCj\/Gd+cLoCWUt3QpE5ufXN4UzvwDtIjKblIV39amq7pxY1YNLmrfNGKcnow4vpecBqYWcVsvD95Wi8Yl9uz5nd7xtj\/pJlqwIDAQABo4GmMIGjMB0GA1UdDgQWBBSIJxcJqbYYYIvs67r2R1nFUlSjtzAPBgNVHRMBAf8EBTADAQH\/MB8GA1UdIwQYMBaAFCvQaUeUdgn+9GuNLkCm90dNfwheMC4GA1UdHwQnMCUwI6AhoB+GHWh0dHA6Ly9jcmwuYXBwbGUuY29tL3Jvb3QuY3JsMA4GA1UdDwEB\/wQEAwIBhjAQBgoqhkiG92NkBgIBBAIFADANBgkqhkiG9w0BAQUFAAOCAQEAT8\/vWb4s9bJsL4\/uE4cy6AU1qG6LfclpDLnZF7x3LNRn4v2abTpZXN+DAb2yriphcrGvzcNFMI+jgw3OHUe08ZOKo3SbpMOYcoc7Pq9FC5JUuTK7kBhTawpOELbZHVBsIYAKiU5XjGtbPD2m\/d73DSMdC0omhz+6kZJMpBkSGW1X9XpYh3toiuSGjErr4kkUqqXdVQCprrtLMK7hoLG8KYDmCXflvjSiAcp\/3OIK5ju4u+y6YpXzBWNBgs0POx1MlaTbq\/nJlelP5E3nJpmB6bz5tCnSAXpm4S6M9iGKxfh44YGuv9OQnamt86\/9OBqWZzAcUaVc7HGKgrRsDwwVHzCCBLswggOjoAMCAQICAQIwDQYJKoZIhvcNAQEFBQAwYjELMAkGA1UEBhMCVVMxEzARBgNVBAoTCkFwcGxlIEluYy4xJjAkBgNVBAsTHUFwcGxlIENlcnRpZmljYXRpb24gQXV0aG9yaXR5MRYwFAYDVQQDEw1BcHBsZSBSb290IENBMB4XDTA2MDQyNTIxNDAzNloXDTM1MDIwOTIxNDAzNlowYjELMAkGA1UEBhMCVVMxEzARBgNVBAoTCkFwcGxlIEluYy4xJjAkBgNVBAsTHUFwcGxlIENlcnRpZmljYXRpb24gQXV0aG9yaXR5MRYwFAYDVQQDEw1BcHBsZSBSb290IENBMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5JGpCR+R2x5HUOsF7V55hC3rNqJXTFXsixmJ3vlLbPUHqyIwAugYPvhQCdN\/QaiY+dHKZpwkaxHQo7vkGyrDH5WeegykR4tb1BY3M8vED03OFGnRyRly9V0O1X9fm\/IlA7pVj01dDfFkNSMVSxVZHbOU9\/acns9QusFYUGePCLQg98usLCBvcLY\/ATCMt0PPD5098ytJKBrI\/s61uQ7ZXhzWyz21Oq30Dw4AkguxIRYudNU8DdtiFqujcZJHU1XBry9Bs\/j743DN5qNMRX4fTGtQlkGJxHRiCxCDQYczioGxMFjsWgQyjGizjx3eZXP\/Z15lvEnYdp8zFGWhd5TJLQIDAQABo4IBejCCAXYwDgYDVR0PAQH\/BAQDAgEGMA8GA1UdEwEB\/wQFMAMBAf8wHQYDVR0OBBYEFCvQaUeUdgn+9GuNLkCm90dNfwheMB8GA1UdIwQYMBaAFCvQaUeUdgn+9GuNLkCm90dNfwheMIIBEQYDVR0gBIIBCDCCAQQwggEABgkqhkiG92NkBQEwgfIwKgYIKwYBBQUHAgEWHmh0dHBzOi8vd3d3LmFwcGxlLmNvbS9hcHBsZWNhLzCBwwYIKwYBBQUHAgIwgbYagbNSZWxpYW5jZSBvbiB0aGlzIGNlcnRpZmljYXRlIGJ5IGFueSBwYXJ0eSBhc3N1bWVzIGFjY2VwdGFuY2Ugb2YgdGhlIHRoZW4gYXBwbGljYWJsZSBzdGFuZGFyZCB0ZXJtcyBhbmQgY29uZGl0aW9ucyBvZiB1c2UsIGNlcnRpZmljYXRlIHBvbGljeSBhbmQgY2VydGlmaWNhdGlvbiBwcmFjdGljZSBzdGF0ZW1lbnRzLjANBgkqhkiG9w0BAQUFAAOCAQEAXDaZTC14t+2Mm9zzd5vydtJ3ME\/BH4WDhRuZPUc38qmbQI4s1LGQEti+9HOb7tJkD8t5TzTYoj75eP9ryAfsfTmDi1Mg0zjEsb+aTwpr\/yv8WacFCXwXQFYRHnTTt4sjO0ej1W8k4uvRt3DfD0XhJ8rxbXjt57UXF6jcfiI1yiXV2Q\/Wa9SiJCMR96Gsj3OBYMYbWwkvkrL4REjwYDieFfU9JmcgijNq9w2Cz97roy\/5U2pbZMBjM3f3OgcsVuvaDyEO2rpzGU+12TZ\/wYdV2aeZuTJC+9jVcZ5+oVK3G72TQiQSKscPHbZNnF5jyEuAF1CqitXa5PzQCQc3sHV1ITGCAcswggHHAgEBMIGjMIGWMQswCQYDVQQGEwJVUzETMBEGA1UECgwKQXBwbGUgSW5jLjEsMCoGA1UECwwjQXBwbGUgV29ybGR3aWRlIERldmVsb3BlciBSZWxhdGlvbnMxRDBCBgNVBAMMO0FwcGxlIFdvcmxkd2lkZSBEZXZlbG9wZXIgUmVsYXRpb25zIENlcnRpZmljYXRpb24gQXV0aG9yaXR5AggO61eH554JjTAJBgUrDgMCGgUAMA0GCSqGSIb3DQEBAQUABIIBAFr1boMUeM+AZ+lt9X7dsrgrBQHDQ9Q2X0y4uBKp9HU7Gb5ktBXmdxF\/l8XArafdTqoKQAl3ROXBLNnDy5QlJEIL+7NQ3ScYo8RQxv0fmT7kCFilBxFASmThxKpCvwBKpwBRiCEuF+TmDWNWh63YQOGCy0qC2lSJmm0ePxLMuHblidD4ab3q+UKvOgCLOUVYVFJHN04r\/NxiJMsd8UjKMAzDGA00a7LcC1FUtoaMb5IU+rX2ZdkK3WcFrZfuz5av3Fdgqek+UE1kCAR08yvvuACVC8ciC6XRnaZ1hm9zfkUGa6l1PUCisYygU3IPqHOIZbBXMvoYLqjR\/g2OryjMFnw="
	//}`)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(b))
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/json")
	res, err := http.DefaultClient.Do(req)
	if res != nil {
		defer res.Body.Close()
	}
	if err != nil {
		return err
	}
	data, err := ioutil.ReadAll(res.Body)
	if err != nil {
		return err
	}
	return json.Unmarshal(data, result)
}

func (a *Apple) CheckEnv(notify *Notification) bool {
	if a.isProduction && notify.Environment != Production {
		return false
	}
	for _, p := range a.pwds {
		if p == notify.Password {
			return true
		}
	}
	return false
}

func GenAutoRenewTransactionID(ia InApp) string {
	return ia.OriginalTransactionID + ia.PurchaseDateMs + ia.ExpiresDateMs
}
