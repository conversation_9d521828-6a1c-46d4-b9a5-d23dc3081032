package alipay

type Trade struct {
	// extend
	NotifyURL string `json:"-"`
	ReturnURL string `json:"-"`

	// required
	Subject     string `json:"subject"`      // 商品的标题/交易标题/订单标题/订单关键字等。
	OutTradeNo  string `json:"out_trade_no"` // 商户网站唯一订单号
	TotalAmount string `json:"total_amount"` // 订单总金额，单位为元，精确到小数点后两位，取值范围[0.01,100000000]

	Body             string `json:"body"`                        // 对一笔交易的具体描述信息。
	TimeoutExpress   string `json:"timeout_express"`             // 该笔订单允许的最晚付款时间，逾期将关闭交易。
	ProductCode      string `json:"product_code,omitempty"`      // 销售产品码，商家和支付宝签约的产品码
	GoodsType        string `json:"goods_type,omitempty"`        // 商品主类型 :0-虚拟类商品,1-实物类商品
	PromoParams      string `json:"promo_params,omitempty"`      // 优惠参数 注：仅与支付宝协商后可用
	PassbackParams   string `json:"passback_params,omitempty"`   // 公用回传参数
	MerchantOrderNo  string `json:"merchant_order_no,omitempty"` // 商户原始订单号，最大长度限制32位
	SpecifiedChannel string `json:"specified_channel,omitempty"`
	BusinessParams   string `json:"business_params,omitempty"` // 商户传入业务信息，具体值要和支付宝约定，应用于安全，营销等参数直传场景，格式为json格式
}

type TradeAppPay struct {
	Trade
	TimeExpire          string               `json:"time_expire,omitempty"` // 绝对超时时间，格式为yyyy-MM-dd HH:mm。
	AgreementSignParams *AgreementSignParams `json:"agreement_sign_params,omitempty"`
}

func (t TradeAppPay) APIName() string {
	return "alipay.trade.app.pay"
}

func (t TradeAppPay) BizJSON() string {
	return marshal(t)
}

func (t TradeAppPay) Extends() map[string]string {
	return map[string]string{
		"notify_url": t.NotifyURL,
	}
}

type AgreementSignParams struct {
	NotifyURL string `json:"-"`

	ProductCode         string `json:"product_code,omitempty"`
	PersonalProductCode string `json:"personal_product_code"`
	SignScene           string `json:"sign_scene"`
	ExternalAgreementNo string `json:"external_agreement_no"`
	ExternalLogonID     string `json:"external_logon_id,omitempty"`
	AccessParams        struct {
		Channel string `json:"channel"`
	} `json:"access_params"`
	PeriodRuleParams struct {
		PeriodType   string `json:"period_type"`
		Period       int32  `json:"period"`
		ExecuteTime  string `json:"execute_time"`
		SingleAmount string `json:"single_amount"`
	} `json:"period_rule_params"`
	SubMerchant *SubMerchant `json:"sub_merchant,omitempty"`
}

type SubMerchant struct {
	ServiceDescription string `json:"sub_merchant_service_description,omitempty"`
}

func (a AgreementSignParams) APIName() string {
	return "alipay.user.agreement.page.sign"
}

func (a AgreementSignParams) BizJSON() string {
	return marshal(a)
}

func (a AgreementSignParams) Extends() map[string]string {
	return map[string]string{
		"notify_url": a.NotifyURL,
	}
}

type TradeWapPay struct {
	Trade
	QuitURL    string `json:"quit_url,omitempty"`
	AuthToken  string `json:"auth_token,omitempty"`
	TimeExpire string `json:"time_expire,omitempty"` // 绝对超时时间，格式为yyyy-MM-dd HH:mm。
}

func (t TradeWapPay) APIName() string {
	return "alipay.trade.wap.pay"
}

func (t TradeWapPay) BizJSON() string {
	return marshal(t)
}

func (t TradeWapPay) Extends() map[string]string {
	return map[string]string{
		"notify_url": t.NotifyURL,
		"return_url": t.ReturnURL,
	}
}

type TradePagePay struct {
	Trade
	TimeExpire string `json:"time_expire,omitempty"` // 绝对超时时间，格式为yyyy-MM-dd HH:mm。
}

func (t TradePagePay) APIName() string {
	return "alipay.trade.page.pay"
}

func (t TradePagePay) BizJSON() string {
	return marshal(t)
}

func (t TradePagePay) Extends() map[string]string {
	return map[string]string{
		"notify_url": t.NotifyURL,
		"return_url": t.ReturnURL,
	}
}

type TradePay struct {
	Trade

	Scene    string `json:"scene,omitempty"`
	AuthCode string `json:"auth_code,omitempty"`

	AgreementParams *AgreementParams `json:"agreement_params,omitempty"`
}

type AgreementParams struct {
	AgreementNo string `json:"agreement_no"`
}

func (t TradePay) APIName() string {
	return "alipay.trade.pay"
}

func (t TradePay) BizJSON() string {
	return marshal(t)
}

func (t TradePay) Extends() map[string]string {
	return map[string]string{
		"notify_url": t.NotifyURL,
	}
}

type AliRes struct {
	Code    string `json:"code"`
	Msg     string `json:"msg"`
	SubCode string `json:"sub_code"`
	SubMsg  string `json:"sub_msg"`
}

type TradePayRes struct {
	AlipayTradePay struct {
		AliRes
		TradeNo    string `json:"trade_no"`     // 支付宝交易号
		OutTradeNo string `json:"out_trade_no"` // 创建交易传入的商户订单号
	} `json:"alipay_trade_pay_response"`
	Sign string `json:"sign"`
}
