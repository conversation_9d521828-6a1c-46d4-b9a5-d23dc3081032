package svc

import (
	"context"
	"creativematrix.com/beyondreading/app/api/bookshelf/model/po"
	"creativematrix.com/beyondreading/app/api/bookshelf/model/vo"
	"creativematrix.com/beyondreading/pkg/logger"
	"gotest.tools/assert"
	"log"
	"testing"
	"time"
)

func TestBookshelfSvc_AddBookshelfBooks(t *testing.T) {
	logger.LogInfo(" start AddBookshelfBooks ")
	bookShelves := make([]*vo.BookToShelf, 0)
	n := time.Now()
	record1 := po.ReadRecord{
		//TocID:     primitive.NewObjectID().Hex(),
		TocName:   "tocName1",
		Title:     "exciting",
		WordIndex: 6666,
		Order:     1,
	}

	bookShelf := &vo.BookToShelf{
		BookId:        "68185f28582200008e003f72",
		Area:          0,
		Created:       &n,
		Updated:       &n,
		ReadRecord:    &record1,
		RecordUpdated: &n,
	}

	bookShelves = append(bookShelves, bookShelf)

	record2 := po.ReadRecord{
		//TocID:     primitive.NewObjectID().Hex(),
		TocName:   "tocName2",
		Title:     "funny",
		WordIndex: 8888,
		Order:     1,
	}

	bookShelf = &vo.BookToShelf{
		BookId:        "68185f47582200008e003f73",
		Area:          1,
		Created:       &n,
		Updated:       &n,
		ReadRecord:    &record2,
		RecordUpdated: &n,
	}

	bookShelves = append(bookShelves, bookShelf)

	req := &vo.AddBooksToShelfReq{
		AppName: "beyondreading",
		Version: "1.0",
		UserId:  "681f55778667ffc412e63f19", //primitive.NewObjectID().Hex()
		Books:   bookShelves,
	}

	resp, err := bsSvc.AddBooksToShelf(context.Background(), req)

	if err == nil {
		for _, state := range resp.Status {
			logger.Errorf("%v", state)
		}
	}
	assert.NilError(t, err)
}

func TestBookshelfSvc_RemoveBooksFromShelf(t *testing.T) {
	req := &vo.RemoveBooksFromShelfReq{
		AppName: "beyondreading",
		Version: "1.0",
		UserId:  "681f55778667ffc412e63f19", //primitive.NewObjectID().Hex()
		BookIds: []string{"68185f28582200008e003f72", "68185f47582200008e003f73"},
	}

	resp, err := bsSvc.RemoveBooksFromShelf(context.Background(), req)

	logger.Errorf("%v\n", resp)

	assert.NilError(t, err)
}

func TestBookshelfSvc_GetBookshelfBooks(t *testing.T) {

	req := &vo.GetBookshelfBooksReq{
		AppName: "beyondreading",
		Version: "1.0",
		UserId:  "681f55778667ffc412e63f19", //primitive.NewObjectID().Hex()
		BookIds: []string{"68185f28582200008e003f72", "68185f47582200008e003f73"},
	}

	got, err := bsSvc.GetBookshelfBooks(context.Background(), req)

	if err == nil {
		logger.LogInfo("%v", got)
	}
	assert.NilError(t, err)

}

func TestBookshelfSvc_GetAllBookshelfBooks(t *testing.T) {
	req := &vo.GetAllBookshelfBooksReq{
		AppName: "beyondreading",
		Version: "1.0",
		UserId:  "681f55778667ffc412e63f19",
	}

	got, err := bsSvc.GetAllBookshelfBooks(context.Background(), req)

	if err == nil {
		logger.LogInfo("%v", got)
	}
	assert.NilError(t, err)
}
