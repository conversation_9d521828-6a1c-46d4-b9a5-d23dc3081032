package vo

import "time"

// GetAccountReq 获取账户信息请求
type GetAccountReq struct {
	UserId uint64 `form:"userId" binding:"required"`
}

// AccountInfo 账户信息
type AccountInfo struct {
	AccountId         uint64     `json:"accountId"`
	UserId            uint64     `json:"userId"`
	CoinBalance       float64    `json:"coinBalance"`
	TotalRecharged    float64    `json:"totalRecharged"`
	TotalConsumed     float64    `json:"totalConsumed"`
	Status            int32      `json:"status"`
	UserType          int32      `json:"userType"`
	UserLevel         int32      `json:"userLevel"`
	VipExpireTime     *time.Time `json:"vipExpireTime,omitempty"`
	MonthlyExpireTime *time.Time `json:"monthlyExpireTime,omitempty"`
	CreatedAt         time.Time  `json:"createdAt"`
	UpdatedAt         time.Time  `json:"updatedAt"`
}

// GetAccountResp 获取账户信息响应
type GetAccountResp struct {
	Account *AccountInfo `json:"account"`
}

// CreateAccountReq 创建账户请求
type CreateAccountReq struct {
	UserId uint64 `json:"userId" binding:"required"`
}

// CreateAccountResp 创建账户响应
type CreateAccountResp struct {
	Account *AccountInfo `json:"account"`
}

// RechargeReq 充值请求
type RechargeReq struct {
	UserId        uint64  `json:"userId" binding:"required"`
	Amount        float64 `json:"amount" binding:"required"`
	PaymentMethod string  `json:"paymentMethod" binding:"required"`
	ExchangeRate  float32 `json:"exchangeRate,omitempty"`
}

// RechargeResp 充值响应
type RechargeResp struct {
	OrderId       string  `json:"orderId"`
	AccountId     uint64  `json:"accountId"`
	UserId        uint64  `json:"userId"`
	Amount        float64 `json:"amount"`
	CoinAmount    float64 `json:"coinAmount"`
	ExchangeRate  float32 `json:"exchangeRate"`
	PaymentMethod string  `json:"paymentMethod"`
	Status        int32   `json:"status"`
	CreatedAt     int64   `json:"createdAt"`
	UpdatedAt     int64   `json:"updatedAt"`
}

// GetAccountLogsReq 获取账户日志请求
type GetAccountLogsReq struct {
	UserId          uint64 `form:"userId" binding:"required"`
	Page            int32  `form:"page,omitempty"`
	PageSize        int32  `form:"pageSize,omitempty"`
	TransactionType string `form:"transactionType,omitempty"`
}

// AccountLogInfo 账户日志信息
type AccountLogInfo struct {
	LogId           uint64    `json:"logId"`
	AccountId       uint64    `json:"accountId"`
	UserId          uint64    `json:"userId"`
	TransactionType string    `json:"transactionType"`
	Amount          float64   `json:"amount"`
	BalanceBefore   float64   `json:"balanceBefore"`
	BalanceAfter    float64   `json:"balanceAfter"`
	OrderId         string    `json:"orderId,omitempty"`
	BookId          string    `json:"bookId,omitempty"`
	ChapterId       string    `json:"chapterId,omitempty"`
	Description     string    `json:"description,omitempty"`
	ExtraData       string    `json:"extraData,omitempty"`
	CreatedAt       time.Time `json:"createdAt"`
}

// GetAccountLogsResp 获取账户日志响应
type GetAccountLogsResp struct {
	Logs  []*AccountLogInfo `json:"logs"`
	Total int64             `json:"total"`
}

// UpdateUserStatusReq 更新用户状态请求
type UpdateUserStatusReq struct {
	UserId            uint64 `form:"userId" binding:"required"`
	UserType          int32  `json:"userType,omitempty"`
	VipExpireTime     int64  `json:"vipExpireTime,omitempty"`
	MonthlyExpireTime int64  `json:"monthlyExpireTime,omitempty"`
}

// UpdateUserStatusResp 更新用户状态响应
type UpdateUserStatusResp struct {
	Account *AccountInfo `json:"account"`
}

// CheckUserStatusReq 检查用户状态请求
type CheckUserStatusReq struct {
	UserId uint64 `form:"userId" binding:"required"`
}

// CheckUserStatusResp 检查用户状态响应
type CheckUserStatusResp struct {
	Account    *AccountInfo `json:"account"`
	HasVip     bool         `json:"hasVip"`
	HasMonthly bool         `json:"hasMonthly"`
}

// DeductCoinsReq 扣除书币请求
type DeductCoinsReq struct {
	UserId          uint64  `json:"userId" binding:"required"`
	Amount          float64 `json:"amount" binding:"required"`
	OrderId         string  `json:"orderId,omitempty"`
	BookId          string  `json:"bookId,omitempty"`
	ChapterId       string  `json:"chapterId,omitempty"`
	TransactionType string  `json:"transactionType" binding:"required"`
	Description     string  `json:"description,omitempty"`
}

// DeductCoinsResp 扣除书币响应
type DeductCoinsResp struct {
	Account *AccountInfo `json:"account"`
	LogId   uint64       `json:"logId"`
}
