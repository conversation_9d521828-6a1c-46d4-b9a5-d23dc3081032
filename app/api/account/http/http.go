package http

import (
	"creativematrix.com/beyondreading/app/api/account/svc"
	"github.com/gin-gonic/gin"
)

// Handler Account HTTP处理器
type Handler struct {
	svc *svc.AccountSvc
}

// New 创建HTTP处理器
func New(svc *svc.AccountSvc) *Handler {
	return &Handler{
		svc: svc,
	}
}

// RegisterRoutes 注册路由
func (h *Handler) RegisterRoutes(r *gin.Engine) {
	v1 := r.Group("/api/v1/account")
	{
		v1.GET("/info", h.GetAccount)
		v1.POST("/create", h.CreateAccount)
		v1.POST("/recharge", h.Recharge)
		v1.GET("/logs", h.GetAccountLogs)
		v1.PUT("/status", h.UpdateUserStatus)
		v1.GET("/status", h.CheckUserStatus)
		v1.POST("/deduct", h.DeductCoins)
	}
}
