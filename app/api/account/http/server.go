package http

import (
	"context"
	"net/http"
	"time"

	"creativematrix.com/beyondreading/app/api/account/conf"
	"creativematrix.com/beyondreading/app/api/account/svc"
	"creativematrix.com/beyondreading/pkg/logger"
	"github.com/gin-gonic/gin"
)

// Server HTTP服务器
type Server struct {
	srv *http.Server
}

// Start 启动HTTP服务器
func Start(c *conf.Config, svc *svc.AccountSvc) (*Server, error) {
	gin.SetMode(gin.ReleaseMode)
	r := gin.New()
	r.Use(gin.Logger())
	r.Use(gin.Recovery())

	handler := New(svc)
	handler.RegisterRoutes(r)

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})

	srv := &http.Server{
		Addr:    c.Port.HTTP,
		Handler: r,
	}

	go func() {
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.LogErrorf("Failed to start HTTP server: %v", err)
		}
	}()

	return &Server{srv: srv}, nil
}

// Close 关闭HTTP服务器
func (s *Server) Close() {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := s.srv.Shutdown(ctx); err != nil {
		logger.LogErrorf("HTTP server shutdown error: %v", err)
	}
}
