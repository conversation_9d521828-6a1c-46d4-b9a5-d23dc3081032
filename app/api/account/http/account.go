package http

import (
	"creativematrix.com/beyondreading/app/api/account/model/vo"
	"creativematrix.com/beyondreading/pkg/logger"
	"github.com/gin-gonic/gin"
	"net/http"
)

// GetAccount 获取账户信息
func (h *Handler) GetAccount(c *gin.Context) {
	var req vo.GetAccountReq
	if err := c.Should<PERSON>ind<PERSON>uery(&req); err != nil {
		logger.LogErrorf("GetAccount bind query error: %v", err)
		ecode.Back(c).Failure(err)
		return
	}

	resp, err := h.svc.GetAccount(c.Request.Context(), &req)
	if err != nil {
		logger.LogErrorf("GetAccount service error: %v", err)
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(data).Success()
}

// CreateAccount 创建账户
func (h *Handler) CreateAccount(c *gin.Context) {
	var req vo.CreateAccountReq
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.LogErrorf("CreateAccount bind json error: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.svc.CreateAccount(c.Request.Context(), &req)
	if err != nil {
		logger.LogErrorf("CreateAccount service error: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// Recharge 充值
func (h *Handler) Recharge(c *gin.Context) {
	var req vo.RechargeReq
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.LogErrorf("Recharge bind json error: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.svc.Recharge(c.Request.Context(), &req)
	if err != nil {
		logger.LogErrorf("Recharge service error: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// GetAccountLogs 获取账户日志
func (h *Handler) GetAccountLogs(c *gin.Context) {
	var req vo.GetAccountLogsReq
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.LogErrorf("GetAccountLogs bind query error: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	resp, err := h.svc.GetAccountLogs(c.Request.Context(), &req)
	if err != nil {
		logger.LogErrorf("GetAccountLogs service error: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// UpdateUserStatus 更新用户状态
func (h *Handler) UpdateUserStatus(c *gin.Context) {
	var req vo.UpdateUserStatusReq
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.LogErrorf("UpdateUserStatus bind query error: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	var bodyReq vo.UpdateUserStatusReq
	if err := c.ShouldBindJSON(&bodyReq); err != nil {
		logger.LogErrorf("UpdateUserStatus bind json error: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 合并query和body参数
	bodyReq.UserId = req.UserId

	resp, err := h.svc.UpdateUserStatus(c.Request.Context(), &bodyReq)
	if err != nil {
		logger.LogErrorf("UpdateUserStatus service error: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// CheckUserStatus 检查用户状态
func (h *Handler) CheckUserStatus(c *gin.Context) {
	var req vo.CheckUserStatusReq
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.LogErrorf("CheckUserStatus bind query error: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.svc.CheckUserStatus(c.Request.Context(), &req)
	if err != nil {
		logger.LogErrorf("CheckUserStatus service error: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// DeductCoins 扣除书币
func (h *Handler) DeductCoins(c *gin.Context) {
	var req vo.DeductCoinsReq
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.LogErrorf("DeductCoins bind json error: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.svc.DeductCoins(c.Request.Context(), &req)
	if err != nil {
		logger.LogErrorf("DeductCoins service error: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	c.JSON(http.StatusOK, resp)
}
