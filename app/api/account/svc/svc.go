package svc

import (
	"context"
	"creativematrix.com/beyondreading/app/api/account/conf"
	"creativematrix.com/beyondreading/app/api/account/dao"
)

type AccountSvc struct {
	conf          *conf.Config
	dao *dao.Dao
}

func Load(c *conf.Config) *AccountSvc {
	svc := &AccountSvc{
		conf:          c,
		dao:           dao.Load(c),
	}

	return svc
}

func (s *AccountSvc) Ping(ctx context.Context) error {
	return s.dao.Ping(ctx)
}

func (s *AccountSvc) Close() {
	s.dao.Close()
}
