package svc

import (
	"context"
	"creativematrix.com/beyondreading/app/api/account/model/vo"
	accountpb "creativematrix.com/beyondreading/proto/account"
)

func (s *AccountSvc) GetAccount(ctx context.Context, req *vo.GetAccountReq) (*vo.GetAccountResp, error) {
	pbReq := &accountpb.GetAccountReq{
		UserId: req.UserId,
	}

	pbResp, err := s.dao.AccountClient.GetAccount(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	resp := &vo.GetAccountResp{
		Account: s.convertAccountFromPB(pbResp.Account),
	}

	return resp, nil
}

func (s *AccountSvc) CreateAccount(ctx context.Context, req *vo.CreateAccountReq) (*vo.CreateAccountResp, error) {
	pbReq := &accountpb.CreateAccountReq{
		UserId: req.UserId,
	}

	pbResp, err := s.dao.AccountClient.CreateAccount(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	resp := &vo.CreateAccountResp{
		Account: s.convertAccountFromPB(pbResp.Account),
	}

	return resp, nil
}

func (s *AccountSvc) Recharge(ctx context.Context, req *vo.RechargeReq) (*vo.RechargeResp, error) {
	pbReq := &accountpb.RechargeReq{
		UserId:        req.UserId,
		Amount:        req.Amount,
		PaymentMethod: req.PaymentType,
		ExchangeRate:  req.ExchangeRate,
		Description:   req.Description,
	}

	pbResp, err := s.dao.AccountClient.Recharge(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	resp := &vo.RechargeResp{
		Account: s.convertAccountFromPB(pbResp.Account),
		LogId:   pbResp.LogId,
	}

	return resp, nil
}

func (s *AccountSvc) GetAccountLogs(ctx context.Context, req *vo.GetAccountLogsReq) (*vo.GetAccountLogsResp, error) {
	pbReq := &accountpb.GetAccountLogsReq{
		UserId:          req.UserId,
		Page:            req.Page,
		PageSize:        req.PageSize,
		TransactionType: req.TransactionType,
	}

	pbResp, err := s.accountClient.GetAccountLogs(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	var logs []*vo.AccountLog
	for _, log := range pbResp.Logs {
		logs = append(logs, s.convertAccountLogFromPB(log))
	}

	resp := &vo.GetAccountLogsResp{
		Logs:  logs,
		Total: pbResp.Total,
	}

	return resp, nil
}

func (s *AccountSvc) UpdateUserStatus(ctx context.Context, req *vo.UpdateUserStatusReq) (*vo.UpdateUserStatusResp, error) {
	pbReq := &accountpb.UpdateUserStatusReq{
		UserId:            req.UserId,
		UserType:          req.UserType,
		VipExpireTime:     req.VipExpireTime,
		MonthlyExpireTime: req.MonthlyExpireTime,
	}

	pbResp, err := s.dao.AccountClient.UpdateUserStatus(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	resp := &vo.UpdateUserStatusResp{
		Account: s.convertAccountFromPB(pbResp.Account),
	}

	return resp, nil
}

func (s *AccountSvc) CheckUserStatus(ctx context.Context, req *vo.CheckUserStatusReq) (*vo.CheckUserStatusResp, error) {
	pbReq := &accountpb.CheckUserStatusReq{
		UserId: req.UserId,
	}

	pbResp, err := s.dao.AccountClient.CheckUserStatus(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	resp := &vo.CheckUserStatusResp{
		Account:    s.convertAccountFromPB(pbResp.Account),
		HasVip:     pbResp.HasVip,
		HasMonthly: pbResp.HasMonthly,
	}

	return resp, nil
}

func (s *AccountSvc) DeductCoins(ctx context.Context, req *vo.DeductCoinsReq) (*vo.DeductCoinsResp, error) {
	pbReq := &accountpb.DeductCoinsReq{
		UserId:          req.UserId,
		Amount:          req.Amount,
		TransactionType: req.TransactionType,
		OrderId:         req.OrderId,
		BookId:          req.BookId,
		ChapterId:       req.ChapterId,
		Description:     req.Description,
	}

	pbResp, err := s.dao.AccountClient.DeductCoins(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	resp := &vo.DeductCoinsResp{
		Account: s.convertAccountFromPB(pbResp.Account),
	}

	return resp, nil
}
