package svc

import (
	"time"

	"creativematrix.com/beyondreading/app/api/account/model/vo"
	accountpb "creativematrix.com/beyondreading/proto/account"
)

func (s *AccountSvc) convertAccountFromPB(pbAccount *accountpb.AccountInfo) *vo.AccountInfo {
	if pbAccount == nil {
		return nil
	}

	account := &vo.AccountInfo{
		AccountId:      pbAccount.AccountId,
		UserId:         pbAccount.UserId,
		CoinBalance:    pbAccount.CoinBalance,
		TotalRecharged: pbAccount.TotalRecharged,
		TotalConsumed:  pbAccount.TotalConsumed,
		Status:         pbAccount.Status,
		UserType:       pbAccount.UserType,
		UserLevel:      pbAccount.UserLevel,
		CreatedAt:      time.Unix(pbAccount.CreatedAt, 0),
		UpdatedAt:      time.Unix(pbAccount.UpdatedAt, 0),
	}

	if pbAccount.VipExpireTime > 0 {
		vipExpireTime := time.Unix(pbAccount.VipExpireTime, 0)
		account.VipExpireTime = &vipExpireTime
	}

	if pbAccount.MonthlyExpireTime > 0 {
		monthlyExpireTime := time.Unix(pbAccount.MonthlyExpireTime, 0)
		account.MonthlyExpireTime = &monthlyExpireTime
	}

	return account
}

func (s *AccountSvc) convertAccountLogFromPB(pbLog *accountpb.AccountLog) *vo.AccountLogInfo {
	if pbLog == nil {
		return nil
	}

	log := &vo.AccountLogInfo{
		LogId:           pbLog.LogId,
		AccountId:       pbLog.AccountId,
		UserId:          pbLog.UserId,
		TransactionType: pbLog.TransactionType,
		Amount:          pbLog.Amount,
		BalanceBefore:   pbLog.BalanceBefore,
		BalanceAfter:    pbLog.BalanceAfter,
		OrderId:         pbLog.OrderId,
		BookId:          pbLog.BookId,
		ChapterId:       pbLog.ChapterId,
		Description:     pbLog.Description,
		ExtraData:       pbLog.ExtraData,
		CreatedAt:       time.Unix(pbLog.CreatedAt, 0),
	}

	return log
}
