package model

import (
	"bytes"
	"encoding/xml"
	"io"
	"net/url"
	"sort"
	"strings"
)

const (
	NT_TRADE      = iota + 1 // 交易通知
	NT_SIGN                  // 签约通知
	NT_UNSIGN                // 解约
	NT_TRADE_SIGN            // 交易加签约
	NT_TRADE_ERR             // 交易失败
)

type NotifyOrder struct {
	ID        int64
	AccountID string
	Type      int
	Data      map[string]interface{}
}

const (
	PT_TRADE = 0
	PT_SIGN  = 1
)

type PayOrder struct {
	Type      int
	Value     string
	Order     map[string]interface{}
	Agreement map[string]interface{}
}

type Payload map[string]string

func (p Payload) Set(key, value string) {
	p[key] = value
}

func (p Payload) Get(key string) string {
	return p[key]
}

func (p Payload) URLSignTemp() string {
	var list = make([]string, 0)
	for key, value := range p {
		if len(value) > 0 {
			list = append(list, key+"="+value)
		}
	}
	sort.Strings(list)
	return strings.Join(list, "&")
}

func (p Payload) URLEncode() string {
	var m = make(url.Values)
	for key, value := range p {
		m.Set(key, value)
	}
	return m.Encode()
}

func (p Payload) ToXML() string {
	var xb = &bytes.Buffer{}
	xb.WriteString("<xml>")
	for key, value := range p {
		if len(value) > 0 {
			xb.WriteString("<" + key + "><![CDATA[" + value + "]]></" + key + ">")
		}
	}
	xb.WriteString("</xml>")
	return xb.String()
}

type xmlMapEntry struct {
	XMLName xml.Name
	Value   string `xml:",chardata"`
}

func (p Payload) UnmarshalXML(d *xml.Decoder, start xml.StartElement) error {
	for {
		var e xmlMapEntry
		err := d.Decode(&e)
		if err == io.EOF {
			break
		} else if err != nil {
			return err
		}
		(p)[e.XMLName.Local] = e.Value
	}
	return nil
}

func (p Payload) ToInterfaceMap() map[string]interface{} {
	m := make(map[string]interface{})
	for k, v := range p {
		m[k] = v
	}
	return m
}
