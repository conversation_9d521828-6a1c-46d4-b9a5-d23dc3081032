package model

import "go.mongodb.org/mongo-driver/bson/primitive"

const (
	Redis_OrderUniqueIDKey      = "DI:mysqlUniqueId:chargeorders"
	Redis_OrderIDToAccountIDKey = "DI:uid:%d"

	MQ_EXPEND_BASE = "summer-q-expend-base"

	EVENT_RECHARGE = "recharge"

	CustomPayStartNum = 990000 // 自定义支付金额起始位
	PayTip            = "账户(%s)购买钻石；谨防诈骗，勿信刷单、代付等行为。"
)

type AccountMessage struct {
	AccountId primitive.ObjectID `json:"accountId"`
	Price     int64              `json:"price"`
}
