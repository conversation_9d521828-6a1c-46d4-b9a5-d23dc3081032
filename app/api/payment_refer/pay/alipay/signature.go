package alipay

import (
	"creativematrix.com/beyondreading/app/api/payment/model"
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
)

func RSA256Sign(content string, privateKey *rsa.PrivateKey) (string, error) {
	hashed := sha256.Sum256([]byte(content))
	sign, err := rsa.SignPKCS1v15(rand.Reader, privateKey, crypto.SHA256, hashed[:])
	if err != nil {
		return "", err
	}
	return base64.StdEncoding.EncodeToString(sign), nil
}

func RSACheck(src string, sign string, key *rsa.PublicKey, hash crypto.Hash) error {
	sig, err := base64.StdEncoding.DecodeString(sign)
	if err != nil {
		return err
	}

	var h = hash.New()
	h.Write([]byte(src))
	var hashed = h.Sum(nil)
	return rsa.VerifyPKCS1v15(key, hash, hashed, sig)
}

func ParsePrivateKey(data string) (*rsa.PrivateKey, error) {
	key, err := base64.StdEncoding.DecodeString(data)
	if err != nil {
		return nil, err
	}
	block, _ := pem.Decode(key)
	if block == nil {
		return nil, model.AlipayRSAKeyError
	}
	pk, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return nil, err
	}
	return pk, nil
}

func ParsePublicKey(data string) (*rsa.PublicKey, error) {
	key, err := base64.StdEncoding.DecodeString(data)
	if err != nil {
		return nil, err
	}
	block, _ := pem.Decode(key)
	if block == nil {
		return nil, model.AlipayRSAKeyError
	}
	pub, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return nil, err
	}
	pubk, ok := pub.(*rsa.PublicKey)
	if !ok {
		return nil, model.AlipayRSAKeyError
	}
	return pubk, nil
}
