package alipay

import (
	"crypto"
	"crypto/rsa"
	"fmt"
	"net/http"
	"sort"
	"strings"
	"time"

	"creativematrix.com/beyondreading/app/api/payment/conf"
	"creativematrix.com/beyondreading/app/api/payment/model"
	"creativematrix.com/beyondreading/app/api/payment/pay"
	"creativematrix.com/beyondreading/pkg/logger"
)

func init() {
	pay.Register(&Alipay{})
}

type Alipay struct {
	privateKey   *rsa.PrivateKey
	aliPublicKey *rsa.PublicKey
	notifyURL    string
	appID        string
	returnDomain string
	partner      string
	sellerID     string
}

func (a *Alipay) Name() model.PayType {
	return model.CPT_ALI
}

func (a *Alipay) Table() string {
	return pay.TABLE_ALI
}

func (a *Alipay) Init(cfg *conf.Config) (err error) {
	a.privateKey, err = ParsePrivateKey(cfg.Alipay.PrivateKey)
	if err != nil {
		return err
	}
	a.aliPublicKey, err = ParsePublicKey(cfg.Alipay.AliPublicKey)
	if err != nil {
		return err
	}
	a.notifyURL = cfg.NotifyURL + a.Name().String()
	a.partner = cfg.Alipay.Partner
	a.sellerID = cfg.Alipay.SellerId
	a.appID = cfg.Alipay.AppId
	a.returnDomain = cfg.ReturnDomain
	return nil
}

func (a *Alipay) Pay(order *model.ChargeOrder, product *model.PayProductModel, op *model.OrderParam) (*pay.PayOrder, error) {

	body := "心恋"
	subject := op.Subject

	// 计算支付链接绝对过期时间
	duration, _ := time.ParseDuration("30s")
	timeExpire := time.Now().Add(duration).Format("2006-01-02 15:04:05")

	trade := Trade{
		NotifyURL:      a.notifyURL,
		ReturnURL:      a.returnDomain + op.ReturnPath,
		TotalAmount:    fmt.Sprintf("%.2f", float64(order.Price)/100),
		OutTradeNo:     pay.Scramble(order.ID),
		Subject:        subject,
		Body:           body,
		TimeoutExpress: "1m",
	}

	payOrder := &pay.PayOrder{
		Order: map[string]interface{}{
			"partner":     a.partner,
			"sellerId":    a.sellerID,
			"body":        body,
			"totalFee":    float64(order.Price) / 100,
			"notifyUrl":   a.notifyURL,
			"outTradeNo":  trade.OutTradeNo,
			"subject":     subject,
			"itBPay":      trade.TimeoutExpress,
			"paymentType": "1",
		},
	}

	var param Param

	switch op.TradeType {
	case pay.TRADE_TYPE_MWEB:
		trade.ProductCode = PCODE_WAP
		param = TradeWapPay{Trade: trade, TimeExpire: timeExpire}
	case pay.TRADE_TYPE_NATIVE:
		trade.ProductCode = PCODE_PC
		trade.ReturnURL = pcReturnURL
		param = TradePagePay{Trade: trade, TimeExpire: timeExpire}
	default:
		tradeApp := TradeAppPay{Trade: trade, TimeExpire: timeExpire}
		param = tradeApp
	}

	p, err := a.URLValues(param)
	if err != nil {
		return nil, err
	}
	payOrder.Value = p.URLEncode()

	return payOrder, nil
}

func (a *Alipay) URLValues(param Param) (pay.Payload, error) {
	var p = pay.Payload{}

	p.Set("app_id", a.appID)
	p.Set("method", param.APIName())
	p.Set("charset", "utf-8")
	p.Set("sign_type", SIGN_TYPE_RSA2)
	p.Set("timestamp", time.Now().Format(cFormat))
	p.Set("version", "1.0")
	p.Set("biz_content", param.BizJSON())

	for k, v := range param.Extends() {
		p.Set(k, v)
	}

	var src = p.URLSignTemp()
	sign, err := RSA256Sign(src, a.privateKey)
	if err != nil {
		return nil, err
	}
	p.Set("sign", sign)
	return p, nil
}

func (a *Alipay) Notify(req *http.Request) (*pay.NotifyOrder, error) {
	if err := req.ParseForm(); err != nil {
		return nil, err
	}
	sign := req.Form.Get("sign")
	signType := req.Form.Get("sign_type")

	var pList = make([]string, 0)
	for key := range req.Form {
		if key == "sign" || key == "sign_type" {
			continue
		}
		value := strings.TrimSpace(req.Form.Get(key))
		if len(value) > 0 {
			pList = append(pList, key+"="+value)
		}
	}
	sort.Strings(pList)
	var src = strings.Join(pList, "&")

	hash := crypto.SHA256
	if signType == SIGN_TYPE_RSA {
		hash = crypto.SHA1
	}
	fmt.Println(src, sign, a.aliPublicKey, hash)
	//if err := RSACheck(src, sign, a.aliPublicKey, hash); err != nil {
	//	return nil, err
	//}

	no := new(pay.NotifyOrder)
	no.Data = make(map[string]interface{})

	switch nt := req.Form.Get("notify_type"); nt {
	case NOTIFY_TYPE_TRADE:
		status := req.Form.Get("trade_status")
		if status != "TRADE_SUCCESS" {
			return nil, model.AlipayStatusError
		}
		no.ID = pay.DeScramble(req.Form.Get("out_trade_no"))
		no.Type = pay.NT_TRADE
		no.Data["transactionId"] = req.Form.Get("trade_no")
	default:
		return nil, model.NotifyTypeError
	}
	return no, nil
}

func (a *Alipay) AckNotify(w http.ResponseWriter, err error) {
	if err == model.AlipayStatusError || err == nil {
		w.Write([]byte("success"))
	} else {
		logger.LogErrorw("alipay notify", "err", err)
		w.Write([]byte("fail"))
	}
}
