package pay

import (
	"net/http"

	"creativematrix.com/beyondreading/app/api/payment/conf"
	"creativematrix.com/beyondreading/app/api/payment/model"
)

var (
	mp = make(map[model.PayType]Payment)
)

func Register(p Payment) {
	mp[p.Name()] = p
}

func RegisterSub(pt model.PayType, p Payment) {
	mp[pt] = p
}

func Get(name model.PayType) Payment {
	if p, ok := mp[name]; ok {
		return p
	}
	return nil
}

const (
	TABLE_ALI      = "pay_ali"
	TABLE_WEIXIN   = "pay_weixin"
	TABLE_APPSTORE = "pay_appstore"
)

const (
	TRADE_TYPE_APP    = "APP"
	TRADE_TYPE_MWEB   = "MWEB"
	TRADE_TYPE_JSAPI  = "JSAPI"
	TRADE_TYPE_NATIVE = "NATIVE"
)

type Payment interface {
	Name() model.PayType
	Table() string
	Init(*conf.Config) error
	// 生成支付订单
	Pay(*model.ChargeOrder, *model.PayProductModel, *model.OrderParam) (*PayOrder, error)
	// 回调通知
	Notify(*http.Request) (*NotifyOrder, error)
	AckNotify(w http.ResponseWriter, err error)
}

func Load(cfg *conf.Config) {
	for k := range mp {
		if err := mp[k].Init(cfg); err != nil {
			panic(err)
		}
	}
}
