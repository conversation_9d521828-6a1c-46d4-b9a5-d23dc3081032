package http

import (
	"crypto/md5"
	"fmt"
	"strings"

	"creativematrix.com/beyondreading/pkg/router"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"

	"creativematrix.com/beyondreading/app/api/payment/model"
	"creativematrix.com/beyondreading/pkg/ecode"
	"creativematrix.com/beyondreading/pkg/utils"
)

var (
	key = "cLspc3m1"
)

func getProducts(c *gin.Context) {
	pp := new(model.ProductParam)
	if err := c.BindQuery(pp); err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	list, err := service.GetProducts(c, pp)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(list).Success()
}

func unifiedorder(c *gin.Context) {
	up := new(model.OrderParam)
	if err := c.MustBindWith(up, binding.Form); err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	// 时间校验

	// 签名校验
	var p = model.Payload{}
	for key, value := range c.Request.Form {
		if key == "sign" || key == "_t" {
			continue
		}
		if len(value) > 0 {
			p.Set(key, strings.TrimSpace(value[0]))
		}
	}
	p.Set("key", key)
	fmt.Println(p.URLSignTemp())
	fmt.Printf("%x\n", md5.Sum([]byte(p.URLSignTemp())))
	if up.Sign != fmt.Sprintf("%x", md5.Sum([]byte(p.URLSignTemp()))) {
		ecode.Back(c).Failure(model.SignError)
		return
	}
	if up.AccountID == "" {
		up.AccountID = c.GetString(router.UserKey)
	}
	if up.SpbillCreateIp == "" {
		up.SpbillCreateIp = utils.ClientIP(c.Request)
	}
	orderRes, err := service.Unifiedorder(c, up)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	// result := make(map[string]interface{})
	// result["orderId"] = orderRes.OrderId
	// result["orderId"] = orderRes.OrderId

	ecode.Back(c).SetData(orderRes).Success()
}

func getOrder(c *gin.Context) {
	fmt.Println("dsldf")

	// var result map[string]interface{}
	// result["status"] = "complated"

	ecode.Back(c).SetData(true).Success()
}

func notify(c *gin.Context) {
	pType := model.PayType(c.Param("type"))
	fmt.Println(pType)
	service.Notify(c, c.Writer, c.Request, pType)
}

func accountStatus(c *gin.Context) {
	as := new(model.AccountStatusParam)
	if as.AccountId == "" {
		as.AccountId = c.GetString(router.UserKey)
	}

	if err := c.BindQuery(as); err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	accountRes, err := service.AccountStatus(c, as.AccountId, utils.ClientIP(c.Request))
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(accountRes).Success()
}
