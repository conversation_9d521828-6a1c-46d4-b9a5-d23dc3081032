notifyURL = "http://lql-api-payment.dev_golang.91quliao.com/payment/notify/"
returnDomain = "http://devh5.91quliao.com/"

[alipay]
partner = "2088241633836086"
sellerId = "<EMAIL>"
appId = "2021002185632174"
appId2 = "2021002185609166"
privateKey = "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"
aliPublicKey = "LS0tLS1CRUdJTiBSU0EgUFJJVkFURSBLRVktLS0tLQpNSUlCSWpBTkJna3Foa2lHOXcwQkFRRUZBQU9DQVE4QU1JSUJDZ0tDQVFFQW1SZDB1akZUL1FqeFdMMUkzcFhDdWRCZ3NhdGUyMGFQamwzSU1qRnFoNjY5elVlMVpMNmVJd1dGdFRsaEkwUm80cm0vSktvWEM3L1lGMUtheFc3bElEdjN5OU95Z2FPT0NINDB3enRKTGYrNEl4MlB2SjVaUExZOStRZUlYTFpMZ05KaSsycmdrMXUvNGNBeWFDb0R5N3QvNlZ1c0dmRmZNSzY2K21oWkNnSDRYUU84WE41bDg0RkxsUTNvdHJPa0VOT2VjclBiekNLeUtiUjJpNjVMc2NObERwZ2NldUtVdkJXOVovdjN2QVVIU3p2elpXZCtZWlRuMzRSTytIdWhvbDY3aGtwQkpwY3k0czFjaHB6WGRKUjcvR2lZOGlrUXdiY0NGcGlSUjdNQkgzTU9zbS80N0ZHVUxuWVpuWHJZT1pYb1htRjhxNDJDTzlFSDZ1OHIxd0lEQVFBQgotLS0tLUVORCBSU0EgUFJJVkFURSBLRVktLS0tLQ=="

[appstore]
sandboxWhiteList = ["618dfb28022da80ffea27574", "6194ab9307cdafedd82e7d4c", "6166ca2ec096ab793e0bd451", "616946341b349e75646ed372"]  #沙盒验证白名单
canUseSanboxIds = ["kl_coin_1", "kl_coin_6", "kl_coin_12"]
[appstore.passwords]
101 = ""
103 = ""

[weixinpay]
[weixinpay.android]
appId = "wx3b849f74d2612b20"
mchId = "1614555460"
key = "********************************"
planId = 1
[weixinpay.xingxuan]
appId = "wx3b849f74d2612b20"
mchId = "1614555460"
key = "********************************"
planId = 1
domain = "http://devh5one.91quliao.com/"
[weixinpay.yijian]
appId = "wx3b849f74d2612b20"
mchId = "1614555460"
key = "********************************"
planId = 1
domain = "http://devh5one.17youni.com/"
[weixinpay.h5]
appId = "wxae894c03bcb67819"
mchId = "1614555460"
key = "********************************"
planId = 1


[chuanglan]
appId = "kGnxl4No"
appKey = "wO3vt2W6"
idCardUrl = "https://api.253.com/open/idcard/id-card-auth"

#声网sdk
[agora]
appId = "********************************"
secret = "df0cc5e0958a4158a067454e9fe7c8a5"
restHost = "https://api.agora.io"
restKey = "********************************"
restSecret = "2e66e3e269fa44fa940b6062ffe6d7af"

[dingPay]
dingPayToken = "a7aed397108ed37174bb1d316d4a9038f8850709a35f9a7e79700431a841cb62"
dingPayKey = "payment"
dingTime = 5
dingAccountTime = 2
