package model

const (
	Redis_UserTokenCache = "user:string:token:%s"
	Redis_UserNOIdCache  = "user:noId"

	//用户主页浏览
	Redis_SeeYouCache = "user:see:you:string:%s"

	RedisNewFocusLive = "live:focus:new:set:%s"

	RedisUserSmsCode  = "user:sms:code:%s"
	RedisUserSmsLimit = "user:sms:limit:%s:%s" // type,mobile
	RedisSmsLoginLock = "user:sms:login:%s"

	RedisVideoRequestId = "chat:requestId:video:string:%s"
	RedisAudioRequestId = "chat:requestId:audio:string:%s"

	RedisReviewRule = "zt:review:rule:hash" //审核策略

	RedisFaceNumLimit = "user:face:verify:limit%s:%s:%s" //头像审核列表
	RedisFaceIDGender = "user:face:verify:gender:%s"     //实名时缓存性别，用作校验

	RedisCodeRateKey = "route:cache:/user/sms/code"

	RedisMultiChatUserRoom = "multi:chat:str:user:%s" // [noId],用户当前多人连麦房间

	RedisAddDecoTimestamp   = "user:decorate:newest:time" //最新增加装扮时间
	RedisClickDecoTimestamp = "user:decorate:click:time"  //用户进入装扮中心最新时间

	RedisUserLocationGap = "user:location:lock:%s"
	RedisUserContact     = "user:official:contact" //官方联系方式

	RedisUserVisitors     = "user:visitors"
	RedisUserVisitorsLock = "user:visitors:%s"
	RedisGiftAtlasScore   = "gift:atlas:score:%d:%d" //礼物图鉴，年-周
)
