//Description 无法活体检测的实名认证
//Date        2021/6/15
//User        cl

package model

import "go.mongodb.org/mongo-driver/bson/primitive"

const TableRealName = "user_real_name"

// TRealName 无法活体检测的实名认证
type TRealName struct {
	Id         primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	NoId       string             `json:"noId" bson:"noId"`
	NickName   string             `json:"nickName" bson:"nickName"`
	Type       string             `json:"type" bson:"type"`
	RealName   string             `json:"realName" bson:"realName"`
	IdCard     string             `json:"idCard" bson:"idCard"`
	IdCardKey  string             `json:"idCardKey" bson:"idCardKey"`
	FrontImg   string             `json:"frontImg" bson:"frontImg"`
	BackImg    string             `json:"backImg" bson:"backImg"`
	HandleImg  string             `json:"handleImg" bson:"handleImg"`
	ReqTime    int64              `json:"reqTime" bson:"reqTime"`
	UpdateTime int64              `json:"updateTime" bson:"updateTime"`
	ReviewTime int64              `json:"reviewTime" bson:"reviewTime"`
	Status     string             `json:"status" bson:"status"`
	Reason     string             `json:"reason" bson:"reason"`
}

type RspRealName struct {
	RealName  string `json:"realName"`
	IdCard    string `json:"idCard"`
	FrontImg  string `json:"frontImg" bson:"frontImg"`
	BackImg   string `json:"backImg" bson:"backImg"`
	HandleImg string `json:"handleImg" bson:"handleImg"`
	Status    string `json:"status"`
}
