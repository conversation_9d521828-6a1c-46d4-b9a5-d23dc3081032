//Description 常量
//Date        2021/7/23
//User        cl

package model

import "time"

const (
	TimeAdd      = 8 * time.Hour
	TimeLayout   = "2006-01-02 15:04:05"
	DateLayout   = "2006-01-02"
	DateLayoutCN = "2006年01月02日"

	RealInit = "Plain"      //未认证
	Success  = "Pass"       //通过
	Process  = "Processing" //认证中
	Reject   = "Reject"     //认证失败

	SMPass   = "PASS"
	SMReview = "REVIEW"
	SMReject = "REJECT"

	Finished   = "Finished"   //完成
	UnFinished = "UnFinished" //未完成

	MQThird = "third"

	SmsTypeCheck = "checkOrigin" // 校验旧手机号

	RelateBoth    = "AB"
	UpdateEsQueue = "user-es-update"
	EsIsNew       = 0
	EsLocation    = 1
	EsInfo        = 2
	EsAlbum       = 5

	UserNotExist = 0
	UserExist    = 1
	Online       = 1 //在线状态
	UserValid    = 1 //正常状态用户
	NewUser      = 1

	LogoffApply  = "Apply"  //申请注销
	LogoffCancel = "Cancel" //取消注销
	LogoffFinish = "Finish" //注销完成

	FBWallet     = "wallet"     //意见反馈类型，充值提现
	FBChat       = "chat"       //私信聊天
	FBAccount    = "account"    //账号相关
	FBFirstPage  = "firstPage"  //首页推荐
	FBLive       = "live"       //语音与视频
	FBOther      = "other"      //其他
	FBAdult      = "adult"      //成人内容
	FBAdvert     = "advert"     //广告
	FBGovernment = "government" //政治
	FBSwear      = "swear"      //语言侮辱

	FBReport       = "report"  //举报类型
	FBSuggest      = "suggest" //反馈类型
	FBFamilyReport = "family"  // 家族举报

	ChatScene      = "chat"      //私聊
	VerifyMsgType  = "VerifyMsg" //认证状态消息
	VerifyCountMax = 10

	Avatar = "avatar"
	Album  = "album"
	Audio  = "audio"
	Real   = "real"

	UserNone     = "none"     //新注册
	UserFinish   = "finish"   //注册完成
	UserUnFinish = "unfinish" //注册未完成
	UserForbid   = "forbid"   //账号封禁
	UserLogoff   = "logoff"   //账号注销

	Male   = "male"
	Female = "female"

	SMSCodeFormat       = "【心恋】验证码:%s，10分钟有效"
	SMSCodeModifyFormat = "【心恋】验证码:%s，当前正在更换手机号，如非本人操作，请尽快登录APP查看"

	ScoutResign = "resign"

	FaceVerifyScore = 60

	AccountNormal   = "normal"
	AccountFreeze   = "freeze"
	AcFreezeForever = "freeze_forever"

	AcNormal  = 1
	AcForever = 2

	FaceHandle = "Handle" //手动提交
	FaceAuto   = "Auto"   //扫脸认证
	Click      = 1        //装扮红点需要点击
	UnClick    = 0        //装扮红点需要点击

	DecorateAvatar   = "avatar"      //头像类型装扮
	DecorateCard     = "card"        //名片类型装扮
	DecorateCar      = "car"         //座驾类型装扮
	DecorateEnter    = "enterRoom"   //进场类型装扮
	DecorateCover    = "liveCover"   //直播封面类型装扮
	DecorateVoice    = "voiceCircle" //声波框类型装扮
	DecorateBubble   = "liveBubble"  //直播气泡
	DecorateFont     = "fontColor"   //字体颜色
	DecorateNick     = "nickColor"   //昵称颜色
	DecorateMedal    = "medal"       //勋章
	DecorateCardInfo = "cardInfo"    //资料卡
	DecoratePrettyNo = "prettyNo"    //靓号

	DecoNew = "DecoNew" //商城上新状态
	DecoOld = "DecoOld" //已点击

	NameAdj       = "adjective"
	NamePart      = "particle"
	NameNoun      = "noun"
	DefaultAvatar = "male-default-avatar"

	AccountTitle   = "账号是否处于安全状态"
	AccountContent = "最近7天内没有更换过设备登录。"
	BalanceTitle   = "账号内无大额未消费或未提现的资产"
	BalanceContent = "账号内无未结清的欠款、大额资金和虚拟权益；注销后，账户中的虚拟权益、余额等视作自动放弃且无法恢复。"
	LawTitle       = "账号内无其他正在进行中的业务及争议纠纷"
	LawContent     = "本帐号及通过本帐号接入的第三方中已无其他正在进行中的经营性业务、无未完成的交易、无任何未处理完成的纠纷（包括但不限于投诉举报、被投诉举报、诉讼等任何纠纷争议）。"

	LogoffNotice = "您的账号已提交注销申请，将于%s注销成功。注销成功前，如你需要继续通过该账号登录使用心恋，请点击“放弃注销”，你的注销申请将终止；点击“了解”，你可通过其他账号进行登录。"

	MQBindAnchorEvent = "MQBindAnchor"
	MQLive            = "summer-q-live" //直播消息队列

	LockOK     = "OK"
	IPLocation = "location"
)

var Decs = []string{DecorateAvatar, DecorateCard, DecorateCar, DecorateEnter, DecorateCover, DecorateVoice,
	DecorateBubble, DecorateFont, DecorateNick, DecorateCardInfo}

var FocusMap = map[string]int{
	"NoANoB": 0,
	"ANoB":   1,
	"NoAB":   2,
	"AB":     3,
}

var FeedBackType = map[string]bool{
	FBWallet:     true,
	FBChat:       true,
	FBAccount:    true,
	FBFirstPage:  true,
	FBLive:       true,
	FBOther:      true,
	FBAdult:      true,
	FBAdvert:     true,
	FBGovernment: true,
	FBSwear:      true,
}

// 人脸认证常量
const (
	CompareFace   = "0"         //人脸比对类型
	CompareKYC    = "1"         //KYC比对类型
	SignVersion   = "hmac_sha1" //FaceID 签名加密方法
	FaceIDSuccess = 1000        //FaceID 验证成功code
	IDCardBucket  = "userreal"
)

const (
	AudioStatus = 1 << iota
	JobStatus
	IncomeStatus
	StarSignStatus
	HeightStatus
	IntroStatus
	AllInfo = 0xff
)

type Priority byte

const (
	NonePriority Priority = 0 //0代表不发送后台，1>2>3
	HighPriority Priority = 1
	MidPriority  Priority = 2
	LowPriority  Priority = 3
)

type CheckMod byte

const (
	RuleForbid CheckMod = 0
	RuleManual CheckMod = 1
	RuleSM     CheckMod = 2
	RuleAll    CheckMod = 3
)

const (
	MQELfEvent Event = "ELfEvent" //恋爱小精灵

	MQLiveRoomEvent = "MQLiveRoomEvent"
)

var (
	BaseUserField = []string{"_id", "noId", "nickName", "msgId", "avatarUrl", "city", "starSign", "age", "gender", "born"}
)
