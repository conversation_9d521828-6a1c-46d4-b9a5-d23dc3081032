//Description es index
//Date        2021/8/10
//User        cl

package model

import "github.com/olivere/elastic/v7"

const (
	Default = "default" //默认连接节点名称

	UserIndex = "usernew" //用户索引
)

// UserES 附近的人，存es的信息
type UserES struct {
	Id          string            `json:"id"`
	NoId        string            `json:"noId,omitempty"`
	NickName    string            `json:"nickName,omitempty"`
	Location    *elastic.GeoPoint `json:"location,omitempty"`
	Gender      string            `json:"gender,omitempty"`
	Born        string            `json:"born,omitempty"`
	Album       int               `json:"album"`
	Info        int               `json:"info,omitempty"`
	Online      int               `json:"online"`
	City        string            `json:"city,omitempty"`
	IsNew       bool              `json:"isNew"`
	IsReal      bool              `json:"isReal"`
	NewScore    int               `json:"newScore"`
	AvatarLevel string            `json:"avatarLevel,omitempty"`
	GroupCode   string            `json:"groupCode,omitempty"`
	LonLatType  string            `json:"lonLatType"`
	Device      *Device           `json:"device,omitempty"`
}

type UpdateES struct {
	Type      int     `json:"type"`
	Timestamp int64   `json:"timestamp"`
	Data      *UserES `json:"data"`
}

type EsUserGender struct {
	Gender string `json:"gender"`
}
