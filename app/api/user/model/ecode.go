package model

import "creativematrix.com/beyondreading/pkg/ecode"

var (
	TOKENERROR    = ecode.New(-11, "TOKEN_ERROR")
	RequestErr    = ecode.New(-400, "请求错误")
	NothingFound  = ecode.New(-404, "未找到")
	ServerErr     = ecode.New(-500, "服务器错误")
	ErrorArgument = ecode.New(-100000, "参数异常")
	SignError     = ecode.New(-10002, "签名错误")
	TimeoutErr    = ecode.New(-504, "请求超时")

	UserForbidErr = ecode.New(-10011, "您的账号已被封禁，无法进行登录")
	CodeRateMsg   = ecode.New(-10008, "验证码发送过于频繁，请稍后重试")
	SendCodeErr   = ecode.New(-10009, "验证码发送失败")

	RealNameRequired    = ecode.New(-13001, "真实姓名必须填写")
	IDNumRequired       = ecode.New(-13002, "身份证号必须填写")
	IDNumPatternErr     = ecode.New(-13003, "身份证号码格式不正确")
	RealNameAgeLimit    = ecode.New(-13004, "平台目前仅支持18-65周岁的成年人认证")
	RealNamePassAlready = ecode.New(-13005, "此身份证号已绑定于其他账号，暂不支持解绑、更换")
	RealNameApplying    = ecode.New(-13006, "该身份证正在审核中，请换张身份证")
	RealNameFaceNotPass = ecode.New(-13007, "实名认证人脸识别未通过")
	ThumbsUpAlready     = ecode.New(-13008, "您已经点过赞了")
	ThumbsUpSelf        = ecode.New(-13009, "不能给自己点赞")
	ImageTooLarge       = ecode.New(-13010, "图片大小不能超过4M")
	AudioHintNoMore     = ecode.New(-13011, "没有更多数据了")
	LogoffReApply       = ecode.New(-13012, "7天内仅可申请注销一次")
	LogoffAccountErr    = ecode.New(-13013, "您的钻石/星票账户目前处在冻结状态，不可注销账号")
	LogoffNoneErr       = ecode.New(-13014, "未申请注销，不能进行取消")
	AgeTooLow           = ecode.New(-13015, "平台暂不支持未成年用户使用")
	InvalidAvatar       = ecode.New(-13016, "您上传的头像不符合规定，请重新上传")
	InvalidAlbum        = ecode.New(-13017, "您上传的照片不符合规定，请重新上传")
	ForbidUploadImg     = ecode.New(-13018, "系统维护中，暂时无法上传头像")
	InvalidText         = ecode.New(-13019, "您的信息含有敏感词汇，请修改")
	ForbidUploadAlbum   = ecode.New(-13020, "系统维护中，暂时无法更新相册")
	FaceNotSame         = ecode.New(-13021, "头像与真人不符")
	FaceVerifyMuch      = ecode.New(-13022, "今日认证次数不能超过10次")
	ForbidUploadAudio   = ecode.New(-13023, "系统维护中，暂时无法更新语音签名")
	NeedNormalAvatar    = ecode.New(-13024, "您的头像正在审核中，暂无法真人认证")
	RealAvatarAlready   = ecode.New(-13025, "您已通过真人认证")
	RealInProgress      = ecode.New(-13026, "您已申请真人认证")
	RealNameAlready     = ecode.New(-13027, "您已通过实名认证")
	RealNameInProgress  = ecode.New(-13028, "您已申请实名认证")
	FaceNotRealName     = ecode.New(-13029, "身份信息与真人不符")
	NeedRealStatus      = ecode.New(-13030, "真人头像审核中，请勿更换头像")
	InvalidNickName     = ecode.New(-13031, "昵称长度不超过10，且不能包含特殊字符")
	InvalidIntro        = ecode.New(-13032, "个人签名不能包含特殊字符")
	UploadIDCardErr     = ecode.New(-13033, "身份证信息上传失败")
	IDCardMultiErr      = ecode.New(-13034, "该身份信息已被其他账号绑定~")
	RealNameImgErr      = ecode.New(-13035, "请上传身份证正反面和手持身份证图片")
	InvalidGroupCode    = ecode.New(-13038, "邀请码不正确")
	MobileLoginErr      = ecode.New(-13039, "手机号正在其他设备登录")
	AgeInvalid          = ecode.New(-13041, "您的实名认证条件不符合平台现行要求，如有疑问可联系客服")
	UserForbidUpdate    = ecode.New(-13042, "系统维护中，暂时无法更新个人信息")
	EmptyIntro          = ecode.New(-13049, "个人签名不能为空")

	ProveImgEmptyErr         = ecode.New(-13051, "证明材料不能为空")
	ApplyReasonEmptyErr      = ecode.New(-13052, "变更原因不能为空")
	NewMobileErr             = ecode.New(-13053, "新手机号不合法")
	OverChangeMobileLimitErr = ecode.New(-13055, "7天内，只允许修改1次手机号哦~")
	InterestNumOver          = ecode.New(-13056, "最多选择20个兴趣标签")

	SetUserRelation           = ecode.New(-40001, "修改用户关系失败")
	GetUsetRelationList       = ecode.New(-40002, "获取关注好友失败")
	GetUserDetail             = ecode.New(-40003, "获取用户详情失败")
	UserNotFound              = ecode.New(-40004, "未查询到用户信息")
	UpdateLonLat              = ecode.New(-40100, "同步地理位置失败")
	LoginInfoFinishInitError  = ecode.New(-40101, "用户已经完成注册初始化")
	LoginInfoRegInitError     = ecode.New(-40102, "用户注册初始化失败")
	GenderEqualError          = ecode.New(-40103, "性别相同")
	GenderSameNotABError      = ecode.New(-40104, "为防止骚扰互相关注后可聊天")
	LoginTypeError            = ecode.New(-41010, "登录类型异常")
	UserSmsMobileError        = ecode.New(-41100, "手机号错误")
	UserSmsMobileMoreError    = ecode.New(-41101, "您今日获取验证码数已达上限")
	UserSmsCheckError         = ecode.New(-41102, "验证码错误，请重试")
	UserWechatLoginError      = ecode.New(-41111, "微信登录unionId异常")
	UserQQLoginError          = ecode.New(-41112, "QQ登录unionId异常")
	UserFalshMobileQueryError = ecode.New(-41200, "闪验失败")
	UserFlashError            = ecode.New(-41201, "一键登录未成功，建议您使用验证码或其他方式登录")
	UserLogoffError           = ecode.New(-41202, "该用户已注销")
	UserAttentionCapOver      = ecode.New(-41203, "关注人数已达上限")
	UserSmsMobileRegError     = ecode.New(-41205, "虚拟号段不允许注册")
)
