package vo

import "time"

// RegisterBySmsReq 手机短信注册请求
type RegisterBySmsReq struct {
	Phone     string `json:"phone" binding:"required"`
	SmsCode   string `json:"smsCode" binding:"required"`
	Nickname  string `json:"nickname,omitempty"`
	ClientIp  string `json:"clientIp,omitempty"`
	UserAgent string `json:"userAgent,omitempty"`
	DeviceId  string `json:"deviceId,omitempty"`
}

// RegisterBySmsResp 手机短信注册响应
type RegisterBySmsResp struct {
	User  *UserInfo `json:"user"`
	Token string    `json:"token"`
}

// RegisterByGoogleReq Google账户注册请求
type RegisterByGoogleReq struct {
	GoogleToken string `json:"googleToken" binding:"required"`
	GoogleId    string `json:"googleId" binding:"required"`
	Email       string `json:"email,omitempty"`
	Nickname    string `json:"nickname,omitempty"`
	Avatar      string `json:"avatar,omitempty"`
	ClientIp    string `json:"clientIp,omitempty"`
	UserAgent   string `json:"userAgent,omitempty"`
	DeviceId    string `json:"deviceId,omitempty"`
}

// RegisterByGoogleResp Google账户注册响应
type RegisterByGoogleResp struct {
	User  *UserInfo `json:"user"`
	Token string    `json:"token"`
}

// RegisterByAppleReq Apple账户注册请求
type RegisterByAppleReq struct {
	AppleToken string `json:"appleToken" binding:"required"`
	AppleId    string `json:"appleId" binding:"required"`
	Email      string `json:"email,omitempty"`
	Nickname   string `json:"nickname,omitempty"`
	ClientIp   string `json:"clientIp,omitempty"`
	UserAgent  string `json:"userAgent,omitempty"`
	DeviceId   string `json:"deviceId,omitempty"`
}

// RegisterByAppleResp Apple账户注册响应
type RegisterByAppleResp struct {
	User  *UserInfo `json:"user"`
	Token string    `json:"token"`
}

// LoginBySmsReq 手机短信登录请求
type LoginBySmsReq struct {
	Phone     string `json:"phone" binding:"required"`
	SmsCode   string `json:"smsCode" binding:"required"`
	ClientIp  string `json:"clientIp,omitempty"`
	UserAgent string `json:"userAgent,omitempty"`
	DeviceId  string `json:"deviceId,omitempty"`
}

// LoginBySmsResp 手机短信登录响应
type LoginBySmsResp struct {
	User  *UserInfo `json:"user"`
	Token string    `json:"token"`
}

// LoginByGoogleReq Google账户登录请求
type LoginByGoogleReq struct {
	GoogleToken string `json:"googleToken" binding:"required"`
	GoogleId    string `json:"googleId" binding:"required"`
	ClientIp    string `json:"clientIp,omitempty"`
	UserAgent   string `json:"userAgent,omitempty"`
	DeviceId    string `json:"deviceId,omitempty"`
}

// LoginByGoogleResp Google账户登录响应
type LoginByGoogleResp struct {
	User  *UserInfo `json:"user"`
	Token string    `json:"token"`
}

// LoginByAppleReq Apple账户登录请求
type LoginByAppleReq struct {
	AppleToken string `json:"appleToken" binding:"required"`
	AppleId    string `json:"appleId" binding:"required"`
	ClientIp   string `json:"clientIp,omitempty"`
	UserAgent  string `json:"userAgent,omitempty"`
	DeviceId   string `json:"deviceId,omitempty"`
}

// LoginByAppleResp Apple账户登录响应
type LoginByAppleResp struct {
	User  *UserInfo `json:"user"`
	Token string    `json:"token"`
}

// GetUserInfoReq 获取用户信息请求
type GetUserInfoReq struct {
	UserId uint64 `form:"userId" binding:"required"`
}

// GetUserInfoResp 获取用户信息响应
type GetUserInfoResp struct {
	User *UserInfo `json:"user"`
}

// UpdateUserInfoReq 更新用户信息请求
type UpdateUserInfoReq struct {
	UserId   uint64 `json:"userId" binding:"required"`
	Nickname string `json:"nickname,omitempty"`
	Avatar   string `json:"avatar,omitempty"`
	Gender   int32  `json:"gender,omitempty"`
	Birthday string `json:"birthday,omitempty"`
	Location string `json:"location,omitempty"`
}

// UpdateUserInfoResp 更新用户信息响应
type UpdateUserInfoResp struct {
	User *UserInfo `json:"user"`
}

// GetLoginLogsReq 获取登录日志请求
type GetLoginLogsReq struct {
	UserId   uint64 `form:"userId" binding:"required"`
	Page     int32  `form:"page,omitempty"`
	PageSize int32  `form:"pageSize,omitempty"`
}

// GetLoginLogsResp 获取登录日志响应
type GetLoginLogsResp struct {
	Logs  []*LoginLogInfo `json:"logs"`
	Total int64           `json:"total"`
}

// SendSmsCodeReq 发送短信验证码请求
type SendSmsCodeReq struct {
	Phone    string `json:"phone" binding:"required"`
	CodeType int32  `json:"codeType" binding:"required"`
}

// SendSmsCodeResp 发送短信验证码响应
type SendSmsCodeResp struct {
	Message string `json:"message"`
}

// VerifySmsCodeReq 验证短信验证码请求
type VerifySmsCodeReq struct {
	Phone    string `json:"phone" binding:"required"`
	SmsCode  string `json:"smsCode" binding:"required"`
	CodeType int32  `json:"codeType" binding:"required"`
}

// VerifySmsCodeResp 验证短信验证码响应
type VerifySmsCodeResp struct {
	IsValid bool   `json:"isValid"`
	Message string `json:"message"`
}

// UserInfo 用户信息
type UserInfo struct {
	UserId            uint64     `json:"userId"`
	Phone             string     `json:"phone,omitempty"`
	Email             string     `json:"email,omitempty"`
	Nickname          string     `json:"nickname"`
	Avatar            string     `json:"avatar,omitempty"`
	Gender            int32      `json:"gender"`
	Birthday          string     `json:"birthday,omitempty"`
	Location          string     `json:"location,omitempty"`
	Status            int32      `json:"status"`
	LoginType         int32      `json:"loginType"`
	GoogleId          string     `json:"googleId,omitempty"`
	AppleId           string     `json:"appleId,omitempty"`
	LastLoginAt       *time.Time `json:"lastLoginAt,omitempty"`
	LastLoginIp       string     `json:"lastLoginIp,omitempty"`
	CreatedAt         time.Time  `json:"createdAt"`
	UpdatedAt         time.Time  `json:"updatedAt"`
}

// LoginLogInfo 登录日志信息
type LoginLogInfo struct {
	LogId       uint64    `json:"logId"`
	UserId      uint64    `json:"userId"`
	LoginType   int32     `json:"loginType"`
	LoginIp     string    `json:"loginIp"`
	UserAgent   string    `json:"userAgent,omitempty"`
	DeviceId    string    `json:"deviceId,omitempty"`
	LoginResult int32     `json:"loginResult"`
	FailReason  string    `json:"failReason,omitempty"`
	CreatedAt   time.Time `json:"createdAt"`
}
