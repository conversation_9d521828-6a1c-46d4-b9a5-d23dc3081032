package model

/*
	恋爱小精灵消息类型
*/

//恋爱小精灵基础结构
type ElBaseF struct {
	Title string `json:"title"  bson:"title"`
	User  ElUser `json:"user"  bson:"user"`
}

type ElUser struct {
	NoId      string `json:"noId"  bson:"noId"` //id
	MsgId     string `json:"msgId"  bson:"msgId"`
	City      string `json:"city" bson:"city"`                     //城市
	NickName  string `json:"nickName"  bson:"nickName,omitempty"`  //名称
	AvatarUrl string `json:"avatarUrl" bson:"avatarUrl,omitempty"` //头像
	StarSign  string `json:"starSign" bson:"starSign"`             //星座
	Age       int    `json:"age" bson:"age"`                       //年龄
	IsXd      int    `json:"isXd"  bson:"isXd"`                    //是否心动  0  正常  1 心动
}
