package model

import (
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	TableInterestTagLib   = "user_interest_tag_lib"
	TableInterestTagAdded = "user_interest_tag_added"
)

type InterestTagLib struct {
	Id   primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	Name string             `json:"name" bson:"name"`
}

type InterestTagSaveReq struct {
	NoId   string   `json:"noId"`
	TagIds []string `json:"tagIds"`
}

type InterestTagAdded struct {
	Id      primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	NoId    string             `json:"noId" bson:"noId"`
	TagId   string             `json:"tagId" bson:"tagId"`
	TagName string             `json:"tagName" bson:"tagName"`
}

type InterestTagAddedVO struct {
	TagId   string `json:"tagId"`
	TagName string `json:"tagName"`
}
