package model

import "go.mongodb.org/mongo-driver/bson/primitive"

const TableSmsCode = "sms_code"

const TableSmsCodeWhite = "sms_code_white"

type TSmsCode struct {
	Id     primitive.ObjectID `bson:"_id,omitempty" json:"_id"`
	UserId primitive.ObjectID `bson:"userId,omitempty" json:"userId"`
	Mobile string             `bson:"mobile" json:"mobile"`
	Date   string             `bson:"date" json:"date"`
	Code   string             `bson:"code" json:"code"`
}
