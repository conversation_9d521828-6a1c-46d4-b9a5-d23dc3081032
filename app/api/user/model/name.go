package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	TableNickName = "user_nicknames"
)

type TNickName struct {
	Id      primitive.ObjectID `bson:"_id,omitempty" json:"_id"`
	Name    string             `bson:"name" json:"name"`
	Type    string             `bson:"type" json:"type"` // noun 名词 particle 助词 adjective 形容词
	Status  string             `bson:"status" json:"status"`
	Created time.Time          `bson:"created" json:"created"`
}

type LoginNameRes struct {
	Noun      []*TNickName `json:"noun"`
	Particle  []*TNickName `json:"particle"`
	Adjective []*TNickName `json:"adjective"`
}
