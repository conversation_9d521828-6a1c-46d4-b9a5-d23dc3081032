//Description faceID结构体
//Date        2021/8/3
//User        cl

package model

import "go.mongodb.org/mongo-driver/bson/primitive"

const TableUserFaceVerify = "user_face_verify"

type TUserFaceVerify struct {
	Id             primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	RequestId      string             `json:"requestId" bson:"requestId"`
	NoId           string             `json:"noId" bson:"noId"`
	SdkType        string             `json:"sdkType" bson:"sdkType"`
	ComparisonType string             `json:"comparisonType" bson:"comparisonType"`
	Status         string             `json:"status" bson:"status"`
	ResultCode     int                `json:"resultCode" bson:"resultCode"`
	Reason         string             `json:"reason" bson:"reason"`
}

const TableReviewFace = "review_face"

type TReviewFace struct {
	ID        primitive.ObjectID `json:"id,omitempty" bson:"_id,omitempty"`
	UserId    string             `json:"userId" bson:"userId"`
	NoId      string             `json:"noId" bson:"noId"`
	NickName  string             `json:"nickName" bson:"nickName"`
	Gender    string             `json:"gender" bson:"gender"`
	AvatarUrl string             `json:"avatarUrl" bson:"avatarUrl"`
	RealUrl   string             `json:"realUrl" bson:"realUrl"`
	Status    string             `json:"status" bson:"status"`
	Type      string             `json:"type" bson:"type"`
	ReqTime   int64              `json:"reqTime" bson:"reqTime"`
	Log       []*AvatarReviewLog `json:"log" bson:"log"`
}

type IDCardInfo struct {
	Result     int    `json:"result"`
	Side       int    `json:"side"`
	Name       Result `json:"name"`
	Gender     Result `json:"gender"`
	Address    Result `json:"address"`
	IDCard     Result `json:"idcard_number"`
	BirthYear  Result `json:"birth_year"`
	BirthMonth Result `json:"birth_month"`
	BirthDay   Result `json:"birth_day"`
}

type Result struct {
	Result string `json:"result"`
}

type RspIDCardInfo struct {
	Name   string `json:"name"`
	IDCard string `json:"idCard"`
}
