package model

type CheckMobileRsp struct {
	IsConflict bool        `json:"isConflict"`
	From       *MobileInfo `json:"from"`
	To         *MobileInfo `json:"to"`
}

type MobileInfo struct {
	NoId        string `json:"noId"`
	Mobile      string `json:"mobile"`
	Nickname    string `json:"nickname"`
	Avatar      string `json:"avatar"`
	WealthLevel int64  `json:"wealthLevel"` // 财富等级
	CharmLevel  int64  `json:"charmLevel"`  // 魅力等级
}

type ApplyModifyMobileReq struct {
	ToMobile    string   `json:"toMobile" binding:"required"` // 新手机号
	SmsCode     string   `json:"smsCode" binding:"required"`  // 新手机验证码
	ModifyType  string   `json:"modifyType" binding:"required"`
	ApplyReason string   `json:"applyReason"`
	ProveImg    []string `json:"proveImg"`
	NoId        string   `json:"-"`
	FromMobile  string   `json:"-"`
}

type ModifyMobileData struct {
	NoId        string   `json:"noId"`
	FromMobile  string   `json:"fromMobile"`
	ToMobile    string   `json:"toMobile"`
	ModifyType  string   `json:"modifyType"`
	ApplyReason string   `json:"applyReason"`
	ProveImg    []string `json:"proveImg"`
	Status      string   `json:"status"`
}
