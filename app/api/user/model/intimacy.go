package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

/*
	亲密度规则
*/
type IntimacyRule struct {
	Id         primitive.ObjectID `json:"id"  bson:"_id,omitempty"`      //ID
	Min        float64            `json:"min"  bson:"min"`               //最小
	Max        float64            `json:"max"  bson:"max"`               //最大
	Recession  float64            `json:"recession"  bson:"recession"`   //每小时衰减
	Level      int                `json:"level"  bson:"level"`           //亲密度等级
	Upgrade    string             `json:"upgrade"  bson:"upgrade"`       //升级需要钻石
	Name       string             `json:"name"  bson:"name"`             //等级名称
	CreateTime *time.Time         `json:"createTime"  bson:"createTime"` //时间
	UpdateTime *time.Time         `json:"updateTime"  bson:"updateTime"` //更新时间
}

type IntimacyRow struct {
	From           string     `json:"from"  bson:"from"`                     //发送者
	To             string     `json:"to"  bson:"to"`                         //接受者
	Intimacy       int64      `json:"intimacy"  bson:"intimacy"`             //亲密度
	IntiUpdateTime *time.Time `json:"intiUpdateTime"  bson:"intiUpdateTime"` //亲密度最后一次更新的时间
}
