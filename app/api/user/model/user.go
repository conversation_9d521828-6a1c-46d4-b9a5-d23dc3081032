package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"creativematrix.com/beyondreading/app/base/user/api"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/utils"
)

const (
	TableUser           = "users"
	TableDecorateStatus = "user_decorate_status" //用户装扮红点点击情况
	TableUserDeviceLog  = "user_device_log"
	TableGiftBackPack   = "gift_backpack"
	TableGuildScout     = "guild_scout"
	TableGuildAnchor    = "guild_anchor" //主播
	TableSwitch         = "switch"

	Apple  = "Apple"
	Weixin = "Weixin"
	QQ     = "QQ"
	Mobile = "Mobile"
)

var (
	ModeLogin = []string{"Apple", "Weixin", "QQ", "Mobile"}
)

type UserModel struct {
	ID             primitive.ObjectID `bson:"_id,omitempty" json:"_id"`
	NickName       string             `bson:"nickname" json:"nickname"`
	RegisterStatus string             `bson:"registerStatus" json:"registerStatus"` // unfinish finish
	NoId           string             `bson:"noId" json:"noId"`
	MsgId          string             `bson:"msgId" json:"msgId"`
	Gender         string             `bson:"gender" json:"gender"`
	ForbidDays     int                `bson:"forbidDays"`
	ForbidTime     int64              `bson:"forbidTime"`
}

// TUserInfo 用户信息
type TUserInfo struct {
	Id                 primitive.ObjectID    `json:"-" bson:"_id,omitempty"`
	NoId               string                `json:"noId" bson:"noId"`
	Status             int                   `json:"status" bson:"status"`
	NickName           string                `json:"nickName" bson:"nickName" check:"check"`   //昵称
	Gender             string                `json:"gender" bson:"gender"`                     //性别 female male
	Age                int                   `json:"age" bson:"age"`                           //年龄
	Wealth             int64                 `json:"wealth" bson:"wealth"`                     //财富值
	Charm              int64                 `json:"charm" bson:"charm"`                       //魅力值
	Like               int                   `json:"like" bson:"like"`                         //点赞数
	StarSign           string                `json:"starSign" bson:"starSign"`                 //星座
	Feeling            string                `json:"feeling" bson:"feeling"`                   //情感状态
	Height             int                   `json:"height" bson:"height" check:"check"`       //身高
	Weight             int                   `json:"weight" bson:"weight" check:"check"`       //体重
	Album              []UserImg             `json:"album" bson:"album"`                       //用户相册
	AvatarUrl          string                `json:"avatarUrl" bson:"avatarUrl" check:"check"` //头像
	CheckAvatar        string                `json:"checkAvatar" bson:"checkAvatar"`           //审核中头像
	AvatarStatus       string                `json:"avatarStatus" bson:"avatarStatus"`         //头像审核状态
	AvatarLevel        string                `json:"avatarLevel" bson:"avatarLevel"`           //头像等级
	AudioUrl           string                `json:"audioUrl" bson:"audioUrl" check:"check"`   //语音介绍
	AudioStatus        string                `json:"audioStatus" bson:"audioStatus"`           //语音审核状态
	Duration           int32                 `json:"duration" bson:"duration"`                 //语音时长
	Education          string                `json:"education" bson:"education"`               //学历
	Job                string                `json:"job" bson:"job" check:"check"`             //工作
	Income             string                `json:"income" bson:"income" check:"check"`       //收入
	Province           string                `json:"province" bson:"province"`                 //省
	City               string                `json:"city" bson:"city" check:"check"`           //城市
	Area               string                `json:"area" bson:"area"`                         //区域
	HomeTown           string                `json:"homeTown" bson:"homeTown" check:"check"`   //家乡
	RealStatus         string                `json:"realStatus" bson:"realStatus"`             //真人认证状态
	RealNameStatus     string                `json:"realNameStatus" bson:"realNameStatus"`     //实名状态
	Intro              string                `json:"intro" bson:"intro" check:"check"`         //介绍
	Lon                float64               `json:"lon" bson:"lon"`                           //经度
	Lat                float64               `json:"lat" bson:"lat"`                           //纬度
	IsOnline           int                   `json:"isOnline" bson:"isOnline"`                 //是否在线
	CharmLevel         int32                 `json:"charmLevel" bson:"charmLevel"`             //魅力等级
	WealthLevel        int32                 `json:"wealthLevel" bson:"wealthLevel"`           //财富等级
	LastLoginTime      time.Time             `json:"lastLoginTime" bson:"lastLoginTime"`       //上次登录
	Born               string                `json:"born" bson:"born"`                         //出生
	Distance           string                `json:"distance" bson:"-"`                        //自定义显示字段 同城距离
	IsLike             bool                  `json:"isLike" bson:"-"`                          //自定义显示字段 是否点赞
	Days               int                   `json:"days" bson:"-"`                            //自定义显示字段 注册了多少天
	NewStatus          int                   `json:"newStatus" bson:"newStatus"`               //自定义显示字段
	ContactStatus      int                   `json:"contactStatus" bson:"-"`                   //自定义显示字段 心动状态
	FollowStatus       int                   `json:"followStatus" bson:"-"`                    //自定义显示字段 是否关注
	BlackStatus        int                   `json:"blackStatus" bson:"-"`                     //自定义显示字段 是否拉黑
	PreWealth          int64                 `json:"preWealth" bson:"-"`                       //自定义显示字段 上级财富值
	PreCharm           int64                 `json:"preCharm" bson:"-"`                        //自定义显示字段 上级魅力值
	NextWealth         int64                 `json:"nextWealth" bson:"-"`                      //自定义显示字段 下级财富值
	NextWealthLv       int32                 `json:"nextWealthLv" bson:"-"`                    //自定义显示字段 下级财富
	NextCharm          int64                 `json:"nextCharm" bson:"-"`                       //自定义显示字段 下级魅力值
	NextCharmLv        int32                 `json:"nextCharmLv" bson:"-"`                     //自定义显示字段 下级魅力
	IsBindPhone        bool                  `json:"isBindPhone" bson:"-"`                     //自定义显示字段 是否绑定手机
	Remark             string                `json:"remark" bson:"-"`                          //自定义显示字段 备注
	ForbidDays         int                   `json:"-" bson:"forbidDays"`                      //封禁天数
	ForbidTime         int64                 `json:"-" bson:"forbidTime"`                      //封禁开始时间
	Uid                string                `bson:"uid,omitempty" json:"uid,omitempty"`
	UnionId            string                `bson:"unionId,omitempty" json:"unionId,omitempty"`
	RegistryType       string                `json:"registryType" bson:"registryType"`               // 注册类型
	RegisterStatus     string                `bson:"registerStatus" json:"registerStatus,omitempty"` // unfinish finish
	InviteCode         string                `bson:"inviteCode" json:"inviteCode,omitempty"`         // 邀请码
	GroupCode          string                `bson:"groupCode" json:"groupCode,omitempty"`           // 公会码
	MsgId              string                `bson:"msgId" json:"msgId,omitempty"`                   //第三方id 由noId 生成
	RcToken            string                `bson:"rcToken" json:"rcToken,omitempty" `              //融云Token
	Device             Device                `bson:"device" json:"-"`
	ContractSignStatus string                `bson:"contractSignStatus" json:"contractSignStatus"` // 电子签约状态
	Mode               *ModeList             `bson:"mode" json:"mode"`                             // 绑定登录方式
	Mobile             string                `bson:"mobile" json:"mobile"`                         // 手机号
	SuperAdmin         string                `bson:"superAdmin" json:"superAdmin"`                 // 超管类型
	IsAnchor           bool                  `bson:"-" json:"isAnchor"`                            // 是否是主播
	IsShowing          bool                  `bson:"-" json:"isShowing"`                           //是否开播
	LiveType           string                `bson:"-" json:"liveType"`                            //是否开播
	Focus              int32                 `bson:"-" json:"focus" `                              //关注
	Fans               int32                 `bson:"-" json:"fans"`                                //粉丝
	WallInfo           *AnchorWallInfo       `bson:"-" json:"wallInfo"`                            // 礼物墙信息
	Decorate           *Decorate             `bson:"-" json:"decorate"`
	AtlasInfo          *Atlas                `bson:"-" json:"atlasInfo"`      // 图鉴徽章信息
	IsLink             bool                  `bson:"-" json:"isLink"`         //是否连麦
	Monitor            string                `bson:"-" json:"monitor"`        //连麦主播id
	MobileMd5          string                `bson:"-" json:"mobileMd5"`      //登录手机号加密
	InterestTag        []*InterestTagAddedVO `bson:"-" json:"interestTag"`    // 兴趣标签
	PayJumpUrlFull     string                `bson:"-" json:"payJumpUrlFull"` // 支付跳转地址-全屏
	PayJumpUrlHalf     string                `bson:"-" json:"payJumpUrlHalf"` // 支付跳转地址-半屏
}

type PrettyNo struct {
	Serial       string `json:"serial"`       // 靓号ID，如：888888
	ImgUrl       string `json:"imgUrl"`       // 静态背景资源，如：xxx.png
	AnimationUrl string `json:"animationUrl"` // 动态背景资源，如：xxx.zip
	Speed        uint32 `json:"speed"`        // 动效速度 单位：ms
}

type Bind struct {
	OpenId  string `bson:"openId" json:"openId"`
	UnionId string `bson:"unionId" json:"unionId"`
}

type AppleBind struct {
	UnionId  string `bson:"unionId" json:"unionId"`
	Email    string `bson:"email" json:"email"`
	FullName string `bson:"fullName" json:"fullName"`
	Token    string `bson:"token" json:"-"`
	Secret   string `bson:"secret" json:"-"`
}

type ModeList struct {
	Apple  *AppleBind `bson:"apple" json:"apple"`
	Weixin *Bind      `bson:"weixin" json:"weixin"`
	QQ     *Bind      `bson:"qq" json:"qq"`
}

type Device struct {
	Platform   string `json:"platform" bson:"platform"`
	Channel    string `json:"channel" bson:"channel"`
	OS         string `json:"os" bson:"os"`
	Model      string `json:"model" bson:"model"`
	AppVersion string `json:"appVersion" bson:"appVersion"`
	IP         string `json:"ip" bson:"ip"`
	Imei       string `json:"imei" bson:"imei"`
	LoginTime  int64  `json:"loginTime" bson:"loginTime"`
}

type AnchorWallInfo struct {
	LightNum int64           `json:"lightNum"` // 已点亮数量
	Num      int64           `json:"num"`      // 礼物墙总数
	List     []*AnchorWallVO `json:"list"`
}

type AnchorWallVO struct {
	GiftId   string `json:"giftId"`
	GiftName string `json:"giftName"`
	CoverUrl string `json:"coverUrl"`
	Diamonds int64  `json:"diamonds"`
	IsLight  bool   `json:"isLight"`
}

// MyInfo 我的聚合信息
type MyInfo struct {
	NoId              string    `json:"noId" bson:"noId"`
	NickName          string    `json:"nickName" bson:"nickName"`             //昵称
	AvatarUrl         string    `json:"avatarUrl" bson:"avatarUrl"`           //头像
	CheckAvatar       string    `json:"checkAvatar" bson:"checkAvatar"`       //审核中头像
	AvatarStatus      string    `json:"avatarStatus" bson:"avatarStatus"`     //头像状态
	RealStatus        string    `json:"realStatus" bson:"realStatus"`         //真人认证状态
	RealNameStatus    string    `json:"realNameStatus" bson:"realNameStatus"` //实名认证状态
	Mobile            string    `json:"-" bson:"mobile"`                      //手机号
	IsBindPhone       bool      `json:"isBindPhone" bson:"-"`                 //是否绑定手机
	Gender            string    `json:"gender" bson:"gender"`                 //性别
	Age               int       `json:"age" bson:"age"`                       //年龄
	Born              int       `json:"-" bson:"born"`                        //计算年龄
	Wealth            int64     `json:"wealth" bson:"wealth"`                 //财富值
	PreWealth         int32     `json:"preWealth" bson:"-"`                   //上一级
	NextWealth        int32     `json:"nextWealth" bson:"-"`                  //下一级
	WealthLevel       int32     `json:"wealthLevel" bson:"-"`                 //财富等级
	Charm             int64     `json:"-" bson:"charm"`                       //魅力等级
	CharmLevel        int32     `json:"charmLevel" bson:"-"`                  //魅力等级
	Intimates         int32     `json:"intimates" bson:"intimates"`           //密友
	Friends           int32     `json:"friends" bson:"friends"`               //好友
	Focus             int32     `json:"focus" bson:"focus"`                   //关注
	Fans              int32     `json:"fans" bson:"fans"`                     //粉丝
	Diamonds          int64     `json:"diamonds" bson:"diamonds"`             //钻石
	StarVotes         int64     `json:"starVotes" bson:"starVotes"`           //星票
	AccountStatus     string    `json:"accountStatus"`                        //账户状态
	DecorateStatus    bool      `json:"decorateStatus"`                       //装扮按钮需要点击
	DecorateNew       bool      `json:"decorateNew"`                          //装扮上新
	AvatarId          string    `json:"avatarId"`                             //头像框
	Medals            []string  `json:"medals"`                               // 勋章
	FansClubManageUrl string    `json:"fansClubManageUrl"`                    // 粉丝团管理h5页面地址（返回空字符串则不展示icon）
	RegisterTime      int64     `json:"registerTime"`                         // 注册时间戳，秒级
	Visitors          int64     `json:"visitors"`                             // 新访客数量
	PrettyNo          *PrettyNo `json:"prettyNo"`                             // 靓号
}

type TUser struct {
	UserId string `json:"userId"`
}

type LoginReq struct {
	Token    string `bson:"token" json:"token"`       // token 凭证
	OpenId   string `bson:"openId" json:"openId"`     // QQ Weixin有
	UnionId  string `bson:"unionId" json:"unionId"`   // 数据库唯一标识
	Type     string `bson:"type" json:"type"`         // bind login
	Mode     string `bson:"mode" json:"mode"`         // QQ Weixin Apple
	Email    string `bson:"email" json:"email"`       // 苹果登录传 emial
	FullName string `bson:"fullName" json:"fullName"` // 苹果登录传 fullName
	Mobile   string `bson:"mobile" json:"mobile"`     // mobile登录传 手机号码
	Secret   string `bson:"-" json:"-"`               //iOS secret,注销用
	Platform string `bson:"-" json:"-"`
}

type WeixinReq struct {
	Code   string `json:"code"`
	Scope  string `json:"scope"`
	AppId  string `json:"-"`
	Secret string `json:"-"`
}

//type BindReq struct {
//	Token    string `bson:"token" json:"token"`       // token 凭证
//	OpenId   string `bson:"openId" json:"openId"`     // QQ Weixin有
//	UnionId  string `bson:"unionId" json:"unionId"`   // 数据库唯一标识
//	Type     string `bson:"type" json:"type"`         // bind login
//	Mode     string `bson:"mode" json:"mode"`         // QQ Weixin Apple
//	Email    string `bson:"email" json:"email"`       // 苹果登录传 emial
//	FullName string `bson:"fullName" json:"fullName"` // 苹果登录传 fullName
//	Mobile   string `bson:"mobile" json:"mobile"`     // mobile登录传 手机号码
//	UserId   string `bson:"userId" json:"userId"`
//}

type LoginRes struct {
	NoId      string `bson:"-" json:"-"`
	Token     string `bson:"token" json:"token"`
	Status    string `bson:"status" json:"status"`
	LogoffMsg string `bson:"-" json:"logoffMsg"`
}

type BindRes struct {
	Status    bool   `bson:"status" json:"status"`
	Mobile    string `bson:"mobile" json:"mobile"`
	BindToken string `json:"bindToken"`
}

type BindListRes struct {
	Mode   string `bson:"mode" json:"mode"` // QQ Weixin Apple Mobile
	Val    string `bson:"value" json:"value"`
	Status bool   `bson:"status" json:"status"`
}

type LoginInfoReq struct {
	Gender         string `json:"gender" bson:"gender"`                               // 性别 female male
	Born           string `json:"born" bson:"born"`                                   // 出生
	StarSign       string `json:"starSign" bson:"starSign"`                           // 星座 枚举
	AvatarUrl      string `json:"avatarUrl,omitempty" bson:"avatarUrl,omitempty"`     // 头像
	CheckAvatar    string `json:"checkAvatar,omitempty" bson:"checkAvatar,omitempty"` //审核头像
	AvatarStatus   string `json:"avatarStatus" bson:"avatarStatus"`                   // 头像审核状态
	NickName       string `json:"nickName" bson:"nickName"`                           // 昵称
	InviteCode     string `json:"inviteCode" bson:"inviteCode"`                       // 邀请码
	GroupCode      string `json:"groupCode" bson:"groupCode"`                         // 公会邀请码
	SMResult       string `json:"smResult" bson:"smResult"`
	SMReason       string `json:"smReason" bson:"smReason"`
	RegisterStatus string `json:"registerStatus" bson:"registerStatus"`
	Channel        string `json:"channel" bson:"-"`
	Device         Device `json:"device" bson:"device"`
}

type LonLatReq struct {
	LonLatType string  `json:"type"`
	Lon        float64 `json:"lon" bson:"lon"`    //经度
	Lat        float64 `json:"lat" bson:"lat"`    //纬度
	Province   string  `json:"province" bson:"-"` //省
	City       string  `json:"city" bson:"city"`  //市
}

func ProtoToUser(user *api.UserInfo, isSelf bool) *TUserInfo {
	data := &TUserInfo{
		NoId:               user.NoId,
		Status:             int(user.Status),
		NickName:           user.NickName,
		Gender:             user.Gender,
		Age:                utils.GetAgeByDate(user.Born),
		Wealth:             user.Wealth,
		Charm:              user.Charm,
		WealthLevel:        int32(utils.GetWealthLv(user.Wealth)),
		CharmLevel:         int32(utils.GetCharmLv(user.Charm)),
		Like:               int(user.Like),
		StarSign:           user.StarSign,
		Feeling:            user.Feeling,
		Height:             int(user.Height),
		Weight:             int(user.Weight),
		RealStatus:         user.RealStatus,
		AvatarUrl:          user.AvatarUrl,
		CheckAvatar:        user.CheckAvatar,
		AvatarStatus:       user.AvatarStatus,
		AvatarLevel:        user.AvatarLevel,
		AudioUrl:           user.AudioUrl,
		AudioStatus:        user.AudioStatus,
		Duration:           user.Duration,
		Education:          user.Education,
		Job:                user.Job,
		Income:             user.Income,
		Province:           user.Province,
		City:               user.City,
		Area:               user.Area,
		HomeTown:           user.HomeTown,
		RealNameStatus:     user.RealNameStatus,
		Intro:              user.Intro,
		Lon:                user.Lon,
		Lat:                user.Lat,
		IsOnline:           int(user.IsOnline),
		MsgId:              user.MsgId,
		RegisterStatus:     user.RegisterStatus,
		RcToken:            user.RcToken,
		ContractSignStatus: user.ContractSignStatus,
		Mobile:             user.Mobile,
		IsBindPhone:        user.Mobile != "",
		SuperAdmin:         user.SuperAdmin,
		MobileMd5:          utils.Md5String(user.Mobile),
	}

	if isSelf {
		data.Uid = user.Uid
		data.Born = user.Born
		data.UnionId = user.UnionId
		data.GroupCode = user.GroupCode
		data.InviteCode = user.InviteCode
		data.RegistryType = user.RegistryType
		data.RegisterStatus = user.RegisterStatus
	}
	id, _ := primitive.ObjectIDFromHex(user.Id)
	if id.Timestamp().After(time.Now().Add(-time.Hour * 168)) {
		data.NewStatus = NewUser
	}

	data.PreWealth = utils.WealthExp[data.WealthLevel]
	if int(data.WealthLevel) >= len(utils.WealthExp)-1 {
		data.NextWealth = data.Wealth
		data.NextWealthLv = data.WealthLevel
	} else {
		data.NextWealth = utils.WealthExp[data.WealthLevel+1]
		data.NextWealthLv = data.WealthLevel + 1
	}
	data.PreCharm = utils.CharmExp[data.CharmLevel]
	if int(data.CharmLevel) >= len(utils.CharmExp)-1 {
		data.NextCharm = data.Charm
		data.NextCharmLv = data.CharmLevel
	} else {
		data.NextCharm = utils.CharmExp[data.CharmLevel+1]
		data.NextCharmLv = data.CharmLevel + 1
	}

	lastLoginTime, err := time.Parse(time.RFC3339, user.LastLoginTime)
	if err != nil {
		logger.LogErrorw("ProtoToUser LastLoginTime", "error", err)
		return nil
	}
	data.LastLoginTime = lastLoginTime
	data.Id, _ = primitive.ObjectIDFromHex(user.Id)

	for _, img := range user.Album {
		if img.Status == Reject || (!isSelf && img.Status != Success) {
			continue
		}
		data.Album = append(data.Album, UserImg{
			ImgUrl: img.ImgUrl,
			Status: img.Status,
		})
	}

	return data
}

type FlashLoginReq struct {
	Type        string `json:"type" bson:"type"`               // 类型 login bind
	AccessToken string `json:"accessToken" bson:"accessToken"` // accessToken
	Timestamp   string `json:"timestamp" bson:"timestamp"`     // timestamp时间戳
	Sign        string `json:"sign" bson:"sign"`               // 签名
	Platform    string `json:"platform" bson:"platform"`       // 从header里面获取渠道
}

type SmsCodeReq struct {
	Type      string `json:"type" bson:"type"`           // 类型 login bind modify
	Mobile    string `json:"mobile" bson:"mobile"`       // 手机号
	Timestamp string `json:"timestamp" bson:"timestamp"` // timestamp时间戳
	Sign      string `json:"sign" bson:"sign"`           // 签名
	Format    string `json:"-"`
}

type SmsLoginReq struct {
	Mobile   string `json:"mobile" bson:"mobile"` // 手机号
	Code     string `json:"code" bson:"code"`     // 验证码
	Type     string `json:"type" bson:"type"`     // 类型 login bind
	Platform string `json:"-" bson:"-"`
}

// TableUserAction 用户点赞，备注
const TableUserAction = "user_action"

type TUserAction struct {
	Id       primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	UserId   string             `bson:"userId" json:"userId"`
	DstUser  string             `bson:"dstUser" json:"dstUser"`
	Remark   string             `bson:"remark,omitempty" json:"remark"`
	ThumbsUp bool               `bson:"thumbsUp,omitempty" json:"thumbsUp"`
}

type ReqUserRemark struct {
	DstUser string `form:"dstUser" json:"dstUser" binding:"required"`
	Remark  string `form:"remark" json:"remark" binding:"required"`
}

type ReqThumbsUp struct {
	DstUid string `json:"dstuid" binding:"required"`
}

// UserImg 用户相册图片
type UserImg struct {
	ImgUrl   string `json:"imgUrl" bson:"imgUrl"`
	Status   string `json:"status" bson:"status"`
	SMResult string `json:"-" bson:"smResult"`
	SMReason string `json:"-" bson:"smReason"`
}

type ReqAlbum struct {
	Album []string `json:"album"`
}

type RspAlbum struct {
	Album []UserImg `json:"album" bson:"album"`
}

type ReqUpdateUser struct {
	NickName     string `json:"nickName,omitempty"`
	Born         string `json:"born,omitempty"`
	Height       int    `json:"height,omitempty"`
	Weight       int    `json:"weight,omitempty"`
	City         string `json:"city,omitempty"`
	Province     string `json:"-"`
	Area         string `json:"area,omitempty"`
	HomeTown     string `json:"homeTown,omitempty"`
	Intro        string `json:"intro,omitempty"`
	Feeling      string `json:"feeling,omitempty"`
	Job          string `json:"job,omitempty"`
	Education    string `json:"education,omitempty"`
	Income       string `json:"income,omitempty"`
	StarSign     string `json:"starSign,omitempty"`
	AvatarUrl    string `json:"avatarUrl,omitempty"`
	AvatarStatus string `json:"avatarStatus,omitempty"`
	AudioUrl     string `json:"audioUrl,omitempty"`
	AudioStatus  string `json:"audioStatus,omitempty"`
	Duration     int32  `json:"duration,omitempty"`
	SMResult     string `json:"smResult,omitempty"`
	SMReason     string `json:"smReason,omitempty"`
	CheckAvatar  string `json:"checkAvatar,omitempty"`
}

// TableAudioHint 语音签名提示语
const TableAudioHint = "user_audio_hint"

type TAudioHint struct {
	Id     primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	Hint   string             `json:"hint" bson:"hint"`
	Sort   float64            `json:"sort" bson:"sort"`
	Status int                `json:"status" bson:"status"`
}

// TableFeedBack 用户反馈
const TableFeedBack = "user_feedback"

type TUserFeedBack struct {
	Id           primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	Category     string             `json:"category" bson:"category"`         //分类
	ImgList      string             `json:"imgList" bson:"imgList"`           //多张图片用逗号分割
	FromNick     string             `json:"fromNick" bson:"fromNick"`         //举报人昵称
	FromUserId   string             `json:"fromUserId" bson:"fromUserId"`     //举报人uid
	FromNoId     string             `json:"fromNoId" bson:"fromNoId"`         //举报人noId
	PostTime     string             `json:"postTime" bson:"postTime"`         //举报时间
	ToNick       string             `json:"toNick" bson:"toNick"`             //被举报人昵称
	ToUserId     string             `json:"toUserId" bson:"toUserId"`         //被举报人uid
	ToNoId       string             `json:"toNoId" bson:"toNoId"`             //被举报人noId
	FeedBackType string             `json:"feedBackType" bson:"feedBackType"` //举报/反馈类型
	Content      string             `json:"content" bson:"content"`           //反馈内容
	Contact      string             `json:"contact" bson:"contact"`           //联系方式
	HandleType   string             `json:"handleType" bson:"handleType"`     //处理类型
	HandleTime   string             `json:"handleTime" bson:"handleTime"`     //处理时间
	Notice       string             `json:"notice" bson:"notice"`             //通知消息
	Remark       string             `json:"remark" bson:"remark"`             //备注
	Forbid       int                `json:"forbid" bson:"forbid"`             //封禁天数
}

type ReqFeedBack struct {
	Category     string `json:"category" binding:"required"`     //分类
	FeedBackType string `json:"feedBackType" binding:"required"` //类型
	Content      string `json:"content" binding:"required"`      //反馈内容
	ImgList      string `json:"imgList"`                         //多张图片用逗号分割
	Contact      string `json:"contact"`                         //联系方式
	NoId         string `json:"noId"`
}

const TableFeedbackType = "user_feedback_type"

type TFeedbackType struct {
	Id           primitive.ObjectID `json:"-" bson:"_id,omitempty"`
	Category     string             `json:"-" bson:"category"`
	Name         string             `json:"name" bson:"name"`
	FeedBackType string             `json:"feedBackType" bson:"feedBackType"`
	Sort         float64            `json:"-" bson:"sort"`
}

// TableLogoff 用户注销信息
const TableLogoff = "user_logoff"

type TUserLogoff struct {
	Id      primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	NoId    string             `json:"noId" bson:"noId"`
	Status  string             `json:"status" bson:"status"`
	ReqTime string             `json:"reqTime" bson:"reqTime"`
}

// ReqLogOff 申请注销对象，发送到mq队列
type ReqLogOff struct {
	ReqTime time.Time `json:"reqTime"` //申请时间
	NoId    string    `json:"noId"`    //申请人
}

const TableDefaultAvatar = "user_default_avatar"

type TDefaultAvatar struct {
	Id     primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	Gender string             `json:"gender" bson:"gender"`
	ImgUrl string             `json:"imgUrl" bson:"imgUrl"`
}

type ReqFace struct {
	ComparisonType string `json:"comparisonType" form:"comparisonType" binding:"required"`
	Avatar         string `json:"avatar" form:"avatar"`
	IdCard         string `json:"idCard" form:"idCard"`
	Name           string `json:"name" form:"name"`
}

const TUserInviteVerify = "user_invite_verify"

// InviteVerifyRecord 邀请记录
type InviteVerifyRecord struct {
	Id       primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	From     string             `json:"from" bson:"from"`
	To       string             `json:"to" bson:"to"`
	IsShow   bool               `json:"isShow" bson:"isShow"`     //是否显示已通过
	IsInvite bool               `json:"isInvite" bson:"isInvite"` //是否邀请
}

// Event 用户信息审核
type Event string

const (
	MQSMAvatarEvent    Event = "SMAvatarEvent" //头像审核
	MQSMAlbumEvent     Event = "SMAlbumEvent"  //相册审核
	MQSMUserAudioEvent Event = "SMUserAudio"   //用户语音签名审核
	MQUserFaceEvent    Event = "MQUserFace"    //真人审核
)

type CheckUser struct {
	Event Event  `json:"event"`
	Data  []byte `json:"data"`
}

type CheckImg struct {
	UserId  string   `json:"userId"`
	TokenId string   `json:"tokenId"`
	ImgUrl  []string `json:"avatarUrl"`
}

const TableReviewRule = "user_review_rule"

type TReviewRule struct {
	ID         primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	Name       string             `json:"name" bson:"name"`
	Type       string             `json:"type" bson:"type"`
	CheckMode  CheckMod           `json:"checkMode" bson:"checkMode"`
	Pass       Priority           `json:"pass" bson:"pass"`
	Review     Priority           `json:"review" bson:"review"`
	Reject     Priority           `json:"reject" bson:"reject"`
	PassMode   string             `json:"passMode" bson:"passMode"`
	ReviewMode string             `json:"reviewMode" bson:"reviewMode"`
	RejectMode string             `json:"rejectMode" bson:"rejectMode"`
}

const TableReviewAvatar = "review_avatar"

// TReviewAvatar 发送后台人审记录
type TReviewAvatar struct {
	ID           primitive.ObjectID `json:"id,omitempty" bson:"_id,omitempty"`
	UserId       string             `json:"userId" bson:"userId"`
	NoId         string             `json:"noId" bson:"noId"`
	NickName     string             `json:"nickName" bson:"nickName"`
	SMResult     string             `json:"smResult" bson:"smResult"`
	SMReason     string             `json:"smReason" bson:"smReason"`
	OldAvatar    string             `json:"oldAvatar" bson:"oldAvatar"`
	AvatarUrl    string             `json:"avatarUrl" bson:"avatarUrl"`
	AvatarLevel  string             `json:"avatarLevel" bson:"avatarLevel"`
	AvatarStatus string             `json:"avatarStatus" bson:"avatarStatus"`
	RealStatus   string             `json:"realStatus" bson:"realStatus"`
	RealUrl      string             `json:"realUrl" bson:"realUrl"`
	ReqTime      int64              `json:"reqTime" bson:"reqTime"`
	UpdateTime   int64              `json:"updateTime" bson:"updateTime"`
	Log          []*AvatarReviewLog `json:"log" bson:"log"`
	Priority     Priority           `json:"priority" bson:"-"`
}

type AvatarReviewLog struct {
	Before   string `json:"before" bson:"before"`
	After    string `json:"after" bson:"after"`
	Operator string `json:"operator" bson:"operator"`
	Reason   string `json:"reason" bson:"reason"`
	Time     string `json:"time" bson:"time"`
}

const TableReviewAlbum = "review_album"

// TReviewAlbum 后台人审记录
type TReviewAlbum struct {
	ID         primitive.ObjectID `json:"id,omitempty" bson:"_id,omitempty"`
	UserId     string             `json:"userId" bson:"userId"`
	NoId       string             `json:"noId" bson:"noId"`
	NickName   string             `json:"nickName" bson:"nickName"`
	SMResult   string             `json:"smResult" bson:"smResult"`
	SMReason   string             `json:"smReason" bson:"smReason"`
	ImgUrl     string             `json:"imgUrl" bson:"imgUrl"`
	Status     string             `json:"status" bson:"status"`
	ReqTime    int64              `json:"reqTime" bson:"reqTime"`
	UpdateTime int64              `json:"updateTime" bson:"updateTime"`
	Log        []*AvatarReviewLog `json:"log" bson:"log"`
	Priority   Priority           `json:"priority" bson:"-"`
}

type TReviewAudio struct {
	ID         primitive.ObjectID `json:"id,omitempty" bson:"_id,omitempty"`
	UserId     string             `json:"userId" bson:"userId"`
	NoId       string             `json:"noId" bson:"noId"`
	NickName   string             `json:"nickName" bson:"nickName"`
	SMResult   string             `json:"smResult" bson:"smResult"`
	SMReason   string             `json:"smReason" bson:"smReason"`
	AudioUrl   string             `json:"audioUrl" bson:"audioUrl"`
	Duration   int32              `json:"duration" bson:"duration"`
	Status     string             `json:"status" bson:"status"`
	ReqTime    int64              `json:"reqTime" bson:"reqTime"`
	UpdateTime int64              `json:"updateTime" bson:"updateTime"`
	Log        []*AvatarReviewLog `json:"log" bson:"log"`
	MsgId      string             `json:"msgId" bson:"-"`
}

type RspWxJsParam struct {
	Timestamp int64  `json:"timestamp"`
	Nonce     string `json:"nonce"`
	Sign      string `json:"sign"`
}

type ReqQueryUser struct {
	UserId string `json:"userId" form:"userId"`
	NoId   string `json:"noId" form:"noId"`
	Mobile string `json:"mobile" form:"mobile"`
}

type RspGuestUser struct {
	UserId    string `json:"userId"`
	NoId      string `json:"noId"`
	NickName  string `json:"nickName"`
	AvatarUrl string `json:"avatarUrl"`
}

type SwitchUserReq struct {
	BindToken string `json:"bindToken"`
}

type SwitchUserRes struct {
	Token  string `json:"token"`
	Status string `json:"status"`
}

type TGuildScout struct {
	Id       primitive.ObjectID `bson:"_id,omitempty"`
	ScoutNum string             `bson:"scoutNum"`
}

type TDecClickStatus struct {
	ID         primitive.ObjectID `bson:"_id,omitempty" json:"id"`
	NoId       string             `bson:"noId" json:"noId"`
	Click      int                `bson:"click" json:"click"`   //获得新装扮需要点击
	Upload     string             `bson:"upload" json:"upload"` //上传新装扮需要点击
	Categories bson.M             `bson:"categories" json:"categories"`
}

type Decorate struct {
	Avatar      string    `json:"avatar"`
	Card        string    `json:"card"`
	Car         string    `json:"car"`
	EnterRoom   string    `json:"enterRoom"`
	LiveBubble  string    `json:"liveBubble"`
	FontColor   string    `json:"fontColor"`
	NickColor   string    `json:"nickColor"`
	Medals      []string  `json:"medals"`
	LiveCover   string    `json:"liveCover"`
	VoiceCircle string    `json:"voiceCircle"`
	CardInfo    string    `json:"cardInfo"`
	PrettyNo    *PrettyNo `json:"prettyNo"` // 靓号
}

type Atlas struct {
	BadgeId  int64 `json:"id"`       // 图鉴徽章ID 1-7级别
	ExpireAt int64 `json:"expireAt"` // 过期时间戳
	Rank     int64 `json:"rank"`     //2.0新礼物图鉴排名
}

type ReqValidLogoff struct {
	Code string `json:"code"`
}

type RspValidLogoff struct {
	Valid bool           `json:"valid"`
	List  []*LogoffCheck `json:"list"`
}

type LogoffCheck struct {
	Check   bool   `json:"check"`
	Title   string `json:"title"`
	Content string `json:"content"`
}

type PackGift struct {
	Num      int64 `bson:"num"`
	Diamonds int64 `bson:"diamonds"`
}

type LiveBaseMessage struct {
	Event string      `json:"event"`
	Ts    int64       `json:"ts"`
	Data  interface{} `json:"data"`
}

type GuildAnchor struct {
	AnchorId   string `json:"anchorId"` // 主播ID
	ChooseCode string `json:"chooseCode"`
	Source     string `json:"source"`
}

type TGuildAnchor struct {
	Id         primitive.ObjectID `json:"id,omitempty" bson:"_id,omitempty"`
	NoId       string             `json:"noId" bson:"noId"`
	UserId     primitive.ObjectID `json:"userId" bson:"userId"`
	Name       string             `json:"name" bson:"name"`
	Type       []string           `json:"type" bson:"type"`             //业务 ：live:直播 2：互动 3：直播&互动
	ScoutId    primitive.ObjectID `json:"scoutId" bson:"scoutId"`       //scout表主键
	GuildId    primitive.ObjectID `json:"guildId" bson:"guildId"`       //guild表主键
	DistrictId primitive.ObjectID `json:"districtId" bson:"districtId"` //区长id
	Status     string             `json:"status" bson:"status"`         //normal正常  logoff注销
}

type TSwitch struct {
	ID     primitive.ObjectID `bson:"_id,omitempty" json:"id" form:"id"`
	Name   string             `json:"name"  bson:"name"`     //game
	Status bool               `json:"status"  bson:"status"` //游戏开关
}

type Contact struct {
	OfficialWX string `json:"officialWx"`
}

const TableUserMatched = "user_matched"

// TUserMatched 用户匹配记录
type TUserMatched struct {
	Id        primitive.ObjectID `json:"id" bson:"_id,omitempty"`
	From      string             `json:"from" bson:"from"` // 匹配者
	To        string             `json:"to" bson:"to"`     // 被匹配者
	CreatedAt time.Time          `json:"createdAt"`        // 匹配时间
}

type Section struct {
	Start int64
	End   int64
}
