package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

const (
	TableToken = "user_tokens"
)

const BindTokenExpireMin = 5

type TToken struct {
	Id      primitive.ObjectID `bson:"_id,omitempty" json:"_id"`
	UserId  primitive.ObjectID `bson:"userId,omitempty" json:"userId"`
	Token   string             `bson:"token" json:"token"`
	Status  string             `bson:"status" json:"status"`
	Created time.Time          `bson:"created" json:"created"`
}
