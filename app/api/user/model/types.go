package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

/*
	IM消息类型
*/

//功能空间
type Namespace string

const (
	NLoveHelp  Namespace = "NLoveHelp"  //恋爱小助手
	NSecretary Namespace = "NSecretary" //小秘书
)

//会话状态
type Status int

//发送消息模式
type SendModel int

const (
	Oneself SendModel = 0 //0 真人
	Robot   SendModel = 1 //1 机器人
)

type Relation string

const (
	NoANoB Relation = "NoANoB" //A未关注B B未关注A ==>普通
	AB     Relation = "AB"     //A关注B B关注A   ==>好友
	ANoB   Relation = "ANoB"   //A关注B B未关注A ==>普通
	NoAB   Relation = "NoAB"   //A未关注B B关注A ==>普通
)

const (
	NoSet   = 0 //未建立会话
	PassSet = 1 //建立会话
	//TopSet  = 2 //置顶操作
)

//TableUserSessionList 会话列表 用户关系
const (
	TableUserSessionList = "chat_user_session_list"
	TableOfSessionList   = "chat_of_session_list"
)

//官方助手会话列表

/*
	@SessionList 聊天会话列表
	@Index: from-to-origin unique
*/
type TSessionList struct {
	Id             primitive.ObjectID `json:"id"  bson:"_id,omitempty"`                //ID
	Union          string             `bson:"union"`                                   //id比较大小
	From           string             `json:"from"  bson:"from"`                       //发送者
	To             string             `json:"to"  bson:"to"`                           //接受者
	SendModel      SendModel          `json:"sendModel"  bson:"sendModel"`             //发送者模式  0 真人 1 机器人
	Message        interface{}        `json:"message"  bson:"message"`                 //最近的一条消息
	Type           string             `json:"type"  bson:"type"`                       //消息类型
	Status         Status             `json:"status"  bson:"status"`                   //会话状态 0 未建立会话状态  1 正常建立会话状态
	Intimacy       int64              `json:"intimacy"  bson:"intimacy"`               //亲密度
	Relation       Relation           `json:"relation"  bson:"relation"`               //关系
	IsBlack        int                `json:"isBlack"  bson:"isBlack"`                 //是否是黑名单  0 正常 1 黑名单
	IntiUpdateTime *time.Time         `json:"intiUpdateTime"  bson:"intiUpdateTime"`   //亲密度最后一次更新的时间
	CreateTime     *time.Time         `json:"createTime"  bson:"createTime,omitempty"` //时间
	UpdateTime     *time.Time         `json:"updateTime"  bson:"updateTime"`           //更新时间
	DeleteTime     *time.Time         `json:"deleteTime"  bson:"deleteTime"`           //删除时间
}

//用户关系返回
type RelationInfoRsp struct {
	UserId   string   `json:"userId"`
	ToUserId string   `json:"toUserId"`
	Relation Relation `json:"relation"`
}

//用户关系列表返回
type RelationListRsp struct {
	UserId    string `json:"userId"`
	NickName  string `json:"nickName"`                   //昵称
	Gender    string `json:"gender"`                     //性别
	Age       int    `json:"age"`                        //年龄
	AvatarUrl string `json:"avatarUrl" bson:"avatarUrl"` //头像
}

//Mongo官方助手
type TOfSessionList struct {
	Id         primitive.ObjectID `json:"id"  bson:"_id,omitempty"` //ID
	UserId     string             `json:"userId"  bson:"userId"`    //用户id
	ToUserId   string             `json:"toUserId"  bson:"toUserId"`
	Namespace  Namespace          `json:"namespace"  bson:"namespace"`   //消息空间 恋爱小助手/小秘书
	Type       string             `json:"type"  bson:"type"`             //消息类型
	UpdateTime time.Time          `json:"updateTime"  bson:"updateTime"` //更新时间
	Message    string             `json:"message"  bson:"message"`       //消息体

}

// PlusF 小秘书消息体
type PlusF struct {
	Title      string   `json:"title"  bson:"title"`           //标题
	Descriptor string   `json:"descriptor"  bson:"descriptor"` //描叙信息
	Options    []Option `json:"option"`
}

type Option struct {
	Descriptor string `json:"descriptor,omitempty"` //描叙信息
	Action     string `json:"action"`               //操作行为
	Name       string `json:"name"`                 //名称
}
