package model

import "go.mongodb.org/mongo-driver/bson/primitive"

const TablePositionManager = "position_manager"

const PositionManagerRedis = "position:customize:list"
const PositionWhitelistRedis = "position:whitelist"

const PositionManagerRedisTTL = 86400

type PositionManager struct {
	ID       primitive.ObjectID `bson:"_id,omitempty" json:"id"` // 记录ID
	Province string             `bson:"province" json:"province"`
	City     string             `bson:"city" json:"city"`
	Lon      float64            `json:"lon" bson:"lon"` //经度
	Lat      float64            `json:"lat" bson:"lat"` //纬度
}

type ReqPositionManagerList struct {
	PageSize int64 `json:"pageSize" form:"pageSize"`
	Page     int64 `json:"page" form:"page"`
}

type PositionWhitelist struct {
	IsIn bool `json:"isIn"`
}
