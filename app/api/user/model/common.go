package model

type UploadInitRsp struct {
	Token        string `json:"token"`
	Key          string `json:"key"`
	UploadDomain string `json:"uploadDomain"`
	Bucket       string `json:"bucket"`
	Url          string `json:"url"`
}

type SetSwitchReq struct {
	Position            string `json:"position"`
	PrivateChatPush     string `json:"privateChatPush"`
	OpenLivePush        string `json:"openLivePush"`
	AutoRefuseMultiChat string `json:"autoRefuseMultiChat"`
	AnonymousBrowse     string `json:"anonymousBrowse"`
}

type GetSwitchRsp struct {
	PrivateChatPush     string `json:"privateChatPush"`
	OpenLivePush        string `json:"openLivePush"`
	AutoRefuseMultiChat string `json:"autoRefuseMultiChat"`
	AnonymousBrowse     string `json:"anonymousBrowse,omitempty"`
}

type BindPushReq struct {
	Cid      string `json:"cid" binding:"required"`
	Platform string `json:"platform"`
	NoId     string `json:"-"`
	MsgId    string `json:"-"`
}

type SetStatusReq struct {
	Status string `json:"status"`
	NoId   string `json:"-"`
}
