package dao

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"

	pc "creativematrix.com/beyondreading/app/base/chat/api"

	"creativematrix.com/beyondreading/app/api/user/model"
	pb "creativematrix.com/beyondreading/app/base/user/api"
	"creativematrix.com/beyondreading/pkg/mongo"
)

/*
	@Author:cwl
	@Info:建立好友关系
	@Date:2021-07-07
*/

var (
	MinIntimacy = 2000
)

/*
@SessionListInsert 新增会话列表
*/
func (d *Dao) SessionListInsert(ctx context.Context, session *mongo.Session, req *model.TSessionList) error {
	_, err := session.Table(model.TableUserSessionList).Insert(req)
	return err
}

/*
@SetAttention 设置用户关系（事务操作）
@From 我
@To 被关注的人
*/
func (d *Dao) AttentionSet(ctx context.Context, session *mongo.Session, req *model.TSessionList) error {
	_, err := session.Table(model.TableUserSessionList).Update(bson.M{"from": req.From, "to": req.To}, bson.M{"$set": bson.M{"relation": req.Relation}})
	return err
}

/*
@AttentionGetById 获取好友关系列表(事务操作)
@userId 用户ID
@reutrn 用户会话列表
*/
func (d *Dao) AttentionSession(ctx context.Context, session *mongo.Session, userId, toUserId string) ([]*model.TSessionList, error) {
	var (
		sessionList = make([]*model.TSessionList, 0)
		err         error
	)

	err = session.Table(model.TableUserSessionList).Find(&sessionList, bson.M{"from": userId, "to": toUserId})

	return sessionList, err
}

/*
@AttentionGetById 获取我关注的好友列表
@userId 用户ID
@reutrn 用户会话列表
*/
func (d *Dao) AttentionGetById(ctx context.Context, userId string, pageSize, pageNum int64) ([]*model.TSessionList, error) {
	var (
		sessionList = make([]*model.TSessionList, 0)
		err         error
	)

	//筛选我关注的和互相关注
	err = d.summerConnChat.Model(model.TableUserSessionList).Find(&sessionList, bson.M{
		"from": userId, "relation": bson.M{"$in": bson.A{model.ANoB, model.AB}}},
		mongo.Sort(bson.M{"deleteTime": 1}), mongo.Skip(pageSize*(pageNum-1)), mongo.Limit(pageSize))

	return sessionList, err
}

func (d *Dao) CountAttentionById(ctx context.Context, userId string) int64 {
	return d.summerConnChat.Model(model.TableUserSessionList).Count(bson.M{
		"from": userId, "relation": bson.M{"$in": bson.A{model.ANoB, model.AB}}})
}

func (d *Dao) AttentionGetByIdAll(ctx context.Context, userId string) ([]*model.TSessionList, error) {
	var (
		sessionList = make([]*model.TSessionList, 0)
		err         error
	)

	//筛选我关注的和互相关注
	err = d.summerConnChat.Model(model.TableUserSessionList).Find(&sessionList, bson.M{
		"from": userId, "relation": bson.M{"$in": bson.A{model.ANoB, model.AB}}})

	return sessionList, err
}

/*
@AttentionMyGetById 获取我被关注的好友列表
@userId 用户ID
@reutrn 用户会话列表
*/
func (d *Dao) AttentionMyGetById(ctx context.Context, userId string, pageSize, pageNum int64) ([]*model.TSessionList, error) {
	var (
		sessionList = make([]*model.TSessionList, 0)
		err         error
	)

	//筛选互相关注的和别人关注我的
	err = d.summerConnChat.Model(model.TableUserSessionList).Find(&sessionList, bson.M{
		"to": userId, "relation": bson.M{"$in": bson.A{model.ANoB, model.AB}}}, mongo.Sort(bson.M{"deleteTime": 1}),
		mongo.Skip(pageSize*(pageNum-1)), mongo.Limit(pageSize))

	return sessionList, err
}

/*
@AttentionMyGetById 获取好友列表(互相关注)
@userId 用户ID
@reutrn 用户会话列表
*/
func (d *Dao) AttentionFriendGetById(ctx context.Context, userId string, pageSize, pageNum int64) ([]*model.TSessionList, error) {
	var (
		sessionList = make([]*model.TSessionList, 0)
		err         error
	)

	//筛选互相关注的和别人关注我的
	err = d.summerConnChat.Model(model.TableUserSessionList).Find(&sessionList, bson.M{"from": userId, "relation": model.AB},
		mongo.Sort(bson.M{"deleteTime": 1}), mongo.Skip(pageSize*(pageNum-1)), mongo.Limit(pageSize))

	return sessionList, err
}

// IntimacyFriendGetById 密友
func (d *Dao) IntimacyFriendGetById(ctx context.Context, userId string, pageSize, pageNum int64) ([]*model.TSessionList, error) {
	var (
		sessionList = make([]*model.TSessionList, 0)
		err         error
	)
	err = d.summerConnChat.Model(model.TableUserSessionList).Find(&sessionList,
		bson.M{"from": userId, "intimacy": bson.M{"$gte": MinIntimacy}}, mongo.Sort(bson.M{"deleteTime": 1}),
		mongo.Skip(pageSize*(pageNum-1)), mongo.Limit(pageSize))

	return sessionList, err
}

/*
@UserListGetByNoIds 根据用户NoId获取用户信息
@userIds 用户Id
*/
func (d *Dao) UserListGetByNoIds(ctx context.Context, userIds *pb.UserIdsReq) (*pb.UserInfoRsp, error) {
	return d.user.GetUsersByIds(ctx, userIds)
}

// 亲密度获取
func (d *Dao) IntimacyGet(ctx context.Context, from, to string) (*pc.IntimacyGetRsp, error) {
	return d.baseChat.IntimacyGet(ctx, &pc.IntimacyGetReq{
		From: from,
		To:   to,
	})
}
