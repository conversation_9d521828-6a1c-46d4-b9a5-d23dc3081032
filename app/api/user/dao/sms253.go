package dao

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"math/rand"
	"net/url"
	"strconv"

	"github.com/go-resty/resty/v2"

	"creativematrix.com/beyondreading/app/api/user/model"
	"creativematrix.com/beyondreading/pkg/logger"
)

type QueryRequest struct {
	AppId  string
	Token  string
	Mobile string
	Sign   string
}

type SmsSendJson struct {
	Account  string
	Password string
	Msg      string
	Phone    string
}

func randNumStr(l int) string {
	numStr := ""
	for i := 0; i < l; {
		numStr += strconv.Itoa(rand.Intn(9))
		i++
	}
	return numStr
}

func (d *Dao) SendCode253(ctx context.Context, mobile, format string) (string, error) {
	var result map[string]interface{}
	code := randNumStr(6)
	var ssj SmsSendJson
	ssj.Account = d.conf.ChuangLan.Account
	ssj.Password = d.conf.ChuangLan.Password
	ssj.Msg = fmt.Sprintf(format, code)
	ssj.Phone = mobile

	_, err := resty.New().R().SetResult(&result).SetError(result).SetBody(ssj).Post(d.conf.ChuangLan.Url)
	if err != nil {
		return "", model.SendCodeErr
	}
	if result["code"] != "0" {
		logger.LogErrorw("send msg", "err", result)
		return "", model.SendCodeErr
	}

	return code, nil
}

func (d *Dao) FlashsdkMobileQuery(ctx context.Context, flr *model.FlashLoginReq) (map[string]interface{}, error) {
	var result map[string]interface{}

	appId := d.conf.Flash.Ios.AppId
	appKey := d.conf.Flash.Ios.AppKey
	if flr.Platform == "android" {
		appId = d.conf.Flash.Android.AppId
		appKey = d.conf.Flash.Android.AppKey
	}
	body := url.Values{}
	body.Set("appId", appId)
	body.Set("token", flr.AccessToken)
	body.Set("sign", sign_param(body, appKey))

	err := doRequest(body, &result, d.conf.Flash.MobileQuery)
	if err != nil {
		return nil, err
	}

	if result["code"] != "200000" {
		logger.LogErrorw("flash login:", "err", result)
		return nil, model.UserFlashError
	}
	data := (result["data"]).(map[string]interface{})
	// tradeNo := data["tradeNo"].(string)  // 流水号
	mobileName := data["mobileName"].(string)
	result["mobile"] = decrpt_phone(mobileName, appKey)
	return result, nil
}

func PKCS5Unpadding(encrypt []byte) []byte {
	padding := encrypt[len(encrypt)-1]
	return encrypt[:len(encrypt)-int(padding)]
}

func sign_param(data url.Values, key string) string {
	fmt.Println(data, "sign_param")
	message := "appId" + data.Get("appId") + "token" + data.Get("token")
	mac := hmac.New(sha256.New, []byte(key))
	mac.Write([]byte(message))
	signature := hex.EncodeToString(mac.Sum(nil))
	return string(signature)
}

func decrpt_phone(data string, key string) string {
	hash := md5.Sum([]byte(key))
	hashString := hex.EncodeToString(hash[:])
	block, _ := aes.NewCipher([]byte(hashString[:16]))
	ecb := cipher.NewCBCDecrypter(block, []byte(hashString[16:]))
	source, _ := hex.DecodeString(data)
	decrypted := make([]byte, len(source))
	ecb.CryptBlocks(decrypted, source)
	return string(PKCS5Unpadding(decrypted))
}

func doRequest(body url.Values, result *map[string]interface{}, url string) error {
	rsp, err := resty.New().R().SetFormDataFromValues(body).SetResult(result).Post(url)
	if err != nil {
		return err
	}

	logger.LogInfo(rsp.String())

	return nil
}
