//Description 数美审核
//Date        2021/8/19
//User        cl

package dao

import (
	"context"
	"fmt"
	"time"

	"creativematrix.com/beyondreading/app/api/user/model"
	"creativematrix.com/beyondreading/pkg/check/shumei"
)

func (d *Dao) Msg(ctx context.Context, request shumei.MsgRequest, response shumei.BaseValidInterface) error {
	return d.sm.Msg(ctx, request, response)
}

func (d *Dao) Img(ctx context.Context, request shumei.ImgRequest, response shumei.BaseValidInterface) error {
	return d.sm.Img(ctx, request, response)
}

func (d *Dao) VideoFile(ctx context.Context, request shumei.VideoFileRequest, response shumei.BaseValidInterface) error {
	return d.sm.VideoFile(ctx, request, response)
}

func (d *Dao) AudioFile(ctx context.Context, request shumei.AudioFileRequest, response shumei.BaseValidInterface) error {
	return d.sm.AudioFile(ctx, request, response)
}

func (d *Dao) SMVideoRequestIdSetCache(ctx context.Context, requestId string, v string) error {
	num, err := d.cache.SetLock(ctx, fmt.Sprintf(model.RedisVideoRequestId, requestId), v, int64(time.Second.Seconds()*30))
	if err != nil {
		return err
	}
	if num == 1 {
		return nil
	}
	return fmt.Errorf("RequestId 异常")
}

func (d *Dao) SMAudioRequestIdSetCache(ctx context.Context, requestId string, v string) error {
	num, err := d.cache.SetLock(ctx, fmt.Sprintf(model.RedisAudioRequestId, requestId), v, int64(time.Second.Seconds()*30))
	if err != nil {
		return err
	}
	if num == 1 {
		return nil
	}
	return fmt.Errorf("RequestId 异常")
}
