package dao

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	rds "github.com/gomodule/redigo/redis"
	"math/rand"
	"strconv"
	"time"

	"golang.org/x/sync/errgroup"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"creativematrix.com/beyondreading/app/api/user/model"
	activitypb "creativematrix.com/beyondreading/app/base/activity/api"
	chatpb "creativematrix.com/beyondreading/app/base/chat/api"
	userpb "creativematrix.com/beyondreading/app/base/user/api"
	"creativematrix.com/beyondreading/pkg/crypto"
	"creativematrix.com/beyondreading/pkg/ecode"
	"creativematrix.com/beyondreading/pkg/encrypt"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/mongo"
	"creativematrix.com/beyondreading/pkg/utils"
)

func (d *Dao) GetUsers(ctx context.Context, ids []string) ([]*userpb.UserInfo, error) {
	infos, err := d.user.GetUsersByIds(ctx, &userpb.UserIdsReq{Ids: ids})
	if err != nil {
		return nil, err
	}
	return infos.Users, nil
}

func (d *Dao) GetGroupCode(ctx context.Context, code string) (*model.TGuildScout, error) {
	data := new(model.TGuildScout)
	err := d.schema[model.TableGuildScout].FindOne(data, bson.M{"scoutNum": code, "status": bson.M{"$ne": model.ScoutResign}})
	if err != nil {
		if err != ecode.NothingFound {
			return nil, err
		} else {
			return nil, nil
		}
	}

	return data, nil
}

func (d *Dao) GetUserInfoByIdM(ctx context.Context, id string) (*model.TUserInfo, error) {
	var userInfo model.TUserInfo

	_id, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, err
	}

	if err := d.schema[model.TableUser].FindOne(&userInfo, bson.M{"_id": _id}); err != nil {
		return nil, err
	}

	return &userInfo, nil
}

// GetUserInfoById rpc调用base
func (d *Dao) GetUserInfoById(ctx context.Context, noId string, cols []string) (*userpb.UserInfo, error) {
	infos, err := d.user.GetUsersByIds(ctx, &userpb.UserIdsReq{Ids: []string{noId}, Cols: cols})

	if err != nil {
		return nil, err
	}
	if len(infos.Users) == 0 {
		return nil, errors.New("user not found")
	}

	return infos.Users[0], nil
}

// GetUserInfoByMId rpc调用base
func (d *Dao) GetUserInfoByMId(ctx context.Context, id string, cols []string) (*userpb.UserInfo, error) {
	infos, err := d.user.GetUsersByMongoIds(ctx, &userpb.MongoIdsReq{Ids: []string{id}, Cols: cols})

	if err != nil {
		return nil, err
	}
	if len(infos.Users) == 0 {
		return nil, errors.New("user not found")
	}

	return infos.Users[0], nil
}

// UpdateUserInfo 更新用户信息
func (d *Dao) UpdateUserInfo(ctx context.Context, userId string, update interface{}) (*model.TUserInfo, error) {
	data, _ := json.Marshal(update)
	req := &userpb.UpdateUserReq{
		Id:   userId,
		Data: data,
	}
	info, err := d.user.UpdateUserInfo(ctx, req)

	if err != nil {
		return nil, err
	}

	return model.ProtoToUser(info, true), nil
}

func (d *Dao) TUser(ctx context.Context) error {
	fmt.Println("ddddddd")

	err := d.SummberBroker.Publish(model.MQ_TEST_Q1, &model.TUser{
		UserId: "SummberBroker123456",
	})
	fmt.Println("err1:", err)

	err2 := d.SummberBroker.Publish(model.MQ_TEST_Q2, &model.TUser{
		UserId: "SummberBroker456123",
	})
	fmt.Println("err1:", err2)

	err3 := d.SummberProducer.Publish(model.MQ_TEST_Q1, &model.TUser{
		UserId: "summberProducer123456",
	})
	fmt.Println("err1:", err3)

	err4 := d.SummberProducer.Publish(model.MQ_TEST_Q2, &model.TUser{
		UserId: "summberProducer456123",
	})
	fmt.Println("err1:", err4)
	return nil
}

func (d *Dao) getNoId(ctx context.Context) (string, error) {
	var tu model.TUserInfo

	noId, err := d.cache.IncrByKey(ctx, model.Redis_UserNOIdCache, 1)
	if err != nil {
		return "", nil
	}

	if noId == 1 {
		if err := d.schema[model.TableUser].FindOne(&tu, bson.M{}, mongo.Sort(bson.M{"noId": -1})); err != nil {
			if err != ecode.NothingFound {
				return "", nil
			}
			noId = d.conf.BeginId
		}
		if tu.NoId != "" {
			noId, _ = strconv.ParseInt(tu.NoId, 10, 64)
		}
		_, err := d.cache.Set(ctx, fmt.Sprintf(model.Redis_UserNOIdCache), noId+1, -1)
		if err != nil {
			return "", nil
		}
	}

	return strconv.FormatInt(noId, 10), nil
}

func (d *Dao) InsertUser(ctx context.Context, lR *model.LoginReq) (string, string, string, error) {
	var (
		noId string
		err  error
	)

	for {
		noId, err = d.getNoId(ctx)
		if err != nil {
			return "", "", "", err
		}

		isPrettyNo := d.ExistPrettyNo(ctx, noId)

		if utils.SkipNum[noId] == "" && !isPrettyNo {
			break
		}
	}

	msgId := encrypt.EecryptRC4([]byte(d.conf.MsgKey.Key), []byte(noId))
	if msgId == "" {
		logger.LogErrorw("InsertUser EecryptRC4", "error", err, "noId", noId)
		return "", "", "", ecode.ServerErr
	}

	u := &model.TUserInfo{}
	u.RegisterStatus = model.UserUnFinish
	u.NoId = noId
	u.MsgId = msgId
	u.Status = model.UserValid
	u.RegistryType = lR.Mode

	var ml model.ModeList
	switch lR.Mode {
	case model.Apple:
		var ab model.AppleBind
		ab.UnionId = lR.UnionId
		ab.Email = lR.Email
		ab.FullName = lR.FullName
		ab.Token = lR.Token
		ab.Secret = lR.Secret

		ml.Apple = &ab
	case model.Weixin:
		var b model.Bind
		b.OpenId = lR.OpenId
		b.UnionId = lR.UnionId

		ml.Weixin = &b
	case model.QQ:
		var b model.Bind
		b.OpenId = lR.OpenId
		b.UnionId = lR.UnionId

		ml.QQ = &b
	case model.Mobile:
		u.Mobile = lR.Mobile
	}
	u.Mode = &ml

	tu, err := d.schema[model.TableUser].Insert(u)
	if err != nil {
		return "", "", "", err
	}

	val, ok := tu.(primitive.ObjectID)
	if ok {
		// 此处要放心队列中，保证一定执行成功
		db, err := d.msshard.DB(val.Hex())
		if err != nil {
			return "", "", "", err
		}
		if _, err := db.ExecContext(ctx, "INSERT INTO accounts (_id) VALUES (?)", val.Hex()); err != nil {
			return "", "", "", err
		}

		return val.Hex(), noId, msgId, nil
	}
	return "", "", "", nil
}

func (d *Dao) UpdateUser(ctx context.Context, lR *model.LoginReq, userId string) error {
	var con primitive.M

	switch lR.Mode {
	case model.Apple:
		x := map[string]string{}
		x["unionId"] = lR.UnionId
		x["fullName"] = lR.FullName
		x["email"] = lR.Email
		x["token"] = lR.Token
		x["secret"] = lR.Secret
		con = bson.M{
			"mode.apple": x,
		}
	case model.Weixin:
		x := map[string]string{}
		x["unionId"] = lR.UnionId
		x["openId"] = lR.OpenId
		con = bson.M{
			"mode.weixin": x,
		}
	case model.QQ:
		x := map[string]string{}
		x["unionId"] = lR.UnionId
		x["openId"] = lR.OpenId
		con = bson.M{
			"mode.qq": x,
		}
	case model.Mobile:
		con = bson.M{
			"mobile": lR.Mobile,
		}
	}

	_id, err := primitive.ObjectIDFromHex(userId)
	if err != nil {
		return err
	}

	update := bson.M{
		"$set": con,
	}
	if _, err := d.schema[model.TableUser].Update(bson.M{
		"_id": _id,
	}, update); err != nil {
		return err
	}

	return nil
}

func (d *Dao) RegisterInfo(ctx context.Context, loginInfo *model.LoginReq) (u model.UserModel, err error) {
	var (
		tu  model.TUserInfo
		con = make(bson.M)
	)
	switch loginInfo.Mode {
	case model.Apple:
		con["mode.apple.unionId"] = loginInfo.UnionId
	case model.Weixin:
		con["mode.weixin.unionId"] = loginInfo.UnionId
	case model.QQ:
		con["mode.qq.unionId"] = loginInfo.UnionId
	case model.Mobile:
		con["mobile"] = loginInfo.Mobile
	}
	con["status"] = model.UserValid
	con["registerStatus"] = bson.M{"$ne": model.UserLogoff}
	if err := d.schema[model.TableUser].FindOne(&tu, con); err != nil {
		if err == ecode.NothingFound {
			u.RegisterStatus = model.UserNone
			return u, nil
		}
		return u, err
	}

	if tu.RegisterStatus == model.UserNone {
		lock, _ := d.cache.SetLock(ctx, fmt.Sprintf(model.RedisSmsLoginLock, loginInfo.Mobile), 1, 5)
		if lock != 1 {
			err = model.MobileLoginErr
			return
		}
	}

	u.ID = tu.Id
	u.NoId = tu.NoId
	u.MsgId = tu.MsgId
	u.Gender = tu.Gender
	u.RegisterStatus = tu.RegisterStatus
	u.ForbidDays = tu.ForbidDays
	u.ForbidTime = tu.ForbidTime

	return u, nil
}

func (d *Dao) UserSetFree(ctx context.Context, noId string) error {
	_, err := d.schema[model.TableUser].Update(bson.M{"noId": noId}, bson.M{"$set": bson.M{
		"registerStatus": model.UserFinish,
		"forbidDays":     0,
		"forbidReason":   "",
		"forbidTime":     0,
	}})

	return err
}

func (d *Dao) GetNickName(ctx context.Context, ltype string) ([]*model.TNickName, error) {
	var tnn []*model.TNickName

	if err := d.schema[model.TableNickName].Find(&tnn, bson.M{"type": ltype, "status": "published"}); err != nil {
		return nil, err
	}
	return tnn, nil
}

func (d *Dao) UpdateLonLat(ctx context.Context, lonLat *model.LonLatReq, userId string) error {
	id, _ := primitive.ObjectIDFromHex(userId)

	updated := bson.M{"$set": lonLat}
	result, err := d.schema[model.TableUser].Update(bson.M{"_id": id}, updated)

	if err != nil || result.MatchedCount == 0 {
		return model.UpdateLonLat
	}

	return err
}

func (d *Dao) SendEsUpdateQ(ctx context.Context, updateES *model.UpdateES) error {
	return d.SummberBroker.Publish(model.UpdateEsQueue, updateES)
}

func (d *Dao) UpdateLoginInfo(ctx context.Context, li *model.LoginInfoReq, userId string) error {
	var tu *model.TUserInfo

	id, _ := primitive.ObjectIDFromHex(userId)
	if err := d.schema[model.TableUser].FindOne(&tu, bson.M{"_id": id}); err != nil {
		return err
	}

	if tu.RegisterStatus == "finish" {
		return model.LoginInfoFinishInitError
	}
	li.RegisterStatus = "finish"
	updated := bson.M{"$set": li}

	result, err := d.schema[model.TableUser].Update(bson.M{"_id": id}, updated)
	if err != nil || result.MatchedCount == 0 {
		return model.LoginInfoRegInitError
	}
	return err
}

func (d *Dao) GetDefaultAvatar(gender string) string {
	data := make([]*model.TDefaultAvatar, 0)
	err := d.schema[model.TableDefaultAvatar].Find(&data, bson.M{"gender": gender})
	if err != nil {
		return ""
	}

	if len(data) > 0 {
		return data[rand.Intn(len(data))].ImgUrl
	}
	return ""
}

// GetAvatars 获取系统配置的默认头像
func (d *Dao) GetAvatars() map[string][]string {
	res := make(map[string][]string)
	data := make([]*model.TDefaultAvatar, 0)
	err := d.schema[model.TableDefaultAvatar].Find(&data, bson.M{})
	if err != nil {
		return res
	}

	for _, d := range data {
		if a, ok := res[d.Gender]; ok {
			a = append(a, d.ImgUrl)
			res[d.Gender] = a
		} else {
			res[d.Gender] = []string{d.ImgUrl}
		}
	}

	return res
}

// SetUserAction 设置备注名,点赞
func (d *Dao) SetUserAction(ctx context.Context, data *model.TUserAction) error {
	session, err := d.NewMongoSession(ctx)
	if err != nil {
		return err
	}

	defer func() {
		if err != nil {
			_ = session.Rollback()
		} else {
			err = session.Commit()
			if err != nil {
				_ = session.Rollback()
			}
		}
		session.Close()
	}()

	_, err = session.Table(model.TableUserAction).Update(bson.M{"userId": data.UserId, "dstUser": data.DstUser}, bson.M{"$set": data}, mongo.Upsert())
	if err != nil {
		return err
	}

	_, err = session.Table(model.TableUser).Update(bson.M{"noId": data.DstUser}, bson.M{"$inc": bson.M{"like": 1}})
	return err
}

// GetUserAction 查询备注名,点赞
func (d *Dao) GetUserAction(ctx context.Context, userId, dstUser string) (*model.TUserAction, error) {
	var action model.TUserAction
	err := d.schema[model.TableUserAction].FindOne(&action, bson.M{"userId": userId, "dstUser": dstUser})

	if err != nil {
		if err == ecode.NothingFound {
			return &action, nil
		}
		return nil, err
	}

	return &action, nil
}

// GetMovedStatus 获取心动和关注状态
func (d *Dao) GetMovedStatus(ctx context.Context, from, to string) (*chatpb.RelationGetRsp, error) {
	result, err := d.baseChat.RelationGet(ctx, &chatpb.RelationGetReq{
		From: from,
		To:   to,
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// GetAlbum 获取相册信息,返回图片是否审核
func (d *Dao) GetAlbum(ctx context.Context, userId string) ([]model.UserImg, error) {
	result := new(model.RspAlbum)
	id, _ := primitive.ObjectIDFromHex(userId)

	err := d.schema[model.TableUser].FindOne(result, bson.M{"_id": id}, mongo.Projection(bson.M{"album": true}))
	if err != nil {
		return nil, err
	}

	return result.Album, nil
}

func (d *Dao) UpdateAlbum(ctx context.Context, userId string, data []*model.UserImg) error {
	id, _ := primitive.ObjectIDFromHex(userId)
	_, err := d.schema[model.TableUser].Update(bson.M{"_id": id}, bson.M{"$set": bson.M{"album": data}})

	if err != nil {
		return err
	}

	return nil
}

// GetFriends 获取个人中心聚合的好友数据
// userId 传 noId
func (d *Dao) GetFriends(c context.Context, userId string) (*chatpb.RelationNumRsq, error) {
	rsp, err := d.baseChat.RelationNum(c, &chatpb.RelationNumReq{UserId: userId})

	if err != nil {
		return nil, err
	}

	return rsp, nil
}

func (d *Dao) GetAudioHint(c context.Context, sort float64) (*model.TAudioHint, error) {
	result := make([]*model.TAudioHint, 0)
	err := d.schema[model.TableAudioHint].Find(&result, bson.M{"status": 1, "sort": bson.M{"$gt": sort}}, mongo.Limit(2))

	if err != nil {
		return nil, err
	}

	if len(result) > 1 {
		return result[0], nil
	} else if len(result) == 1 {
		result[0].Sort = 0
		return result[0], nil
	}

	return nil, model.AudioHintNoMore
}

// SaveFeedBack 保存反馈举报内容
func (d *Dao) SaveFeedBack(data *model.TUserFeedBack) error {
	_, err := d.schema[model.TableFeedBack].Insert(data)

	return err
}

// GetFeedbackList 获取反馈类型
func (d *Dao) GetFeedbackList(ctx context.Context, category string) ([]*model.TFeedbackType, error) {
	data := make([]*model.TFeedbackType, 0)
	err := d.schema[model.TableFeedbackType].Find(&data, bson.M{"category": category}, mongo.Sort(bson.M{"sort": 1}))
	if err != nil {
		return nil, err
	}

	return data, nil
}

// IsRepeatLogoff 查询是否重复申请注销
func (d *Dao) IsRepeatLogoff(ctx context.Context, noId string) (bool, error) {
	data := make([]*model.TUserLogoff, 0)
	err := d.schema[model.TableLogoff].Find(&data, bson.M{"noId": noId, "status": bson.M{
		"$in": bson.A{model.LogoffApply, model.LogoffCancel}}})

	if err != nil {
		return false, err
	}

	timeLimit, _ := time.Parse(model.DateLayout, time.Now().Format(model.DateLayout))
	timeLimit = timeLimit.AddDate(0, 0, -6)
	for _, d := range data {
		dt, _ := time.Parse(model.DateLayout, d.ReqTime)
		if dt.After(timeLimit) || dt.Equal(timeLimit) {
			return true, nil
		}
	}

	return false, nil
}

// RecordLogoff 保存注销记录
func (d *Dao) RecordLogoff(ctx context.Context, noId string) error {
	data := &model.TUserLogoff{
		NoId:    noId,
		Status:  model.LogoffApply,
		ReqTime: time.Now().Format(model.DateLayout),
	}

	_, err := d.schema[model.TableLogoff].Insert(data)

	return err
}

// IsApplyLogoff 是否正在申请注销
func (d *Dao) IsApplyLogoff(ctx context.Context, noId string) (bool, error) {
	var data model.TUserLogoff
	err := d.schema[model.TableLogoff].FindOne(&data, bson.M{"noId": noId, "status": model.LogoffApply})

	if err != nil {
		if err != ecode.NothingFound {
			return false, err
		}
		return false, nil
	}

	return true, nil
}

func (d *Dao) GetLogoffRecord(ctx context.Context, noId string) (*model.TUserLogoff, error) {
	data := new(model.TUserLogoff)
	err := d.schema[model.TableLogoff].FindOne(data, bson.M{"noId": noId, "status": model.LogoffApply},
		mongo.Sort(bson.M{"_id": -1}))
	if err != nil {
		if err != ecode.NothingFound {
			return nil, err
		}
	}

	return data, nil
}

// CancelLogoff 取消注销
func (d *Dao) CancelLogoff(ctx context.Context, noId string) error {
	_, err := d.schema[model.TableLogoff].Update(bson.M{"noId": noId, "status": model.LogoffApply},
		bson.M{"$set": bson.M{"status": model.LogoffCancel}}, mongo.Multi())

	return err
}

func (d *Dao) InviteVerifyMsg(noId, dstUser, from, to string, dst *model.TUserInfo) {
	data := new(model.InviteVerifyRecord)
	err := d.schema[model.TUserInviteVerify].FindOne(data, bson.M{"from": noId, "to": dstUser})
	if err != nil && err != ecode.NothingFound {
		return
	}

	isReal := false
	//限制邀请次数
	if data.From == "" {
		data.From = noId
		data.To = dstUser
		data.IsInvite = true
		if dst.RealNameStatus == model.Success {
			data.IsShow = true
			isReal = true
		} else {
			data.IsShow = false
		}
		_, err = d.schema[model.TUserInviteVerify].Insert(data)
		if err != nil {
			logger.LogErrorf("send invite msg err:%s", err.Error())
			return
		}
	} else {
		if data.IsShow {
			return
		} else {
			if dst.RealNameStatus == model.Success {
				isReal = true
				_, err = d.schema[model.TUserInviteVerify].Update(bson.M{"from": noId, "to": dstUser},
					bson.M{"$set": bson.M{"isShow": true}})
				if err != nil {
					logger.LogErrorf("send invite msg err:%s", err.Error())
					return
				}
			} else {
				return
			}
		}
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*3)
	defer cancel()
	if dst.RealStatus != model.Success {
		dst.RealStatus = model.Reject
	}
	if dst.RealNameStatus != model.Success {
		dst.RealNameStatus = model.Reject
	}
	msg := &chatpb.VerifyMsg{
		Content: "show verify msg",
		Extra: &chatpb.VerifyMsgExtra{
			Subject: chatpb.Subject_PrivateCode,
			Data: &chatpb.VerifyMsgData{
				SubType: model.VerifyMsgType,
				Attach: &chatpb.VerifyMsgAttach{
					IsReal:         isReal,
					RealStatus:     dst.RealStatus,
					RealNameStatus: dst.RealNameStatus,
				},
			},
		},
	}
	rmsgToQ := chatpb.SendMessageReq{
		Level: chatpb.Level_Mid,
		From:  from,
		To:    to,
		Mode:  chatpb.Mode_Private,
		Data:  utils.JsonByte(msg),
	}

	_, err = d.baseChat.SendMessage(ctx, &rmsgToQ)
	if err != nil {
		logger.LogErrorf("invite verify msg:%s", err.Error())
	}
}

func (d *Dao) CheckUserMedia(event model.Event, data []byte) error {
	user := &model.CheckUser{
		Event: event,
		Data:  data,
	}
	err := d.thirdBroker.Publish(model.MQThird, user)
	if err != nil {
		logger.LogErrorw("send user review to queue", string(event), err.Error())
		return err
	}

	return nil
}

func (d *Dao) GetReviewRule(ctx context.Context, ruleType string) (*model.TReviewRule, error) {
	rule := new(model.TReviewRule)
	jsonStr, err := d.cache.HGet(ctx, model.RedisReviewRule, ruleType)

	if err != nil {
		logger.LogErrorf("cache rule not found:%s", err.Error())
	} else {
		err = utils.Transform([]byte(jsonStr), rule)
		if err == nil {
			return rule, nil
		}
	}

	err = d.schema[model.TableReviewRule].FindOne(rule, bson.M{"type": ruleType})

	if err != nil {
		return nil, err
	}

	return rule, nil
}

func (d *Dao) InsertSecretMsg(ctx context.Context, noId, msg string) error {
	msgModel := model.PlusF{
		Title:      "用户举报",
		Descriptor: msg,
	}

	data := &model.TOfSessionList{
		UserId:     noId,
		Namespace:  model.NSecretary,
		Type:       chatpb.OfSessionType_PlusF.String(),
		UpdateTime: time.Now(),
		Message:    utils.JsonString(msgModel),
	}

	_, err := d.summerConnChat.Model(model.TableOfSessionList).Insert(data)

	return err
}

func (d *Dao) MakePullSecret(ctx context.Context, noId, title string) {
	users, err := d.user.GetUsersByIds(ctx, &userpb.UserIdsReq{
		Ids:  []string{noId},
		Cols: []string{"msgId"},
	})

	if err != nil || len(users.Users) == 0 {
		logger.LogErrorw("user not found")
		return
	}

	rmsg := &chatpb.Secretary{
		Content: "pull secret",
		Extra: &chatpb.SecretaryChatExtra{
			Subject: chatpb.Subject_SecretaryCode,
			Data: &chatpb.SecretaryData{
				SubType: chatpb.SubType_SecretaryPull.String(),
				Attach: &chatpb.SecretaryAttach{
					Status: "OK", Title: title,
				},
			},
		},
	}

	rmsgToQ := chatpb.SendMessageReq{
		Level: chatpb.Level_Mid,
		From:  d.conf.SystemUser.MsgId,
		To:    users.Users[0].MsgId,
		Mode:  chatpb.Mode_Private,
		Data:  utils.JsonByte(rmsg),
	}

	_, err = d.baseChat.SendMessage(ctx, &rmsgToQ)
	if err != nil {
		logger.LogErrorw("MakePullSecret", err.Error())
	}
}

func (d *Dao) IncrFocusInLiveSet(ctx context.Context, anchorId, noId string) error {
	return d.liveCache.SAdd(ctx, fmt.Sprintf(model.RedisNewFocusLive, anchorId), noId)
}

func (d *Dao) GetLiveCacheStr(ctx context.Context, key string) (string, error) {
	return d.liveCache.GetString(ctx, key)
}

func (d *Dao) QueryUserById(ctx context.Context, param *model.ReqQueryUser) (*model.TUserInfo, error) {
	query := make(bson.M)
	if param.NoId != "" {
		query["noId"] = param.NoId
	}
	if param.Mobile != "" {
		query["mobile"] = param.Mobile
	}
	if param.UserId != "" {
		id, e := primitive.ObjectIDFromHex(param.UserId)
		if e != nil {
			return nil, ecode.NothingFound
		}
		query["_id"] = id
	}
	query["status"] = model.UserValid

	user := new(model.TUserInfo)
	err := d.schema[model.TableUser].FindOne(user, query)
	if err != nil {
		return nil, err
	}

	if user.RegisterStatus == model.UserLogoff {
		return nil, model.UserLogoffError
	}

	return user, nil
}

// GetDecorateStatus 装扮中心是否显示红点, 是否商城上新
func (d *Dao) GetDecorateStatus(ctx context.Context, noId string) (bool, bool) {
	var (
		isGet bool
		isNew bool
		pipe  = d.cache.Pipeline(ctx)
	)

	pipe.Send("get", model.RedisAddDecoTimestamp) //最新添加装扮的时间
	pipe.Send("hget", model.RedisClickDecoTimestamp, noId)
	pipe.Send("hset", model.RedisClickDecoTimestamp, noId, time.Now().Unix())
	rec, err := pipe.Receive()
	if err == nil {
		add, _ := rds.Int64(rec[0], nil)
		userTime, _ := rds.Int64(rec[1], nil)
		isNew = add > userTime
	}

	data := new(model.TDecClickStatus)
	err = d.schema[model.TableDecorateStatus].FindOne(data, bson.M{"noId": noId})
	if err != nil {
		if err != ecode.NothingFound {
			return false, false
		}
	}

	_, err = d.schema[model.TableDecorateStatus].Update(bson.M{"noId": noId}, bson.M{"$set": bson.M{
		"noId":   noId,
		"click":  model.UnClick,
		"upload": model.DecoOld,
	}}, mongo.Upsert())
	if err != nil {
		return isGet, isNew
	}

	return data.Click == model.Click, isNew
}

func (d *Dao) GetUsersDecorate(ctx context.Context, noIds []string, category string) (map[string]*activitypb.Decorate, error) {
	dec, err := d.activity.MultiGetUsersDec(ctx, &activitypb.GetUsersDecReq{NoIds: noIds, Category: category})
	if err != nil {
		return nil, err
	}

	return dec.Decorates, nil
}

func (d *Dao) GetUserDecs(ctx context.Context, noId string, categories []string) (map[string]*activitypb.Decorate, error) {
	decs, err := d.activity.GetUserDecs(ctx, &activitypb.GetUserDecsReq{NoId: noId, Categories: categories})
	if err != nil {
		return nil, err
	}

	return decs.Decorates, nil
}

func (d *Dao) MultiGetUsersDec(ctx context.Context, noIds []string, category string) (map[string]*activitypb.Decorate, error) {
	rsp, err := d.activity.MultiGetUsersDec(ctx, &activitypb.GetUsersDecReq{
		NoIds:    noIds,
		Category: category,
	})
	if err != nil {
		return nil, err
	}
	return rsp.Decorates, nil
}

func (d *Dao) GetUserDecLimit(ctx context.Context, noId, category string, limit int64) ([]*activitypb.Decorate, error) {
	decs, err := d.activity.DecorateManyGet(ctx, &activitypb.DecorateManyGetReq{
		NoId: noId, Category: category, Limit: limit})

	if err != nil {
		return nil, err
	}
	return decs.Decorates, nil
}

func (d *Dao) GetUserAllDec(ctx context.Context, noId, scene string) (*model.Decorate, error) {
	var (
		err      error
		decs     map[string]*activitypb.Decorate
		prettyNo map[string]*activitypb.Decorate
		medals   []*activitypb.Decorate
		eg       errgroup.Group
	)

	eg.Go(func() error {
		decs, err = d.GetUserDecs(ctx, noId, model.Decs)
		return err
	})
	eg.Go(func() error {
		prettyNo, err = d.MultiGetUsersDec(ctx, []string{noId}, model.DecoratePrettyNo)
		return err
	})
	eg.Go(func() error {
		medals, err = d.GetUserDecLimit(ctx, noId, model.DecorateMedal, 5)
		// 私信页面仅展示超管勋章
		if scene == model.ChatScene {
			medals = d.FilterSuperAdminDecorate(medals)
		}
		return err
	})
	if err = eg.Wait(); err != nil {
		return nil, err
	}

	rsp := new(model.Decorate)
	decIds := make(map[string]string)
	for _, dec := range decs {
		decIds[dec.Category] = dec.Serial
	}
	_ = utils.JsonCopy(decIds, rsp)
	for _, medal := range medals {
		rsp.Medals = append(rsp.Medals, medal.Serial)
	}
	if pm, ok := prettyNo[noId]; ok && pm != nil {
		rsp.PrettyNo = &model.PrettyNo{
			Serial:       pm.Serial,
			ImgUrl:       pm.ImgUrl,
			AnimationUrl: pm.AnimationUrl,
			Speed:        pm.Seconds,
		}
	}

	return rsp, nil
}

func (d *Dao) LatestLoginCount(ctx context.Context, noId string, days int) int64 {
	return d.schema[model.TableUserDeviceLog].Count(bson.M{
		"noId": noId,
		"loginTime": bson.M{
			"$gt": time.Now().AddDate(0, 0, -days).Unix()},
	})
}

func (d *Dao) SendLiveMQ(anchorId, chooseCode, source string) {
	data := model.LiveBaseMessage{
		Event: model.MQBindAnchorEvent,
		Ts:    time.Now().Unix(),
		Data: model.GuildAnchor{
			AnchorId:   anchorId,
			ChooseCode: chooseCode,
			Source:     source,
		},
	}

	_ = d.thirdBroker.Publish(model.MQLive, data)
}

func (d *Dao) GetScoutNum(ctx context.Context, idCard string) (string, error) {
	cardNum, _ := crypto.Decrypt(idCard, []byte(d.userTokenSecret))
	idCardKey := utils.Md5String(cardNum)
	user := new(model.TUserInfo)
	err := d.schema[model.TableUser].FindOne(user,
		bson.M{
			"registerStatus": model.UserLogoff,
			"idCardKey":      idCardKey,
		},
		mongo.Sort(bson.M{"_id": -1}))
	if err != nil {
		return "", err
	}

	guildAnchor := new(model.TGuildAnchor)
	err = d.schema[model.TableGuildAnchor].FindOne(guildAnchor, bson.M{"noId": user.NoId, "status": bson.M{"$ne": "remove"}})
	if err != nil {
		return "", err
	}

	scout := new(model.TGuildScout)
	err = d.schema[model.TableGuildScout].FindOne(scout, bson.M{"_id": guildAnchor.ScoutId})
	if err != nil {
		return "", err
	}

	return scout.ScoutNum, nil
}

func (d *Dao) GetSuperAdminDecorate() []string {
	var (
		s []string
	)
	s = append(s, d.conf.Decorate.Official...)
	s = append(s, d.conf.Decorate.SuperAdmin...)
	return s
}

func (d *Dao) FilterSuperAdminDecorate(userMedals []*activitypb.Decorate) []*activitypb.Decorate {
	var (
		medals []*activitypb.Decorate
		all    = d.GetSuperAdminDecorate()
	)
	for _, medal := range userMedals {
		if utils.Contains(all, medal.Serial) {
			medals = append(medals, medal)
		}
	}
	return medals
}

func (d *Dao) SetLock(ctx context.Context, key string, sec int64) bool {
	p := d.cache.Pipeline(ctx)
	p.Send("set", key, 0, "ex", sec, "nx")
	rec, err := p.Receive()
	if err != nil {
		return false
	}
	res, _ := rds.String(rec[0], nil)

	return res == model.LockOK
}

func (d *Dao) GetLocationByIP(ctx context.Context, ip string) (*userpb.IPLocRsp, error) {
	location, err := d.user.GetIPLocation(ctx, &userpb.IPLocReq{Ip: ip})
	if err != nil {
		return nil, err
	}
	return location, nil
}

func (d *Dao) SwitchGet(ctx context.Context, name string) (*model.TSwitch, error) {
	var rsp = new(model.TSwitch)
	err := d.schema[model.TableSwitch].FindOne(&rsp, bson.M{
		"name": name,
	})
	if err != nil {
		if err != ecode.NothingFound {
			return nil, err
		}
		rsp.Status = false
		rsp.Name = name

		_, err = d.schema[model.TableSwitch].Insert(&rsp)
	}
	return rsp, err
}

func (d *Dao) GetUserContact(ctx context.Context) *model.Contact {
	str, _ := d.cache.GetString(ctx, model.RedisUserContact)
	if str != "" {
		contact := new(model.Contact)
		err := json.Unmarshal([]byte(str), contact)
		if err != nil {
			return nil
		}
		return contact
	}
	return nil
}
