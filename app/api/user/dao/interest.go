package dao

import (
	"context"
	"creativematrix.com/beyondreading/app/api/user/model"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (d *Dao) GetInterestTagLib(ctx context.Context) ([]*model.InterestTagLib, error) {
	var result []*model.InterestTagLib

	if err := d.schema[model.TableInterestTagLib].Find(&result, bson.M{}); err != nil {
		return nil, err
	}

	return result, nil
}

func (d *Dao) GetInterestTagByIds(ctx context.Context, ids []primitive.ObjectID) (map[string]*model.InterestTagLib, error) {
	var ret []*model.InterestTagLib
	query := bson.M{
		"_id": bson.M{
			"$in": ids,
		},
	}

	if err := d.schema[model.TableInterestTagLib].Find(&ret, query); err != nil {
		return nil, err
	}

	result := make(map[string]*model.InterestTagLib)
	for _, v := range ret {
		result[v.Id.Hex()] = v
	}

	return result, nil
}

func (d *Dao) InterestTagSave(ctx context.Context, noId string, tagIds []string) error {
	filter := bson.M{
		"noId": noId,
	}
	if err := d.schema[model.TableInterestTagAdded].Delete(filter); err != nil {
		return err
	}
	if len(tagIds) > 0 {
		idsObj := make([]primitive.ObjectID, 0)

		for _, id := range tagIds {
			idObj, err := primitive.ObjectIDFromHex(id)
			if err != nil {
				return err
			}
			idsObj = append(idsObj, idObj)
		}
		libs, err := d.GetInterestTagByIds(ctx, idsObj)
		if err != nil {
			return err
		}

		adds := make([]interface{}, 0)
		for _, v := range tagIds {
			if tag, ok := libs[v]; ok {
				adds = append(adds, &model.InterestTagAdded{
					NoId:    noId,
					TagId:   v,
					TagName: tag.Name,
				})
			}
		}

		if _, err = d.schema[model.TableInterestTagAdded].InsertMany(adds); err != nil {
			return err
		}
	}

	return nil
}

func (d *Dao) DelInterestTag(ctx context.Context, ids []string) error {
	idsObj := make([]primitive.ObjectID, 0)

	for _, id := range ids {
		idObj, err := primitive.ObjectIDFromHex(id)
		if err != nil {
			return err
		}
		idsObj = append(idsObj, idObj)
	}

	filter := bson.M{
		"tagId": bson.M{
			"$in": idsObj,
		},
	}
	if err := d.schema[model.TableInterestTagAdded].Delete(filter); err != nil {
		return err
	}

	return nil
}

func (d *Dao) GetInterestTagAdded(ctx context.Context, noId string) ([]*model.InterestTagAddedVO, error) {
	var ret []*model.InterestTagAdded

	query := bson.M{
		"noId": noId,
	}

	if err := d.schema[model.TableInterestTagAdded].Find(&ret, query); err != nil {
		return nil, err
	}

	result := make([]*model.InterestTagAddedVO, 0)
	for _, r := range ret {
		result = append(result, &model.InterestTagAddedVO{
			TagId:   r.TagId,
			TagName: r.TagName,
		})
	}

	return result, nil
}
