package dao

import (
	"fmt"

	"creativematrix.com/beyondreading/app/api/user/conf"
	accountpb "creativematrix.com/beyondreading/app/base/account/api"
	activity "creativematrix.com/beyondreading/app/base/activity/api"
	chatpb "creativematrix.com/beyondreading/app/base/chat/api"
	livepb "creativematrix.com/beyondreading/app/base/live/api"
	userpb "creativematrix.com/beyondreading/app/base/user/api"
	"creativematrix.com/beyondreading/pkg/check/shumei"
	"creativematrix.com/beyondreading/pkg/elastic"
	fc "creativematrix.com/beyondreading/pkg/face"
	"creativematrix.com/beyondreading/pkg/minio"
	"creativematrix.com/beyondreading/pkg/mongo"
	"creativematrix.com/beyondreading/pkg/mq"
	"creativematrix.com/beyondreading/pkg/mysql"
	"creativematrix.com/beyondreading/pkg/rabbitmq"
	"creativematrix.com/beyondreading/pkg/redis"
	"creativematrix.com/beyondreading/pkg/wangsu"
	"github.com/BurntSushi/toml"
)

var d Dao

func init() {
	app := "api-user"
	c, err := loadConf(app)
	if err != nil {
		panic(err)
	}

	summerConn := mongo.Connect(c.MongodbUser)

	summerConnChat := mongo.Connect(c.MongodbChat)

	schema := map[string]*mongo.Model{}

	for _, name := range summerTable {
		schema[name] = summerConn.Model(name)
	}
	baseChat, err := chatpb.NewClient(c.Base)
	if err != nil {
		panic(err)
	}

	user, err := userpb.NewClient(c.Base)
	if err != nil {
		panic(err)
	}

	account, err := accountpb.NewClient(c.Base)
	if err != nil {
		panic(err)
	}

	es, err := elastic.NewEsPool(c.ElasticSearch)
	if err != nil {
		panic(err)
	}

	minIO, err := minio.NewMinio(c.Minio)
	if err != nil {
		panic(err)
	}

	baseLive, err := livepb.NewClient(c.Base)
	if err != nil {
		panic(err)
	}

	act, err := activity.NewClient(c.Base)
	if err != nil {
		panic(err)
	}

	d = Dao{
		msshard:       mysql.New(c.Mysql),
		es:            es,
		schema:        schema,
		thirdBroker:   rabbitmq.NewBroker(c.RabbitMQ.URL, c.RabbitMQ.ThirdEx),
		SummberBroker: rabbitmq.NewBroker(c.RabbitMQ.URL, c.RabbitMQ.SummerEx),
		SummberProducer: mq.NewProducer(&mq.ProducerOptions{
			BrokerURL: c.RabbitMQ.URL,
			ExchangeOpt: &mq.ExchangeOption{
				Type: c.RabbitMQ.SummerEx.Type,
				Name: c.RabbitMQ.SummerEx.Name,
			},
		}),
		baseChat:   baseChat,
		account:    account,
		activity:   act,
		cache:      redis.Load(c.RedisUser),
		liveCache:  redis.Load(c.RedisLive),
		wangsu:     wangsu.NewWangsu(c.Wangsu),
		user:       user,
		summerConn: summerConn,
		face:       fc.FaceClient(fc.FaceID, c.Face),
		sm:         shumei.SM(c.SM),
		conf:       c,

		userTokenSecret: c.UserTokenSecret,
		minio:           minIO,
		baseLive:        baseLive,
		summerConnChat:  summerConnChat,
	}
}

func loadConf(app string) (*conf.Config, error) {
	cf := &conf.Config{}
	if _, err := toml.DecodeFile("../../../base.toml", cf); err != nil {
		return nil, err
	}
	if _, err := toml.DecodeFile(fmt.Sprintf("../cmd/%s.toml", app), cf); err != nil {
		return nil, err
	}
	return cf, nil
}
