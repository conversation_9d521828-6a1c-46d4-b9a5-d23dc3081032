//Description faceID结果
//Date        2021/8/3
//User        cl

package dao

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/go-resty/resty/v2"

	mgd "go.mongodb.org/mongo-driver/mongo"

	"go.mongodb.org/mongo-driver/bson"

	"github.com/gomodule/redigo/redis"

	"creativematrix.com/beyondreading/app/api/user/model"
	"creativematrix.com/beyondreading/pkg/crypto"
	"creativematrix.com/beyondreading/pkg/ecode"
	face "creativematrix.com/beyondreading/pkg/face"
	"creativematrix.com/beyondreading/pkg/mongo"
	"creativematrix.com/beyondreading/pkg/utils"
)

func (d *Dao) GetVerifyNum(ctx context.Context, noId, compareType string) (int, error) {
	num, err := d.cache.GetString(ctx, fmt.Sprintf(model.RedisFaceNumLimit, compareType,
		noId, time.Now().Format(model.DateLayout)))
	if err != nil && err != redis.ErrNil {
		return 0, err
	}

	count, _ := strconv.Atoi(num)
	return count, nil
}

func (d *Dao) CacheVerifyNum(ctx context.Context, noId, compareType string) error {
	key := fmt.Sprintf(model.RedisFaceNumLimit, compareType, noId, time.Now().Format(model.DateLayout))
	exist, _ := d.cache.ExistKey(ctx, key)
	if exist {
		return d.cache.IncrKey(ctx, key)
	} else {
		ttl := (23-time.Now().Hour())*3600 + (60-time.Now().Minute())*60
		_, err := d.cache.Set(ctx, key, 1, int64(ttl))
		return err
	}
}

func (d *Dao) CacheUserGender(ctx context.Context, noId, gender string) error {
	_, err := d.cache.Set(ctx, fmt.Sprintf(model.RedisFaceIDGender, noId), gender, 120)

	return err
}

func (d *Dao) GetCacheGender(ctx context.Context, noId string) (string, error) {
	return d.cache.GetString(ctx, fmt.Sprintf(model.RedisFaceIDGender, noId))
}

func (d *Dao) UpdateEsData(ctx context.Context, userId string, data interface{}) error {
	if client, ok := d.es.Pool[model.Default]; ok {
		_, err := client.Update().Index(model.UserIndex).Id(userId).Doc(data).Do(ctx)
		if err != nil {
			return err
		}
	}

	return nil
}

func (d *Dao) GetFaceToken(userId, liveType, comparisonType, idCard, name, imgRef, img string) (*face.RspToken, error) {
	reqParam := &face.ReqToken{
		UserId:         userId,
		ComparisonType: comparisonType,
		LiveNessType:   liveType,
		IdCardNum:      idCard,
		IdCardName:     name,
		ImageRef:       imgRef,
		Image:          img,
	}

	return d.face.GetToken(reqParam)
}

func (d *Dao) FaceIDVerify(comparisonType, bizToken, megData string) (*face.RspVerify, error) {
	return d.face.Verify(comparisonType, bizToken, megData)
}

func (d *Dao) SavaFaceResult(ctx context.Context, data *model.TUserFaceVerify) error {
	_, err := d.schema[model.TableUserFaceVerify].Insert(data)

	return err
}

func (d *Dao) OcrIDCard(imagePath string) (*model.IDCardInfo, error) {
	rsp := new(model.IDCardInfo)

	_, err := resty.New().R().SetMultipartFormData(map[string]string{
		"api_key":    d.conf.Face.FaceID.AppIdYY,
		"api_secret": d.conf.Face.FaceID.SecretYY,
	}).SetFile("image", imagePath).SetResult(rsp).Post(d.conf.Face.FaceID.OcrUrl)

	if err != nil {
		return nil, err
	}

	return rsp, nil
}

func (d *Dao) UploadFaceBest(filename string) (string, error) {
	imgUrl, err := d.wangsu.SimpleUpload(filename, "user/real/"+filename)
	if err != nil {
		return "", err
	}

	if i := strings.Index(imgUrl, "&"); i > 0 {
		imgUrl = imgUrl[:i]
	}
	return strings.TrimPrefix(imgUrl, d.conf.BaseImgUrl), nil
}

// GetRealAvatar 获取真人认证留底头像
func (d *Dao) GetRealAvatar(ctx context.Context, noId string) (string, error) {
	data := new(model.TReviewFace)
	err := d.schema[model.TableReviewFace].FindOne(data, bson.M{"noId": noId})
	if err != nil {
		return "", err
	}

	return data.RealUrl, nil
}

func (d *Dao) LocUpload(ctx context.Context, filename, filepath string) (string, error) {
	err := d.minio.UploadFile(ctx, model.IDCardBucket, filename, filepath)
	if err != nil {
		return "", err
	}

	return fmt.Sprintf("%s/%s", model.IDCardBucket, filename), nil
}

func (d *Dao) UpdateIdCardInfo(ctx context.Context, noId, idName, idCard string) error {
	idCardKey := utils.Md5String(idCard)
	user := new(model.MyInfo)
	err := d.schema[model.TableUser].FindOne(user, bson.M{"idCardKey": idCardKey, "realNameStatus": bson.M{
		"$in": []string{model.Success, model.Process}}}, mongo.Select([]string{"noId"}))
	if err != nil && err != ecode.NothingFound {
		return ecode.ServerErr
	}

	if user.NoId != "" {
		return model.IDCardMultiErr
	}

	en, _ := crypto.Encrypt([]byte(idCard), []byte(d.userTokenSecret))
	_, err = d.schema[model.TableUser].Update(bson.M{"noId": noId}, bson.M{"$set": bson.M{
		"idName": idName, "idCard": en, "idCardKey": idCardKey}})

	return err
}

func (d *Dao) ReqRealName(ctx context.Context, data *model.TRealName) error {
	_, err := d.summerConn.Transaction(ctx, func(sessCtx mgd.SessionContext) (interface{}, error) {
		_, e := d.summerConn.Model(model.TableRealName).Update(bson.M{"noId": data.NoId, "log": primitive.Null{}}, bson.M{
			"$set": bson.M{"log": true}},
			mongo.Context(sessCtx), mongo.Multi())
		if e != nil {
			return nil, e
		}

		_, e = d.summerConn.Model(model.TableUser).Update(bson.M{"noId": data.NoId}, bson.M{"$set": bson.M{"realNameStatus": data.Status}}, mongo.Context(sessCtx))
		return nil, e
	})
	if err != nil {
		return err
	}

	_, err = d.schema[model.TableRealName].Insert(data)

	return err
}

func (d *Dao) GetRealNameInfo(ctx context.Context, noId string) (*model.RspRealName, error) {
	data := new(model.RspRealName)
	err := d.schema[model.TableRealName].FindOne(data, bson.M{"noId": noId})

	return data, err
}
