package dao

import (
	"context"

	"creativematrix.com/beyondreading/app/api/user/model"
	chatpb "creativematrix.com/beyondreading/app/base/chat/api"
	livepb "creativematrix.com/beyondreading/app/base/live/api"
	userpb "creativematrix.com/beyondreading/app/base/user/api"
)

func (d *Dao) LiveInfo(ctx context.Context, req *livepb.LiveInfoReq) (*livepb.LiveInfoRsp, error) {
	return d.baseLive.LiveInfo(ctx, req)
}

func (d *Dao) IsAdministrator(ctx context.Context, req *livepb.IsAdministratorReq) (*livepb.IsAdministratorRsp, error) {
	return d.baseLive.IsAdministrator(ctx, req)
}

func (d *Dao) IsAnchor(ctx context.Context, noId string) (*livepb.IsAnchorRsp, error) {
	return d.baseLive.IsAnchor(ctx, &livepb.IsAnchorReq{NoId: noId})
}

func (d *Dao) BindUserPushRelation(ctx context.Context, req *userpb.BindPushRelationReq) error {
	_, err := d.user.BindPushRelation(ctx, req)
	return err
}

func (d *Dao) SetUserSwitch(ctx context.Context, req *userpb.SetUserSwitchReq) error {
	_, err := d.user.SetUserSwitch(ctx, req)
	return err
}

func (d *Dao) GetUserSwitch(ctx context.Context, req *userpb.GetUserSwitchReq) (*userpb.GetUserSwitchRsp, error) {
	return d.user.GetUserSwitch(ctx, req)
}

func (d *Dao) UnBindPushAlias(ctx context.Context, userId string) error {
	_, err := d.user.UnBindPushRelation(ctx, &userpb.UnBindPushRelationReq{
		NoId: userId,
	})
	return err
}

func (d *Dao) SetUserPushStatus(ctx context.Context, noId, status string) error {
	_, err := d.user.SetPushStatus(ctx, &userpb.SetPushStatusReq{
		NoId:   noId,
		Status: status,
	})
	return err
}

func (d *Dao) GetAndCheckMobile(ctx context.Context, noId, mobile string, checkType userpb.CheckType) (*userpb.GetAndCheckMobileRsp, error) {
	return d.user.GetAndCheckMobile(ctx, &userpb.GetAndCheckMobileReq{
		NoId:     noId,
		ToMobile: mobile,
		Type:     checkType,
	})
}

func (d *Dao) ApplyModifyMobile(ctx context.Context, param *model.ApplyModifyMobileReq) error {
	var modifyType userpb.ModifyType
	switch param.ModifyType {
	case userpb.ModifyType_UserSelf.String():
		modifyType = userpb.ModifyType_UserSelf
	case userpb.ModifyType_AdminHelp.String():
		modifyType = userpb.ModifyType_AdminHelp
	default:
		return model.ErrorArgument
	}

	_, err := d.user.ApplyModifyMobile(ctx, &userpb.ApplyModifyMobileReq{
		NoId:        param.NoId,
		FromMobile:  param.FromMobile,
		ToMobile:    param.ToMobile,
		ModifyType:  modifyType,
		ApplyReason: param.ApplyReason,
		ProveImg:    param.ProveImg,
	})
	return err
}

func (d *Dao) GetModifyMobileInfo(ctx context.Context, noId string) (*userpb.GetModifyMobileInfoRsp, error) {
	return d.user.GetModifyMobileInfo(ctx, &userpb.GetModifyMobileInfoReq{
		NoId: noId,
	})
}

func (d *Dao) SendSecretary(ctx context.Context, req *chatpb.SendSecretaryReq) (rsp *chatpb.SendSecretaryRsp, err error) {
	return d.baseChat.SendSecretary(ctx, req)
}

func (d *Dao) GetFansClubOptions(ctx context.Context, clubId string) (*livepb.GetFansClubOptionsRsp, error) {
	return d.baseLive.GetFansClubOptions(ctx, &livepb.GetFansClubOptionsReq{
		ClubId: clubId,
	})
}
