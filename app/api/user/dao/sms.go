package dao

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"creativematrix.com/beyondreading/app/api/user/model"
)

const (
	TimeFormat = "2006-01-02"
)

func (d *Dao) SendCodeCount(ctx context.Context, mobile, userId string) (int64, error) {
	var count int64 = 0

	date := time.Now().Format(TimeFormat)

	if userId == "" {
		count = d.schema[model.TableSmsCode].Count(bson.M{"mobile": mobile, "date": date})
	} else {
		uO, err := primitive.ObjectIDFromHex(userId)
		if err != nil {
			return count, err
		}
		count = d.schema[model.TableSmsCode].Count(bson.M{"mobile": mobile, "date": date, "userId": uO})
	}
	return count, nil
}

func (d *Dao) InsertCode(ctx context.Context, sc *model.TSmsCode) error {
	_, err := d.cache.Set(ctx, fmt.Sprintf(model.RedisUserSmsCode, sc.Mobile), sc.Code, 600)
	if err != nil {
		return err
	}

	sc.Date = time.Now().Format(TimeFormat)
	_, err = d.schema[model.TableSmsCode].Insert(sc)
	if err != nil {
		return err
	}

	return nil
}

func (d *Dao) SetSmsLimit(ctx context.Context, typ, mobile string) error {
	_, err := d.cache.Set(ctx, fmt.Sprintf(model.RedisUserSmsLimit, typ, mobile), mobile, 60)
	return err
}

func (d *Dao) IsSmsLimit(ctx context.Context, typ, mobile string) bool {
	st, _ := d.cache.GetString(ctx, fmt.Sprintf(model.RedisUserSmsLimit, typ, mobile))
	return len(st) > 0
}

func (d *Dao) GetSmsCode(ctx context.Context, mobile string) (string, error) {
	return d.cache.GetString(ctx, fmt.Sprintf(model.RedisUserSmsCode, mobile))
}

func (d *Dao) CheckCode(ctx context.Context, mobile, code, userId string) (bool, error) {
	if mobile == d.conf.AdminPhone {
		return true, nil
	}
	var sc model.TSmsCode

	if userId == "" {
		sc, e := d.GetSmsCode(ctx, mobile)
		if e != nil {
			return false, nil
		} else {
			return sc == code, nil
		}
	} else {
		_id, err := primitive.ObjectIDFromHex(userId)
		if err != nil {
			return false, err
		}

		if err := d.schema[model.TableSmsCode].FindOne(&sc, bson.M{"mobile": mobile, "code": code, "userId": _id}); err != nil {
			return false, err
		}
		// 比对一下时间是否在5分钟内
		return validIdWithTime(sc.Id, time.Minute*10), nil
	}
}

func validIdWithTime(id primitive.ObjectID, duration time.Duration) bool {
	return id.Timestamp().Add(model.TimeAdd + duration).After(time.Now())
}

func (d *Dao) SendCodeWhite(ctx context.Context, mobile string) (int64, error) {
	count := d.schema[model.TableSmsCodeWhite].Count(bson.M{"mobile": mobile})

	return count, nil
}
