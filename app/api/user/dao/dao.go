package dao

import (
	"context"

	"creativematrix.com/beyondreading/app/api/user/model"
	accountpb "creativematrix.com/beyondreading/app/base/account/api"
	activity "creativematrix.com/beyondreading/app/base/activity/api"
	chatpb "creativematrix.com/beyondreading/app/base/chat/api"
	livepb "creativematrix.com/beyondreading/app/base/live/api"
	userpb "creativematrix.com/beyondreading/app/base/user/api"
	"creativematrix.com/beyondreading/pkg/check/shumei"
	fc "creativematrix.com/beyondreading/pkg/face"
	"creativematrix.com/beyondreading/pkg/minio"
	"creativematrix.com/beyondreading/pkg/mysql"
	"creativematrix.com/beyondreading/pkg/wangsu"

	"creativematrix.com/beyondreading/app/api/user/conf"
	"creativematrix.com/beyondreading/pkg/elastic"
	"creativematrix.com/beyondreading/pkg/mongo"
	"creativematrix.com/beyondreading/pkg/mq"
	"creativematrix.com/beyondreading/pkg/rabbitmq"
	"creativematrix.com/beyondreading/pkg/redis"
)

type Dao struct {
	msshard         mysql.Mysqler
	cache           redis.Redis
	liveCache       redis.Redis
	schema          map[string]*mongo.Model
	userTokenSecret string
	es              *elastic.Elastic
	wangsu          *wangsu.Wangsu
	user            userpb.UserClient
	baseChat        chatpb.ChatClient
	account         accountpb.AccountClient
	SummberBroker   *rabbitmq.Broker
	SummberProducer *mq.Producer
	thirdBroker     *rabbitmq.Broker
	conf            *conf.Config
	face            fc.IFace
	summerConn      *mongo.Connection
	summerConnChat  *mongo.Connection
	sm              shumei.ISM //数美
	minio           *minio.Minio

	baseLive livepb.LiveClient
	activity activity.ActivityClient
}

var summerTable = []string{
	model.TableUser,
	model.TableRealName,
	model.TableToken,
	model.TableNickName,
	model.TableUserAction,
	model.TableAudioHint,
	model.TableUserFaceVerify,
	model.TableFeedBack,
	model.TableFeedbackType,
	model.TableLogoff,
	model.TableUserSessionList,
	model.TableOfSessionList,
	model.TableDefaultAvatar,
	model.TUserInviteVerify,
	model.TableReviewRule,
	model.TableReviewFace,
	model.TableSmsCode,
	model.TableSmsCodeWhite,
	model.TableGuildScout,
	model.TablePositionManager,
	model.TableDecorateStatus,
	model.TableUserDeviceLog,
	model.TableGiftBackPack,
	model.TableGuildAnchor,
	model.TableSwitch,
	model.TableInterestTagLib,
	model.TableInterestTagAdded,
	model.TableUserMatched,
	model.TableDecorateConf,
}

func (d *Dao) Ping(ctx context.Context) (err error) {
	return
}

func (d *Dao) Close() {
	d.es.Close()
}

func Load(c *conf.Config) *Dao {

	summerConn := mongo.Connect(c.MongodbUser)

	summerConnChat := mongo.Connect(c.MongodbChat)

	schema := map[string]*mongo.Model{}

	for _, name := range summerTable {
		schema[name] = summerConn.Model(name)
	}
	baseChat, err := chatpb.NewClient(c.Base)
	if err != nil {
		panic(err)
	}

	user, err := userpb.NewClient(c.Base)
	if err != nil {
		panic(err)
	}

	account, err := accountpb.NewClient(c.Base)
	if err != nil {
		panic(err)
	}

	es, err := elastic.NewEsPool(c.ElasticSearch)
	if err != nil {
		panic(err)
	}

	minIO, err := minio.NewMinio(c.Minio)
	if err != nil {
		panic(err)
	}

	baseLive, err := livepb.NewClient(c.Base)
	if err != nil {
		panic(err)
	}

	act, err := activity.NewClient(c.Base)
	if err != nil {
		panic(err)
	}

	return &Dao{
		msshard:       mysql.New(c.Mysql),
		es:            es,
		schema:        schema,
		thirdBroker:   rabbitmq.NewBroker(c.RabbitMQ.URL, c.RabbitMQ.ThirdEx),
		SummberBroker: rabbitmq.NewBroker(c.RabbitMQ.URL, c.RabbitMQ.SummerEx),
		SummberProducer: mq.NewProducer(&mq.ProducerOptions{
			BrokerURL: c.RabbitMQ.URL,
			ExchangeOpt: &mq.ExchangeOption{
				Type: c.RabbitMQ.SummerEx.Type,
				Name: c.RabbitMQ.SummerEx.Name,
			},
		}),
		baseChat:   baseChat,
		account:    account,
		activity:   act,
		cache:      redis.Load(c.RedisUser),
		liveCache:  redis.Load(c.RedisLive),
		wangsu:     wangsu.NewWangsu(c.Wangsu),
		user:       user,
		summerConn: summerConn,
		face:       fc.FaceClient(fc.FaceID, c.Face),
		sm:         shumei.SM(c.SM),
		conf:       c,

		userTokenSecret: c.UserTokenSecret,
		minio:           minIO,
		baseLive:        baseLive,
		summerConnChat:  summerConnChat,
	}
}

func (d *Dao) GetUserIdByToken(ctx context.Context, token string) (*userpb.TokenRes, error) {
	res, err := d.user.TokenToID(ctx, &userpb.TokenReq{Token: token})
	if err != nil {
		return nil, err
	}
	return res, nil
}

func (d *Dao) NewMongoSession(ctx context.Context) (*mongo.Session, error) {
	return d.summerConn.NewSession(ctx)
}

func (d *Dao) NewChatMongoSession(ctx context.Context) (*mongo.Session, error) {
	return d.summerConnChat.NewSession(ctx)
}
