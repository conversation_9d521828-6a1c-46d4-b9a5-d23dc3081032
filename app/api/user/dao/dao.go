package dao

import (
	"context"

	"creativematrix.com/beyondreading/app/api/user/conf"
	userapi "creativematrix.com/beyondreading/app/base/user/api"
	userpb "creativematrix.com/beyondreading/proto/user"
)

type Dao struct {
	UserClient userpb.UserServiceClient
}

func Load(c *conf.Config) *Dao {

	uClient, err := userapi.NewClient(c.Base)
	if err != nil {
		panic(err)
	}

	return &Dao{
		UserClient: uClient,
	}
}

func (d *Dao) Ping(ctx context.Context) error {
	return nil
}

func (d *Dao) Close() {
	// 关闭连接
}
