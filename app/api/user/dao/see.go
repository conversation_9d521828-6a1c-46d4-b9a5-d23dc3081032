package dao

import (
	"context"
	"creativematrix.com/beyondreading/pkg/typeconvert"
	"fmt"
	"github.com/gomodule/redigo/redis"
	"time"

	"creativematrix.com/beyondreading/app/api/user/model"
	pc "creativematrix.com/beyondreading/app/base/chat/api"
	pb "creativematrix.com/beyondreading/app/base/user/api"
)

// 新增官方助手会话列表
func (d *Dao) OfSessionListInsert(ctx context.Context, row *model.TOfSessionList) error {
	_, err := d.summerConnChat.Model(model.TableOfSessionList).Insert(row)
	return err
}

func (d *Dao) GetUsersByIds(ctx context.Context, req *pb.UserIdsReq) (*pb.UserInfoRsp, error) {
	return d.user.GetUsersByIds(ctx, req)
}

func (d *Dao) HeartJumpGet(ctx context.Context, from, to string) (*pc.HeartJumpRsp, error) {
	return d.baseChat.HeartJumpList(ctx, &pc.HeartJumpReq{
		From:  from,
		Users: []string{to},
	})
}

func (d *Dao) SeeYouCacheSet(ctx context.Context, from, to string) (int64, error) {
	return d.cache.SetLock(ctx, fmt.Sprintf(model.Redis_SeeYouCache,
		fmt.Sprintf("%s_%s", from, to)), 1, int64(time.Hour.Seconds()*24*30))
}

func (d *Dao) SendMessage(ctx context.Context, req *pc.SendMessageReq) (*pc.SendMessageRes, error) {
	return d.baseChat.SendMessage(ctx, req)
}

func (d *Dao) UserLoveRings(ctx context.Context, from, to, tips string) error {
	_, err := d.baseChat.UserLoveRings(ctx, &pc.UserLoveRingsReq{
		From: from,
		To:   to,
		Tips: tips,
	})
	return err
}

// UserVisitorsUpdate 新增访客记录
func (d *Dao) UserVisitorsUpdate(ctx context.Context, from, to string) error {
	_, err := d.baseChat.UserVisitorsUpdate(ctx, &pc.UserVisitorsUpdateReq{
		From: from,
		To:   to,
	})
	return err
}

// IncrUserVisitors 新增访客次数
func (d *Dao) IncrUserVisitors(ctx context.Context, to string) error {
	_, err := d.cache.HIncrByInt(ctx, model.RedisUserVisitors, to, 1, -1)
	return err
}

// GetUserVisitors 新增访客次数
func (d *Dao) GetUserVisitors(ctx context.Context, to string) (int64, error) {
	rsp, err := d.cache.HGet(ctx, model.RedisUserVisitors, to)
	if err != nil && err != redis.ErrNil {
		return 0, err
	}
	visitors := typeconvert.StringToInt64(rsp)
	if visitors > 99 {
		return 99, nil
	}
	return visitors, nil
}
