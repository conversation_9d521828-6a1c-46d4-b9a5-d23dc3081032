package dao

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"creativematrix.com/beyondreading/app/api/user/model"
	"creativematrix.com/beyondreading/pkg/crypto"
	"creativematrix.com/beyondreading/pkg/ecode"
	"creativematrix.com/beyondreading/pkg/mongo"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const tokenTtl = 86400 * 90

func (d *Dao) CreateToken(ctx context.Context, userId, noId, msgId, gender string) (string, error) {
	uid, err := d.genEncryptToken(userId, noId, msgId, gender)
	if err != nil {
		return "", err
	}

	id, _ := primitive.ObjectIDFromHex(userId)

	token := &model.TToken{}
	token.UserId = id
	token.Token = uid
	token.Status = "enable"
	token.Created = time.Now()

	_, err = d.schema[model.TableToken].Insert(token)
	if err != nil {
		return "", err
	}

	return uid, nil
}

// 生成对称加密token
func (d *Dao) genEncryptToken(userId, noId, msgId, gender string) (string, error) {
	joinStr := userId + "_" + noId + "_" + msgId + "_" + gender
	return crypto.Encrypt([]byte(joinStr), []byte(d.userTokenSecret))
}

func (d *Dao) GenEncryptBindToken(srcUserId, dstUserId string) (string, error) {
	expire := strconv.FormatInt(time.Now().Add(time.Minute*model.BindTokenExpireMin).Unix(), 10)
	joinStr := srcUserId + "_" + dstUserId + "_" + expire
	return crypto.Encrypt([]byte(joinStr), []byte(d.userTokenSecret))
}

func (d *Dao) ParseEncryptBindToken(bindToken string) (srcUserId, dstUserId string, expire int64, err error) {
	str, err := crypto.Decrypt(bindToken, []byte(d.userTokenSecret))
	if err != nil {
		return "", "", 0, err
	}
	arr := strings.Split(str, "_")
	if len(arr) != 3 {
		return "", "", 0, ecode.RequestErr
	}
	expireTime, err := strconv.ParseInt(arr[2], 10, 64)
	if err != nil {
		return "", "", 0, err
	}
	return arr[0], arr[1], expireTime, nil
}

func (d *Dao) CacheToken(ctx context.Context, userId string, token string) (interface{}, error) {
	return d.cache.Set(ctx, fmt.Sprintf(model.Redis_UserTokenCache, token), userId, tokenTtl)
}

func (d *Dao) InvalidTokens(ctx context.Context, userId string) (string, error) {
	id, _ := primitive.ObjectIDFromHex(userId)
	_, err := d.schema[model.TableToken].Update(bson.M{"userId": id}, bson.M{"$set": bson.M{"status": "disabled"}}, mongo.Multi())
	if err != nil {
		return "", err
	}

	return "", err
}

func (d *Dao) RemoveCacheToken(ctx context.Context, token string) error {
	return d.cache.DelKey(ctx, fmt.Sprintf(model.Redis_UserTokenCache, token))
}

func (d *Dao) LastTokenStatus(ctx context.Context, userId string) error {
	token := new(model.TToken)
	id, _ := primitive.ObjectIDFromHex(userId)
	err := d.schema[model.TableToken].FindOne(token, bson.M{"userId": id, "status": "enable"})
	if err != nil {
		if err != ecode.NothingFound {
			return err
		} else {
			return nil
		}
	}

	_, err = d.cache.Set(ctx, fmt.Sprintf(model.Redis_UserTokenCache, token.Token), -1, tokenTtl)

	return err
}

func (d *Dao) GetLastToken(ctx context.Context, userId string) (string, error) {
	token := new(model.TToken)
	id, _ := primitive.ObjectIDFromHex(userId)
	err := d.schema[model.TableToken].FindOne(token, bson.M{"userId": id, "status": "enable"})
	if err != nil {
		return "", err
	}

	return token.Token, nil
}
