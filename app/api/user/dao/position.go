package dao

import (
	"context"
	"creativematrix.com/beyondreading/app/api/user/model"
	"encoding/json"
	"github.com/gomodule/redigo/redis"
	"go.mongodb.org/mongo-driver/bson"
)

func (d *Dao) PositionList(ctx context.Context) ([]*model.PositionManager, error) {
	data := make([]*model.PositionManager, 0)

	positionFunc := func() (string, error) {
		if err := d.schema[model.TablePositionManager].Find(&data, bson.M{}); err != nil {
			return "", err
		}

		bts, err := json.Marshal(&data)
		if err != nil {
			return "", err
		}
		return string(bts), nil
	}

	str, err := d.cache.FuncString(ctx, model.PositionManagerRedis, positionFunc, model.PositionManagerRedisTTL)
	if err != nil {
		return nil, err
	}
	if err := json.Unmarshal([]byte(str), &data); err != nil {
		return nil, err
	}

	return data, nil
}

func (d *Dao) CheckUserInWhitelist(ctx context.Context, noId string) (bool, error) {
	isExist, err := d.cache.SisMember(ctx, model.PositionWhitelistRedis, noId)
	if err != nil {
		if err == redis.ErrNil {
			return false, nil
		}
		return false, err
	}

	return isExist, nil
}
