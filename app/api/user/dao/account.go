//Description rpc调用account
//Date        2021/7/31
//User        cl

package dao

import (
	"context"
	"fmt"
	"github.com/gomodule/redigo/redis"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"creativematrix.com/beyondreading/app/api/user/model"
	accountpb "creativematrix.com/beyondreading/app/base/account/api"
	"creativematrix.com/beyondreading/pkg/ecode"
)

func (d *Dao) GetWallet(c context.Context, userId string) (*accountpb.Accounts, error) {
	account, err := d.account.GetAccountByID(c, &accountpb.GetAccountByIDReq{UserId: userId})

	if err != nil {
		return nil, err
	}

	if account.Account == nil {
		return nil, ecode.NothingFound
	}

	return account.Account, nil
}

func (d *Dao) GetGiftWallUserBadge(ctx context.Context, noId string) (*accountpb.GetGiftWallUserBadgeRsp, error) {
	req := &accountpb.GetGiftWallUserBadgeReq{
		NoId: noId,
	}

	return d.account.GetGiftWallUserBadge(ctx, req)
}

func (d *Dao) GetAtlasRank(ctx context.Context, noId string) int64 {
	year, week := time.Now().ISOWeek()
	p := d.cache.Pipeline(ctx)
	p.Send("ZREVRANK", fmt.Sprintf(model.RedisGiftAtlasScore, year, week), noId)
	p.Send("ZSCORE", fmt.Sprintf(model.RedisGiftAtlasScore, year, week), noId)
	rev, err := p.Receive()
	if err != nil {
		return 0
	}

	rank, _ := redis.Int64(rev[0], nil)
	score, _ := redis.Int64(rev[1], nil)
	rank++
	if score < 1 {
		rank = 0
	}
	return rank
}

func (d *Dao) GetGiftAnchorWall(ctx context.Context, noId string) (*model.AnchorWallInfo, error) {
	req := &accountpb.GetGiftAnchorWallReq{
		AnchorId: noId,
	}
	wallRsp, err := d.account.GetGiftAnchorWall(ctx, req)
	if err != nil {
		return nil, err
	}

	rsp := &model.AnchorWallInfo{
		LightNum: wallRsp.LightNum,
		Num:      wallRsp.Num,
		List:     make([]*model.AnchorWallVO, 0),
	}
	for _, v := range wallRsp.List {
		vo := &model.AnchorWallVO{
			GiftId:   v.GiftId,
			GiftName: v.GiftName,
			CoverUrl: v.CoverUrl,
			Diamonds: v.Diamonds,
			IsLight:  v.IsLight,
		}
		rsp.List = append(rsp.List, vo)
	}

	return rsp, nil
}

func (d *Dao) CheckUserAccount(ctx context.Context, userId string) (bool, error) {
	wallet, err := d.GetWallet(ctx, userId)
	if err != nil {
		return false, err
	}
	if wallet.Diamonds > 10000 || wallet.Cash > 1000000 || wallet.StarVotes > 10000 {
		return false, nil
	}

	id, _ := primitive.ObjectIDFromHex(userId)
	cur, _, err := d.schema[model.TableGiftBackPack].Aggregate(bson.A{
		bson.M{"$match": bson.M{
			"accountId": id,
			"num":       bson.M{"$gt": 0},
			"expireAt":  bson.M{"$gt": time.Now()},
		}},
		bson.M{"$project": bson.M{"accountId": 1, "giftId": 1, "num": 1}},
		bson.M{"$lookup": bson.M{
			"from":         "gift_info",
			"localField":   "giftId",
			"foreignField": "_id",
			"as":           "gift",
		}},
		bson.M{"$match": bson.M{"gift": bson.M{"$ne": bson.A{}}}},
		bson.M{"$unwind": "$gift"},
		bson.M{"$replaceRoot": bson.M{"newRoot": bson.M{"num": "$num", "diamonds": "$gift.diamonds"}}},
	})
	//{"$group": {"_id": "", "total": {"$sum" : {"$multiply": ["$num", "$diamonds"] }}}}
	if err != nil {
		return false, err
	}
	data := make([]*model.PackGift, 0)
	err = cur.All(ctx, &data)
	if err != nil {
		return false, err
	}
	var total int64
	for _, g := range data {
		total += g.Num * g.Diamonds
	}
	if total > 10000 {
		return false, nil
	}

	return true, nil
}
