//Description @todo
//Date        2022/6/21
//User        bobeiwangluo

package apple

import (
	"context"
	"crypto/x509"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"strings"
	"testing"

	"github.com/BurntSushi/toml"

	"github.com/dgrijalva/jwt-go"

	"creativematrix.com/beyondreading/app/api/user/conf"
)

func Test_JWT(t *testing.T) {
	cliToken := "eyJraWQiOiJmaDZCczhDIiwiYWxnIjoiUlMyNTYifQ.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.g_FJEckgYeRQSJTehRXVy9bACqBr8g3ow8AgRNRAA0iEtdwofmF0flGUnnPH2-Hvv7b1xO3T2zXFrecQzMG2k7j1Wdu6h-2lL4dlSDnriykM5X6aLpdqfH2koNG6BwgnlQ0kR1N8lHcNfUuO55NrRtGLD8wfuf-ey0hsNzbMflTbWq5FH_2tR1Z8VyKj-of2LvPGsFeO6_ffgEaT7qW3H-ChpC6TzAuMHO4XxLahZ_Vrz8CJQcRK47rWwsGktVUZDn4jeg3jSYcDxzz4iDXdqkG1j2AmJ4u1-oMAF5kmVfzGQjiPcR0pHXmos7wmpSAH52m1aQi5zskaoWgc7ovloA"
	cliTokenArr := strings.Split(cliToken, ".")
	if len(cliTokenArr) < 3 {
		fmt.Println("cliToken Split err")
		return
	}
	// 解析cliToken的header获取kid
	cliHeader, err := jwt.DecodeSegment(cliTokenArr[0])
	if err != nil {
		fmt.Println(err.Error())
		return
	}
	var jHeader JwtHeader
	_ = json.Unmarshal(cliHeader, &jHeader)

	// 效验pubKey 及 token
	token, err := jwt.ParseWithClaims(cliToken, &JwtClaims{}, func(token *jwt.Token) (interface{}, error) {
		return GetRSAPublicKey(context.Background(), jHeader.Kid), nil
	})
	if err != nil {
		fmt.Println(err.Error())
		return
	}
	fmt.Println(token.Claims.(*JwtClaims).ExpiresAt)

	payload := jwt.Token{
		Method: jwt.SigningMethodES256,
		Header: map[string]interface{}{
			"alg": jHeader.Alg,
			"typ": "JWT",
			"kid": jHeader.Kid,
		},
		Claims: token.Claims,
	}

	config, err := LoadConf()
	if err != nil {
		t.Fatal(err.Error())
	}
	block, _ := pem.Decode([]byte(config.IosSecret))
	//3.x509解析
	rsa, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		t.Fatal(err.Error())
	}
	fmt.Println("rsa ok:", rsa)

	// Sign and get the complete encoded token as a string using the secret
	clientSecret, err := payload.SignedString(rsa)
	if err != nil {
		t.Fatal(err.Error())
	}
	fmt.Println("client secret:", clientSecret)
}

func LoadConf() (*conf.Config, error) {
	cf := &conf.Config{}
	if _, err := toml.DecodeFile("../../../../base.toml", cf); err != nil {
		return nil, err
	}
	if _, err := toml.DecodeFile("../../cmd/api-user.toml", cf); err != nil {
		return nil, err
	}
	return cf, nil
}
