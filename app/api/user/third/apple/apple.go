package apple

import (
	"context"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"errors"
	"math/big"
	"strings"

	"github.com/go-resty/resty/v2"

	"github.com/dgrijalva/jwt-go"
)

const (
	PUBLIC_KEY_REQ_URL    = "https://appleid.apple.com/auth/keys"
	REVOKE_TOKEN_URL      = "https://appleid.apple.com/auth/revoke"
	APPLE_URL             = "https://appleid.apple.com"
	APPLICATION_CLIENT_ID = "com.xykj.klSocial"
)

type JwtClaims struct {
	jwt.StandardClaims
}

type JwtHeader struct {
	Kid string `json:"kid"`
	Alg string `json:"alg"`
}

type JwtKeys struct {
	Kty string `json:"kty"`
	Kid string `json:"kid"`
	Use string `json:"use"`
	Alg string `json:"alg"`
	N   string `json:"n"`
	E   string `json:"e"`
}

// 认证客户端传递过来的token是否有效
func VerifyIdentityToken(ctx context.Context, cliToken string, cliUserID, iosSecret string) (string, error) {
	// 数据由 头部、载荷、签名 三部分组成
	cliTokenArr := strings.Split(cliToken, ".")
	if len(cliTokenArr) < 3 {
		return "", errors.New("cliToken Split err")
	}

	// 解析cliToken的header获取kid
	cliHeader, err := jwt.DecodeSegment(cliTokenArr[0])
	if err != nil {
		return "", err
	}

	var jHeader JwtHeader
	err = json.Unmarshal(cliHeader, &jHeader)
	if err != nil {
		return "", err
	}

	// 效验pubKey 及 token
	token, err := jwt.ParseWithClaims(cliToken, &JwtClaims{}, func(token *jwt.Token) (interface{}, error) {
		return GetRSAPublicKey(ctx, jHeader.Kid), nil
	})

	if err != nil {
		return "", err
	}

	// 信息验证
	if claims, ok := token.Claims.(*JwtClaims); ok && token.Valid {
		if claims.Issuer != APPLE_URL || claims.Audience != APPLICATION_CLIENT_ID || claims.Subject != cliUserID {
			return "", errors.New("verify token info fail, info is not match")
		}
		// here is verify ok !
	} else {
		return "", errors.New("token claims parse fail")
	}

	return genCliSecret(iosSecret, jHeader, token), nil
}

func genCliSecret(iosSecret string, jHeader JwtHeader, token *jwt.Token) string {
	payload := jwt.Token{
		Method: jwt.SigningMethodES256,
		Header: map[string]interface{}{
			"alg": jHeader.Alg,
			"typ": "JWT",
			"kid": jHeader.Kid,
		},
		Claims: token.Claims,
	}
	block, _ := pem.Decode([]byte(iosSecret))
	iosRSA, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return ""
	}
	clientSecret, _ := payload.SignedString(iosRSA)
	return clientSecret
}

// 向苹果服务器获取解密signature所需要用的publicKey
func GetRSAPublicKey(ctx context.Context, kid string) *rsa.PublicKey {
	var jKeys map[string][]JwtKeys
	if err := doRequest(ctx, PUBLIC_KEY_REQ_URL, &jKeys); err != nil {
		return nil
	}

	// 获取验证所需的公钥
	var pubKey rsa.PublicKey
	// 通过cliHeader的kid比对获取n和e值 构造公钥
	for _, data := range jKeys {
		for _, val := range data {
			if val.Kid == kid {
				n_bin, _ := base64.RawURLEncoding.DecodeString(val.N)
				n_data := new(big.Int).SetBytes(n_bin)

				e_bin, _ := base64.RawURLEncoding.DecodeString(val.E)
				e_data := new(big.Int).SetBytes(e_bin)

				pubKey.N = n_data
				pubKey.E = int(e_data.Uint64())
				break
			}
		}
	}

	if pubKey.E <= 0 {
		return nil
	}

	return &pubKey
}

func doRequest(ctx context.Context, url string, result *map[string][]JwtKeys) error {
	client := resty.New()
	_, err := client.R().SetResult(result).SetError(result).ForceContentType("application/json").Get(url)
	if err != nil {
		return err
	}

	return nil
}
