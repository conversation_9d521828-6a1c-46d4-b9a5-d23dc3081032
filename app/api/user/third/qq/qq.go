package qq

import (
	"context"
	"errors"
	"strings"

	"github.com/go-resty/resty/v2"

	"creativematrix.com/beyondreading/app/api/user/model"
)

const (
	//appId = "101788450"
	//appKey  = "cda9o3l62HUVaH9I"
	baseUrl = "https://openmobile.qq.com/user/get_simple_userinfo"
	//oAuth   = "https://graph.qq.com/oauth2.0/me"
)

type SimpleUserInfoQQ struct {
	Ret       int    `json:"ret"`
	Msg       string `json:"msg"`
	FigureUrl string `json:"figureurl"`
}

func GetSimpleUserInfo(ctx context.Context, kr *model.LoginReq, appId string) error {
	client := resty.New()
	res := new(SimpleUserInfoQQ)
	_, err := client.R().SetQueryParams(map[string]string{
		"access_token":       kr.<PERSON>,
		"openid":             kr.<PERSON>d,
		"oauth_consumer_key": appId,
	}).SetResult(res).SetError(res).ForceContentType("application/json").Get(baseUrl)

	if err != nil {
		return err
	}

	if res.Ret < 0 {
		return errors.New(res.Msg)
	}

	if !strings.Contains(res.FigureUrl, kr.OpenId) {
		return model.UserQQLoginError
	}

	return nil
}
