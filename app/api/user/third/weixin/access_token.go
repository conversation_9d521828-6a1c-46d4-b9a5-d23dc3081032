//Description 微信公众号token
//Date        2021/10/21
//User        cl

package weixin

import (
	"net/url"
	"sync"
	"time"

	"github.com/go-resty/resty/v2"

	"creativematrix.com/beyondreading/pkg/logger"
)

type PublicWX struct {
	mutex       sync.Mutex
	AppId       string
	Secret      string
	AccessToken string
	JsApiTicket string
}

type AccessToken struct {
	ErrCode     int    `json:"errcode"`
	ErrMsg      string `json:"errmsg"`
	AccessToken string `json:"access_token"`
	Ticket      string `json:"ticket"`
}

func NewWXPublic(appId, secret string) *PublicWX {
	return &PublicWX{
		AppId:  appId,
		Secret: secret,
	}
}

func (wx *PublicWX) Init() {
	wx.refreshToken()
	go func() {
		ticker := time.NewTicker(time.Minute * 30)
		for t := range ticker.C {
			wx.refreshToken()
			logger.LogInfoF("%s: accessToken=%s , signTicket=%s", t.String(), wx.AccessToken, wx.JsApiTicket)
		}
	}()
}

func (wx *PublicWX) refreshToken() {
	wx.mutex.Lock()
	defer wx.mutex.Unlock()

	wx.getAccessToken()
	wx.getJsTicket()
}

func (wx *PublicWX) getAccessToken() {
	reqUrl := baseUrl + "cgi-bin/token"
	param := make(url.Values)
	param.Add("appid", wx.AppId)
	param.Add("secret", wx.Secret)
	param.Add("grant_type", "client_credential")

	rsp := new(AccessToken)
	_, err := resty.New().R().SetQueryParamsFromValues(param).SetResult(rsp).SetError(rsp).Get(reqUrl)
	if err != nil {
		logger.LogErrorw("wx getAccessToken:", err.Error())
		return
	}

	if rsp.ErrCode != 0 {
		logger.LogErrorw("wx getAccessToken:", rsp.ErrMsg)
		return
	}

	wx.AccessToken = rsp.AccessToken
}

func (wx *PublicWX) getJsTicket() {
	reqUrl := baseUrl + "cgi-bin/ticket/getticket"
	param := make(url.Values)
	param.Add("access_token", wx.AccessToken)
	param.Add("type", "jsapi")

	rsp := new(AccessToken)
	_, err := resty.New().R().SetQueryParamsFromValues(param).SetResult(rsp).SetError(rsp).
		ForceContentType("application/json").Get(reqUrl)
	if err != nil {
		logger.LogErrorw("wx getJsTicket:", err.Error())
		return
	}

	if rsp.ErrCode != 0 {
		logger.LogErrorw("wx getJsTicket:", rsp.ErrMsg)
		return
	}

	wx.JsApiTicket = rsp.Ticket
}
