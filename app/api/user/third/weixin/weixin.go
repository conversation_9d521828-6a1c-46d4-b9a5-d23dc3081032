package weixin

import (
	"context"
	"errors"

	"github.com/go-resty/resty/v2"

	"creativematrix.com/beyondreading/app/api/user/model"
)

const (
	baseUrl = "https://api.weixin.qq.com/"
)

type WeChatRsp struct {
	ErrCode int    `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
	UnionId string `json:"unionid"`
}

type WeChatOauthAccessTokenRsp struct {
	ErrCode      int    `json:"errcode"`
	ErrMsg       string `json:"errmsg"`
	AccessToken  string `json:"access_token"`
	OpenId       string `json:"openid"`
	RefreshToken string `json:"refresh_token"`
}

func GetSnsUserInfo(ctx context.Context, kr *model.LoginReq, result *WeChatRsp) error {
	_, err := resty.New().R().SetQueryParams(map[string]string{
		"access_token": kr.<PERSON>,
		"openid":       kr.<PERSON>Id,
	}).SetResult(result).SetError(result).ForceContentType("application/json").Get(baseUrl + "sns/userinfo")

	if err != nil {
		return err
	}

	if result.UnionId == "" {
		return errors.New(result.ErrMsg)
	}

	return nil
}

func GetSnsUserInfoOauthAccessToken(ctx context.Context, kr *model.WeixinReq, result *WeChatOauthAccessTokenRsp) error {
	_, err := resty.New().R().SetQueryParams(map[string]string{
		"appid":      kr.AppId,
		"secret":     kr.Secret,
		"code":       kr.Code,
		"grant_type": "authorization_code",
	}).SetResult(result).SetError(result).ForceContentType("application/json").Get(baseUrl + "sns/oauth2/access_token")

	if err != nil {
		return err
	}

	if result.OpenId == "" {
		return errors.New(result.ErrMsg)
	}

	return nil
}
