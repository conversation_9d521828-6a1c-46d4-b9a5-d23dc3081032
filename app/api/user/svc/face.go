//Description faceID人脸认证
//Date        2021/8/2
//User        cl

package svc

import (
	"context"
	"encoding/base64"
	"fmt"
	"io/ioutil"
	"os"
	"time"

	"github.com/go-resty/resty/v2"

	"go.mongodb.org/mongo-driver/bson/primitive"
	"golang.org/x/sync/errgroup"

	"go.mongodb.org/mongo-driver/bson"

	"creativematrix.com/beyondreading/app/api/user/model"
	userpb "creativematrix.com/beyondreading/app/base/user/api"
	"creativematrix.com/beyondreading/pkg/crypto"
	fc "creativematrix.com/beyondreading/pkg/face"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/typeconvert"
	"creativematrix.com/beyondreading/pkg/utils"
)

func (s *Svc) VerifyLimit(ctx context.Context, noId string, param *model.ReqFace) error {
	count, err := s.dao.GetVerifyNum(ctx, noId, param.ComparisonType)
	if err != nil {
		return err
	}

	if count >= model.VerifyCountMax {
		return model.FaceVerifyMuch
	}

	user, err := s.dao.GetUserInfoById(ctx, noId, []string{"avatarStatus", "realStatus", "realNameStatus"})
	if err != nil {
		return err
	}

	if param.ComparisonType == model.CompareFace {
		if user.AvatarStatus != model.Success {
			return model.NeedNormalAvatar
		}
		if user.RealStatus == model.Success {
			return model.RealAvatarAlready
		} else if user.RealStatus == model.Process {
			return model.RealInProgress
		}
	} else {
		//实名状态校验
		if user.RealNameStatus == model.Success {
			return model.RealNameAlready
		} else if user.RealNameStatus == model.Process {
			return model.RealNameInProgress
		}
		err = s.dao.UpdateIdCardInfo(ctx, noId, param.Name, param.IdCard)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *Svc) VerifyAvatar(ctx context.Context, baseImgUrl, noId, imgUrl string) (string, error) {
	//获取头像， 真人认证best_img
	realImg, err := s.dao.GetRealAvatar(ctx, noId)
	if err != nil {
		return "", err
	}
	realImgPath := fmt.Sprintf("face-real-%s", noId)
	imgPath := fmt.Sprintf("face-real1-%s", noId)
	defer func() {
		_ = os.Remove(realImgPath)
		_ = os.Remove(imgPath)
	}()

	var eg errgroup.Group
	eg.Go(func() error {
		_, e := resty.New().R().SetOutput(realImgPath).Get(baseImgUrl + realImg)
		return e
	})
	eg.Go(func() error {
		_, e := resty.New().R().SetOutput(imgPath).Get(baseImgUrl + imgUrl)
		return e
	})

	err = eg.Wait()
	if err != nil {
		return "", err
	}

	face, err := s.dao.GetFaceToken(noId, fc.RawImage, model.CompareFace, "", "", imgPath, realImgPath)
	if err != nil {
		return "", err
	}

	rspFace, err := s.dao.FaceIDVerify(model.CompareFace, face.Token, "")
	if err != nil {
		return "", err
	}

	err = s.dao.CacheVerifyNum(ctx, noId, model.CompareFace)
	if err != nil {
		return "", model.ServerErr
	}

	if rspFace.ResultCode != model.FaceIDSuccess && rspFace.Verification.Ref1.Confidence < model.FaceVerifyScore {
		return "", model.FaceNotSame
	}

	return realImg, nil
}

// GetBizToken 获取客户端启动人脸sdk所需参数
func (s *Svc) GetBizToken(ctx context.Context, req *model.ReqFace, noId, fileName string) (string, error) {
	face, err := s.dao.GetFaceToken(noId, fc.MegLive, req.ComparisonType, req.IdCard, req.Name, fileName, "")
	if err != nil {
		return "", err
	}

	//记录次数
	err = s.dao.CacheVerifyNum(ctx, noId, req.ComparisonType)
	if err != nil {
		return "", model.ServerErr
	}

	return face.Token, nil
}

func (s *Svc) CacheGenderByNoId(ctx context.Context, noId, idGender string) error {
	gender := model.Female
	id := typeconvert.StringToInt(idGender)
	if id%2 == 1 {
		gender = model.Male
	}

	return s.dao.CacheUserGender(ctx, noId, gender)
}

// FaceIDVerify 人脸验证
// megData 为客户端提供的视频数据
func (s *Svc) FaceIDVerify(ctx context.Context, noId, userId, compareType, bizToken, megData string) (string, error) {
	result, err := s.dao.FaceIDVerify(compareType, bizToken, megData)
	if err != nil {
		return model.Reject, err
	}

	data := buildFaceVerifyResult(result, noId, compareType)
	user, e := s.dao.GetUserInfoById(ctx, noId, []string{"nickName", "gender", "avatarUrl", "idName", "idCard", "idCardKey"})
	if e != nil {
		return model.Reject, e
	}

	var eg errgroup.Group
	eg.Go(func() error {
		//保存验证结果
		return s.dao.SavaFaceResult(ctx, data)
	})

	eg.Go(func() error {
		//更新user状态
		return s.updateUserFace(ctx, noId, userId, compareType, data.Status, user.Gender)
	})

	status := data.Status
	if data.Status == model.Success {
		if compareType == model.CompareFace {
			status = model.Process
			//真人faceID通过，再发后台人审
			eg.Go(func() error {
				//上传活体 best_image
				realUrl, e := s.uploadRealImg(result, noId)
				if e != nil {
					return e
				}
				reviewFace := buildReviewData(userId, noId, realUrl, user)

				return s.dao.CheckUserMedia(model.MQUserFaceEvent, utils.JsonByte(reviewFace))
			})
		} else {
			if user.IdCard != "" {
				go s.bindAnchorByIdCard(ctx, noId, user.IdCard)
				eg.Go(func() error {
					return s.saveIDCardInfo(ctx, noId, user)
				})
			}
		}
	}

	err = eg.Wait()
	if err != nil {
		return "", model.ServerErr
	}

	if data.Status == model.Reject {
		if compareType == model.CompareFace {
			return "", model.FaceNotSame
		} else {
			return "", model.FaceNotRealName
		}
	}

	return status, err
}

func (s *Svc) saveIDCardInfo(ctx context.Context, noId string, user *userpb.UserInfo) error {
	idCard, _ := crypto.Decrypt(user.IdCard, []byte(s.conf.UserTokenSecret))
	now := time.Now().Unix()
	data := &model.TRealName{
		NoId:       noId,
		NickName:   user.NickName,
		Type:       model.FaceAuto,
		RealName:   user.IdName,
		IdCard:     user.IdCard,
		IdCardKey:  utils.Md5String(idCard),
		ReqTime:    now,
		UpdateTime: now,
		ReviewTime: now,
		Status:     model.Success,
	}

	return s.dao.ReqRealName(ctx, data)
}

// 判断该身份证是否绑定过公会
func (s *Svc) bindAnchorByIdCard(ctx context.Context, noId, idCard string) {
	chooseCode, err := s.dao.GetScoutNum(ctx, idCard)
	if err != nil {
		logger.LogErrorw("bind anchor by idCard", idCard, err.Error())
		return
	}

	s.dao.SendLiveMQ(noId, chooseCode, "rebind")
}

func buildFaceVerifyResult(result *fc.RspVerify, noId, compareType string) *model.TUserFaceVerify {
	record := &model.TUserFaceVerify{
		Id:             primitive.ObjectID{},
		RequestId:      result.RequestId,
		NoId:           noId,
		SdkType:        string(fc.FaceID),
		ComparisonType: compareType,
		Status:         model.Success,
		ResultCode:     result.ResultCode,
		Reason:         result.Error,
	}

	if record.ResultCode != model.FaceIDSuccess && result.Verification.Ref1.Confidence < model.FaceVerifyScore {
		record.Status = model.Reject
	}

	return record
}

func buildReviewData(userId, noId, realUrl string, user *userpb.UserInfo) *model.TReviewFace {
	return &model.TReviewFace{
		UserId:    userId,
		NoId:      noId,
		NickName:  user.NickName,
		Gender:    user.Gender,
		AvatarUrl: user.AvatarUrl,
		RealUrl:   realUrl,
		Status:    model.Process,
		Type:      model.Real,
		ReqTime:   time.Now().Unix(),
	}
}

func (s *Svc) updateUserFace(ctx context.Context, noId, userId, compareType, status, gender string) error {
	data := make(bson.M)
	if compareType == model.CompareFace {
		if status == model.Success {
			data["realStatus"] = model.Process
		} else {
			data["realStatus"] = model.Reject
		}
	} else {
		data["realNameStatus"] = status
		cg, e := s.dao.GetCacheGender(ctx, noId)
		if e != nil {
			return e
		}
		//更新性别
		if cg != gender {
			data["gender"] = cg
			e = s.dao.UpdateEsData(ctx, userId, model.EsUserGender{Gender: cg})
			if e != nil {
				return e
			}
		}
	}

	_, err := s.dao.UpdateUserInfo(ctx, userId, data)
	return err
}

// 上传用户真人认证活体照片
func (s *Svc) uploadRealImg(data *fc.RspVerify, noId string) (string, error) {
	b, err := base64.StdEncoding.DecodeString(data.Images.ImageBest)
	if err != nil {
		return "", err
	}

	fileName := fmt.Sprintf("%d-avatar1-%s.jpeg", time.Now().Unix(), noId)
	defer os.Remove(fileName)
	err = ioutil.WriteFile(fileName, b, 0666)
	if err != nil {
		return "", err
	}

	return s.dao.UploadFaceBest(fileName)
}

func (s *Svc) GetIDCardInfo(ctx context.Context, imagePath, noId string) (*model.RspIDCardInfo, error) {
	cardInfo, err := s.dao.OcrIDCard(imagePath)
	if err != nil {
		return nil, err
	}

	age := utils.GetAgeByDate(fmt.Sprintf("%s-%02s-%02s", cardInfo.BirthYear.Result,
		cardInfo.BirthMonth.Result, cardInfo.BirthDay.Result))

	if cardInfo.Side == 0 {
		if age < 18 {
			return nil, model.AgeTooLow
		} else {
			_, err = s.dao.LocUpload(ctx, imagePath+"-0", imagePath)
		}
	} else {
		_, err = s.dao.LocUpload(ctx, imagePath+"-1", imagePath)
	}

	if err != nil {
		return nil, model.UploadIDCardErr
	}

	rsp := new(model.RspIDCardInfo)
	rsp.IDCard = cardInfo.IDCard.Result
	rsp.Name = cardInfo.Name.Result

	return rsp, nil
}

// RealNameLoc 无法活体检测的实名认证
func (s *Svc) RealNameLoc(ctx context.Context, noId, name, idCard, reqType string, files []string) error {
	err := s.dao.UpdateIdCardInfo(ctx, noId, name, idCard)
	if err != nil {
		return err
	}

	imgUrls := make([]string, 0)
	for _, file := range files {
		imgUrl, _ := s.dao.LocUpload(ctx, file, noId+"/"+file)
		imgUrls = append(imgUrls, imgUrl)
	}

	if len(imgUrls) < 3 {
		return model.RealNameImgErr
	}

	user, err := s.dao.GetUserInfoById(ctx, noId, []string{"nickName"})
	if err != nil {
		return err
	}

	idCardEn, _ := crypto.Encrypt([]byte(idCard), []byte(s.conf.UserTokenSecret))
	now := time.Now().Unix()
	data := &model.TRealName{
		NoId:       noId,
		NickName:   user.NickName,
		Type:       reqType,
		RealName:   name,
		IdCard:     idCardEn,
		IdCardKey:  utils.Md5String(idCard),
		FrontImg:   imgUrls[0],
		BackImg:    imgUrls[1],
		HandleImg:  imgUrls[2],
		ReqTime:    now,
		UpdateTime: now,
		ReviewTime: now,
		Status:     model.Process,
	}

	return s.dao.ReqRealName(ctx, data)
}

func (s *Svc) GetRealNameInfo(ctx context.Context, noId string) (*model.RspRealName, error) {
	info, err := s.dao.GetRealNameInfo(ctx, noId)
	if err != nil {
		return nil, err
	}

	rsp := &model.RspRealName{
		RealName:  info.RealName,
		FrontImg:  info.FrontImg,
		BackImg:   info.BackImg,
		HandleImg: info.HandleImg,
		Status:    info.Status,
	}
	rsp.IdCard, _ = crypto.Decrypt(info.IdCard, []byte(s.conf.UserTokenSecret))

	return rsp, nil
}
