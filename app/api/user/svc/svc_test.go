package svc

import (
	"log"

	"creativematrix.com/beyondreading/app/api/user/conf"

	"github.com/BurntSushi/toml"
)

var svc *Svc

func init() {
	cf, err := LoadConf()
	if err != nil {
		log.Fatal(err)
	}

	svc = Load(cf)
}

func LoadConf() (*conf.Config, error) {
	cf := &conf.Config{}
	if _, err := toml.DecodeFile("../../../base.toml", cf); err != nil {
		return nil, err
	}
	if _, err := toml.DecodeFile("../cmd/api-user.toml", cf); err != nil {
		return nil, err
	}
	return cf, nil
}
