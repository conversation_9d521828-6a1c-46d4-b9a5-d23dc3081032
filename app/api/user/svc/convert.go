package svc

import (
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/utils"
	"time"

	"creativematrix.com/beyondreading/app/api/user/model/vo"
	userpb "creativematrix.com/beyondreading/proto/user"
)

// convertUserFromPB 转换用户信息从PB到VO
func (s *UserSvc) convertUserFromPB(pbUser *userpb.UserInfo) *vo.UserInfo {
	if pbUser == nil {
		return nil
	}

	userVo := &vo.UserInfo{}

	if err := utils.JsonCopy(pbUser, userVo); err != nil {
		logger.LogErrorf(err.Error())
		return nil
	}
	/*
		user := &vo.UserInfo{
			UserId:      pbUser.UserId,
			Phone:       pbUser.Phone,
			Email:       pbUser.Email,
			Nickname:    pbUser.Nickname,
			Avatar:      pbUser.Avatar,
			Gender:      pbUser.Gender,
			Birthday:    pbUser.Birthday,
			Location:    pbUser.Location,
			Status:      pbUser.Status,
			LoginType:   pbUser.LoginType,
			GoogleId:    pbUser.GoogleId,
			AppleId:     pbUser.AppleId,
			LastLoginIp: pbUser.LastLoginIp,
			CreatedAt:   time.Unix(pbUser.CreatedAt, 0),
			UpdatedAt:   time.Unix(pbUser.UpdatedAt, 0),
		}
	*/

	if pbUser.LastLoginAt > 0 {
		lastLoginAt := time.Unix(pbUser.LastLoginAt, 0)
		userVo.LastLoginAt = &lastLoginAt
	}

	return userVo
}

// convertLoginLogFromPB 转换登录日志从PB到VO
func (s *UserSvc) convertLoginLogFromPB(pbLog *userpb.LoginLog) *vo.LoginLogInfo {
	if pbLog == nil {
		return nil
	}

	loginLog := &vo.LoginLogInfo{}

	if err := utils.JsonCopy(pbLog, loginLog); err != nil {
		logger.LogErrorf(err.Error())
		return nil
	}

	/*
		log := &vo.LoginLogInfo{
			LogId:       pbLog.LogId,
			UserId:      pbLog.UserId,
			LoginType:   pbLog.LoginType,
			LoginIp:     pbLog.LoginIp,
			UserAgent:   pbLog.UserAgent,
			DeviceId:    pbLog.DeviceId,
			LoginResult: pbLog.LoginResult,
			FailReason:  pbLog.FailReason,
			CreatedAt:   time.Unix(pbLog.CreatedAt, 0),
		}
	*/

	return loginLog
}
