package svc

import (
	"context"
	pc "creativematrix.com/beyondreading/app/base/chat/api"
	"errors"
	"fmt"
	"strings"
	"time"

	"golang.org/x/sync/errgroup"

	"creativematrix.com/beyondreading/app/api/user/model"
	"creativematrix.com/beyondreading/app/api/user/third/apple"
	"creativematrix.com/beyondreading/app/api/user/third/qq"
	"creativematrix.com/beyondreading/app/api/user/third/weixin"
	"creativematrix.com/beyondreading/pkg/check/shumei"
	"creativematrix.com/beyondreading/pkg/ecode"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/utils"
)

func (s *Svc) ThirdCheck(ctx context.Context, lR *model.LoginReq) error {
	switch lR.Mode {
	default:
		return model.LoginTypeError
	case model.Weixin:
		var data weixin.WeChatRsp
		err := weixin.GetSnsUserInfo(ctx, lR, &data)
		if err != nil {
			return err
		}
		if data.ErrMsg != "" {
			return errors.New(data.ErrMsg)
		}

		if lR.UnionId != data.UnionId {
			return model.UserWechatLoginError
		}
	case model.QQ:
		err := qq.GetSimpleUserInfo(ctx, lR, s.conf.QQ.AppID)
		if err != nil {
			return err
		}

	case model.Apple:
		clientSecret, err := apple.VerifyIdentityToken(ctx, lR.Token, lR.UnionId, s.conf.IosSecret)
		if err != nil {
			return err
		}
		lR.Secret = clientSecret
	}
	return nil
}

func (s *Svc) Login(ctx context.Context, loginInfo *model.LoginReq) (*model.LoginRes, error) {
	ls := &model.LoginRes{}

	if loginInfo.Mode != "Mobile" {
		// 验证用户信息，得到第三方给的用户信息
		if err := s.ThirdCheck(ctx, loginInfo); err != nil {
			return nil, err
		}
	}

	// 检查是否存在信息
	um, err := s.dao.RegisterInfo(ctx, loginInfo)
	if err != nil {
		return nil, err
	}
	ls.Status = um.RegisterStatus

	logoff, err := s.dao.GetLogoffRecord(ctx, um.NoId)
	if err != nil {
		return nil, err
	}
	if logoff.ReqTime != "" {
		reqTime, _ := time.Parse(model.DateLayout, logoff.ReqTime)
		day := reqTime.AddDate(0, 0, 7).Format(model.DateLayoutCN)
		ls.LogoffMsg = fmt.Sprintf(model.LogoffNotice, day)
		ls.Status = model.UserLogoff
	}

	var (
		userId string
		noId   string
		msgId  string
		gender string
	)

	switch um.RegisterStatus {
	case model.UserNone:
		//未注册
		userId, noId, msgId, err = s.dao.InsertUser(ctx, loginInfo)
		if err != nil {
			return nil, err
		}
		gender = "unkown"
		ls.Status = model.UserUnFinish
	case model.UserUnFinish, model.UserFinish:
		userId = um.ID.Hex()
		noId = um.NoId
		msgId = um.MsgId
		gender = um.Gender
		if loginInfo.Secret != "" {
			_ = s.dao.UpdateUser(ctx, loginInfo, userId)
		}
	case model.UserForbid:
		if time.Now().AddDate(0, 0, -um.ForbidDays).Unix() < um.ForbidTime {
			return nil, model.UserForbidErr
		} else {
			userId = um.ID.Hex()
			noId = um.NoId
			msgId = um.MsgId
			gender = um.Gender
			ls.Status = model.UserFinish
			err = s.dao.UserSetFree(ctx, um.NoId)
			if err != nil {
				return nil, err
			}
		}
	}

	// 获取token(finish 客户端直接跳转正常使用app, unfinish继续完成注册)
	token, err := s.getToken(ctx, userId, noId, msgId, gender)
	if err != nil {
		return nil, err
	}
	go s.sendReLoginMsg(ctx, noId, msgId, loginInfo.Platform, token)
	ls.Token = token
	ls.NoId = noId

	return ls, nil
}

func (s *Svc) sendReLoginMsg(ctx context.Context, noId, msgId, platform, token string) {
	_, err := s.dao.SendMessage(ctx, &pc.SendMessageReq{
		Event: "PrivateEvent",
		Level: pc.Level_High,
		From:  s.conf.SystemUser.MsgId,
		To:    msgId,
		Mode:  pc.Mode_Private,
		Data: utils.JsonByte(pc.PrivateChat{
			Content: "已在其他设备登录",
			Extra: &pc.PrivateChatExtra{
				Subject: pc.Subject_PrivateCode,
				Data: &pc.PrivateData{
					SubType: pc.SubType_ReLoginMsg.String(),
					Attach: &pc.Attach{
						ReLogin: &pc.ReLogin{
							Platform: platform,
							Token:    token,
						},
					},
				},
			},
		}),
	})
	if err != nil {
		logger.LogErrorw("sendReLoginMsg", "noId", noId, "error", err)
	}
}

func (s *Svc) BindList(ctx context.Context, userId string) ([]*model.BindListRes, error) {

	br := make([]*model.BindListRes, 0)
	userInfo, err := s.dao.GetUserInfoByIdM(ctx, userId)
	if err != nil {
		return nil, err
	}

	for _, v := range model.ModeLogin {
		switch v {
		case model.Mobile:
			status := false
			val := ""
			if userInfo.Mobile != "" {
				status = true
				val = userInfo.Mobile[0:3] + "****" + userInfo.Mobile[7:]
			}
			br = append(br, &model.BindListRes{
				Status: status,
				Val:    val,
				Mode:   "Mobile",
			})
		case model.Weixin:
			status := false
			val := ""
			if userInfo.Mode.Weixin != nil && userInfo.Mode.Weixin.UnionId != "" {
				status = true
			}
			br = append(br, &model.BindListRes{
				Status: status,
				Val:    val,
				Mode:   "Weixin",
			})
		case model.QQ:
			status := false
			val := ""
			if userInfo.Mode.QQ != nil && userInfo.Mode.QQ.UnionId != "" {
				status = true
			}
			br = append(br, &model.BindListRes{
				Status: status,
				Val:    val,
				Mode:   "QQ",
			})
		case model.Apple:
			status := false
			val := ""
			if userInfo.Mode.Apple != nil && userInfo.Mode.Apple.UnionId != "" {
				status = true
				if userInfo.Mode.Apple.FullName != "" {
					val = userInfo.Mode.Apple.FullName
				}
			}
			br = append(br, &model.BindListRes{
				Status: status,
				Val:    val,
				Mode:   "Apple",
			})
		}
	}

	return br, nil
}

func (s *Svc) SmsLogin(ctx context.Context, sccr *model.SmsLoginReq) (*model.LoginRes, error) {
	if s.conf.SmsSwitch {
		v, err := s.dao.CheckCode(ctx, sccr.Mobile, sccr.Code, "")
		if err != nil && err != ecode.NothingFound {
			return nil, err
		}
		if !v {
			return nil, model.UserSmsCheckError
		}
	}

	var lR model.LoginReq
	lR.Mode = "Mobile"
	lR.Type = sccr.Type
	lR.Mobile = sccr.Mobile
	lR.Platform = sccr.Platform

	return s.Login(ctx, &lR)
}

func (s *Svc) FlashLogin(ctx context.Context, flr *model.FlashLoginReq) (*model.LoginRes, error) {
	rQ, err := s.dao.FlashsdkMobileQuery(ctx, flr)
	if err != nil {
		return nil, err
	}

	var lR model.LoginReq
	lR.Mode = "Mobile"
	lR.Mobile = rQ["mobile"].(string)
	lR.Platform = flr.Platform

	return s.Login(ctx, &lR)
}

func (s *Svc) Bind(ctx context.Context, br *model.LoginReq, userId string) (*model.BindRes, error) {
	ls := &model.BindRes{}
	ls.Status = false

	if br.Mode != "Mobile" {
		// 验证用户信息，得到第三方给的用户信息
		if err := s.ThirdCheck(ctx, br); err != nil {
			return nil, err
		}
	}

	// 检查是否存在信息
	um, err := s.dao.RegisterInfo(ctx, br)
	if err != nil {
		return nil, err
	}
	if um.RegisterStatus == "none" {
		// 绑定账号
		if err := s.dao.UpdateUser(ctx, br, userId); err != nil {
			return nil, err
		}
		ls.Status = true
	} else if um.ID.Hex() == userId {
		ls.Status = true
	} else {
		//生成bindToken
		ls.Status = false
		bindToken, err := s.dao.GenEncryptBindToken(userId, um.ID.Hex())
		if err != nil {
			return nil, err
		}
		ls.BindToken = bindToken
	}
	return ls, nil
}

func (s *Svc) BindFlash(ctx context.Context, flr *model.FlashLoginReq, userId string) (*model.BindRes, error) {
	rQ, err := s.dao.FlashsdkMobileQuery(ctx, flr)
	if err != nil {
		return nil, err
	}
	var lR model.LoginReq
	lR.Mode = "Mobile"
	lR.Mobile = rQ["mobile"].(string)

	bindRes, err := s.Bind(ctx, &lR, userId)
	if err != nil {
		return nil, err
	}
	bindRes.Mobile = lR.Mobile[0:3] + "****" + lR.Mobile[7:]

	return bindRes, nil
}

func (s *Svc) SmsBind(ctx context.Context, sccr *model.SmsLoginReq, userId string) (*model.BindRes, error) {
	if s.conf.SmsSwitch {
		v, err := s.dao.CheckCode(ctx, sccr.Mobile, sccr.Code, "")
		if err != nil {
			return nil, err
		}
		if !v {
			return nil, model.UserSmsCheckError
		}
	}
	var lR model.LoginReq
	lR.Mode = "Mobile"
	lR.Type = sccr.Type
	lR.Mobile = sccr.Mobile

	return s.Bind(ctx, &lR, userId)
}

func (s *Svc) SwitchUser(ctx context.Context, req *model.SwitchUserReq, userId string) (*model.SwitchUserRes, error) {
	res := &model.SwitchUserRes{}
	srcUserId, dstUserId, expireTime, err := s.dao.ParseEncryptBindToken(req.BindToken)
	if err != nil {
		return res, err
	}
	if time.Now().Unix() > expireTime {
		return res, model.TimeoutErr
	}
	if userId != srcUserId {
		return res, model.TOKENERROR
	}
	dstUser, err := s.dao.GetUserInfoByIdM(ctx, dstUserId)
	if err != nil {
		return res, err
	}
	res.Status = dstUser.RegisterStatus
	switch dstUser.RegisterStatus {
	case model.UserForbid:
		if time.Now().AddDate(0, 0, -dstUser.ForbidDays).Unix() < dstUser.ForbidTime {
			return nil, model.UserForbidErr
		} else {
			res.Status = model.UserFinish
			err = s.dao.UserSetFree(ctx, dstUser.NoId)
			if err != nil {
				return nil, err
			}
		}
	case model.UserNone:
		return res, ecode.ErrorArgument
	}

	token, err := s.getToken(ctx, dstUserId, dstUser.NoId, dstUser.MsgId, dstUser.MsgId)
	if err != nil {
		return nil, err
	}
	res.Token = token
	return res, nil
}

func (s *Svc) getToken(ctx context.Context, userId, noId, msgId, gender string) (string, error) {
	// 清除redis缓存token
	err := s.dao.LastTokenStatus(ctx, userId)
	if err != nil {
		return "", err
	}

	// 作废mongodb token
	_, err = s.dao.InvalidTokens(ctx, userId)
	if err != nil {
		return "", err
	}

	// 创建token
	token, err := s.dao.CreateToken(ctx, userId, noId, msgId, gender)
	if err != nil {
		return "", err
	}

	// 把token加入redis
	_, err = s.dao.CacheToken(ctx, userId, token)
	if err != nil {
		return "", err
	}

	return token, nil
}

func (s *Svc) LoginName(ctx context.Context) (string, error) {
	var (
		adj, part, noun string
		eg              errgroup.Group
	)
	eg.Go(func() error {
		names, e := s.dao.GetNickName(ctx, model.NameAdj)
		if e != nil {
			return e
		}
		num := utils.RandNum(len(names))
		if num != -1 {
			adj = names[num].Name
		}
		return nil
	})
	eg.Go(func() error {
		names, e := s.dao.GetNickName(ctx, model.NamePart)
		if e != nil {
			return e
		}
		num := utils.RandNum(len(names))
		if num != -1 {
			part = names[num].Name
		}
		return nil
	})
	eg.Go(func() error {
		names, e := s.dao.GetNickName(ctx, model.NameNoun)
		if e != nil {
			return e
		}
		num := utils.RandNum(len(names))
		if num != -1 {
			noun = names[num].Name
		}
		return nil
	})
	if err := eg.Wait(); err != nil {
		return "", err
	}

	return fmt.Sprintf("%s%s%s", adj, part, noun), nil
}

func (s *Svc) Logout(ctx context.Context, userId, noId string, token string) (bool, error) {

	// 作废mongodb token
	_, err := s.dao.InvalidTokens(ctx, userId)
	if err != nil {
		return false, err
	}
	// 删除redis缓存
	if err = s.dao.RemoveCacheToken(ctx, token); err != nil {
		return false, err
	}
	// 解绑推送
	_ = s.dao.UnBindPushAlias(ctx, noId)

	return true, nil
}

func (s *Svc) LoginInfo(ctx context.Context, loginInfo *model.LoginInfoReq, userId, noId, msgId string) error {
	if loginInfo.GroupCode != "" {
		code, e := s.dao.GetGroupCode(ctx, loginInfo.GroupCode)
		if e != nil {
			return e
		}
		if code == nil {
			return model.InvalidGroupCode
		}
	}

	err := s.ReviewMsg(ctx, loginInfo.NickName, msgId, true)
	if err != nil {
		return err
	}

	//默认头像
	var (
		passMode string
		priority model.Priority
	)

	oldAvatar := loginInfo.AvatarUrl
	if !strings.Contains(loginInfo.AvatarUrl, model.DefaultAvatar) {
		oldAvatar = s.GetAvatars(loginInfo.Gender)
	} else {
		loginInfo.AvatarUrl = ""
	}
	isReview := false
	if loginInfo.AvatarUrl != "" {
		isReview = true
		smImgUrl := s.conf.BaseImgUrl + loginInfo.AvatarUrl
		rule, e := s.getRule(ctx, model.Avatar)
		if e != nil {
			return e
		}

		switch rule.CheckMode & model.RuleAll {
		case model.RuleForbid:
			return model.ForbidUploadImg
		case model.RuleManual:
			loginInfo.AvatarStatus = model.Process
			loginInfo.CheckAvatar = loginInfo.AvatarUrl
			loginInfo.AvatarUrl = oldAvatar
			priority = model.HighPriority
		case model.RuleSM:
			fallthrough
		case model.RuleAll:
			smRsp, err := s.checkImg(ctx, smImgUrl, msgId)
			if err != nil {
				return err
			}
			priority, passMode = getPassMode(rule, smRsp.RiskLevel)
			loginInfo.AvatarStatus = getLocStatus(passMode)
			loginInfo.CheckAvatar = loginInfo.AvatarUrl
			if loginInfo.AvatarStatus == model.Process {
				//审核中
				loginInfo.AvatarUrl = oldAvatar
			}
			loginInfo.SMResult = smRsp.RiskLevel
			loginInfo.SMReason = smRsp.Detail.Description
		}
	} else {
		loginInfo.AvatarUrl = oldAvatar
		loginInfo.AvatarStatus = model.Success
	}

	if passMode == model.SMReject {
		return model.InvalidAvatar
	}

	if err = s.dao.UpdateLoginInfo(ctx, loginInfo, userId); err != nil {
		return err
	}

	err = s.esUpdateInfo(ctx, userId, noId, loginInfo)
	if err != nil {
		return err
	}

	if isReview {
		record := &model.TReviewAvatar{
			UserId:       userId,
			NoId:         noId,
			AvatarUrl:    loginInfo.CheckAvatar,
			OldAvatar:    oldAvatar,
			NickName:     loginInfo.NickName,
			SMResult:     loginInfo.SMResult,
			SMReason:     loginInfo.SMReason,
			AvatarStatus: model.Process,
			ReqTime:      time.Now().Unix(),
			UpdateTime:   time.Now().Unix(),
			Priority:     priority,
		}
		err = s.dao.CheckUserMedia(model.MQSMAvatarEvent, utils.JsonByte(record))
		if err != nil {
			return err
		}
	}

	return err
}

func (s *Svc) esUpdateInfo(ctx context.Context, userId, noId string, info *model.LoginInfoReq) error {
	//注册信息写入es
	esUser := &model.UserES{
		Id:        userId,
		NoId:      noId,
		NickName:  info.NickName,
		Gender:    info.Gender,
		Born:      info.Born,
		Info:      model.StarSignStatus,
		Online:    model.Online,
		IsNew:     true,
		IsReal:    false,
		GroupCode: info.GroupCode,
	}

	err := s.dao.SendEsUpdateQ(ctx, &model.UpdateES{
		Type:      model.EsIsNew,
		Timestamp: time.Now().Unix(),
		Data:      esUser},
	)

	return err
}

func (s *Svc) checkImg(ctx context.Context, imgUrl, msgId string) (*shumei.ImgResponse, error) {
	rspImg := new(shumei.ImgResponse)
	err := s.dao.Img(ctx, shumei.ImgRequest{
		Data: &shumei.ImgData{
			Img:     imgUrl,
			TokenId: msgId,
		},
	}, rspImg)

	return rspImg, err
}

// 返回流程状态
func getLocStatus(ruleMode string) string {
	switch ruleMode {
	case model.SMPass:
		return model.Success
	case model.SMReview:
		//后台人审
		return model.Process
	case model.SMReject:
		return model.Reject
	}

	return model.Reject
}

func getPassMode(rule *model.TReviewRule, riskLevel string) (model.Priority, string) {
	switch riskLevel {
	case model.SMPass:
		return rule.Pass, rule.PassMode
	case model.SMReview:
		return rule.Review, rule.ReviewMode
	case model.SMReject:
		return rule.Reject, rule.RejectMode
	default:
		logger.LogErrorf("invalid riskLevel:%s", riskLevel)
		return model.NonePriority, model.SMReject
	}
}

// 数美风险等级，返回本地策略
func (s *Svc) getRule(ctx context.Context, t string) (*model.TReviewRule, error) {
	rule, err := s.dao.GetReviewRule(ctx, t)
	if err != nil {
		return nil, model.ServerErr
	}

	return rule, nil
}

func (s *Svc) RandAvatar(ctx context.Context, gender string) string {
	return s.GetAvatars(gender)
}
