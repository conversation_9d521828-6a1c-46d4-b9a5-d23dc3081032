package svc

import (
	"context"
	"fmt"
	"math/rand"
	"time"

	"creativematrix.com/beyondreading/app/api/user/model"
	"creativematrix.com/beyondreading/app/api/user/third/weixin"
	"creativematrix.com/beyondreading/pkg/typeconvert"
	"creativematrix.com/beyondreading/pkg/utils"
)

func (s *Svc) WeixinInfo(ctx context.Context, wr *model.WeixinReq) (map[string]interface{}, error) {
	w := make(map[string]interface{})

	var woqt weixin.WeChatOauthAccessTokenRsp

	wr.AppId = s.conf.WeiXin.AppID
	wr.Secret = s.conf.WeiXin.AppSecret
	if err := weixin.GetSnsUserInfoOauthAccessToken(ctx, wr, &woqt); err != nil {
		return nil, err
	}

	w["openid"] = woqt.OpenId
	if wr.Scope == "snsapi_userinfo" {
		// snsapi_userinfo用于登录
		var wcr weixin.WeChatRsp
		lR := &model.LoginReq{
			Token:  woqt.AccessToken,
			OpenId: woqt.OpenId,
		}

		err := weixin.GetSnsUserInfo(ctx, lR, &wcr)
		if err != nil {
			return nil, err
		}

		req := &model.LoginReq{
			UnionId: wcr.UnionId,
			Mode:    model.Weixin,
		}
		// 检查是否存在信息
		user, err := s.dao.RegisterInfo(ctx, req)
		if err != nil {
			return nil, err
		}
		if user.RegisterStatus == model.UserNone || user.RegisterStatus == model.UserLogoff {
			return nil, model.NothingFound
		}

		token, err := s.dao.GetLastToken(ctx, user.ID.Hex())
		if err != nil {
			return nil, err
		}
		w["token"] = token
	}

	return w, nil
}

// GetJsSign 微信js sdk sign
func (s *Svc) GetJsSign(url string) *model.RspWxJsParam {
	ts := time.Now().Unix()
	nonce := utils.Md5String(typeconvert.Int64ToString(ts + rand.Int63n(10000)))
	signStr := fmt.Sprintf("jsapi_ticket=%s&noncestr=%s&timestamp=%d&url=%s", s.wxPublic.JsApiTicket,
		nonce, ts, url)

	return &model.RspWxJsParam{
		Timestamp: ts,
		Nonce:     nonce,
		Sign:      utils.Sha1(signStr),
	}
}

func (s *Svc) GetContact(ctx context.Context) *model.Contact {
	contact := s.dao.GetUserContact(ctx)
	if contact != nil {
		return contact
	}

	return &model.Contact{
		OfficialWX: s.conf.OfficialWX,
	}
}
