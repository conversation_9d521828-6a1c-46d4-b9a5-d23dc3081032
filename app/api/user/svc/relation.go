package svc

import (
	"context"
	"time"

	"creativematrix.com/beyondreading/app/api/user/model"
	pc "creativematrix.com/beyondreading/app/base/chat/api"
	livepb "creativematrix.com/beyondreading/app/base/live/api"
	pb "creativematrix.com/beyondreading/app/base/user/api"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/mongo"
	"creativematrix.com/beyondreading/pkg/utils"
)

/*
	@Author:cwl
	@Info:建立好友关系
	@Date:2021-07-07
*/

// AttentionSet 建立关注
func (s *Svc) AttentionSet(ctx context.Context, userId, toUserId string, mode int) (*model.RelationInfoRsp, error) {
	var (
		err error
	)
	if mode < 0 || mode > 1 || userId == toUserId {
		return nil, model.ErrorArgument
	}

	fCap := s.dao.CountAttentionById(ctx, userId)
	if fCap >= s.conf.Focus.MaxCap {
		return nil, model.UserAttentionCapOver
	}

	if e := s.checkUserStatus(ctx, toUserId); e != nil {
		return nil, e
	}

	session, err := s.dao.NewChatMongoSession(ctx)
	if err != nil {
		return nil, model.ServerErr
	}

	defer func() {
		if err != nil {
			_ = session.Rollback()
		} else {
			err = session.Commit()
			if err != nil {
				_ = session.Rollback()
			}
		}
		session.Close()
	}()
	//TODO 查看建立关系的用户是否存在

	err = s.analysisUser(ctx, session, userId, toUserId, mode)
	if err != nil {
		return nil, model.SetUserRelation
	}

	userSessionList, err := s.dao.AttentionSession(ctx, session, userId, toUserId)
	if err != nil {
		logger.LogErrorw("AttentionSet SessionListGet", "error", err)
		return nil, model.SetUserRelation
	}
	if len(userSessionList) == 0 {
		logger.LogErrorw("AttentionSet userSessionList length", "error", "not found result")
		return nil, model.SetUserRelation
	}

	if mode == 1 {
		go s.liveSendMessage(ctx, userId, toUserId)
	}

	tr := &model.RelationInfoRsp{
		UserId:   userId,
		ToUserId: toUserId,
		Relation: userSessionList[0].Relation,
	}

	return tr, nil
}

func (s *Svc) checkUserStatus(ctx context.Context, dstUser string) error {
	user, err := s.getUserByNoId(ctx, dstUser, "registerStatus")
	if err != nil {
		return err
	}
	if user.RegisterStatus == model.UserLogoff {
		return model.UserLogoffError
	}
	return nil
}

func (s *Svc) liveSendMessage(ctx context.Context, userId, toUserId string) {
	var err error
	defer func() {
		if err != nil {
			logger.LogErrorw("liveSendMessage", "error", err)
		}
	}()

	liveInfo, err := s.dao.LiveInfo(ctx, &livepb.LiveInfoReq{
		AnchorId: toUserId,
	})

	if err != nil {
		return
	}

	//开播需要发送消息
	if liveInfo.OpenStatus != "Open" {
		return
	}

	_ = s.dao.IncrFocusInLiveSet(ctx, toUserId, userId)

	user, err := s.getUserByNoId(ctx, userId, "noId", "nickName", "wealth")
	if err != nil {
		return
	}

	notice := &pc.Notice{
		NickName:    user.NickName,
		Content:     "关注了主播",
		WealthLevel: int32(utils.GetWealthLv(user.Wealth)),
		NoId:        user.NoId,
	}

	notice.Identity = s.identityGet(ctx, toUserId, userId)

	err = s.systemNoticeSend(ctx, liveInfo.LiveMsgId, pc.SubType_LVAttention.String(), notice)
}

func (s *Svc) identityGet(ctx context.Context, anchorId, noId string) int32 {
	rsp, err := s.dao.IsAdministrator(ctx, &livepb.IsAdministratorReq{
		AnchorId: anchorId,
		NoId:     noId,
	})

	if err != nil {
		logger.LogErrorw("identityGet IsAdministrator", "error", err, "anchorId", anchorId, "noId", noId)
		return 0
	}
	if rsp.Status {
		return 1
	}
	return 0
}

//checkUserValidate TODO 女性关注对方亲密度是否达标
//func (s *Svc) checkUserValidate(ctx context.Context, from, to string) error {
//	toUserRsp, err := s.dao.UserListGetByNoIds(ctx, &pb.UserIdsReq{
//		Ids:  []string{to},
//		Cols: []string{"_id", "gender"},
//	})
//	if err != nil {
//		logger.LogErrorw("AttentionSet UserListGetByNoIds", "error", err)
//		return model.ServerErr
//	}
//
//	if len(toUserRsp.Users) == 0 {
//		logger.LogErrorw("AttentionSet toUser", "error", err, "to", to)
//		return model.UserNotFound
//	}
//
//	if toUserRsp.Users[0].Gender == "male" {
//		return nil
//	}
//
//	rsp, err := s.dao.IntimacyGet(ctx, from, to)
//	if err != nil {
//		logger.LogErrorw("FemaleIntimacyLevel IntimacyGet", "error", err)
//		return model.IntimacyGetError
//	}
//
//	//校验女生亲密值是否达标
//	if rsp.Intimacy < FemaleAttention {
//		return model.NoOKLevel
//	}
//
//	return nil
//}

// 分析用户关系
func (s *Svc) analysisUser(ctx context.Context, session *mongo.Session, userId string, toUserId string, mode int) error {
	userSessionList, err := s.dao.AttentionSession(ctx, session, userId, toUserId)
	if err != nil {
		return err
	}
	union := utils.NewUnion(userId, toUserId)
	t := time.Now()

	//双方记录都未发现、新增两条数据
	if len(userSessionList) == 0 {
		if mode == 0 {
			err = s.dao.SessionListInsert(ctx, session, &model.TSessionList{
				Union:      union,
				From:       userId,
				To:         toUserId,
				Relation:   model.NoANoB, //user关注了toUser、toUser没有关注user
				CreateTime: &t,
				UpdateTime: &t,
			})
			if err != nil {
				return err
			}

			//逆角度思考
			err = s.dao.SessionListInsert(ctx, session, &model.TSessionList{
				Union:      union,
				From:       toUserId,
				To:         userId,
				Relation:   model.NoANoB, //user关注了toUser、toUser没有关注user
				CreateTime: &t,
				UpdateTime: &t,
			})
			if err != nil {
				return err
			}

		} else {
			err = s.dao.SessionListInsert(ctx, session, &model.TSessionList{
				Union:      union,
				From:       userId,
				To:         toUserId,
				Relation:   model.ANoB, //user关注了toUser、toUser没有关注user
				CreateTime: &t,
				UpdateTime: &t,
			})
			if err != nil {
				return err
			}

			//逆角度思考
			err = s.dao.SessionListInsert(ctx, session, &model.TSessionList{
				Union:      union,
				From:       toUserId,
				To:         userId,
				Relation:   model.NoAB, //user关注了toUser、toUser没有关注user
				CreateTime: &t,
				UpdateTime: &t,
			})
			if err != nil {
				return err
			}
		}

	} else if len(userSessionList) != 0 {
		relationType := userSessionList[0].Relation

		switch relationType {
		//互相没有关注==>
		case model.NoANoB:
			//取消关注
			if mode == 0 {
				return nil
			} else { //A 关注 B
				err = s.dao.AttentionSet(ctx, session, &model.TSessionList{
					From:     userId,
					To:       toUserId,
					Relation: model.ANoB,
				})
				if err != nil {
					return err
				}

				err = s.dao.AttentionSet(ctx, session, &model.TSessionList{
					From:     toUserId,
					To:       userId,
					Relation: model.NoAB,
				})
				if err != nil {
					return err
				}
			}

		//互相关注
		case model.AB:
			//取消A关注B
			if mode == 0 {
				err = s.dao.AttentionSet(ctx, session, &model.TSessionList{
					From:     userId,
					To:       toUserId,
					Relation: model.NoAB,
				})
				if err != nil {
					return err
				}
				err = s.dao.AttentionSet(ctx, session, &model.TSessionList{
					From:     toUserId,
					To:       userId,
					Relation: model.ANoB,
				})
				if err != nil {
					return err
				}
			} else {
				return nil
			}

		//A关注了B B没有关注A
		case model.ANoB:
			//取消A关注B
			if mode == 0 {
				err = s.dao.AttentionSet(ctx, session, &model.TSessionList{
					From:     userId,
					To:       toUserId,
					Relation: model.NoANoB,
				})
				if err != nil {
					return err
				}

				err = s.dao.AttentionSet(ctx, session, &model.TSessionList{
					From:     toUserId,
					To:       userId,
					Relation: model.NoANoB,
				})
				if err != nil {
					return err
				}
			} else {
				return nil
			}

		//A没有关注B  B关注了A
		case model.NoAB:
			if mode == 0 {
				return nil
			} else {
				err = s.dao.AttentionSet(ctx, session, &model.TSessionList{
					From:     userId,
					To:       toUserId,
					Relation: model.AB,
				})
				if err != nil {
					return err
				}

				err = s.dao.AttentionSet(ctx, session, &model.TSessionList{
					From:     toUserId,
					To:       userId,
					Relation: model.AB,
				})
				if err != nil {
					return err
				}
			}
		}
	}
	return nil
}

/*
@AttentionGetList 获取关注的好友列表
@userId 用户ID
*/
func (s *Svc) AttentionGetList(ctx context.Context, userId string, t int, pageSize, pageNum int64) ([]*model.RelationListRsp, error) {
	var (
		rsps []*model.TSessionList
		err  error
	)

	switch t {
	case 0:
		rsps, err = s.dao.IntimacyFriendGetById(ctx, userId, pageSize, pageNum)
		if err != nil {
			logger.LogErrorw("AttentionGetList AttentionFriendGetById", "error", err)
			return nil, model.GetUsetRelationList
		}

	//密友 0     好友 1    关注  2   粉丝  3
	case 1:
		rsps, err = s.dao.AttentionFriendGetById(ctx, userId, pageSize, pageNum)
		if err != nil {
			logger.LogErrorw("AttentionGetList IntimacyFriendGetById", "error", err)
			return nil, model.GetUsetRelationList
		}

	case 2:
		rsps, err = s.dao.AttentionGetById(ctx, userId, pageSize, pageNum)
		if err != nil {
			logger.LogErrorw("AttentionGetList AttentionGetById", "error", err)

			return nil, model.GetUsetRelationList
		}

	case 3:
		rsps, err = s.dao.AttentionMyGetById(ctx, userId, pageSize, pageNum)
		if err != nil {
			logger.LogErrorw("AttentionGetList AttentionMyGetById", "error", err)
			return nil, model.GetUsetRelationList
		}

	default:
		return nil, model.ErrorArgument
	}

	if len(rsps) == 0 {
		return nil, nil
	}
	return s.fillUserInfo(ctx, rsps, t)
}

func (s *Svc) FocusList(ctx context.Context, userId string) ([]string, error) {
	var noIds []string
	list, err := s.dao.AttentionGetByIdAll(ctx, userId)
	if err != nil {
		logger.LogErrorw("AttentionGetList AttentionGetById", "error", err)
		return nil, model.GetUsetRelationList
	}

	for _, ll := range list {
		noIds = append(noIds, ll.To)
	}
	return noIds, nil
}

// fillUserInfo 填空用户详情信息
func (s *Svc) fillUserInfo(ctx context.Context, userSessionList []*model.TSessionList, t int) ([]*model.RelationListRsp, error) {
	userIds := make([]string, 0)
	relationListRsp := make([]*model.RelationListRsp, 0)

	for _, v := range userSessionList {
		if t == 3 {
			userIds = append(userIds, v.From)
			continue
		}
		userIds = append(userIds, v.To)
	}

	userList, err := s.dao.UserListGetByNoIds(ctx, &pb.UserIdsReq{
		Ids:  userIds,
		Cols: []string{"_id", "noId", "nickName", "gender", "born", "avatarUrl"},
	})
	if err != nil {
		return nil, model.GetUserDetail
	}

	for _, v := range userList.Users {
		if v.RegisterStatus == model.UserLogoff {
			continue
		}
		relationListRsp = append(relationListRsp, &model.RelationListRsp{
			UserId:    v.NoId,
			NickName:  v.NickName,
			Gender:    v.Gender,
			Age:       utils.GetAgeByDate(v.Born),
			AvatarUrl: v.AvatarUrl,
		})
	}

	return relationListRsp, nil
}

func (s *Svc) getUserByNoId(ctx context.Context, noId string, cols ...string) (*pb.UserInfo, error) {
	userRsp, err := s.dao.GetUsersByIds(ctx, &pb.UserIdsReq{
		Ids:  []string{noId},
		Cols: cols,
	})

	if err != nil {
		return nil, err
	}

	if len(userRsp.Users) == 0 {
		return nil, model.UserNotFound
	}

	return userRsp.Users[0], nil
}

func (s *Svc) systemNoticeSend(ctx context.Context, liveMsgId, subType string, notice *pc.Notice) error {

	_, err := s.dao.SendMessage(ctx, &pc.SendMessageReq{
		Event: model.MQLiveRoomEvent,
		Level: pc.Level_High,
		From:  s.conf.SystemUser.MsgId,
		To:    liveMsgId,
		Mode:  pc.Mode_ChatRoom,
		Data: utils.JsonByte(&pc.LiveRoom{
			Extra: &pc.LiveRoomExtra{
				Subject: pc.Subject_LiveroomCode,
				Data: &pc.LiveRoomData{
					SubType: subType,
					Attach: &pc.LiveRoomAttach{
						RoomId: liveMsgId,
						Notice: notice,
					},
				},
			},
		}),
	})
	if err != nil {
		logger.LogErrorw("adminstratorInsertNotice SendMessage", "error", err)
		return err
	}

	return nil
}

func (s *Svc) BindPushRelation(ctx context.Context, req *model.BindPushReq) error {
	return s.dao.BindUserPushRelation(ctx, &pb.BindPushRelationReq{
		NoId:     req.NoId,
		Cid:      req.Cid,
		Alias:    req.MsgId,
		Platform: req.Platform,
	})
}

func (s *Svc) SetSwitch(ctx context.Context, noId string, req *model.SetSwitchReq) error {
	return s.dao.SetUserSwitch(ctx, &pb.SetUserSwitchReq{
		NoId:                noId,
		Position:            req.Position,
		PrivateChatPush:     req.PrivateChatPush,
		OpenLivePush:        req.OpenLivePush,
		AutoRefuseMultiChat: req.AutoRefuseMultiChat,
		AnonymousBrowse:     req.AnonymousBrowse,
	})
}

func (s *Svc) GetSwitch(ctx context.Context, noId string) (*model.GetSwitchRsp, error) {
	rsp := &model.GetSwitchRsp{
		PrivateChatPush:     "on",
		OpenLivePush:        "on",
		AutoRefuseMultiChat: "off",
	}
	data, err := s.dao.GetUserSwitch(ctx, &pb.GetUserSwitchReq{
		NoIds: []string{noId},
	})
	if err != nil {
		return nil, err
	}
	if len(data.List) == 0 {
		return rsp, nil
	}

	rsp.PrivateChatPush = data.List[0].PrivateChatPush
	rsp.OpenLivePush = data.List[0].OpenLivePush
	rsp.AutoRefuseMultiChat = data.List[0].AutoRefuseMultiChat

	isSuperAdmin, _ := s.GetSuperAdmin(ctx, noId)
	if isSuperAdmin {
		rsp.AnonymousBrowse = data.List[0].AnonymousBrowse
	}

	return rsp, nil
}

func (s *Svc) SetPushStatus(ctx context.Context, req *model.SetStatusReq) error {
	return s.dao.SetUserPushStatus(ctx, req.NoId, req.Status)
}
