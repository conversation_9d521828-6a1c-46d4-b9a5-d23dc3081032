package svc

import (
	"context"
	"creativematrix.com/beyondreading/app/api/user/model"
	"creativematrix.com/beyondreading/pkg/utils"
	"golang.org/x/sync/errgroup"
)

func (s *Svc) InterestTagSave(ctx context.Context, req *model.InterestTagSaveReq) (bool, error) {
	if len(req.TagIds) > 20 {
		return false, model.InterestNumOver
	}

	if err := s.dao.InterestTagSave(ctx, req.NoId, req.TagIds); err != nil {
		return false, err
	}
	return true, nil
}

func (s *Svc) InterestTagSwitch(ctx context.Context, noId string) ([]*model.InterestTagAddedVO, error) {
	var (
		eg  errgroup.Group
		err error

		lib   []*model.InterestTagLib
		added = make(map[string]*model.InterestTagAddedVO)
	)

	eg.Go(func() error {
		if lib, err = s.dao.GetInterestTagLib(ctx); err != nil {
			return err
		}
		return nil
	})
	eg.Go(func() error {
		addedSli, err := s.dao.GetInterestTagAdded(ctx, noId)
		if err != nil {
			return err
		}
		for _, v := range addedSli {
			added[v.TagName] = v
		}

		return nil
	})

	if err = eg.Wait(); err != nil {
		return nil, err
	}

	result := make([]*model.InterestTagAddedVO, 0)
	lib = shuffle(lib)

	for _, v := range lib {
		if len(result) == 20 {
			break
		}

		if _, ok := added[v.Name]; !ok {
			result = append(result, &model.InterestTagAddedVO{
				TagId:   v.Id.Hex(),
				TagName: v.Name,
			})
		}
	}

	return result, nil
}

func shuffle(sli []*model.InterestTagLib) []*model.InterestTagLib {
	nums := make([]*model.InterestTagLib, len(sli))
	buf := make([]*model.InterestTagLib, len(sli))

	copy(buf, sli)
	for i := range nums {
		j := utils.RangeRandom(0, int64(len(buf)-1))
		nums[i] = buf[j]
		buf = append(buf[0:j], buf[j+1:]...)
	}

	return nums
}
