package svc

import (
	"context"
	"regexp"
	"time"

	"creativematrix.com/beyondreading/app/api/user/model"
	chatpb "creativematrix.com/beyondreading/app/base/chat/api"
	userpb "creativematrix.com/beyondreading/app/base/user/api"
	"creativematrix.com/beyondreading/pkg/ecode"
)

func (s *Svc) CheckMobile(ctx context.Context, noId, mobile string) (*model.CheckMobileRsp, error) {
	if !isMobile(mobile) {
		return nil, model.NewMobileErr
	}

	rpcRsp, err := s.dao.GetAndCheckMobile(ctx, noId, mobile, userpb.CheckType_CheckConflict)
	if err != nil {
		return nil, err
	}

	rsp := &model.CheckMobileRsp{
		IsConflict: rpcRsp.IsConflict,
	}
	if v := rpcRsp.From; v != nil {
		rsp.From = &model.MobileInfo{
			NoId:        v.NoId,
			Nickname:    v.Nickname,
			Avatar:      v.Avatar,
			WealthLevel: v.WealthLevel,
			CharmLevel:  v.Charm<PERSON>evel,
			Mobile:      v.Mobile,
		}
	}
	if v := rpcRsp.To; v != nil {
		rsp.To = &model.MobileInfo{
			NoId:        v.NoId,
			Nickname:    v.Nickname,
			Avatar:      v.Avatar,
			WealthLevel: v.WealthLevel,
			CharmLevel:  v.CharmLevel,
			Mobile:      v.Mobile,
		}
	}

	return rsp, nil
}

func (s *Svc) ApplyModifyMobile(ctx context.Context, param *model.ApplyModifyMobileReq) error {
	var msg string

	if !isMobile(param.ToMobile) {
		return model.NewMobileErr
	}

	uInfo, err := s.dao.GetUserInfoById(ctx, param.NoId, []string{"mobile"})
	if err != nil {
		return err
	}
	param.FromMobile = uInfo.Mobile

	switch param.ModifyType {
	case userpb.ModifyType_AdminHelp.String():
		if len(param.ApplyReason) == 0 {
			return model.ApplyReasonEmptyErr
		}
		if len(param.ProveImg) == 0 {
			return model.ProveImgEmptyErr
		}
	case userpb.ModifyType_UserSelf.String():
		msg = "你提交的更换手机号申请，已通过审批，请立即使用新手机号登录"
	}

	if err = s.checkSmsCode(ctx, param.ToMobile, param.SmsCode); err != nil {
		return err
	}

	if err = s.dao.ApplyModifyMobile(ctx, param); err != nil {
		return err
	}

	if len(msg) > 0 {
		go s.sendModifyMobileNotify(param.NoId, msg)
	}

	return nil
}

func (s *Svc) GetModifyMobileInfo(ctx context.Context, noId string) (*model.ModifyMobileData, error) {
	rpcRsp, err := s.dao.GetModifyMobileInfo(ctx, noId)
	if err != nil {
		return nil, err
	}
	return &model.ModifyMobileData{
		NoId:        rpcRsp.NoId,
		FromMobile:  rpcRsp.FromMobile,
		ToMobile:    rpcRsp.ToMobile,
		ModifyType:  rpcRsp.ModifyType,
		ApplyReason: rpcRsp.ApplyReason,
		ProveImg:    rpcRsp.ProveImg,
		Status:      genStatus(rpcRsp.Status),
	}, nil
}

func (s *Svc) OriginMobileCode(ctx context.Context, noId string) (bool, error) {
	rpcRsp, err := s.dao.GetAndCheckMobile(ctx, noId, "", userpb.CheckType_CheckLimitTimes)
	if err != nil {
		return false, err
	}
	if rpcRsp.IsOverTimes {
		return false, model.OverChangeMobileLimitErr
	}

	user, err := s.dao.GetUserInfoById(ctx, noId, []string{"mobile"})
	if err != nil {
		return false, err
	}

	if s.dao.IsSmsLimit(ctx, model.SmsTypeCheck, user.Mobile) {
		return false, model.CodeRateMsg
	}

	b, err := s.SmsCode(ctx, &model.SmsCodeReq{Mobile: user.Mobile, Format: model.SMSCodeModifyFormat})
	if err != nil {
		return false, err
	}

	_ = s.dao.SetSmsLimit(ctx, model.SmsTypeCheck, user.Mobile)
	return b, nil
}

func (s *Svc) CheckOriginMobileCode(ctx context.Context, noId, code string) error {
	user, err := s.dao.GetUserInfoById(ctx, noId, []string{"mobile"})
	if err != nil {
		return err
	}

	return s.checkSmsCode(ctx, user.Mobile, code)
}

func genStatus(s string) string {
	if s == userpb.ModifyStatus_Timeout.String() {
		return userpb.ModifyStatus_Reject.String()
	}
	return s
}

func (s *Svc) checkSmsCode(ctx context.Context, mobile, smsCode string) error {
	if s.conf.SmsSwitch {
		v, err := s.dao.CheckCode(ctx, mobile, smsCode, "")
		if err != nil && err != ecode.NothingFound {
			return err
		}
		if !v {
			return model.UserSmsCheckError
		}
	}
	return nil
}

func (s *Svc) sendModifyMobileNotify(noId, msg string) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second)
	defer cancel()
	uInfo, err := s.dao.GetUserInfoById(ctx, noId, []string{"msgId"})
	if err != nil || uInfo == nil {
		return
	}
	_, _ = s.dao.SendSecretary(ctx, &chatpb.SendSecretaryReq{
		Title:  msg,
		Type:   chatpb.OfSessionType_PlusF.String(),
		UserId: noId,
		MsgId:  uInfo.MsgId,
	})
}

func isMobile(mobile string) bool {
	reg := `^1([3-9])\d{9}$`
	rgx := regexp.MustCompile(reg)
	return rgx.MatchString(mobile)
}
