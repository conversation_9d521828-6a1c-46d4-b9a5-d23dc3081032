package svc

import (
	"context"
	"creativematrix.com/beyondreading/app/api/user/model"
)

func (s *Svc) GetPositionList(ctx context.Context) ([]*model.PositionManager, error) {
	list, err := s.dao.PositionList(ctx)
	if err != nil {
		return nil, err
	}

	return list, nil
}

func (s *Svc) CheckPositionWhitelist(ctx context.Context, noId string) (*model.PositionWhitelist, error) {
	isIn, err := s.dao.CheckUserInWhitelist(ctx, noId)
	if err != nil {
		return nil, err
	}

	return &model.PositionWhitelist{IsIn: isIn}, nil
}
