package svc

import (
	"context"
	"creativematrix.com/beyondreading/app/api/user/model"
	"fmt"
)

func (s *Svc) SeeYou(ctx context.Context, from, to string) error {
	lock := s.dao.SetLock(ctx, fmt.Sprintf(model.RedisUserVisitorsLock, fmt.Sprintf("%s_%s", from, to)), 30)
	if !lock {
		return nil
	}
	_ = s.dao.UserVisitorsUpdate(ctx, from, to) // 增加访客记录
	_ = s.dao.IncrUserVisitors(ctx, to)         // 增加访客数量
	return nil
}
