package svc

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"
	"sort"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"github.com/go-resty/resty/v2"

	"golang.org/x/sync/errgroup"

	"github.com/olivere/elastic/v7"

	"creativematrix.com/beyondreading/app/api/user/model"
	"creativematrix.com/beyondreading/app/api/user/third/apple"
	chatpb "creativematrix.com/beyondreading/app/base/chat/api"
	livepb "creativematrix.com/beyondreading/app/base/live/api"
	"creativematrix.com/beyondreading/app/base/user/api"
	userpb "creativematrix.com/beyondreading/app/base/user/api"
	"creativematrix.com/beyondreading/pkg/check/shumei"
	"creativematrix.com/beyondreading/pkg/ecode"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/utils"
)

// GetMyInfo 获取主页个人信息聚合
func (s *Svc) GetMyInfo(ctx context.Context, userId, noId, ip string) (*model.MyInfo, error) {
	//获取自己的信息
	myInfo := new(model.MyInfo)

	var eg errgroup.Group
	eg.Go(func() error {
		info, e := s.dao.GetUserInfoById(ctx, noId, utils.MongoColumns(myInfo))
		if e != nil {
			return e
		}
		copyMyInfo(myInfo, info)
		return nil
	})

	eg.Go(func() error {
		myInfo.DecorateStatus, myInfo.DecorateNew = s.dao.GetDecorateStatus(ctx, noId)
		dec, e := s.dao.GetUsersDecorate(ctx, []string{noId}, "avatar")
		if e != nil {
			return e
		}
		medalsDec, e := s.dao.GetUserDecLimit(ctx, noId, model.DecorateMedal, 5)
		if e != nil {
			return e
		}
		if avatar, ok := dec[noId]; ok {
			myInfo.AvatarId = avatar.Serial
		}
		if len(medalsDec) > 0 {
			var (
				medals []string
			)
			superAdminDecorate := s.dao.GetSuperAdminDecorate()
			for _, medal := range medalsDec {
				if utils.Contains(superAdminDecorate, medal.Serial) {
					medals = append(medals, medal.Serial)
				}
			}
			myInfo.Medals = medals
		}
		return nil
	})

	eg.Go(func() error {
		pMap, err := s.dao.MultiGetUsersDec(ctx, []string{noId}, model.DecoratePrettyNo)
		if err != nil {
			return err
		}
		if pv, ok := pMap[noId]; ok && pv != nil {
			myInfo.PrettyNo = &model.PrettyNo{
				Serial:       pv.Serial,
				ImgUrl:       pv.ImgUrl,
				AnimationUrl: pv.AnimationUrl,
				Speed:        pv.Seconds,
			}
		}
		return nil
	})

	eg.Go(func() error {
		//rpc好友数据
		friend, e := s.dao.GetFriends(ctx, noId)
		if e == nil {
			myInfo.Intimates = friend.IntimacyFriendsNum
			myInfo.Friends = friend.GoodFriendsNum
			myInfo.Focus = friend.AttentionNum
			myInfo.Fans = friend.FansNum
		}
		return nil
	})

	eg.Go(func() error {
		rsp, err := s.dao.GetFansClubOptions(ctx, noId)
		if err != nil {
			return err
		}
		if rsp.EnableManage || rsp.EnableShowJoined {
			myInfo.FansClubManageUrl = fmt.Sprintf("%sh5/appH5/fanClub", s.conf.H5Domain)
		}
		return nil
	})

	eg.Go(func() error {
		//rpc获取钱包信息
		wallet, e := s.dao.GetWallet(ctx, userId)
		if e == nil {
			myInfo.Diamonds = wallet.Diamonds
			myInfo.StarVotes = wallet.StarVotes
			myInfo.AccountStatus = model.AccountNormal
			if wallet.Status == model.AcNormal {
				if wallet.EndTime > time.Now().Format(model.TimeLayout) {
					myInfo.AccountStatus = model.AccountFreeze
				}
			} else if wallet.Status == model.AcForever {
				myInfo.AccountStatus = model.AcFreezeForever
			}
		}
		return nil
	})

	eg.Go(func() error {
		myInfo.Visitors, _ = s.dao.GetUserVisitors(ctx, noId)
		return nil
	})

	err := eg.Wait()
	if err != nil {
		return nil, err
	}

	go s.updateUserLocation(userId, ip)

	rT, _ := primitive.ObjectIDFromHex(userId)
	myInfo.RegisterTime = rT.Timestamp().Unix()

	return myInfo, nil
}

func copyMyInfo(m *model.MyInfo, data *api.UserInfo) {
	m.NoId = data.NoId
	m.NickName = data.NickName
	m.AvatarUrl = data.AvatarUrl
	m.CheckAvatar = data.CheckAvatar
	m.AvatarStatus = data.AvatarStatus
	m.RealStatus = data.RealStatus
	m.CharmLevel = int32(utils.GetCharmLv(data.Charm))
	m.RealNameStatus = data.RealNameStatus
	m.Wealth = data.Wealth
	m.WealthLevel = int32(utils.GetWealthLv(m.Wealth))
	m.PreWealth = int32(utils.WealthExp[m.WealthLevel])
	m.IsBindPhone = data.Mobile != ""
	if int(m.WealthLevel) >= len(utils.WealthExp)-1 {
		m.NextWealth = int32(data.Wealth)
	} else {
		m.NextWealth = int32(utils.WealthExp[m.WealthLevel+1])
	}
	m.Gender = data.Gender
	m.Age = utils.GetAgeByDate(data.Born)
}

func (s *Svc) updateUserLocation(userId, ip string) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	lock := s.dao.SetLock(ctx, fmt.Sprintf(model.RedisUserLocationGap, userId), 300)
	if !lock {
		return
	}
	loc, err := s.dao.GetLocationByIP(ctx, ip)
	if err != nil {
		logger.LogErrorw("GetLocationByIP", ip, err.Error())
		return
	}

	_, err = s.dao.UpdateUserInfo(ctx, userId, bson.M{"province": formatProvince(loc.Province)})
	if err != nil {
		logger.LogErrorw("UpdateUserInfo", ip, err.Error())
	}
}

func formatProvince(s string) string {
	return strings.TrimRight(strings.TrimRight(s, "省"), "市")
}

// GetUser 获取个人信息
func (s *Svc) GetUser(ctx context.Context, noId, dstUser, scene, userId, ip string) (*model.TUserInfo, error) {
	//获取自己的信息
	var (
		userInfo    *model.TUserInfo
		err         error
		eg          errgroup.Group
		action      *model.TUserAction
		decorate    *model.Decorate
		anchorRsp   *livepb.IsAnchorRsp
		friend      *chatpb.RelationNumRsq
		wallInfo    *model.AnchorWallInfo
		atlasInfo   *model.Atlas
		link        string
		interestTag []*model.InterestTagAddedVO
	)

	eg.Go(func() error {
		decorate, err = s.dao.GetUserAllDec(ctx, dstUser, scene)
		return err
	})
	eg.Go(func() error {
		if noId == dstUser {
			u, e := s.dao.GetUserInfoById(ctx, noId, nil)
			if e != nil {
				return e
			}
			userInfo = model.ProtoToUser(u, true)
			//更新个人信息-省
			go s.updateUserLocation(userId, ip)
		} else {
			userInfo, err = s.getOtherUser(ctx, noId, dstUser, scene)
			if err != nil {
				return err
			}
		}
		tSwitch, _ := s.dao.SwitchGet(ctx, model.IPLocation)
		if tSwitch == nil || !tSwitch.Status {
			userInfo.Province = ""
		}
		return nil
	})

	eg.Go(func() error {
		friend, err = s.dao.GetFriends(ctx, dstUser)
		if err != nil {
			return err
		}

		link, _ = s.dao.GetLiveCacheStr(ctx, fmt.Sprintf(model.RedisMultiChatUserRoom, dstUser))
		action, err = s.dao.GetUserAction(ctx, noId, dstUser)
		return err
	})
	eg.Go(func() error { // 查询用户主播身份
		if anchorRsp, err = s.dao.IsAnchor(context.Background(), dstUser); err != nil {
			return err
		}
		if anchorRsp.Ret { // 查询主播礼物墙信息
			if wallInfo, err = s.dao.GetGiftAnchorWall(context.Background(), dstUser); err != nil {
				return err
			}
		}

		return nil
	})
	eg.Go(func() error {
		badgeRsp, errG := s.dao.GetGiftWallUserBadge(ctx, dstUser)
		if errG != nil {
			return errG
		}
		if badgeRsp.Badge != nil && badgeRsp.Badge.BadgeId > 0 {
			atlasInfo = &model.Atlas{
				BadgeId:  badgeRsp.Badge.BadgeId,
				ExpireAt: badgeRsp.Badge.ExpireAt,
			}
			atlasInfo.Rank = s.dao.GetAtlasRank(ctx, dstUser)
		}

		return nil
	})
	eg.Go(func() error {
		if interestTag, err = s.dao.GetInterestTagAdded(ctx, dstUser); err != nil {
			return err
		}
		return nil
	})

	if err = eg.Wait(); err != nil {
		return nil, err
	}

	if action.Remark != "" {
		userInfo.Remark = action.Remark
	}
	userInfo.Decorate = decorate
	userInfo.WallInfo = wallInfo
	userInfo.AtlasInfo = atlasInfo
	userInfo.IsLike = action.ThumbsUp
	userInfo.ContactStatus = model.UserValid
	userInfo.IsLink = link != ""
	userInfo.Monitor = link
	userInfo.Days = int(time.Since(userInfo.Id.Timestamp()) / (time.Hour * 24))
	userInfo.InterestTag = interestTag

	fillUserInfo(userInfo, anchorRsp, friend)
	s.fillPayJumpUrl(userInfo)
	return userInfo, nil
}

func (s *Svc) fillPayJumpUrl(user *model.TUserInfo) {
	if s.conf.PayJump.Full == "" && s.conf.PayJump.Half == "" && len(s.conf.PayJump.Domains) == 0 {
		return
	}
	if len(s.conf.PayJump.Domains) == 1 {
		user.PayJumpUrlFull = s.conf.PayJump.Domains[0].Domain + s.conf.PayJump.Full
		user.PayJumpUrlHalf = s.conf.PayJump.Domains[0].Domain + s.conf.PayJump.Half
		return
	}

	var (
		sectionTotal int64
		curr         int64
		sections     = make([]*model.Section, 0)
	)
	for i := 0; i < len(s.conf.PayJump.Domains); i++ {
		sectionTotal += s.conf.PayJump.Domains[i].Probability

		sections = append(sections, &model.Section{
			Start: curr,
			End:   curr + s.conf.PayJump.Domains[i].Probability,
		})
		curr += s.conf.PayJump.Domains[i].Probability
	}
	randNum := utils.RangeRandom(0, sectionTotal)

	for j := 0; j < len(sections); j++ {
		if randNum >= sections[j].Start && randNum < sections[j].End {
			user.PayJumpUrlFull = s.conf.PayJump.Domains[j].Domain + s.conf.PayJump.Full
			user.PayJumpUrlHalf = s.conf.PayJump.Domains[j].Domain + s.conf.PayJump.Half
			return
		}
	}
}

func fillUserInfo(user *model.TUserInfo, anchor *livepb.IsAnchorRsp, friend *chatpb.RelationNumRsq) {
	user.IsAnchor = anchor.Ret
	user.IsShowing = anchor.IsShowing
	user.LiveType = anchor.LiveType
	user.IsShowing = anchor.IsShowing
	user.Focus = friend.AttentionNum
	user.Fans = friend.FansNum
	user.Days = int(time.Since(user.Id.Timestamp()) / (time.Hour * 24))
}

// 查看其他人主页，返回距离，点赞和是否心动状态
func (s *Svc) getOtherUser(ctx context.Context, noId, dstUser, scene string) (*model.TUserInfo, error) {
	users, err := s.dao.GetUsers(ctx, []string{noId, dstUser})
	if err != nil {
		return nil, err
	}

	var city string
	var lon, lat float64
	var fromMsgId, toMsgId string
	var dst *model.TUserInfo
	for _, u := range users {
		if u.NoId == dstUser {
			fromMsgId = u.MsgId
			dst = model.ProtoToUser(u, false)
		} else {
			toMsgId = u.MsgId
			city = u.City
			lon, lat = u.Lon, u.Lat
		}
	}

	if dst == nil {
		return nil, model.NothingFound
	}
	if dst.City != "" && dst.City == city {
		//计算距离显示
		dst.Distance = utils.FormatDistance(utils.GetDistance(lat, lon, dst.Lat, dst.Lon))
	}

	status, err := s.dao.GetMovedStatus(ctx, noId, dstUser)
	if err != nil {
		return nil, err
	}
	dst.ContactStatus = model.UserValid
	dst.FollowStatus = model.FocusMap[status.Relation]
	dst.BlackStatus = int(status.IsBlack)

	status, err = s.dao.GetMovedStatus(ctx, dstUser, noId)
	if err != nil {
		return nil, err
	}
	if status.IsBlack == 1 {
		dst.BlackStatus += 2
	}

	if scene == model.ChatScene {
		go s.dao.InviteVerifyMsg(noId, dstUser, fromMsgId, toMsgId, dst)
	}

	return dst, nil
}

func formatTimeLoc(start, end string) ([]time.Time, error) {
	startDate, err := time.ParseInLocation(model.TimeLayout, start, time.Local)
	if err != nil {
		return nil, fmt.Errorf("wrong forbidUpdate startTime:%s", start)
	}

	endDate, err := time.ParseInLocation(model.TimeLayout, end, time.Local)
	if err != nil {
		return nil, fmt.Errorf("wrong forbidUpdate endTime:%s", end)
	}

	return []time.Time{startDate, endDate}, nil
}

func (s *Svc) validTime() error {
	now := time.Now()
	for _, t := range s.conf.ForbidUpdate {
		ts, err := formatTimeLoc(t.StartTime, t.EndTime)
		if err != nil {
			return err
		}

		//时间段内
		if now.Sub(ts[0]) > 0 && now.Sub(ts[1]) < 0 {
			return model.UserForbidUpdate
		}
	}
	return nil
}

// UpdateUserInfo 更新用户基本信息
func (s *Svc) UpdateUserInfo(ctx context.Context, userId, noId, msgId string, updateInfo *model.ReqUpdateUser) (*model.TUserInfo, error) {
	if updateInfo.NickName != "" || updateInfo.Intro != "" {
		if e := s.validTime(); e != nil {
			return nil, e
		}
		if err := s.ReviewMsg(ctx, updateInfo.NickName+updateInfo.Intro, msgId, updateInfo.NickName != ""); err != nil {
			return nil, err
		}
	}

	//修改生日，计算年龄和星座
	if updateInfo.Born != "" {
		bd, err := time.Parse(model.DateLayout, updateInfo.Born)
		if err != nil {
			return nil, err
		}
		updateInfo.StarSign = utils.GetStarSign(bd)
	}

	var passMode, realUrl, oldAvatar string
	var priority model.Priority
	var eg errgroup.Group
	isReview := false
	if updateInfo.AvatarUrl != "" {
		if e := s.validTime(); e != nil {
			return nil, e
		}
		isReview = true
		//如果真人审核中，不允许更改头像
		u, err := s.dao.GetUserInfoById(ctx, noId, []string{"realStatus", "avatarUrl"})
		if err != nil {
			return nil, err
		}
		oldAvatar = u.AvatarUrl
		if u.RealStatus == model.Process {
			return nil, model.NeedRealStatus
		} else if u.RealStatus == model.Success {
			//校验头像和真人图片
			realUrl, err = s.VerifyAvatar(ctx, s.conf.BaseImgUrl, noId, updateInfo.AvatarUrl)
			if err != nil {
				return nil, model.FaceNotSame
			}
		}

		smImgUrl := s.conf.BaseImgUrl + updateInfo.AvatarUrl
		rule, e := s.getRule(ctx, model.Avatar)
		if e != nil {
			return nil, e
		}

		switch rule.CheckMode & model.RuleAll {
		case model.RuleForbid:
			return nil, model.ForbidUploadImg
		case model.RuleManual:
			updateInfo.AvatarStatus = model.Process
			updateInfo.CheckAvatar = updateInfo.AvatarUrl
			updateInfo.AvatarUrl = ""
			priority = model.HighPriority
		case model.RuleSM:
			fallthrough
		case model.RuleAll:
			smRsp, err := s.checkImg(ctx, smImgUrl, msgId)
			if err != nil {
				return nil, err
			}
			priority, passMode = getPassMode(rule, smRsp.RiskLevel)
			updateInfo.AvatarStatus = getLocStatus(passMode)
			updateInfo.CheckAvatar = updateInfo.AvatarUrl
			if updateInfo.AvatarStatus == model.Reject {
				updateInfo.AvatarStatus = ""
				updateInfo.AvatarUrl = ""
			} else if updateInfo.AvatarStatus == model.Process {
				//审核中
				updateInfo.AvatarUrl = ""
			}
			updateInfo.SMResult = smRsp.RiskLevel
			updateInfo.SMReason = smRsp.Detail.Description
		}
	}
	if updateInfo.AudioUrl != "" {
		if e := s.validTime(); e != nil {
			return nil, e
		}
		rule, e := s.getRule(ctx, model.Audio)
		if e != nil {
			return nil, e
		}
		if rule.CheckMode == model.RuleForbid {
			return nil, model.ForbidUploadAudio
		}
		updateInfo.AudioStatus = model.Process
	}

	if passMode == model.SMReject {
		return nil, model.InvalidAvatar
	}

	user, err := s.dao.UpdateUserInfo(ctx, userId, updateInfo)
	if err != nil {
		return nil, err
	}
	score := getInfoScore(user)
	_ = utils.JsonCopy(updateInfo, user)
	newScore := getInfoScore(user)

	eg.Go(func() error {
		return s.esUpdate(ctx, userId, user, score, newScore)
	})
	if updateInfo.AudioUrl != "" {
		eg.Go(func() error {
			record := getCheckAudio(userId, noId, user.NickName, msgId, updateInfo)
			return s.dao.CheckUserMedia(model.MQSMUserAudioEvent, utils.JsonByte(record))
		})
	}
	if isReview {
		//发送后台审核
		eg.Go(func() error {
			record := getCheckAvatar(userId, realUrl, oldAvatar, user, priority, updateInfo)
			return s.dao.CheckUserMedia(model.MQSMAvatarEvent, utils.JsonByte(record))
		})
	}
	eg.Go(func() error {
		decorate, e := s.dao.GetUserAllDec(ctx, noId, "")
		user.Decorate = decorate
		return e
	})
	if err = eg.Wait(); err != nil {
		return nil, err
	}

	return user, nil
}

func (s *Svc) esUpdate(ctx context.Context, userId string, info *model.TUserInfo, rawScore, score int) error {
	//更新es
	data := &model.UserES{
		Id:       userId,
		NoId:     info.NoId,
		NickName: info.NickName,
		Born:     info.Born,
		Info:     rawScore,
		NewScore: score,
	}
	err := s.dao.SendEsUpdateQ(ctx, &model.UpdateES{Type: model.EsInfo, Data: data})

	return err
}

func getInfoScore(u interface{}) int {
	v := reflect.ValueOf(u)
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}
	total := 0

	job := v.FieldByName("Job")
	if job.Kind() != reflect.Invalid && !job.IsZero() {
		total += model.JobStatus
	}
	income := v.FieldByName("Income")
	if income.Kind() != reflect.Invalid && !income.IsZero() {
		total += model.IncomeStatus
	}
	starSign := v.FieldByName("StarSign")
	if starSign.Kind() != reflect.Invalid && !starSign.IsZero() {
		total += model.StarSignStatus
	}
	height := v.FieldByName("Height")
	if height.Kind() != reflect.Invalid && !height.IsZero() {
		total += model.HeightStatus
	}
	intro := v.FieldByName("Intro")
	if intro.Kind() != reflect.Invalid && !intro.IsZero() {
		total += model.IntroStatus
	}

	return total
}

// UpdateAlbum 相册修改
func (s *Svc) UpdateAlbum(ctx context.Context, userId, noId, msgId string, imgs []string) ([]*model.UserImg, error) {
	if e := s.validTime(); e != nil {
		return nil, e
	}
	//获取相册原始信息
	rawIMG, err := s.dao.GetAlbum(ctx, userId)
	if err != nil {
		return nil, err
	}

	m := make(map[string]model.UserImg)
	for i, img := range rawIMG {
		if img.Status == model.Reject {
			continue
		}
		m[img.ImgUrl] = rawIMG[i]
	}

	data := make([]*model.UserImg, 0)
	checkImgs := make([]string, 0)
	imgIndex := make(map[string]int)
	for i, img := range imgs {
		imgIndex[img] = i
		if raw, ok := m[img]; ok {
			//已提交
			data = append(data, &raw)
		} else {
			//新增
			checkImgs = append(checkImgs, img)
			data = append(data, &model.UserImg{ImgUrl: img})
		}
	}

	user, err := s.dao.GetUserInfoById(ctx, noId, []string{"nickName"})
	if err != nil {
		return nil, err
	}

	var passMode string
	//需要后台审核的数据
	checkData := make([]*model.TReviewAlbum, 0)
	if len(checkImgs) > 0 {
		rule, e := s.getRule(ctx, model.Album)
		if e != nil {
			return nil, err
		}

		switch rule.CheckMode & model.RuleAll {
		case model.RuleForbid:
			return nil, model.ForbidUploadAlbum
		case model.RuleManual:
			//1不过数美，发后台人审
			for _, imgUrl := range checkImgs {
				data[imgIndex[imgUrl]].Status = model.Process
				checkData = append(checkData, getCheckImg(userId, noId, user.NickName, model.NonePriority, data[imgIndex[imgUrl]]))
			}
		case model.RuleSM:
			//2过数美，reject部分提示，不人审， pass部分不发后台，review部分发后台
			fallthrough
		case model.RuleAll:
			var eg errgroup.Group
			ch := make(chan *model.UserImg, len(checkImgs))
			for _, img := range checkImgs {
				_img := img
				eg.Go(func() error {
					return s.checkAlbum(ctx, _img, msgId, ch)
				})
			}
			e = eg.Wait()

			close(ch)
			if e != nil {
				return nil, e
			}

			for result := range ch {
				p, mode := getPassMode(rule, result.SMResult)
				result.Status = getLocStatus(mode)
				data[imgIndex[result.ImgUrl]] = result
				checkData = append(checkData, getCheckImg(userId, noId, user.NickName, p, result))
				if mode == model.SMReject {
					passMode = model.SMReject
				}
			}
		}
	}

	if len(checkData) > 0 {
		//mq里保存到mongo，redis
		err = s.dao.CheckUserMedia(model.MQSMAlbumEvent, utils.JsonByte(checkData))
		if err != nil {
			return nil, err
		}
	}

	if len(rawIMG) != len(data) {
		err = s.dao.SendEsUpdateQ(ctx, &model.UpdateES{Type: model.EsAlbum, Data: &model.UserES{Id: userId, Album: len(data)}})
		if err != nil {
			return nil, err
		}
	}

	err = s.dao.UpdateAlbum(ctx, userId, data)
	if err != nil {
		return nil, err
	}

	if passMode == model.SMReject {
		return nil, model.InvalidAlbum
	}

	return data, nil
}

func getCheckAudio(userId, noId, nickName, msgId string, info *model.ReqUpdateUser) *model.TReviewAudio {
	return &model.TReviewAudio{
		UserId:     userId,
		NoId:       noId,
		AudioUrl:   info.AudioUrl,
		NickName:   nickName,
		Status:     model.Process,
		Duration:   info.Duration,
		ReqTime:    time.Now().Unix(),
		UpdateTime: time.Now().Unix(),
		MsgId:      msgId,
	}
}

func getCheckAvatar(userId, realUrl, oldAvatar string, user *model.TUserInfo, p model.Priority, info *model.ReqUpdateUser) *model.TReviewAvatar {
	return &model.TReviewAvatar{
		UserId:       userId,
		NoId:         user.NoId,
		AvatarUrl:    info.CheckAvatar,
		OldAvatar:    oldAvatar,
		SMResult:     info.SMResult,
		SMReason:     info.SMReason,
		NickName:     user.NickName,
		AvatarStatus: model.Process,
		RealStatus:   user.RealStatus,
		RealUrl:      realUrl,
		ReqTime:      time.Now().Unix(),
		UpdateTime:   time.Now().Unix(),
		Priority:     p,
	}
}

// 保存相册审核记录
func getCheckImg(userId, noId, nickName string, p model.Priority, info *model.UserImg) *model.TReviewAlbum {
	return &model.TReviewAlbum{
		UserId:     userId,
		NoId:       noId,
		NickName:   nickName,
		ImgUrl:     info.ImgUrl,
		SMReason:   info.SMReason,
		SMResult:   info.SMResult,
		Status:     model.Process,
		ReqTime:    time.Now().Unix(),
		UpdateTime: time.Now().Unix(),
		Priority:   p,
	}
}

// 数美审核相册图片
func (s *Svc) checkAlbum(ctx context.Context, imgUrl, msgId string, ch chan *model.UserImg) error {
	smRsp, err := s.checkImg(ctx, s.conf.BaseImgUrl+imgUrl, msgId)
	if err != nil {
		return err
	}
	res := model.UserImg{
		ImgUrl:   imgUrl,
		SMResult: smRsp.RiskLevel,
		SMReason: smRsp.Detail.Description,
	}

	ch <- &res
	return nil
}

// GetUserRealStatus 获取真人认证状态
// return 认证状态
func (s *Svc) GetUserRealStatus(ctx context.Context, noId string) (string, error) {
	userInfo, err := s.dao.GetUserInfoById(ctx, noId, []string{"realStatus"})
	if err != nil {
		return "", err
	}

	if userInfo.RealStatus == "" {
		userInfo.RealStatus = model.RealInit
	}
	return userInfo.RealStatus, nil
}

// GetCompleteStatus 获取资料完善状态
func (s *Svc) GetCompleteStatus(ctx context.Context, noId string) (string, error) {
	userInfo, err := s.dao.GetUserInfoById(ctx, noId, nil)
	if err != nil {
		return "", err
	}

	if len(userInfo.Album) == 0 {
		return model.UnFinished, nil
	}

	if isUserComplete(model.ProtoToUser(userInfo, true)) {
		return model.Finished, nil
	}

	return model.UnFinished, nil
}

func isUserComplete(user *model.TUserInfo) bool {
	t := reflect.TypeOf(user)
	v := reflect.ValueOf(user)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
		v = v.Elem()
	}
	for i := 0; i < t.NumField(); i++ {
		tag := t.Field(i).Tag.Get("check")
		if tag != "" && v.Field(i).IsZero() {
			return false
		}
	}
	return true
}

// RemarkUsername 设置备注名
func (s *Svc) RemarkUsername(ctx context.Context, userId string, req *model.ReqUserRemark) error {
	ur := &model.TUserAction{
		UserId:  userId,
		DstUser: req.DstUser,
		Remark:  req.Remark,
	}

	return s.dao.SetUserAction(ctx, ur)
}

// ThumbsUpUser 用户点赞
func (s *Svc) ThumbsUpUser(ctx context.Context, userId, dstUser string) error {

	action, err := s.dao.GetUserAction(ctx, userId, dstUser)
	if err != nil {
		return err
	}
	if action.ThumbsUp {
		return model.ThumbsUpAlready
	}

	thumbsUp := &model.TUserAction{
		UserId:   userId,
		DstUser:  dstUser,
		ThumbsUp: true,
	}

	return s.dao.SetUserAction(ctx, thumbsUp)
}

func (s *Svc) User_T1(data []byte) error {
	var msg model.TUser
	if err := json.Unmarshal(data, &msg); err != nil {
		logger.LogWarnw("unmarshal error", "msg", string(data), "err", err)
		return err
	}

	fmt.Println("user_t1", msg.UserId)

	return nil
}

func (s *Svc) User_T2(data []byte) error {
	var msg model.TUser
	if err := json.Unmarshal(data, &msg); err != nil {
		logger.LogWarnw("unmarshal error", "msg", string(data), "err", err)
		return err
	}

	fmt.Println("user_t2", msg.UserId)

	return nil
}

func (s *Svc) TUser(ctx context.Context) error {
	return s.dao.TUser(ctx)
}

func (s *Svc) LonLat(ctx context.Context, lonLat *model.LonLatReq, device *model.Device, userId, noId string) error {
	param := &model.UserES{
		Id: userId,
		Location: &elastic.GeoPoint{
			Lat: lonLat.Lat,
			Lon: lonLat.Lon,
		},
		Online:     model.Online,
		City:       lonLat.City,
		Device:     device,
		LonLatType: lonLat.LonLatType,
	}
	if lonLat.Lon == 0 && lonLat.Lat == 0 {
		user, err := s.dao.GetUserInfoById(ctx, noId, []string{"lon", "lat"})
		if err != nil {
			return err
		}
		param.Location.Lon = user.Lon
		param.Location.Lat = user.Lat
	}

	err := s.dao.SendEsUpdateQ(ctx, &model.UpdateES{Type: model.EsLocation, Data: param})
	if err != nil {
		return err
	}

	if err := s.dao.UpdateLonLat(ctx, lonLat, userId); err != nil {
		return err
	}
	return nil
}

// GetAudioHint 获取随机语音签名提示语
func (s *Svc) GetAudioHint(ctx context.Context, sort float64) (*model.TAudioHint, error) {
	return s.dao.GetAudioHint(ctx, sort)
}

func (s *Svc) GetFeedbackType(ctx context.Context, category string) ([]*model.TFeedbackType, error) {
	return s.dao.GetFeedbackList(ctx, category)
}

func (s *Svc) UserFeedBack(ctx context.Context, noId string, req *model.ReqFeedBack) error {
	var (
		eg       errgroup.Group
		fa       *chatpb.Family
		fb       *model.TUserFeedBack
		from, to *api.UserInfo
	)

	eg.Go(func() error {
		rsp, err := s.dao.GetUsersByIds(ctx, &api.UserIdsReq{
			Ids:  []string{noId},
			Cols: []string{"noId", "nickName", "mobile"},
		})
		if err != nil {
			return err
		}
		if len(rsp.Users) == 0 {
			return model.ErrorArgument
		}
		from = rsp.Users[0]
		return err
	})

	eg.Go(func() error {
		if req.Category != model.FBFamilyReport {
			if len(req.NoId) > 0 {
				rsp, err := s.dao.GetUsersByIds(ctx, &api.UserIdsReq{
					Ids:  []string{req.NoId},
					Cols: []string{"noId", "nickName", "mobile"},
				})
				if err != nil {
					return err
				}
				if len(rsp.Users) == 0 {
					return model.ErrorArgument
				}
				to = rsp.Users[0]
			}
			return nil
		}
		rsp, err := s.dao.FamilyQuery(ctx, "", []string{req.NoId})
		if err != nil {
			return err
		}
		if len(rsp.Items) == 0 {
			return model.ErrorArgument
		}
		fa = rsp.Items[0]
		return err
	})

	if err := eg.Wait(); err != nil {
		return err
	}

	contact := req.Contact
	if contact == "" {
		contact = from.Mobile
	}
	fb = &model.TUserFeedBack{
		Category:     req.Category,
		ImgList:      req.ImgList,
		FromNick:     from.NickName,
		FromUserId:   from.Id,
		FromNoId:     from.NoId,
		PostTime:     time.Now().Format(model.TimeLayout),
		FeedBackType: req.FeedBackType,
		Content:      req.Content,
		Contact:      contact,
	}
	if req.Category != model.FBFamilyReport {
		if to != nil {
			fb.ToUserId = to.Id
			fb.ToNick = to.NickName
			fb.ToNoId = to.NoId
		}
	} else {
		fb.ToUserId = fa.Id
		fb.ToNick = fa.Name
		fb.ToNoId = fa.Id
	}

	_ = s.dao.InsertSecretMsg(ctx, fb.FromNoId, "您好，您的举报已收到，我们将尽快处理，感谢您的举报")
	s.dao.MakePullSecret(ctx, fb.FromNoId, "举报消息")

	return s.dao.SaveFeedBack(fb)
}

// LogoffApply 申请注销
func (s *Svc) LogoffApply(ctx context.Context, noId string) error {
	//1.校验7天内是否申请过注销
	isLogoff, err := s.dao.IsRepeatLogoff(ctx, noId)
	if err != nil {
		return err
	}

	if isLogoff {
		//提示7天内仅申请一次
		return model.LogoffReApply
	}

	var eg errgroup.Group
	eg.Go(func() error {
		user, e := s.dao.QueryUserById(ctx, &model.ReqQueryUser{NoId: noId})
		if e != nil {
			return e
		}
		if user.Mode != nil && user.Mode.Apple != nil {
			//2.iOS注销策略
			iosTokenRevoke(noId, user.Mode.Apple.Token, user.Mode.Apple.Secret)
		}
		return nil
	})
	eg.Go(func() error {
		//3.保存注销记录
		return s.dao.RecordLogoff(ctx, noId)
	})

	return eg.Wait()
}

func iosTokenRevoke(noId, token, clientSecret string) {
	body := map[string]string{
		"client_id":       apple.APPLICATION_CLIENT_ID,
		"client_secret":   clientSecret,
		"token":           token,
		"token_type_hint": "access_token",
	}

	result := make(map[string]string)
	_, err := resty.New().R().SetHeader("Content-Type", "application/x-www-form-urlencoded").SetFormData(body).SetResult(result).Post(apple.REVOKE_TOKEN_URL)
	logger.LogInfow("RevokeIOSToken", noId, err, "res", result)
}

func (s *Svc) ValidLogoff(ctx context.Context, noId string, req *model.ReqValidLogoff) (*model.RspValidLogoff, error) {
	user, err := s.dao.GetUserInfoById(ctx, noId, []string{"noId", "mobile"})
	if err != nil {
		return nil, err
	}
	if s.conf.SmsSwitch {
		v, err := s.dao.CheckCode(ctx, user.Mobile, req.Code, "")
		if err != nil && err != ecode.NothingFound {
			return nil, err
		}
		if !v {
			return nil, model.UserSmsCheckError
		}
	}

	var (
		eg  errgroup.Group
		rsp = new(model.RspValidLogoff)
	)
	rsp.List = make([]*model.LogoffCheck, 3)

	eg.Go(func() error {
		rsp.List[0] = &model.LogoffCheck{
			Check:   s.dao.LatestLoginCount(ctx, noId, 7) < 2,
			Title:   model.AccountTitle,
			Content: model.AccountContent}
		return nil
	})
	eg.Go(func() error {
		rsp.List[1] = &model.LogoffCheck{Title: model.BalanceTitle, Content: model.BalanceContent}
		rsp.List[1].Check, _ = s.dao.CheckUserAccount(ctx, user.Id)
		return nil
	})
	rsp.List[2] = &model.LogoffCheck{
		Check:   true,
		Title:   model.LawTitle,
		Content: model.LawContent,
	}

	if err = eg.Wait(); err != nil {
		return nil, err
	}
	rsp.Valid = rsp.List[0].Check && rsp.List[1].Check

	return rsp, nil
}

// LogoffStatus 查询是否正在申请注销
func (s *Svc) LogoffStatus(ctx context.Context, noId string) (bool, error) {
	return s.dao.IsApplyLogoff(ctx, noId)
}

// CancelLogoff 取消注销
func (s *Svc) CancelLogoff(ctx context.Context, noId string) error {
	isApply, err := s.dao.IsApplyLogoff(ctx, noId)

	if err != nil {
		return err
	}

	if !isApply {
		return model.LogoffNoneErr
	}

	return s.dao.CancelLogoff(ctx, noId)
}

func (s *Svc) ReviewMsg(ctx context.Context, msg, msgId string, isNick bool) error {
	msgRsp := new(shumei.MsgResponse)
	data := shumei.MsgRequest{Data: &shumei.MsgData{
		Text:    msg,
		TokenId: msgId,
	}}
	if isNick {
		data.Data.Nickname = msg
	}
	err := s.dao.Msg(ctx, data, msgRsp)

	if err != nil || msgRsp.Pass() != nil {
		return model.InvalidText
	}

	return nil
}

func (s *Svc) QueryUserGuest(ctx context.Context, req *model.ReqQueryUser) (*model.RspGuestUser, error) {
	user, err := s.dao.QueryUserById(ctx, req)
	if err != nil {
		return nil, err
	}

	rsp := &model.RspGuestUser{
		UserId:    user.Id.Hex(),
		NoId:      user.NoId,
		NickName:  user.NickName,
		AvatarUrl: s.conf.BaseImgUrl + user.AvatarUrl,
	}

	return rsp, nil
}

func (s *Svc) GetUserQueryList(ctx context.Context, noIds string) ([]*model.RspGuestUser, error) {
	ids := strings.Split(noIds, ",")

	index := make(map[string]int)
	for i, noId := range ids {
		index[noId] = i
	}

	users, err := s.dao.GetUsers(ctx, ids)
	if err != nil {
		return nil, err
	}

	sort.Slice(users, func(i, j int) bool {
		return index[users[i].NoId] < index[users[j].NoId]
	})

	res := make([]*model.RspGuestUser, 0)
	for _, user := range users {
		res = append(res, &model.RspGuestUser{
			UserId:    user.Id,
			NoId:      user.NoId,
			NickName:  user.NickName,
			AvatarUrl: s.conf.BaseImgUrl + user.AvatarUrl,
		})
	}

	return res, nil
}

func (s *Svc) GetWebLogin(ctx context.Context) string {
	return s.conf.WebLogin
}

func (s *Svc) GetSuperAdmin(ctx context.Context, noId string) (bool, string) {
	user, err := s.dao.GetUsersByIds(ctx, &userpb.UserIdsReq{
		Ids:  []string{noId},
		Cols: []string{"noId", "superAdmin"},
	})

	if err != nil {
		return false, ""
	}

	if len(user.Users) == 0 {
		return false, ""
	}

	if user.Users[0].GetSuperAdmin() == "" {
		return false, ""
	}

	return true, user.Users[0].GetSuperAdmin()
}

func (s *Svc) CheckFriendInfo(ctx context.Context, noId string) (bool, error) {
	info, err := s.dao.GetUserInfoById(ctx, noId, nil)
	if err != nil {
		return false, err
	}

	if info.AvatarUrl == "" || info.Height == 0 || info.Weight == 0 || info.Born == "" || info.StarSign == "" {
		return false, nil
	}

	interests, err := s.dao.GetInterestTagAdded(ctx, noId)
	if err != nil {
		return false, err
	}
	if len(interests) == 0 {
		return false, nil
	}

	return true, nil
}
