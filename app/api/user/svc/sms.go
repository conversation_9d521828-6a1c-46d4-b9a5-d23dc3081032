package svc

import (
	"context"

	"creativematrix.com/beyondreading/app/api/user/model"
)

func (s *Svc) SmsCodeWhite(ctx context.Context, mobile string) (bool, error) {
	count, err := s.dao.SendCodeWhite(ctx, mobile)
	if err != nil {
		return false, nil
	}

	if count > 0 {
		return true, nil
	}

	return false, nil
}

func (s *Svc) SmsCode(ctx context.Context, scr *model.SmsCodeReq) (bool, error) {
	if !s.conf.SmsSwitch {
		return true, nil
	}

	count, err := s.dao.SendCodeCount(ctx, scr.Mobile, "")
	if err != nil {
		return false, nil
	}

	if count > 9 {
		return false, model.UserSmsMobileMoreError
	}

	code, err := s.dao.SendCode253(ctx, scr.Mobile, scr.Format)
	if err != nil {
		return false, err
	}

	var sc model.TSmsCode
	sc.Mobile = scr.Mobile
	sc.Code = code

	err = s.dao.InsertCode(ctx, &sc)
	if err != nil {
		return false, err
	}

	return true, nil
}

func (s *Svc) LogoffCode(ctx context.Context, noId string) (bool, error) {
	user, err := s.dao.GetUserInfoById(ctx, noId, []string{"mobile"})
	if err != nil {
		return false, err
	}

	return s.SmsCode(ctx, &model.SmsCodeReq{Mobile: user.Mobile, Format: model.SMSCodeFormat})
}
