package svc

import (
	"context"
	"testing"

	"creativematrix.com/beyondreading/app/api/user/model"
	userpb "creativematrix.com/beyondreading/app/base/user/api"
	"creativematrix.com/beyondreading/pkg/utils"
	"gotest.tools/assert"
)

func TestMobile(t *testing.T) {
	ctx := context.Background()

	t.Run("", func(t *testing.T) {
		data, err := svc.CheckMobile(ctx, "7778909", "1761111111")
		assert.NilError(t, err)
		t.Log(string(utils.JsonByte(data)))
	})

	t.Run("", func(t *testing.T) {
		data, err := svc.GetModifyMobileInfo(ctx, "7778909")
		assert.NilError(t, err)
		t.Log(string(utils.JsonByte(data)))
	})

	t.Run("", func(t *testing.T) {
		err := svc.ApplyModifyMobile(ctx, &model.ApplyModifyMobileReq{
			NoId:        "7778911",
			FromMobile:  "17612121212",
			ToMobile:    "17622222222",
			ApplyReason: "1111",
			ProveImg:    []string{"1.jpg", "2.jpg"},
			ModifyType:  userpb.ModifyType_UserSelf.String(),
		})
		assert.NilError(t, err)
	})
}
