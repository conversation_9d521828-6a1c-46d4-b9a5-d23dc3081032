package svc

import (
	"context"
	"math/rand"
	"sync"
	"time"

	"creativematrix.com/beyondreading/app/api/user/conf"
	"creativematrix.com/beyondreading/app/api/user/dao"
	"creativematrix.com/beyondreading/app/api/user/third/weixin"
)

type Svc struct {
	conf     *conf.Config
	dao      *dao.Dao
	wxPublic *weixin.PublicWX
	avatars  sync.Map
}

func Load(c *conf.Config) *Svc {
	svc := &Svc{
		conf:     c,
		dao:      dao.Load(c),
		wxPublic: weixin.NewWXPublic(c.WeiXin.AppID, c.WeiXin.AppSecret),
	}

	svc.wxPublic.Init()
	go svc.initAvatars()
	return svc
}

func (s *Svc) initAvatars() {
	s.loadAvatar()
	ticker := time.NewTicker(time.Minute * 5)
	for range ticker.C {
		s.loadAvatar()
	}
}

func (s *Svc) loadAvatar() {
	data := s.dao.GetAvatars()
	for k, v := range data {
		s.avatars.Store(k, v)
	}
}

func (s *Svc) GetAvatars(gender string) string {
	load, ok := s.avatars.Load(gender)
	if ok {
		if data, y := load.([]string); y {
			i := rand.Intn(len(data))
			return data[i]
		}
	}
	return ""
}

func (s *Svc) Ping(ctx context.Context) error {
	return s.dao.Ping(ctx)
}

func (s *Svc) Close() {
	s.dao.Close()
}
