package main

import (
	"creativematrix.com/beyondreading/pkg/ecode"
	"creativematrix.com/beyondreading/pkg/middlewares"
	"math/rand"
	"os"
	"os/signal"
	"syscall"
	"time"

	"creativematrix.com/beyondreading/app/api/user/conf"
	"creativematrix.com/beyondreading/app/api/user/http"
	"creativematrix.com/beyondreading/app/api/user/svc"
	"creativematrix.com/beyondreading/pkg/debug"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/tracer"
)

const (
	app = "api-user"
)

func main() {
	config := conf.Load(app)

	rand.Seed(time.Now().Unix())
	logger.InitLog(app, config.Log.Level)
	tracer.InitTracing(config.Base, app)
	service := svc.Load(config)
	ecode.Init(&config.Base)
	middlewares.Init(&config.Base)

	http.Start(config, service)
	debug.Start(config.Base, service)
	logger.LogInfo(app, " started listen on ", config.Port.HTTP)

	c := make(chan os.Signal, 1)
	signal.Notify(c, syscall.SIGHUP, syscall.SIGQUIT, syscall.SIGTERM, syscall.SIGINT)
	for {
		s := <-c
		switch s {
		case syscall.SIGQUIT, syscall.SIGTERM, syscall.SIGINT:
			logger.LogWarnw("api-user service exit")
			service.Close()
			time.Sleep(time.Second * 5)
			return
		case syscall.SIGHUP:
		default:
			return
		}
	}
}
