adminPhone = "18888888888"

smsSwitch = false

webLogin = "http://dev.91quliao.com"

officialWX = "xinlianjiaoyou"

forbidUpdate = [
    { startTime = "2022-10-08 14:00:00", endTime = "2022-10-08 14:05:00" },
    { startTime = "2022-10-08 14:10:00", endTime = "2022-10-08 14:15:00" },
]

iosSecret = '''-----B<PERSON>IN PRIVATE KEY-----
MIGTAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBHkwdwIBAQQgu8qlxqaNIR4rs0A1
wd7uMdw0dDEqqu1mWwHVvXijvr2gCgYIKoZIzj0DAQehRANCAARKs7UkCsibpUoG
I9gOFGd/8qetPpYaZ/cWtyT3X5pGa0oJ59VbKEplS5ZrTYCrN8K4pgw5xsd0STjF
JKCEyqQz
-----<PERSON><PERSON> PRIVATE KEY-----
'''

#FaceID人脸
[Face]
[Face.FaceID]
bizTokenUrl = "https://api.megvii.com/faceid/v3/sdk/get_biz_token"
verifyUrl = "https://api.megvii.com/faceid/v3/sdk/verify"
ocrUrl = "https://api.megvii.com/faceid/v3/ocridcard"
appIdYY = "AL6fN7O0ya1ysppkJo3LNGWvHSqcXP6s"
secretYY = "0gWTDVSQqLQa4hMIIsyBQBBiN3xZzy1H"
appIdWY = "LyakG-2VLh0tApZ0WPUo4hMdjCrrAduV"
secretWY = "dtbnapTi7F8Gf79clHU0rxEnIDlTLwBY"

#网宿
[wangsu]
ak = "L9L3pormHdS4WHQOLou2BHQtjVJJFvzRRhQe"
sk = "L9450qe7fXwUTG2LrsFn8flCMypQLSEZqQZ6tPPdTeeSWdHz29zhgk7a8CARjPJG"
upDomain = "devgolang.up27.v1.wcsapi.com"
mgrDomain = "devgolang.mgr27.v1.wcsapi.com"
bucket = "bobeigolang"
endPoint = "s3-cn-east-1.wcsapi.com"
cdnDomain = "http://goimg.17kuxiu.com"



[rabbitmq]
[rabbitmq.thirdEx]
name = "summer-exchange"
type = "direct"

[chuanglan]
account = "YZM3351330"
password = "XWa2vlYjb"
url = "http://smssh1.253.com/msg/v1/send/json"

[weixin]
AppID = "wxae894c03bcb67819"
AppSecret = "fe00a9b097a147be51cedd4c99df43d6"

[qq]
AppID = "**********"
AppKey = "cda9o3l62HUVaH9I"

[flash]
mobileQuery = "https://api.253.com/open/flashsdk/mobile-query"
mobileValidate = "https://api.253.com/open/flashsdk/mobile-validate"
[flash.ios]
appId = "tazqUMxA"
appKey = "YNZ7UKQF"
[flash.android]
appId = "PqOAaPMF"
appKey = "nV5Ae8UJ"

[minio]
endpoint = "minio.dev_golang.91quliao.com"
domain = "minio.dev_golang.91quliao.com"
accesskey = "zhuita"
secretkey = "zhuita123"

[focus]
maxCap = 2000