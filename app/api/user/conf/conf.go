package conf

import (
	"fmt"

	"creativematrix.com/beyondreading/pkg/redis"

	"creativematrix.com/beyondreading/pkg/check/shumei"
	fc "creativematrix.com/beyondreading/pkg/face"
	"creativematrix.com/beyondreading/pkg/minio"
	"creativematrix.com/beyondreading/pkg/wangsu"

	"creativematrix.com/beyondreading/pkg/config"
	"creativematrix.com/beyondreading/pkg/rabbitmq"
)

type Config struct {
	config.Base

	MongodbUser string
	BaseImgUrl  string
	AdminPhone  string
	OfficialWX  string

	RedisUser *redis.Config
	RedisLive *redis.Config

	SmsSwitch bool
	WebLogin  string
	IosSecret string

	Log struct {
		Level string
	}

	Face *fc.FaceConf

	RabbitMQ struct {
		URL      string
		SummerEx *rabbitmq.ExchangeConfig
		ThirdEx  *rabbitmq.ExchangeConfig
	}

	Wangsu *wangsu.Config
	SM     shumei.SMConfig

	//对称加密
	MsgKey struct {
		Key string
	}

	Flash struct {
		MobileQuery    string
		MobileValidate string
		Ios            *FlashConfig
		Android        *FlashConfig
	}

	ChuangLan struct {
		Account  string
		Password string
		Url      string
	}

	WeiXin struct {
		AppID     string
		AppSecret string
	}

	QQ struct {
		AppID  string
		AppKey string
	}

	Minio *minio.Config

	Decorate struct {
		SuperAdmin []string
		Official   []string
	}

	ForbidUpdate []struct {
		StartTime string
		EndTime   string
	}

	Focus struct {
		MaxCap int64
	}

	H5Domain string

	PayJump struct {
		Full    string
		Half    string
		Domains []*PayJumpDomain
	}
}

type PayJumpDomain struct {
	Domain      string
	Channel     string
	Probability int64
	Start       int64
	End         int64
}

type FlashConfig struct {
	AppId  string
	AppKey string
}

func Load(app string) *Config {
	var conf = new(Config)
	if err := config.Load(app, conf); err != nil {
		panic(fmt.Sprintf("config load failed: %v", err))
	}
	return conf
}
