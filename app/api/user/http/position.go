package http

import (
	"creativematrix.com/beyondreading/pkg/ecode"
	"creativematrix.com/beyondreading/pkg/router"
	"github.com/gin-gonic/gin"
)

func positionList(c *gin.Context) {
	data, err := service.GetPositionList(c)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(data).Success()
}

func checkPositionWhitelist(c *gin.Context) {
	noId := c.GetString(router.NoId)
	data, err := service.CheckPositionWhitelist(c, noId)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(data).Success()
}
