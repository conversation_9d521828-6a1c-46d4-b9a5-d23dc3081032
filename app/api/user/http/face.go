//Description faceID认证
//Date        2021/8/2
//User        cl

package http

import (
	"fmt"
	"os"
	"time"

	"github.com/go-resty/resty/v2"

	"github.com/gin-gonic/gin"

	"creativematrix.com/beyondreading/app/api/user/model"
	"creativematrix.com/beyondreading/pkg/ecode"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/router"
	"creativematrix.com/beyondreading/pkg/utils"
)

func getIDCardInfo(c *gin.Context) {
	noId := c.GetString(router.NoId)

	fh, err := c.FormFile("image")
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	tmpFile := fmt.Sprintf("%d-idcard-%s", time.Now().Unix(), noId)
	if err := c.SaveUploadedFile(fh, tmpFile); err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	defer os.Remove(tmpFile)

	rsp, err := service.GetIDCardInfo(c, tmpFile, noId)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(rsp).Success()
}

// 获取人脸验证token
func getBizToken(c *gin.Context) {
	rsp := ecode.Back(c)
	noId := c.GetString(router.NoId)
	param := new(model.ReqFace)
	err := c.ShouldBind(param)
	if err != nil {
		rsp.Failure(model.ErrorArgument)
		return
	}

	//次数限制， 头像审核状态判断
	err = service.VerifyLimit(c, noId, param)
	if err != nil {
		rsp.Failure(err)
		return
	}

	if param.ComparisonType == model.CompareFace {
		//人脸比对需要上传照片
		tmpFile := fmt.Sprintf("face-avatar-%s", noId)
		_, err = resty.New().R().SetOutput(tmpFile).Get(baseImgUrl + param.Avatar)
		if err != nil {
			rsp.Failure(err)
			return
		}

		fi, err := os.Stat(tmpFile)
		if err != nil {
			rsp.Failure(err)
			return
		}

		if fi.Size() > 4*1024*1024 {
			rsp.Failure(model.ImageTooLarge)
			return
		}
		defer os.Remove(tmpFile)
		token, err := service.GetBizToken(c, param, noId, tmpFile)
		if err != nil {
			rsp.Failure(err)
			return
		}

		rsp.SetData(token).Success()
	} else if param.ComparisonType == model.CompareKYC {
		//KYC 需要上传身份证信息
		if param.IdCard == "" || param.Name == "" {
			rsp.Failure(model.ErrorArgument)
			return
		}

		//校验年龄
		if err = verifyAge(param.IdCard); err != nil {
			rsp.Failure(err)
			return
		}

		err = service.CacheGenderByNoId(c, noId, string(param.IdCard[len(param.IdCard)-2]))
		if err != nil {
			rsp.Failure(err)
			return
		}

		token, err := service.GetBizToken(c, param, noId, "")
		if err != nil {
			rsp.Failure(err)
			return
		}

		rsp.SetData(token).Success()
	} else {
		rsp.Failure(model.RequestErr)
	}
}

// 人脸验证
func faceVerify(c *gin.Context) {
	noId := c.GetString(router.NoId)
	userId := c.GetString(router.UserKey)
	compareType := c.PostForm("comparisonType")
	bizToken := c.PostForm("bizToken")
	logger.LogInfo("param:====", compareType, bizToken)
	rsp := ecode.Back(c)

	if compareType == "" || bizToken == "" {
		rsp.Failure(model.ErrorArgument)
		return
	}

	//人脸比对需要上传照片
	fh, err := c.FormFile("megData")
	if err != nil {
		rsp.Failure(model.ErrorArgument)
		return
	}
	tmpFile := fmt.Sprintf("face-verify-%s", noId)
	if c.SaveUploadedFile(fh, tmpFile) != nil {
		rsp.Failure(model.ImageTooLarge)
		return
	}
	defer os.Remove(tmpFile)

	status, e := service.FaceIDVerify(c, noId, userId, compareType, bizToken, tmpFile)
	if e != nil {
		rsp.Failure(e)
		return
	}

	rsp.SetData(status).Success()
}

// 无法认证，增加人工审核实名接口
func realNameLoc(c *gin.Context) {
	noId := c.GetString(router.NoId)
	form, _ := c.MultipartForm()
	rsp := ecode.Back(c)
	if len(form.File["images"]) < 3 {
		rsp.Failure(model.RealNameImgErr)
		return
	}

	name := c.PostForm("name")
	idCard := c.PostForm("idCard")
	//校验年龄
	if err := verifyAge(idCard); err != nil {
		rsp.Failure(err)
		return
	}

	param := &model.ReqFace{
		ComparisonType: model.CompareKYC,
		IdCard:         idCard,
		Name:           name,
	}
	_, err := service.GetBizToken(c, param, noId, "")
	if err != nil {
		rsp.Failure(err)
		return
	}

	filenames := make([]string, 0)
	_ = os.Mkdir(noId, 0755)
	for i, fh := range form.File["images"] {
		filename := fmt.Sprintf("realname-%d-%d-%s", time.Now().Unix(), i, noId)
		err := c.SaveUploadedFile(fh, noId+"/"+filename)
		if err != nil {
			ecode.Back(c).Failure(err)
			return
		}
		filenames = append(filenames, filename)
	}
	defer os.RemoveAll(noId)

	err = service.RealNameLoc(c, noId, name, idCard, model.FaceHandle, filenames)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).Success()
}

func realNameInfo(c *gin.Context) {
	noId := c.GetString(router.NoId)

	info, err := service.GetRealNameInfo(c, noId)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(info).Success()
}

func verifyAge(idCard string) error {
	if !utils.IdNumPattern(idCard) {
		return model.IDNumPatternErr
	}
	age := utils.GetAgeByDate(fmt.Sprintf("%s-%s-%s", idCard[6:10], idCard[10:12], idCard[12:14]))

	if age < 18 || age > 55 {
		return model.AgeInvalid
	}
	return nil
}
