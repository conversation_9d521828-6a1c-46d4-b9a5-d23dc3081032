package http

import (
	"crypto/md5"
	"fmt"
	"regexp"

	"github.com/gin-gonic/gin"

	"creativematrix.com/beyondreading/app/api/user/model"
	"creativematrix.com/beyondreading/pkg/ecode"
	"creativematrix.com/beyondreading/pkg/router"
)

func SmsParamSign(s *model.SmsCodeReq) string {
	var ups = UrlParamSign{}
	ups["type"] = s.Type
	ups["mobile"] = s.Mobile
	ups["timestamp"] = s.Timestamp
	ups["key"] = key

	return fmt.Sprintf("%x", md5.Sum([]byte(ParamToString(ups))))
}

func IsContain(items []string, item string) bool {
	for _, eachItem := range items {
		if eachItem == item {
			return true
		}
	}
	return false
}

func smsCode(c *gin.Context) {
	var s model.SmsCodeReq
	if err := c.ShouldBindJSON(&s); err != nil {
		ecode.Back(c).Failure(ecode.RequestErr)
		return
	}

	// 检查手机号
	reg := `^1([3-9])\d{9}$`
	rgx := regexp.MustCompile(reg)
	if !rgx.MatchString(s.Mobile) {
		ecode.Back(c).Failure(model.UserSmsMobileError)
		return
	}

	var limitMobilePre = []string{"162", "165", "167", "170", "171"}
	if IsContain(limitMobilePre, s.Mobile[0:3]) {
		isWhite, err := service.SmsCodeWhite(c, s.Mobile)
		if err != nil || !isWhite {
			ecode.Back(c).Failure(model.UserSmsMobileRegError)
			return
		}
	}

	// 签名
	if s.Sign != SmsParamSign(&s) {
		ecode.Back(c).Failure(model.SignError)
		return
	}

	// 业务逻辑+ 数量限制
	s.Format = model.SMSCodeFormat
	result, err := service.SmsCode(c, &s)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(result).Success()
}

func logoffCode(c *gin.Context) {
	noId := c.GetString(router.NoId)

	result, err := service.LogoffCode(c, noId)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(result).Success()
}
