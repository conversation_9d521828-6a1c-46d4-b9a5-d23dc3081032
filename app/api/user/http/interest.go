package http

import (
	"creativematrix.com/beyondreading/app/api/user/model"
	"creativematrix.com/beyondreading/pkg/ecode"
	"creativematrix.com/beyondreading/pkg/router"
	"github.com/gin-gonic/gin"
)

func interestTagSave(c *gin.Context) {
	var req *model.InterestTagSaveReq
	if err := c.ShouldBindJSON(&req); err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	req.NoId = c.GetString(router.NoId)

	rsp, err := service.InterestTagSave(c, req)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(rsp).Success()
}

func interestTagSwitch(c *gin.Context) {
	noId := c.GetString(router.NoId)

	rsp, err := service.InterestTagSwitch(c, noId)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(rsp).Success()
}
