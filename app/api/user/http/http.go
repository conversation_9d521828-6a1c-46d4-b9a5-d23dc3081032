package http

import (
	"creativematrix.com/beyondreading/app/api/user/conf"
	"creativematrix.com/beyondreading/app/api/user/model"
	"creativematrix.com/beyondreading/app/api/user/svc"
	"creativematrix.com/beyondreading/pkg/rateLimit"
	bbrouter "creativematrix.com/beyondreading/pkg/router"
)

var (
	service    *svc.Svc
	limit      *rateLimit.LimitClient
	baseImgUrl string
)

func Start(c *conf.Config, s *svc.Svc) {

	baseImgUrl = c.BaseImgUrl
	service = s
	limit = rateLimit.New(c.RedisUser)
	router := bbrouter.Start(c.Base)

	rateLimiter := limit.RateLimiter(rateLimit.MaxThreads(1))

	rp := router.Group("/user", router.AuthUser, rateLimiter)
	{
		rp.POST("/update", updateUserInfo)      //更新基本信息
		rp.POST("/album", updateAlbum)          //更新相册
		rp.POST("/remark/name", remarkUsername) //设置备注名
		rp.POST("/thumbs/up", thumbsUpUser)     //点赞
		rp.GET("/testmq", testmq)               //测试mq
		rp.POST("/ocridcard", getIDCardInfo)    //获取身份证信息
		rp.POST("/biztoken", getBizToken)       //获取人脸token
		rp.POST("/face/verify", faceVerify)     //验证人脸
		rp.POST("/realname", realNameLoc)
		rp.POST("/relation/set", setAttention) //设置用户关系
		rp.POST("/see/you", seeYou)            //浏览主页
		rp.POST("/lonlat", router.ValidHead, lonLat)
		rp.POST("/feedback", feedBack)          //用户反馈
		rp.POST("/logoff", logoff)              //注销
		rp.POST("/logoff/valid", logoffValid)   //注销校验
		rp.POST("/logoff/cancel", cancelLogoff) //取消注销

		rp.POST("/bind/third", bindThird)
		rp.POST("/bind/flash", bindFlash)
		rp.POST("/bind/sms", bindSms)
		rp.POST("/switch", router.AuthUser, switchUser)

		rp.POST("/bind/cid", bindCid)     // 绑定用户与个推关系
		rp.POST("/set/switch", setSwitch) // 设置用户开关
		rp.GET("/get/switch", getSwitch)  // 获取用户开关
		rp.POST("/set/status", setStatus) // 用户在app位置

		rp.GET("/web/loginUrl", webLoginUrl) //web 第三方登录回调地址

		rp.POST("/mobile/modify/apply", applyModifyMobile)

		rp.POST("/interest/tag/save", interestTagSave)    // 保存标签
		rp.GET("/interest/tag/switch", interestTagSwitch) // 换一组标签
	}

	v1 := router.Group("/user", router.AuthUser)
	{
		v1.GET("/real/status", getRealStatus)         //查询真人认证状态
		v1.GET("/complete/status", getCompleteStatus) //查询资料完成状态
		v1.GET("/realname/info", realNameInfo)
		v1.GET("/relation/get", getAttention)     //获取好友列表
		v1.GET("/relation/focusIds", getFocusIds) //获取好友列表ID

		v1.GET("/mobile/modify/check", checkModifyMobile)
		v1.GET("/mobile/modify/info", getModifyMobileInfo)
		v1.GET("/mobile/sms/check", checkOriginMobileSms)
		v1.POST("/mobile/origin/code", originMobileCode)
	}

	smsCodeLimit := limit.RateLimiter(rateLimit.ExpireTime(60), rateLimit.MaxThreads(1),
		rateLimit.IsLimitTime(true),
		rateLimit.Key(model.RedisCodeRateKey),
		rateLimit.BodyParam([]string{"mobile", "type"}),
		rateLimit.MsgParam(model.CodeRateMsg.Message()),
	)
	router.POST("/user/sms/code", smsCodeLimit, smsCode) // smsCodeLimit 速度限制 60s 一次 一天10条

	logoffRate := limit.RateLimiter(rateLimit.ExpireTime(60), rateLimit.MaxThreads(1),
		rateLimit.IsLimitTime(true),
		rateLimit.MsgParam(model.CodeRateMsg.Message()),
	)
	router.POST("/user/logoff/code", router.AuthUser, logoffRate, logoffCode)

	loginRate := limit.RateLimiter(rateLimit.MaxThreads(1), rateLimit.BodyParam([]string{"mobile"}),
		rateLimit.IsLimitTime(true), rateLimit.ExpireTime(5))
	v2 := router.Group("/user")
	{
		v2.POST("/sms/flash", router.ValidImei, loginRate, flashLogin, router.ValidIP)
		v2.POST("/login/third", router.ValidImei, loginRate, login, router.ValidIP)
		v2.POST("/login/sms", router.ValidImei, loginRate, smsLogin, router.ValidIP)
		v2.GET("/login/name", loginName)
		v2.GET("/login/avatar", loginAvatar)
		v2.POST("/login/info", router.AuthUser, router.ValidHead, rateLimiter, loginInfo)
		v2.GET("/logout", router.AuthUser, logout)
	}

	v3 := router.Group("/user", router.AuthUser)
	{
		v3.GET("/bind/list", bindList)
		v3.GET("/info", getInfo)                      //用户详情
		v3.GET("/my", router.AfterAuth, getMyInfo)    //我的聚合信息
		v3.GET("/audio/random", getAudioHint)         //语音签名提示
		v3.GET("/feedback/type", getFeedBackType)     //用户反馈类型
		v3.GET("/logoff/status", logoffStatus)        //注销状态
		v3.GET("/check/friend/info", checkFriendInfo) // 检查交友主页信息
	}

	vw := router.Group("/user")
	{
		vw.GET("/contact", userContact)
		vw.POST("/weixin/info", weixinInfo)
		vw.GET("/weixin/jssdkinit", jsSdkInit)
		vw.GET("/query/guest", userGuest)
		vw.GET("/query/guest/list", userQueryList)
	}

	position := router.Group("/position", router.AuthUser)
	{
		position.GET("/customize/list", positionList)
		position.GET("/whitelist/check", checkPositionWhitelist)
	}

	router.POST("/usi2xuamnkfumujkt3jymsy6f9", smsCodeLimit, smsCode)                                 //获取验证码
	router.POST("/usaegqayhfqpmaaasu48ck8are", router.AuthUser, rateLimiter, setAttention)            //设置用户关系
	router.POST("/usuuhv9iwihxjhdbbqkt45l5rp", router.ValidImei, loginRate, smsLogin, router.ValidIP) //验证码
	router.GET("/ussztq78rwefzmciazhrjd6haq", router.AuthUser, getInfo)                               //用户详情
	router.GET("/usibb9r2mq7daz85wmcfrdyb7f", router.AuthUser, logout)                                //登出
	router.GET("/us7fa2lgu6srjyn2w4dlatjzxu", router.AuthUser, router.AfterAuth, getMyInfo)           //我的聚合信息
	router.GET("/usfamugwrvztflcacskacvs6sn", router.AuthUser, getFeedBackType)                       //用户反馈类型
	router.POST("/ush4gbxtqkpgxscc7cegdj8bbf", router.AuthUser, rateLimiter, feedBack)                //用户反馈
	router.GET("/uscrl8m82ubmurvu89gu8jdfce", userGuest)                                              //游客信息
	router.GET("/ussedrmcwhdj5u6jttx2hxwypf", userContact)                                            //联系客服

	go func() {
		if err := router.Run(c.Port.HTTP); err != nil {
			panic(err)
		}
	}()
}
