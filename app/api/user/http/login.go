package http

import (
	"sort"
	"strings"

	"github.com/gin-gonic/gin"

	"creativematrix.com/beyondreading/app/api/user/model"
	"creativematrix.com/beyondreading/pkg/ecode"
	"creativematrix.com/beyondreading/pkg/router"
	"creativematrix.com/beyondreading/pkg/utils"
)

var key = "pv0ZelZ5"

type UrlParamSign map[string]string

func ParamToString(p map[string]string) string {
	var list = make([]string, 0)
	for key, value := range p {
		if len(value) > 0 {
			list = append(list, key+"="+value)
		}
	}
	sort.Strings(list)
	return strings.Join(list, "&")
}

func login(c *gin.Context) {
	var l model.LoginReq
	if err := c.ShouldBindJSON(&l); err != nil {
		ecode.Back(c).Failure(ecode.RequestErr)
		return
	}

	l.Platform = strings.ToLower(c.GetHeader("platform"))
	result, err := service.Login(c, &l)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	c.Set(router.NoId, result.NoId)
	c.Set(router.Status, result.Status)
	c.Next()
	if _, ok := c.Get(router.NextErr); ok {
		return
	}
	ecode.Back(c).SetData(result).Success()
}

func bindList(c *gin.Context) {
	userId := c.GetString(router.UserKey)
	result, err := service.BindList(c, userId)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(result).Success()
}

func bindThird(c *gin.Context) {
	var l model.LoginReq
	if err := c.ShouldBindJSON(&l); err != nil {
		ecode.Back(c).Failure(ecode.RequestErr)
		return
	}

	userId := c.GetString(router.UserKey)

	result, err := service.Bind(c, &l, userId)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(result).Success()
}

func bindFlash(c *gin.Context) {
	var s model.FlashLoginReq
	if err := c.ShouldBindJSON(&s); err != nil {
		ecode.Back(c).Failure(ecode.RequestErr)
		return
	}
	userId := c.GetString(router.UserKey)
	s.Platform = strings.ToLower(c.GetHeader("platform"))

	result, err := service.BindFlash(c, &s, userId)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(result).Success()
}

func bindSms(c *gin.Context) {
	var s model.SmsLoginReq
	if err := c.ShouldBindJSON(&s); err != nil {
		ecode.Back(c).Failure(ecode.RequestErr)
		return
	}

	userId := c.GetString(router.UserKey)

	result, err := service.SmsBind(c, &s, userId)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(result).Success()
}

func smsLogin(c *gin.Context) {
	var s model.SmsLoginReq
	if err := c.ShouldBindJSON(&s); err != nil {
		ecode.Back(c).Failure(ecode.RequestErr)
		return
	}

	s.Platform = strings.ToLower(c.GetHeader("platform"))
	result, err := service.SmsLogin(c, &s)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	c.Set(router.NoId, result.NoId)
	c.Set(router.Status, result.Status)
	c.Next()
	if _, ok := c.Get(router.NextErr); ok {
		return
	}
	ecode.Back(c).SetData(result).Success()
}

func flashLogin(c *gin.Context) {
	var s model.FlashLoginReq
	if err := c.ShouldBindJSON(&s); err != nil {
		ecode.Back(c).Failure(ecode.RequestErr)
		return
	}

	s.Platform = strings.ToLower(c.GetHeader("platform"))
	result, err := service.FlashLogin(c, &s)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	c.Set(router.NoId, result.NoId)
	c.Set(router.Status, result.Status)
	c.Next()
	if _, ok := c.Get(router.NextErr); ok {
		return
	}
	ecode.Back(c).SetData(result).Success()
}

func loginName(c *gin.Context) {
	result, err := service.LoginName(c)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(result).Success()
}

func loginInfo(c *gin.Context) {
	var l model.LoginInfoReq
	if err := c.ShouldBindJSON(&l); err != nil {
		ecode.Back(c).Failure(ecode.RequestErr)
		return
	}
	var h model.Device
	if err := c.ShouldBindHeader(&h); err != nil {
		ecode.Back(c).Failure(ecode.RequestErr)
		return
	}
	h.IP = utils.ClientIP(c.Request)
	h.Channel = l.Channel
	l.Device = h

	userId := c.GetString(router.UserKey)
	noId := c.GetString(router.NoId)
	msgId := c.GetString(router.MsgId)

	if !validNickname(l.NickName) {
		ecode.Back(c).Failure(model.InvalidNickName)
		return
	}
	err := service.LoginInfo(c, &l, userId, noId, msgId)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(true).Success()
}

func logout(c *gin.Context) {
	userId := c.GetString(router.UserKey)
	token := c.GetHeader("token")
	noId := c.GetString(router.NoId)

	result, err := service.Logout(c, userId, noId, token)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(result).Success()
}

func switchUser(c *gin.Context) {
	var s model.SwitchUserReq
	if err := c.ShouldBindJSON(&s); err != nil {
		ecode.Back(c).Failure(ecode.RequestErr)
		return
	}
	userId := c.GetString(router.UserKey)
	result, err := service.SwitchUser(c, &s, userId)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(result).Success()
}

func loginAvatar(c *gin.Context) {
	result := service.RandAvatar(c, c.DefaultQuery("gender", model.Male))

	ecode.Back(c).SetData(result).Success()
}
