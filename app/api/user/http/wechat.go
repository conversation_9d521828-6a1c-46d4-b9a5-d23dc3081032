package http

import (
	"creativematrix.com/beyondreading/app/api/user/model"
	"creativematrix.com/beyondreading/pkg/ecode"
	"github.com/gin-gonic/gin"
)

func weixinInfo(c *gin.Context) {
	// scope snsapi_userinfo能拿到用户token
	// scope 能拿到用户openId
	var wr model.WeixinReq
	if err := c.ShouldBindJSON(&wr); err != nil {
		ecode.Back(c).Failure(ecode.RequestErr)
		return
	}

	result, err := service.WeixinInfo(c, &wr)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(result).Success()
}

func jsSdkInit(c *gin.Context) {
	url := c.Query("url")

	ecode.Back(c).SetData(service.GetJsSign(url)).Success()
}

func userGuest(c *gin.Context) {
	noId := c.Query("noid")

	user, err := service.QueryUserGuest(c, &model.ReqQueryUser{NoId: noId})
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(user).Success()
}

func userQueryList(c *gin.Context) {
	noIds := c.Query("noids")

	users, err := service.GetUserQueryList(c, noIds)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(users).Success()
}

func userContact(c *gin.Context) {
	contact := service.GetContact(c)

	ecode.Back(c).SetData(contact).Success()
}
