package http

import (
	"strings"

	"creativematrix.com/beyondreading/app/api/user/model"
	"creativematrix.com/beyondreading/pkg/ecode"
	"creativematrix.com/beyondreading/pkg/router"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
)

type setAttentionReq struct {
	ToUserId string `json:"toUserId" binding:"required"`
	Mode     int    `json:"mode"` //0 取消关注  1 建立关注
}

func setAttention(c *gin.Context) {
	var q setAttentionReq
	if err := c.Bind(&q); err != nil {
		ecode.Back(c).Failure(ecode.ErrorArgument)
		return
	}

	noId := c.GetString(router.NoId)

	userRelation, err := service.AttentionSet(c, noId, q.ToUserId, q.Mode)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(userRelation).Success()
}

type getAttentionReq struct {
	Type     int   `json:"type" form:"type"` // 密友 0     好友 1    关注  2   粉丝  3
	PageSize int64 `json:"pageSize"  form:"pageSize"`
	PageNum  int64 `json:"pageNum"  form:"pageNum"`
}

func getAttention(c *gin.Context) {
	var q = &getAttentionReq{
		PageSize: 20,
		PageNum:  1,
	}
	if err := c.MustBindWith(&q, binding.Query); err != nil {
		ecode.Back(c).Failure(ecode.ErrorArgument)
		return
	}

	noId := c.GetString(router.NoId)

	rsps, err := service.AttentionGetList(c, noId, q.Type, q.PageSize, q.PageNum)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(rsps).Success()
}

func getFocusIds(c *gin.Context) {
	noId := c.GetString(router.NoId)
	data, err := service.FocusList(c, noId)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(data).Success()
}

func bindCid(c *gin.Context) {
	param := &model.BindPushReq{}
	if err := c.ShouldBindJSON(param); err != nil {
		ecode.Back(c).Failure(ecode.ErrorArgument)
		return
	}

	platform := c.GetString("platform")
	if len(param.Platform) == 0 && len(platform) > 0 {
		param.Platform = strings.ToLower(platform)
	}
	param.NoId = c.GetString(router.NoId)
	param.MsgId = c.GetString(router.MsgId)
	err := service.BindPushRelation(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).Success()
}

func setSwitch(c *gin.Context) {
	var s model.SetSwitchReq
	if err := c.ShouldBindJSON(&s); err != nil {
		ecode.Back(c).Failure(ecode.ErrorArgument)
		return
	}

	noId := c.GetString(router.NoId)
	err := service.SetSwitch(c, noId, &s)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).Success()
}

func getSwitch(c *gin.Context) {
	noId := c.GetString(router.NoId)
	data, err := service.GetSwitch(c, noId)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(data).Success()
}

func setStatus(c *gin.Context) {
	var s model.SetStatusReq
	if err := c.ShouldBindJSON(&s); err != nil {
		ecode.Back(c).Failure(ecode.ErrorArgument)
		return
	}
	s.NoId = c.GetString(router.NoId)
	err := service.SetPushStatus(c, &s)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).Success()
}
