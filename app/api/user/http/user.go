package http

import (
	"regexp"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"creativematrix.com/beyondreading/app/api/user/model"
	"creativematrix.com/beyondreading/pkg/ecode"
	"creativematrix.com/beyondreading/pkg/router"
	"creativematrix.com/beyondreading/pkg/typeconvert"
	"creativematrix.com/beyondreading/pkg/utils"
)

// 更新用户基本信息
//
//	{
//		"nickName": "test",
//		"born": "1996-05-08",
//		"height": 175,
//		"weight": 70,
//		"city": "上海市",
//		"province": "上海市",
//		"area": "闵行区",
//		"hometown": "湖北省,武汉市",
//		"intro": "test",
//		"feeling": "单身",
//		"job": "金融,证券",
//		"education": "本科",
//		"income": "30～50万",
//	}
func updateUserInfo(c *gin.Context) {
	userId := c.GetString(router.UserKey)
	noId := c.GetString(router.NoId)
	msgId := c.GetString(router.MsgId)
	param := new(model.ReqUpdateUser)
	err := c.BindJSON(param)
	if err != nil {
		ecode.Back(c).Failure(ecode.RequestErr)
		return
	}

	if param.NickName != "" {
		if !validNickname(param.NickName) {
			ecode.Back(c).Failure(model.InvalidNickName)
			return
		}
	}

	if param.Intro != "" {
		if len(strings.Trim(param.Intro, " ")) == 0 {
			ecode.Back(c).Failure(model.EmptyIntro)
			return
		}
		if !validIntro(param.Intro) {
			ecode.Back(c).Failure(model.InvalidIntro)
			return
		}
	}
	//过滤字段，部分字段不可修改
	userInfo, e := service.UpdateUserInfo(c, userId, noId, msgId, param)
	if e != nil {
		ecode.Back(c).Failure(e)
		return
	}

	ecode.Back(c).SetData(userInfo).Success()
}

func updateAlbum(c *gin.Context) {
	userId := c.GetString(router.UserKey)
	noId := c.GetString(router.NoId)
	msgId := c.GetString(router.MsgId)
	param := new(model.ReqAlbum)
	err := c.BindJSON(param)
	if err != nil {
		ecode.Back(c).Failure(ecode.RequestErr)
		return
	}

	album, e := service.UpdateAlbum(c, userId, noId, msgId, param.Album)
	if e != nil {
		ecode.Back(c).Failure(e)
		return
	}

	ecode.Back(c).SetData(album).Success()
}

// 获取真人认证状态
func getRealStatus(c *gin.Context) {
	userId := c.GetString(router.NoId)
	dstUser := c.Query("dstuid")
	if dstUser == "" {
		dstUser = userId
	}

	status, err := service.GetUserRealStatus(c, dstUser)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(status).Success()
}

// 获取资料是否完成
func getCompleteStatus(c *gin.Context) {
	userId := c.GetString(router.NoId)
	status, err := service.GetCompleteStatus(c, userId)

	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(status).Success()
}

// 设置备注
func remarkUsername(c *gin.Context) {
	userId := c.GetString(router.NoId)
	param := new(model.ReqUserRemark)
	err := c.BindJSON(param)
	if err != nil {
		ecode.Back(c).Failure(ecode.ErrorArgument)
		return
	}

	err = service.RemarkUsername(c, userId, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).Success()
}

// 点赞
func thumbsUpUser(c *gin.Context) {
	userId := c.GetString(router.NoId)
	param := new(model.ReqThumbsUp)
	err := c.BindJSON(param)
	if err != nil {
		ecode.Back(c).Failure(ecode.RequestErr)
		return
	}

	err = service.ThumbsUpUser(c, userId, param.DstUid)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).Success()
}

func testmq(c *gin.Context) {
	err := service.TUser(c)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	time.Sleep(time.Second * 5)

	ecode.Back(c).Success()
}

func getInfo(c *gin.Context) {
	noId := c.GetString(router.NoId)
	userId := c.GetString(router.UserKey)
	dstUser := c.Query("dstuid")
	if dstUser == "" {
		dstUser = noId
	}

	scene := c.Query("scene")
	userInfo, err := service.GetUser(c, noId, dstUser, scene, userId, utils.ClientIP(c.Request))
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(userInfo).Success()
}

func getMyInfo(c *gin.Context) {
	userId := c.GetString(router.UserKey)
	noId := c.GetString(router.NoId)

	userInfo, err := service.GetMyInfo(c, userId, noId, utils.ClientIP(c.Request))
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(userInfo).Success()
}

func getAudioHint(c *gin.Context) {
	sort := typeconvert.StringToFloat64(c.Query("sort"))

	hint, err := service.GetAudioHint(c, sort)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(hint).Success()
}

func lonLat(c *gin.Context) {
	var l model.LonLatReq
	if err := c.ShouldBindJSON(&l); err != nil {
		ecode.Back(c).Failure(ecode.RequestErr)
		return
	}

	var d model.Device
	if err := c.ShouldBindHeader(&d); err != nil {
		ecode.Back(c).Failure(ecode.RequestErr)
		return
	}
	d.LoginTime = time.Now().Unix()
	d.IP = utils.ClientIP(c.Request)

	userId := c.GetString(router.UserKey)
	noId := c.GetString(router.NoId)

	err := service.LonLat(c, &l, &d, userId, noId)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(true).Success()
}

func getFeedBackType(c *gin.Context) {
	t := c.Query("category")
	if t != model.FBReport && t != model.FBSuggest && t != model.FBFamilyReport {
		ecode.Back(c).Failure(ecode.ErrorArgument)
		return
	}

	data, err := service.GetFeedbackType(c, t)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(data).Success()
}

// 用户反馈
func feedBack(c *gin.Context) {
	noId := c.GetString(router.NoId)
	param := new(model.ReqFeedBack)
	err := c.BindJSON(param)

	if err != nil || !model.FeedBackType[param.FeedBackType] {
		ecode.Back(c).Failure(ecode.ErrorArgument)
		return
	}

	err = service.UserFeedBack(c, noId, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).Success()
}

// 用户申请注销
func logoff(c *gin.Context) {
	noId := c.GetString(router.NoId)

	err := service.LogoffApply(c, noId)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).Success()
}

// 用户注销状态
func logoffStatus(c *gin.Context) {
	noId := c.GetString(router.NoId)

	status, err := service.LogoffStatus(c, noId)

	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(status).Success()
}

// 取消注销
func cancelLogoff(c *gin.Context) {
	noId := c.GetString(router.NoId)

	err := service.CancelLogoff(c, noId)

	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).Success()
}

func validNickname(nickname string) bool {
	reg, _ := regexp.Compile(`^[\p{Han}A-Za-z0-9_]{1,10}$`)
	return reg.MatchString(nickname)
}

func validIntro(intro string) bool {
	reg, _ := regexp.Compile(`^[\p{Han}A-Za-z0-9_,?。；:/!？，：！ ]+$`)
	return reg.MatchString(intro)
}

// 注销
func logoffValid(c *gin.Context) {
	noId := c.GetString(router.NoId)
	param := new(model.ReqValidLogoff)
	err := c.BindJSON(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	data, err := service.ValidLogoff(c, noId, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(data).Success()
}

func webLoginUrl(c *gin.Context) {
	ecode.Back(c).SetData(service.GetWebLogin(c)).Success()
}

func checkFriendInfo(c *gin.Context) {
	noId := c.GetString(router.NoId)

	rsp, err := service.CheckFriendInfo(c, noId)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(rsp).Success()
}
