package http

import (
	"creativematrix.com/beyondreading/app/api/user/model"
	"creativematrix.com/beyondreading/pkg/ecode"
	"creativematrix.com/beyondreading/pkg/router"
	"github.com/gin-gonic/gin"
)

func checkModifyMobile(c *gin.Context) {
	noId := c.GetString(router.NoId)
	mobile := c.Query("mobile")
	data, err := service.CheckMobile(c, noId, mobile)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

func getModifyMobileInfo(c *gin.Context) {
	noId := c.GetString(router.NoId)
	data, err := service.GetModifyMobileInfo(c, noId)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

func checkOriginMobileSms(c *gin.Context) {
	noId := c.GetString(router.NoId)
	code := c.Query("code")
	err := service.CheckOriginMobileCode(c, noId, code)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).Success()
}

func applyModifyMobile(c *gin.Context) {
	param := new(model.ApplyModifyMobileReq)
	param.NoId = c.GetString(router.NoId)
	err := c.ShouldBindJSON(param)
	if err != nil {
		ecode.Back(c).Failure(ecode.ErrorArgument)
		return
	}

	err = service.ApplyModifyMobile(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).Success()
}

func originMobileCode(c *gin.Context) {
	noId := c.GetString(router.NoId)
	result, err := service.OriginMobileCode(c, noId)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(result).Success()
}
