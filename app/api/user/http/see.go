package http

import (
	"creativematrix.com/beyondreading/pkg/ecode"
	"creativematrix.com/beyondreading/pkg/router"
	"github.com/gin-gonic/gin"
)

type seeYouReq struct {
	NoId string `json:"noId"`
}

func seeYou(c *gin.Context) {
	var q seeYouReq
	from := c.GetString(router.NoId)

	if err := c.ShouldBindJSON(&q); err != nil {
		ecode.Back(c).Failure(ecode.ErrorArgument)
		return
	}

	if q.NoId == from {
		ecode.Back(c).Failure(ecode.ErrorArgument)
		return
	}

	err := service.SeeYou(c, from, q.NoId)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	ecode.Back(c).SetData(true).Success()
}
