package svc

import (
	"context"
	"creativematrix.com/beyondreading/app/common/po"
	"creativematrix.com/beyondreading/pkg/utils"
	pb "creativematrix.com/beyondreading/proto/books"
	"creativematrix.com/beyondreading/proto/pbcommon"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"log"
)

func (s *BooksSvc) FindByIds(ctx context.Context, req *pb.FindByIdsReq) (*pb.BooklistResp, error) {
	rsps, err := s.dao.FindByIds(ctx, req.Ids)
	if err != nil {
		return nil, err
	}

	books := make([]*pbcommon.Book, 0)

	for _, book := range rsps {
		var tmpBook pbcommon.Book
		err := utils.JsonCopy(*book, &tmpBook)
		if err != nil {
			log.Fatal(err)
		}
		books = append(books, &tmpBook)
	}

	return &pb.BooklistResp{Books: books}, nil
}

func (s *BooksSvc) FindBooksInfo(ctx context.Context, req *pb.FindByIdsReq) (*pb.FindBooksInfoResp, error) {

	rsps, err := s.dao.FindBookInfo(ctx, req.Ids)
	if err != nil {
		return nil, err
	}

	bookMap := make(map[string]*pbcommon.BookInfo, len(rsps))

	for _, book := range rsps {
		var pbBookInfo pbcommon.BookInfo
		err := utils.JsonCopy(book, &pbBookInfo)
		if err != nil {
			log.Fatal(err)
		}

		bookMap[book.ID.Hex()] = &pbBookInfo
	}

	return &pb.FindBooksInfoResp{Books: bookMap}, nil
}

func (s *BooksSvc) AddBook(ctx context.Context, book *pbcommon.Book) (*pb.AddBookResp, error) {

	poBook := &po.Book{}
	if err := utils.JsonCopy(book, poBook); err != nil {
		logger.Errorf("BooksSvc.AddBook err:%v \n", err)
	}

	id, err := s.dao.AddBook(ctx, poBook)
	if err != nil {
		return nil, err
	}

	return &pb.AddBookResp{Id: id}, nil
}

func (s *BooksSvc) UpdateBook(ctx context.Context, req *pb.UpdateBookReq) (*pb.NormalResponse, error) {
	poBook := &po.Book{}
	if err := utils.JsonCopy(req.Book, poBook); err != nil {
		logger.Errorf("BooksSvc.AddBook err:%v \n", err)
	}

	bId, err := primitive.ObjectIDFromHex(req.Book.Id)
	if err != nil {
		return nil, err
	}

	poBook.ID = bId

	err = s.dao.UpdateBook(ctx, poBook, req.UpdateField)
	if err != nil {
		return nil, err
	}

	return &pb.NormalResponse{
		Err:     "",
		Message: "OK",
	}, nil
}

func (s *BooksSvc) GetBookCopyrightDesc(ctx context.Context, request *pb.GetBookCopyrightDescRequest) (*pb.GetBookCopyrightDescResponse, error) {
	//TODO implement me
	panic("implement me")
}

func (s *BooksSvc) BatchGetBookCopyrightDesc(ctx context.Context, request *pb.BatchGetBookCopyrightDescRequest) (*pb.BatchGetBookCopyrightDescResponse, error) {
	//TODO implement me
	panic("implement me")
}

func (s *BooksSvc) FindCpSourceByBooks(ctx context.Context, request *pb.FindCpSourceByBooksRequest) (*pb.FindCpSourceByBooksResponse, error) {
	//TODO implement me
	panic("implement me")
}

func (s *BooksSvc) AddCpSource(ctx context.Context, source *pbcommon.CpSource) (*pb.NormalResponse, error) {
	//TODO implement me
	panic("implement me")
}

func (s *BooksSvc) ListCopyrightDesc(ctx context.Context, request *pb.ListCopyrightDescRequest) (*pb.ListCopyrightDescResponse, error) {
	//TODO implement me
	panic("implement me")
}

func (s *BooksSvc) CreateCopyrightDesc(ctx context.Context, request *pb.CreateCopyrightDescRequest) (*pb.Empty, error) {
	//TODO implement me
	panic("implement me")
}

func (s *BooksSvc) UpdateCopyrightDesc(ctx context.Context, request *pb.UpdateCopyrightDescRequest) (*pb.Empty, error) {
	//TODO implement me
	panic("implement me")
}

func (s *BooksSvc) DeleteCopyrightDesc(ctx context.Context, request *pb.DeleteCopyrightDescRequest) (*pb.Empty, error) {
	//TODO implement me
	panic("implement me")
}

func (s *BooksSvc) CheckBookToShelf(ctx context.Context, req *pb.CheckBookToShelfReq) (*pb.CheckBookToShelfRes, error) {
	//TODO implement me
	panic("implement me")
}
