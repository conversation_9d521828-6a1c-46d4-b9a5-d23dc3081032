package service

import (
	"context"
	"fmt"
	"time"

	"creativematrix.com/beyondreading/gen/app/base/payment/conf"
	"creativematrix.com/beyondreading/gen/common/po"
	"creativematrix.com/beyondreading/pkg/logger"
)

type OrderService struct {
	config *conf.Config
}

func NewOrderService(config *conf.Config) *OrderService {
	return &OrderService{
		config: config,
	}
}

// ValidateOrder 验证订单
func (o *OrderService) ValidateOrder(ctx context.Context, order *po.PaymentOrder) error {
	// 验证订单金额
	if order.Amount <= 0 {
		return fmt.Errorf("invalid order amount: %d", order.Amount)
	}

	// 验证货币类型
	if order.Currency == "" {
		order.Currency = o.config.Payment.DefaultCurrency
	}

	// 验证产品ID
	if order.ProductId == "" {
		return fmt.Errorf("product ID is required")
	}

	// 验证支付方式
	if order.PaymentMethod < po.PaymentMethodGooglePay || order.PaymentMethod > po.PaymentMethodWechatPay {
		return fmt.Errorf("invalid payment method: %d", order.PaymentMethod)
	}

	// 验证支付类型
	if order.PaymentType < po.PaymentTypeVipPurchase || order.PaymentType > po.PaymentTypeCoinRecharge {
		return fmt.Errorf("invalid payment type: %d", order.PaymentType)
	}

	return nil
}

// ProcessExpiredOrders 处理过期订单
func (o *OrderService) ProcessExpiredOrders(ctx context.Context, orders []*po.PaymentOrder) error {
	for _, order := range orders {
		if order.Status == po.OrderStatusPending && time.Now().After(order.ExpiredAt) {
			logger.LogInfof("Processing expired order: %s", order.OrderId)
			
			// 更新订单状态为已取消
			// 这里需要调用DAO层更新订单状态
			// 实际实现中需要注入DAO依赖
		}
	}
	
	return nil
}

// CalculateOrderAmount 计算订单金额
func (o *OrderService) CalculateOrderAmount(ctx context.Context, productId string, quantity int32) (int64, error) {
	// 这里应该根据产品ID查询产品价格
	// 实际实现中需要查询产品配置表
	
	// 示例价格映射
	priceMap := map[string]int64{
		"vip_monthly":         999,  // $9.99
		"vip_yearly":          9999, // $99.99
		"monthly_subscription": 599,  // $5.99
		"coins_100":           99,   // $0.99
		"coins_500":           499,  // $4.99
		"coins_1000":          999,  // $9.99
	}
	
	price, exists := priceMap[productId]
	if !exists {
		return 0, fmt.Errorf("unknown product: %s", productId)
	}
	
	return price * int64(quantity), nil
}

// GetOrderStatus 获取订单状态描述
func (o *OrderService) GetOrderStatus(status int32) string {
	switch status {
	case po.OrderStatusPending:
		return "Pending"
	case po.OrderStatusPaid:
		return "Paid"
	case po.OrderStatusCancelled:
		return "Cancelled"
	case po.OrderStatusRefunded:
		return "Refunded"
	case po.OrderStatusFailed:
		return "Failed"
	default:
		return "Unknown"
	}
}

// GetPaymentMethodName 获取支付方式名称
func (o *OrderService) GetPaymentMethodName(method int32) string {
	switch method {
	case po.PaymentMethodGooglePay:
		return "Google Pay"
	case po.PaymentMethodApplePay:
		return "Apple Pay"
	case po.PaymentMethodPaypal:
		return "PayPal"
	case po.PaymentMethodAlipay:
		return "Alipay"
	case po.PaymentMethodWechatPay:
		return "WeChat Pay"
	default:
		return "Unknown"
	}
}

// GetPaymentTypeName 获取支付类型名称
func (o *OrderService) GetPaymentTypeName(paymentType int32) string {
	switch paymentType {
	case po.PaymentTypeVipPurchase:
		return "VIP Purchase"
	case po.PaymentTypeMonthlySubscription:
		return "Monthly Subscription"
	case po.PaymentTypeCoinRecharge:
		return "Coin Recharge"
	default:
		return "Unknown"
	}
}
