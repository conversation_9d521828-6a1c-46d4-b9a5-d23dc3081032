package alipay

import "encoding/json"

const (
	// cReturnURL  = "http://devh5.91quliao.com/money"
	pcReturnURL = "https://www.imkela.com/pay/"
	cFormat     = "2006-01-02 15:04:05"
	//cDomain     = "https://openapi.alipay.com/gateway.do"
	//cSuccess    = "10000"
)

const (
	SIGN_TYPE_RSA  = "RSA"
	SIGN_TYPE_RSA2 = "RSA2"
)

const (
	NOTIFY_TYPE_TRADE  = "trade_status_sync"
	NOTIFY_TYPE_SIGN   = "dut_user_sign"
	NOTIFY_TYPE_UNSIGN = "dut_user_unsign"
)

// ali 产品码
const (
	PCODE_WAP      = "QUICK_WAP_WAY"
	PCODE_CYCLE    = "CYCLE_PAY_AUTH"
	PERPCODE_CYCLE = "CYCLE_PAY_AUTH_P"
	PCODE_PC       = "FAST_INSTANT_TRADE_PAY"
)

type Param interface {
	APIName() string
	BizJSON() string
	Extends() map[string]string
}

func marshal(obj interface{}) string {
	var bytes, err = json.Marshal(obj)
	if err != nil {
		return ""
	}
	return string(bytes)
}
