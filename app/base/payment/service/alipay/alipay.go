package alipay

import (
	"context"
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	"creativematrix.com/beyondreading/gen/app/base/payment/conf"
	"creativematrix.com/beyondreading/gen/app/base/payment/service"
	"creativematrix.com/beyondreading/gen/common/po"
	"creativematrix.com/beyondreading/pkg/logger"
)

func init() {
	service.Register(&Alipay{})
}

// Alipay 支付宝支付服务
type Alipay struct {
	appID      string
	privateKey *rsa.PrivateKey
	publicKey  *rsa.PublicKey
	notifyURL  string
	returnURL  string
	gateway    string
	client     *http.Client
}

// AlipayConfig 支付宝配置
type AlipayConfig struct {
	AppID      string `json:"app_id"`
	PrivateKey string `json:"private_key"`
	PublicKey  string `json:"public_key"`
	NotifyURL  string `json:"notify_url"`
	ReturnURL  string `json:"return_url"`
	Gateway    string `json:"gateway"`
}

// AlipayRequest 支付宝请求
type AlipayRequest struct {
	Method      string `json:"method"`
	AppID       string `json:"app_id"`
	Charset     string `json:"charset"`
	SignType    string `json:"sign_type"`
	Sign        string `json:"sign"`
	Timestamp   string `json:"timestamp"`
	Version     string `json:"version"`
	NotifyURL   string `json:"notify_url,omitempty"`
	ReturnURL   string `json:"return_url,omitempty"`
	BizContent  string `json:"biz_content"`
}

// AlipayResponse 支付宝响应
type AlipayResponse struct {
	Code    string `json:"code"`
	Msg     string `json:"msg"`
	SubCode string `json:"sub_code"`
	SubMsg  string `json:"sub_msg"`
}

// TradeAppPayRequest APP支付请求
type TradeAppPayRequest struct {
	OutTradeNo  string `json:"out_trade_no"`
	TotalAmount string `json:"total_amount"`
	Subject     string `json:"subject"`
	Body        string `json:"body,omitempty"`
	TimeExpire  string `json:"time_expire,omitempty"`
	ProductCode string `json:"product_code"`
}

// TradeRefundRequest 退款请求
type TradeRefundRequest struct {
	OutTradeNo   string `json:"out_trade_no"`
	RefundAmount string `json:"refund_amount"`
	RefundReason string `json:"refund_reason,omitempty"`
	OutRequestNo string `json:"out_request_no,omitempty"`
}

// TradeRefundResponse 退款响应
type TradeRefundResponse struct {
	AlipayResponse
	TradeNo      string `json:"trade_no"`
	OutTradeNo   string `json:"out_trade_no"`
	BuyerLogonID string `json:"buyer_logon_id"`
	RefundFee    string `json:"refund_fee"`
	OutRequestNo string `json:"out_request_no"`
}

// Name 返回支付方式名称
func (a *Alipay) Name() service.PaymentType {
	return service.PaymentTypeAlipay
}

// Init 初始化支付宝服务
func (a *Alipay) Init(cfg *conf.Config) error {
	// 从配置中获取支付宝配置
	alipayConfig := cfg.Alipay
	if alipayConfig == nil {
		return fmt.Errorf("alipay config is nil")
	}

	a.appID = alipayConfig.AppID
	a.notifyURL = alipayConfig.NotifyURL
	a.returnURL = alipayConfig.ReturnURL
	a.gateway = alipayConfig.Gateway
	if a.gateway == "" {
		a.gateway = "https://openapi.alipay.com/gateway.do"
	}

	// 解析私钥
	privateKey, err := parsePrivateKey(alipayConfig.PrivateKey)
	if err != nil {
		return fmt.Errorf("failed to parse private key: %w", err)
	}
	a.privateKey = privateKey

	// 解析公钥
	publicKey, err := parsePublicKey(alipayConfig.PublicKey)
	if err != nil {
		return fmt.Errorf("failed to parse public key: %w", err)
	}
	a.publicKey = publicKey

	a.client = &http.Client{
		Timeout: 30 * time.Second,
	}

	return nil
}

// CreatePayment 创建支付订单
func (a *Alipay) CreatePayment(ctx context.Context, order *po.PaymentOrder, product *po.PaymentProduct, param *service.OrderParam) (*service.PaymentOrder, error) {
	// 构建支付请求
	bizContent := TradeAppPayRequest{
		OutTradeNo:  fmt.Sprintf("%d", order.ID),
		TotalAmount: fmt.Sprintf("%.2f", product.Price),
		Subject:     param.Subject,
		Body:        product.Description,
		TimeExpire:  time.Now().Add(15 * time.Minute).Format("2006-01-02 15:04:05"),
		ProductCode: "QUICK_MSECURITY_PAY",
	}

	bizContentStr, err := json.Marshal(bizContent)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal biz content: %w", err)
	}

	// 构建请求参数
	params := map[string]string{
		"app_id":      a.appID,
		"method":      "alipay.trade.app.pay",
		"charset":     "utf-8",
		"sign_type":   "RSA2",
		"timestamp":   time.Now().Format("2006-01-02 15:04:05"),
		"version":     "1.0",
		"notify_url":  a.notifyURL,
		"biz_content": string(bizContentStr),
	}

	// 生成签名
	sign, err := a.generateSign(params)
	if err != nil {
		return nil, fmt.Errorf("failed to generate sign: %w", err)
	}
	params["sign"] = sign

	// 构建支付字符串
	var payParams []string
	for key, value := range params {
		payParams = append(payParams, fmt.Sprintf("%s=%s", key, url.QueryEscape(value)))
	}
	payString := strings.Join(payParams, "&")

	// 构建返回数据
	paymentOrder := &service.PaymentOrder{
		Type:  0,
		Value: payString,
		Order: map[string]interface{}{
			"appid":       a.appID,
			"outTradeNo":  bizContent.OutTradeNo,
			"totalAmount": bizContent.TotalAmount,
			"subject":     bizContent.Subject,
			"body":        bizContent.Body,
			"notifyUrl":   a.notifyURL,
		},
	}

	return paymentOrder, nil
}

// HandleNotify 处理支付通知
func (a *Alipay) HandleNotify(ctx context.Context, req *http.Request) (*service.NotifyOrder, error) {
	if err := req.ParseForm(); err != nil {
		return nil, fmt.Errorf("failed to parse form: %w", err)
	}

	// 验证签名
	if err := a.verifySign(req.Form); err != nil {
		return nil, fmt.Errorf("failed to verify sign: %w", err)
	}

	// 获取通知参数
	tradeStatus := req.Form.Get("trade_status")
	outTradeNo := req.Form.Get("out_trade_no")
	tradeNo := req.Form.Get("trade_no")

	if outTradeNo == "" || tradeNo == "" {
		return nil, fmt.Errorf("missing required parameters")
	}

	// 解析订单ID
	orderID := outTradeNo // 这里可能需要根据实际情况转换

	notifyOrder := &service.NotifyOrder{
		ID:   0, // 需要从outTradeNo解析
		Type: service.NotifyTypeTrade,
		Data: map[string]interface{}{
			"trade_status": tradeStatus,
			"out_trade_no": outTradeNo,
			"trade_no":     tradeNo,
		},
	}

	// 根据交易状态判断通知类型
	switch tradeStatus {
	case "TRADE_SUCCESS", "TRADE_FINISHED":
		notifyOrder.Type = service.NotifyTypeTrade
	default:
		notifyOrder.Type = service.NotifyTypeTradeErr
	}

	return notifyOrder, nil
}

// AckNotify 确认通知
func (a *Alipay) AckNotify(w http.ResponseWriter, err error) {
	if err != nil {
		logger.LogErrorf("Alipay notify error: %v", err)
		w.Write([]byte("failure"))
	} else {
		w.Write([]byte("success"))
	}
}

// RefundPayment 退款
func (a *Alipay) RefundPayment(ctx context.Context, refund *service.RefundOrder) (*service.RefundResult, error) {
	// 构建退款请求
	bizContent := TradeRefundRequest{
		OutTradeNo:   refund.OrderID,
		RefundAmount: fmt.Sprintf("%.2f", refund.RefundAmount),
		RefundReason: refund.Reason,
		OutRequestNo: refund.RefundID,
	}

	bizContentStr, err := json.Marshal(bizContent)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal biz content: %w", err)
	}

	// 构建请求参数
	params := map[string]string{
		"app_id":      a.appID,
		"method":      "alipay.trade.refund",
		"charset":     "utf-8",
		"sign_type":   "RSA2",
		"timestamp":   time.Now().Format("2006-01-02 15:04:05"),
		"version":     "1.0",
		"biz_content": string(bizContentStr),
	}

	// 生成签名
	sign, err := a.generateSign(params)
	if err != nil {
		return nil, fmt.Errorf("failed to generate sign: %w", err)
	}
	params["sign"] = sign

	// 发送请求
	resp, err := a.doRequest(params)
	if err != nil {
		return nil, fmt.Errorf("failed to do request: %w", err)
	}

	var refundResp TradeRefundResponse
	if err := json.Unmarshal(resp, &refundResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %w", err)
	}

	result := &service.RefundResult{
		Success:      refundResp.Code == "10000",
		RefundID:     refundResp.OutRequestNo,
		RefundAmount: refund.RefundAmount,
		Message:      refundResp.Msg,
	}

	return result, nil
}

// QueryPayment 查询支付状态
func (a *Alipay) QueryPayment(ctx context.Context, orderID string) (map[string]interface{}, error) {
	// 实现支付查询逻辑
	return map[string]interface{}{
		"order_id": orderID,
		"status":   "unknown",
	}, nil
}

// QueryRefund 查询退款状态
func (a *Alipay) QueryRefund(ctx context.Context, refundID string) (map[string]interface{}, error) {
	// 实现退款查询逻辑
	return map[string]interface{}{
		"refund_id": refundID,
		"status":    "unknown",
	}, nil
}

// 辅助函数

// parsePrivateKey 解析私钥
func parsePrivateKey(privateKeyStr string) (*rsa.PrivateKey, error) {
	block, _ := pem.Decode([]byte(privateKeyStr))
	if block == nil {
		return nil, fmt.Errorf("failed to decode private key")
	}

	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		// 尝试PKCS8格式
		key, err := x509.ParsePKCS8PrivateKey(block.Bytes)
		if err != nil {
			return nil, fmt.Errorf("failed to parse private key: %w", err)
		}
		if rsaKey, ok := key.(*rsa.PrivateKey); ok {
			return rsaKey, nil
		}
		return nil, fmt.Errorf("not an RSA private key")
	}

	return privateKey, nil
}

// parsePublicKey 解析公钥
func parsePublicKey(publicKeyStr string) (*rsa.PublicKey, error) {
	block, _ := pem.Decode([]byte(publicKeyStr))
	if block == nil {
		return nil, fmt.Errorf("failed to decode public key")
	}

	publicKey, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse public key: %w", err)
	}

	if rsaKey, ok := publicKey.(*rsa.PublicKey); ok {
		return rsaKey, nil
	}

	return nil, fmt.Errorf("not an RSA public key")
}

// generateSign 生成签名
func (a *Alipay) generateSign(params map[string]string) (string, error) {
	// 排序参数
	var keys []string
	for key := range params {
		if key != "sign" && params[key] != "" {
			keys = append(keys, key)
		}
	}
	sort.Strings(keys)

	// 构建签名字符串
	var signStr []string
	for _, key := range keys {
		signStr = append(signStr, fmt.Sprintf("%s=%s", key, params[key]))
	}
	signString := strings.Join(signStr, "&")

	// 计算签名
	hash := sha256.Sum256([]byte(signString))
	signature, err := rsa.SignPKCS1v15(rand.Reader, a.privateKey, crypto.SHA256, hash[:])
	if err != nil {
		return "", err
	}

	return base64.StdEncoding.EncodeToString(signature), nil
}

// verifySign 验证签名
func (a *Alipay) verifySign(params url.Values) error {
	sign := params.Get("sign")
	if sign == "" {
		return fmt.Errorf("missing sign parameter")
	}

	// 构建验证字符串
	var keys []string
	for key := range params {
		if key != "sign" && key != "sign_type" && params.Get(key) != "" {
			keys = append(keys, key)
		}
	}
	sort.Strings(keys)

	var signStr []string
	for _, key := range keys {
		signStr = append(signStr, fmt.Sprintf("%s=%s", key, params.Get(key)))
	}
	signString := strings.Join(signStr, "&")

	// 验证签名
	signBytes, err := base64.StdEncoding.DecodeString(sign)
	if err != nil {
		return fmt.Errorf("failed to decode sign: %w", err)
	}

	hash := sha256.Sum256([]byte(signString))
	err = rsa.VerifyPKCS1v15(a.publicKey, crypto.SHA256, hash[:], signBytes)
	if err != nil {
		return fmt.Errorf("failed to verify sign: %w", err)
	}

	return nil
}

// doRequest 发送请求
func (a *Alipay) doRequest(params map[string]string) ([]byte, error) {
	// 构建请求参数
	values := url.Values{}
	for key, value := range params {
		values.Set(key, value)
	}

	// 发送POST请求
	resp, err := a.client.PostForm(a.gateway, values)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	return ioutil.ReadAll(resp.Body)
}
