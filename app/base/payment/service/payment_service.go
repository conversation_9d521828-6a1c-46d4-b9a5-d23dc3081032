package service

import (
	"context"
	"creativematrix.com/beyondreading/app/api/payment/model/po"
	"creativematrix.com/beyondreading/app/base/payment/conf"
	"fmt"
)

type PaymentService struct {
	config    *conf.Config
	googlePay *GooglePayService
	applePay  *ApplePayService
	paypal    *PayPalService
	alipay    *AlipayService
	wechatPay *WechatPayService
}

func NewPaymentService(config *conf.Config) *PaymentService {
	return &PaymentService{
		config:    config,
		googlePay: NewGooglePayService(config),
		applePay:  NewApplePayService(config),
		paypal:    NewPayPalService(config),
		alipay:    NewAlipayService(config),
		wechatPay: NewWechatPayService(config),
	}
}

// CreatePayment 创建支付
func (p *PaymentService) CreatePayment(ctx context.Context, order *po.PaymentOrder) (*PaymentResult, error) {
	switch order.PaymentMethod {
	case po.PaymentMethodGooglePay:
		return p.googlePay.CreatePayment(ctx, order)
	case po.PaymentMethodApplePay:
		return p.applePay.CreatePayment(ctx, order)
	case po.PaymentMethodPaypal:
		return p.paypal.CreatePayment(ctx, order)
	case po.PaymentMethodAlipay:
		return p.alipay.CreatePayment(ctx, order)
	case po.PaymentMethodWechatPay:
		return p.wechatPay.CreatePayment(ctx, order)
	default:
		return nil, fmt.Errorf("unsupported payment method: %d", order.PaymentMethod)
	}
}

// VerifyCallback 验证支付回调
func (p *PaymentService) VerifyCallback(ctx context.Context, callback *po.PaymentCallback) (*CallbackResult, error) {
	switch callback.PaymentMethod {
	case po.PaymentMethodGooglePay:
		return p.googlePay.VerifyCallback(ctx, callback)
	case po.PaymentMethodApplePay:
		return p.applePay.VerifyCallback(ctx, callback)
	case po.PaymentMethodPaypal:
		return p.paypal.VerifyCallback(ctx, callback)
	case po.PaymentMethodAlipay:
		return p.alipay.VerifyCallback(ctx, callback)
	case po.PaymentMethodWechatPay:
		return p.wechatPay.VerifyCallback(ctx, callback)
	default:
		return nil, fmt.Errorf("unsupported payment method: %d", callback.PaymentMethod)
	}
}

// ProcessRefund 处理退款
func (p *PaymentService) ProcessRefund(ctx context.Context, refund *po.PaymentRefund) (*RefundResult, error) {
	switch refund.PaymentMethod {
	case po.PaymentMethodGooglePay:
		return p.googlePay.ProcessRefund(ctx, refund)
	case po.PaymentMethodApplePay:
		return p.applePay.ProcessRefund(ctx, refund)
	case po.PaymentMethodPaypal:
		return p.paypal.ProcessRefund(ctx, refund)
	case po.PaymentMethodAlipay:
		return p.alipay.ProcessRefund(ctx, refund)
	case po.PaymentMethodWechatPay:
		return p.wechatPay.ProcessRefund(ctx, refund)
	default:
		return nil, fmt.Errorf("unsupported payment method: %d", refund.PaymentMethod)
	}
}

// GetSupportedMethods 获取支持的支付方式
func (p *PaymentService) GetSupportedMethods(platform, region string) []*PaymentMethodInfo {
	var methods []*PaymentMethodInfo

	// Google Pay
	if p.isMethodSupported(po.PaymentMethodGooglePay, platform, region) {
		methods = append(methods, &PaymentMethodInfo{
			Method:              po.PaymentMethodGooglePay,
			Name:                "google_pay",
			DisplayName:         "Google Pay",
			Icon:                "/icons/google_pay.png",
			Enabled:             true,
			SupportedCurrencies: []string{"USD", "EUR", "GBP", "JPY"},
		})
	}

	// Apple Pay
	if p.isMethodSupported(po.PaymentMethodApplePay, platform, region) {
		methods = append(methods, &PaymentMethodInfo{
			Method:              po.PaymentMethodApplePay,
			Name:                "apple_pay",
			DisplayName:         "Apple Pay",
			Icon:                "/icons/apple_pay.png",
			Enabled:             true,
			SupportedCurrencies: []string{"USD", "EUR", "GBP", "JPY"},
		})
	}

	// PayPal
	if p.isMethodSupported(po.PaymentMethodPaypal, platform, region) {
		methods = append(methods, &PaymentMethodInfo{
			Method:              po.PaymentMethodPaypal,
			Name:                "paypal",
			DisplayName:         "PayPal",
			Icon:                "/icons/paypal.png",
			Enabled:             true,
			SupportedCurrencies: []string{"USD", "EUR", "GBP", "JPY", "CAD", "AUD"},
		})
	}

	// Alipay
	if p.isMethodSupported(po.PaymentMethodAlipay, platform, region) {
		methods = append(methods, &PaymentMethodInfo{
			Method:              po.PaymentMethodAlipay,
			Name:                "alipay",
			DisplayName:         "支付宝",
			Icon:                "/icons/alipay.png",
			Enabled:             true,
			SupportedCurrencies: []string{"CNY", "USD"},
		})
	}

	// WeChat Pay
	if p.isMethodSupported(po.PaymentMethodWechatPay, platform, region) {
		methods = append(methods, &PaymentMethodInfo{
			Method:              po.PaymentMethodWechatPay,
			Name:                "wechat_pay",
			DisplayName:         "微信支付",
			Icon:                "/icons/wechat_pay.png",
			Enabled:             true,
			SupportedCurrencies: []string{"CNY"},
		})
	}

	return methods
}

// isMethodSupported 检查支付方式是否支持
func (p *PaymentService) isMethodSupported(method int32, platform, region string) bool {
	switch method {
	case po.PaymentMethodGooglePay:
		return platform == "android" || platform == "web"
	case po.PaymentMethodApplePay:
		return platform == "ios" || platform == "web"
	case po.PaymentMethodPaypal:
		return true // 支持所有平台
	case po.PaymentMethodAlipay:
		return region == "CN" || region == "HK" || region == "TW"
	case po.PaymentMethodWechatPay:
		return region == "CN"
	default:
		return false
	}
}

// 支付结果结构
type PaymentResult struct {
	Success     bool              `json:"success"`
	PaymentUrl  string            `json:"paymentUrl"`
	PaymentData string            `json:"paymentData"`
	Message     string            `json:"message"`
	Metadata    map[string]string `json:"metadata"`
}

// 回调结果结构
type CallbackResult struct {
	Valid         bool              `json:"valid"`
	Status        int32             `json:"status"`
	TransactionId string            `json:"transactionId"`
	Amount        int64             `json:"amount"`
	Currency      string            `json:"currency"`
	Message       string            `json:"message"`
	Metadata      map[string]string `json:"metadata"`
}

// 退款结果结构
//type RefundResult struct {
//	Success  bool              `json:"success"`
//	RefundId string            `json:"refundId"`
//	Status   int32             `json:"status"`
//	Message  string            `json:"message"`
//	Metadata map[string]string `json:"metadata"`
//}

// 支付方式信息结构
type PaymentMethodInfo struct {
	Method              int32             `json:"method"`
	Name                string            `json:"name"`
	DisplayName         string            `json:"displayName"`
	Icon                string            `json:"icon"`
	Enabled             bool              `json:"enabled"`
	SupportedCurrencies []string          `json:"supportedCurrencies"`
	Config              map[string]string `json:"config"`
}
