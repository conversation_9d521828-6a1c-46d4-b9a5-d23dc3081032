package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"creativematrix.com/beyondreading/app/base/payment/conf"
	"creativematrix.com/beyondreading/app/common/po"
	"creativematrix.com/beyondreading/pkg/logger"
)

type PayPalService struct {
	config *conf.Config
	client *http.Client
}

type PayPalAccessTokenResponse struct {
	Scope       string `json:"scope"`
	AccessToken string `json:"access_token"`
	TokenType   string `json:"token_type"`
	AppId       string `json:"app_id"`
	ExpiresIn   int    `json:"expires_in"`
	Nonce       string `json:"nonce"`
}

type PayPalOrder struct {
	Id     string `json:"id"`
	Status string `json:"status"`
	Links  []struct {
		Href   string `json:"href"`
		Rel    string `json:"rel"`
		Method string `json:"method"`
	} `json:"links"`
}

type PayPalOrderRequest struct {
	Intent             string                    `json:"intent"`
	PurchaseUnits      []PayPalPurchaseUnit      `json:"purchase_units"`
	PaymentSource      *PayPalPaymentSource      `json:"payment_source,omitempty"`
	ApplicationContext *PayPalApplicationContext `json:"application_context,omitempty"`
}

type PayPalPurchaseUnit struct {
	ReferenceId string       `json:"reference_id"`
	Amount      PayPalAmount `json:"amount"`
	Description string       `json:"description,omitempty"`
}

type PayPalAmount struct {
	CurrencyCode string `json:"currency_code"`
	Value        string `json:"value"`
}

type PayPalPaymentSource struct {
	PayPal *PayPalPaymentSourcePayPal `json:"paypal,omitempty"`
}

type PayPalPaymentSourcePayPal struct {
	ExperienceContext *PayPalExperienceContext `json:"experience_context,omitempty"`
}

type PayPalExperienceContext struct {
	PaymentMethodPreference string `json:"payment_method_preference,omitempty"`
	BrandName               string `json:"brand_name,omitempty"`
	Locale                  string `json:"locale,omitempty"`
	LandingPage             string `json:"landing_page,omitempty"`
	ShippingPreference      string `json:"shipping_preference,omitempty"`
	UserAction              string `json:"user_action,omitempty"`
	ReturnUrl               string `json:"return_url,omitempty"`
	CancelUrl               string `json:"cancel_url,omitempty"`
}

type PayPalApplicationContext struct {
	ReturnUrl string `json:"return_url,omitempty"`
	CancelUrl string `json:"cancel_url,omitempty"`
}

func NewPayPalService(config *conf.Config) *PayPalService {
	return &PayPalService{
		config: config,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// CreatePayment 创建PayPal支付
func (p *PayPalService) CreatePayment(ctx context.Context, order *po.PaymentOrder) (*PaymentResult, error) {
	// 获取访问令牌
	accessToken, err := p.getAccessToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get access token: %w", err)
	}

	// 创建PayPal订单
	paypalOrder, err := p.createPayPalOrder(ctx, accessToken, order)
	if err != nil {
		return nil, fmt.Errorf("failed to create PayPal order: %w", err)
	}

	// 获取支付链接
	var approveLink string
	for _, link := range paypalOrder.Links {
		if link.Rel == "approve" {
			approveLink = link.Href
			break
		}
	}

	return &PaymentResult{
		Success:     true,
		PaymentUrl:  approveLink,
		PaymentData: paypalOrder.Id,
		Message:     "PayPal order created successfully",
		Metadata: map[string]string{
			"paypalOrderId": paypalOrder.Id,
			"status":        paypalOrder.Status,
		},
	}, nil
}

// VerifyCallback 验证PayPal支付回调
func (p *PayPalService) VerifyCallback(ctx context.Context, callback *po.PaymentCallback) (*CallbackResult, error) {
	paypalOrderId, exists := callback.CallbackData["paypalOrderId"]
	if !exists {
		return &CallbackResult{
			Valid:   false,
			Message: "Missing PayPal order ID",
		}, nil
	}

	// 获取访问令牌
	accessToken, err := p.getAccessToken(ctx)
	if err != nil {
		logger.LogErrorf("Failed to get PayPal access token: %v", err)
		return &CallbackResult{
			Valid:   false,
			Message: fmt.Sprintf("Failed to get access token: %v", err),
		}, nil
	}

	// 验证PayPal订单
	isValid, orderDetails, err := p.verifyPayPalOrder(ctx, accessToken, paypalOrderId)
	if err != nil {
		logger.LogErrorf("Failed to verify PayPal order: %v", err)
		return &CallbackResult{
			Valid:   false,
			Message: fmt.Sprintf("Verification failed: %v", err),
		}, nil
	}

	if !isValid {
		return &CallbackResult{
			Valid:   false,
			Message: "Invalid PayPal order",
		}, nil
	}

	// 确定支付状态
	status := po.OrderStatusFailed
	if orderDetails.Status == "COMPLETED" {
		status = po.OrderStatusPaid
	} else if orderDetails.Status == "APPROVED" {
		status = po.OrderStatusPending
	}

	return &CallbackResult{
		Valid:         true,
		Status:        int32(status),
		TransactionId: paypalOrderId,
		Amount:        callback.Amount,
		Currency:      callback.Currency,
		Message:       "Verification successful",
		Metadata: map[string]string{
			"paypalOrderId": paypalOrderId,
			"status":        orderDetails.Status,
		},
	}, nil
}

// ProcessRefund 处理PayPal退款
func (p *PayPalService) ProcessRefund(ctx context.Context, refund *po.PaymentRefund) (*RefundResult, error) {
	// 获取访问令牌
	accessToken, err := p.getAccessToken(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get access token: %w", err)
	}

	// 处理退款
	success, refundId, err := p.processPayPalRefund(ctx, accessToken, refund)
	if err != nil {
		logger.LogErrorf("Failed to process PayPal refund: %v", err)
		return &RefundResult{
			Success:  false,
			RefundId: refund.RefundId,
			Status:   po.RefundStatusFailed,
			Message:  fmt.Sprintf("Refund failed: %v", err),
		}, nil
	}

	status := po.RefundStatusSuccess
	if !success {
		status = po.RefundStatusFailed
	}

	return &RefundResult{
		Success:  success,
		RefundId: refundId,
		Status:   int32(status),
		Message:  "Refund processed successfully",
		Metadata: map[string]string{
			"paypalRefundId": refundId,
		},
	}, nil
}

// getAccessToken 获取PayPal访问令牌
func (p *PayPalService) getAccessToken(ctx context.Context) (string, error) {
	apiURL := "https://api.paypal.com/v1/oauth2/token"
	if p.config.PayPal.Environment == "sandbox" {
		apiURL = "https://api.sandbox.paypal.com/v1/oauth2/token"
	}

	data := url.Values{}
	data.Set("grant_type", "client_credentials")

	req, err := http.NewRequestWithContext(ctx, "POST", apiURL, strings.NewReader(data.Encode()))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Accept", "application/json")
	req.Header.Set("Accept-Language", "en_US")
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.SetBasicAuth(p.config.PayPal.ClientId, p.config.PayPal.ClientSecret)

	resp, err := p.client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("API request failed with status: %d", resp.StatusCode)
	}

	var tokenResp PayPalAccessTokenResponse
	if err := json.NewDecoder(resp.Body).Decode(&tokenResp); err != nil {
		return "", fmt.Errorf("failed to decode response: %w", err)
	}

	return tokenResp.AccessToken, nil
}

// createPayPalOrder 创建PayPal订单
func (p *PayPalService) createPayPalOrder(ctx context.Context, accessToken string, order *po.PaymentOrder) (*PayPalOrder, error) {
	apiURL := "https://api.paypal.com/v2/checkout/orders"
	if p.config.PayPal.Environment == "sandbox" {
		apiURL = "https://api.sandbox.paypal.com/v2/checkout/orders"
	}

	// 构建订单请求
	orderReq := PayPalOrderRequest{
		Intent: "CAPTURE",
		PurchaseUnits: []PayPalPurchaseUnit{
			{
				ReferenceId: order.OrderId,
				Amount: PayPalAmount{
					CurrencyCode: order.Currency,
					Value:        fmt.Sprintf("%.2f", float64(order.Amount)/100),
				},
				Description: order.Description,
			},
		},
		ApplicationContext: &PayPalApplicationContext{
			ReturnUrl: order.ReturnUrl,
			CancelUrl: order.CancelUrl,
		},
	}

	requestData, err := json.Marshal(orderReq)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	req, err := http.NewRequestWithContext(ctx, "POST", apiURL, bytes.NewReader(requestData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+accessToken)
	req.Header.Set("PayPal-Request-Id", order.OrderId)

	resp, err := p.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusCreated {
		return nil, fmt.Errorf("API request failed with status: %d", resp.StatusCode)
	}

	var paypalOrder PayPalOrder
	if err := json.NewDecoder(resp.Body).Decode(&paypalOrder); err != nil {
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return &paypalOrder, nil
}

// verifyPayPalOrder 验证PayPal订单
func (p *PayPalService) verifyPayPalOrder(ctx context.Context, accessToken, orderId string) (bool, *PayPalOrder, error) {
	apiURL := fmt.Sprintf("https://api.paypal.com/v2/checkout/orders/%s", orderId)
	if p.config.PayPal.Environment == "sandbox" {
		apiURL = fmt.Sprintf("https://api.sandbox.paypal.com/v2/checkout/orders/%s", orderId)
	}

	req, err := http.NewRequestWithContext(ctx, "GET", apiURL, nil)
	if err != nil {
		return false, nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+accessToken)

	resp, err := p.client.Do(req)
	if err != nil {
		return false, nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return false, nil, fmt.Errorf("API request failed with status: %d", resp.StatusCode)
	}

	var order PayPalOrder
	if err := json.NewDecoder(resp.Body).Decode(&order); err != nil {
		return false, nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return true, &order, nil
}

// processPayPalRefund 处理PayPal退款
func (p *PayPalService) processPayPalRefund(ctx context.Context, accessToken string, refund *po.PaymentRefund) (bool, string, error) {
	// PayPal退款需要capture ID，这里简化处理
	// 实际实现需要根据订单获取capture ID

	logger.LogInfof("PayPal refund request: orderId=%s, refundId=%s, amount=%d",
		refund.OrderId, refund.RefundId, refund.RefundAmount)

	// 这里返回成功，实际需要调用PayPal退款API
	return true, refund.RefundId, nil
}
