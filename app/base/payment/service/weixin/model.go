package weixin

import (
	"fmt"
	"strconv"
	"time"

	"creativematrix.com/beyondreading/app/api/payment/pay"
)

type QueryContract struct {
	ContractID string
}

func (q QueryContract) APIURL() string {
	return "https://api.mch.weixin.qq.com/papay/querycontract"
}

func (q QueryContract) Payload() pay.Payload {
	return pay.Payload{
		"contract_id": q.ContractID,
		"version":     "1.0",
	}
}

type UnifiedOrder struct {
	Body           string
	OutTradeNo     string
	TotalFee       int
	SpbillCreateIP string
	TimeStart      string
	TimeExpire     string
	NotifyURL      string
	TradeType      string
	NonceStr       string
	Openid         string
}

func (u UnifiedOrder) APIURL() string {
	return "https://api.mch.weixin.qq.com/pay/unifiedorder"
}

func (u UnifiedOrder) Payload() pay.Payload {
	return pay.Payload{
		"body":             u.Body,
		"out_trade_no":     u.OutTradeNo,
		"total_fee":        strconv.Itoa(u.TotalFee),
		"spbill_create_ip": u.SpbillCreateIP,
		"time_start":       u.TimeStart,
		"time_expire":      u.TimeExpire,
		"notify_url":       u.NotifyURL,
		"trade_type":       u.TradeType,
		"nonce_str":        u.NonceStr,
		"openid":           u.Openid,
	}
}

type ContractOrder struct {
	UnifiedOrder
	ContractMcId   string
	ContractAppId  string
	PlanID         int
	ContractCode   string
	RequestSerial  int64
	DisplayAccount string
}

func (c ContractOrder) APIURL() string {
	return "https://api.mch.weixin.qq.com/pay/contractorder"
}

func (c ContractOrder) Payload() pay.Payload {
	p := c.UnifiedOrder.Payload()
	p.Set("contract_mchid", c.ContractMcId)
	p.Set("contract_appid", c.ContractAppId)
	p.Set("plan_id", strconv.Itoa(c.PlanID))
	p.Set("contract_code", c.ContractCode)
	p.Set("request_serial", strconv.FormatInt(c.RequestSerial, 10))
	p.Set("contract_display_account", c.DisplayAccount)
	p.Set("contract_notify_url", c.NotifyURL)
	return p
}

type PappayApply struct {
	UnifiedOrder
	ContractID string
}

func (p PappayApply) APIURL() string {
	return "https://api.mch.weixin.qq.com/pay/pappayapply"
}
func (p PappayApply) Payload() pay.Payload {
	pl := p.UnifiedOrder.Payload()
	pl.Set("contract_id", p.ContractID)
	return pl
}

type DeleteContract struct {
	ContractID string
}

func (d DeleteContract) APIURL() string {
	return "https://api.mch.weixin.qq.com/papay/deletecontract"
}

func (d DeleteContract) Payload() pay.Payload {
	return pay.Payload{
		"contract_id":                 d.ContractID,
		"contract_termination_remark": "用户主动解约",
		"version":                     "1.0",
	}
}

type Paporderquery struct {
	OutTradeNo string
	NonceStr   string
}

func (p Paporderquery) APIURL() string {
	return "https://api.mch.weixin.qq.com/pay/paporderquery"
}

func (p Paporderquery) Payload() pay.Payload {
	return pay.Payload{
		"out_trade_no": p.OutTradeNo,
		"nonce_str":    p.NonceStr,
	}
}

type PreenTrustWeb struct {
	PlanID         int
	ContractCode   string
	RequestSerial  int64
	DisplayAccount string
	NotifyURL      string
}

func (p PreenTrustWeb) APIURL() string {
	return "https://api.mch.weixin.qq.com/papay/preentrustweb"
}

func (p PreenTrustWeb) Payload() pay.Payload {
	return pay.Payload{
		"plan_id":                  strconv.Itoa(p.PlanID),
		"contract_code":            p.ContractCode,
		"request_serial":           strconv.FormatInt(p.RequestSerial, 10),
		"contract_display_account": p.DisplayAccount,
		"notify_url":               p.NotifyURL,
		"version":                  "1.0",
		"timestamp":                fmt.Sprintf("%d", time.Now().Unix()),
	}
}
