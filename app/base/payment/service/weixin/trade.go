package weixin

type PreOrderRes struct {
	PrepayID           string `xml:"prepay_id"`
	TradeType          string `xml:"trade_type"`
	CodeURL            string `xml:"code_url"`
	MWebURL            string `xml:"mweb_url"`
	PreEntrustwebID    string `xml:"pre_entrustweb_id"`
	ContractResultCode string `xml:"contract_result_code"`
}

type QueryContractRes struct {
	ContractID    string `xml:"contract_id"`
	ContractCode  string `xml:"contract_code"`
	ContractState int    `xml:"contract_state"`
}

type PappayApplyRes struct {
}

type TradeNotification struct {
	ReturnCode         string `xml:"return_code"`
	ReturnMsg          string `xml:"return_msg"`
	AppID              string `xml:"appid"`
	MchID              string `xml:"mch_id"`
	DeviceInfo         string `xml:"device_info"`
	NonceStr           string `xml:"nonce_str"`
	Sign               string `xml:"sign"`
	SignType           string `xml:"sign_type"`
	ResultCode         string `xml:"result_code"`
	ErrCode            string `xml:"err_code"`
	ErrCodeDes         string `xml:"err_code_des"`
	OpenID             string `xml:"openid"`
	IsSubscribe        string `xml:"is_subscribe"`
	TradeType          string `xml:"trade_type"`
	BankType           string `xml:"bank_type"`
	TotalFee           int    `xml:"total_fee"`
	SettlementTotalFee int    `xml:"settlement_total_fee"`
	FeeType            string `xml:"fee_type"`
	CashFee            int    `xml:"cash_fee"`
	CashFeeType        string `xml:"cash_fee_type"`
	CouponFee          int    `xml:"coupon_fee"`
	CouponCount        int    `xml:"coupon_count"`
	TransactionID      string `xml:"transaction_id"`
	OutTradeNo         string `xml:"out_trade_no"`
	Attach             string `xml:"attach"`
	TimeEnd            string `xml:"time_end"`
	TradeState         string `xml:"trade_state"`
}

type ContractNotification struct {
	MchID        string `xml:"mch_id"`
	ContractCode string `xml:"contract_code"`
	PlanID       string `xml:"plan_id"`
	Openid       string `xml:"openid"`
	ChangeType   string `xml:"change_type"`
	OperateTime  string `xml:"operate_time"`
	ContractID   string `xml:"contract_id"`
}
