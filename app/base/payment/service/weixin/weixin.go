package weixin

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/xml"
	"fmt"
	"io/ioutil"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"creativematrix.com/beyondreading/gen/app/base/payment/conf"
	"creativematrix.com/beyondreading/gen/app/base/payment/service"
	"creativematrix.com/beyondreading/gen/common/po"
	"creativematrix.com/beyondreading/pkg/logger"
)

func init() {
	service.Register(&Weixin{})
}

// Weixin 微信支付服务
type Weixin struct {
	appID     string
	mchID     string
	apiKey    string
	notifyURL string
	gateway   string
	client    *http.Client
}

// WeixinConfig 微信支付配置
type WeixinConfig struct {
	AppID     string `json:"app_id"`
	MchID     string `json:"mch_id"`
	APIKey    string `json:"api_key"`
	NotifyURL string `json:"notify_url"`
	Gateway   string `json:"gateway"`
}

// UnifiedOrderRequest 统一下单请求
type UnifiedOrderRequest struct {
	XMLName        xml.Name `xml:"xml"`
	AppID          string   `xml:"appid"`
	MchID          string   `xml:"mch_id"`
	NonceStr       string   `xml:"nonce_str"`
	Sign           string   `xml:"sign"`
	Body           string   `xml:"body"`
	OutTradeNo     string   `xml:"out_trade_no"`
	TotalFee       int      `xml:"total_fee"`
	SpbillCreateIP string   `xml:"spbill_create_ip"`
	NotifyURL      string   `xml:"notify_url"`
	TradeType      string   `xml:"trade_type"`
	OpenID         string   `xml:"openid,omitempty"`
	ProductID      string   `xml:"product_id,omitempty"`
}

// UnifiedOrderResponse 统一下单响应
type UnifiedOrderResponse struct {
	XMLName    xml.Name `xml:"xml"`
	ReturnCode string   `xml:"return_code"`
	ReturnMsg  string   `xml:"return_msg"`
	AppID      string   `xml:"appid"`
	MchID      string   `xml:"mch_id"`
	NonceStr   string   `xml:"nonce_str"`
	Sign       string   `xml:"sign"`
	ResultCode string   `xml:"result_code"`
	PrepayID   string   `xml:"prepay_id"`
	TradeType  string   `xml:"trade_type"`
	CodeURL    string   `xml:"code_url"`
	ErrCode    string   `xml:"err_code"`
	ErrCodeDes string   `xml:"err_code_des"`
}

// RefundRequest 退款请求
type RefundRequest struct {
	XMLName       xml.Name `xml:"xml"`
	AppID         string   `xml:"appid"`
	MchID         string   `xml:"mch_id"`
	NonceStr      string   `xml:"nonce_str"`
	Sign          string   `xml:"sign"`
	OutTradeNo    string   `xml:"out_trade_no"`
	OutRefundNo   string   `xml:"out_refund_no"`
	TotalFee      int      `xml:"total_fee"`
	RefundFee     int      `xml:"refund_fee"`
	RefundDesc    string   `xml:"refund_desc,omitempty"`
}

// RefundResponse 退款响应
type RefundResponse struct {
	XMLName       xml.Name `xml:"xml"`
	ReturnCode    string   `xml:"return_code"`
	ReturnMsg     string   `xml:"return_msg"`
	ResultCode    string   `xml:"result_code"`
	AppID         string   `xml:"appid"`
	MchID         string   `xml:"mch_id"`
	NonceStr      string   `xml:"nonce_str"`
	Sign          string   `xml:"sign"`
	TransactionID string   `xml:"transaction_id"`
	OutTradeNo    string   `xml:"out_trade_no"`
	OutRefundNo   string   `xml:"out_refund_no"`
	RefundID      string   `xml:"refund_id"`
	RefundFee     int      `xml:"refund_fee"`
	ErrCode       string   `xml:"err_code"`
	ErrCodeDes    string   `xml:"err_code_des"`
}

// NotifyRequest 支付通知请求
type NotifyRequest struct {
	XMLName       xml.Name `xml:"xml"`
	AppID         string   `xml:"appid"`
	MchID         string   `xml:"mch_id"`
	NonceStr      string   `xml:"nonce_str"`
	Sign          string   `xml:"sign"`
	ResultCode    string   `xml:"result_code"`
	OpenID        string   `xml:"openid"`
	TradeType     string   `xml:"trade_type"`
	BankType      string   `xml:"bank_type"`
	TotalFee      int      `xml:"total_fee"`
	CashFee       int      `xml:"cash_fee"`
	TransactionID string   `xml:"transaction_id"`
	OutTradeNo    string   `xml:"out_trade_no"`
	TimeEnd       string   `xml:"time_end"`
}

// Name 返回支付方式名称
func (w *Weixin) Name() service.PaymentType {
	return service.PaymentTypeWeixin
}

// Init 初始化微信支付服务
func (w *Weixin) Init(cfg *conf.Config) error {
	// 从配置中获取微信支付配置
	weixinConfig := cfg.Weixin
	if weixinConfig == nil {
		return fmt.Errorf("weixin config is nil")
	}

	w.appID = weixinConfig.AppID
	w.mchID = weixinConfig.MchID
	w.apiKey = weixinConfig.APIKey
	w.notifyURL = weixinConfig.NotifyURL
	w.gateway = weixinConfig.Gateway
	if w.gateway == "" {
		w.gateway = "https://api.mch.weixin.qq.com"
	}

	w.client = &http.Client{
		Timeout: 30 * time.Second,
	}

	return nil
}

// CreatePayment 创建支付订单
func (w *Weixin) CreatePayment(ctx context.Context, order *po.PaymentOrder, product *po.PaymentProduct, param *service.OrderParam) (*service.PaymentOrder, error) {
	// 构建统一下单请求
	req := &UnifiedOrderRequest{
		AppID:          w.appID,
		MchID:          w.mchID,
		NonceStr:       generateNonceStr(),
		Body:           param.Subject,
		OutTradeNo:     fmt.Sprintf("%d", order.ID),
		TotalFee:       int(product.Price * 100), // 转换为分
		SpbillCreateIP: param.SpbillCreateIp,
		NotifyURL:      w.notifyURL,
		TradeType:      string(param.TradeType),
		OpenID:         param.OpenID,
		ProductID:      param.ProductID,
	}

	// 生成签名
	req.Sign = w.generateSign(req)

	// 发送请求
	resp, err := w.unifiedOrder(req)
	if err != nil {
		return nil, fmt.Errorf("failed to unified order: %w", err)
	}

	if resp.ReturnCode != "SUCCESS" {
		return nil, fmt.Errorf("unified order failed: %s", resp.ReturnMsg)
	}

	if resp.ResultCode != "SUCCESS" {
		return nil, fmt.Errorf("unified order result failed: %s", resp.ErrCodeDes)
	}

	// 构建支付参数
	var paymentOrder *service.PaymentOrder
	switch param.TradeType {
	case service.TradeTypeApp:
		paymentOrder = w.buildAppPayment(resp)
	case service.TradeTypeJSAPI:
		paymentOrder = w.buildJSAPIPayment(resp)
	case service.TradeTypeNative:
		paymentOrder = w.buildNativePayment(resp)
	default:
		return nil, fmt.Errorf("unsupported trade type: %s", param.TradeType)
	}

	return paymentOrder, nil
}

// HandleNotify 处理支付通知
func (w *Weixin) HandleNotify(ctx context.Context, req *http.Request) (*service.NotifyOrder, error) {
	body, err := ioutil.ReadAll(req.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read body: %w", err)
	}

	var notify NotifyRequest
	if err := xml.Unmarshal(body, &notify); err != nil {
		return nil, fmt.Errorf("failed to unmarshal notify: %w", err)
	}

	// 验证签名
	if !w.verifySign(&notify) {
		return nil, fmt.Errorf("invalid sign")
	}

	// 构建通知订单
	notifyOrder := &service.NotifyOrder{
		ID:   0, // 需要从out_trade_no解析
		Type: service.NotifyTypeTrade,
		Data: map[string]interface{}{
			"result_code":    notify.ResultCode,
			"out_trade_no":   notify.OutTradeNo,
			"transaction_id": notify.TransactionID,
			"total_fee":      notify.TotalFee,
			"time_end":       notify.TimeEnd,
		},
	}

	// 根据结果码判断通知类型
	if notify.ResultCode == "SUCCESS" {
		notifyOrder.Type = service.NotifyTypeTrade
	} else {
		notifyOrder.Type = service.NotifyTypeTradeErr
	}

	return notifyOrder, nil
}

// AckNotify 确认通知
func (w *Weixin) AckNotify(writer http.ResponseWriter, err error) {
	if err != nil {
		logger.LogErrorf("Weixin notify error: %v", err)
		writer.Write([]byte("<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[ERROR]]></return_msg></xml>"))
	} else {
		writer.Write([]byte("<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>"))
	}
}

// RefundPayment 退款
func (w *Weixin) RefundPayment(ctx context.Context, refund *service.RefundOrder) (*service.RefundResult, error) {
	// 构建退款请求
	req := &RefundRequest{
		AppID:       w.appID,
		MchID:       w.mchID,
		NonceStr:    generateNonceStr(),
		OutTradeNo:  refund.OrderID,
		OutRefundNo: refund.RefundID,
		TotalFee:    int(refund.TotalAmount * 100),
		RefundFee:   int(refund.RefundAmount * 100),
		RefundDesc:  refund.Reason,
	}

	// 生成签名
	req.Sign = w.generateRefundSign(req)

	// 发送退款请求
	resp, err := w.refund(req)
	if err != nil {
		return nil, fmt.Errorf("failed to refund: %w", err)
	}

	result := &service.RefundResult{
		Success:      resp.ReturnCode == "SUCCESS" && resp.ResultCode == "SUCCESS",
		RefundID:     resp.RefundID,
		RefundAmount: float64(resp.RefundFee) / 100,
		Message:      resp.ReturnMsg,
	}

	if !result.Success && resp.ErrCodeDes != "" {
		result.Message = resp.ErrCodeDes
	}

	return result, nil
}

// QueryPayment 查询支付状态
func (w *Weixin) QueryPayment(ctx context.Context, orderID string) (map[string]interface{}, error) {
	// 实现支付查询逻辑
	return map[string]interface{}{
		"order_id": orderID,
		"status":   "unknown",
	}, nil
}

// QueryRefund 查询退款状态
func (w *Weixin) QueryRefund(ctx context.Context, refundID string) (map[string]interface{}, error) {
	// 实现退款查询逻辑
	return map[string]interface{}{
		"refund_id": refundID,
		"status":    "unknown",
	}, nil
}

// 辅助函数

// generateNonceStr 生成随机字符串
func generateNonceStr() string {
	return fmt.Sprintf("%d", time.Now().UnixNano())
}

// generateSign 生成签名
func (w *Weixin) generateSign(req *UnifiedOrderRequest) string {
	params := map[string]string{
		"appid":            req.AppID,
		"mch_id":           req.MchID,
		"nonce_str":        req.NonceStr,
		"body":             req.Body,
		"out_trade_no":     req.OutTradeNo,
		"total_fee":        strconv.Itoa(req.TotalFee),
		"spbill_create_ip": req.SpbillCreateIP,
		"notify_url":       req.NotifyURL,
		"trade_type":       req.TradeType,
	}

	if req.OpenID != "" {
		params["openid"] = req.OpenID
	}
	if req.ProductID != "" {
		params["product_id"] = req.ProductID
	}

	return w.sign(params)
}

// generateRefundSign 生成退款签名
func (w *Weixin) generateRefundSign(req *RefundRequest) string {
	params := map[string]string{
		"appid":         req.AppID,
		"mch_id":        req.MchID,
		"nonce_str":     req.NonceStr,
		"out_trade_no":  req.OutTradeNo,
		"out_refund_no": req.OutRefundNo,
		"total_fee":     strconv.Itoa(req.TotalFee),
		"refund_fee":    strconv.Itoa(req.RefundFee),
	}

	if req.RefundDesc != "" {
		params["refund_desc"] = req.RefundDesc
	}

	return w.sign(params)
}

// sign 签名算法
func (w *Weixin) sign(params map[string]string) string {
	// 排序参数
	var keys []string
	for key := range params {
		if params[key] != "" {
			keys = append(keys, key)
		}
	}
	sort.Strings(keys)

	// 构建签名字符串
	var signStr []string
	for _, key := range keys {
		signStr = append(signStr, fmt.Sprintf("%s=%s", key, params[key]))
	}
	signString := strings.Join(signStr, "&") + "&key=" + w.apiKey

	// 计算MD5
	hash := md5.Sum([]byte(signString))
	return fmt.Sprintf("%X", hash)
}

// verifySign 验证签名
func (w *Weixin) verifySign(notify *NotifyRequest) bool {
	params := map[string]string{
		"appid":          notify.AppID,
		"mch_id":         notify.MchID,
		"nonce_str":      notify.NonceStr,
		"result_code":    notify.ResultCode,
		"openid":         notify.OpenID,
		"trade_type":     notify.TradeType,
		"bank_type":      notify.BankType,
		"total_fee":      strconv.Itoa(notify.TotalFee),
		"cash_fee":       strconv.Itoa(notify.CashFee),
		"transaction_id": notify.TransactionID,
		"out_trade_no":   notify.OutTradeNo,
		"time_end":       notify.TimeEnd,
	}

	expectedSign := w.sign(params)
	return expectedSign == notify.Sign
}

// unifiedOrder 统一下单
func (w *Weixin) unifiedOrder(req *UnifiedOrderRequest) (*UnifiedOrderResponse, error) {
	xmlData, err := xml.Marshal(req)
	if err != nil {
		return nil, err
	}

	resp, err := w.client.Post(w.gateway+"/pay/unifiedorder", "application/xml", bytes.NewReader(xmlData))
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var response UnifiedOrderResponse
	if err := xml.Unmarshal(body, &response); err != nil {
		return nil, err
	}

	return &response, nil
}

// refund 退款
func (w *Weixin) refund(req *RefundRequest) (*RefundResponse, error) {
	xmlData, err := xml.Marshal(req)
	if err != nil {
		return nil, err
	}

	resp, err := w.client.Post(w.gateway+"/secapi/pay/refund", "application/xml", bytes.NewReader(xmlData))
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var response RefundResponse
	if err := xml.Unmarshal(body, &response); err != nil {
		return nil, err
	}

	return &response, nil
}

// buildAppPayment 构建APP支付参数
func (w *Weixin) buildAppPayment(resp *UnifiedOrderResponse) *service.PaymentOrder {
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	nonceStr := generateNonceStr()

	params := map[string]string{
		"appid":     w.appID,
		"partnerid": w.mchID,
		"prepayid":  resp.PrepayID,
		"package":   "Sign=WXPay",
		"noncestr":  nonceStr,
		"timestamp": timestamp,
	}

	sign := w.sign(params)

	return &service.PaymentOrder{
		Type:  0,
		Value: "",
		Order: map[string]interface{}{
			"appid":     w.appID,
			"partnerid": w.mchID,
			"prepayid":  resp.PrepayID,
			"package":   "Sign=WXPay",
			"noncestr":  nonceStr,
			"timestamp": timestamp,
			"sign":      sign,
		},
	}
}

// buildJSAPIPayment 构建JSAPI支付参数
func (w *Weixin) buildJSAPIPayment(resp *UnifiedOrderResponse) *service.PaymentOrder {
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	nonceStr := generateNonceStr()

	params := map[string]string{
		"appId":     w.appID,
		"timeStamp": timestamp,
		"nonceStr":  nonceStr,
		"package":   "prepay_id=" + resp.PrepayID,
		"signType":  "MD5",
	}

	sign := w.sign(params)

	return &service.PaymentOrder{
		Type:  1,
		Value: "",
		Order: map[string]interface{}{
			"appId":     w.appID,
			"timeStamp": timestamp,
			"nonceStr":  nonceStr,
			"package":   "prepay_id=" + resp.PrepayID,
			"signType":  "MD5",
			"paySign":   sign,
		},
	}
}

// buildNativePayment 构建Native支付参数
func (w *Weixin) buildNativePayment(resp *UnifiedOrderResponse) *service.PaymentOrder {
	return &service.PaymentOrder{
		Type:  2,
		Value: resp.CodeURL,
		Order: map[string]interface{}{
			"code_url": resp.CodeURL,
		},
	}
}
