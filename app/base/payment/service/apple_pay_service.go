package service

import (
	"context"
	"creativematrix.com/beyondreading/app/base/payment/conf"
	"creativematrix.com/beyondreading/app/common/po"
	"creativematrix.com/beyondreading/pkg/logger"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"
)

type ApplePayService struct {
	config *conf.Config
}

type AppleReceiptData struct {
	ReceiptType                string               `json:"receipt_type"`
	AdamId                     int64                `json:"adam_id"`
	AppItemId                  int64                `json:"app_item_id"`
	BundleId                   string               `json:"bundle_id"`
	ApplicationVersion         string               `json:"application_version"`
	DownloadId                 int64                `json:"download_id"`
	VersionExternalIdentifier  int64                `json:"version_external_identifier"`
	ReceiptCreationDate        string               `json:"receipt_creation_date"`
	ReceiptCreationDateMs      string               `json:"receipt_creation_date_ms"`
	ReceiptCreationDatePst     string               `json:"receipt_creation_date_pst"`
	RequestDate                string               `json:"request_date"`
	RequestDateMs              string               `json:"request_date_ms"`
	RequestDatePst             string               `json:"request_date_pst"`
	OriginalPurchaseDate       string               `json:"original_purchase_date"`
	OriginalPurchaseDateMs     string               `json:"original_purchase_date_ms"`
	OriginalPurchaseDatePst    string               `json:"original_purchase_date_pst"`
	OriginalApplicationVersion string               `json:"original_application_version"`
	InApp                      []AppleInAppPurchase `json:"in_app"`
}

type AppleInAppPurchase struct {
	Quantity                string `json:"quantity"`
	ProductId               string `json:"product_id"`
	TransactionId           string `json:"transaction_id"`
	OriginalTransactionId   string `json:"original_transaction_id"`
	PurchaseDate            string `json:"purchase_date"`
	PurchaseDateMs          string `json:"purchase_date_ms"`
	PurchaseDatePst         string `json:"purchase_date_pst"`
	OriginalPurchaseDate    string `json:"original_purchase_date"`
	OriginalPurchaseDateMs  string `json:"original_purchase_date_ms"`
	OriginalPurchaseDatePst string `json:"original_purchase_date_pst"`
	ExpiresDate             string `json:"expires_date,omitempty"`
	ExpiresDateMs           string `json:"expires_date_ms,omitempty"`
	ExpiresDatePst          string `json:"expires_date_pst,omitempty"`
	WebOrderLineItemId      string `json:"web_order_line_item_id,omitempty"`
	IsTrialPeriod           string `json:"is_trial_period,omitempty"`
	IsInIntroOfferPeriod    string `json:"is_in_intro_offer_period,omitempty"`
}

type AppleVerifyResponse struct {
	Status             int                  `json:"status"`
	Environment        string               `json:"environment"`
	Receipt            AppleReceiptData     `json:"receipt"`
	LatestReceiptInfo  []AppleInAppPurchase `json:"latest_receipt_info,omitempty"`
	LatestReceipt      string               `json:"latest_receipt,omitempty"`
	PendingRenewalInfo []interface{}        `json:"pending_renewal_info,omitempty"`
	IsRetryable        bool                 `json:"is-retryable,omitempty"`
}

func NewApplePayService(config *conf.Config) *ApplePayService {
	return &ApplePayService{
		config: config,
	}
}

// CreatePayment 创建Apple Pay支付
func (a *ApplePayService) CreatePayment(ctx context.Context, order *po.PaymentOrder) (*PaymentResult, error) {
	// Apple Pay支付通常在客户端完成，这里主要是生成支付数据
	paymentData := map[string]interface{}{
		"productId": order.ProductId,
		"orderId":   order.OrderId,
		"bundleId":  a.config.ApplePay.BundleId,
		"amount":    order.Amount,
		"currency":  order.Currency,
	}

	paymentDataJson, err := json.Marshal(paymentData)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal payment data: %w", err)
	}

	return &PaymentResult{
		Success:     true,
		PaymentUrl:  "", // Apple Pay不需要支付URL
		PaymentData: string(paymentDataJson),
		Message:     "Payment data generated successfully",
		Metadata: map[string]string{
			"bundleId":    a.config.ApplePay.BundleId,
			"productId":   order.ProductId,
			"environment": a.config.ApplePay.Environment,
		},
	}, nil
}

// VerifyCallback 验证Apple Pay支付回调
func (a *ApplePayService) VerifyCallback(ctx context.Context, callback *po.PaymentCallback) (*CallbackResult, error) {
	receiptData, exists := callback.CallbackData["receiptData"]
	if !exists {
		return &CallbackResult{
			Valid:   false,
			Message: "Missing receipt data",
		}, nil
	}

	// 验证收据
	isValid, receiptInfo, err := a.verifyReceipt(ctx, receiptData)
	if err != nil {
		logger.LogErrorf("Failed to verify Apple receipt: %v", err)
		return &CallbackResult{
			Valid:   false,
			Message: fmt.Sprintf("Verification failed: %v", err),
		}, nil
	}

	if !isValid {
		return &CallbackResult{
			Valid:   false,
			Message: "Invalid receipt",
		}, nil
	}

	// 查找对应的内购项目
	var targetPurchase *AppleInAppPurchase
	productId, _ := callback.CallbackData["productId"]

	for _, purchase := range receiptInfo.Receipt.InApp {
		if purchase.ProductId == productId {
			targetPurchase = &purchase
			break
		}
	}

	if targetPurchase == nil {
		return &CallbackResult{
			Valid:   false,
			Message: "Product not found in receipt",
		}, nil
	}

	return &CallbackResult{
		Valid:         true,
		Status:        po.OrderStatusPaid,
		TransactionId: targetPurchase.TransactionId,
		Amount:        callback.Amount,
		Currency:      callback.Currency,
		Message:       "Verification successful",
		Metadata: map[string]string{
			"transactionId":         targetPurchase.TransactionId,
			"originalTransactionId": targetPurchase.OriginalTransactionId,
			"productId":             targetPurchase.ProductId,
			"purchaseDate":          targetPurchase.PurchaseDate,
			"quantity":              targetPurchase.Quantity,
		},
	}, nil
}

// ProcessRefund 处理Apple Pay退款
func (a *ApplePayService) ProcessRefund(ctx context.Context, refund *po.PaymentRefund) (*RefundResult, error) {
	// Apple Pay退款通常通过App Store Connect手动处理
	// 这里可以记录退款请求，但实际退款需要手动操作

	logger.LogInfof("Apple Pay refund request: orderId=%s, refundId=%s, amount=%d",
		refund.OrderId, refund.RefundId, refund.RefundAmount)

	return &RefundResult{
		Success:  false, // Apple Pay不支持自动退款
		RefundId: refund.RefundId,
		Status:   po.RefundStatusPending,
		Message:  "Apple Pay refunds must be processed manually through App Store Connect",
		Metadata: map[string]string{
			"note": "Manual refund required",
		},
	}, nil
}

// verifyReceipt 验证Apple收据
func (a *ApplePayService) verifyReceipt(ctx context.Context, receiptData string) (bool, *AppleVerifyResponse, error) {
	// 构建验证请求
	verifyURL := "https://buy.itunes.apple.com/verifyReceipt"
	if a.config.ApplePay.Environment == "sandbox" {
		verifyURL = "https://sandbox.itunes.apple.com/verifyReceipt"
	}

	requestBody := map[string]interface{}{
		"receipt-data": receiptData,
		"password":     a.config.ApplePay.SharedSecret,
	}

	requestData, err := json.Marshal(requestBody)
	if err != nil {
		return false, nil, fmt.Errorf("failed to marshal request: %w", err)
	}

	// 发送验证请求
	req, err := http.NewRequestWithContext(ctx, "POST", verifyURL,
		strings.NewReader(string(requestData)))
	if err != nil {
		return false, nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	reqClient := &http.Client{
		Timeout: 30 * time.Second,
	}
	// 发送请求
	resp, err := reqClient.Do(req)
	if err != nil {
		return false, nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 解析响应
	var verifyResp AppleVerifyResponse
	if err := json.NewDecoder(resp.Body).Decode(&verifyResp); err != nil {
		return false, nil, fmt.Errorf("failed to decode response: %w", err)
	}

	// 检查状态码
	if verifyResp.Status != 0 {
		return false, &verifyResp, fmt.Errorf("verification failed with status: %d", verifyResp.Status)
	}

	// 验证Bundle ID
	if verifyResp.Receipt.BundleId != a.config.ApplePay.BundleId {
		return false, &verifyResp, fmt.Errorf("bundle id mismatch: expected %s, got %s",
			a.config.ApplePay.BundleId, verifyResp.Receipt.BundleId)
	}

	return true, &verifyResp, nil
}
