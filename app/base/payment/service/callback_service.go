package service

import (
	"context"
	"fmt"
	"time"

	"creativematrix.com/beyondreading/app/base/payment/conf"
	"creativematrix.com/beyondreading/app/common/po"
	"creativematrix.com/beyondreading/pkg/logger"
)

type CallbackService struct {
	config *conf.Config
}

func NewCallbackService(config *conf.Config) *CallbackService {
	return &CallbackService{
		config: config,
	}
}

// ProcessCallback 处理支付回调
func (c *CallbackService) ProcessCallback(ctx context.Context, callback *po.PaymentCallback) error {
	// 验证回调数据
	if err := c.validateCallback(callback); err != nil {
		return fmt.Errorf("invalid callback: %w", err)
	}

	// 检查是否已处理
	if callback.Processed {
		logger.LogWarnf("Callback already processed: orderId=%s, transactionId=%s",
			callback.OrderId, callback.TransactionId)
		return nil
	}

	// 处理回调逻辑
	logger.LogInfof("Processing payment callback: orderId=%s, method=%d, status=%d",
		callback.OrderId, callback.PaymentMethod, callback.Status)

	// 标记为已处理
	callback.Processed = true
	callback.ProcessedAt = &time.Time{}
	*callback.ProcessedAt = time.Now()

	return nil
}

// validateCallback 验证回调数据
func (c *CallbackService) validateCallback(callback *po.PaymentCallback) error {
	if callback.OrderId == "" {
		return fmt.Errorf("order ID is required")
	}

	if callback.PaymentMethod < po.PaymentMethodGooglePay || callback.PaymentMethod > po.PaymentMethodWechatPay {
		return fmt.Errorf("invalid payment method: %d", callback.PaymentMethod)
	}

	if callback.Amount <= 0 {
		return fmt.Errorf("invalid amount: %d", callback.Amount)
	}

	if callback.Currency == "" {
		return fmt.Errorf("currency is required")
	}

	return nil
}

// RetryFailedCallbacks 重试失败的回调
func (c *CallbackService) RetryFailedCallbacks(ctx context.Context, callbacks []*po.PaymentCallback) error {
	for _, callback := range callbacks {
		if !callback.Processed {
			logger.LogInfof("Retrying failed callback: orderId=%s", callback.OrderId)

			// 重新处理回调
			if err := c.ProcessCallback(ctx, callback); err != nil {
				logger.LogErrorf("Failed to retry callback: %v", err)
				continue
			}
		}
	}

	return nil
}

// GetCallbackStatus 获取回调状态描述
func (c *CallbackService) GetCallbackStatus(processed bool) string {
	if processed {
		return "Processed"
	}
	return "Pending"
}

// IsCallbackExpired 检查回调是否过期
func (c *CallbackService) IsCallbackExpired(callback *po.PaymentCallback) bool {
	// 回调超过24小时未处理视为过期
	expireTime := callback.CreatedAt.Add(24 * time.Hour)
	return time.Now().After(expireTime)
}

// GetCallbackRetryCount 获取回调重试次数
func (c *CallbackService) GetCallbackRetryCount(callback *po.PaymentCallback) int32 {
	// 从回调数据中获取重试次数
	if retryCountStr, exists := callback.CallbackData["retry_count"]; exists {
		// 这里需要转换字符串为数字
		// 简化处理，返回默认值
		_ = retryCountStr
		return 0
	}
	return 0
}

// ShouldRetryCallback 判断是否应该重试回调
func (c *CallbackService) ShouldRetryCallback(callback *po.PaymentCallback) bool {
	// 检查重试次数
	retryCount := c.GetCallbackRetryCount(callback)
	if retryCount >= c.config.Payment.CallbackRetryTimes {
		return false
	}

	// 检查是否过期
	if c.IsCallbackExpired(callback) {
		return false
	}

	// 检查是否已处理
	if callback.Processed {
		return false
	}

	return true
}

// FormatCallbackData 格式化回调数据
func (c *CallbackService) FormatCallbackData(callback *po.PaymentCallback) map[string]interface{} {
	return map[string]interface{}{
		"orderId":       callback.OrderId,
		"paymentMethod": callback.PaymentMethod,
		"transactionId": callback.TransactionId,
		"status":        callback.Status,
		"amount":        callback.Amount,
		"currency":      callback.Currency,
		"processed":     callback.Processed,
		"createdAt":     callback.CreatedAt,
		"processedAt":   callback.ProcessedAt,
		"callbackData":  callback.CallbackData,
	}
}

// LogCallback 记录回调日志
func (c *CallbackService) LogCallback(callback *po.PaymentCallback, action string, result string) {
	logger.LogInfof("Payment callback %s: orderId=%s, method=%d, action=%s, result=%s",
		action, callback.OrderId, callback.PaymentMethod, action, result)
}
