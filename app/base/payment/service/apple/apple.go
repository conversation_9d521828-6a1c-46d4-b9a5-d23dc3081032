package apple

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"

	"creativematrix.com/beyondreading/gen/app/base/payment/conf"
	"creativematrix.com/beyondreading/gen/app/base/payment/service"
	"creativematrix.com/beyondreading/gen/common/po"
	"creativematrix.com/beyondreading/pkg/logger"
)

func init() {
	service.Register(&Apple{})
}

// Apple Apple支付服务
type Apple struct {
	bundleID     string
	sharedSecret string
	environment  string // sandbox 或 production
	client       *http.Client
}

// AppleConfig Apple支付配置
type AppleConfig struct {
	BundleID     string `json:"bundle_id"`
	SharedSecret string `json:"shared_secret"`
	Environment  string `json:"environment"`
}

// ReceiptVerifyRequest 收据验证请求
type ReceiptVerifyRequest struct {
	ReceiptData             string `json:"receipt-data"`
	Password                string `json:"password,omitempty"`
	ExcludeOldTransactions  bool   `json:"exclude-old-transactions,omitempty"`
}

// ReceiptVerifyResponse 收据验证响应
type ReceiptVerifyResponse struct {
	Status             int                `json:"status"`
	Environment        string             `json:"environment"`
	Receipt            Receipt            `json:"receipt"`
	LatestReceiptInfo  []InAppPurchase    `json:"latest_receipt_info"`
	PendingRenewalInfo []RenewalInfo      `json:"pending_renewal_info"`
	IsRetryable        bool               `json:"is-retryable"`
}

// Receipt 收据信息
type Receipt struct {
	ReceiptType                string          `json:"receipt_type"`
	AdamID                     int64           `json:"adam_id"`
	AppItemID                  int64           `json:"app_item_id"`
	BundleID                   string          `json:"bundle_id"`
	ApplicationVersion         string          `json:"application_version"`
	DownloadID                 int64           `json:"download_id"`
	VersionExternalIdentifier  int64           `json:"version_external_identifier"`
	ReceiptCreationDate        string          `json:"receipt_creation_date"`
	ReceiptCreationDateMS      string          `json:"receipt_creation_date_ms"`
	ReceiptCreationDatePST     string          `json:"receipt_creation_date_pst"`
	RequestDate                string          `json:"request_date"`
	RequestDateMS              string          `json:"request_date_ms"`
	RequestDatePST             string          `json:"request_date_pst"`
	OriginalPurchaseDate       string          `json:"original_purchase_date"`
	OriginalPurchaseDateMS     string          `json:"original_purchase_date_ms"`
	OriginalPurchaseDatePST    string          `json:"original_purchase_date_pst"`
	OriginalApplicationVersion string          `json:"original_application_version"`
	InApp                      []InAppPurchase `json:"in_app"`
}

// InAppPurchase 内购信息
type InAppPurchase struct {
	Quantity                string `json:"quantity"`
	ProductID               string `json:"product_id"`
	TransactionID           string `json:"transaction_id"`
	OriginalTransactionID   string `json:"original_transaction_id"`
	PurchaseDate            string `json:"purchase_date"`
	PurchaseDateMS          string `json:"purchase_date_ms"`
	PurchaseDatePST         string `json:"purchase_date_pst"`
	OriginalPurchaseDate    string `json:"original_purchase_date"`
	OriginalPurchaseDateMS  string `json:"original_purchase_date_ms"`
	OriginalPurchaseDatePST string `json:"original_purchase_date_pst"`
	ExpiresDate             string `json:"expires_date"`
	ExpiresDateMS           string `json:"expires_date_ms"`
	ExpiresDatePST          string `json:"expires_date_pst"`
	WebOrderLineItemID      string `json:"web_order_line_item_id"`
	IsTrialPeriod           string `json:"is_trial_period"`
	IsInIntroOfferPeriod    string `json:"is_in_intro_offer_period"`
}

// RenewalInfo 续费信息
type RenewalInfo struct {
	ExpirationIntent   string `json:"expiration_intent"`
	AutoRenewProductID string `json:"auto_renew_product_id"`
	RetryFlag          string `json:"retry_flag"`
	AutoRenewStatus    string `json:"auto_renew_status"`
	PriceConsentStatus string `json:"price_consent_status"`
	ProductID          string `json:"product_id"`
}

// Name 返回支付方式名称
func (a *Apple) Name() service.PaymentType {
	return service.PaymentTypeApple
}

// Init 初始化Apple支付服务
func (a *Apple) Init(cfg *conf.Config) error {
	// 从配置中获取Apple支付配置
	appleConfig := cfg.Apple
	if appleConfig == nil {
		return fmt.Errorf("apple config is nil")
	}

	a.bundleID = appleConfig.BundleID
	a.sharedSecret = appleConfig.SharedSecret
	a.environment = appleConfig.Environment
	if a.environment == "" {
		a.environment = "sandbox"
	}

	a.client = &http.Client{
		Timeout: 30 * time.Second,
	}

	return nil
}

// CreatePayment 创建支付订单
func (a *Apple) CreatePayment(ctx context.Context, order *po.PaymentOrder, product *po.PaymentProduct, param *service.OrderParam) (*service.PaymentOrder, error) {
	// Apple支付不需要预创建订单，直接返回产品信息
	paymentOrder := &service.PaymentOrder{
		Type:  0,
		Value: "",
		Order: map[string]interface{}{
			"product_id":     product.ProductId,
			"bundle_id":      a.bundleID,
			"environment":    a.environment,
			"order_id":       fmt.Sprintf("%d", order.ID),
		},
	}

	return paymentOrder, nil
}

// HandleNotify 处理支付通知
func (a *Apple) HandleNotify(ctx context.Context, req *http.Request) (*service.NotifyOrder, error) {
	body, err := ioutil.ReadAll(req.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read body: %w", err)
	}

	var notifyData map[string]interface{}
	if err := json.Unmarshal(body, &notifyData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal notify data: %w", err)
	}

	// 获取收据数据
	receiptData, ok := notifyData["receipt-data"].(string)
	if !ok {
		return nil, fmt.Errorf("missing receipt-data")
	}

	// 验证收据
	verifyResp, err := a.verifyReceipt(receiptData)
	if err != nil {
		return nil, fmt.Errorf("failed to verify receipt: %w", err)
	}

	if verifyResp.Status != 0 {
		return nil, fmt.Errorf("receipt verification failed with status: %d", verifyResp.Status)
	}

	// 构建通知订单
	notifyOrder := &service.NotifyOrder{
		ID:   0, // 需要从业务逻辑中解析
		Type: service.NotifyTypeTrade,
		Data: map[string]interface{}{
			"receipt":     verifyResp.Receipt,
			"environment": verifyResp.Environment,
			"status":      verifyResp.Status,
		},
	}

	return notifyOrder, nil
}

// AckNotify 确认通知
func (a *Apple) AckNotify(w http.ResponseWriter, err error) {
	if err != nil {
		logger.LogErrorf("Apple notify error: %v", err)
		w.WriteHeader(http.StatusBadRequest)
		w.Write([]byte(`{"status": "error"}`))
	} else {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status": "success"}`))
	}
}

// RefundPayment 退款
func (a *Apple) RefundPayment(ctx context.Context, refund *service.RefundOrder) (*service.RefundResult, error) {
	// Apple支付的退款需要通过App Store Connect后台处理
	// 这里只是记录退款请求
	result := &service.RefundResult{
		Success:      false,
		RefundID:     refund.RefundID,
		RefundAmount: refund.RefundAmount,
		Message:      "Apple refund requires manual processing through App Store Connect",
	}

	return result, nil
}

// QueryPayment 查询支付状态
func (a *Apple) QueryPayment(ctx context.Context, orderID string) (map[string]interface{}, error) {
	// Apple支付查询需要通过收据验证
	return map[string]interface{}{
		"order_id": orderID,
		"status":   "unknown",
		"message":  "Use receipt verification to check payment status",
	}, nil
}

// QueryRefund 查询退款状态
func (a *Apple) QueryRefund(ctx context.Context, refundID string) (map[string]interface{}, error) {
	// Apple退款查询需要通过App Store Connect
	return map[string]interface{}{
		"refund_id": refundID,
		"status":    "unknown",
		"message":   "Check refund status through App Store Connect",
	}, nil
}

// VerifyReceipt 验证收据
func (a *Apple) VerifyReceipt(ctx context.Context, receiptData string) (*ReceiptVerifyResponse, error) {
	return a.verifyReceipt(receiptData)
}

// 辅助函数

// verifyReceipt 验证收据
func (a *Apple) verifyReceipt(receiptData string) (*ReceiptVerifyResponse, error) {
	req := &ReceiptVerifyRequest{
		ReceiptData:            receiptData,
		Password:               a.sharedSecret,
		ExcludeOldTransactions: true,
	}

	reqData, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}

	// 选择验证URL
	var verifyURL string
	if a.environment == "production" {
		verifyURL = "https://buy.itunes.apple.com/verifyReceipt"
	} else {
		verifyURL = "https://sandbox.itunes.apple.com/verifyReceipt"
	}

	// 发送验证请求
	resp, err := a.client.Post(verifyURL, "application/json", bytes.NewReader(reqData))
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var verifyResp ReceiptVerifyResponse
	if err := json.Unmarshal(body, &verifyResp); err != nil {
		return nil, err
	}

	// 如果是生产环境但返回21007错误（沙盒收据），尝试沙盒验证
	if verifyResp.Status == 21007 && a.environment == "production" {
		sandboxURL := "https://sandbox.itunes.apple.com/verifyReceipt"
		resp, err := a.client.Post(sandboxURL, "application/json", bytes.NewReader(reqData))
		if err != nil {
			return nil, err
		}
		defer resp.Body.Close()

		body, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			return nil, err
		}

		if err := json.Unmarshal(body, &verifyResp); err != nil {
			return nil, err
		}
	}

	return &verifyResp, nil
}
