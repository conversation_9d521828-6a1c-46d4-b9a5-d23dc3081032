package service

import (
	"context"
	"fmt"
	"time"

	"creativematrix.com/beyondreading/app/base/payment/conf"
	"creativematrix.com/beyondreading/app/common/po"
	"creativematrix.com/beyondreading/pkg/logger"
)

type RefundService struct {
	config *conf.Config
}

func NewRefundService(config *conf.Config) *RefundService {
	return &RefundService{
		config: config,
	}
}

// ValidateRefund 验证退款请求
func (r *RefundService) ValidateRefund(ctx context.Context, refund *po.PaymentRefund, order *po.PaymentOrder) error {
	// 验证订单状态
	if order.Status != po.OrderStatusPaid {
		return fmt.Errorf("order is not paid, cannot refund")
	}

	// 验证退款金额
	if refund.RefundAmount <= 0 {
		return fmt.Errorf("invalid refund amount: %d", refund.RefundAmount)
	}

	if refund.RefundAmount > order.Amount {
		return fmt.Errorf("refund amount cannot exceed order amount")
	}

	// 验证退款时间限制
	if r.isRefundExpired(order) {
		return fmt.Errorf("refund period has expired")
	}

	// 验证退款原因
	if refund.Reason == "" {
		return fmt.Errorf("refund reason is required")
	}

	return nil
}

// isRefundExpired 检查退款是否过期
func (r *RefundService) isRefundExpired(order *po.PaymentOrder) bool {
	if order.PaidAt == nil {
		return true
	}

	// 退款期限为支付后指定天数内
	expireTime := order.PaidAt.AddDate(0, 0, int(r.config.Payment.RefundTimeoutDays))
	return time.Now().After(expireTime)
}

// ProcessRefund 处理退款
func (r *RefundService) ProcessRefund(ctx context.Context, refund *po.PaymentRefund) error {
	logger.LogInfof("Processing refund: refundId=%s, orderId=%s, amount=%d",
		refund.RefundId, refund.OrderId, refund.RefundAmount)

	// 更新退款状态
	refund.Status = po.RefundStatusPending
	refund.CreatedAt = time.Now()
	refund.UpdatedAt = refund.CreatedAt

	return nil
}

// GetRefundStatus 获取退款状态描述
func (r *RefundService) GetRefundStatus(status int32) string {
	switch status {
	case po.RefundStatusPending:
		return "Pending"
	case po.RefundStatusSuccess:
		return "Success"
	case po.RefundStatusFailed:
		return "Failed"
	case po.RefundStatusCancelled:
		return "Cancelled"
	default:
		return "Unknown"
	}
}

// CalculateRefundFee 计算退款手续费
func (r *RefundService) CalculateRefundFee(refundAmount int64, paymentMethod int32) int64 {
	// 不同支付方式的手续费率不同
	var feeRate float64

	switch paymentMethod {
	case po.PaymentMethodGooglePay:
		feeRate = 0.0 // Google Play不收取退款手续费
	case po.PaymentMethodApplePay:
		feeRate = 0.0 // Apple Pay不收取退款手续费
	case po.PaymentMethodPaypal:
		feeRate = 0.029 // PayPal收取2.9%手续费
	case po.PaymentMethodAlipay:
		feeRate = 0.006 // 支付宝收取0.6%手续费
	case po.PaymentMethodWechatPay:
		feeRate = 0.006 // 微信支付收取0.6%手续费
	default:
		feeRate = 0.0
	}

	return int64(float64(refundAmount) * feeRate)
}

// GetRefundableAmount 获取可退款金额
func (r *RefundService) GetRefundableAmount(order *po.PaymentOrder, existingRefunds []*po.PaymentRefund) int64 {
	totalRefunded := int64(0)

	// 计算已退款金额
	for _, refund := range existingRefunds {
		if refund.Status == po.RefundStatusSuccess {
			totalRefunded += refund.RefundAmount
		}
	}

	return order.Amount - totalRefunded
}

// IsPartialRefundAllowed 检查是否允许部分退款
func (r *RefundService) IsPartialRefundAllowed(paymentMethod int32) bool {
	switch paymentMethod {
	case po.PaymentMethodGooglePay:
		return false // Google Play不支持部分退款
	case po.PaymentMethodApplePay:
		return false // Apple Pay不支持部分退款
	case po.PaymentMethodPaypal:
		return true // PayPal支持部分退款
	case po.PaymentMethodAlipay:
		return true // 支付宝支持部分退款
	case po.PaymentMethodWechatPay:
		return true // 微信支付支持部分退款
	default:
		return false
	}
}

// GenerateRefundReason 生成退款原因
func (r *RefundService) GenerateRefundReason(refundType string) string {
	switch refundType {
	case "user_request":
		return "User requested refund"
	case "duplicate_payment":
		return "Duplicate payment"
	case "service_unavailable":
		return "Service unavailable"
	case "technical_issue":
		return "Technical issue"
	default:
		return "Refund requested"
	}
}

// ValidateRefundWindow 验证退款时间窗口
func (r *RefundService) ValidateRefundWindow(order *po.PaymentOrder) error {
	if order.PaidAt == nil {
		return fmt.Errorf("order is not paid")
	}

	// 检查是否在退款期限内
	if r.isRefundExpired(order) {
		return fmt.Errorf("refund period has expired (limit: %d days)", r.config.Payment.RefundTimeoutDays)
	}

	return nil
}

// FormatRefundData 格式化退款数据
func (r *RefundService) FormatRefundData(refund *po.PaymentRefund) map[string]interface{} {
	return map[string]interface{}{
		"refundId":      refund.RefundId,
		"orderId":       refund.OrderId,
		"userId":        refund.UserId,
		"paymentMethod": refund.PaymentMethod,
		"refundAmount":  refund.RefundAmount,
		"currency":      refund.Currency,
		"reason":        refund.Reason,
		"status":        refund.Status,
		"createdAt":     refund.CreatedAt,
		"updatedAt":     refund.UpdatedAt,
		"processedAt":   refund.ProcessedAt,
	}
}

// LogRefund 记录退款日志
func (r *RefundService) LogRefund(refund *po.PaymentRefund, action string, result string) {
	logger.LogInfof("Payment refund %s: refundId=%s, orderId=%s, action=%s, result=%s",
		action, refund.RefundId, refund.OrderId, action, result)
}
