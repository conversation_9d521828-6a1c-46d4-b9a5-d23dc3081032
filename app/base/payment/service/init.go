package service

import (
	"fmt"

	"creativematrix.com/beyondreading/app/base/payment/conf"
	"creativematrix.com/beyondreading/pkg/logger"
)

// PaymentServiceManager 支付服务管理器
type PaymentServiceManager struct {
	services map[PaymentType]Payment
	config   *conf.Config
}

// NewPaymentServiceManager 创建支付服务管理器
func NewPaymentServiceManager(cfg *conf.Config) *PaymentServiceManager {
	manager := &PaymentServiceManager{
		services: make(map[PaymentType]Payment),
		config:   cfg,
	}

	// 初始化所有已注册的支付服务
	for paymentType, service := range paymentServices {
		if err := service.Init(cfg); err != nil {
			logger.LogErrorf("Failed to initialize payment service %s: %v", paymentType.String(), err)
			continue
		}
		manager.services[paymentType] = service
		logger.LogInfof("Payment service %s initialized successfully", paymentType.String())
	}

	return manager
}

// GetService 获取支付服务
func (m *PaymentServiceManager) GetService(paymentType PaymentType) Payment {
	return m.services[paymentType]
}

// GetAllServices 获取所有支付服务
func (m *PaymentServiceManager) GetAllServices() map[PaymentType]Payment {
	return m.services
}

// IsServiceAvailable 检查支付服务是否可用
func (m *PaymentServiceManager) IsServiceAvailable(paymentType PaymentType) bool {
	_, exists := m.services[paymentType]
	return exists
}

// GetAvailableServices 获取可用的支付服务列表
func (m *PaymentServiceManager) GetAvailableServices() []PaymentType {
	var services []PaymentType
	for paymentType := range m.services {
		services = append(services, paymentType)
	}
	return services
}

// CreatePaymentByType 根据支付类型创建支付订单
func (m *PaymentServiceManager) CreatePaymentByType(paymentType PaymentType) (Payment, error) {
	service := m.GetService(paymentType)
	if service == nil {
		return nil, fmt.Errorf("payment service %s not available", paymentType.String())
	}
	return service, nil
}

// 全局支付服务管理器实例
var globalManager *PaymentServiceManager

// InitPaymentServices 初始化支付服务
func InitPaymentServices(cfg *conf.Config) error {
	globalManager = NewPaymentServiceManager(cfg)

	logger.LogInfof("Payment services initialized: %v", globalManager.GetAvailableServices())

	return nil
}

// GetPaymentServiceManager 获取全局支付服务管理器
func GetPaymentServiceManager() *PaymentServiceManager {
	return globalManager
}

// GetGlobalPaymentService 获取全局支付服务
func GetGlobalPaymentService(paymentType PaymentType) Payment {
	if globalManager == nil {
		return nil
	}
	return globalManager.GetService(paymentType)
}

// IsGlobalServiceAvailable 检查全局支付服务是否可用
func IsGlobalServiceAvailable(paymentType PaymentType) bool {
	if globalManager == nil {
		return false
	}
	return globalManager.IsServiceAvailable(paymentType)
}

// GetAvailablePaymentTypes 获取可用的支付类型
func GetAvailablePaymentTypes() []PaymentType {
	if globalManager == nil {
		return nil
	}
	return globalManager.GetAvailableServices()
}

// PaymentTypeFromString 从字符串转换为支付类型
func PaymentTypeFromString(s string) PaymentType {
	switch s {
	case "alipay":
		return PaymentTypeAlipay
	case "weixin":
		return PaymentTypeWeixin
	case "apple":
		return PaymentTypeApple
	case "google":
		return PaymentTypeGoogle
	case "paypal":
		return PaymentTypePaypal
	default:
		return PaymentTypeUnknown
	}
}

// ValidatePaymentType 验证支付类型
func ValidatePaymentType(paymentType PaymentType) bool {
	switch paymentType {
	case PaymentTypeAlipay, PaymentTypeWeixin, PaymentTypeApple, PaymentTypeGoogle, PaymentTypePaypal:
		return true
	default:
		return false
	}
}

// GetSupportedPaymentMethods 获取支持的支付方式列表
func GetSupportedPaymentMethods() []map[string]interface{} {
	var methods []map[string]interface{}

	if globalManager == nil {
		return methods
	}

	for paymentType := range globalManager.services {
		methods = append(methods, map[string]interface{}{
			"type":      int(paymentType),
			"name":      paymentType.String(),
			"available": true,
		})
	}

	return methods
}

// GetPaymentMethodInfo 获取支付方式信息
func GetPaymentMethodInfo(paymentType PaymentType) map[string]interface{} {
	info := map[string]interface{}{
		"type":      int(paymentType),
		"name":      paymentType.String(),
		"available": IsGlobalServiceAvailable(paymentType),
	}

	// 添加特定支付方式的额外信息
	switch paymentType {
	case PaymentTypeAlipay:
		info["display_name"] = "支付宝"
		info["icon"] = "alipay"
		info["platforms"] = []string{"android", "ios", "web"}
	case PaymentTypeWeixin:
		info["display_name"] = "微信支付"
		info["icon"] = "weixin"
		info["platforms"] = []string{"android", "ios", "web"}
	case PaymentTypeApple:
		info["display_name"] = "Apple Pay"
		info["icon"] = "apple"
		info["platforms"] = []string{"ios"}
	case PaymentTypeGoogle:
		info["display_name"] = "Google Play"
		info["icon"] = "google"
		info["platforms"] = []string{"android"}
	case PaymentTypePaypal:
		info["display_name"] = "PayPal"
		info["icon"] = "paypal"
		info["platforms"] = []string{"web"}
	}

	return info
}
