package paypal

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
	"time"

	"creativematrix.com/beyondreading/gen/app/base/payment/conf"
	"creativematrix.com/beyondreading/gen/app/base/payment/service"
	"creativematrix.com/beyondreading/gen/common/po"
	"creativematrix.com/beyondreading/pkg/logger"
)

func init() {
	service.Register(&PayPal{})
}

// PayPal PayPal支付服务
type PayPal struct {
	clientID     string
	clientSecret string
	environment  string // sandbox 或 live
	baseURL      string
	accessToken  string
	tokenExpiry  time.Time
	client       *http.Client
}

// PayPalConfig PayPal配置
type PayPalConfig struct {
	ClientID     string `json:"client_id"`
	ClientSecret string `json:"client_secret"`
	Environment  string `json:"environment"`
}

// AccessTokenResponse 访问令牌响应
type AccessTokenResponse struct {
	Scope       string `json:"scope"`
	AccessToken string `json:"access_token"`
	TokenType   string `json:"token_type"`
	AppID       string `json:"app_id"`
	ExpiresIn   int    `json:"expires_in"`
	Nonce       string `json:"nonce"`
}

// PaymentRequest 支付请求
type PaymentRequest struct {
	Intent string `json:"intent"`
	Payer  Payer  `json:"payer"`
	RedirectUrls RedirectUrls `json:"redirect_urls"`
	Transactions []Transaction `json:"transactions"`
}

// Payer 付款人
type Payer struct {
	PaymentMethod string `json:"payment_method"`
}

// RedirectUrls 重定向URL
type RedirectUrls struct {
	ReturnURL string `json:"return_url"`
	CancelURL string `json:"cancel_url"`
}

// Transaction 交易
type Transaction struct {
	Amount      Amount `json:"amount"`
	Description string `json:"description"`
	InvoiceNumber string `json:"invoice_number"`
}

// Amount 金额
type Amount struct {
	Total    string `json:"total"`
	Currency string `json:"currency"`
}

// PaymentResponse 支付响应
type PaymentResponse struct {
	ID           string       `json:"id"`
	Intent       string       `json:"intent"`
	State        string       `json:"state"`
	Cart         string       `json:"cart"`
	Payer        Payer        `json:"payer"`
	Transactions []Transaction `json:"transactions"`
	Links        []Link       `json:"links"`
	CreateTime   string       `json:"create_time"`
	UpdateTime   string       `json:"update_time"`
}

// Link 链接
type Link struct {
	Href   string `json:"href"`
	Rel    string `json:"rel"`
	Method string `json:"method"`
}

// ExecutePaymentRequest 执行支付请求
type ExecutePaymentRequest struct {
	PayerID string `json:"payer_id"`
}

// RefundRequest 退款请求
type RefundRequest struct {
	Amount Amount `json:"amount"`
	Reason string `json:"reason,omitempty"`
}

// RefundResponse 退款响应
type RefundResponse struct {
	ID           string `json:"id"`
	Amount       Amount `json:"amount"`
	State        string `json:"state"`
	Reason       string `json:"reason"`
	CreateTime   string `json:"create_time"`
	UpdateTime   string `json:"update_time"`
	ParentPayment string `json:"parent_payment"`
}

// Name 返回支付方式名称
func (p *PayPal) Name() service.PaymentType {
	return service.PaymentTypePaypal
}

// Init 初始化PayPal支付服务
func (p *PayPal) Init(cfg *conf.Config) error {
	// 从配置中获取PayPal配置
	paypalConfig := cfg.PayPal
	if paypalConfig == nil {
		return fmt.Errorf("paypal config is nil")
	}

	p.clientID = paypalConfig.ClientID
	p.clientSecret = paypalConfig.ClientSecret
	p.environment = paypalConfig.Environment
	if p.environment == "" {
		p.environment = "sandbox"
	}

	// 设置基础URL
	if p.environment == "live" {
		p.baseURL = "https://api.paypal.com"
	} else {
		p.baseURL = "https://api.sandbox.paypal.com"
	}

	p.client = &http.Client{
		Timeout: 30 * time.Second,
	}

	// 获取访问令牌
	if err := p.getAccessToken(); err != nil {
		return fmt.Errorf("failed to get access token: %w", err)
	}

	return nil
}

// CreatePayment 创建支付订单
func (p *PayPal) CreatePayment(ctx context.Context, order *po.PaymentOrder, product *po.PaymentProduct, param *service.OrderParam) (*service.PaymentOrder, error) {
	// 确保访问令牌有效
	if err := p.ensureValidToken(); err != nil {
		return nil, fmt.Errorf("failed to ensure valid token: %w", err)
	}

	// 构建支付请求
	paymentReq := &PaymentRequest{
		Intent: "sale",
		Payer: Payer{
			PaymentMethod: "paypal",
		},
		RedirectUrls: RedirectUrls{
			ReturnURL: param.ReturnPath + "/success",
			CancelURL: param.ReturnPath + "/cancel",
		},
		Transactions: []Transaction{
			{
				Amount: Amount{
					Total:    fmt.Sprintf("%.2f", product.Price),
					Currency: product.Currency,
				},
				Description:   param.Subject,
				InvoiceNumber: fmt.Sprintf("%d", order.ID),
			},
		},
	}

	// 发送创建支付请求
	paymentResp, err := p.createPayment(paymentReq)
	if err != nil {
		return nil, fmt.Errorf("failed to create payment: %w", err)
	}

	// 获取审批URL
	var approvalURL string
	for _, link := range paymentResp.Links {
		if link.Rel == "approval_url" {
			approvalURL = link.Href
			break
		}
	}

	// 构建返回数据
	paymentOrder := &service.PaymentOrder{
		Type:  1,
		Value: approvalURL,
		Order: map[string]interface{}{
			"payment_id":   paymentResp.ID,
			"approval_url": approvalURL,
			"state":        paymentResp.State,
		},
	}

	return paymentOrder, nil
}

// HandleNotify 处理支付通知
func (p *PayPal) HandleNotify(ctx context.Context, req *http.Request) (*service.NotifyOrder, error) {
	// PayPal通常通过IPN或Webhooks发送通知
	if err := req.ParseForm(); err != nil {
		return nil, fmt.Errorf("failed to parse form: %w", err)
	}

	// 获取支付ID和付款人ID
	paymentID := req.Form.Get("payment_id")
	payerID := req.Form.Get("payer_id")

	if paymentID == "" || payerID == "" {
		return nil, fmt.Errorf("missing payment_id or payer_id")
	}

	// 执行支付
	executeReq := &ExecutePaymentRequest{
		PayerID: payerID,
	}

	paymentResp, err := p.executePayment(paymentID, executeReq)
	if err != nil {
		return nil, fmt.Errorf("failed to execute payment: %w", err)
	}

	// 构建通知订单
	notifyOrder := &service.NotifyOrder{
		ID:   0, // 需要从业务逻辑中解析
		Type: service.NotifyTypeTrade,
		Data: map[string]interface{}{
			"payment_id": paymentResp.ID,
			"state":      paymentResp.State,
			"payer_id":   payerID,
		},
	}

	// 根据支付状态判断通知类型
	if paymentResp.State == "approved" {
		notifyOrder.Type = service.NotifyTypeTrade
	} else {
		notifyOrder.Type = service.NotifyTypeTradeErr
	}

	return notifyOrder, nil
}

// AckNotify 确认通知
func (p *PayPal) AckNotify(w http.ResponseWriter, err error) {
	if err != nil {
		logger.LogErrorf("PayPal notify error: %v", err)
		w.WriteHeader(http.StatusBadRequest)
		w.Write([]byte(`{"status": "error"}`))
	} else {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status": "success"}`))
	}
}

// RefundPayment 退款
func (p *PayPal) RefundPayment(ctx context.Context, refund *service.RefundOrder) (*service.RefundResult, error) {
	// 确保访问令牌有效
	if err := p.ensureValidToken(); err != nil {
		return nil, fmt.Errorf("failed to ensure valid token: %w", err)
	}

	// 构建退款请求
	refundReq := &RefundRequest{
		Amount: Amount{
			Total:    fmt.Sprintf("%.2f", refund.RefundAmount),
			Currency: "USD", // 需要从订单中获取货币类型
		},
		Reason: refund.Reason,
	}

	// 发送退款请求
	refundResp, err := p.refundSale(refund.OrderID, refundReq)
	if err != nil {
		return nil, fmt.Errorf("failed to refund sale: %w", err)
	}

	result := &service.RefundResult{
		Success:      refundResp.State == "completed",
		RefundID:     refundResp.ID,
		RefundAmount: refund.RefundAmount,
		Message:      refundResp.State,
	}

	return result, nil
}

// QueryPayment 查询支付状态
func (p *PayPal) QueryPayment(ctx context.Context, orderID string) (map[string]interface{}, error) {
	// 确保访问令牌有效
	if err := p.ensureValidToken(); err != nil {
		return nil, fmt.Errorf("failed to ensure valid token: %w", err)
	}

	// 查询支付状态
	payment, err := p.getPayment(orderID)
	if err != nil {
		return nil, fmt.Errorf("failed to get payment: %w", err)
	}

	return map[string]interface{}{
		"payment_id": payment.ID,
		"state":      payment.State,
		"intent":     payment.Intent,
	}, nil
}

// QueryRefund 查询退款状态
func (p *PayPal) QueryRefund(ctx context.Context, refundID string) (map[string]interface{}, error) {
	// 确保访问令牌有效
	if err := p.ensureValidToken(); err != nil {
		return nil, fmt.Errorf("failed to ensure valid token: %w", err)
	}

	// 查询退款状态
	refund, err := p.getRefund(refundID)
	if err != nil {
		return nil, fmt.Errorf("failed to get refund: %w", err)
	}

	return map[string]interface{}{
		"refund_id": refund.ID,
		"state":     refund.State,
		"amount":    refund.Amount,
	}, nil
}

// 辅助函数

// getAccessToken 获取访问令牌
func (p *PayPal) getAccessToken() error {
	data := url.Values{}
	data.Set("grant_type", "client_credentials")

	req, err := http.NewRequest("POST", p.baseURL+"/v1/oauth2/token", strings.NewReader(data.Encode()))
	if err != nil {
		return err
	}

	req.SetBasicAuth(p.clientID, p.clientSecret)
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	resp, err := p.client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	var tokenResp AccessTokenResponse
	if err := json.Unmarshal(body, &tokenResp); err != nil {
		return err
	}

	p.accessToken = tokenResp.AccessToken
	p.tokenExpiry = time.Now().Add(time.Duration(tokenResp.ExpiresIn) * time.Second)

	return nil
}

// ensureValidToken 确保访问令牌有效
func (p *PayPal) ensureValidToken() error {
	if time.Now().After(p.tokenExpiry.Add(-5 * time.Minute)) {
		return p.getAccessToken()
	}
	return nil
}

// createPayment 创建支付
func (p *PayPal) createPayment(payment *PaymentRequest) (*PaymentResponse, error) {
	data, err := json.Marshal(payment)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", p.baseURL+"/v1/payments/payment", bytes.NewReader(data))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+p.accessToken)

	resp, err := p.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var paymentResp PaymentResponse
	if err := json.Unmarshal(body, &paymentResp); err != nil {
		return nil, err
	}

	return &paymentResp, nil
}

// executePayment 执行支付
func (p *PayPal) executePayment(paymentID string, execute *ExecutePaymentRequest) (*PaymentResponse, error) {
	data, err := json.Marshal(execute)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", p.baseURL+"/v1/payments/payment/"+paymentID+"/execute", bytes.NewReader(data))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+p.accessToken)

	resp, err := p.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var paymentResp PaymentResponse
	if err := json.Unmarshal(body, &paymentResp); err != nil {
		return nil, err
	}

	return &paymentResp, nil
}

// getPayment 获取支付信息
func (p *PayPal) getPayment(paymentID string) (*PaymentResponse, error) {
	req, err := http.NewRequest("GET", p.baseURL+"/v1/payments/payment/"+paymentID, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Authorization", "Bearer "+p.accessToken)

	resp, err := p.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var payment PaymentResponse
	if err := json.Unmarshal(body, &payment); err != nil {
		return nil, err
	}

	return &payment, nil
}

// refundSale 退款
func (p *PayPal) refundSale(saleID string, refund *RefundRequest) (*RefundResponse, error) {
	data, err := json.Marshal(refund)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", p.baseURL+"/v1/payments/sale/"+saleID+"/refund", bytes.NewReader(data))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+p.accessToken)

	resp, err := p.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var refundResp RefundResponse
	if err := json.Unmarshal(body, &refundResp); err != nil {
		return nil, err
	}

	return &refundResp, nil
}

// getRefund 获取退款信息
func (p *PayPal) getRefund(refundID string) (*RefundResponse, error) {
	req, err := http.NewRequest("GET", p.baseURL+"/v1/payments/refund/"+refundID, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Authorization", "Bearer "+p.accessToken)

	resp, err := p.client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var refund RefundResponse
	if err := json.Unmarshal(body, &refund); err != nil {
		return nil, err
	}

	return &refund, nil
}
