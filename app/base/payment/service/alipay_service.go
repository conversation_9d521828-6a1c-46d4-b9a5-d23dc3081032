package service

import (
	"context"
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"net/http"
	"net/url"
	"sort"
	"strings"
	"time"

	"creativematrix.com/beyondreading/gen/app/base/payment/conf"
	"creativematrix.com/beyondreading/gen/common/po"
	"creativematrix.com/beyondreading/pkg/logger"
)

type AlipayService struct {
	config     *conf.Config
	client     *http.Client
	privateKey *rsa.PrivateKey
	publicKey  *rsa.PublicKey
}

type AlipayTradeCreateResponse struct {
	AlipayTradeCreateResponse struct {
		Code       string `json:"code"`
		Msg        string `json:"msg"`
		SubCode    string `json:"sub_code,omitempty"`
		SubMsg     string `json:"sub_msg,omitempty"`
		TradeNo    string `json:"trade_no,omitempty"`
		OutTradeNo string `json:"out_trade_no,omitempty"`
		QrCode     string `json:"qr_code,omitempty"`
	} `json:"alipay_trade_create_response"`
	Sign string `json:"sign"`
}

type AlipayTradeQueryResponse struct {
	AlipayTradeQueryResponse struct {
		Code         string `json:"code"`
		Msg          string `json:"msg"`
		SubCode      string `json:"sub_code,omitempty"`
		SubMsg       string `json:"sub_msg,omitempty"`
		TradeNo      string `json:"trade_no,omitempty"`
		OutTradeNo   string `json:"out_trade_no,omitempty"`
		TradeStatus  string `json:"trade_status,omitempty"`
		TotalAmount  string `json:"total_amount,omitempty"`
		ReceiptAmount string `json:"receipt_amount,omitempty"`
		BuyerPayAmount string `json:"buyer_pay_amount,omitempty"`
		PointAmount   string `json:"point_amount,omitempty"`
		InvoiceAmount string `json:"invoice_amount,omitempty"`
		SendPayDate   string `json:"send_pay_date,omitempty"`
		BuyerUserId   string `json:"buyer_user_id,omitempty"`
		BuyerLogonId  string `json:"buyer_logon_id,omitempty"`
	} `json:"alipay_trade_query_response"`
	Sign string `json:"sign"`
}

func NewAlipayService(config *conf.Config) *AlipayService {
	service := &AlipayService{
		config: config,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}

	// 解析私钥
	if privateKey, err := service.parsePrivateKey(config.Alipay.PrivateKey); err == nil {
		service.privateKey = privateKey
	} else {
		logger.LogErrorf("Failed to parse Alipay private key: %v", err)
	}

	// 解析公钥
	if publicKey, err := service.parsePublicKey(config.Alipay.PublicKey); err == nil {
		service.publicKey = publicKey
	} else {
		logger.LogErrorf("Failed to parse Alipay public key: %v", err)
	}

	return service
}

// CreatePayment 创建支付宝支付
func (a *AlipayService) CreatePayment(ctx context.Context, order *po.PaymentOrder) (*PaymentResult, error) {
	// 构建支付参数
	params := map[string]string{
		"app_id":      a.config.Alipay.AppId,
		"method":      "alipay.trade.create",
		"charset":     "utf-8",
		"sign_type":   "RSA2",
		"timestamp":   time.Now().Format("2006-01-02 15:04:05"),
		"version":     "1.0",
		"notify_url":  a.config.Alipay.NotifyUrl,
		"return_url":  a.config.Alipay.ReturnUrl,
	}

	// 构建业务参数
	bizContent := map[string]interface{}{
		"out_trade_no": order.OrderId,
		"total_amount": fmt.Sprintf("%.2f", float64(order.Amount)/100),
		"subject":      order.ProductName,
		"body":         order.Description,
		"product_code": "QUICK_WAP_WAY",
	}

	bizContentJson, err := json.Marshal(bizContent)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal biz content: %w", err)
	}

	params["biz_content"] = string(bizContentJson)

	// 生成签名
	sign, err := a.generateSign(params)
	if err != nil {
		return nil, fmt.Errorf("failed to generate sign: %w", err)
	}

	params["sign"] = sign

	// 构建支付URL
	paymentUrl := a.config.Alipay.Gateway + "?" + a.buildQuery(params)

	return &PaymentResult{
		Success:    true,
		PaymentUrl: paymentUrl,
		PaymentData: order.OrderId,
		Message:    "Alipay payment created successfully",
		Metadata: map[string]string{
			"outTradeNo": order.OrderId,
			"gateway":    a.config.Alipay.Gateway,
		},
	}, nil
}

// VerifyCallback 验证支付宝支付回调
func (a *AlipayService) VerifyCallback(ctx context.Context, callback *po.PaymentCallback) (*CallbackResult, error) {
	// 验证签名
	isValid := a.verifySign(callback.CallbackData)
	if !isValid {
		return &CallbackResult{
			Valid:   false,
			Message: "Invalid signature",
		}, nil
	}

	// 获取交易状态
	tradeStatus, exists := callback.CallbackData["trade_status"]
	if !exists {
		return &CallbackResult{
			Valid:   false,
			Message: "Missing trade status",
		}, nil
	}

	// 确定支付状态
	status := po.OrderStatusFailed
	switch tradeStatus {
	case "TRADE_SUCCESS", "TRADE_FINISHED":
		status = po.OrderStatusPaid
	case "WAIT_BUYER_PAY":
		status = po.OrderStatusPending
	case "TRADE_CLOSED":
		status = po.OrderStatusCancelled
	}

	tradeNo, _ := callback.CallbackData["trade_no"]
	outTradeNo, _ := callback.CallbackData["out_trade_no"]

	return &CallbackResult{
		Valid:         true,
		Status:        status,
		TransactionId: tradeNo,
		Amount:        callback.Amount,
		Currency:      callback.Currency,
		Message:       "Verification successful",
		Metadata: map[string]string{
			"tradeNo":     tradeNo,
			"outTradeNo":  outTradeNo,
			"tradeStatus": tradeStatus,
		},
	}, nil
}

// ProcessRefund 处理支付宝退款
func (a *AlipayService) ProcessRefund(ctx context.Context, refund *po.PaymentRefund) (*RefundResult, error) {
	// 构建退款参数
	params := map[string]string{
		"app_id":    a.config.Alipay.AppId,
		"method":    "alipay.trade.refund",
		"charset":   "utf-8",
		"sign_type": "RSA2",
		"timestamp": time.Now().Format("2006-01-02 15:04:05"),
		"version":   "1.0",
	}

	// 构建业务参数
	bizContent := map[string]interface{}{
		"out_trade_no":   refund.OrderId,
		"refund_amount":  fmt.Sprintf("%.2f", float64(refund.RefundAmount)/100),
		"refund_reason":  refund.Reason,
		"out_request_no": refund.RefundId,
	}

	bizContentJson, err := json.Marshal(bizContent)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal biz content: %w", err)
	}

	params["biz_content"] = string(bizContentJson)

	// 生成签名
	sign, err := a.generateSign(params)
	if err != nil {
		return nil, fmt.Errorf("failed to generate sign: %w", err)
	}

	params["sign"] = sign

	// 发送退款请求
	success, refundId, err := a.sendRefundRequest(ctx, params)
	if err != nil {
		logger.LogErrorf("Failed to send Alipay refund request: %v", err)
		return &RefundResult{
			Success:  false,
			RefundId: refund.RefundId,
			Status:   po.RefundStatusFailed,
			Message:  fmt.Sprintf("Refund failed: %v", err),
		}, nil
	}

	status := po.RefundStatusSuccess
	if !success {
		status = po.RefundStatusFailed
	}

	return &RefundResult{
		Success:  success,
		RefundId: refundId,
		Status:   status,
		Message:  "Refund processed successfully",
		Metadata: map[string]string{
			"alipayRefundId": refundId,
		},
	}, nil
}

// generateSign 生成签名
func (a *AlipayService) generateSign(params map[string]string) (string, error) {
	if a.privateKey == nil {
		return "", fmt.Errorf("private key not configured")
	}

	// 排序参数
	var keys []string
	for k := range params {
		if k != "sign" && params[k] != "" {
			keys = append(keys, k)
		}
	}
	sort.Strings(keys)

	// 构建待签名字符串
	var signStr strings.Builder
	for i, k := range keys {
		if i > 0 {
			signStr.WriteString("&")
		}
		signStr.WriteString(k)
		signStr.WriteString("=")
		signStr.WriteString(params[k])
	}

	// RSA2签名
	hash := sha256.Sum256([]byte(signStr.String()))
	signature, err := rsa.SignPKCS1v15(rand.Reader, a.privateKey, crypto.SHA256, hash[:])
	if err != nil {
		return "", fmt.Errorf("failed to sign: %w", err)
	}

	return base64.StdEncoding.EncodeToString(signature), nil
}

// verifySign 验证签名
func (a *AlipayService) verifySign(params map[string]string) bool {
	if a.publicKey == nil {
		logger.LogErrorf("Public key not configured")
		return false
	}

	sign, exists := params["sign"]
	if !exists {
		return false
	}

	// 排序参数
	var keys []string
	for k := range params {
		if k != "sign" && k != "sign_type" && params[k] != "" {
			keys = append(keys, k)
		}
	}
	sort.Strings(keys)

	// 构建待验证字符串
	var signStr strings.Builder
	for i, k := range keys {
		if i > 0 {
			signStr.WriteString("&")
		}
		signStr.WriteString(k)
		signStr.WriteString("=")
		signStr.WriteString(params[k])
	}

	// 验证签名
	signBytes, err := base64.StdEncoding.DecodeString(sign)
	if err != nil {
		logger.LogErrorf("Failed to decode sign: %v", err)
		return false
	}

	hash := sha256.Sum256([]byte(signStr.String()))
	err = rsa.VerifyPKCS1v15(a.publicKey, crypto.SHA256, hash[:], signBytes)
	return err == nil
}

// parsePrivateKey 解析私钥
func (a *AlipayService) parsePrivateKey(privateKeyStr string) (*rsa.PrivateKey, error) {
	block, _ := pem.Decode([]byte(privateKeyStr))
	if block == nil {
		return nil, fmt.Errorf("failed to parse PEM block")
	}

	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		// 尝试PKCS8格式
		key, err := x509.ParsePKCS8PrivateKey(block.Bytes)
		if err != nil {
			return nil, fmt.Errorf("failed to parse private key: %w", err)
		}
		rsaKey, ok := key.(*rsa.PrivateKey)
		if !ok {
			return nil, fmt.Errorf("not an RSA private key")
		}
		return rsaKey, nil
	}

	return privateKey, nil
}

// parsePublicKey 解析公钥
func (a *AlipayService) parsePublicKey(publicKeyStr string) (*rsa.PublicKey, error) {
	block, _ := pem.Decode([]byte(publicKeyStr))
	if block == nil {
		return nil, fmt.Errorf("failed to parse PEM block")
	}

	publicKey, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("failed to parse public key: %w", err)
	}

	rsaKey, ok := publicKey.(*rsa.PublicKey)
	if !ok {
		return nil, fmt.Errorf("not an RSA public key")
	}

	return rsaKey, nil
}

// buildQuery 构建查询字符串
func (a *AlipayService) buildQuery(params map[string]string) string {
	var query url.Values = make(url.Values)
	for k, v := range params {
		query.Set(k, v)
	}
	return query.Encode()
}

// sendRefundRequest 发送退款请求
func (a *AlipayService) sendRefundRequest(ctx context.Context, params map[string]string) (bool, string, error) {
	// 这里简化处理，实际需要发送HTTP请求到支付宝API
	logger.LogInfof("Alipay refund request: %+v", params)
	
	// 返回成功，实际需要解析支付宝响应
	return true, params["out_request_no"], nil
}
