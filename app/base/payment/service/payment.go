package service

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"creativematrix.com/beyondreading/gen/app/base/payment/conf"
	"creativematrix.com/beyondreading/gen/common/po"
)

// PaymentType 支付类型枚举
type PaymentType int32

const (
	PaymentTypeUnknown PaymentType = 0
	PaymentTypeAlipay  PaymentType = 1
	PaymentTypeWeixin  PaymentType = 2
	PaymentTypeApple   PaymentType = 3
	PaymentTypeGoogle  PaymentType = 4
	PaymentTypePaypal  PaymentType = 5
)

func (p PaymentType) String() string {
	switch p {
	case PaymentTypeAlipay:
		return "alipay"
	case PaymentTypeWeixin:
		return "weixin"
	case PaymentTypeApple:
		return "apple"
	case PaymentTypeGoogle:
		return "google"
	case PaymentTypePaypal:
		return "paypal"
	default:
		return "unknown"
	}
}

// TradeType 交易类型
type TradeType string

const (
	TradeTypeApp    TradeType = "APP"
	TradeTypeMWeb   TradeType = "MWEB"
	TradeTypeJSAPI  TradeType = "JSAPI"
	TradeTypeNative TradeType = "NATIVE"
)

// OrderParam 订单参数
type OrderParam struct {
	Subject         string    // 订单标题
	ReturnPath      string    // 返回路径
	TradeType       TradeType // 交易类型
	Platform        string    // 平台
	Channel         string    // 渠道
	SpbillCreateIp  string    // 客户端IP
	OpenID          string    // 微信OpenID
	ProductID       string    // 产品ID
	TransactionID   string    // 交易ID（Apple用）
	Receipt         string    // 收据（Apple用）
	NotifyType      string    // 通知类型
}

// PaymentOrder 支付订单
type PaymentOrder struct {
	Type      int                    `json:"type"`
	Value     string                 `json:"value"`
	Order     map[string]interface{} `json:"order"`
	Agreement map[string]interface{} `json:"agreement"`
}

// NotifyOrder 通知订单
type NotifyOrder struct {
	ID         int64                  `json:"id"`
	AccountID  string                 `json:"accountId"`
	Type       int                    `json:"type"`
	Data       map[string]interface{} `json:"data"`
	UseSandbox bool                   `json:"useSandbox"`
}

// RefundOrder 退款订单
type RefundOrder struct {
	OrderID      string  `json:"orderId"`
	RefundID     string  `json:"refundId"`
	TotalAmount  float64 `json:"totalAmount"`
	RefundAmount float64 `json:"refundAmount"`
	Reason       string  `json:"reason"`
}

// RefundResult 退款结果
type RefundResult struct {
	Success      bool   `json:"success"`
	RefundID     string `json:"refundId"`
	RefundAmount float64 `json:"refundAmount"`
	Message      string `json:"message"`
}

// 通知类型常量
const (
	NotifyTypeTrade     = 1 // 交易通知
	NotifyTypeSign      = 2 // 签约通知
	NotifyTypeUnsign    = 3 // 解约通知
	NotifyTypeTradeSign = 4 // 交易加签约
	NotifyTypeTradeErr  = 5 // 交易失败
)

// Payment 支付接口
type Payment interface {
	// Name 返回支付方式名称
	Name() PaymentType

	// Init 初始化支付服务
	Init(cfg *conf.Config) error

	// CreatePayment 创建支付订单
	CreatePayment(ctx context.Context, order *po.PaymentOrder, product *po.PaymentProduct, param *OrderParam) (*PaymentOrder, error)

	// HandleNotify 处理支付通知
	HandleNotify(ctx context.Context, req *http.Request) (*NotifyOrder, error)

	// AckNotify 确认通知
	AckNotify(w http.ResponseWriter, err error)

	// RefundPayment 退款
	RefundPayment(ctx context.Context, refund *RefundOrder) (*RefundResult, error)

	// QueryPayment 查询支付状态
	QueryPayment(ctx context.Context, orderID string) (map[string]interface{}, error)

	// QueryRefund 查询退款状态
	QueryRefund(ctx context.Context, refundID string) (map[string]interface{}, error)
}

// 全局支付服务注册表
var paymentServices = make(map[PaymentType]Payment)

// Register 注册支付服务
func Register(payment Payment) {
	paymentServices[payment.Name()] = payment
}

// GetPaymentService 获取支付服务
func GetPaymentService(paymentType PaymentType) Payment {
	return paymentServices[paymentType]
}

// GetAllPaymentServices 获取所有支付服务
func GetAllPaymentServices() map[PaymentType]Payment {
	return paymentServices
}

// InitAllPaymentServices 初始化所有支付服务
func InitAllPaymentServices(cfg *conf.Config) error {
	for _, service := range paymentServices {
		if err := service.Init(cfg); err != nil {
			return err
		}
	}
	return nil
}

// Payload 支付参数载体
type Payload map[string]string

func (p Payload) Set(key, value string) {
	p[key] = value
}

func (p Payload) Get(key string) string {
	return p[key]
}

func (p Payload) URLEncode() string {
	// URL编码实现
	var params []string
	for key, value := range p {
		if value != "" {
			params = append(params, key+"="+value)
		}
	}
	return strings.Join(params, "&")
}

func (p Payload) ToXML() string {
	// XML格式实现
	var xml strings.Builder
	xml.WriteString("<xml>")
	for key, value := range p {
		if value != "" {
			xml.WriteString("<" + key + "><![CDATA[" + value + "]]></" + key + ">")
		}
	}
	xml.WriteString("</xml>")
	return xml.String()
}

// 工具函数
func GenerateOrderID(prefix string) string {
	// 生成订单ID的实现
	return fmt.Sprintf("%s%d", prefix, time.Now().UnixNano())
}

func GenerateNonceStr() string {
	// 生成随机字符串的实现
	return fmt.Sprintf("%d", time.Now().UnixNano())
}
