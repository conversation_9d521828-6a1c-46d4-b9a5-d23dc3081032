package google

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"creativematrix.com/beyondreading/gen/app/base/payment/conf"
	"creativematrix.com/beyondreading/gen/app/base/payment/service"
	"creativematrix.com/beyondreading/gen/common/po"
	"creativematrix.com/beyondreading/pkg/logger"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
	"google.golang.org/api/androidpublisher/v3"
	"google.golang.org/api/option"
)

func init() {
	service.Register(&Google{})
}

// Google Google支付服务
type Google struct {
	packageName    string
	serviceAccount string
	client         *androidpublisher.Service
	httpClient     *http.Client
}

// GoogleConfig Google支付配置
type GoogleConfig struct {
	PackageName    string `json:"package_name"`
	ServiceAccount string `json:"service_account"` // JSON格式的服务账号密钥
}

// PurchaseVerifyRequest 购买验证请求
type PurchaseVerifyRequest struct {
	PackageName   string `json:"packageName"`
	ProductID     string `json:"productId"`
	PurchaseToken string `json:"purchaseToken"`
}

// PurchaseVerifyResponse 购买验证响应
type PurchaseVerifyResponse struct {
	ConsumptionState    int    `json:"consumptionState"`
	DeveloperPayload    string `json:"developerPayload"`
	Kind                string `json:"kind"`
	OrderID             string `json:"orderId"`
	PurchaseState       int    `json:"purchaseState"`
	PurchaseTimeMillis  string `json:"purchaseTimeMillis"`
	PurchaseType        int    `json:"purchaseType"`
	AcknowledgementState int   `json:"acknowledgementState"`
}

// SubscriptionVerifyResponse 订阅验证响应
type SubscriptionVerifyResponse struct {
	Kind                         string `json:"kind"`
	StartTimeMillis              string `json:"startTimeMillis"`
	ExpiryTimeMillis             string `json:"expiryTimeMillis"`
	AutoRenewing                 bool   `json:"autoRenewing"`
	PriceCurrencyCode            string `json:"priceCurrencyCode"`
	PriceAmountMicros            string `json:"priceAmountMicros"`
	CountryCode                  string `json:"countryCode"`
	DeveloperPayload             string `json:"developerPayload"`
	PaymentState                 int    `json:"paymentState"`
	CancelReason                 int    `json:"cancelReason"`
	UserCancellationTimeMillis   string `json:"userCancellationTimeMillis"`
	OrderID                      string `json:"orderId"`
	PurchaseType                 int    `json:"purchaseType"`
	AcknowledgementState         int    `json:"acknowledgementState"`
}

// Name 返回支付方式名称
func (g *Google) Name() service.PaymentType {
	return service.PaymentTypeGoogle
}

// Init 初始化Google支付服务
func (g *Google) Init(cfg *conf.Config) error {
	// 从配置中获取Google支付配置
	googleConfig := cfg.Google
	if googleConfig == nil {
		return fmt.Errorf("google config is nil")
	}

	g.packageName = googleConfig.PackageName
	g.serviceAccount = googleConfig.ServiceAccount

	// 创建OAuth2配置
	ctx := context.Background()
	creds, err := google.CredentialsFromJSON(ctx, []byte(g.serviceAccount), androidpublisher.AndroidpublisherScope)
	if err != nil {
		return fmt.Errorf("failed to create credentials: %w", err)
	}

	// 创建HTTP客户端
	g.httpClient = oauth2.NewClient(ctx, creds.TokenSource)

	// 创建Android Publisher服务
	g.client, err = androidpublisher.NewService(ctx, option.WithHTTPClient(g.httpClient))
	if err != nil {
		return fmt.Errorf("failed to create android publisher service: %w", err)
	}

	return nil
}

// CreatePayment 创建支付订单
func (g *Google) CreatePayment(ctx context.Context, order *po.PaymentOrder, product *po.PaymentProduct, param *service.OrderParam) (*service.PaymentOrder, error) {
	// Google支付不需要预创建订单，直接返回产品信息
	paymentOrder := &service.PaymentOrder{
		Type:  0,
		Value: "",
		Order: map[string]interface{}{
			"product_id":   product.ProductId,
			"package_name": g.packageName,
			"order_id":     fmt.Sprintf("%d", order.ID),
		},
	}

	return paymentOrder, nil
}

// HandleNotify 处理支付通知
func (g *Google) HandleNotify(ctx context.Context, req *http.Request) (*service.NotifyOrder, error) {
	// Google Play通常通过Pub/Sub发送通知
	// 这里处理HTTP通知的情况
	var notifyData map[string]interface{}
	if err := json.NewDecoder(req.Body).Decode(&notifyData); err != nil {
		return nil, fmt.Errorf("failed to decode notify data: %w", err)
	}

	// 获取购买令牌和产品ID
	purchaseToken, ok := notifyData["purchaseToken"].(string)
	if !ok {
		return nil, fmt.Errorf("missing purchaseToken")
	}

	productID, ok := notifyData["productId"].(string)
	if !ok {
		return nil, fmt.Errorf("missing productId")
	}

	// 验证购买
	purchase, err := g.verifyPurchase(ctx, productID, purchaseToken)
	if err != nil {
		return nil, fmt.Errorf("failed to verify purchase: %w", err)
	}

	// 构建通知订单
	notifyOrder := &service.NotifyOrder{
		ID:   0, // 需要从业务逻辑中解析
		Type: service.NotifyTypeTrade,
		Data: map[string]interface{}{
			"purchase_token":  purchaseToken,
			"product_id":      productID,
			"purchase_state":  purchase.PurchaseState,
			"order_id":        purchase.OrderID,
		},
	}

	// 根据购买状态判断通知类型
	if purchase.PurchaseState == 0 { // 已购买
		notifyOrder.Type = service.NotifyTypeTrade
	} else {
		notifyOrder.Type = service.NotifyTypeTradeErr
	}

	return notifyOrder, nil
}

// AckNotify 确认通知
func (g *Google) AckNotify(w http.ResponseWriter, err error) {
	if err != nil {
		logger.LogErrorf("Google notify error: %v", err)
		w.WriteHeader(http.StatusBadRequest)
		w.Write([]byte(`{"status": "error"}`))
	} else {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte(`{"status": "success"}`))
	}
}

// RefundPayment 退款
func (g *Google) RefundPayment(ctx context.Context, refund *service.RefundOrder) (*service.RefundResult, error) {
	// Google Play的退款需要通过Google Play Console处理
	// 这里只是记录退款请求
	result := &service.RefundResult{
		Success:      false,
		RefundID:     refund.RefundID,
		RefundAmount: refund.RefundAmount,
		Message:      "Google Play refund requires manual processing through Google Play Console",
	}

	return result, nil
}

// QueryPayment 查询支付状态
func (g *Google) QueryPayment(ctx context.Context, orderID string) (map[string]interface{}, error) {
	// Google支付查询需要通过购买令牌验证
	return map[string]interface{}{
		"order_id": orderID,
		"status":   "unknown",
		"message":  "Use purchase token verification to check payment status",
	}, nil
}

// QueryRefund 查询退款状态
func (g *Google) QueryRefund(ctx context.Context, refundID string) (map[string]interface{}, error) {
	// Google退款查询需要通过Google Play Console
	return map[string]interface{}{
		"refund_id": refundID,
		"status":    "unknown",
		"message":   "Check refund status through Google Play Console",
	}, nil
}

// VerifyPurchase 验证购买
func (g *Google) VerifyPurchase(ctx context.Context, productID, purchaseToken string) (*PurchaseVerifyResponse, error) {
	return g.verifyPurchase(ctx, productID, purchaseToken)
}

// VerifySubscription 验证订阅
func (g *Google) VerifySubscription(ctx context.Context, subscriptionID, purchaseToken string) (*SubscriptionVerifyResponse, error) {
	return g.verifySubscription(ctx, subscriptionID, purchaseToken)
}

// AcknowledgePurchase 确认购买
func (g *Google) AcknowledgePurchase(ctx context.Context, productID, purchaseToken string) error {
	return g.acknowledgePurchase(ctx, productID, purchaseToken)
}

// 辅助函数

// verifyPurchase 验证购买
func (g *Google) verifyPurchase(ctx context.Context, productID, purchaseToken string) (*PurchaseVerifyResponse, error) {
	purchase, err := g.client.Purchases.Products.Get(g.packageName, productID, purchaseToken).Context(ctx).Do()
	if err != nil {
		return nil, fmt.Errorf("failed to get purchase: %w", err)
	}

	response := &PurchaseVerifyResponse{
		ConsumptionState:     int(purchase.ConsumptionState),
		DeveloperPayload:     purchase.DeveloperPayload,
		Kind:                 purchase.Kind,
		OrderID:              purchase.OrderId,
		PurchaseState:        int(purchase.PurchaseState),
		PurchaseTimeMillis:   purchase.PurchaseTimeMillis,
		PurchaseType:         int(purchase.PurchaseType),
		AcknowledgementState: int(purchase.AcknowledgementState),
	}

	return response, nil
}

// verifySubscription 验证订阅
func (g *Google) verifySubscription(ctx context.Context, subscriptionID, purchaseToken string) (*SubscriptionVerifyResponse, error) {
	subscription, err := g.client.Purchases.Subscriptions.Get(g.packageName, subscriptionID, purchaseToken).Context(ctx).Do()
	if err != nil {
		return nil, fmt.Errorf("failed to get subscription: %w", err)
	}

	response := &SubscriptionVerifyResponse{
		Kind:                         subscription.Kind,
		StartTimeMillis:              subscription.StartTimeMillis,
		ExpiryTimeMillis:             subscription.ExpiryTimeMillis,
		AutoRenewing:                 subscription.AutoRenewing,
		PriceCurrencyCode:            subscription.PriceCurrencyCode,
		PriceAmountMicros:            subscription.PriceAmountMicros,
		CountryCode:                  subscription.CountryCode,
		DeveloperPayload:             subscription.DeveloperPayload,
		PaymentState:                 int(subscription.PaymentState),
		CancelReason:                 int(subscription.CancelReason),
		UserCancellationTimeMillis:   subscription.UserCancellationTimeMillis,
		OrderID:                      subscription.OrderId,
		PurchaseType:                 int(subscription.PurchaseType),
		AcknowledgementState:         int(subscription.AcknowledgementState),
	}

	return response, nil
}

// acknowledgePurchase 确认购买
func (g *Google) acknowledgePurchase(ctx context.Context, productID, purchaseToken string) error {
	ackReq := &androidpublisher.ProductPurchasesAcknowledgeRequest{
		DeveloperPayload: "",
	}

	_, err := g.client.Purchases.Products.Acknowledge(g.packageName, productID, purchaseToken, ackReq).Context(ctx).Do()
	if err != nil {
		return fmt.Errorf("failed to acknowledge purchase: %w", err)
	}

	return nil
}
