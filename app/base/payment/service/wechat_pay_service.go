package service

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/xml"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"sort"
	"strconv"
	"strings"
	"time"

	"creativematrix.com/beyondreading/app/base/payment/conf"
	"creativematrix.com/beyondreading/app/common/po"
	"creativematrix.com/beyondreading/pkg/logger"
)

type WechatPayService struct {
	config *conf.Config
	client *http.Client
}

type WechatUnifiedOrderRequest struct {
	XMLName        xml.Name `xml:"xml"`
	AppId          string   `xml:"appid"`
	MchId          string   `xml:"mch_id"`
	NonceStr       string   `xml:"nonce_str"`
	Sign           string   `xml:"sign"`
	Body           string   `xml:"body"`
	OutTradeNo     string   `xml:"out_trade_no"`
	TotalFee       int      `xml:"total_fee"`
	SpbillCreateIp string   `xml:"spbill_create_ip"`
	NotifyUrl      string   `xml:"notify_url"`
	TradeType      string   `xml:"trade_type"`
}

type WechatUnifiedOrderResponse struct {
	XMLName    xml.Name `xml:"xml"`
	ReturnCode string   `xml:"return_code"`
	ReturnMsg  string   `xml:"return_msg"`
	AppId      string   `xml:"appid,omitempty"`
	MchId      string   `xml:"mch_id,omitempty"`
	NonceStr   string   `xml:"nonce_str,omitempty"`
	Sign       string   `xml:"sign,omitempty"`
	ResultCode string   `xml:"result_code,omitempty"`
	PrepayId   string   `xml:"prepay_id,omitempty"`
	TradeType  string   `xml:"trade_type,omitempty"`
	CodeUrl    string   `xml:"code_url,omitempty"`
	ErrCode    string   `xml:"err_code,omitempty"`
	ErrCodeDes string   `xml:"err_code_des,omitempty"`
}

type WechatOrderQueryRequest struct {
	XMLName       xml.Name `xml:"xml"`
	AppId         string   `xml:"appid"`
	MchId         string   `xml:"mch_id"`
	OutTradeNo    string   `xml:"out_trade_no,omitempty"`
	TransactionId string   `xml:"transaction_id,omitempty"`
	NonceStr      string   `xml:"nonce_str"`
	Sign          string   `xml:"sign"`
}

type WechatOrderQueryResponse struct {
	XMLName       xml.Name `xml:"xml"`
	ReturnCode    string   `xml:"return_code"`
	ReturnMsg     string   `xml:"return_msg"`
	AppId         string   `xml:"appid,omitempty"`
	MchId         string   `xml:"mch_id,omitempty"`
	NonceStr      string   `xml:"nonce_str,omitempty"`
	Sign          string   `xml:"sign,omitempty"`
	ResultCode    string   `xml:"result_code,omitempty"`
	OutTradeNo    string   `xml:"out_trade_no,omitempty"`
	TransactionId string   `xml:"transaction_id,omitempty"`
	TradeState    string   `xml:"trade_state,omitempty"`
	TotalFee      int      `xml:"total_fee,omitempty"`
	ErrCode       string   `xml:"err_code,omitempty"`
	ErrCodeDes    string   `xml:"err_code_des,omitempty"`
}

func NewWechatPayService(config *conf.Config) *WechatPayService {
	return &WechatPayService{
		config: config,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// CreatePayment 创建微信支付
func (w *WechatPayService) CreatePayment(ctx context.Context, order *po.PaymentOrder) (*PaymentResult, error) {
	// 构建统一下单请求
	req := WechatUnifiedOrderRequest{
		AppId:          w.config.WechatPay.AppId,
		MchId:          w.config.WechatPay.MchId,
		NonceStr:       w.generateNonceStr(),
		Body:           order.ProductName,
		OutTradeNo:     order.OrderId,
		TotalFee:       int(order.Amount), // 微信支付金额单位为分
		SpbillCreateIp: order.ClientIp,
		NotifyUrl:      w.config.WechatPay.NotifyUrl,
		TradeType:      "NATIVE", // 扫码支付
	}

	// 生成签名
	req.Sign = w.generateSign(w.structToMap(req))

	// 发送请求
	resp, err := w.sendUnifiedOrderRequest(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to send unified order request: %w", err)
	}

	if resp.ReturnCode != "SUCCESS" {
		return nil, fmt.Errorf("unified order failed: %s", resp.ReturnMsg)
	}

	if resp.ResultCode != "SUCCESS" {
		return nil, fmt.Errorf("unified order result failed: %s - %s", resp.ErrCode, resp.ErrCodeDes)
	}

	return &PaymentResult{
		Success:     true,
		PaymentUrl:  resp.CodeUrl, // 二维码链接
		PaymentData: resp.PrepayId,
		Message:     "WeChat Pay order created successfully",
		Metadata: map[string]string{
			"prepayId":  resp.PrepayId,
			"tradeType": resp.TradeType,
			"codeUrl":   resp.CodeUrl,
		},
	}, nil
}

// VerifyCallback 验证微信支付回调
func (w *WechatPayService) VerifyCallback(ctx context.Context, callback *po.PaymentCallback) (*CallbackResult, error) {
	// 验证签名
	isValid := w.verifySign(callback.CallbackData)
	if !isValid {
		return &CallbackResult{
			Valid:   false,
			Message: "Invalid signature",
		}, nil
	}

	// 检查返回状态
	returnCode, exists := callback.CallbackData["return_code"]
	if !exists || returnCode != "SUCCESS" {
		return &CallbackResult{
			Valid:   false,
			Message: "Return code is not SUCCESS",
		}, nil
	}

	resultCode, exists := callback.CallbackData["result_code"]
	if !exists || resultCode != "SUCCESS" {
		return &CallbackResult{
			Valid:   false,
			Message: "Result code is not SUCCESS",
		}, nil
	}

	// 获取交易信息
	transactionId, _ := callback.CallbackData["transaction_id"]
	outTradeNo, _ := callback.CallbackData["out_trade_no"]
	totalFeeStr, _ := callback.CallbackData["total_fee"]

	totalFee, _ := strconv.ParseInt(totalFeeStr, 10, 64)

	return &CallbackResult{
		Valid:         true,
		Status:        po.OrderStatusPaid,
		TransactionId: transactionId,
		Amount:        totalFee,
		Currency:      "CNY", // 微信支付默认人民币
		Message:       "Verification successful",
		Metadata: map[string]string{
			"transactionId": transactionId,
			"outTradeNo":    outTradeNo,
			"totalFee":      totalFeeStr,
		},
	}, nil
}

// ProcessRefund 处理微信支付退款
func (w *WechatPayService) ProcessRefund(ctx context.Context, refund *po.PaymentRefund) (*RefundResult, error) {
	// 微信支付退款需要调用退款API
	// 这里简化处理，实际需要实现完整的退款流程

	logger.LogInfof("WeChat Pay refund request: orderId=%s, refundId=%s, amount=%d",
		refund.OrderId, refund.RefundId, refund.RefundAmount)

	// 构建退款请求
	success, refundId, err := w.processWechatRefund(ctx, refund)
	if err != nil {
		logger.LogErrorf("Failed to process WeChat Pay refund: %v", err)
		return &RefundResult{
			Success:  false,
			RefundId: refund.RefundId,
			Status:   po.RefundStatusFailed,
			Message:  fmt.Sprintf("Refund failed: %v", err),
		}, nil
	}

	status := po.RefundStatusSuccess
	if !success {
		status = po.RefundStatusFailed
	}

	return &RefundResult{
		Success:  success,
		RefundId: refundId,
		Status:   int32(status),
		Message:  "Refund processed successfully",
		Metadata: map[string]string{
			"wechatRefundId": refundId,
		},
	}, nil
}

// generateNonceStr 生成随机字符串
func (w *WechatPayService) generateNonceStr() string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, 32)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}
	return string(b)
}

// generateSign 生成签名
func (w *WechatPayService) generateSign(params map[string]interface{}) string {
	// 排序参数
	var keys []string
	for k := range params {
		if k != "sign" && params[k] != "" && params[k] != nil {
			keys = append(keys, k)
		}
	}
	sort.Strings(keys)

	// 构建待签名字符串
	var signStr strings.Builder
	for i, k := range keys {
		if i > 0 {
			signStr.WriteString("&")
		}
		signStr.WriteString(k)
		signStr.WriteString("=")
		signStr.WriteString(fmt.Sprintf("%v", params[k]))
	}

	// 添加API密钥
	signStr.WriteString("&key=")
	signStr.WriteString(w.config.WechatPay.ApiKey)

	// MD5签名
	hash := md5.Sum([]byte(signStr.String()))
	return fmt.Sprintf("%X", hash)
}

// verifySign 验证签名
func (w *WechatPayService) verifySign(params map[string]string) bool {
	sign, exists := params["sign"]
	if !exists {
		return false
	}

	// 转换为interface{}类型
	interfaceParams := make(map[string]interface{})
	for k, v := range params {
		interfaceParams[k] = v
	}

	expectedSign := w.generateSign(interfaceParams)
	return sign == expectedSign
}

// structToMap 结构体转map
func (w *WechatPayService) structToMap(obj interface{}) map[string]interface{} {
	result := make(map[string]interface{})

	// 这里简化处理，实际需要使用反射
	if req, ok := obj.(WechatUnifiedOrderRequest); ok {
		result["appid"] = req.AppId
		result["mch_id"] = req.MchId
		result["nonce_str"] = req.NonceStr
		result["body"] = req.Body
		result["out_trade_no"] = req.OutTradeNo
		result["total_fee"] = req.TotalFee
		result["spbill_create_ip"] = req.SpbillCreateIp
		result["notify_url"] = req.NotifyUrl
		result["trade_type"] = req.TradeType
	}

	return result
}

// sendUnifiedOrderRequest 发送统一下单请求
func (w *WechatPayService) sendUnifiedOrderRequest(ctx context.Context, req WechatUnifiedOrderRequest) (*WechatUnifiedOrderResponse, error) {
	// 序列化XML
	xmlData, err := xml.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal XML: %w", err)
	}

	// 发送HTTP请求
	httpReq, err := http.NewRequestWithContext(ctx, "POST",
		w.config.WechatPay.Gateway+"/pay/unifiedorder", bytes.NewReader(xmlData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/xml")

	resp, err := w.client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// 解析XML响应
	var wechatResp WechatUnifiedOrderResponse
	if err := xml.Unmarshal(respData, &wechatResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal XML: %w", err)
	}

	return &wechatResp, nil
}

// processWechatRefund 处理微信退款
func (w *WechatPayService) processWechatRefund(ctx context.Context, refund *po.PaymentRefund) (bool, string, error) {
	// 这里简化处理，实际需要调用微信退款API
	logger.LogInfof("Processing WeChat Pay refund: %+v", refund)

	// 返回成功，实际需要发送退款请求并解析响应
	return true, refund.RefundId, nil
}
