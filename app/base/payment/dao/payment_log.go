package dao

import (
	"context"
	"creativematrix.com/beyondreading/app/api/payment/model/po"
	"time"

	"creativematrix.com/beyondreading/app/base/payment/model"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/mongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// CreatePaymentLog 创建支付日志
func (d *Dao) CreatePaymentLog(ctx context.Context, log *po.PaymentLog) error {
	log.CreatedAt = time.Now()

	insertedId, err := d.schema[model.TablePaymentLogs].Insert(log)
	if err != nil {
		logger.LogErrorf("Failed to create payment log: %v", err)
		return err
	}

	// 获取插入的ID
	if oid, ok := insertedId.(primitive.ObjectID); ok {
		log.ID = oid
	}

	return nil
}

// GetPaymentLogsByOrderId 根据订单ID获取支付日志
func (d *Dao) GetPaymentLogsByOrderId(ctx context.Context, orderId string, page, pageSize int32) ([]*po.PaymentLog, int64, error) {
	filter := bson.M{"orderId": orderId}

	// 获取总数
	total := d.schema[model.TablePaymentLogs].Count(filter)

	// 分页查询
	skip := (page - 1) * pageSize
	var logs []*po.PaymentLog
	err := d.schema[model.TablePaymentLogs].Find(filter, &logs, mongo.Skip(int64(int(skip))), mongo.Limit(int64(int(pageSize))), mongo.Sort(bson.M{"createdAt": -1}))
	if err != nil {
		logger.LogErrorf("Failed to find payment logs: %v", err)
		return nil, 0, err
	}

	return logs, total, nil
}

// GetPaymentLogsByUserId 根据用户ID获取支付日志
func (d *Dao) GetPaymentLogsByUserId(ctx context.Context, userId uint64, page, pageSize int32) ([]*po.PaymentLog, int64, error) {
	filter := bson.M{"userId": userId}

	// 获取总数
	total := d.schema[model.TablePaymentLogs].Count(filter)

	// 分页查询
	skip := (page - 1) * pageSize
	var logs []*po.PaymentLog
	err := d.schema[model.TablePaymentLogs].Find(filter, &logs, mongo.Context(ctx),
		mongo.Skip(int64(skip)),
		mongo.Limit(int64(pageSize)),
		mongo.Sort(bson.M{"createdAt": -1}))
	if err != nil {
		logger.LogErrorf("Failed to find payment logs: %v", err)
		return nil, 0, err
	}

	return logs, total, nil
}

// CleanOldPaymentLogs 清理旧的支付日志
func (d *Dao) CleanOldPaymentLogs(ctx context.Context, days int32) error {
	cutoffTime := time.Now().AddDate(0, 0, -int(days))
	filter := bson.M{
		"createdAt": bson.M{"$lt": cutoffTime},
	}

	err := d.schema[model.TablePaymentLogs].Delete(filter)
	if err != nil {
		logger.LogErrorf("Failed to clean old payment logs: %v", err)
		return err
	}

	logger.LogInfof("Cleaned old payment logs")
	return nil
}

// LogPaymentAction 记录支付操作日志
func (d *Dao) LogPaymentAction(ctx context.Context, orderId string, userId uint64, action, status, message string, data map[string]string) error {
	log := &po.PaymentLog{
		OrderId: orderId,
		UserId:  userId,
		Action:  action,
		Status:  status,
		Message: message,
		Data:    data,
	}

	return d.CreatePaymentLog(ctx, log)
}
