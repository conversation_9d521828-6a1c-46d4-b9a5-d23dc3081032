package dao

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"creativematrix.com/beyondreading/gen/common/po"
	"creativematrix.com/beyondreading/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// CreatePaymentOrder 创建支付订单
func (d *Dao) CreatePaymentOrder(ctx context.Context, order *po.PaymentOrder) error {
	order.CreatedAt = time.Now()
	order.UpdatedAt = order.CreatedAt
	order.ExpiredAt = order.CreatedAt.Add(time.Duration(d.conf.Payment.OrderExpireMinutes) * time.Minute)

	model := d.schema["payment_orders"]
	_, err := model.InsertOne(ctx, order)
	if err != nil {
		logger.LogErrorf("Failed to create payment order: %v", err)
		return fmt.Errorf("failed to create payment order: %w", err)
	}

	// 设置缓存
	err = d.setPaymentOrderCache(ctx, order)
	if err != nil {
		logger.LogErrorf("Failed to set payment order cache: %v", err)
	}

	return nil
}

// GetPaymentOrderById 根据订单ID获取支付订单
func (d *Dao) GetPaymentOrderById(ctx context.Context, orderId string) (*po.PaymentOrder, error) {
	// 先从缓存获取
	order, err := d.getPaymentOrderFromCache(ctx, orderId)
	if err == nil && order != nil {
		return order, nil
	}

	// 缓存未命中，从数据库查询
	model := d.schema["payment_orders"]
	filter := bson.M{"orderId": orderId}

	var result po.PaymentOrder
	err = model.FindOne(ctx, filter).Decode(&result)
	if err != nil {
		if err.Error() == "mongo: no documents in result" {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get payment order: %w", err)
	}

	// 设置缓存
	err = d.setPaymentOrderCache(ctx, &result)
	if err != nil {
		logger.LogErrorf("Failed to set payment order cache: %v", err)
	}

	return &result, nil
}

// GetPaymentOrdersByUserId 根据用户ID获取支付订单列表
func (d *Dao) GetPaymentOrdersByUserId(ctx context.Context, userId uint64, page, pageSize int32, filters map[string]interface{}) ([]*po.PaymentOrder, int64, error) {
	model := d.schema["payment_orders"]

	// 构建查询条件
	filter := bson.M{"userId": userId}
	for key, value := range filters {
		if value != nil {
			filter[key] = value
		}
	}

	// 获取总数
	total, err := model.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count payment orders: %w", err)
	}

	// 分页查询
	skip := (page - 1) * pageSize
	opts := options.Find().
		SetSkip(int64(skip)).
		SetLimit(int64(pageSize)).
		SetSort(bson.D{{"createdAt", -1}})

	cursor, err := model.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to find payment orders: %w", err)
	}
	defer cursor.Close(ctx)

	var orders []*po.PaymentOrder
	for cursor.Next(ctx) {
		var order po.PaymentOrder
		if err := cursor.Decode(&order); err != nil {
			logger.LogErrorf("Failed to decode payment order: %v", err)
			continue
		}
		orders = append(orders, &order)
	}

	return orders, total, nil
}

// UpdatePaymentOrder 更新支付订单
func (d *Dao) UpdatePaymentOrder(ctx context.Context, orderId string, updates bson.M) error {
	model := d.schema["payment_orders"]

	updates["updatedAt"] = time.Now()
	filter := bson.M{"orderId": orderId}
	update := bson.M{"$set": updates}

	result, err := model.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update payment order: %w", err)
	}

	if result.MatchedCount == 0 {
		return fmt.Errorf("payment order not found: %s", orderId)
	}

	// 删除缓存
	err = d.deletePaymentOrderCache(ctx, orderId)
	if err != nil {
		logger.LogErrorf("Failed to delete payment order cache: %v", err)
	}

	return nil
}

// UpdatePaymentOrderStatus 更新支付订单状态
func (d *Dao) UpdatePaymentOrderStatus(ctx context.Context, orderId string, status int32, transactionId string, failureReason string) error {
	updates := bson.M{
		"status": status,
	}

	if transactionId != "" {
		updates["transactionId"] = transactionId
	}

	if status == po.OrderStatusPaid {
		updates["paidAt"] = time.Now()
	}

	if failureReason != "" {
		updates["failureReason"] = failureReason
	}

	return d.UpdatePaymentOrder(ctx, orderId, updates)
}

// GetExpiredOrders 获取过期订单
func (d *Dao) GetExpiredOrders(ctx context.Context, limit int32) ([]*po.PaymentOrder, error) {
	model := d.schema["payment_orders"]

	filter := bson.M{
		"status":    po.OrderStatusPending,
		"expiredAt": bson.M{"$lt": time.Now()},
	}

	opts := options.Find().
		SetLimit(int64(limit)).
		SetSort(bson.D{{"expiredAt", 1}})

	cursor, err := model.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to find expired orders: %w", err)
	}
	defer cursor.Close(ctx)

	var orders []*po.PaymentOrder
	for cursor.Next(ctx) {
		var order po.PaymentOrder
		if err := cursor.Decode(&order); err != nil {
			logger.LogErrorf("Failed to decode expired order: %v", err)
			continue
		}
		orders = append(orders, &order)
	}

	return orders, nil
}

// 缓存相关方法
func (d *Dao) getPaymentOrderFromCache(ctx context.Context, orderId string) (*po.PaymentOrder, error) {
	key := fmt.Sprintf("payment:order:%s", orderId)
	data, err := d.cache.RGetString(ctx, key)
	if err != nil {
		return nil, err
	}

	var order po.PaymentOrder
	err = json.Unmarshal([]byte(data), &order)
	if err != nil {
		return nil, err
	}

	return &order, nil
}

func (d *Dao) setPaymentOrderCache(ctx context.Context, order *po.PaymentOrder) error {
	key := fmt.Sprintf("payment:order:%s", order.OrderId)
	data, err := json.Marshal(order)
	if err != nil {
		return err
	}

	expireTime := time.Hour * 24 // 24小时过期
	_, err = d.cache.RSet(ctx, key, string(data), expireTime)
	return err
}

func (d *Dao) deletePaymentOrderCache(ctx context.Context, orderId string) error {
	key := fmt.Sprintf("payment:order:%s", orderId)
	_, err := d.cache.RDel(ctx, key)
	return err
}
