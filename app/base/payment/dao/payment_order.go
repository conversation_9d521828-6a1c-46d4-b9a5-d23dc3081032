package dao

import (
	"context"
	"creativematrix.com/beyondreading/app/api/payment/model/po"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"creativematrix.com/beyondreading/app/base/payment/model"
	"creativematrix.com/beyondreading/pkg/ecode"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/mongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// CreatePaymentOrder 创建支付订单
func (d *Dao) CreatePaymentOrder(ctx context.Context, order *po.PaymentOrder) error {
	order.CreatedAt = time.Now()
	order.UpdatedAt = order.CreatedAt
	order.ExpiredAt = order.CreatedAt.Add(time.Duration(d.conf.Payment.OrderExpireMinutes) * time.Minute)

	// 使用正确的mongo包方法
	insertedId, err := d.schema[model.TablePaymentOrders].Insert(order)
	if err != nil {
		logger.LogErrorf("Failed to create payment order: %v", err)
		return err
	}

	// 获取插入的ID
	if oid, ok := insertedId.(primitive.ObjectID); ok {
		order.ID = oid
	}

	// 设置缓存
	err = d.setPaymentOrderCache(ctx, order)
	if err != nil {
		logger.LogErrorf("Failed to set payment order cache: %v", err)
	}

	return nil
}

// GetPaymentOrderById 根据订单ID获取支付订单
func (d *Dao) GetPaymentOrderById(ctx context.Context, orderId string) (*po.PaymentOrder, error) {
	// 先从缓存获取
	order, err := d.getPaymentOrderFromCache(ctx, orderId)
	if err == nil && order != nil {
		return order, nil
	}

	// 缓存未命中，从数据库查询
	var result po.PaymentOrder
	err = d.schema[model.TablePaymentOrders].FindOne(bson.M{"orderId": orderId}, &result)
	if err != nil {
		if errors.Is(err, ecode.NothingFound) {
			return nil, nil
		}
		logger.LogErrorf("Failed to get payment order: %v", err)
		return nil, err
	}

	// 设置缓存
	err = d.setPaymentOrderCache(ctx, &result)
	if err != nil {
		logger.LogErrorf("Failed to set payment order cache: %v", err)
	}

	return &result, nil
}

// GetPaymentOrdersByUserId 根据用户ID获取支付订单列表
func (d *Dao) GetPaymentOrdersByUserId(ctx context.Context, userId uint64, page, pageSize int32, filters map[string]interface{}) ([]*po.PaymentOrder, int64, error) {
	// 构建查询条件
	filter := bson.M{"userId": userId}
	for key, value := range filters {
		if value != nil {
			filter[key] = value
		}
	}

	// 获取总数
	total := d.schema[model.TablePaymentOrders].Count(filter)

	// 分页查询
	skip := (page - 1) * pageSize
	var orders []*po.PaymentOrder
	err := d.schema[model.TablePaymentOrders].Find(filter, &orders,
		mongo.Context(ctx),
		mongo.Skip(int64(skip)),
		mongo.Limit(int64(pageSize)),
		mongo.Sort(bson.M{"createdAt": -1}))
	if err != nil {
		logger.LogErrorf("Failed to find payment orders: %v", err)
		return nil, 0, err
	}

	return orders, total, nil
}

// UpdatePaymentOrder 更新支付订单
func (d *Dao) UpdatePaymentOrder(ctx context.Context, orderId string, updates bson.M) error {
	updates["updatedAt"] = time.Now()
	filter := bson.M{"orderId": orderId}
	update := bson.M{"$set": updates}

	_, err := d.schema[model.TablePaymentOrders].Update(filter, update)
	if err != nil {
		logger.LogErrorf("Failed to update payment order: %v", err)
		return err
	}

	// 删除缓存
	err = d.deletePaymentOrderCache(ctx, orderId)
	if err != nil {
		logger.LogErrorf("Failed to delete payment order cache: %v", err)
	}

	return nil
}

// UpdatePaymentOrderStatus 更新支付订单状态
func (d *Dao) UpdatePaymentOrderStatus(ctx context.Context, orderId string, status int32, transactionId string, failureReason string) error {
	updates := bson.M{
		"status": status,
	}

	if transactionId != "" {
		updates["transactionId"] = transactionId
	}

	if status == po.OrderStatusPaid {
		updates["paidAt"] = time.Now()
	}

	if failureReason != "" {
		updates["failureReason"] = failureReason
	}

	return d.UpdatePaymentOrder(ctx, orderId, updates)
}

// GetExpiredOrders 获取过期订单
func (d *Dao) GetExpiredOrders(ctx context.Context, limit int32) ([]*po.PaymentOrder, error) {
	filter := bson.M{
		"status":    po.OrderStatusPending,
		"expiredAt": bson.M{"$lt": time.Now()},
	}

	var orders []*po.PaymentOrder
	err := d.schema[model.TablePaymentOrders].Find(filter, &orders,
		mongo.Context(ctx),
		mongo.Limit(int64(limit)),
		mongo.Sort(bson.M{"expiredAt": 1}))
	if err != nil {
		logger.LogErrorf("Failed to find expired orders: %v", err)
		return nil, err
	}

	return orders, nil
}

// 缓存相关方法
func (d *Dao) getPaymentOrderFromCache(ctx context.Context, orderId string) (*po.PaymentOrder, error) {
	key := fmt.Sprintf(model.RedisPaymentOrderId, orderId)
	data, err := d.cache.GetString(ctx, key)
	if err != nil {
		return nil, err
	}

	var order po.PaymentOrder
	err = json.Unmarshal([]byte(data), &order)
	if err != nil {
		return nil, err
	}

	return &order, nil
}

func (d *Dao) setPaymentOrderCache(ctx context.Context, order *po.PaymentOrder) error {
	key := fmt.Sprintf(model.RedisPaymentOrderId, order.OrderId)
	data, err := json.Marshal(order)
	if err != nil {
		return err
	}

	_, err = d.cache.Set(ctx, key, string(data), int64(model.CachePaymentOrderExpire))
	return err
}

func (d *Dao) deletePaymentOrderCache(ctx context.Context, orderId string) error {
	key := fmt.Sprintf(model.RedisPaymentOrderId, orderId)
	err := d.cache.DelKey(ctx, key)
	return err
}
