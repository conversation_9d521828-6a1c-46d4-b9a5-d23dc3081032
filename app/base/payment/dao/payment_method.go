package dao

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"strings"

	"creativematrix.com/beyondreading/app/base/payment/model"
	"creativematrix.com/beyondreading/app/common/po"
	"creativematrix.com/beyondreading/pkg/logger"
)

// GetPaymentMethods 获取支付方式列表
func (d *Dao) GetPaymentMethods(ctx context.Context, enabled *bool) ([]*po.PaymentMethodConfig, error) {
	// 先从缓存获取
	if enabled == nil || *enabled {
		methods, err := d.getPaymentMethodsFromCache(ctx)
		if err == nil && methods != nil {
			return methods, nil
		}
	}

	// 构建SQL查询
	query := "SELECT id, method, name, enabled, config, sort_order, created_at, updated_at FROM payment_methods"
	args := []interface{}{}

	if enabled != nil {
		query += " WHERE enabled = ?"
		args = append(args, *enabled)
	}

	query += " ORDER BY method ASC"

	// 获取数据库连接 - payment_methods不分片，使用默认连接
	db, err := d.mysql.DB("0")
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}

	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		logger.LogErrorf("Failed to query payment methods: %v", err)
		return nil, fmt.Errorf("failed to query payment methods: %w", err)
	}
	defer rows.Close()

	var methods []*po.PaymentMethodConfig
	for rows.Next() {
		method := &po.PaymentMethodConfig{}
		var configStr string
		err := rows.Scan(&method.ID, &method.Method, &method.Name, &method.Enabled,
			&configStr, &method.SortOrder, &method.CreatedAt, &method.UpdatedAt)
		if err != nil {
			logger.LogErrorf("Failed to scan payment method: %v", err)
			continue
		}

		// 解析配置JSON
		if configStr != "" {
			if err := json.Unmarshal([]byte(configStr), &method.Config); err != nil {
				logger.LogErrorf("Failed to unmarshal method config: %v", err)
				method.Config = make(map[string]string)
			}
		} else {
			method.Config = make(map[string]string)
		}

		methods = append(methods, method)
	}

	// 设置缓存（仅缓存启用的支付方式）
	if enabled == nil || *enabled {
		err = d.setPaymentMethodsCache(ctx, methods)
		if err != nil {
			logger.LogErrorf("Failed to set payment methods cache: %v", err)
		}
	}

	return methods, nil
}

// GetPaymentMethodByMethod 根据支付方式获取配置
func (d *Dao) GetPaymentMethodByMethod(ctx context.Context, method int32) (*po.PaymentMethodConfig, error) {
	// 先从缓存获取
	methodConfig, err := d.getPaymentMethodFromCache(ctx, method)
	if err == nil && methodConfig != nil {
		return methodConfig, nil
	}

	query := "SELECT id, method, name, enabled, config, sort_order, created_at, updated_at FROM payment_methods WHERE method = ?"

	// 获取数据库连接
	db, err := d.mysql.DB("0")
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}

	var result po.PaymentMethodConfig
	var configStr string
	err = db.QueryRowContext(ctx, query, method).Scan(
		&result.ID, &result.Method, &result.Name, &result.Enabled,
		&configStr, &result.SortOrder, &result.CreatedAt, &result.UpdatedAt)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		logger.LogErrorf("Failed to get payment method: %v", err)
		return nil, fmt.Errorf("failed to get payment method: %w", err)
	}

	// 解析配置JSON
	if configStr != "" {
		if err := json.Unmarshal([]byte(configStr), &result.Config); err != nil {
			logger.LogErrorf("Failed to unmarshal method config: %v", err)
			result.Config = make(map[string]string)
		}
	} else {
		result.Config = make(map[string]string)
	}

	// 设置缓存
	err = d.setPaymentMethodCache(ctx, &result)
	if err != nil {
		logger.LogErrorf("Failed to set payment method cache: %v", err)
	}

	return &result, nil
}

// GetPaymentProducts 获取支付产品列表
func (d *Dao) GetPaymentProducts(ctx context.Context, productType *int32, enabled *bool) ([]*po.PaymentProduct, error) {
	// 生成缓存键
	cacheKey := d.generateProductsCacheKey(productType, enabled)

	// 先从缓存获取
	if enabled == nil || *enabled {
		products, err := d.getPaymentProductsFromCache(ctx, cacheKey)
		if err == nil && products != nil {
			return products, nil
		}
	}

	// 构建SQL查询
	query := "SELECT id, product_id, product_name, product_type, price, currency, coin_amount, vip_days, enabled, sort_order, created_at, updated_at FROM payment_products"
	args := []interface{}{}
	conditions := []string{}

	if productType != nil {
		conditions = append(conditions, "product_type = ?")
		args = append(args, *productType)
	}
	if enabled != nil {
		conditions = append(conditions, "enabled = ?")
		args = append(args, *enabled)
	}

	if len(conditions) > 0 {
		query += " WHERE " + strings.Join(conditions, " AND ")
	}

	query += " ORDER BY sort_order ASC, created_at ASC"

	// 获取数据库连接
	db, err := d.mysql.DB("0")
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}

	rows, err := db.QueryContext(ctx, query, args...)
	if err != nil {
		logger.LogErrorf("Failed to query payment products: %v", err)
		return nil, fmt.Errorf("failed to query payment products: %w", err)
	}
	defer rows.Close()

	var products []*po.PaymentProduct
	for rows.Next() {
		product := &po.PaymentProduct{}
		err := rows.Scan(&product.ID, &product.ProductId, &product.ProductName, &product.ProductType,
			&product.Price, &product.Currency, &product.CoinAmount, &product.VipDays,
			&product.Enabled, &product.SortOrder, &product.CreatedAt, &product.UpdatedAt)
		if err != nil {
			logger.LogErrorf("Failed to scan payment product: %v", err)
			continue
		}

		products = append(products, product)
	}

	// 设置缓存（仅缓存启用的产品）
	if enabled == nil || *enabled {
		err = d.setPaymentProductsCache(ctx, cacheKey, products)
		if err != nil {
			logger.LogErrorf("Failed to set payment products cache: %v", err)
		}
	}

	return products, nil
}

// GetPaymentProductById 根据产品ID获取支付产品
func (d *Dao) GetPaymentProductById(ctx context.Context, productId string) (*po.PaymentProduct, error) {
	// 先从缓存获取
	product, err := d.getPaymentProductFromCache(ctx, productId)
	if err == nil && product != nil {
		return product, nil
	}

	query := "SELECT id, product_id, product_name, product_type, price, currency, coin_amount, vip_days, enabled, sort_order, created_at, updated_at FROM payment_products WHERE product_id = ?"

	// 获取数据库连接
	db, err := d.mysql.DB("0")
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}

	var result po.PaymentProduct
	err = db.QueryRowContext(ctx, query, productId).Scan(
		&result.ID, &result.ProductId, &result.ProductName, &result.ProductType,
		&result.Price, &result.Currency, &result.CoinAmount, &result.VipDays,
		&result.Enabled, &result.SortOrder, &result.CreatedAt, &result.UpdatedAt)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		logger.LogErrorf("Failed to get payment product: %v", err)
		return nil, fmt.Errorf("failed to get payment product: %w", err)
	}

	// 设置缓存
	err = d.setPaymentProductCache(ctx, &result)
	if err != nil {
		logger.LogErrorf("Failed to set payment product cache: %v", err)
	}

	return &result, nil
}

// 缓存相关方法
func (d *Dao) getPaymentMethodsFromCache(ctx context.Context) ([]*po.PaymentMethodConfig, error) {
	key := "payment:methods:enabled"
	data, err := d.cache.GetString(ctx, key)
	if err != nil {
		return nil, err
	}

	var methods []*po.PaymentMethodConfig
	err = json.Unmarshal([]byte(data), &methods)
	if err != nil {
		return nil, err
	}

	return methods, nil
}

func (d *Dao) setPaymentMethodsCache(ctx context.Context, methods []*po.PaymentMethodConfig) error {
	key := "payment:methods:enabled"
	data, err := json.Marshal(methods)
	if err != nil {
		return err
	}

	_, err = d.cache.Set(ctx, key, string(data), int64(model.CachePaymentMethodExpire))
	return err
}

func (d *Dao) getPaymentMethodFromCache(ctx context.Context, method int32) (*po.PaymentMethodConfig, error) {
	key := fmt.Sprintf(model.RedisPaymentMethodId, method)
	data, err := d.cache.GetString(ctx, key)
	if err != nil {
		return nil, err
	}

	var methodConfig po.PaymentMethodConfig
	err = json.Unmarshal([]byte(data), &methodConfig)
	if err != nil {
		return nil, err
	}

	return &methodConfig, nil
}

func (d *Dao) setPaymentMethodCache(ctx context.Context, methodConfig *po.PaymentMethodConfig) error {
	key := fmt.Sprintf(model.RedisPaymentMethodId, methodConfig.Method)
	data, err := json.Marshal(methodConfig)
	if err != nil {
		return err
	}

	_, err = d.cache.Set(ctx, key, string(data), int64(model.CachePaymentMethodExpire))
	return err
}

func (d *Dao) getPaymentProductFromCache(ctx context.Context, productId string) (*po.PaymentProduct, error) {
	key := fmt.Sprintf(model.RedisPaymentProductId, productId)
	data, err := d.cache.GetString(ctx, key)
	if err != nil {
		return nil, err
	}

	var product po.PaymentProduct
	err = json.Unmarshal([]byte(data), &product)
	if err != nil {
		return nil, err
	}

	return &product, nil
}

func (d *Dao) setPaymentProductCache(ctx context.Context, product *po.PaymentProduct) error {
	key := fmt.Sprintf(model.RedisPaymentProductId, product.ProductId)
	data, err := json.Marshal(product)
	if err != nil {
		return err
	}

	_, err = d.cache.Set(ctx, key, string(data), int64(model.CachePaymentProductExpire))
	return err
}

// generateProductsCacheKey 生成产品列表缓存键
func (d *Dao) generateProductsCacheKey(productType *int32, enabled *bool) string {
	key := "payment:products"

	if productType != nil {
		key += fmt.Sprintf(":type_%d", *productType)
	}

	if enabled != nil && *enabled {
		key += ":enabled"
	}

	return key
}

// getPaymentProductsFromCache 从缓存获取产品列表
func (d *Dao) getPaymentProductsFromCache(ctx context.Context, cacheKey string) ([]*po.PaymentProduct, error) {
	data, err := d.cache.GetString(ctx, cacheKey)
	if err != nil {
		return nil, err
	}

	var products []*po.PaymentProduct
	err = json.Unmarshal([]byte(data), &products)
	if err != nil {
		return nil, err
	}

	return products, nil
}

// setPaymentProductsCache 设置产品列表缓存
func (d *Dao) setPaymentProductsCache(ctx context.Context, cacheKey string, products []*po.PaymentProduct) error {
	data, err := json.Marshal(products)
	if err != nil {
		return err
	}

	_, err = d.cache.Set(ctx, cacheKey, string(data), int64(model.CachePaymentProductExpire))
	return err
}

// CreatePaymentMethod 创建支付方式配置
func (d *Dao) CreatePaymentMethod(ctx context.Context, method *po.PaymentMethodConfig) error {
	query := `INSERT INTO payment_methods (method, name, enabled, config, sort_order, created_at, updated_at)
			  VALUES (?, ?, ?, ?, ?, NOW(), NOW())`

	// 序列化配置
	configStr, err := json.Marshal(method.Config)
	if err != nil {
		return fmt.Errorf("failed to marshal config: %w", err)
	}

	// 获取数据库连接
	db, err := d.mysql.DB("0")
	if err != nil {
		return fmt.Errorf("failed to get database connection: %w", err)
	}

	_, err = db.ExecContext(ctx, query, method.Method, method.Name, method.Enabled,
		string(configStr), method.SortOrder)
	if err != nil {
		logger.LogErrorf("Failed to create payment method: %v", err)
		return fmt.Errorf("failed to create payment method: %w", err)
	}

	return nil
}

// UpdatePaymentMethod 更新支付方式配置
func (d *Dao) UpdatePaymentMethod(ctx context.Context, method int32, updates map[string]interface{}) error {
	if len(updates) == 0 {
		return nil
	}

	// 构建更新语句
	setParts := []string{}
	args := []interface{}{}

	for field, value := range updates {
		if field == "config" {
			// 配置字段需要序列化
			if configMap, ok := value.(map[string]string); ok {
				configStr, err := json.Marshal(configMap)
				if err != nil {
					return fmt.Errorf("failed to marshal config: %w", err)
				}
				setParts = append(setParts, "config = ?")
				args = append(args, string(configStr))
			}
		} else {
			setParts = append(setParts, fmt.Sprintf("%s = ?", field))
			args = append(args, value)
		}
	}

	setParts = append(setParts, "updated_at = NOW()")
	args = append(args, method)

	query := fmt.Sprintf("UPDATE payment_methods SET %s WHERE method = ?", strings.Join(setParts, ", "))

	// 获取数据库连接
	db, err := d.mysql.DB("0")
	if err != nil {
		return fmt.Errorf("failed to get database connection: %w", err)
	}

	_, err = db.ExecContext(ctx, query, args...)
	if err != nil {
		logger.LogErrorf("Failed to update payment method: %v", err)
		return fmt.Errorf("failed to update payment method: %w", err)
	}

	// 删除缓存
	cacheKey := fmt.Sprintf(model.RedisPaymentMethodId, method)
	err = d.cache.DelKey(ctx, cacheKey)
	if err != nil {
		return err
	}

	return nil
}

// CreatePaymentProduct 创建支付产品配置
func (d *Dao) CreatePaymentProduct(ctx context.Context, product *po.PaymentProduct) error {
	query := `INSERT INTO payment_products (product_id, product_name, product_type, price, currency,
			  coin_amount, vip_days, enabled, sort_order, created_at, updated_at)
			  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`

	// 获取数据库连接
	db, err := d.mysql.DB("0")
	if err != nil {
		return fmt.Errorf("failed to get database connection: %w", err)
	}

	_, err = db.ExecContext(ctx, query, product.ProductId, product.ProductName, product.ProductType,
		product.Price, product.Currency, product.CoinAmount, product.VipDays,
		product.Enabled, product.SortOrder)
	if err != nil {
		logger.LogErrorf("Failed to create payment product: %v", err)
		return fmt.Errorf("failed to create payment product: %w", err)
	}

	return nil
}

// UpdatePaymentProduct 更新支付产品配置
func (d *Dao) UpdatePaymentProduct(ctx context.Context, productId string, updates map[string]interface{}) error {
	if len(updates) == 0 {
		return nil
	}

	// 构建更新语句
	setParts := []string{}
	args := []interface{}{}

	for field, value := range updates {
		setParts = append(setParts, fmt.Sprintf("%s = ?", field))
		args = append(args, value)
	}

	setParts = append(setParts, "updated_at = NOW()")
	args = append(args, productId)

	query := fmt.Sprintf("UPDATE payment_products SET %s WHERE product_id = ?", strings.Join(setParts, ", "))

	// 获取数据库连接
	db, err := d.mysql.DB("0")
	if err != nil {
		return fmt.Errorf("failed to get database connection: %w", err)
	}

	_, err = db.ExecContext(ctx, query, args...)
	if err != nil {
		logger.LogErrorf("Failed to update payment product: %v", err)
		return fmt.Errorf("failed to update payment product: %w", err)
	}

	// 删除缓存
	cacheKey := fmt.Sprintf(model.RedisPaymentProductId, productId)
	err = d.cache.DelKey(ctx, cacheKey)
	if err != nil {
		return err
	}

	return nil
}
