package dao

import (
	"context"
	"creativematrix.com/beyondreading/pkg/ecode"
	"encoding/json"
	"errors"
	"fmt"

	"creativematrix.com/beyondreading/app/base/payment/model"
	"creativematrix.com/beyondreading/app/common/po"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/mongo"
	"go.mongodb.org/mongo-driver/bson"
)

// GetPaymentMethods 获取支付方式列表
func (d *Dao) GetPaymentMethods(ctx context.Context, enabled *bool) ([]*po.PaymentMethodConfig, error) {
	// 先从缓存获取
	if enabled == nil || *enabled {
		methods, err := d.getPaymentMethodsFromCache(ctx)
		if err == nil && methods != nil {
			return methods, nil
		}
	}

	// 构建SQL查询
	query := "SELECT id, method, name, enabled, config, sort_order, created_at, updated_at FROM payment_methods"
	args := []interface{}{}

	if enabled != nil {
		query += " WHERE enabled = ?"
		args = append(args, *enabled)
	}

	query += " ORDER BY method ASC"

	rows, err := d.mysql.QueryContext(ctx, query, args...)
	if err != nil {
		logger.LogErrorf("Failed to query payment methods: %v", err)
		return nil, err
	}
	defer rows.Close()

	var methods []*po.PaymentMethodConfig
	for rows.Next() {
		method := &po.PaymentMethodConfig{}
		var configStr string
		err := rows.Scan(&method.ID, &method.Method, &method.Name, &method.Enabled,
			&configStr, &method.SortOrder, &method.CreatedAt, &method.UpdatedAt)
		if err != nil {
			logger.LogErrorf("Failed to scan payment method: %v", err)
			continue
		}

		// 解析配置JSON
		if configStr != "" {
			if err := json.Unmarshal([]byte(configStr), &method.Config); err != nil {
				logger.LogErrorf("Failed to unmarshal method config: %v", err)
				method.Config = make(map[string]string)
			}
		} else {
			method.Config = make(map[string]string)
		}

		methods = append(methods, method)
	}

	// 设置缓存（仅缓存启用的支付方式）
	if enabled == nil || *enabled {
		err = d.setPaymentMethodsCache(ctx, methods)
		if err != nil {
			logger.LogErrorf("Failed to set payment methods cache: %v", err)
		}
	}

	return methods, nil
}

// GetPaymentMethodByMethod 根据支付方式获取配置
func (d *Dao) GetPaymentMethodByMethod(ctx context.Context, method int32) (*po.PaymentMethodConfig, error) {
	// 先从缓存获取
	methodConfig, err := d.getPaymentMethodFromCache(ctx, method)
	if err == nil && methodConfig != nil {
		return methodConfig, nil
	}

	query := "SELECT id, method, name, enabled, config, sort_order, created_at, updated_at FROM payment_methods WHERE method = ?"

	var result po.PaymentMethodConfig
	var configStr string
	err = d.mysql.QueryRowContext(ctx, query, method).Scan(
		&result.ID, &result.Method, &result.Name, &result.Enabled,
		&configStr, &result.SortOrder, &result.CreatedAt, &result.UpdatedAt)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		logger.LogErrorf("Failed to get payment method: %v", err)
		return nil, err
	}

	// 解析配置JSON
	if configStr != "" {
		if err := json.Unmarshal([]byte(configStr), &result.Config); err != nil {
			logger.LogErrorf("Failed to unmarshal method config: %v", err)
			result.Config = make(map[string]string)
		}
	} else {
		result.Config = make(map[string]string)
	}

	// 设置缓存
	err = d.setPaymentMethodCache(ctx, &result)
	if err != nil {
		logger.LogErrorf("Failed to set payment method cache: %v", err)
	}

	return &result, nil
}

// GetPaymentProducts 获取支付产品列表
func (d *Dao) GetPaymentProducts(ctx context.Context, productType *int32, enabled *bool) ([]*po.PaymentProduct, error) {
	// 构建SQL查询
	query := "SELECT id, product_id, product_name, product_type, price, currency, coin_amount, vip_days, enabled, sort_order, created_at, updated_at FROM payment_products"
	args := []interface{}{}
	conditions := []string{}

	if productType != nil {
		conditions = append(conditions, "product_type = ?")
		args = append(args, *productType)
	}
	if enabled != nil {
		conditions = append(conditions, "enabled = ?")
		args = append(args, *enabled)
	}

	if len(conditions) > 0 {
		query += " WHERE " + strings.Join(conditions, " AND ")
	}

	query += " ORDER BY sort_order ASC, created_at ASC"

	rows, err := d.mysql.QueryContext(ctx, query, args...)
	if err != nil {
		logger.LogErrorf("Failed to query payment products: %v", err)
		return nil, err
	}
	defer rows.Close()

	var products []*po.PaymentProduct
	for rows.Next() {
		product := &po.PaymentProduct{}
		err := rows.Scan(&product.ID, &product.ProductId, &product.ProductName, &product.ProductType,
			&product.Price, &product.Currency, &product.CoinAmount, &product.VipDays,
			&product.Enabled, &product.SortOrder, &product.CreatedAt, &product.UpdatedAt)
		if err != nil {
			logger.LogErrorf("Failed to scan payment product: %v", err)
			continue
		}

		products = append(products, product)
	}

	return products, nil
}

// GetPaymentProductById 根据产品ID获取支付产品
func (d *Dao) GetPaymentProductById(ctx context.Context, productId string) (*po.PaymentProduct, error) {
	// 先从缓存获取
	product, err := d.getPaymentProductFromCache(ctx, productId)
	if err == nil && product != nil {
		return product, nil
	}

	query := "SELECT id, product_id, product_name, product_type, price, currency, coin_amount, vip_days, enabled, sort_order, created_at, updated_at FROM payment_products WHERE product_id = ?"

	var result po.PaymentProduct
	err = d.mysql.QueryRowContext(ctx, query, productId).Scan(
		&result.ID, &result.ProductId, &result.ProductName, &result.ProductType,
		&result.Price, &result.Currency, &result.CoinAmount, &result.VipDays,
		&result.Enabled, &result.SortOrder, &result.CreatedAt, &result.UpdatedAt)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		logger.LogErrorf("Failed to get payment product: %v", err)
		return nil, err
	}

	// 设置缓存
	err = d.setPaymentProductCache(ctx, &result)
	if err != nil {
		logger.LogErrorf("Failed to set payment product cache: %v", err)
	}

	return &result, nil
}

// 缓存相关方法
func (d *Dao) getPaymentMethodsFromCache(ctx context.Context) ([]*po.PaymentMethodConfig, error) {
	key := "payment:methods:enabled"
	data, err := d.cache.GetString(ctx, key)
	if err != nil {
		return nil, err
	}

	var methods []*po.PaymentMethodConfig
	err = json.Unmarshal([]byte(data), &methods)
	if err != nil {
		return nil, err
	}

	return methods, nil
}

func (d *Dao) setPaymentMethodsCache(ctx context.Context, methods []*po.PaymentMethodConfig) error {
	key := "payment:methods:enabled"
	data, err := json.Marshal(methods)
	if err != nil {
		return err
	}

	_, err = d.cache.Set(ctx, key, string(data), int64(model.CachePaymentMethodExpire))
	return err
}

func (d *Dao) getPaymentMethodFromCache(ctx context.Context, method int32) (*po.PaymentMethodConfig, error) {
	key := fmt.Sprintf(model.RedisPaymentMethodId, method)
	data, err := d.cache.GetString(ctx, key)
	if err != nil {
		return nil, err
	}

	var methodConfig po.PaymentMethodConfig
	err = json.Unmarshal([]byte(data), &methodConfig)
	if err != nil {
		return nil, err
	}

	return &methodConfig, nil
}

func (d *Dao) setPaymentMethodCache(ctx context.Context, methodConfig *po.PaymentMethodConfig) error {
	key := fmt.Sprintf(model.RedisPaymentMethodId, methodConfig.Method)
	data, err := json.Marshal(methodConfig)
	if err != nil {
		return err
	}

	_, err = d.cache.Set(ctx, key, string(data), int64(model.CachePaymentMethodExpire))
	return err
}

func (d *Dao) getPaymentProductFromCache(ctx context.Context, productId string) (*po.PaymentProduct, error) {
	key := fmt.Sprintf(model.RedisPaymentProductId, productId)
	data, err := d.cache.GetString(ctx, key)
	if err != nil {
		return nil, err
	}

	var product po.PaymentProduct
	err = json.Unmarshal([]byte(data), &product)
	if err != nil {
		return nil, err
	}

	return &product, nil
}

func (d *Dao) setPaymentProductCache(ctx context.Context, product *po.PaymentProduct) error {
	key := fmt.Sprintf(model.RedisPaymentProductId, product.ProductId)
	data, err := json.Marshal(product)
	if err != nil {
		return err
	}

	_, err = d.cache.Set(ctx, key, string(data), int64(model.CachePaymentProductExpire))
	return err
}
