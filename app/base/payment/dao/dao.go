package dao

import (
	"context"

	"creativematrix.com/beyondreading/app/base/payment/conf"
	"creativematrix.com/beyondreading/app/base/payment/model"
	"creativematrix.com/beyondreading/pkg/mongo"
	"creativematrix.com/beyondreading/pkg/rabbitmq"
	"creativematrix.com/beyondreading/pkg/redis"
)

type Dao struct {
	conf        *conf.Config
	cache       redis.Redis
	schema      map[string]*mongo.Model
	rabbitMQ    *rabbitmq.Broker
	paymentConn *mongo.Connection
}

var paymentTables = []string{
	model.TablePaymentOrders,
	model.TablePaymentCallbacks,
	model.TablePaymentRefunds,
	model.TablePaymentMethods,
	model.TablePaymentProducts,
	model.TablePaymentLogs,
}

func Load(c *conf.Config) *Dao {
	// MongoDB连接
	paymentConn := mongo.Connect(c.MongodbPayment)

	// 初始化MongoDB模型
	schema := make(map[string]*mongo.Model)
	for _, name := range paymentTables {
		schema[name] = paymentConn.Model(name)
	}

	// Redis连接
	redisConn := redis.Load(c.RedisPayment)

	// RabbitMQ连接
	rabbitMQConn := rabbitmq.NewBroker(
		c.RabbitMQ.URL,
		c.RabbitMQ.MatrixEx)

	return &Dao{
		conf:        c,
		cache:       redisConn,
		schema:      schema,
		rabbitMQ:    rabbitMQConn,
		paymentConn: paymentConn,
	}
}

func (d *Dao) Ping(ctx context.Context) error {
	return nil
}

func (d *Dao) Close() {

}

// PublishMessage 发布消息到RabbitMQ
func (d *Dao) PublishMessage(ctx context.Context, routingKey string, message interface{}) error {
	return d.rabbitMQ.Publish(routingKey, message)
}
