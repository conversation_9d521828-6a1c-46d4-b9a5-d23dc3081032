package dao

import (
	"context"
	"fmt"

	"creativematrix.com/beyondreading/gen/app/base/payment/conf"
	"creativematrix.com/beyondreading/pkg/mongo"
	"creativematrix.com/beyondreading/pkg/redis"
	"creativematrix.com/beyondreading/pkg/rabbitmq"
)

type Dao struct {
	conf     *conf.Config
	mongo    *mongo.Connection
	cache    redis.Redis
	rabbitMQ *rabbitmq.Broker
	schema   map[string]*mongo.Model
}

func Load(c *conf.Config) *Dao {
	// MongoDB连接
	mongoConn := mongo.Connect(c.MongoDB.URI)

	// Redis连接
	redisConn := redis.Load(&redis.Config{
		Addr:     c.Redis.Addr,
		Password: c.Redis.Password,
		DB:       c.Redis.DB,
	})

	// RabbitMQ连接
	rabbitMQConn := rabbitmq.NewBroker(&rabbitmq.Config{
		URL:      c.RabbitMQ.URL,
		Exchange: c.RabbitMQ.Exchange,
	})

	// 初始化MongoDB模型
	schema := make(map[string]*mongo.Model)
	schema["payment_orders"] = mongoConn.Model("payment_orders")
	schema["payment_callbacks"] = mongoConn.Model("payment_callbacks")
	schema["payment_refunds"] = mongoConn.Model("payment_refunds")
	schema["payment_methods"] = mongoConn.Model("payment_methods")
	schema["payment_products"] = mongoConn.Model("payment_products")
	schema["payment_logs"] = mongoConn.Model("payment_logs")

	return &Dao{
		conf:     c,
		mongo:    mongoConn,
		cache:    redisConn,
		rabbitMQ: rabbitMQConn,
		schema:   schema,
	}
}

func (d *Dao) Ping(ctx context.Context) error {
	// 检查MongoDB连接
	if err := d.mongo.HealthCheck(); err != nil {
		return fmt.Errorf("mongodb ping failed: %w", err)
	}

	// 检查Redis连接
	if _, err := d.cache.RDo(ctx, "PING"); err != nil {
		return fmt.Errorf("redis ping failed: %w", err)
	}

	return nil
}

func (d *Dao) Close() {
	// 关闭Redis连接
	if err := d.cache.RClose(); err != nil {
		fmt.Printf("Failed to close redis connection: %v\n", err)
	}

	// 关闭RabbitMQ连接
	if d.rabbitMQ != nil {
		d.rabbitMQ.Close()
	}
}

// PublishMessage 发布消息到RabbitMQ
func (d *Dao) PublishMessage(ctx context.Context, routingKey string, message interface{}) error {
	return d.rabbitMQ.Publish(ctx, routingKey, message)
}
