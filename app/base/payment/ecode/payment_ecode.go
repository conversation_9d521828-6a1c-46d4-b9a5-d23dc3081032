package ecode

import "creativematrix.com/beyondreading/pkg/ecode"

// Payment模块错误码定义
var (
	// 支付订单相关错误码 (5000-5099)
	PaymentOrderCreateFailed = ecode.New(5001, "Failed to create payment order")
	PaymentOrderNotFound     = ecode.New(5002, "Payment order not found")
	PaymentOrderQueryFailed  = ecode.New(5003, "Failed to query payment order")
	PaymentOrderUpdateFailed = ecode.New(5004, "Failed to update payment order")
	PaymentOrderDeleteFailed = ecode.New(5005, "Failed to delete payment order")
	PaymentOrderExpired      = ecode.New(5006, "Payment order has expired")
	PaymentOrderCancelled    = ecode.New(5007, "Payment order has been cancelled")
	PaymentOrderPaid         = ecode.New(5008, "Payment order has been paid")

	// 支付回调相关错误码 (5100-5199)
	PaymentCallbackCreateFailed = ecode.New(5101, "Failed to create payment callback")
	PaymentCallbackNotFound     = ecode.New(5102, "Payment callback not found")
	PaymentCallbackQueryFailed  = ecode.New(5103, "Failed to query payment callback")
	PaymentCallbackUpdateFailed = ecode.New(5104, "Failed to update payment callback")
	PaymentCallbackDeleteFailed = ecode.New(5105, "Failed to delete payment callback")
	PaymentCallbackInvalidId    = ecode.New(5106, "Invalid payment callback ID")
	PaymentCallbackProcessed    = ecode.New(5107, "Payment callback already processed")

	// 支付退款相关错误码 (5200-5299)
	PaymentRefundCreateFailed = ecode.New(5201, "Failed to create payment refund")
	PaymentRefundNotFound     = ecode.New(5202, "Payment refund not found")
	PaymentRefundQueryFailed  = ecode.New(5203, "Failed to query payment refund")
	PaymentRefundUpdateFailed = ecode.New(5204, "Failed to update payment refund")
	PaymentRefundDeleteFailed = ecode.New(5205, "Failed to delete payment refund")
	PaymentRefundExpired      = ecode.New(5206, "Payment refund period has expired")
	PaymentRefundProcessed    = ecode.New(5207, "Payment refund already processed")

	// 支付方式相关错误码 (5300-5399)
	PaymentMethodNotFound     = ecode.New(5301, "Payment method not found")
	PaymentMethodNotSupported = ecode.New(5302, "Payment method not supported")
	PaymentMethodDisabled     = ecode.New(5303, "Payment method is disabled")
	PaymentMethodConfigError  = ecode.New(5304, "Payment method configuration error")

	// 支付产品相关错误码 (5400-5499)
	PaymentProductNotFound  = ecode.New(5401, "Payment product not found")
	PaymentProductDisabled  = ecode.New(5402, "Payment product is disabled")
	PaymentProductExpired   = ecode.New(5403, "Payment product has expired")
	PaymentProductInvalid   = ecode.New(5404, "Invalid payment product")

	// 支付验证相关错误码 (5500-5599)
	PaymentVerificationFailed = ecode.New(5501, "Payment verification failed")
	PaymentSignatureInvalid   = ecode.New(5502, "Invalid payment signature")
	PaymentReceiptInvalid     = ecode.New(5503, "Invalid payment receipt")
	PaymentAmountMismatch     = ecode.New(5504, "Payment amount mismatch")
	PaymentCurrencyInvalid    = ecode.New(5505, "Invalid payment currency")

	// 支付服务相关错误码 (5600-5699)
	PaymentServiceUnavailable = ecode.New(5601, "Payment service unavailable")
	PaymentServiceTimeout     = ecode.New(5602, "Payment service timeout")
	PaymentServiceError       = ecode.New(5603, "Payment service error")
	PaymentConfigError        = ecode.New(5604, "Payment configuration error")
)
