package conf

import (
	"creativematrix.com/beyondreading/pkg/config"
	"creativematrix.com/beyondreading/pkg/rabbitmq"
	"creativematrix.com/beyondreading/pkg/redis"
	"fmt"
)

type Config struct {
	config.Base

	Log struct {
		Level string
	}

	MongodbPayment string

	RabbitMQ struct {
		URL      string
		MatrixEx *rabbitmq.ExchangeConfig
	}

	RedisPayment *redis.Config

	GooglePay struct {
		PackageName    string `toml:"package_name"`
		ServiceAccount string `toml:"service_account"`
		PrivateKey     string `toml:"private_key"`
	} `toml:"google_pay"`

	ApplePay struct {
		BundleId     string `toml:"bundle_id"`
		Environment  string `toml:"environment"` // sandbox or production
		SharedSecret string `toml:"shared_secret"`
	} `toml:"apple_pay"`

	PayPal struct {
		ClientId     string `toml:"client_id"`
		ClientSecret string `toml:"client_secret"`
		Environment  string `toml:"environment"` // sandbox or live
		WebhookId    string `toml:"webhook_id"`
	} `toml:"paypal"`

	Alipay struct {
		AppId      string `toml:"app_id"`
		PrivateKey string `toml:"private_key"`
		PublicKey  string `toml:"public_key"`
		NotifyUrl  string `toml:"notify_url"`
		ReturnUrl  string `toml:"return_url"`
		Gateway    string `toml:"gateway"`
	} `toml:"alipay"`

	WechatPay struct {
		AppId     string `toml:"app_id"`
		MchId     string `toml:"mch_id"`
		ApiKey    string `toml:"api_key"`
		NotifyUrl string `toml:"notify_url"`
		Gateway   string `toml:"gateway"`
	} `toml:"wechat_pay"`

	Payment struct {
		OrderExpireMinutes int32  `toml:"order_expire_minutes"`
		CallbackRetryTimes int32  `toml:"callback_retry_times"`
		RefundTimeoutDays  int32  `toml:"refund_timeout_days"`
		DefaultCurrency    string `toml:"default_currency"`
	} `toml:"payment"`
}

func Load(app string) *Config {
	var conf = new(Config)
	if err := config.Load(app, conf); err != nil {
		panic(fmt.Sprintf("config load failed: %v", err))
	}
	return conf
}
