package model

import "time"

// MongoDB表名常量
const (
	TablePaymentOrders    = "payment_orders"
	TablePaymentCallbacks = "payment_callbacks"
	TablePaymentRefunds   = "payment_refunds"
	TablePaymentMethods   = "payment_methods"
	TablePaymentProducts  = "payment_products"
	TablePaymentLogs      = "payment_logs"
)

// Redis缓存Key模板
const (
	RedisPaymentOrderId    = "payment:order:%s"
	RedisPaymentMethodId   = "payment:method:%d"
	RedisPaymentProductId  = "payment:product:%s"
	RedisPaymentCallbackId = "payment:callback:%s"
)

// 缓存过期时间
const (
	CachePaymentOrderExpire  = 24 * time.Hour  // 支付订单缓存24小时
	CachePaymentMethodExpire = 1 * time.Hour   // 支付方式缓存1小时
	CachePaymentProductExpire = 30 * time.Minute // 支付产品缓存30分钟
	CachePaymentCallbackExpire = 1 * time.Hour  // 支付回调缓存1小时
)
