package svc

import (
	"context"

	"creativematrix.com/beyondreading/app/base/payment/conf"
	"creativematrix.com/beyondreading/app/base/payment/dao"
	"creativematrix.com/beyondreading/app/base/payment/service"
)

type PaymentSvc struct {
	conf            *conf.Config
	dao             *dao.Dao
	paymentService  *service.PaymentService
	orderService    *service.OrderService
	callbackService *service.CallbackService
	refundService   *service.RefundService
}

func Load(c *conf.Config) *PaymentSvc {
	svc := &PaymentSvc{
		conf:            c,
		dao:             dao.Load(c),
		paymentService:  service.NewPaymentService(c),
		orderService:    service.NewOrderService(c),
		callbackService: service.NewCallbackService(c),
		refundService:   service.NewRefundService(c),
	}

	return svc
}

func (s *PaymentSvc) Ping(ctx context.Context) error {
	return s.dao.Ping(ctx)
}

func (s *PaymentSvc) Close() {
	s.dao.Close()
}
