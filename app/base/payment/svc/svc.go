package svc

import (
	"context"
	"creativematrix.com/beyondreading/app/base/payment/conf"
	"creativematrix.com/beyondreading/app/base/payment/dao"
)

type PaymentSvc struct {
	conf *conf.Config
	dao  *dao.Dao
}

func Load(c *conf.Config) *PaymentSvc {
	svc := &PaymentSvc{
		conf: c,
		dao:  dao.Load(c),
	}

	return svc
}

func (s *PaymentSvc) Ping(ctx context.Context) error {
	return s.dao.Ping(ctx)
}

func (s *PaymentSvc) Close() {
	s.dao.Close()
}
