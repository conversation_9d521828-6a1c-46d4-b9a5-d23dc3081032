package svc

import (
	"creativematrix.com/beyondreading/gen/common/po"
	pb "creativematrix.com/beyondreading/gen/proto/payment"
)

// convertOrderToPB 转换支付订单为protobuf格式
func (s *PaymentSvc) convertOrderToPB(order *po.PaymentOrder) *pb.PaymentOrderInfo {
	if order == nil {
		return nil
	}

	pbOrder := &pb.PaymentOrderInfo{
		OrderId:       order.OrderId,
		UserId:        order.UserId,
		PaymentMethod: pb.PaymentMethod(order.PaymentMethod),
		PaymentType:   pb.PaymentType(order.PaymentType),
		Amount:        order.Amount,
		Currency:      order.Currency,
		ProductId:     order.ProductId,
		ProductName:   order.ProductName,
		Description:   order.Description,
		Status:        pb.OrderStatus(order.Status),
		TransactionId: order.TransactionId,
		Metadata:      order.Metadata,
		CreatedAt:     order.CreatedAt.Unix(),
		UpdatedAt:     order.UpdatedAt.Unix(),
		ExpiredAt:     order.ExpiredAt.Unix(),
		FailureReason: order.FailureReason,
	}

	if order.PaidAt != nil {
		pbOrder.PaidAt = order.PaidAt.Unix()
	}

	return pbOrder
}

// convertCallbackToPB 转换支付回调为protobuf格式
func (s *PaymentSvc) convertCallbackToPB(callback *po.PaymentCallback) *pb.ProcessPaymentCallbackReq {
	if callback == nil {
		return nil
	}

	return &pb.ProcessPaymentCallbackReq{
		PaymentMethod: pb.PaymentMethod(callback.PaymentMethod),
		OrderId:       callback.OrderId,
		TransactionId: callback.TransactionId,
		Status:        pb.OrderStatus(callback.Status),
		Amount:        callback.Amount,
		Currency:      callback.Currency,
		CallbackData:  callback.CallbackData,
		Signature:     callback.Signature,
	}
}

// convertRefundToPB 转换退款记录为protobuf格式
func (s *PaymentSvc) convertRefundToPB(refund *po.PaymentRefund) *pb.RefundPaymentReq {
	if refund == nil {
		return nil
	}

	return &pb.RefundPaymentReq{
		OrderId:      refund.OrderId,
		UserId:       refund.UserId,
		RefundAmount: refund.RefundAmount,
		Reason:       refund.Reason,
		RefundId:     refund.RefundId,
	}
}
