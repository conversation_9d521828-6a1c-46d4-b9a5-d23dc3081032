package svc

import (
	"creativematrix.com/beyondreading/app/common/po"
	pb "creativematrix.com/beyondreading/proto/payment"
)

// convertOrderToPB 转换支付订单为protobuf格式
func (s *PaymentSvc) convertOrderToPB(order *po.PaymentOrder) *pb.PaymentOrderInfo {
	if order == nil {
		return nil
	}

	pbOrder := &pb.PaymentOrderInfo{
		OrderId:       order.OrderId,
		UserId:        order.UserId,
		PaymentMethod: pb.PaymentMethod(order.PaymentMethod),
		PaymentType:   pb.PaymentType(order.PaymentType),
		Amount:        order.Amount,
		Currency:      order.Currency,
		ProductId:     order.ProductId,
		ProductName:   order.ProductName,
		Description:   order.Description,
		Status:        pb.OrderStatus(order.Status),
		TransactionId: order.TransactionId,
		Metadata:      order.Metadata,
		CreatedAt:     order.CreatedAt.Unix(),
		UpdatedAt:     order.UpdatedAt.Unix(),
		ExpiredAt:     order.ExpiredAt.Unix(),
		FailureReason: order.FailureReason,
	}

	if order.PaidAt != nil {
		pbOrder.PaidAt = order.PaidAt.Unix()
	}

	return pbOrder
}

// convertCallbackToPB 转换支付回调为protobuf格式
func (s *PaymentSvc) convertCallbackToPB(callback *po.PaymentCallback) *pb.ProcessPaymentCallbackReq {
	if callback == nil {
		return nil
	}

	return &pb.ProcessPaymentCallbackReq{
		PaymentMethod: pb.PaymentMethod(callback.PaymentMethod),
		OrderId:       callback.OrderId,
		TransactionId: callback.TransactionId,
		Status:        pb.OrderStatus(callback.Status),
		Amount:        callback.Amount,
		Currency:      callback.Currency,
		CallbackData:  callback.CallbackData,
		Signature:     callback.Signature,
	}
}

// convertRefundToPB 转换退款记录为protobuf格式
func (s *PaymentSvc) convertRefundToPB(refund *po.PaymentRefund) *pb.RefundPaymentReq {
	if refund == nil {
		return nil
	}

	return &pb.RefundPaymentReq{
		OrderId:      refund.OrderId,
		UserId:       refund.UserId,
		RefundAmount: refund.RefundAmount,
		Reason:       refund.Reason,
		RefundId:     refund.RefundId,
	}
}

// convertProductToPB 转换支付产品为protobuf格式
func (s *PaymentSvc) convertProductToPB(product *po.PaymentProduct) *pb.ProductInfo {
	if product == nil {
		return nil
	}

	pbProduct := &pb.ProductInfo{
		ProductId:   product.ProductId,
		ProductName: product.ProductName,
		ProductType: pb.PaymentType(product.ProductType),
		Price:       product.Price,
		Currency:    product.Currency,
		CoinAmount:  product.CoinAmount,
		VipDays:     product.VipDays,
		MonthlyDays: product.MonthlyDays,
		Description: product.Description,
		Enabled:     product.Enabled,
		SortOrder:   product.SortOrder,
		CreatedAt:   product.CreatedAt.Unix(),
		UpdatedAt:   product.UpdatedAt.Unix(),
	}

	return pbProduct
}

// isProductSupportedOnPlatform 判断产品是否支持指定平台
func (s *PaymentSvc) isProductSupportedOnPlatform(productId, platform string) bool {
	switch platform {
	case "ios":
		// iOS产品通常以com.开头或包含ios关键字
		return len(productId) > 4 && (productId[:4] == "com." ||
			productId == "vip_monthly_ios" || productId == "vip_yearly_ios" ||
			productId == "coins_100_ios" || productId == "coins_500_ios" || productId == "coins_1000_ios")
	case "android":
		// Android产品通常不以com.开头，或包含android关键字
		return !(len(productId) > 4 && productId[:4] == "com.") ||
			productId == "vip_monthly_android" || productId == "vip_yearly_android" ||
			productId == "coins_100_android" || productId == "coins_500_android" || productId == "coins_1000_android"
	case "web", "h5":
		// Web产品通常是通用产品
		return !(len(productId) > 4 && productId[:4] == "com.") &&
			productId != "vip_monthly_ios" && productId != "vip_yearly_ios" &&
			productId != "coins_100_ios" && productId != "coins_500_ios" && productId != "coins_1000_ios"
	default:
		// 未知平台，返回所有产品
		return true
	}
}
