package svc

import (
	"context"
	"creativematrix.com/beyondreading/app/base/payment/conf"
	"creativematrix.com/beyondreading/pkg/logger"
	pb "creativematrix.com/beyondreading/proto/payment"
	"fmt"
	"github.com/BurntSushi/toml"
	"gotest.tools/assert"
	"testing"
)

func doInit() *PaymentSvc {
	app := "base-payment"
	cf, err := loadConf(app)
	if err != nil {
		panic(err)
	}

	logger.InitLog(app, cf.Log.Level)
	paymentSvc := Load(cf)

	return paymentSvc
}

func loadConf(app string) (*conf.Config, error) {
	cf := &conf.Config{}
	if _, err := toml.DecodeFile("../../../base.toml", cf); err != nil {
		return nil, err
	}
	if _, err := toml.DecodeFile(fmt.Sprintf("../cmd/%s.toml", app), cf); err != nil {
		return nil, err
	}
	return cf, nil
}

func TestPaymentSvc_CreatePaymentOrderVIPMonthly(t *testing.T) {
	paymentSvc := doInit()
	req := &pb.CreatePaymentOrderReq{
		UserId:        12345,                                           // 用户ID
		PaymentMethod: pb.PaymentMethod_GOOGLE_PAY,                     // 支付方式：Google Pay
		PaymentType:   pb.PaymentType_VIP_PURCHASE,                     // 支付类型：VIP购买
		Amount:        999,                                             // 支付金额（分）：$9.99
		Currency:      "USD",                                           // 货币类型
		ProductId:     "vip_monthly",                                   // 产品ID
		ProductName:   "VIP Monthly Subscription",                      // 产品名称
		Description:   "VIP monthly subscription for premium features", // 订单描述
		Metadata: map[string]string{ // 额外元数据
			"platform":    "android",
			"app_version": "1.0.0",
			"user_level":  "2",
		},
		ClientIp:  "*************",                                 // 客户端IP
		UserAgent: "BeyondReading/1.0.0 (Android 12)",              // 用户代理
		ReturnUrl: "https://app.beyondreading.com/payment/success", // 支付成功返回URL
		CancelUrl: "https://app.beyondreading.com/payment/cancel",  // 支付取消返回URL
	}

	got, err := paymentSvc.CreatePaymentOrder(context.Background(), req)
	if err != nil {
		logger.LogErrorf("Failed to create payment order: %v", err)
		return
	}

	logger.LogInfof("Payment order created: %s, payment URL: %s", got.Order.OrderId, got.PaymentUrl)

	assert.NilError(t, err)
}

func TestPaymentSvc_CreatePaymentOrderCoinRecharge(t *testing.T) {
	paymentSvc := doInit()
	req := &pb.CreatePaymentOrderReq{
		UserId:        12345,                            // 用户ID
		PaymentMethod: pb.PaymentMethod_PAYPAL,          // 支付方式：PayPal
		PaymentType:   pb.PaymentType_COIN_RECHARGE,     // 支付类型：书币充值
		Amount:        499,                              // 支付金额（分）：$4.99
		Currency:      "USD",                            // 货币类型
		ProductId:     "coins_500",                      // 产品ID
		ProductName:   "500 Coins",                      // 产品名称
		Description:   "Recharge 500 coins for reading", // 订单描述
		Metadata: map[string]string{ // 额外元数据
			"platform":    "ios",
			"coin_amount": "500",
			"bonus_coins": "50",
		},
		ClientIp:  "********",                        // 客户端IP
		UserAgent: "BeyondReading/1.0.0 (iOS 15.0)",  // 用户代理
		ReturnUrl: "beyondreading://payment/success", // 支付成功返回URL（App深链接）
		CancelUrl: "beyondreading://payment/cancel",  // 支付取消返回URL
	}

	got, err := paymentSvc.CreatePaymentOrder(context.Background(), req)
	if err != nil {
		logger.LogErrorf("Failed to create coin recharge order: %v", err)
		return
	}

	logger.LogInfof("Coin recharge order created: %s", got.Order.OrderId)
	assert.NilError(t, err)
}

func TestPaymentSvc_CreatePaymentOrderMonthly(t *testing.T) {
	paymentSvc := doInit()
	req := &pb.CreatePaymentOrderReq{
		UserId:        12345,                                        // 用户ID
		PaymentMethod: pb.PaymentMethod_APPLE_PAY,                   // 支付方式：Apple Pay
		PaymentType:   pb.PaymentType_MONTHLY_SUBSCRIPTION,          // 支付类型：包月订阅
		Amount:        599,                                          // 支付金额（分）：$5.99
		Currency:      "USD",                                        // 货币类型
		ProductId:     "monthly_subscription",                       // 产品ID
		ProductName:   "Monthly Reading Subscription",               // 产品名称
		Description:   "Monthly subscription for unlimited reading", // 订单描述
		Metadata: map[string]string{ // 额外元数据
			"platform":        "ios",
			"subscription_id": "sub_12345",
			"trial_period":    "7",
		},
		ClientIp:  "**********",                             // 客户端IP
		UserAgent: "BeyondReading/1.0.0 (iPhone; iOS 15.0)", // 用户代理
		ReturnUrl: "beyondreading://subscription/success",   // 支付成功返回URL
		CancelUrl: "beyondreading://subscription/cancel",    // 支付取消返回URL
	}

	got, err := paymentSvc.CreatePaymentOrder(context.Background(), req)
	if err != nil {
		logger.LogErrorf("Failed to create subscription order: %v", err)
		return
	}

	logger.LogInfof("Subscription order created: %s", got.Order.OrderId)
	assert.NilError(t, err)
}

func TestPaymentSvc_CreatePaymentOrderAliPay(t *testing.T) {
	paymentSvc := doInit()
	req := &pb.CreatePaymentOrderReq{
		UserId:        12345,                          // 用户ID
		PaymentMethod: pb.PaymentMethod_ALIPAY,        // 支付方式：支付宝
		PaymentType:   pb.PaymentType_VIP_PURCHASE,    // 支付类型：VIP购买
		Amount:        6800,                           // 支付金额（分）：¥68.00
		Currency:      "CNY",                          // 货币类型：人民币
		ProductId:     "vip_yearly",                   // 产品ID
		ProductName:   "VIP年费会员",                  // 产品名称
		Description:   "VIP年费会员，享受全站免费阅读", // 订单描述
		Metadata: map[string]string{ // 额外元数据
			"platform": "web",
			"region":   "CN",
			"language": "zh-CN",
		},
		ClientIp:  "***************",                                              // 客户端IP
		UserAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", // 用户代理
		ReturnUrl: "https://www.beyondreading.com/payment/success",                // 支付成功返回URL
		CancelUrl: "https://www.beyondreading.com/payment/cancel",                 // 支付取消返回URL
	}

	got, err := paymentSvc.CreatePaymentOrder(context.Background(), req)
	if err != nil {
		logger.LogErrorf("Failed to create alipay order: %v", err)
		return
	}

	logger.LogInfof("Alipay order created: %s, payment URL: %s", got.Order.OrderId, got.PaymentUrl)
	assert.NilError(t, err)
}

func TestPaymentSvc_GetPaymentMethods(t *testing.T) {
	paymentSvc := doInit()
	req := &pb.GetPaymentMethodsReq{}
	got, err := paymentSvc.GetPaymentMethods(context.Background(), req)
	if err != nil {
		logger.LogErrorf("Failed to create alipay order: %v", err)
		return
	}

	for _, method := range got.Methods {
		logger.LogInfof("Payment method:%v", method)
	}
	
	assert.NilError(t, err)
}
