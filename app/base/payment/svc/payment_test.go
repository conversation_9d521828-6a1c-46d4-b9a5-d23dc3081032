package svc

import (
	"context"
	"creativematrix.com/beyondreading/app/base/payment/conf"
	"creativematrix.com/beyondreading/pkg/logger"
	pb "creativematrix.com/beyondreading/proto/payment"
	"fmt"
	"github.com/BurntSushi/toml"
	"gotest.tools/assert"
	"testing"
)

func doInit() *PaymentSvc {
	app := "base-payment"
	cf, err := loadConf(app)
	if err != nil {
		panic(err)
	}

	logger.InitLog(app, cf.Log.Level)
	paymentSvc := Load(cf)

	return paymentSvc
}

func loadConf(app string) (*conf.Config, error) {
	cf := &conf.Config{}
	if _, err := toml.DecodeFile("../../../base.toml", cf); err != nil {
		return nil, err
	}
	if _, err := toml.DecodeFile(fmt.Sprintf("../cmd/%s.toml", app), cf); err != nil {
		return nil, err
	}
	return cf, nil
}

func TestPaymentSvc_CreatePaymentOrderVIPMonthly(t *testing.T) {
	paymentSvc := doInit()
	req := &pb.CreatePaymentOrderReq{
		UserId:        12345,                                           // 用户ID
		PaymentMethod: pb.PaymentMethod_GOOGLE_PAY,                     // 支付方式：Google Pay
		PaymentType:   pb.PaymentType_VIP_PURCHASE,                     // 支付类型：VIP购买
		Amount:        999,                                             // 支付金额（分）：$9.99
		Currency:      "USD",                                           // 货币类型
		ProductId:     "vip_monthly",                                   // 产品ID
		ProductName:   "VIP Monthly Subscription",                      // 产品名称
		Description:   "VIP monthly subscription for premium features", // 订单描述
		Metadata: map[string]string{ // 额外元数据
			"platform":    "android",
			"app_version": "1.0.0",
			"user_level":  "2",
		},
		ClientIp:  "*************",                                 // 客户端IP
		UserAgent: "BeyondReading/1.0.0 (Android 12)",              // 用户代理
		ReturnUrl: "https://app.beyondreading.com/payment/success", // 支付成功返回URL
		CancelUrl: "https://app.beyondreading.com/payment/cancel",  // 支付取消返回URL
	}

	got, err := paymentSvc.CreatePaymentOrder(context.Background(), req)
	if err != nil {
		logger.LogErrorf("Failed to create payment order: %v", err)
		return
	}

	logger.LogInfof("Payment order created: %s, payment URL: %s", got.Order.OrderId, got.PaymentUrl)

	assert.NilError(t, err)
}

func TestPaymentSvc_CreatePaymentOrderCoinRecharge(t *testing.T) {
	paymentSvc := doInit()
	req := &pb.CreatePaymentOrderReq{
		UserId:        12345,                            // 用户ID
		PaymentMethod: pb.PaymentMethod_PAYPAL,          // 支付方式：PayPal
		PaymentType:   pb.PaymentType_COIN_RECHARGE,     // 支付类型：书币充值
		Amount:        499,                              // 支付金额（分）：$4.99
		Currency:      "USD",                            // 货币类型
		ProductId:     "coins_500",                      // 产品ID
		ProductName:   "500 Coins",                      // 产品名称
		Description:   "Recharge 500 coins for reading", // 订单描述
		Metadata: map[string]string{ // 额外元数据
			"platform":    "ios",
			"coin_amount": "500",
			"bonus_coins": "50",
		},
		ClientIp:  "********",                        // 客户端IP
		UserAgent: "BeyondReading/1.0.0 (iOS 15.0)",  // 用户代理
		ReturnUrl: "beyondreading://payment/success", // 支付成功返回URL（App深链接）
		CancelUrl: "beyondreading://payment/cancel",  // 支付取消返回URL
	}

	got, err := paymentSvc.CreatePaymentOrder(context.Background(), req)
	if err != nil {
		logger.LogErrorf("Failed to create coin recharge order: %v", err)
		return
	}

	logger.LogInfof("Coin recharge order created: %s", got.Order.OrderId)
	assert.NilError(t, err)
}

func TestPaymentSvc_CreatePaymentOrderMonthly(t *testing.T) {
	paymentSvc := doInit()
	req := &pb.CreatePaymentOrderReq{
		UserId:        12345,                                        // 用户ID
		PaymentMethod: pb.PaymentMethod_APPLE_PAY,                   // 支付方式：Apple Pay
		PaymentType:   pb.PaymentType_MONTHLY_SUBSCRIPTION,          // 支付类型：包月订阅
		Amount:        599,                                          // 支付金额（分）：$5.99
		Currency:      "USD",                                        // 货币类型
		ProductId:     "monthly_subscription",                       // 产品ID
		ProductName:   "Monthly Reading Subscription",               // 产品名称
		Description:   "Monthly subscription for unlimited reading", // 订单描述
		Metadata: map[string]string{ // 额外元数据
			"platform":        "ios",
			"subscription_id": "sub_12345",
			"trial_period":    "7",
		},
		ClientIp:  "**********",                             // 客户端IP
		UserAgent: "BeyondReading/1.0.0 (iPhone; iOS 15.0)", // 用户代理
		ReturnUrl: "beyondreading://subscription/success",   // 支付成功返回URL
		CancelUrl: "beyondreading://subscription/cancel",    // 支付取消返回URL
	}

	got, err := paymentSvc.CreatePaymentOrder(context.Background(), req)
	if err != nil {
		logger.LogErrorf("Failed to create subscription order: %v", err)
		return
	}

	logger.LogInfof("Subscription order created: %s", got.Order.OrderId)
	assert.NilError(t, err)
}

func TestPaymentSvc_CreatePaymentOrderAliPay(t *testing.T) {
	paymentSvc := doInit()
	req := &pb.CreatePaymentOrderReq{
		UserId:        12345,                       // 用户ID
		PaymentMethod: pb.PaymentMethod_ALIPAY,     // 支付方式：支付宝
		PaymentType:   pb.PaymentType_VIP_PURCHASE, // 支付类型：VIP购买
		Amount:        6800,                        // 支付金额（分）：¥68.00
		Currency:      "CNY",                       // 货币类型：人民币
		ProductId:     "vip_yearly",                // 产品ID
		ProductName:   "VIP年费会员",                   // 产品名称
		Description:   "VIP年费会员，享受全站免费阅读",          // 订单描述
		Metadata: map[string]string{ // 额外元数据
			"platform": "web",
			"region":   "CN",
			"language": "zh-CN",
		},
		ClientIp:  "***************",                                              // 客户端IP
		UserAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", // 用户代理
		ReturnUrl: "https://www.beyondreading.com/payment/success",                // 支付成功返回URL
		CancelUrl: "https://www.beyondreading.com/payment/cancel",                 // 支付取消返回URL
	}

	got, err := paymentSvc.CreatePaymentOrder(context.Background(), req)
	if err != nil {
		logger.LogErrorf("Failed to create alipay order: %v", err)
		return
	}

	logger.LogInfof("Alipay order created: %s, payment URL: %s", got.Order.OrderId, got.PaymentUrl)
	assert.NilError(t, err)
}

func TestPaymentSvc_GetPaymentMethods(t *testing.T) {
	paymentSvc := doInit()
	req := &pb.GetPaymentMethodsReq{
		Platform: "android",
		Region:   "CN",
	}
	got, err := paymentSvc.GetPaymentMethods(context.Background(), req)
	if err != nil {
		logger.LogErrorf("Failed to create alipay order: %v", err)
		return
	}

	for _, method := range got.Methods {
		logger.LogInfof("Payment method:%v", method)
	}

	assert.NilError(t, err)
}

func TestGetProducts(t *testing.T) {
	// 初始化服务
	svc := doInit()

	ctx := context.Background()

	// 测试用例1: 获取所有产品
	t.Run("GetAllProducts", func(t *testing.T) {
		req := &pb.GetProductsReq{}

		resp, err := svc.GetProducts(ctx, req)
		if err != nil {
			t.Fatalf("GetProducts failed: %v", err)
		}

		if resp.Code != 200 {
			t.Errorf("Expected code 200, got %d", resp.Code)
		}

		if len(resp.Products) == 0 {
			t.Log("No products found")
		} else {
			t.Logf("Found %d products", len(resp.Products))
			for _, product := range resp.Products {
				t.Logf("Product: %s - %s, Price: %d %s",
					product.ProductId, product.ProductName, product.Price, product.Currency)
			}
		}
	})

	// 测试用例2: 获取VIP产品
	t.Run("GetVipProducts", func(t *testing.T) {
		req := &pb.GetProductsReq{
			PaymentType: pb.PaymentType_VIP_PURCHASE,
			Enabled:     true,
		}

		resp, err := svc.GetProducts(ctx, req)
		if err != nil {
			t.Fatalf("GetProducts failed: %v", err)
		}

		if resp.Code != 200 {
			t.Errorf("Expected code 200, got %d", resp.Code)
		}

		for _, product := range resp.Products {
			if product.ProductType != pb.PaymentType_VIP_PURCHASE {
				t.Errorf("Expected VIP product, got type %v", product.ProductType)
			}
			if product.VipDays <= 0 {
				t.Errorf("VIP product should have vipDays > 0, got %d", product.VipDays)
			}
			t.Logf("VIP Product: %s - %d days, Price: %d %s",
				product.ProductName, product.VipDays, product.Price, product.Currency)
		}
	})

	// 测试用例3: 获取书币充值产品
	t.Run("GetCoinRechargeProducts", func(t *testing.T) {
		req := &pb.GetProductsReq{
			PaymentType: pb.PaymentType_COIN_RECHARGE,
			Enabled:     true,
		}

		resp, err := svc.GetProducts(ctx, req)
		if err != nil {
			t.Fatalf("GetProducts failed: %v", err)
		}

		if resp.Code != 200 {
			t.Errorf("Expected code 200, got %d", resp.Code)
		}

		for _, product := range resp.Products {
			if product.ProductType != pb.PaymentType_COIN_RECHARGE {
				t.Errorf("Expected coin recharge product, got type %v", product.ProductType)
			}
			if product.CoinAmount <= 0 {
				t.Errorf("Coin product should have coinAmount > 0, got %f", product.CoinAmount)
			}
			t.Logf("Coin Product: %s - %.0f coins, Price: %d %s",
				product.ProductName, product.CoinAmount, product.Price, product.Currency)
		}
	})

	// 测试用例4: 平台过滤测试
	t.Run("GetProductsByPlatform", func(t *testing.T) {
		platforms := []string{"ios", "android", "web"}

		for _, platform := range platforms {
			req := &pb.GetProductsReq{
				Platform: platform,
				Enabled:  true,
			}

			resp, err := svc.GetProducts(ctx, req)
			if err != nil {
				t.Fatalf("GetProducts failed for platform %s: %v", platform, err)
			}

			if resp.Code != 200 {
				t.Errorf("Expected code 200 for platform %s, got %d", platform, resp.Code)
			}

			t.Logf("Platform %s has %d products", platform, len(resp.Products))
			for _, product := range resp.Products {
				t.Logf("  %s - %s", product.ProductId, product.ProductName)
			}
		}
	})

	// 测试用例5: 货币过滤测试
	t.Run("GetProductsByCurrency", func(t *testing.T) {
		currencies := []string{"USD", "CNY"}

		for _, currency := range currencies {
			req := &pb.GetProductsReq{
				Currency: currency,
				Enabled:  true,
			}

			resp, err := svc.GetProducts(ctx, req)
			if err != nil {
				t.Fatalf("GetProducts failed for currency %s: %v", currency, err)
			}

			if resp.Code != 200 {
				t.Errorf("Expected code 200 for currency %s, got %d", currency, resp.Code)
			}

			t.Logf("Currency %s has %d products", currency, len(resp.Products))
			for _, product := range resp.Products {
				if product.Currency != currency {
					t.Errorf("Expected currency %s, got %s for product %s",
						currency, product.Currency, product.ProductId)
				}
			}
		}
	})
}
