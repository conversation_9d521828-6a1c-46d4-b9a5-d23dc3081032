package svc

import (
	"context"
	"creativematrix.com/beyondreading/app/api/payment/model"
	"creativematrix.com/beyondreading/app/api/payment/model/po"
	"creativematrix.com/beyondreading/app/base/payment/service"
	"fmt"
	"time"

	"creativematrix.com/beyondreading/pkg/logger"
	pb "creativematrix.com/beyondreading/proto/payment"
)

// CreatePaymentOrder 创建支付订单
func (s *PaymentSvc) CreatePaymentOrder(ctx context.Context, req *pb.CreatePaymentOrderReq) (*pb.CreatePaymentOrderResp, error) {
	// 生成订单ID
	orderId := fmt.Sprintf("PAY%d%s%d", req.UserId, req.ProductId, time.Now().UnixMilli())

	// 获取产品信息
	product, err := s.dao.GetPaymentProductById(ctx, req.ProductId)
	if err != nil {
		logger.LogErrorf("Failed to get payment product: %v", err)
		return nil, fmt.Errorf("failed to get payment product: %w", err)
	}
	if product == nil {
		return nil, fmt.Errorf("payment product not found: %s", req.ProductId)
	}

	// 检查产品是否启用
	if !product.Enabled {
		return nil, fmt.Errorf("payment product is disabled: %s", req.ProductId)
	}

	payment := service.GetPaymentService(service.PaymentType(req.PaymentMethod))
	if payment == nil {
		return nil, model.PaymentNotFound
	}

	// 创建支付订单
	order := &po.PaymentOrder{
		OrderId:       orderId,
		UserId:        req.UserId,
		PaymentMethod: int32(req.PaymentMethod),
		PaymentType:   int32(req.PaymentType),
		Amount:        req.Amount,
		Currency:      req.Currency,
		ProductId:     req.ProductId,
		ProductName:   req.ProductName,
		Description:   req.Description,
		Status:        po.OrderStatusPending,
		Metadata:      req.Metadata,
		ClientIp:      req.ClientIp,
		UserAgent:     req.UserAgent,
		ReturnUrl:     req.ReturnUrl,
		CancelUrl:     req.CancelUrl,
	}

	orderParam := &service.OrderParam{}

	// 创建支付
	paymentResult, err := payment.CreatePayment(context.Background(),
		order,
		product,
		orderParam)

	if err != nil {
		logger.LogErrorf("Failed to create payment: %v", err)
		return &pb.CreatePaymentOrderResp{
			Code:    500,
			Message: "Failed to create payment",
		}, nil
	}

	// 保存到数据库
	err = s.dao.CreatePaymentOrder(ctx, order)
	if err != nil {
		logger.LogErrorf("Failed to create payment order: %v", err)
		return &pb.CreatePaymentOrderResp{
			Code:    500,
			Message: "Failed to create payment order",
		}, nil
	}

	// 更新订单支付信息
	if err == nil {
		err = s.dao.UpdatePaymentOrder(ctx, orderId, map[string]interface{}{
			"paymentUrl":  paymentResult.PaymentUrl,
			"paymentData": paymentResult.PaymentData,
		})
		if err != nil {
			logger.LogErrorf("Failed to update payment order: %v", err)
		}
	}

	return &pb.CreatePaymentOrderResp{
		Code:        200,
		Message:     "Payment order created successfully",
		Order:       s.convertOrderToPB(order),
		PaymentUrl:  paymentResult.PaymentUrl,
		PaymentData: paymentResult.PaymentData,
	}, nil
}

// ProcessPaymentCallback 处理支付回调
func (s *PaymentSvc) ProcessPaymentCallback(ctx context.Context, req *pb.ProcessPaymentCallbackReq) (*pb.ProcessPaymentCallbackResp, error) {
	// 创建回调记录
	callback := &po.PaymentCallback{
		OrderId:       req.OrderId,
		PaymentMethod: int32(req.PaymentMethod),
		TransactionId: req.TransactionId,
		Status:        int32(req.Status),
		Amount:        req.Amount,
		Currency:      req.Currency,
		CallbackData:  req.CallbackData,
		Signature:     req.Signature,
	}

	// 保存回调记录
	err := s.dao.CreatePaymentCallback(ctx, callback)
	if err != nil {
		logger.LogErrorf("Failed to create payment callback: %v", err)
		return &pb.ProcessPaymentCallbackResp{
			Code:    500,
			Message: "Failed to process callback",
			Success: false,
		}, nil
	}

	// 验证回调
	callbackResult, err := s.paymentService.VerifyCallback(ctx, callback)
	if err != nil {
		logger.LogErrorf("Failed to verify callback: %v", err)
		return &pb.ProcessPaymentCallbackResp{
			Code:    500,
			Message: "Failed to verify callback",
			Success: false,
		}, nil
	}

	if !callbackResult.Valid {
		logger.LogWarnf("Invalid payment callback: %s", callbackResult.Message)
		return &pb.ProcessPaymentCallbackResp{
			Code:    400,
			Message: callbackResult.Message,
			Success: false,
		}, nil
	}

	// 更新订单状态
	err = s.dao.UpdatePaymentOrderStatus(ctx, req.OrderId, callbackResult.Status, callbackResult.TransactionId, "")
	if err != nil {
		logger.LogErrorf("Failed to update payment order status: %v", err)
		return &pb.ProcessPaymentCallbackResp{
			Code:    500,
			Message: "Failed to update order status",
			Success: false,
		}, nil
	}

	// 如果支付成功，处理业务逻辑
	if callbackResult.Status == po.OrderStatusPaid {
		err = s.processSuccessfulPayment(ctx, req.OrderId)
		if err != nil {
			logger.LogErrorf("Failed to process successful payment: %v", err)
		}
	}

	return &pb.ProcessPaymentCallbackResp{
		Code:    200,
		Message: "Callback processed successfully",
		Success: true,
	}, nil
}

// GetPaymentOrder 获取支付订单
func (s *PaymentSvc) GetPaymentOrder(ctx context.Context, req *pb.GetPaymentOrderReq) (*pb.GetPaymentOrderResp, error) {
	order, err := s.dao.GetPaymentOrderById(ctx, req.OrderId)
	if err != nil {
		logger.LogErrorf("Failed to get payment order: %v", err)
		return &pb.GetPaymentOrderResp{
			Code:    500,
			Message: "Failed to get payment order",
		}, nil
	}

	if order == nil {
		return &pb.GetPaymentOrderResp{
			Code:    404,
			Message: "Payment order not found",
		}, nil
	}

	// 检查用户权限
	if req.UserId > 0 && order.UserId != req.UserId {
		return &pb.GetPaymentOrderResp{
			Code:    403,
			Message: "Access denied",
		}, nil
	}

	return &pb.GetPaymentOrderResp{
		Code:    200,
		Message: "Success",
		Order:   s.convertOrderToPB(order),
	}, nil
}

// GetPaymentOrders 获取支付订单列表
func (s *PaymentSvc) GetPaymentOrders(ctx context.Context, req *pb.GetPaymentOrdersReq) (*pb.GetPaymentOrdersResp, error) {
	// 设置默认分页参数
	page := req.Page
	pageSize := req.PageSize
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	// 构建查询条件
	filters := make(map[string]interface{})
	if req.PaymentMethod != pb.PaymentMethod_UNKNOWN_PAYMENT {
		filters["paymentMethod"] = int32(req.PaymentMethod)
	}
	if req.PaymentType != pb.PaymentType_UNKNOWN_TYPE {
		filters["paymentType"] = int32(req.PaymentType)
	}
	if req.Status != pb.OrderStatus_UNKNOWN_STATUS {
		filters["status"] = int32(req.Status)
	}
	if req.StartTime > 0 && req.EndTime > 0 {
		filters["createdAt"] = map[string]interface{}{
			"$gte": time.Unix(req.StartTime, 0),
			"$lte": time.Unix(req.EndTime, 0),
		}
	}

	orders, total, err := s.dao.GetPaymentOrdersByUserId(ctx, req.UserId, page, pageSize, filters)
	if err != nil {
		logger.LogErrorf("Failed to get payment orders: %v", err)
		return &pb.GetPaymentOrdersResp{
			Code:    500,
			Message: "Failed to get payment orders",
		}, nil
	}

	var pbOrders []*pb.PaymentOrderInfo
	for _, order := range orders {
		pbOrders = append(pbOrders, s.convertOrderToPB(order))
	}

	return &pb.GetPaymentOrdersResp{
		Code:    200,
		Message: "Success",
		Orders:  pbOrders,
		Total:   total,
	}, nil
}

// CancelPaymentOrder 取消支付订单
func (s *PaymentSvc) CancelPaymentOrder(ctx context.Context, req *pb.CancelPaymentOrderReq) (*pb.CancelPaymentOrderResp, error) {
	// 获取订单
	order, err := s.dao.GetPaymentOrderById(ctx, req.OrderId)
	if err != nil {
		logger.LogErrorf("Failed to get payment order: %v", err)
		return &pb.CancelPaymentOrderResp{
			Code:    500,
			Message: "Failed to get payment order",
		}, nil
	}

	if order == nil {
		return &pb.CancelPaymentOrderResp{
			Code:    404,
			Message: "Payment order not found",
		}, nil
	}

	// 检查用户权限
	if order.UserId != req.UserId {
		return &pb.CancelPaymentOrderResp{
			Code:    403,
			Message: "Access denied",
		}, nil
	}

	// 检查订单状态
	if order.Status != po.OrderStatusPending {
		return &pb.CancelPaymentOrderResp{
			Code:    400,
			Message: "Order cannot be cancelled",
		}, nil
	}

	// 更新订单状态
	err = s.dao.UpdatePaymentOrderStatus(ctx, req.OrderId, po.OrderStatusCancelled, "", req.Reason)
	if err != nil {
		logger.LogErrorf("Failed to cancel payment order: %v", err)
		return &pb.CancelPaymentOrderResp{
			Code:    500,
			Message: "Failed to cancel payment order",
		}, nil
	}

	return &pb.CancelPaymentOrderResp{
		Code:    200,
		Message: "Payment order cancelled successfully",
	}, nil
}

// RefundPayment 退款
func (s *PaymentSvc) RefundPayment(ctx context.Context, req *pb.RefundPaymentReq) (*pb.RefundPaymentResp, error) {
	// 获取订单
	order, err := s.dao.GetPaymentOrderById(ctx, req.OrderId)
	if err != nil {
		logger.LogErrorf("Failed to get payment order: %v", err)
		return &pb.RefundPaymentResp{
			Code:    500,
			Message: "Failed to get payment order",
		}, nil
	}

	if order == nil {
		return &pb.RefundPaymentResp{
			Code:    404,
			Message: "Payment order not found",
		}, nil
	}

	// 检查用户权限
	if order.UserId != req.UserId {
		return &pb.RefundPaymentResp{
			Code:    403,
			Message: "Access denied",
		}, nil
	}

	// 检查订单状态
	if order.Status != po.OrderStatusPaid {
		return &pb.RefundPaymentResp{
			Code:    400,
			Message: "Order cannot be refunded",
		}, nil
	}

	// 创建退款记录
	refund := &po.PaymentRefund{
		RefundId:      req.RefundId,
		OrderId:       req.OrderId,
		UserId:        req.UserId,
		PaymentMethod: order.PaymentMethod,
		RefundAmount:  req.RefundAmount,
		Currency:      order.Currency,
		Reason:        req.Reason,
		Status:        po.RefundStatusPending,
	}

	// 处理退款
	refundResult, err := s.paymentService.ProcessRefund(ctx, refund)
	if err != nil {
		logger.LogErrorf("Failed to process refund: %v", err)
		return &pb.RefundPaymentResp{
			Code:    500,
			Message: "Failed to process refund",
		}, nil
	}

	return &pb.RefundPaymentResp{
		Code:     200,
		Message:  refundResult.Message,
		RefundId: refundResult.RefundId,
		Success:  refundResult.Success,
	}, nil
}

// GetPaymentMethods 获取支付方式列表
func (s *PaymentSvc) GetPaymentMethods(ctx context.Context, req *pb.GetPaymentMethodsReq) (*pb.GetPaymentMethodsResp, error) {
	methods := s.paymentService.GetSupportedMethods(req.Platform, req.Region)

	var pbMethods []*pb.PaymentMethodInfo
	for _, method := range methods {
		pbMethods = append(pbMethods, &pb.PaymentMethodInfo{
			Method:              pb.PaymentMethod(method.Method),
			Name:                method.Name,
			DisplayName:         method.DisplayName,
			Icon:                method.Icon,
			Enabled:             method.Enabled,
			SupportedCurrencies: method.SupportedCurrencies,
			Config:              method.Config,
		})
	}

	return &pb.GetPaymentMethodsResp{
		Code:    200,
		Message: "Success",
		Methods: pbMethods,
	}, nil
}

// GetProducts 获取支付产品列表
func (s *PaymentSvc) GetProducts(ctx context.Context, req *pb.GetProductsReq) (*pb.GetProductsResp, error) {
	// 构建查询条件
	var productType *int32
	var enabled *bool

	// 产品类型过滤
	if req.PaymentType != pb.PaymentType_UNKNOWN_TYPE {
		pt := int32(req.PaymentType)
		productType = &pt
	}

	// 启用状态过滤
	if req.Enabled {
		e := true
		enabled = &e
	}

	// 从DAO层获取产品列表
	products, err := s.dao.GetPaymentProducts(ctx, productType, enabled)
	if err != nil {
		logger.LogErrorf("Failed to get payment products: %v", err)
		return &pb.GetProductsResp{
			Code:    500,
			Message: "Failed to get payment products",
		}, nil
	}

	// 平台过滤（在应用层进行，因为数据库表中没有platform字段）
	var filteredProducts []*po.PaymentProduct
	if req.Platform != "" {
		for _, product := range products {
			// 根据产品ID判断平台
			if s.isProductSupportedOnPlatform(product.ProductId, req.Platform) {
				filteredProducts = append(filteredProducts, product)
			}
		}
	} else {
		filteredProducts = products
	}

	// 货币过滤
	if req.Currency != "" {
		var currencyFilteredProducts []*po.PaymentProduct
		for _, product := range filteredProducts {
			if product.Currency == req.Currency {
				currencyFilteredProducts = append(currencyFilteredProducts, product)
			}
		}
		filteredProducts = currencyFilteredProducts
	}

	// 转换为protobuf格式
	var pbProducts []*pb.ProductInfo
	for _, product := range filteredProducts {
		pbProducts = append(pbProducts, s.convertProductToPB(product))
	}

	return &pb.GetProductsResp{
		Code:     200,
		Message:  "Success",
		Products: pbProducts,
	}, nil
}

// processSuccessfulPayment 处理成功支付的业务逻辑
func (s *PaymentSvc) processSuccessfulPayment(ctx context.Context, orderId string) error {
	order, err := s.dao.GetPaymentOrderById(ctx, orderId)
	if err != nil {
		return fmt.Errorf("failed to get payment order: %w", err)
	}

	switch order.PaymentType {
	case po.PaymentTypeVipPurchase:
		return s.processVipPurchase(ctx, order)
	case po.PaymentTypeMonthlySubscription:
		return s.processMonthlySubscription(ctx, order)
	case po.PaymentTypeCoinRecharge:
		return s.processCoinRecharge(ctx, order)
	default:
		return fmt.Errorf("unknown payment type: %d", order.PaymentType)
	}
}

// processVipPurchase 处理VIP购买
func (s *PaymentSvc) processVipPurchase(ctx context.Context, order *po.PaymentOrder) error {
	// 这里应该调用account服务来更新用户VIP状态
	logger.LogInfof("Processing VIP purchase for user %d, order %s", order.UserId, order.OrderId)

	// 发送消息到RabbitMQ进行异步处理
	message := map[string]interface{}{
		"type":      "vip_purchase",
		"userId":    order.UserId,
		"orderId":   order.OrderId,
		"amount":    order.Amount,
		"productId": order.ProductId,
	}

	return s.dao.PublishMessage(ctx, "payment.vip.purchased", message)
}

// processMonthlySubscription 处理包月订阅
func (s *PaymentSvc) processMonthlySubscription(ctx context.Context, order *po.PaymentOrder) error {
	// 这里应该调用account服务来更新用户订阅状态
	logger.LogInfof("Processing monthly subscription for user %d, order %s", order.UserId, order.OrderId)

	// 发送消息到RabbitMQ进行异步处理
	message := map[string]interface{}{
		"type":      "monthly_subscription",
		"userId":    order.UserId,
		"orderId":   order.OrderId,
		"amount":    order.Amount,
		"productId": order.ProductId,
	}

	return s.dao.PublishMessage(ctx, "payment.subscription.activated", message)
}

// processCoinRecharge 处理书币充值
func (s *PaymentSvc) processCoinRecharge(ctx context.Context, order *po.PaymentOrder) error {
	// 这里应该调用account服务来增加用户书币
	logger.LogInfof("Processing coin recharge for user %d, order %s", order.UserId, order.OrderId)

	// 发送消息到RabbitMQ进行异步处理
	message := map[string]interface{}{
		"type":      "coin_recharge",
		"userId":    order.UserId,
		"orderId":   order.OrderId,
		"amount":    order.Amount,
		"productId": order.ProductId,
	}

	return s.dao.PublishMessage(ctx, "payment.coin.recharged", message)
}
