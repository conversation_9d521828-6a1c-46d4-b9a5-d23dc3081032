package grpc

import (
	"net"
	"time"

	"creativematrix.com/beyondreading/app/base/payment/conf"
	"creativematrix.com/beyondreading/app/base/payment/svc"
	"creativematrix.com/beyondreading/pkg/gm"
	pb "creativematrix.com/beyondreading/proto/payment"
	"google.golang.org/grpc"
	"google.golang.org/grpc/keepalive"
)

const App = "base-payment"

func Start(c *conf.Config, s *svc.PaymentSvc) (*grpc.Server, error) {
	server := grpc.NewServer(
		grpc.KeepaliveParams(keepalive.ServerParameters{
			MaxConnectionIdle:     time.Minute * 5,
			MaxConnectionAge:      time.Hour,
			MaxConnectionAgeGrace: time.Minute * 5,
			Time:                  time.Minute * 10,
			Timeout:               time.Second * 3,
		}),
		grpc.UnaryInterceptor(gm.UnaryServerInterceptor()),
	)

	pb.RegisterPaymentServer(server, s)

	lis, err := net.Listen("tcp", c.Port.GRPC)
	if err != nil {
		return nil, err
	}

	go func() {
		if err := server.Serve(lis); err != nil {
			panic(err)
		}
	}()

	return server, nil
}
