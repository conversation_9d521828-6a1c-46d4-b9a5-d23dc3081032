# User Base服务配置文件

[log]
level = "info"

[port]
http = ":9571"
debug = ":9572"
grpc = ":9573"

[redisUser]
address = "127.0.0.1:6379"
password = "123456"
maxIdle = 3
maxActive = 1000


[sms]
provider = "aliyun"
access_key = "your_aliyun_access_key"
secret_key = "your_aliyun_secret_key"
sign_name = "BeyondReading"

[sms.template]
register = "SMS_123456789"
login = "SMS_987654321"

[jwt]
secret = "your_jwt_secret_key_here"
expire_time = 604800  # 7天
issuer = "beyondreading"

[google]
client_id = "your_google_client_id"
client_secret = "your_google_client_secret"

[apple]
team_id = "your_apple_team_id"
client_id = "your_apple_client_id"
key_id = "your_apple_key_id"
key_file = "path/to/apple/private/key.p8"


