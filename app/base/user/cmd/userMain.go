package main

import (
	"creativematrix.com/beyondreading/app/base/user/api"
	"creativematrix.com/beyondreading/pkg/discovery"
	"creativematrix.com/beyondreading/pkg/utils"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"creativematrix.com/beyondreading/app/base/user/conf"
	"creativematrix.com/beyondreading/app/base/user/grpc"
	"creativematrix.com/beyondreading/app/base/user/svc"
	"creativematrix.com/beyondreading/pkg/debug"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/tracer"
)

func main() {
	config := conf.Load(api.App)
	logger.InitLog(api.App, config.Log.Level)
	tracer.InitTracing(config.Base, api.App)

	service := svc.Load(config)
	server, err := grpc.Start(config, service)
	if err != nil {
		panic(fmt.Sprintf("start user server failed: %v", err))
	}

	etcdRegister := discovery.NewRegister(config.Etcd.Addrs, logger.Log)
	var discoveryServerAddr string
	if config.Base.IsWinDebug {
		discoveryServerAddr = utils.InternalWindowsIP() + config.Port.GRPC
	} else {
		discoveryServerAddr = utils.InternalIP() + config.Port.GRPC
	}

	node := discovery.Server{
		Name: api.App,
		//Addr: utils.InternalIP() + config.Port.GRPC,
		Addr: discoveryServerAddr,
	}

	if _, err := etcdRegister.Register(node, 10); err != nil {
		panic(fmt.Sprintf("server register failed: %v", err))
	}

	debug.Start(config.Base, service)

	logger.LogInfo(api.App, " started listen on ", config.Port.GRPC)

	c := make(chan os.Signal, 1)
	signal.Notify(c, syscall.SIGHUP, syscall.SIGQUIT, syscall.SIGTERM, syscall.SIGINT)
	for {
		s := <-c
		switch s {
		case syscall.SIGQUIT, syscall.SIGTERM, syscall.SIGINT:
			logger.LogWarnw("user base service exit")
			etcdRegister.Stop()
			server.GracefulStop()
			service.Close()
			time.Sleep(time.Second * 2)
			return
		case syscall.SIGHUP:
		default:
			return
		}
	}
}
