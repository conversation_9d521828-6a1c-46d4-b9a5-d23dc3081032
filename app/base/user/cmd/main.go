package main

import (
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"creativematrix.com/beyondreading/app/base/user/api"
	"creativematrix.com/beyondreading/app/base/user/conf"
	"creativematrix.com/beyondreading/app/base/user/grpc"
	"creativematrix.com/beyondreading/app/base/user/svc"
	"creativematrix.com/beyondreading/pkg/debug"
	"creativematrix.com/beyondreading/pkg/discovery"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/tracer"
	"creativematrix.com/beyondreading/pkg/utils"
)

func main() {
	config := conf.Load(api.App)
	logger.InitLog(api.App, config.Log.Level)
	tracer.InitTracing(config.Base, api.App)

	svc := svc.Load(config)
	server, err := grpc.Start(config, svc)
	if err != nil {
		panic(fmt.Sprintf("start user server failed: %v", err))
	}

	etcdRegister := discovery.NewRegister(config.Etcd.Addrs, logger.Log)
	node := discovery.Server{
		Name: api.App,
		Addr: utils.InternalIP() + config.Port.GRPC,
	}

	if _, err := etcdRegister.Register(node, 10); err != nil {
		panic(fmt.Sprintf("server register failed: %v", err))
	}

	debug.Start(config.Base, svc)
	logger.LogInfo("base user service started listen on ", config.Port.GRPC)

	c := make(chan os.Signal, 1)
	signal.Notify(c, syscall.SIGHUP, syscall.SIGQUIT, syscall.SIGTERM, syscall.SIGINT)
	for {
		s := <-c
		switch s {
		case syscall.SIGQUIT, syscall.SIGTERM, syscall.SIGINT:
			logger.LogWarnw("base user service exit")

			etcdRegister.Stop()
			server.Stop()
			svc.Close()
			time.Sleep(time.Second)
			return
		case syscall.SIGHUP:
		default:
			return
		}
	}
}
