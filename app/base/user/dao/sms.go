package dao

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"creativematrix.com/beyondreading/app/common/po"
)

// CreateSmsCode 创建短信验证码
func (d *Dao) CreateSmsCode(ctx context.Context, smsCode *po.SmsCode) error {
	query := `INSERT INTO smsCode (phone, code, codeType, isUsed, expireAt, createdAt)
			  VALUES (?, ?, ?, ?, ?, ?)`

	smsCode.CreatedAt = time.Now()
	smsCode.ExpireAt = time.Now().Add(5 * time.Minute) // 5分钟过期
	smsCode.IsUsed = false

	// 获取数据库连接（smsCode表不分表，使用默认连接）
	db, err := d.msshard.DB("0")
	if err != nil {
		return fmt.Errorf("failed to get database connection: %w", err)
	}

	result, err := db.ExecContext(ctx, query,
		smsCode.Phone, smsCode.Code, smsCode.CodeType,
		smsCode.IsUsed, smsCode.ExpireAt, smsCode.CreatedAt)

	if err != nil {
		return fmt.Errorf("failed to create sms code: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("failed to get sms code id: %w", err)
	}

	smsCode.Id = uint64(id)

	return nil
}

// GetValidSmsCode 获取有效的短信验证码
func (d *Dao) GetValidSmsCode(ctx context.Context, phone string, codeType int32) (*po.SmsCode, error) {
	query := `SELECT id, phone, code, codeType, isUsed, expireAt, createdAt
			  FROM smsCode 
			  WHERE phone = ? AND codeType = ? AND isUsed = 0 AND expireAt > NOW()
			  ORDER BY createdAt DESC LIMIT 1`

	// 获取数据库连接（smsCode表不分表，使用默认连接）
	db, err := d.msshard.DB("0")
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}

	smsCode := &po.SmsCode{}
	err = db.GetContext(ctx, smsCode, query, phone, codeType)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get valid sms code: %w", err)
	}

	return smsCode, nil
}

// VerifySmsCode 验证短信验证码
func (d *Dao) VerifySmsCode(ctx context.Context, phone, code string, codeType int32) (bool, error) {
	// 获取有效的验证码
	smsCode, err := d.GetValidSmsCode(ctx, phone, codeType)
	if err != nil {
		return false, err
	}

	if smsCode == nil {
		return false, nil
	}

	// 验证码不匹配
	if smsCode.Code != code {
		return false, nil
	}

	// 标记为已使用
	err = d.MarkSmsCodeUsed(ctx, smsCode.Id)
	if err != nil {
		return false, fmt.Errorf("failed to mark sms code as used: %w", err)
	}

	return true, nil
}

// MarkSmsCodeUsed 标记短信验证码为已使用
func (d *Dao) MarkSmsCodeUsed(ctx context.Context, id uint64) error {
	query := `UPDATE smsCode SET isUsed = 1 WHERE id = ?`

	// 获取数据库连接（smsCode表不分表，使用默认连接）
	db, err := d.msshard.DB("0")
	if err != nil {
		return fmt.Errorf("failed to get database connection: %w", err)
	}

	_, err = db.ExecContext(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to mark sms code as used: %w", err)
	}

	return nil
}

// GetSmsCodeSendCount 获取短信验证码发送次数（用于限流）
func (d *Dao) GetSmsCodeSendCount(ctx context.Context, phone string, startTime time.Time) (int64, error) {
	query := `SELECT COUNT(*) FROM smsCode WHERE phone = ? AND createdAt >= ?`

	// 获取数据库连接（smsCode表不分表，使用默认连接）
	db, err := d.msshard.DB("0")
	if err != nil {
		return 0, fmt.Errorf("failed to get database connection: %w", err)
	}

	var count int64
	err = db.GetContext(ctx, &count, query, phone, startTime)
	if err != nil {
		return 0, fmt.Errorf("failed to get sms code send count: %w", err)
	}

	return count, nil
}

// CleanExpiredSmsCode 清理过期的短信验证码
func (d *Dao) CleanExpiredSmsCode(ctx context.Context) error {
	query := `DELETE FROM smsCode WHERE expireAt < NOW()`

	// 获取数据库连接（smsCode表不分表，使用默认连接）
	db, err := d.msshard.DB("0")
	if err != nil {
		return fmt.Errorf("failed to get database connection: %w", err)
	}

	_, err = db.ExecContext(ctx, query)
	if err != nil {
		return fmt.Errorf("failed to clean expired sms code: %w", err)
	}

	return nil
}
