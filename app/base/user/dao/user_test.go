package dao

import (
	"context"
	"testing"
	"time"

	"creativematrix.com/beyondreading/app/base/user/model"

	"gotest.tools/assert"
)

func TestDao_UpdatePushSwitch(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	err := d.UpsertSwitchSetting(ctx, &model.TUserSwitchSetting{
		NoId:            "1",
		Position:        "on",
		PrivateChatPush: "off",
		OpenLivePush:    "on",
		CreateTime:      time.Now(),
	})
	assert.NilError(t, err)
}

func TestDao_GetSwitchSetting(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	data, err := d.GetSwitchSetting(ctx, []string{"1"})
	assert.NilError(t, err)
	t.Log(data)
}

func TestDao_GetUserFansList(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	list, err := d.GetUserFansList(ctx, "1", 0, 0)
	assert.NilError(t, err)

	for _, l := range list {
		t.Logf("from:%s, to:%s, relation:%s", l.From, l.To, l.Relation)
	}
}
