package dao

import (
	"context"
	"creativematrix.com/beyondreading/pkg/check/shumei"
	"creativematrix.com/beyondreading/pkg/logger"
	"fmt"
	"strconv"

	pb "creativematrix.com/beyondreading/app/base/user/api"
	"creativematrix.com/beyondreading/app/base/user/model"
	"creativematrix.com/beyondreading/pkg/ecode"
	"creativematrix.com/beyondreading/pkg/mongo"
	"creativematrix.com/beyondreading/pkg/utils"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (d *Dao) GetUsersByIds(ctx context.Context, req *pb.UserIdsReq) ([]*model.TUserInfo, error) {
	var rsps []*model.TUserInfo
	var err error

	if len(req.Cols) == 0 {
		err = d.schema[model.TableUser].Find(&rsps, bson.M{"noId": bson.M{"$in": req.Ids}, "status": model.UserValid})
	} else {
		err = d.schema[model.TableUser].Find(&rsps, bson.M{"noId": bson.M{"$in": req.Ids}, "status": model.UserValid},
			mongo.Select(append(req.Cols, "gender", "registerStatus")))
	}
	return rsps, err
}

func (d *Dao) GetUsersByMongoIds(ctx context.Context, req *pb.MongoIdsReq) ([]*model.TUserInfo, error) {
	var rsps []*model.TUserInfo
	var err error
	args := bson.A{}
	for _, v := range req.Ids {
		id, _ := primitive.ObjectIDFromHex(v)
		args = append(args, id)
	}
	if len(req.Cols) == 0 {
		err = d.schema[model.TableUser].Find(&rsps, bson.M{"_id": bson.M{"$in": args}, "status": model.UserValid})
	} else {
		err = d.schema[model.TableUser].Find(&rsps, bson.M{"_id": bson.M{"$in": args}, "status": model.UserValid}, mongo.Select(req.Cols))
	}
	return rsps, err
}

func (d *Dao) GetUsersByMsgIds(ctx context.Context, req *pb.MsgIdsReq) ([]*model.TUserInfo, error) {
	var rsps []*model.TUserInfo
	var err error
	if len(req.Cols) == 0 {
		err = d.schema[model.TableUser].Find(&rsps, bson.M{"msgId": bson.M{"$in": req.Ids}, "status": model.UserValid})
	} else {
		err = d.schema[model.TableUser].Find(&rsps, bson.M{"msgId": bson.M{"$in": req.Ids}, "status": model.UserValid}, mongo.Select(req.Cols))
	}
	return rsps, err
}

func (d *Dao) UpdateMobile(ctx context.Context, noId, mobile string) error {
	_, err := d.schema[model.TableUser].Update(
		bson.M{"noId": noId},
		bson.M{"$set": bson.M{"mobile": mobile}},
	)
	return err
}

func (d *Dao) IsUserExistByNoId(ctx context.Context, noId string) (int32, error) {
	u := new(model.TUserInfo)
	err := d.schema[model.TableUser].FindOne(u, bson.M{"noId": noId, "status": model.UserValid}, mongo.Projection(bson.M{"noId": 1}))
	if err != nil {
		return model.UserNotExist, err
	}

	if u.NoId == noId {
		return model.UserExist, nil
	} else {
		return model.UserNotExist, model.NotExist
	}
}

func (d *Dao) IsUserUsingMobile(ctx context.Context, mobile string) (string, error) {
	u := new(model.TUserInfo)
	err := d.schema[model.TableUser].FindOne(u, bson.M{"mobile": mobile, "registerStatus": bson.M{"$ne": model.UserLogoff}})
	if err != nil && err != model.NothingFound {
		return "", err
	}

	return u.NoId, nil
}

func (d *Dao) UpdateUserInfo(ctx context.Context, userId string, data bson.M) (*model.TUserInfo, error) {
	id, err := primitive.ObjectIDFromHex(userId)
	if err != nil {
		return nil, err
	}

	if len(data) > 0 {
		_, err = d.schema[model.TableUser].Update(bson.M{"_id": id}, bson.M{"$set": data})
		if err != nil {
			return nil, err
		}
	}

	u := new(model.TUserInfo)
	err = d.schema[model.TableUser].FindOne(u, bson.M{"_id": id})
	if err != nil {
		return nil, err
	}
	return u, nil
}

func (d *Dao) PullUser(ctx context.Context, query interface{}, limit int, isRand int, cols ...string) ([]*model.TUserInfo, error) {
	var (
		count int64
		users []*model.TUserInfo
	)
	//随机 查询总数
	if isRand == 1 {
		count = d.schema[model.TableUser].Count(query)
	}
	indexs := utils.RandRangeNum(int(count), limit)

	for _, v := range indexs {
		cursor, err := d.summerConn.Collection(
			model.TableUser).Cursor(
			query, mongo.Skip(int64(v)), mongo.Limit(1), mongo.Select(cols))
		if err != nil {
			return nil, err
		}

		var row = new(model.TUserInfo)
		for cursor.Next(ctx) {
			err = cursor.Decode(&row)
			if err != nil {
				return nil, err
			}

			users = append(users, row)

			_ = cursor.Close(ctx)
		}
	}
	return users, nil
}

// GetUserRemark 获取用户对其他人备注
func (d *Dao) GetUserRemark(ctx context.Context, noId string, ids []string) (map[string]string, error) {
	data := make([]*model.TUserAction, 0)
	err := d.schema[model.TableUserAction].Find(&data, bson.M{"userId": noId, "dstUser": bson.M{"$in": ids}})

	if err != nil {
		return nil, err
	}

	m := make(map[string]string)
	for _, d := range data {
		if d.Remark != "" {
			m[d.DstUser] = d.Remark
		}
	}

	return m, nil
}

func (d *Dao) GetOnlineFemale(ctx context.Context) ([]string, error) {
	return d.cache.HGetAllStrings(ctx, model.Redis_UserFemaleActive)
}

func (d *Dao) GetOnlineMale(ctx context.Context) ([]string, error) {
	return d.cache.HGetAllStrings(ctx, model.Redis_UserMaleActive)
}

func (d *Dao) GetOnlineFemaleSet(ctx context.Context, noId, user string) error {
	return d.cache.HSet(ctx, model.Redis_UserFemaleActive, noId, user)
}

func (d *Dao) GetOnlineMaleSet(ctx context.Context, noId, user string) error {
	return d.cache.HSet(ctx, model.Redis_UserMaleActive, noId, user)
}

func (d *Dao) UpdateAlbumStatus(ctx context.Context, id primitive.ObjectID, album map[string]string) error {
	result := new(model.RspAlbum)
	err := d.schema[model.TableUser].FindOne(result, bson.M{"_id": id}, mongo.Projection(bson.M{"album": true}))
	if err != nil {
		return err
	}

	for i, img := range result.Album {
		if status, ok := album[img.ImgUrl]; ok {
			result.Album[i].Status = status
		}
	}

	_, err = d.schema[model.TableUser].Update(bson.M{"_id": id}, bson.M{"$set": bson.M{"album": result.Album}})

	return err
}

func (d *Dao) RobotAdd(ctx context.Context, users []interface{}) error {
	_, err := d.schema[model.TableUser].InsertMany(users)
	return err
}

func (d *Dao) GetNoId(ctx context.Context) (string, error) {
	var tu model.TUserInfo

	noId, err := d.cache.IncrByKey(ctx, model.Redis_UserNOIdCache, 1)
	if err != nil {
		return "", nil
	}

	if noId == 1 {
		if err := d.schema[model.TableUser].FindOne(&tu, bson.M{}, mongo.Sort(bson.M{"noId": -1})); err != nil {
			if err != ecode.NothingFound {
				return "", nil
			}
			noId = d.conf.BeginId
		}
		if tu.NoId != "" {
			noId, _ = strconv.ParseInt(tu.NoId, 10, 64)
		}
		_, err := d.cache.Set(ctx, fmt.Sprintf(model.Redis_UserNOIdCache), noId+1, -1)
		if err != nil {
			return "", nil
		}
	}

	return strconv.FormatInt(noId, 10), nil
}

func (d *Dao) RobotGet(ctx context.Context, query interface{}, selects []string) (rsps []*model.TUserInfo, err error) {
	if len(selects) != 0 {
		err = d.schema[model.TableUser].Find(&rsps, query, mongo.Select(selects))
	} else {
		err = d.schema[model.TableUser].Find(&rsps, query)
	}
	return
}

func (d *Dao) GetNickName(ctx context.Context, ltype string) ([]*model.TNickName, error) {
	var tnn []*model.TNickName

	if err := d.schema[model.TableNickName].Find(&tnn, bson.M{"type": ltype, "status": "published"}); err != nil {
		return nil, err
	}
	return tnn, nil
}

func (d *Dao) OnlineNum(ctx context.Context) int64 {
	return d.schema[model.TableUser].Count(bson.M{
		"isOnline": 1,
	})
}

func (d *Dao) UpsertSwitchSetting(ctx context.Context, data *model.TUserSwitchSetting) error {
	_, err := d.schema[model.TableUserSwitchSetting].Update(bson.M{"noId": data.NoId}, bson.M{"$set": data}, mongo.Upsert())
	return err
}

func (d *Dao) GetSwitchSetting(ctx context.Context, noIds []string) ([]*model.TUserSwitchSetting, error) {
	var (
		err  error
		list = make([]*model.TUserSwitchSetting, 0)
	)
	if len(noIds) == 0 {
		return list, nil
	}

	err = d.schema[model.TableUserSwitchSetting].Find(&list, bson.M{"noId": bson.M{"$in": noIds}})
	return list, err
}

func (d *Dao) GetUserFansList(ctx context.Context, userId string, pageNum, pageSize int64) ([]*model.TUserSessionList, error) {
	var (
		err  error
		list = make([]*model.TUserSessionList, 0)
	)

	var opts []mongo.Opts
	if pageSize > 0 && pageNum > 0 {
		opts = append(opts, mongo.Skip(pageSize*(pageNum-1)), mongo.Limit(pageSize))
	}

	err = d.summerConnChat.Model(model.TableUserSessionList).Find(
		&list,
		bson.M{
			"to":       userId,
			"relation": bson.M{"$in": bson.A{model.ANoB, model.AB}},
		},
		opts...,
	)

	return list, err
}

func (d *Dao) GetReviewRule(ctx context.Context, ruleType string) (*model.TReviewRule, error) {
	rule := new(model.TReviewRule)
	jsonStr, err := d.cache.HGet(ctx, model.RedisReviewRule, ruleType)

	if err != nil {
		logger.LogErrorf("cache rule not found:%s", err.Error())
	} else {
		err = utils.Transform([]byte(jsonStr), rule)
		if err == nil {
			return rule, nil
		}
	}

	err = d.schema[model.TableReviewRule].FindOne(rule, bson.M{"type": ruleType})

	if err != nil {
		return nil, err
	}

	return rule, nil
}

func (d *Dao) Msg(ctx context.Context, request shumei.MsgRequest, response shumei.BaseValidInterface) error {
	return d.sm.Msg(ctx, request, response)
}

func (d *Dao) CheckUserMedia(event model.Event, data []byte) error {
	user := &model.CheckUser{
		Event: event,
		Data:  data,
	}
	err := d.RabbitMQ.Publish(model.MQThird, user)
	if err != nil {
		logger.LogErrorw("send user review to queue", string(event), err.Error())
		return err
	}

	return nil
}
