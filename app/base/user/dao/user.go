package dao

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"time"

	"creativematrix.com/beyondreading/app/base/user/model"
	"creativematrix.com/beyondreading/app/common/po"
	js "creativematrix.com/beyondreading/pkg/json"
	"creativematrix.com/beyondreading/pkg/logger"
)

// CreateUser 创建用户
func (d *Dao) CreateUser(ctx context.Context, user *po.User) (*po.User, error) {
	// 先生成一个临时的userId用于分表（实际会被数据库自增ID覆盖）
	tempUserId := uint64(time.Now().UnixNano() % 7)

	query := `INSERT INTO %s (phone, email, nickname, avatar, gender, birthday, location, status, loginType, googleId, appleId, lastLoginAt, lastLoginIp, createdAt, updatedAt)
			  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

	query = d.setUserTable(query, tempUserId)

	now := time.Now()
	user.CreatedAt = now
	user.UpdatedAt = now

	// 获取数据库连接
	userIdStr := fmt.Sprintf("%d", tempUserId)
	db, err := d.msshard.DB(userIdStr)
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}

	result, err := db.ExecContext(ctx, query,
		user.Phone, user.Email, user.Nickname, user.Avatar, user.Gender,
		user.Birthday, user.Location, user.Status, user.LoginType,
		user.GoogleId, user.AppleId, user.LastLoginAt, user.LastLoginIp,
		user.CreatedAt, user.UpdatedAt)

	if err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	userId, err := result.LastInsertId()
	if err != nil {
		return nil, fmt.Errorf("failed to get user id: %w", err)
	}

	user.UserId = uint64(userId)

	// 设置用户信息缓存
	err = d.setUserToCache(ctx, user)
	if err != nil {
		logger.LogErrorf("Failed to set user to cache: %v", err)
	}

	// 设置手机号到用户ID的映射缓存
	if user.Phone != "" {
		err = d.setPhoneToUserIdCache(ctx, user.Phone, user.UserId)
		if err != nil {
			logger.LogErrorf("Failed to set phone to userId cache: %v", err)
		}
	}

	// 设置邮箱到用户ID的映射缓存
	if user.Email != "" {
		err = d.setEmailToUserIdCache(ctx, user.Email, user.UserId)
		if err != nil {
			logger.LogErrorf("Failed to set email to userId cache: %v", err)
		}
	}

	// 设置Google ID到用户ID的映射缓存
	if user.GoogleId != "" {
		err = d.setGoogleToUserIdCache(ctx, user.GoogleId, user.UserId)
		if err != nil {
			logger.LogErrorf("Failed to set google to userId cache: %v", err)
		}
	}

	// 设置Apple ID到用户ID的映射缓存
	if user.AppleId != "" {
		err = d.setAppleToUserIdCache(ctx, user.AppleId, user.UserId)
		if err != nil {
			logger.LogErrorf("Failed to set apple to userId cache: %v", err)
		}
	}

	logger.LogInfof("User %d created and cached successfully", user.UserId)
	return user, nil
}

// GetUserById 根据用户ID获取用户信息
func (d *Dao) GetUserById(ctx context.Context, userId uint64) (*po.User, error) {
	// 先从缓存获取
	user, err := d.getUserFromCache(ctx, userId)
	if err == nil && user != nil {
		logger.LogInfof("User %d found in cache", userId)
		return user, nil
	}

	// 缓存未命中，从数据库查询
	query := `SELECT userId, phone, email, nickname, avatar, gender, birthday, location, status, loginType, googleId, appleId, lastLoginAt, lastLoginIp, createdAt, updatedAt
			  FROM %s WHERE userId = ?`

	query = d.setUserTable(query, userId)

	// 获取数据库连接
	userIdStr := fmt.Sprintf("%d", userId)
	db, err := d.msshard.DB(userIdStr)
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}

	user = &po.User{}
	err = db.GetContext(ctx, user, query, userId)
	if err != nil {
		if err == sql.ErrNoRows {
			// 用户不存在，设置空缓存防止缓存穿透
			err = d.setEmptyUserCache(ctx, userId)
			if err != nil {
				logger.LogErrorf("Failed to set empty user cache: %v", err)
			}
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get user by id: %w", err)
	}

	// 设置缓存
	err = d.setUserToCache(ctx, user)
	if err != nil {
		logger.LogErrorf("Failed to set user to cache: %v", err)
	} else {
		logger.LogInfof("User %d cached successfully", userId)
	}

	return user, nil
}

// GetUserByPhone 根据手机号获取用户信息
func (d *Dao) GetUserByPhone(ctx context.Context, phone string) (*po.User, error) {
	// 先尝试从缓存通过手机号获取用户ID
	userId, err := d.getUserIdByPhoneFromCache(ctx, phone)
	if err == nil && userId > 0 {
		// 通过用户ID获取完整用户信息（会走缓存）
		return d.GetUserById(ctx, userId)
	}

	// 缓存未命中，需要查询所有分表
	for i := 0; i < 7; i++ {
		query := fmt.Sprintf(`SELECT userId, phone, email, nickname, avatar, gender, birthday, location, status, loginType, googleId, appleId, lastLoginAt, lastLoginIp, createdAt, updatedAt
							  FROM user%02d WHERE phone = ?`, i)

		// 获取数据库连接
		shardIdStr := fmt.Sprintf("%d", i)
		db, err := d.msshard.DB(shardIdStr)
		if err != nil {
			continue // 跳过这个分片，继续查询下一个
		}

		user := &po.User{}
		err = db.GetContext(ctx, user, query, phone)
		if err == nil {
			// 设置用户信息缓存
			err = d.setUserToCache(ctx, user)
			if err != nil {
				logger.LogErrorf("Failed to set user to cache: %v", err)
			}

			// 设置手机号到用户ID的映射缓存
			err = d.setPhoneToUserIdCache(ctx, phone, user.UserId)
			if err != nil {
				logger.LogErrorf("Failed to set phone to userId cache: %v", err)
			}

			logger.LogInfof("User found by phone %s and cached", phone)
			return user, nil
		}
		if err != sql.ErrNoRows {
			return nil, fmt.Errorf("failed to get user by phone: %w", err)
		}
	}

	return nil, nil
}

// GetUserByEmail 根据邮箱获取用户信息
func (d *Dao) GetUserByEmail(ctx context.Context, email string) (*po.User, error) {
	// 先尝试从缓存通过邮箱获取用户ID
	userId, err := d.getUserIdByEmailFromCache(ctx, email)
	if err == nil && userId > 0 {
		// 通过用户ID获取完整用户信息（会走缓存）
		return d.GetUserById(ctx, userId)
	}

	// 缓存未命中，需要查询所有分表
	for i := 0; i < 7; i++ {
		query := fmt.Sprintf(`SELECT userId, phone, email, nickname, avatar, gender, birthday, location, status, loginType, googleId, appleId, lastLoginAt, lastLoginIp, createdAt, updatedAt
							  FROM user%02d WHERE email = ?`, i)

		// 获取数据库连接
		shardIdStr := fmt.Sprintf("%d", i)
		db, err := d.msshard.DB(shardIdStr)
		if err != nil {
			continue // 跳过这个分片，继续查询下一个
		}

		user := &po.User{}
		err = db.GetContext(ctx, user, query, email)
		if err == nil {
			// 设置用户信息缓存
			err = d.setUserToCache(ctx, user)
			if err != nil {
				logger.LogErrorf("Failed to set user to cache: %v", err)
			}

			// 设置邮箱到用户ID的映射缓存
			err = d.setEmailToUserIdCache(ctx, email, user.UserId)
			if err != nil {
				logger.LogErrorf("Failed to set email to userId cache: %v", err)
			}

			logger.LogInfof("User found by email %s and cached", email)
			return user, nil
		}
		if err != sql.ErrNoRows {
			return nil, fmt.Errorf("failed to get user by email: %w", err)
		}
	}

	return nil, nil
}

// GetUserByGoogleId 根据Google ID获取用户信息
func (d *Dao) GetUserByGoogleId(ctx context.Context, googleId string) (*po.User, error) {
	// 先尝试从缓存通过Google ID获取用户ID
	userId, err := d.getUserIdByGoogleIdFromCache(ctx, googleId)
	if err == nil && userId > 0 {
		// 通过用户ID获取完整用户信息（会走缓存）
		return d.GetUserById(ctx, userId)
	}

	// 缓存未命中，需要查询所有分表
	for i := 0; i < 7; i++ {
		query := fmt.Sprintf(`SELECT userId, phone, email, nickname, avatar, gender, birthday, location, status, loginType, googleId, appleId, lastLoginAt, lastLoginIp, createdAt, updatedAt
							  FROM user%02d WHERE googleId = ?`, i)

		// 获取数据库连接
		shardIdStr := fmt.Sprintf("%d", i)
		db, err := d.msshard.DB(shardIdStr)
		if err != nil {
			continue // 跳过这个分片，继续查询下一个
		}

		user := &po.User{}
		err = db.GetContext(ctx, user, query, googleId)
		if err == nil {
			// 设置用户信息缓存
			err = d.setUserToCache(ctx, user)
			if err != nil {
				logger.LogErrorf("Failed to set user to cache: %v", err)
			}

			// 设置Google ID到用户ID的映射缓存
			err = d.setGoogleToUserIdCache(ctx, googleId, user.UserId)
			if err != nil {
				logger.LogErrorf("Failed to set google to userId cache: %v", err)
			}

			logger.LogInfof("User found by google ID %s and cached", googleId)
			return user, nil
		}
		if err != sql.ErrNoRows {
			return nil, fmt.Errorf("failed to get user by google id: %w", err)
		}
	}

	return nil, nil
}

// GetUserByAppleId 根据Apple ID获取用户信息
func (d *Dao) GetUserByAppleId(ctx context.Context, appleId string) (*po.User, error) {
	// 先尝试从缓存通过Apple ID获取用户ID
	userId, err := d.getUserIdByAppleIdFromCache(ctx, appleId)
	if err == nil && userId > 0 {
		// 通过用户ID获取完整用户信息（会走缓存）
		return d.GetUserById(ctx, userId)
	}

	// 缓存未命中，需要查询所有分表
	for i := 0; i < 7; i++ {
		query := fmt.Sprintf(`SELECT userId, phone, email, nickname, avatar, gender, birthday, location, status, loginType, googleId, appleId, lastLoginAt, lastLoginIp, createdAt, updatedAt
							  FROM user%02d WHERE appleId = ?`, i)

		// 获取数据库连接
		shardIdStr := fmt.Sprintf("%d", i)
		db, err := d.msshard.DB(shardIdStr)
		if err != nil {
			continue // 跳过这个分片，继续查询下一个
		}

		user := &po.User{}
		err = db.GetContext(ctx, user, query, appleId)
		if err == nil {
			// 设置用户信息缓存
			err = d.setUserToCache(ctx, user)
			if err != nil {
				logger.LogErrorf("Failed to set user to cache: %v", err)
			}

			// 设置Apple ID到用户ID的映射缓存
			err = d.setAppleToUserIdCache(ctx, appleId, user.UserId)
			if err != nil {
				logger.LogErrorf("Failed to set apple to userId cache: %v", err)
			}

			logger.LogInfof("User found by apple ID %s and cached", appleId)
			return user, nil
		}
		if err != sql.ErrNoRows {
			return nil, fmt.Errorf("failed to get user by apple id: %w", err)
		}
	}

	return nil, nil
}

// UpdateUser 更新用户信息
func (d *Dao) UpdateUser(ctx context.Context, user *po.User) (*po.User, error) {
	// 先获取原始用户信息（用于缓存清理）
	oldUser, err := d.GetUserById(ctx, user.UserId)
	if err != nil {
		return nil, fmt.Errorf("failed to get original user: %w", err)
	}

	query := `UPDATE %s SET nickname = ?, avatar = ?, gender = ?, birthday = ?, location = ?, updatedAt = ?
			  WHERE userId = ?`

	query = d.setUserTable(query, user.UserId)

	user.UpdatedAt = time.Now()

	// 获取数据库连接
	userIdStr := fmt.Sprintf("%d", user.UserId)
	db, err := d.msshard.DB(userIdStr)
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}

	_, err = db.ExecContext(ctx, query,
		user.Nickname, user.Avatar, user.Gender, user.Birthday, user.Location,
		user.UpdatedAt, user.UserId)

	if err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	// 获取更新后的完整用户信息
	updatedUser, err := d.getUserFromDB(ctx, user.UserId)
	if err != nil {
		logger.LogErrorf("Failed to get updated user from DB: %v", err)
		updatedUser = user // 使用传入的用户信息作为备选
	}

	// 更新缓存
	err = d.setUserToCache(ctx, updatedUser)
	if err != nil {
		logger.LogErrorf("Failed to update user cache: %v", err)
	} else {
		logger.LogInfof("User %d cache updated successfully", user.UserId)
	}

	// 如果手机号或邮箱发生变化，需要更新映射缓存
	if oldUser != nil {
		if oldUser.Phone != updatedUser.Phone {
			// 删除旧手机号映射，添加新手机号映射
			if oldUser.Phone != "" {
				d.deletePhoneToUserIdCache(ctx, oldUser.Phone)
			}
			if updatedUser.Phone != "" {
				d.setPhoneToUserIdCache(ctx, updatedUser.Phone, updatedUser.UserId)
			}
		}

		if oldUser.Email != updatedUser.Email {
			// 删除旧邮箱映射，添加新邮箱映射
			if oldUser.Email != "" {
				d.deleteEmailToUserIdCache(ctx, oldUser.Email)
			}
			if updatedUser.Email != "" {
				d.setEmailToUserIdCache(ctx, updatedUser.Email, updatedUser.UserId)
			}
		}
	}

	return updatedUser, nil
}

// UpdateUserLoginInfo 更新用户登录信息
func (d *Dao) UpdateUserLoginInfo(ctx context.Context, userId uint64, loginIp string) error {
	query := `UPDATE %s SET lastLoginAt = ?, lastLoginIp = ?, updatedAt = ?
			  WHERE userId = ?`

	query = d.setUserTable(query, userId)

	now := time.Now()

	// 获取数据库连接
	userIdStr := fmt.Sprintf("%d", userId)
	db, err := d.msshard.DB(userIdStr)
	if err != nil {
		return fmt.Errorf("failed to get database connection: %w", err)
	}

	_, err = db.ExecContext(ctx, query, now, loginIp, now, userId)

	if err != nil {
		return fmt.Errorf("failed to update user login info: %w", err)
	}

	// 获取更新后的用户信息并更新缓存
	updatedUser, err := d.getUserFromDB(ctx, userId)
	if err != nil {
		logger.LogErrorf("Failed to get updated user from DB: %v", err)
		// 如果获取失败，删除缓存以确保数据一致性
		d.deleteUserCache(ctx, userId)
	} else {
		// 更新缓存
		err = d.setUserToCache(ctx, updatedUser)
		if err != nil {
			logger.LogErrorf("Failed to update user cache: %v", err)
		} else {
			logger.LogInfof("User %d login info cache updated successfully", userId)
		}
	}

	return nil
}

// getUserFromCache 从缓存获取用户信息
func (d *Dao) getUserFromCache(ctx context.Context, userId uint64) (*po.User, error) {
	key := fmt.Sprintf(model.RedisUserInfoId, userId)

	userJstr, err := d.cache.GetString(ctx, key)
	if err != nil {
		return nil, err
	}

	user := &po.User{}
	err = js.JSON.UnmarshalFromString(userJstr, user)
	if err != nil {
		logger.LogErrorf("Failed to unmarshal user from cache: %v", err)
		return nil, err
	}

	return user, nil
}

// setUserToCache 设置用户信息到缓存
func (d *Dao) setUserToCache(ctx context.Context, user *po.User) error {
	key := fmt.Sprintf(model.RedisUserInfoId, user.UserId)
	expireTime := int64(24 * 60 * 60)

	_, err := d.cache.Set(ctx, key, user, expireTime)
	if err != nil {
		logger.LogErrorf("Failed to set user to cache: %v", err)
		return err
	}

	return nil
}

// deleteUserCache 删除用户缓存
func (d *Dao) deleteUserCache(ctx context.Context, userId uint64) error {
	key := fmt.Sprintf(model.RedisUserInfoId, userId)

	err := d.cache.DelKey(ctx, key)
	if err != nil {
		logger.LogErrorf("Failed to delete user cache: %v", err)
		return err
	}

	return nil
}

// getUserFromDB 直接从数据库获取用户信息（不走缓存）
func (d *Dao) getUserFromDB(ctx context.Context, userId uint64) (*po.User, error) {
	query := `SELECT userId, phone, email, nickname, avatar, gender, birthday, location, status, loginType, googleId, appleId, lastLoginAt, lastLoginIp, createdAt, updatedAt
			  FROM %s WHERE userId = ?`

	query = d.setUserTable(query, userId)

	// 获取数据库连接
	userIdStr := fmt.Sprintf("%d", userId)
	db, err := d.msshard.DB(userIdStr)
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}

	user := &po.User{}
	err = db.GetContext(ctx, user, query, userId)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get user from db: %w", err)
	}

	return user, nil
}

// setEmptyUserCache 设置空用户缓存（防止缓存穿透）
func (d *Dao) setEmptyUserCache(ctx context.Context, userId uint64) error {
	key := fmt.Sprintf(model.RedisUserInfoId, userId)

	expireTime := int64(5 * 60)
	_, err := d.cache.Set(ctx, key, "null", expireTime)
	if err != nil {
		logger.LogErrorf("Failed to set empty user cache: %v", err)
		return err
	}

	return nil
}

// getUserIdByPhoneFromCache 从缓存获取手机号对应的用户ID
func (d *Dao) getUserIdByPhoneFromCache(ctx context.Context, phone string) (uint64, error) {
	key := fmt.Sprintf(model.RedisPhoneToUserId, phone)

	userIdStr, err := d.cache.GetString(ctx, key)
	if err != nil {
		return 0, err
	}

	userId, err := strconv.ParseUint(userIdStr, 10, 64)
	if err != nil {
		return 0, err
	}

	return userId, nil
}

// setPhoneToUserIdCache 设置手机号到用户ID的映射缓存
func (d *Dao) setPhoneToUserIdCache(ctx context.Context, phone string, userId uint64) error {
	key := fmt.Sprintf(model.RedisPhoneToUserId, phone)

	expireTime := int64(24 * 60 * 60)
	_, err := d.cache.Set(ctx, key, userId, expireTime)
	if err != nil {
		logger.LogErrorf("Failed to set phone to userId cache: %v", err)
		return err
	}

	return nil
}

// deletePhoneToUserIdCache 删除手机号到用户ID的映射缓存
func (d *Dao) deletePhoneToUserIdCache(ctx context.Context, phone string) error {
	key := fmt.Sprintf(model.RedisPhoneToUserId, phone)

	err := d.cache.DelKey(ctx, key)
	if err != nil {
		logger.LogErrorf("Failed to delete phone to userId cache: %v", err)
		return err
	}

	return nil
}

// getUserIdByEmailFromCache 从缓存获取邮箱对应的用户ID
func (d *Dao) getUserIdByEmailFromCache(ctx context.Context, email string) (uint64, error) {
	key := fmt.Sprintf(model.RedisEmailToUserId, email)

	userIdStr, err := d.cache.GetString(ctx, key)
	if err != nil {
		return 0, err
	}

	userId, err := strconv.ParseUint(userIdStr, 10, 64)
	if err != nil {
		return 0, err
	}

	return userId, nil
}

// setEmailToUserIdCache 设置邮箱到用户ID的映射缓存
func (d *Dao) setEmailToUserIdCache(ctx context.Context, email string, userId uint64) error {
	key := fmt.Sprintf(model.RedisEmailToUserId, email)

	expireTime := int64(24 * 60 * 60)
	_, err := d.cache.Set(ctx, key, userId, expireTime)
	if err != nil {
		logger.LogErrorf("Failed to set email to userId cache: %v", err)
		return err
	}

	return nil
}

// deleteEmailToUserIdCache 删除邮箱到用户ID的映射缓存
func (d *Dao) deleteEmailToUserIdCache(ctx context.Context, email string) error {
	key := fmt.Sprintf(model.RedisEmailToUserId, email)

	err := d.cache.DelKey(ctx, key)
	if err != nil {
		logger.LogErrorf("Failed to delete email to userId cache: %v", err)
		return err
	}

	return nil
}

// getUserIdByGoogleIdFromCache 从缓存获取Google ID对应的用户ID
func (d *Dao) getUserIdByGoogleIdFromCache(ctx context.Context, googleId string) (uint64, error) {
	key := fmt.Sprintf(model.RedisGoogleToUserId, googleId)

	userIdStr, err := d.cache.GetString(ctx, key)
	if err != nil {
		return 0, err
	}

	userId, err := strconv.ParseUint(userIdStr, 10, 64)
	if err != nil {
		return 0, err
	}

	return userId, nil
}

// setGoogleToUserIdCache 设置Google ID到用户ID的映射缓存
func (d *Dao) setGoogleToUserIdCache(ctx context.Context, googleId string, userId uint64) error {
	key := fmt.Sprintf(model.RedisGoogleToUserId, googleId)

	expireTime := int64(24 * 60 * 60)
	_, err := d.cache.Set(ctx, key, userId, expireTime)
	if err != nil {
		logger.LogErrorf("Failed to set google to userId cache: %v", err)
		return err
	}

	return nil
}

// deleteGoogleToUserIdCache 删除Google ID到用户ID的映射缓存
func (d *Dao) deleteGoogleToUserIdCache(ctx context.Context, googleId string) error {
	key := fmt.Sprintf(model.RedisGoogleToUserId, googleId)

	err := d.cache.DelKey(ctx, key)
	if err != nil {
		logger.LogErrorf("Failed to delete google to userId cache: %v", err)
		return err
	}

	return nil
}

// getUserIdByAppleIdFromCache 从缓存获取Apple ID对应的用户ID
func (d *Dao) getUserIdByAppleIdFromCache(ctx context.Context, appleId string) (uint64, error) {
	key := fmt.Sprintf(model.RedisAppleToUserId, appleId)

	userIdStr, err := d.cache.GetString(ctx, key)
	if err != nil {
		return 0, err
	}

	userId, err := strconv.ParseUint(userIdStr, 10, 64)
	if err != nil {
		return 0, err
	}

	return userId, nil
}

// setAppleToUserIdCache 设置Apple ID到用户ID的映射缓存
func (d *Dao) setAppleToUserIdCache(ctx context.Context, appleId string, userId uint64) error {
	key := fmt.Sprintf(model.RedisAppleToUserId, appleId)
	expireTime := int64(24 * 60 * 60)
	_, err := d.cache.Set(ctx, key, userId, expireTime)
	if err != nil {
		logger.LogErrorf("Failed to set apple to userId cache: %v", err)
		return err
	}

	return nil
}

// deleteAppleToUserIdCache 删除Apple ID到用户ID的映射缓存
func (d *Dao) deleteAppleToUserIdCache(ctx context.Context, appleId string) error {
	key := fmt.Sprintf(model.RedisAppleToUserId, appleId)

	err := d.cache.DelKey(ctx, key)
	if err != nil {
		logger.LogErrorf("Failed to delete apple to userId cache: %v", err)
		return err
	}

	return nil
}
