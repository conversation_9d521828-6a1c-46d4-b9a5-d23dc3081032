package dao

import (
	"fmt"

	"creativematrix.com/beyondreading/app/base/user/conf"
	"creativematrix.com/beyondreading/pkg/check/shumei"
	"creativematrix.com/beyondreading/pkg/elastic"
	"creativematrix.com/beyondreading/pkg/im/push/getui"
	"creativematrix.com/beyondreading/pkg/mongo"
	"creativematrix.com/beyondreading/pkg/rabbitmq"
	"creativematrix.com/beyondreading/pkg/redis"
	"github.com/BurntSushi/toml"
)

var (
	d Dao
)

func init() {
	app := "base-user"
	c, err := loadConf(app)
	if err != nil {
		panic(err)
	}

	summerConn := mongo.Connect(c.Summer)
	summerConnChat := mongo.Connect(c.MongodbChat)
	schema := map[string]*mongo.Model{}

	for _, name := range summerTable {
		schema[name] = summerConn.Model(name)
	}

	es, err := elastic.NewEsPool(c.ElasticSearch)
	if err != nil {
		panic(err)
	}

	gt, err := getui.InitGeTui(c.GeTui.AppId, c.GeTui.AppKey, c.GeTui.AppSecret, c.GeTui.MasterSecret)
	if err != nil {
		panic(err)
	}

	d = Dao{
		es:             es,
		sm:             shumei.SM(c.SM),
		schema:         schema,
		cache:          redis.Load(c.Cache),
		RabbitMQ:       rabbitmq.NewBroker(c.RabbitMQ.URL, c.RabbitMQ.SummerEx),
		summerConn:     summerConn,
		summerConnChat: summerConnChat,
		conf:           c,
		push:           gt,
	}
}

func loadConf(app string) (*conf.Config, error) {
	cf := &conf.Config{}
	if _, err := toml.DecodeFile("../../../base.toml", cf); err != nil {
		return nil, err
	}
	if _, err := toml.DecodeFile(fmt.Sprintf("../cmd/%s.toml", app), cf); err != nil {
		return nil, err
	}
	return cf, nil
}
