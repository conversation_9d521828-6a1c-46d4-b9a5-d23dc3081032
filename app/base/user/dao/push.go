package dao

import (
	"context"
	"fmt"
	"time"

	pb "creativematrix.com/beyondreading/app/base/user/api"
	"creativematrix.com/beyondreading/app/base/user/model"
	"creativematrix.com/beyondreading/pkg/im/push"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/mongo"
	"creativematrix.com/beyondreading/pkg/utils"
	"go.mongodb.org/mongo-driver/bson"
)

func (d *Dao) UpsertPushRelation(ctx context.Context, req *pb.BindPushRelationReq) error {
	data := &model.TGeTuiPushRelation{
		NoId:       req.NoId,
		Cid:        req.Cid,
		Alias:      req.Alias,
		CreateTime: time.Now(),
	}
	// ios特殊处理，默认置换为后台
	if req.Platform == model.PlatformIos {
		data.Status = model.OnStatusBackground
	}
	_, err := d.schema[model.TableUserPushRelation].Update(bson.M{"noId": req.NoId}, bson.M{"$set": data}, mongo.Upsert())
	return err
}

func (d *Dao) UpdatePushUserStatus(ctx context.Context, noId, status string) error {
	data := bson.M{"status": status}
	_, err := d.schema[model.TableUserPushRelation].Update(bson.M{"noId": noId}, bson.M{"$set": data})
	return err
}

func (d *Dao) BindCidAlias(ctx context.Context, cid, alias string) error {
	var list = make([]*push.UserAlias, 0)
	list = append(list, &push.UserAlias{
		Cid:   cid,
		Alias: alias,
	})
	return d.push.BindAlias(ctx, push.BindAliasParam{
		DataList: list,
	})
}

// UnbindAllAlias 解绑别名
func (d *Dao) UnbindAllAlias(ctx context.Context, alias string) error {
	return d.push.UnbindAllAlias(ctx, alias)
}

func (d *Dao) GetPushAliasList(ctx context.Context, noIds []string) ([]*model.TGeTuiPushRelation, error) {
	var (
		err  error
		list = make([]*model.TGeTuiPushRelation, 0)
	)
	if len(noIds) == 0 {
		return list, nil
	}

	err = d.schema[model.TableUserPushRelation].Find(&list, bson.M{
		"noId":   bson.M{"$in": noIds},
		"status": bson.M{"$ne": model.OnStatusFront},
	})
	return list, err
}

// GetUserAlias 获取用户别名
func (d *Dao) GetUserAlias(ctx context.Context, noId string) (string, error) {
	result := new(model.TGeTuiPushRelation)
	err := d.schema[model.TableUserPushRelation].FindOne(&result, bson.M{"noId": noId})
	return result.Alias, err
}

func (d *Dao) PushByAlias(ctx context.Context, fromId string, alias []string, param *push.MessageParam) error {
	var (
		err    error
		maxOps = 200
	)

	if len(alias) == 0 {
		return nil
	}

	switch len(alias) == 1 {
	case true: // 单个推送
		err = d.push.PushSingleByAlias(ctx, push.PushSingleByAliasParam{
			RequestId:   param.RequestId,
			Audience:    &push.Audience{Alias: alias},
			Settings:    param.Settings,
			PushMessage: param.PushMessage,
			PushChannel: param.PushChannel,
		})
	case false: // 多个推
		doPush := func(_alias []string) error {
			dCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			defer cancel()

			taskId, cErr := d.createMessage(dCtx, param)
			if cErr != nil {
				return cErr
			}
			return d.push.PushListByAlias(dCtx, push.PushListByAliasParam{
				ToList: push.ToList{
					Audience: &push.Audience{
						Alias: _alias,
					},
					TaskId:  taskId,
					IsAsync: true, // 异步推送
				},
			})
		}
		cnt := len(alias) / maxOps
		for i := 0; i <= cnt; i++ {
			var pa []string
			switch temp := i == cnt; temp {
			case false:
				pa = alias[i*maxOps : (i+1)*maxOps]
			case true:
				pa = alias[i*maxOps:]
			}
			if len(pa) == 0 {
				continue
			}
			go func() {
				if err = doPush(pa); err != nil {
					logger.LogErrorw("GeTui push doPush error", "err", err, "fromId", fromId, "alias", pa)
				}
			}()
		}
	}

	return err
}

func (d *Dao) createMessage(ctx context.Context, param *push.MessageParam) (string, error) {
	return d.push.CreateMessage(ctx, push.CreateMessageParam{
		RequestId:   param.RequestId,
		Settings:    param.Settings,
		PushMessage: param.PushMessage,
		PushChannel: param.PushChannel,
	})
}

func (d *Dao) IncDailyPushOpenLiveTimes(ctx context.Context, noId string) (int64, error) {
	return d.cache.IncrByKeyV2(ctx, fmt.Sprintf(model.RedisPushOpenLive, noId, utils.FormatDay()), 1, 86401)
}
