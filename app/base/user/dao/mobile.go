package dao

import (
	"context"
	"time"

	pb "creativematrix.com/beyondreading/app/base/user/api"
	"creativematrix.com/beyondreading/app/base/user/model"
	"creativematrix.com/beyondreading/pkg/mongo"
	"creativematrix.com/beyondreading/pkg/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (d *Dao) AddModifyMobileApply(ctx context.Context, doc interface{}) error {
	_, err := d.schema[model.TableUserMobileModifyRecord].Insert(doc)
	return err
}

func (d *Dao) UpdateModifyMobileApply(ctx context.Context, id string, doc interface{}) error {
	_id, _ := primitive.ObjectIDFromHex(id)
	_, err := d.schema[model.TableUserMobileModifyRecord].Update(bson.M{"_id": _id}, bson.M{"$set": doc})
	return err
}

// GetLatestModifyMobileApply 获取用户未通过的最新一条数据
func (d *Dao) GetLatestModifyMobileApply(ctx context.Context, noId string) (*model.TUserMobileModifyRecord, error) {
	var (
		results = make([]*model.TUserMobileModifyRecord, 0)
	)
	err := d.schema[model.TableUserMobileModifyRecord].Find(&results,
		bson.M{"noId": noId},
		mongo.Sort(bson.M{"applyTime": -1}),
		mongo.Limit(1),
	)
	if err != nil {
		return nil, err
	}
	if len(results) == 0 {
		return &model.TUserMobileModifyRecord{}, nil
	} else {
		return results[0], nil
	}
}

func (d *Dao) ExistModifyMobileApply(ctx context.Context, noId string) bool {
	count := d.schema[model.TableUserMobileModifyRecord].Count(bson.M{"noId": noId, "status": pb.ModifyStatus_Apply.String()})
	return count > 0
}

func (d *Dao) ExistModifyMobileRecord(ctx context.Context, noId string, preDay int) bool {
	query := bson.M{
		"noId":   noId,
		"status": pb.ModifyStatus_Pass.String(),
	}
	s, e := genDateBetween(preDay)
	st, _ := time.ParseInLocation("2006-01-02 15:04:05", s, time.Local)
	et, _ := time.ParseInLocation("2006-01-02 15:04:05", e, time.Local)
	query["updateTime"] = bson.M{
		"$gte": st,
		"$lte": et,
	}
	count := d.schema[model.TableUserMobileModifyRecord].Count(query)
	return count > 0
}

func (d *Dao) GetTimeoutModifyRecord(ctx context.Context) ([]*model.TUserMobileModifyRecord, error) {
	var (
		results = make([]*model.TUserMobileModifyRecord, 0)
	)

	preDate := time.Now().Add(-time.Hour * 72)
	st, _ := time.ParseInLocation(utils.TimeFormat, preDate.Format(utils.TimeFormat), time.Local)
	query := bson.M{
		"applyTime": bson.M{"$lt": st},
		"status":    pb.ModifyStatus_Apply.String(),
	}

	err := d.schema[model.TableUserMobileModifyRecord].Find(&results, query)
	if err != nil {
		return nil, err
	}
	return results, nil
}

func (d *Dao) UpdateTimeoutModifyRecord(ctx context.Context, _ids []primitive.ObjectID, reason string) error {
	_, err := d.schema[model.TableUserMobileModifyRecord].Update(
		bson.M{"_id": bson.M{"$in": _ids}},
		bson.M{"$set": bson.M{
			"status":      pb.ModifyStatus_Timeout.String(),
			"adminReason": reason,
			"updateTime":  time.Now(),
		}},
		mongo.Multi(),
	)
	return err
}

func (d *Dao) GetModifyMobileRecordList(ctx context.Context, param *model.MobileModifyRecordParam) ([]*model.TUserMobileModifyRecord, int64, error) {
	var (
		query   = bson.M{}
		results = make([]*model.TUserMobileModifyRecord, 0)
	)
	if len(param.NoId) > 0 {
		query["noId"] = param.NoId
	}
	if len(param.ApplyTimeStart) > 0 && len(param.ApplyTimeEnd) > 0 {
		st, _ := time.ParseInLocation("2006-01-02 15:04:05", param.ApplyTimeStart+" 00:00:00", time.Local)
		et, _ := time.ParseInLocation("2006-01-02 15:04:05", param.ApplyTimeEnd+" 23:59:59", time.Local)
		query["applyTime"] = bson.M{
			"$gte": st,
			"$lte": et,
		}
	}
	if len(param.Filter) > 0 {
		query["$and"] = bson.A{param.Filter}
	}

	err := d.schema[model.TableUserMobileModifyRecord].Find(&results, query,
		mongo.Skip(param.Skip),
		mongo.Limit(param.Limit),
		mongo.Sort(bson.M{"applyTime": -1}),
	)
	if err != nil {
		return nil, 0, err
	}

	count := d.schema[model.TableUserMobileModifyRecord].Count(query)

	return results, count, nil
}

func genDateBetween(preDays int) (string, string) {
	var (
		nt    = time.Now()
		begin = " 00:00:00"
		end   = " 23:59:59"
		today = nt.Format("2006-01-02")
	)
	if preDays <= 0 {
		return today + begin, today + end
	}
	pre := nt.AddDate(0, 0, -(preDays - 1))
	preDay := pre.Format("2006-01-02")
	return preDay + begin, today + end
}
