package dao

import (
	"context"
	"creativematrix.com/beyondreading/app/api/user/model"
	"go.mongodb.org/mongo-driver/bson"
)

func (d *Dao) GetInterestTagAdded(ctx context.Context, noId string) ([]*model.InterestTagAdded, error) {
	var ret []*model.InterestTagAdded

	query := bson.M{
		"noId": noId,
	}

	if err := d.schema[model.TableInterestTagAdded].Find(&ret, query); err != nil {
		return nil, err
	}

	return ret, nil
}

func (d *Dao) GetInterestsTagAdded(ctx context.Context, noIds []string) ([]*model.InterestTagAdded, error) {
	var ret []*model.InterestTagAdded

	query := bson.M{
		"noId": bson.M{
			"$in": noIds,
		},
	}

	if err := d.schema[model.TableInterestTagAdded].Find(&ret, query); err != nil {
		return nil, err
	}

	return ret, nil
}
