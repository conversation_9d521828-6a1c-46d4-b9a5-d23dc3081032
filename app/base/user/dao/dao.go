package dao

import (
	"context"
	"fmt"

	"creativematrix.com/beyondreading/app/base/user/conf"
	"creativematrix.com/beyondreading/app/common"
	"creativematrix.com/beyondreading/pkg/mysql"
	"creativematrix.com/beyondreading/pkg/redis"
)

type Dao struct {
	msshard mysql.Mysqler
	cache   redis.Redis
	conf    *conf.Config
}

func Load(c *conf.Config) *Dao {
	mysqlConn := mysql.New(c.Mysql)
	redisConn := redis.Load(c.RedisUser)

	return &Dao{
		msshard: mysqlConn,
		cache:   redisConn,
		conf:    c,
	}
}

func (d *Dao) Ping(ctx context.Context) error {
	// 检查MySQL连接
	for _, db := range d.msshard.All() {
		if err := db.PingContext(ctx); err != nil {
			return fmt.Errorf("mysql ping failed: %w", err)
		}
	}

	// 检查Redis连接
	if _, err := d.cache.RDo(ctx, "PING"); err != nil {
		return fmt.Errorf("redis ping failed: %w", err)
	}

	return nil
}

func (d *Dao) Close() {
	// 关闭Redis连接
	if err := d.cache.RClose(); err != nil {
		fmt.Printf("Failed to close redis connection: %v\n", err)
	}
}

// setUserTable 设置用户分表名称
func (d *Dao) setUserTable(query string, userId uint64) string {
	shardIndex := userId % common.UserTableShardNum
	tableName := fmt.Sprintf("user%02d", shardIndex)
	return fmt.Sprintf(query, tableName)
}

// setLoginLogTable 设置登录日志分表名称
func (d *Dao) setLoginLogTable(query string, userId uint64) string {
	shardIndex := userId % common.UserLoginLogTableShardNum
	tableName := fmt.Sprintf("loginLog%02d", shardIndex)
	return fmt.Sprintf(query, tableName)
}
