package dao

import (
	"context"
	"creativematrix.com/beyondreading/pkg/check/shumei"
	"creativematrix.com/beyondreading/pkg/logger"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	rds "github.com/gomodule/redigo/redis"

	"creativematrix.com/beyondreading/app/base/user/conf"
	"creativematrix.com/beyondreading/app/base/user/model"
	"creativematrix.com/beyondreading/pkg/ecode"
	"creativematrix.com/beyondreading/pkg/elastic"
	"creativematrix.com/beyondreading/pkg/im/push/getui"
	"creativematrix.com/beyondreading/pkg/mongo"
	"creativematrix.com/beyondreading/pkg/rabbitmq"
	"creativematrix.com/beyondreading/pkg/redis"
)

type Dao struct {
	cache    redis.Redis
	schema   map[string]*mongo.Model
	es       *elastic.Elastic
	RabbitMQ *rabbitmq.Broker

	sm             shumei.ISM //数美
	summerConn     *mongo.Connection
	summerConnChat *mongo.Connection

	conf *conf.Config

	push *getui.GeTui
}

var summerTable = []string{
	model.TableUser,
	//用户关系、用户会话列表
	model.TableUserAction,
	model.TableNickName,
	model.TableToken,
	model.TableDeviceStatus,
	model.TableUserForbid,
	model.TableUserWhite,
	model.TableForbidSwitch,
	model.TableUserSwitchSetting,
	model.TableUserPushRelation,
	model.TableUserSwitchSetting,
	model.TableReviewRule,
	model.TableUserMobileModifyRecord,
	model.TableInterestTagAdded,
}

func (d *Dao) Ping(ctx context.Context) (err error) {
	return
}

func (d *Dao) Close() {
	d.es.Close()
}

func Load(c *conf.Config) *Dao {
	summerConn := mongo.Connect(c.Summer)
	summerConnChat := mongo.Connect(c.MongodbChat)
	schema := map[string]*mongo.Model{}

	for _, name := range summerTable {
		schema[name] = summerConn.Model(name)
	}

	es, err := elastic.NewEsPool(c.ElasticSearch)
	if err != nil {
		panic(err)
	}

	gt, err := getui.InitGeTui(c.GeTui.AppId, c.GeTui.AppKey, c.GeTui.AppSecret, c.GeTui.MasterSecret)
	if err != nil {
		panic(err)
	}

	return &Dao{
		es:             es,
		sm:             shumei.SM(c.SM),
		schema:         schema,
		cache:          redis.Load(c.Cache),
		RabbitMQ:       rabbitmq.NewBroker(c.RabbitMQ.URL, c.RabbitMQ.SummerEx),
		summerConn:     summerConn,
		summerConnChat: summerConnChat,
		conf:           c,
		push:           gt,
	}
}

func (d *Dao) TokenToID(ctx context.Context, token string) (id string, err error) {
	userId, err := d.cache.GetString(ctx, fmt.Sprintf(model.Redis_UserTokenCache, token))
	if err != nil && err != rds.ErrNil {
		logger.LogInfow("RedisToken", "err", err.Error())
		return "", err
	}

	if userId == "" {
		if token == "" {
			return "", ecode.TOKENERROR
		}
		var t *model.TToken
		if err = d.schema[model.TableToken].FindOne(&t, bson.M{"token": token, "status": "enable"},
			mongo.Sort(bson.M{"_id": -1})); err != nil {
			return "", err
		}

		if time.Since(t.Id.Timestamp()) > time.Hour*24*90 {
			//过期，重新登录
			return "", ecode.TOKENERROR
		}
		_, err = d.cache.Set(ctx, fmt.Sprintf(model.Redis_UserTokenCache, token), t.UserId.Hex(), model.TokenTTL)
		if err != nil {
			return "", err
		}

		return t.UserId.Hex(), nil
	}

	return userId, nil
}
