//Description 封禁
//Date        2022/2/18
//User        cl

package dao

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"

	"creativematrix.com/beyondreading/app/base/user/model"
	"creativematrix.com/beyondreading/pkg/ecode"
)

func (d *Dao) GetForbidSwitch(ctx context.Context) (*model.TUserForbidSwitch, error) {
	data := new(model.TUserForbidSwitch)
	err := d.schema[model.TableForbidSwitch].FindOne(data, bson.M{})
	return data, err
}

func (d *Dao) GetDeviceStatus(ctx context.Context, imei string) (*model.TDeviceStatus, error) {
	data := new(model.TDeviceStatus)
	err := d.schema[model.TableDeviceStatus].FindOne(data, bson.M{"imei": imei})
	if err != nil {
		if err != ecode.NothingFound {
			return nil, err
		}
		data.Status = model.DeviceNormal
	}

	return data, nil
}

func (d *Dao) GetUserWhiteList(ctx context.Context, noId string) (bool, error) {
	whiteUser := new(model.TUserWhiteList)
	err := d.schema[model.TableUserWhite].FindOne(whiteUser, bson.M{"noId": noId})
	if err != nil {
		return false, err
	}
	return true, nil
}

func (d *Dao) GetForbidIP(ctx context.Context, ip string) (bool, error) {
	ipConf := new(model.TUserForbidConf)
	err := d.schema[model.TableUserForbid].FindOne(ipConf, bson.M{"ip": ip})
	if err != nil {
		return false, err
	}
	return true, nil
}

func (d *Dao) GetForbidLonLat(ctx context.Context, lon, lat float32) (bool, error) {
	lonLat := new(model.TUserForbidConf)
	err := d.schema[model.TableUserForbid].FindOne(lonLat, bson.M{
		"rect.top":    bson.M{"$gt": lat},
		"rect.right":  bson.M{"$gt": lon},
		"rect.bottom": bson.M{"$lt": lat},
		"rect.left":   bson.M{"$lt": lon},
	})
	if err != nil {
		return false, err
	}
	return true, nil
}
