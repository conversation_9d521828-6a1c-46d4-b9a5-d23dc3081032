package dao

import (
	"context"
	"creativematrix.com/beyondreading/app/common"
	"fmt"
	"time"

	"creativematrix.com/beyondreading/app/common/po"
	"creativematrix.com/beyondreading/pkg/logger"
)

// CreateLoginLog 创建登录日志
func (d *Dao) CreateLoginLog(ctx context.Context, log *po.LoginLog) error {
	query := `INSERT INTO %s (userId, loginType, loginIp, userAgent, deviceId, loginResult, failReason, createdAt)
			  VALUES (?, ?, ?, ?, ?, ?, ?, ?)`

	query = d.setLoginLogTable(query, log.UserId)

	log.CreatedAt = time.Now()

	// 获取数据库连接
	userIdStr := fmt.Sprintf("%d", log.UserId)
	db, err := d.msshard.DB(userIdStr)
	if err != nil {
		return fmt.Errorf("failed to get database connection: %w", err)
	}

	result, err := db.ExecContext(ctx, query,
		log.UserId, log.LoginType, log.LoginIp, log.UserAgent,
		log.DeviceId, log.LoginResult, log.FailReason, log.CreatedAt)

	if err != nil {
		return fmt.Errorf("failed to create login log: %w", err)
	}

	logId, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("failed to get login log id: %w", err)
	}

	log.LogId = uint64(logId)

	return nil
}

// GetLoginLogs 获取用户登录日志
func (d *Dao) GetLoginLogs(ctx context.Context, userId uint64, page, pageSize int32) ([]*po.LoginLog, int64, error) {
	offset := (page - 1) * pageSize

	// 获取总数
	countQuery := `SELECT COUNT(*) FROM %s WHERE userId = ?`
	countQuery = d.setLoginLogTable(countQuery, userId)

	// 获取数据库连接
	userIdStr := fmt.Sprintf("%d", userId)
	db, err := d.msshard.DB(userIdStr)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get database connection: %w", err)
	}

	var total int64
	err = db.GetContext(ctx, &total, countQuery, userId)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get login logs count: %w", err)
	}

	if total == 0 {
		return []*po.LoginLog{}, 0, nil
	}

	// 获取日志列表
	query := `SELECT logId, userId, loginType, loginIp, userAgent, deviceId, loginResult, failReason, createdAt
			  FROM %s WHERE userId = ? ORDER BY createdAt DESC LIMIT ? OFFSET ?`

	query = d.setLoginLogTable(query, userId)

	var logs []*po.LoginLog
	err = db.SelectContext(ctx, &logs, query, userId, pageSize, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get login logs: %w", err)
	}

	return logs, total, nil
}

// GetLoginLogsByIp 根据IP获取登录日志（用于风控）
func (d *Dao) GetLoginLogsByIp(ctx context.Context, loginIp string, startTime, endTime time.Time) ([]*po.LoginLog, error) {
	var allLogs []*po.LoginLog

	// 需要查询所有分表
	for i := 0; i < 7; i++ {
		query := fmt.Sprintf(`SELECT logId, userId, loginType, loginIp, userAgent, deviceId, loginResult, failReason, createdAt
							  FROM loginLog%02d WHERE loginIp = ? AND createdAt BETWEEN ? AND ?
							  ORDER BY createdAt DESC`, i)

		// 获取数据库连接
		shardIdStr := fmt.Sprintf("%d", i)
		db, err := d.msshard.DB(shardIdStr)
		if err != nil {
			logger.LogErrorf("Failed to get database connection for loginLog%02d: %v", i, err)
			continue
		}

		var logs []*po.LoginLog
		err = db.SelectContext(ctx, &logs, query, loginIp, startTime, endTime)
		if err != nil {
			logger.LogErrorf("Failed to get login logs from loginLog%02d: %v", i, err)
			continue
		}

		allLogs = append(allLogs, logs...)
	}

	return allLogs, nil
}

// GetFailedLoginCount 获取失败登录次数（用于风控）
func (d *Dao) GetFailedLoginCount(ctx context.Context, userId uint64, startTime time.Time) (int64, error) {
	query := `SELECT COUNT(*) FROM %s WHERE userId = ? AND loginResult = 2 AND createdAt >= ?`
	query = d.setLoginLogTable(query, userId)

	// 获取数据库连接
	userIdStr := fmt.Sprintf("%d", userId)
	db, err := d.msshard.DB(userIdStr)
	if err != nil {
		return 0, fmt.Errorf("failed to get database connection: %w", err)
	}

	var count int64
	err = db.GetContext(ctx, &count, query, userId, startTime)
	if err != nil {
		return 0, fmt.Errorf("failed to get failed login count: %w", err)
	}

	return count, nil
}

// GetFailedLoginCountByIp 根据IP获取失败登录次数（用于风控）
func (d *Dao) GetFailedLoginCountByIp(ctx context.Context, loginIp string, startTime time.Time) (int64, error) {
	var totalCount int64

	// 获取数据库连接
	db, err := d.msshard.DB("")
	if err != nil {
		logger.LogErrorf("Failed to get database connection for loginLog: %v", err)
		return -1, err
	}
	// 需要查询所有分表
	for i := 0; i < common.UserLoginLogTableShardNum; i++ {
		query := fmt.Sprintf(`SELECT COUNT(*) FROM loginLog%02d WHERE loginIp = ? AND loginResult = 2 AND createdAt >= ?`, i)

		var count int64
		err := db.GetContext(ctx, &count, query, loginIp, startTime)
		if err != nil {
			logger.LogErrorf("Failed to get failed login count from loginLog%02d: %v", i, err)
			continue
		}

		totalCount += count
	}

	return totalCount, nil
}
