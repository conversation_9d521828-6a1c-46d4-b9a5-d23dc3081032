package dao

import (
	"context"
	"testing"
	"time"

	pb "creativematrix.com/beyondreading/app/base/user/api"
	"creativematrix.com/beyondreading/app/base/user/model"
	"creativematrix.com/beyondreading/pkg/utils"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"gotest.tools/assert"
)

func TestMobile(t *testing.T) {
	noId := "7778909"
	ctx := context.Background()

	t.Run("apply self modify", func(t *testing.T) {
		err := d.AddModifyMobileApply(ctx, model.TUserMobileModifyRecord{
			NoId:        noId,
			FromMobile:  "17611111111",
			ToMobile:    "17622222222",
			Type:        pb.ModifyType_UserSelf.String(),
			Status:      pb.ModifyStatus_Pass.String(),
			ApplyReason: "更换手机号",
			ApplyTime:   time.Now(),
			UpdateTime:  time.Now(),
		})
		assert.NilError(t, err)
	})

	t.Run("apply by help", func(t *testing.T) {
		err := d.AddModifyMobileApply(ctx, model.TUserMobileModifyRecord{
			NoId:        noId,
			FromMobile:  "17611111111",
			ToMobile:    "17622222222",
			Type:        pb.ModifyType_AdminHelp.String(),
			Status:      pb.ModifyStatus_Apply.String(),
			ApplyReason: "更换手机号",
			ProveImg:    []string{"1111.png", "2222.png"},
			ApplyTime:   time.Now(),
		})
		assert.NilError(t, err)
	})

	t.Run("reject", func(t *testing.T) {
		err := d.UpdateModifyMobileApply(ctx, "6333c206b40af6250660a848", model.TUserMobileModifyRecord{
			OperatorId:   "1111",
			OperatorName: "admin111",
			Status:       pb.ModifyStatus_Reject.String(),
			AdminReason:  "不符合更换要求",
			UpdateTime:   time.Now(),
		})
		assert.NilError(t, err)
	})

	t.Run("exist apply", func(t *testing.T) {
		b := d.ExistModifyMobileApply(ctx, noId)
		t.Log(b)
	})

	t.Run("exist pass limit 7 day", func(t *testing.T) {
		b := d.ExistModifyMobileRecord(ctx, noId, 7)
		t.Log(b)
	})

	t.Run("latest modify apply", func(t *testing.T) {
		data, err := d.GetLatestModifyMobileApply(ctx, noId)
		assert.NilError(t, err)

		t.Log(data)
	})

	t.Run("", func(t *testing.T) {
		results, _, err := d.GetModifyMobileRecordList(ctx, &model.MobileModifyRecordParam{
			NoId:           "",
			ApplyTimeStart: "",
			ApplyTimeEnd:   "",
			Skip:           0,
			Limit:          0,
		})
		assert.NilError(t, err)
		for _, result := range results {
			t.Log(string(utils.JsonByte(result)))
		}
	})

	t.Run("", func(t *testing.T) {
		t.Log(genDateBetween(7))
	})

	t.Run("", func(t *testing.T) {
		results, err := d.GetTimeoutModifyRecord(ctx)
		assert.NilError(t, err)
		for _, result := range results {
			t.Log(string(utils.JsonByte(result)))
		}
	})

	t.Run("", func(t *testing.T) {
		var _ids []primitive.ObjectID
		results, _ := d.GetTimeoutModifyRecord(ctx)
		for _, result := range results {
			_ids = append(_ids, result.Id)
		}

		err := d.UpdateTimeoutModifyRecord(ctx, _ids, "")
		assert.NilError(t, err)
	})
}
