package conf

import (
	"creativematrix.com/beyondreading/pkg/redis"
	"fmt"

	"creativematrix.com/beyondreading/pkg/config"
)

type Config struct {
	config.Base

	Log struct {
		Level string `toml:"level"`
	} `toml:"log"`

	RedisUser *redis.Config

	SMS struct {
		Provider  string `toml:"provider"`   // 短信服务提供商：aliyun, tencent
		AccessKey string `toml:"access_key"` // AccessKey
		SecretKey string `toml:"secret_key"` // SecretKey
		SignName  string `toml:"sign_name"`  // 短信签名
		Template  struct {
			Register string `toml:"register"` // 注册模板ID
			Login    string `toml:"login"`    // 登录模板ID
		} `toml:"template"`
	} `toml:"sms"`

	JWT struct {
		Secret     string `toml:"secret"`      // JWT密钥
		ExpireTime int64  `toml:"expire_time"` // 过期时间（秒）
		Issuer     string `toml:"issuer"`      // 签发者
	} `toml:"jwt"`

	Google struct {
		ClientID     string `toml:"client_id"`     // Google OAuth客户端ID
		ClientSecret string `toml:"client_secret"` // Google OAuth客户端密钥
	} `toml:"google"`

	Apple struct {
		TeamID   string `toml:"team_id"`   // Apple Team ID
		ClientID string `toml:"client_id"` // Apple Client ID
		KeyID    string `toml:"key_id"`    // Apple Key ID
		KeyFile  string `toml:"key_file"`  // Apple私钥文件路径
	} `toml:"apple"`
}

func Load(app string) *Config {
	var conf = new(Config)
	if err := config.Load(app, conf); err != nil {
		panic(fmt.Sprintf("config load failed: %v", err))
	}
	return conf
}
