package conf

import (
	"creativematrix.com/beyondreading/pkg/check/shumei"
	"fmt"

	"creativematrix.com/beyondreading/pkg/config"
	"creativematrix.com/beyondreading/pkg/im/push/getui"
	"creativematrix.com/beyondreading/pkg/im/rong_cloud"
	"creativematrix.com/beyondreading/pkg/rabbitmq"
)

type Config struct {
	config.Base

	Log struct {
		Level string
	}

	Rcould rong_cloud.RcouldConfig

	NotifyURL string

	Weixinpay map[string]*WxConf

	Appstore struct {
		IsProduction    bool
		CanUseSanboxIds []string
		Passwords       map[string]string
	}

	Alipay struct {
		Partner      string
		SellerId     string
		AppId        string
		PrivateKey   string
		AliPublicKey string
	}

	RabbitMQ struct {
		URL      string
		SummerEx *rabbitmq.ExchangeConfig
	}

	//对称加密
	MsgKey struct {
		Key string
	}

	GeTui getui.Config
	SM    shumei.SMConfig
}

type WxConf struct {
	AppId  string
	MchId  string
	Key    string
	PlanId int
}

func Load(app string) *Config {
	var conf = new(Config)
	if err := config.Load(app, conf); err != nil {
		panic(fmt.Sprintf("config load failed: %v", err))
	}
	return conf
}
