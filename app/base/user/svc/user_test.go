package svc

import (
	"context"
	"creativematrix.com/beyondreading/app/base/user/conf"
	"creativematrix.com/beyondreading/pkg/logger"
	pb "creativematrix.com/beyondreading/proto/user"
	"fmt"
	"github.com/BurntSushi/toml"
	"gotest.tools/assert"
	"testing"
)

func doInit() *UserSvc {
	app := "base-user"
	cf, err := loadConf(app)
	if err != nil {
		panic(err)
	}

	logger.InitLog(app, cf.Log.Level)
	userSvc := Load(cf)

	return userSvc
}

func loadConf(app string) (*conf.Config, error) {
	cf := &conf.Config{}
	if _, err := toml.DecodeFile("../../../base.toml", cf); err != nil {
		return nil, err
	}
	if _, err := toml.DecodeFile(fmt.Sprintf("../cmd/%s.toml", app), cf); err != nil {
		return nil, err
	}
	return cf, nil
}

func TestUserSvc_RegisterBySms(t *testing.T) {

	userSvc := doInit()
	req := &pb.RegisterBySmsReq{
		Phone:     "13666666666",
		SmsCode:   "633720",
		Nickname:  "NickName",
		ClientIp:  "***********",
		UserAgent: "Mozilla/5.0",
		DeviceId:  "666666",
	}
	got, err := userSvc.RegisterBySms(context.Background(), req)
	if err != nil {
		logger.LogErrorf("%v", err)
	}

	fmt.Printf("%v\n", got)
	assert.NilError(t, err)
}

func TestUserSvc_SendSmsCode(t *testing.T) {
	userSvc := doInit()
	req := &pb.SendSmsCodeReq{
		Phone:    "13666666666",
		CodeType: 1,
	}
	got, err := userSvc.SendSmsCode(context.Background(), req)
	if err != nil {
		logger.LogErrorf("%v", err)
	}

	fmt.Printf("%v\n", got)
	assert.NilError(t, err)
}

func TestUserSvc_LoginBySms(t *testing.T) {
	userSvc := doInit()
	//req := &pb.SendSmsCodeReq{
	//	Phone:    "13666666666",
	//	CodeType: 2,
	//}
	//got, err := userSvc.SendSmsCode(context.Background(), req)
	//if err != nil {
	//	logger.LogErrorf("%v", err)
	//}

	req2 := pb.LoginBySmsReq{
		Phone:     "13666666666",
		SmsCode:   "083058",
		ClientIp:  "***********",
		UserAgent: "Mozilla/5.0",
		DeviceId:  "666666",
	}

	loginRsp, err := userSvc.LoginBySms(context.Background(), &req2)
	if err != nil {
		logger.LogErrorf("%v", err)
	}

	fmt.Printf("%v\n", loginRsp)
	assert.NilError(t, err)
}
