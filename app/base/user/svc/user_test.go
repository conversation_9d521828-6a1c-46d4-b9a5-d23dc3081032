package svc

import (
	"context"
	"creativematrix.com/beyondreading/app/base/user/model"
	"encoding/json"
	"testing"
	"time"

	pb "creativematrix.com/beyondreading/app/base/user/api"
	"gotest.tools/assert"
)

var noId = "7778909"

func TestSvc_UpdateSwitchSetting(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	_, err := svc.UpdateSwitchSetting(ctx, &pb.SetUserSwitchReq{
		NoId:                noId,
		AutoRefuseMultiChat: model.SwitchOn,
	})
	assert.NilError(t, err)
}

func TestSvc_GetSwitchSetting(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	data, err := svc.GetSwitchSetting(ctx, &pb.GetUserSwitchReq{
		NoIds: []string{noId},
	})
	assert.NilError(t, err)
	b, _ := json.Marshal(data)
	t.Log(string(b))
}

func TestSvc_GetLocationFromIP(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	//ip := "***************"
	ip := "***************"
	data, err := svc.GetLocationFromIP(ctx, ip)
	assert.NilError(t, err)
	b, _ := json.Marshal(data)
	t.Log(string(b))
}
