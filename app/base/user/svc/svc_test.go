package svc

import (
	"fmt"

	"creativematrix.com/beyondreading/app/base/user/conf"
	"creativematrix.com/beyondreading/pkg/logger"
	"github.com/BurntSushi/toml"
)

var svc *Svc

func init() {
	app := "base-user"
	cf, err := loadConf(app)
	if err != nil {
		panic(err)
	}

	logger.InitLog(app, cf.Log.Level)
	svc = Load(cf)
}

func loadConf(app string) (*conf.Config, error) {
	cf := &conf.Config{}
	if _, err := toml.DecodeFile("../../../base.toml", cf); err != nil {
		return nil, err
	}
	if _, err := toml.DecodeFile(fmt.Sprintf("../cmd/%s.toml", app), cf); err != nil {
		return nil, err
	}
	return cf, nil
}
