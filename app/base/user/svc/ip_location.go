//Description   ip地理位置
//Time          2022/9/28
//User          cl

package svc

import (
	"context"
	pb "creativematrix.com/beyondreading/app/base/user/api"
	"creativematrix.com/beyondreading/app/base/user/model"
	"creativematrix.com/beyondreading/pkg/logger"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"github.com/go-resty/resty/v2"
)

func (s *Svc) GetLocationFromIP(ctx context.Context, ip string) (*pb.IPLocRsp, error) {
	str := model.GetSignStr(ip)
	hash := hmac.New(sha256.New, []byte(model.LocAppSecret))
	hash.Write([]byte(str))
	sum := hash.Sum(nil)
	sign := base64.StdEncoding.EncodeToString(sum)

	headers := map[string]string{
		"Accept":                 "application/json; charset=utf-8",
		"x-ca-key":               model.LocAppKey,
		"x-ca-signature-method":  "HmacSHA256",
		"X-Ca-Signature-Headers": "",
		"X-Ca-Signature":         sign,
	}

	res := new(pb.IPLocRsp)
	rsp := new(model.LocRsp)
	_, err := resty.New().R().SetHeaders(headers).SetResult(rsp).Get(model.LocUrl + ip)
	if err != nil {
		logger.LogErrorw("GetLocationFromIP", ip, err.Error())
		return res, nil
	}

	res.Province = rsp.Result.AddressInfo.Province
	return res, nil
}
