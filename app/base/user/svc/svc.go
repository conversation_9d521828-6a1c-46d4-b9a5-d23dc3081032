package svc

import (
	"context"

	"creativematrix.com/beyondreading/app/base/user/conf"
	"creativematrix.com/beyondreading/app/base/user/dao"
	"creativematrix.com/beyondreading/app/base/user/service"
)

type UserSvc struct {
	conf          *conf.Config
	dao           *dao.Dao
	smsService    *service.SmsService
	jwtService    *service.JwtService
	googleService *service.GoogleService
	appleService  *service.AppleService
}

func Load(c *conf.Config) *UserSvc {
	svc := &UserSvc{
		conf:          c,
		dao:           dao.Load(c),
		smsService:    service.NewSmsService(c),
		jwtService:    service.NewJwtService(c),
		googleService: service.NewGoogleService(c),
		appleService:  service.NewAppleService(c),
	}

	return svc
}

func (s *UserSvc) Ping(ctx context.Context) error {
	return s.dao.Ping(ctx)
}

func (s *UserSvc) Close() {
	s.dao.Close()
}
