package svc

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	pb "creativematrix.com/beyondreading/app/base/user/api"
	"creativematrix.com/beyondreading/app/base/user/model"
	"creativematrix.com/beyondreading/pkg/im/push"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/utils"
)

func (s *Svc) UpsertPushRelation(ctx context.Context, req *pb.BindPushRelationReq) error {
	var err error
	defer func() {
		if err != nil {
			logger.LogErrorw("GeTui push bind alias error", "req", req, "err", err)
		}
	}()

	// 解绑该别名绑定过的cid，处理一个账号登陆多台设备后，该alias绑定多个cid，出现消息乱串问题
	if err = s.dao.UnbindAllAlias(ctx, req.Alias); err != nil {
		return err
	}
	// 重新绑定别名
	if err = s.dao.BindCidAlias(ctx, req.Cid, req.<PERSON>); err != nil {
		return err
	}
	// 维护用户别名与cid关系
	if err = s.dao.UpsertPushRelation(ctx, req); err != nil {
		return err
	}

	return err
}

func (s *Svc) UnBindPushRelation(ctx context.Context, req *pb.UnBindPushRelationReq) error {
	alias, err := s.dao.GetUserAlias(ctx, req.NoId)
	if err != nil && err != model.NothingFound {
		return err
	}

	if len(alias) == 0 {
		return nil
	}
	return s.dao.UnbindAllAlias(ctx, alias)
}

func (s *Svc) PushByAlias(ctx context.Context, req *pb.PushByAliasReq) error {
	var (
		nickname, avatar  string
		title, body       string
		iosTitle, iosBody string
		targetIds         []string
	)
	// 获取用户信息
	users, err := s.dao.GetUsersByIds(ctx, &pb.UserIdsReq{
		Ids: []string{req.FromId},
	})
	if err != nil {
		return err
	}
	if len(users) == 0 || users[0] == nil {
		logger.LogWarnw("GeTui push fromId empty", "fromId", req.FromId, "usersInfo", users)
		return nil
	}
	nickname = users[0].NickName
	avatar = users[0].AvatarUrl
	switch req.TargetType {
	case pb.TargetType_OpenLive:
		title = fmt.Sprintf("你关注的@%s", nickname)
		body = "正在直播，快来围观~"
		iosTitle = "心恋"
		iosBody = fmt.Sprintf("你关注的@%s正在直播～", nickname)
	case pb.TargetType_ChatMessage:
		title = "新消息～"
		body = fmt.Sprintf("%s给你发送了%d条消息", nickname, 1)
		iosTitle = ""
		iosBody = fmt.Sprintf("%s给你发送了%d条消息", nickname, 1)
	}

	// 检查推送开关
	sm, err := s.getPushSwitchToMap(ctx, req.ToIds)
	if err != nil {
		return err
	}

	for _, id := range req.ToIds {
		var open bool
		v, ok := sm[id]
		if !ok {
			open = true
		}
		switch req.TargetType {
		case pb.TargetType_OpenLive:
			if ok && v.OpenLivePush != model.SwitchOff {
				open = true
			}

			if open && !s.isOverLimit(ctx, id) {
				targetIds = append(targetIds, id)
			}
		case pb.TargetType_ChatMessage:
			if ok && v.PrivateChatPush != model.SwitchOff {
				open = true
			}

			if open {
				targetIds = append(targetIds, id)
			}
		}
	}

	logoUrl := fmt.Sprintf("%s%s", s.conf.BaseImgUrl, utils.RelativePath(avatar))
	param := &model.NotificationParam{
		Title:      title,
		Body:       body,
		IosTitle:   iosTitle,
		IosBody:    iosBody,
		LogoUrl:    logoUrl,
		FromId:     req.FromId,
		TargetType: req.TargetType,
	}
	if req.Attach != nil {
		param.LiveType = req.Attach.LiveType
	}

	message := s.genNotifyMessage(param)

	go func() {
		pCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		var alias []string
		aliasList, _ := s.dao.GetPushAliasList(pCtx, targetIds)
		for _, al := range aliasList {
			alias = append(alias, al.Alias)
		}

		if err = s.dao.PushByAlias(pCtx, req.FromId, alias, message); err != nil {
			logger.LogErrorw("GeTui push by alias error", "err", err, "fromId", req.FromId, "alias", alias)
			return
		}

		logger.LogInfow("GeTui push doPush success", "fromId", req.FromId, "targetIds", targetIds, "switch", sm, "alias", alias)
	}()
	return nil
}

func (s *Svc) genNotifyMessage(param *model.NotificationParam) *push.MessageParam {
	iosChannel := genIosChannel(param)
	notification := genNotification(param)

	return &push.MessageParam{
		RequestId: utils.Uuid(),
		Settings: &push.Settings{
			TTL: 3600000, // 默认一小时，消息离线时间设置，单位毫秒
			Strategy: &push.Strategy{
				Default: 1, // 1: 表示该消息在用户在线时推送个推通道，用户离线时推送厂商通道。
				Ios:     4, // 4: 表示该消息优先从厂商通道下发，若消息内容在厂商通道代发失败后会从个推通道下发。
				St:      4,
				Hw:      4,
				Xm:      4,
				Vv:      4,
				Mz:      4,
				Op:      4,
			},
		},
		PushMessage: &push.PushMessage{
			Notification: notification,
		},
		PushChannel: &push.PushChannel{
			Ios: iosChannel,
			Android: &push.AndroidChannel{Ups: &push.Ups{
				Notification: notification,
			}},
		},
	}
}

func (s *Svc) getPushSwitchToMap(ctx context.Context, toIds []string) (map[string]*pb.SwitchInfo, error) {
	sMap := make(map[string]*pb.SwitchInfo)
	if len(toIds) == 0 {
		return sMap, nil
	}
	rsp, err := s.GetSwitchSetting(ctx, &pb.GetUserSwitchReq{
		NoIds: toIds,
	})
	if err != nil {
		return sMap, err
	}
	for _, info := range rsp.List {
		sMap[info.NoId] = info
	}
	return sMap, nil
}

func genIosChannel(param *model.NotificationParam) *push.IosChannel {
	ms := make([]*push.Multimedia, 0)
	ms = append(ms, &push.Multimedia{
		Url:  param.LogoUrl,
		Type: 1,
	})

	var pl string
	switch param.TargetType {
	case pb.TargetType_OpenLive: // 跳转直播间
		b, _ := json.Marshal(&model.PayLoad{
			PushType: param.TargetType.String(),
			Attach: &model.Attach{
				NoId:    param.FromId,
				SubType: genLiveSubType(param.LiveType),
			},
		})
		pl = string(b)
	case pb.TargetType_ChatMessage:
		b, _ := json.Marshal(&model.PayLoad{
			PushType: param.TargetType.String(),
			Attach: &model.Attach{
				NoId: param.FromId,
			},
		})
		pl = string(b)
	}

	return &push.IosChannel{
		Type: "notify",
		Aps: &push.Aps{
			Alert: &push.Alert{
				Title: param.IosTitle,
				Body:  param.IosBody,
			},
		},
		AutoBadge:  "+1",
		PayLoad:    pl,
		Multimedia: ms,
	}
}

func genNotification(param *model.NotificationParam) *push.Notification {
	var intent string
	base := "intent://app.kela.com#Intent;scheme=kela;launchFlags=0x4000000;package=com.xingxuan.kela;component=com.xingxuan.kela/com.tianyu.zhuita.ui.splash.SplashActivity;S.gtparam=%s;S.gttask=;end"
	switch param.TargetType {
	case pb.TargetType_OpenLive: // 开播
		intent = fmt.Sprintf(base, fmt.Sprintf("kela://app.kela.com/live?anchorId=%s&subType=%s",
			param.FromId, genLiveSubType(param.LiveType)))
	case pb.TargetType_ChatMessage: // 私聊
		intent = fmt.Sprintf(base, fmt.Sprintf("kela://app.kela.com/message?fromId=%s", param.FromId))
	}
	return &push.Notification{
		Title:        param.Title,
		Body:         param.Body,
		ClickType:    "intent",
		Intent:       intent,
		ChannelLevel: 4,
		LogoUrl:      param.LogoUrl,
	}
}

func genLiveSubType(liveType string) string {
	var subType string
	switch liveType {
	case "Live":
		subType = "liveHot"
	case "Voice":
		subType = "VoiceHot"
	}
	return subType
}

func (s *Svc) isOverLimit(ctx context.Context, noId string) bool {
	// 今日收到开播推送次数限制
	times, err := s.dao.IncDailyPushOpenLiveTimes(ctx, noId)
	if err != nil {
		logger.LogErrorw("pushOpenLive inc redis error", "noId", noId, "err", err.Error())
		return true
	}
	if times > model.PushOpenLiveTimes {
		logger.LogInfow("GeTui push open live over receive times", "noId", noId, "times", times)
		return true
	}
	return false
}

func (s *Svc) SetPushStatus(ctx context.Context, req *pb.SetPushStatusReq) error {
	return s.dao.UpdatePushUserStatus(ctx, req.NoId, req.Status)
}
