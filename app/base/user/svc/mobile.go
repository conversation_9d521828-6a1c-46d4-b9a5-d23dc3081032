package svc

import (
	"context"
	"time"

	pb "creativematrix.com/beyondreading/app/base/user/api"
	"creativematrix.com/beyondreading/app/base/user/model"
	"creativematrix.com/beyondreading/pkg/utils"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *Svc) TimeoutModifyMobile(ctx context.Context, req *pb.TimeoutModifyMobileReq) (*pb.TimeoutModifyMobileRsp, error) {
	var (
		_ids    []primitive.ObjectID
		noIdMap = make(map[string]struct{})
		noIds   []string
		tMap    = make(map[string]string)
		rsp     = new(pb.TimeoutModifyMobileRsp)
	)

	results, err := s.dao.GetTimeoutModifyRecord(ctx)
	if err != nil {
		return nil, err
	}
	if len(results) == 0 {
		return rsp, nil
	}

	for _, result := range results {
		_ids = append(_ids, result.Id)
		noIdMap[result.NoId] = struct{}{}
	}

	if err = s.dao.UpdateTimeoutModifyRecord(ctx, _ids, defaultAdminReason(pb.ModifyStatus_Timeout)); err != nil {
		return nil, err
	}

	for k := range noIdMap {
		noIds = append(noIds, k)
	}

	uInfos, err := s.dao.GetUsersByIds(ctx, &pb.UserIdsReq{Ids: noIds, Cols: []string{"noId", "msgId"}})
	if err != nil {
		return nil, err
	}
	for _, info := range uInfos {
		tMap[info.NoId] = info.MsgId
	}
	rsp.TMap = tMap
	return rsp, nil
}

func (s *Svc) GetAndCheckMobile(ctx context.Context, req *pb.GetAndCheckMobileReq) (*pb.GetAndCheckMobileRsp, error) {
	var (
		rsp = new(pb.GetAndCheckMobileRsp)
	)

	switch req.Type {
	case pb.CheckType_CheckLimitTimes:
		rsp.IsOverTimes = s.isOverModifyTimes(ctx, req.NoId)
	case pb.CheckType_CheckConflict:
		toId, err := s.dao.IsUserUsingMobile(ctx, req.ToMobile)
		if err != nil {
			return nil, err
		}
		if len(toId) == 0 {
			return rsp, nil
		}

		if req.NoId == toId {
			return rsp, model.FromToMobileIsSameErr
		}

		rsp.IsConflict = len(toId) > 0

		uInfos, err := s.dao.GetUsersByIds(ctx, &pb.UserIdsReq{Ids: []string{req.NoId, toId}})
		if err != nil {
			return nil, err
		}

		for _, info := range uInfos {
			mData := &pb.MobileUserInfo{
				NoId:        info.NoId,
				Nickname:    info.NickName,
				Avatar:      info.AvatarUrl,
				WealthLevel: utils.GetWealthLv(info.Wealth),
				CharmLevel:  utils.GetCharmLv(info.Charm),
				Mobile:      info.Mobile,
			}
			if info.NoId == req.NoId {
				rsp.From = mData
			} else {
				rsp.To = mData
			}
		}
	}

	return rsp, nil
}

func (s *Svc) GetLatestModifyRecord(ctx context.Context, req *pb.GetModifyMobileInfoReq) (*pb.GetModifyMobileInfoRsp, error) {
	rsp := &pb.GetModifyMobileInfoRsp{NoId: req.NoId}
	data, err := s.dao.GetLatestModifyMobileApply(ctx, req.NoId)
	if err != nil {
		return nil, err
	}

	if len(data.Status) > 0 && data.Status != pb.ModifyStatus_Pass.String() {
		rsp.FromMobile = data.FromMobile
		rsp.ToMobile = data.ToMobile
		rsp.ApplyReason = data.ApplyReason
		rsp.ProveImg = data.ProveImg
		rsp.ModifyType = data.Type
		rsp.Status = data.Status
	} else {
		mobile, err := s.getSelfMobile(ctx, req.NoId)
		if err != nil {
			return nil, err
		}
		rsp.FromMobile = mobile
	}
	return rsp, nil
}

func (s *Svc) GetModifyMobileList(ctx context.Context, req *pb.GetModifyMobileListReq) (*pb.GetModifyMobileListRsp, error) {
	param := &model.MobileModifyRecordParam{
		NoId:           req.NoId,
		ApplyTimeStart: req.StartTime,
		ApplyTimeEnd:   req.EndTime,
	}

	param.Filter = filterToQuery(req.FilterParam)
	param.Skip = (req.Page - 1) * req.PageSize
	param.Limit = req.PageSize

	results, count, err := s.dao.GetModifyMobileRecordList(ctx, param)
	if err != nil {
		return nil, err
	}

	var list = make([]*pb.ModifyMobileData, 0)
	for _, result := range results {
		list = append(list, &pb.ModifyMobileData{
			Id:           result.Id.Hex(),
			NoId:         result.NoId,
			FromMobile:   result.FromMobile,
			ToMobile:     result.ToMobile,
			ApplyReason:  result.ApplyReason,
			AdminReason:  result.AdminReason,
			OperatorName: result.OperatorName,
			ProveImg:     result.ProveImg,
			Status:       resultToFilter(result.Type, result.Status),
			ApplyTime:    handleDateAddTime(result.ApplyTime),
			UpdateTime:   handleDateAddTime(result.UpdateTime),
		})
	}

	return &pb.GetModifyMobileListRsp{
		List:  list,
		Count: count,
	}, nil
}

func (s *Svc) ApplyModifyMobile(ctx context.Context, req *pb.ApplyModifyMobileReq) error {
	var err error
	isReal, err := s.isRealName(ctx, req.NoId)
	if err != nil {
		return err
	}
	if !isReal {
		return model.MobileUserNotRealNameErr
	}

	switch len(req.Id) > 0 {
	case true:
		// 后台审核记录
		err = s.reviewModifyRecord(ctx, req)
	case false:
		// 客户端主动申请/后台特许修改
		err = s.addModifyRecord(ctx, req)
	}
	return err
}

func (s *Svc) addModifyRecord(ctx context.Context, req *pb.ApplyModifyMobileReq) error {
	// 审核中
	if s.dao.ExistModifyMobileApply(ctx, req.NoId) {
		switch req.ModifyType {
		case pb.ModifyType_AdminAuth:
			return model.AuthModifyHasApplyErr
		default:
			return model.ModifyHasApplyErr
		}
	}
	// 7日内仅能修改1次手机号
	if req.ModifyType != pb.ModifyType_AdminAuth {
		if s.isOverModifyTimes(ctx, req.NoId) {
			return model.OverChangeMobileLimitErr
		}
	}
	// 手机号是否已经被占用
	err := s.checkMobile(ctx, req.NoId, req.FromMobile, req.ToMobile, false)
	if err != nil {
		return err
	}

	addDoc := &model.TUserMobileModifyRecord{
		NoId:         req.NoId,
		FromMobile:   req.FromMobile,
		ToMobile:     req.ToMobile,
		OperatorId:   req.OperatorId,
		OperatorName: req.OperatorName,
		Type:         req.ModifyType.String(),
		ApplyReason:  req.ApplyReason,
		ApplyTime:    time.Now(),
	}
	switch req.ModifyType {
	case pb.ModifyType_UserSelf:
		addDoc.Status = pb.ModifyStatus_Pass.String()
		addDoc.UpdateTime = time.Now()
	case pb.ModifyType_AdminHelp:
		addDoc.Status = pb.ModifyStatus_Apply.String()
		addDoc.ProveImg = req.ProveImg
	case pb.ModifyType_AdminAuth:
		addDoc.Status = pb.ModifyStatus_Pass.String()
		addDoc.UpdateTime = time.Now()
	default:
		return model.NothingFound
	}

	if addDoc.Status == pb.ModifyStatus_Pass.String() {
		return s.modifyUserMobile(ctx, "", addDoc)
	} else {
		return s.dao.AddModifyMobileApply(ctx, addDoc)
	}
}

func (s *Svc) getSelfMobile(ctx context.Context, noId string) (string, error) {
	uInfos, err := s.dao.GetUsersByIds(ctx, &pb.UserIdsReq{Ids: []string{noId}})
	if err != nil {
		return "", err
	}
	if len(uInfos) == 0 || uInfos[0] == nil || len(uInfos[0].Mobile) == 0 {
		return "", model.NothingFound
	}
	return uInfos[0].Mobile, nil
}

func (s *Svc) isRealName(ctx context.Context, noId string) (bool, error) {
	uInfos, err := s.dao.GetUsersByIds(ctx, &pb.UserIdsReq{Ids: []string{noId}})
	if err != nil {
		return false, err
	}
	if len(uInfos) == 0 || uInfos[0] == nil {
		return false, nil
	}
	return uInfos[0].RealNameStatus == model.Success, nil
}

func (s *Svc) modifyUserMobile(ctx context.Context, id string, doc *model.TUserMobileModifyRecord) error {
	var err error
	if len(id) > 0 {
		err = s.dao.UpdateModifyMobileApply(ctx, id, doc)
	} else {
		err = s.dao.AddModifyMobileApply(ctx, doc)
	}
	if err != nil {
		return err
	}

	return s.dao.UpdateMobile(ctx, doc.NoId, doc.ToMobile)
}

func (s *Svc) reviewModifyRecord(ctx context.Context, req *pb.ApplyModifyMobileReq) error {
	if !isAdminHelpApply(req.ModifyType) {
		return model.NothingFound
	}

	uptDoc := &model.TUserMobileModifyRecord{
		NoId:         req.NoId,
		ToMobile:     req.ToMobile,
		OperatorId:   req.OperatorId,
		OperatorName: req.OperatorName,
		AdminReason:  req.AdminReason,
		Status:       req.ModifyStatus.String(),
		UpdateTime:   time.Now(),
	}
	if len(uptDoc.AdminReason) == 0 {
		uptDoc.AdminReason = defaultAdminReason(req.ModifyStatus)
	}

	if uptDoc.Status == pb.ModifyStatus_Pass.String() {
		// 手机号是否已经被占用
		err := s.checkMobile(ctx, req.NoId, req.FromMobile, req.ToMobile, true)
		if err != nil {
			return err
		}

		return s.modifyUserMobile(ctx, req.Id, uptDoc)
	} else {
		return s.dao.UpdateModifyMobileApply(ctx, req.Id, uptDoc)
	}
}

func (s *Svc) isOverModifyTimes(ctx context.Context, noId string) bool {
	return s.dao.ExistModifyMobileRecord(ctx, noId, model.ChangeMobilePreDay)
}

func (s *Svc) checkMobile(ctx context.Context, noId, fromMobile, toMobile string, isAdmin bool) error {
	fromId, err := s.dao.IsUserUsingMobile(ctx, fromMobile)
	if err != nil {
		return err
	}
	if len(fromId) == 0 {
		return model.FromMobileNotExistErr
	}
	if noId != fromId {
		return model.MobileNotSelfErr
	}

	toId, err := s.dao.IsUserUsingMobile(ctx, toMobile)
	if err != nil {
		return err
	}
	if len(toId) > 0 {
		if isAdmin {
			return model.ReviewToMobileHasExistErr
		} else {
			return model.ToMobileHasExistErr
		}
	}
	return nil
}

func isAdminHelpApply(typ pb.ModifyType) bool {
	return typ == pb.ModifyType_AdminHelp
}

func defaultAdminReason(status pb.ModifyStatus) (r string) {
	switch status {
	case pb.ModifyStatus_Reject:
		r = "资料不完整，请按照要求重新提交"
	case pb.ModifyStatus_Timeout:
		r = "材料递交不完全，请重新提交"
	}
	return
}

func filterToQuery(filterParam pb.FilterParam) bson.M {
	query := bson.M{}
	switch filterParam {
	case pb.FilterParam_FilterApply:
		query["type"] = pb.ModifyType_AdminHelp.String()
		query["status"] = pb.ModifyStatus_Apply.String()
	case pb.FilterParam_FilterSelfModify:
		query["type"] = pb.ModifyType_UserSelf.String()
		query["status"] = pb.ModifyStatus_Pass.String()
	case pb.FilterParam_FilterPass:
		query["type"] = pb.ModifyType_AdminHelp.String()
		query["status"] = pb.ModifyStatus_Pass.String()
	case pb.FilterParam_FilterTimeout:
		query["type"] = pb.ModifyType_AdminHelp.String()
		query["status"] = pb.ModifyStatus_Timeout.String()
	case pb.FilterParam_FilterReject:
		query["type"] = pb.ModifyType_AdminHelp.String()
		query["status"] = pb.ModifyStatus_Reject.String()
	case pb.FilterParam_FilterAdminAuth:
		query["type"] = pb.ModifyType_AdminAuth.String()
		query["status"] = pb.ModifyStatus_Pass.String()
	default:
		query["status"] = bson.M{"$ne": pb.ModifyStatus_Apply.String()}
	}
	return query
}

func resultToFilter(typ, status string) string {
	switch {
	case typ == pb.ModifyType_UserSelf.String() && status == pb.ModifyStatus_Pass.String():
		return pb.FilterParam_FilterSelfModify.String()
	case typ == pb.ModifyType_AdminHelp.String() && status == pb.ModifyStatus_Pass.String():
		return pb.FilterParam_FilterPass.String()
	case typ == pb.ModifyType_AdminHelp.String() && status == pb.ModifyStatus_Timeout.String():
		return pb.FilterParam_FilterTimeout.String()
	case typ == pb.ModifyType_AdminHelp.String() && status == pb.ModifyStatus_Reject.String():
		return pb.FilterParam_FilterReject.String()
	case typ == pb.ModifyType_AdminAuth.String() && status == pb.ModifyStatus_Pass.String():
		return pb.FilterParam_FilterAdminAuth.String()
	}

	return ""
}

func handleDateAddTime(t time.Time) string {
	if t.IsZero() {
		return ""
	}

	return t.Add(8 * time.Hour).Format(utils.TimeFormat)
}
