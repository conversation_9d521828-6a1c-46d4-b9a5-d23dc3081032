//Description 封禁查询
//Date        2022/2/18
//User        cl

package svc

import (
	"context"
	"strings"

	"golang.org/x/sync/errgroup"

	"creativematrix.com/beyondreading/app/base/user/api"
	"creativematrix.com/beyondreading/app/base/user/model"
	"creativematrix.com/beyondreading/pkg/ecode"
)

// ValidHead 校验是否封禁
// req.url /login/info /lonlat
func (s *Svc) ValidHead(ctx context.Context, req *api.ValidHeadReq) (*api.ValidHeadRsp, error) {
	var (
		err       error
		eg        errgroup.Group
		deviceSts = new(model.TDeviceStatus)
		rsp       = new(api.ValidHeadRsp)
		ipForbid  bool
		lonForbid bool
	)

	shift, err := s.dao.GetForbidSwitch(ctx)
	if err != nil {
		return nil, err
	}

	//注册时不校验
	if strings.Contains(req.Url, model.RegisterUrl) {
		if !shift.Register {
			return rsp, nil
		}
	}

	eg.Go(func() error {
		deviceSts, err = s.dao.GetDeviceStatus(ctx, req.Imei)
		return err
	})

	eg.Go(func() error {
		isWhite, _ := s.dao.GetUserWhiteList(ctx, req.NoId)
		if !isWhite {
			if shift.IP {
				//校验ip
				ipForbid, _ = s.dao.GetForbidIP(ctx, req.Ip)
			}
			if shift.LonLat {
				//校验经纬度
				lonForbid, _ = s.dao.GetForbidLonLat(ctx, req.Lon, req.Lat)
			}
		}
		return nil
	})

	if err = eg.Wait(); err != nil {
		return nil, err
	}

	if deviceSts.Status == model.DeviceForbid {
		rsp.Code = int32(ecode.ForbidDeviceErr.Code())
	}
	if ipForbid || lonForbid {
		rsp.Code = int32(ecode.ForbidIPErr.Code())
	}

	return rsp, nil
}

// ValidImei 登录前校验设备id
func (s *Svc) ValidImei(ctx context.Context, req *api.ValidHeadReq) (*api.ValidHeadRsp, error) {
	deviceSts, err := s.dao.GetDeviceStatus(ctx, req.Imei)
	if err != nil {
		return nil, err
	}
	rsp := new(api.ValidHeadRsp)
	if deviceSts.Status == model.DeviceForbid {
		rsp.Code = int32(ecode.ForbidDeviceErr.Code())
	}

	return rsp, nil
}

// ValidByIP 登录后校验ip 经纬度
func (s *Svc) ValidByIP(ctx context.Context, req *api.ValidHeadReq) (*api.ValidHeadRsp, error) {
	var (
		ipForbid bool
		err      error
		rsp      = new(api.ValidHeadRsp)
	)
	shift, err := s.dao.GetForbidSwitch(ctx)
	if err != nil {
		return nil, err
	}

	if req.Status == model.UnFinish {
		//注册校验
		if !shift.Register {
			return rsp, nil
		}
	} else {
		//登录校验
		if !shift.Login {
			return rsp, nil
		}
	}

	isWhite, _ := s.dao.GetUserWhiteList(ctx, req.NoId)
	if isWhite {
		return rsp, nil
	}

	if shift.IP {
		//校验ip
		ipForbid, _ = s.dao.GetForbidIP(ctx, req.Ip)
		if ipForbid {
			rsp.Code = int32(ecode.ForbidIPErr.Code())
		}
	}

	return rsp, nil
}
