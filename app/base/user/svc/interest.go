package svc

import (
	"context"
	pb "creativematrix.com/beyondreading/app/base/user/api"
)

func (s *Svc) GetUserInterest(ctx context.Context, req *pb.GetUserInterestReq) (*pb.GetUserInterestRsp, error) {
	rsp := &pb.GetUserInterestRsp{
		Interests: make([]*pb.UserInterestTag, 0),
	}

	adds, err := s.dao.GetInterestTagAdded(ctx, req.NoId)
	if err != nil {
		return nil, err
	}

	for _, a := range adds {
		rsp.Interests = append(rsp.Interests, &pb.UserInterestTag{
			TagId:   a.TagId,
			TagName: a.TagName,
		})
	}

	return rsp, nil
}

func (s *Svc) GetUserInterests(ctx context.Context, req *pb.GetUserInterestsReq) (*pb.GetUserInterestsRsp, error) {
	rsp := &pb.GetUserInterestsRsp{
		Ret: make(map[string]*pb.UserInterestsVO),
	}

	interests, err := s.dao.GetInterestsTagAdded(ctx, req.NoIds)
	if err != nil {
		return nil, err
	}

	for _, v := range interests {
		if rsp.Ret[v.NoId] == nil {
			rsp.Ret[v.NoId] = &pb.UserInterestsVO{
				Interests: make([]string, 0),
			}
		}
		rsp.Ret[v.NoId].Interests = append(rsp.Ret[v.NoId].Interests, v.TagName)
	}

	return rsp, nil
}
