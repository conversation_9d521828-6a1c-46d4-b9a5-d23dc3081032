package svc

import (
	"context"
	"gotest.tools/assert"
	"testing"

	pb "creativematrix.com/beyondreading/app/base/user/api"
	"creativematrix.com/beyondreading/pkg/utils"
)

var (
	fromMobile = "17611111111"
	toMobile   = "17622222222"
)

func TestSvc_ModifyUserMobile(t *testing.T) {
	ctx := context.Background()

	t.Run("self apply", func(t *testing.T) {
		err := svc.ApplyModifyMobile(ctx, &pb.ApplyModifyMobileReq{
			NoId:       noId,
			FromMobile: fromMobile,
			ToMobile:   toMobile,
			ModifyType: pb.ModifyType_UserSelf,
		})
		assert.NilError(t, err)
	})

	t.Run("apply admin help", func(t *testing.T) {
		err := svc.ApplyModifyMobile(ctx, &pb.ApplyModifyMobileReq{
			NoId:        noId,
			FromMobile:  fromMobile,
			ToMobile:    toMobile,
			ModifyType:  pb.ModifyType_AdminHelp,
			ApplyReason: "手机无法收到验证码",
			ProveImg:    []string{"1.png", "2.png"},
		})
		assert.NilError(t, err)
	})

	t.Run("pass admin help apply", func(t *testing.T) {
		err := svc.ApplyModifyMobile(ctx, &pb.ApplyModifyMobileReq{
			OperatorId:   "112233",
			OperatorName: "运营1",
			ModifyType:   pb.ModifyType_AdminHelp,
			ModifyStatus: pb.ModifyStatus_Pass,
			Id:           "63353b58e1cb0d2f48e1fda2",
		})
		assert.NilError(t, err)
	})

	t.Run("reject admin help apply", func(t *testing.T) {
		err := svc.ApplyModifyMobile(ctx, &pb.ApplyModifyMobileReq{
			OperatorId:   "112233",
			OperatorName: "运营1",
			ModifyType:   pb.ModifyType_AdminHelp,
			ModifyStatus: pb.ModifyStatus_Reject,
			Id:           "63353b58e1cb0d2f48e1fda2",
		})
		assert.NilError(t, err)
	})

	t.Run("timeout admin help apply", func(t *testing.T) {
		err := svc.ApplyModifyMobile(ctx, &pb.ApplyModifyMobileReq{
			OperatorId:   "112233",
			OperatorName: "运营1",
			ModifyType:   pb.ModifyType_AdminHelp,
			ModifyStatus: pb.ModifyStatus_Timeout,
			Id:           "63353b58e1cb0d2f48e1fda2",
		})
		assert.NilError(t, err)
	})

	t.Run("admin auth modify", func(t *testing.T) {
		err := svc.ApplyModifyMobile(ctx, &pb.ApplyModifyMobileReq{
			NoId:         noId,
			FromMobile:   fromMobile,
			ToMobile:     toMobile,
			OperatorId:   "11111",
			OperatorName: "运营1",
			ModifyType:   pb.ModifyType_AdminAuth,
			ApplyReason:  "客服确认修改",
		})
		assert.NilError(t, err)
	})

	t.Run("", func(t *testing.T) {
		data, err := svc.GetModifyMobileList(ctx, &pb.GetModifyMobileListReq{
			NoId:      "",
			StartTime: "",
			EndTime:   "",
			//FilterParam: pb.FilterParam_FilterApply,
			Page:     1,
			PageSize: 10,
		})
		assert.NilError(t, err)

		for _, d := range data.List {
			t.Log(string(utils.JsonByte(d)))
		}
	})
}
