//Description   审核相关信息
//Time          2022/9/20
//User          cl

package svc

import (
	"context"
	"creativematrix.com/beyondreading/app/base/user/api"
	"creativematrix.com/beyondreading/app/base/user/model"
	"creativematrix.com/beyondreading/pkg/check/shumei"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/utils"
	"time"
)

func (s *Svc) ReviewInfo(ctx context.Context, req *api.ReviewInfoReq) (*api.ReviewInfoRsp, error) {
	users, err := s.dao.GetUsersByIds(ctx, &api.UserIdsReq{Ids: []string{req.NoId}, Cols: []string{"noId", "nickName", "msgId"}})
	if err != nil || len(users) == 0 {
		return nil, model.ServerErr
	}

	//Success,Processing,Reject
	status, err := s.reviewText(ctx, req.ReqText, req.RawText, req.RuleType, model.Event(req.Event), users[0])
	if err != nil {
		return nil, err
	}

	return &api.ReviewInfoRsp{Status: status}, nil
}

func (s *Svc) reviewText(ctx context.Context, reqText, rawText, ruleType string, event model.Event, user *model.TUserInfo) (string, error) {
	rule, e := s.dao.GetReviewRule(ctx, ruleType)
	if e != nil {
		return "", model.ServerErr
	}

	var (
		mode     string
		priority model.Priority
		smRsp    *shumei.MsgResponse
	)
	switch rule.CheckMode & model.RuleAll {
	case model.RuleForbid:
		return "", model.ReviewForbidErr
	case model.RuleManual:
		mode = model.Process
		priority = model.HighPriority
	case model.RuleSM, model.RuleAll:
		smRsp, e = s.reviewMsg(ctx, reqText, user.MsgId)
		if e != nil {
			return "", model.ServerErr
		}
		priority, mode = getPassMode(rule, smRsp.RiskLevel)
		mode = getLocStatus(mode)
	}

	if mode == model.Reject {
		return mode, model.ReviewTextErr
	}

	reviewNotice := getCheckNotice(user, reqText, rawText, mode, priority, smRsp)
	e = s.dao.CheckUserMedia(event, utils.JsonByte(reviewNotice))
	if e != nil {
		return "", e
	}

	return mode, nil
}

func (s *Svc) reviewMsg(ctx context.Context, msg, msgId string) (*shumei.MsgResponse, error) {
	msgRsp := new(shumei.MsgResponse)
	err := s.dao.Msg(ctx, shumei.MsgRequest{Data: &shumei.MsgData{
		Text:    msg,
		TokenId: msgId,
	}}, msgRsp)

	if err != nil {
		return nil, model.ServerErr
	}

	return msgRsp, nil
}

func getPassMode(rule *model.TReviewRule, riskLevel string) (model.Priority, string) {
	switch riskLevel {
	case model.SMPass:
		return rule.Pass, rule.PassMode
	case model.SMReview:
		return rule.Review, rule.ReviewMode
	case model.SMReject:
		return rule.Reject, rule.RejectMode
	default:
		logger.LogErrorf("invalid riskLevel:%s", riskLevel)
		return model.NonePriority, model.SMReject
	}
}

func getLocStatus(ruleMode string) string {
	switch ruleMode {
	case model.SMPass:
		return model.Success
	case model.SMReview:
		//后台人审
		return model.Process
	case model.SMReject:
		return model.Reject
	}

	return model.Reject
}

func getCheckNotice(user *model.TUserInfo, reqNotice, notice, mode string, priority model.Priority, smRsp *shumei.MsgResponse) *model.TReviewNotice {
	data := &model.TReviewNotice{
		NoId:       user.NoId,
		NickName:   user.NickName,
		Notice:     reqNotice,
		OldNotice:  notice,
		Status:     mode,
		ReqTime:    time.Now().Unix(),
		UpdateTime: time.Now().Unix(),
		Priority:   priority,
	}

	if smRsp != nil {
		data.SMResult = smRsp.RiskLevel
		data.SMReason = smRsp.RiskDescription
	}

	return data
}
