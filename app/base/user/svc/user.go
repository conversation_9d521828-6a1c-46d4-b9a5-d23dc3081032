package svc

import (
	"context"
	"fmt"
	"time"

	"creativematrix.com/beyondreading/app/base/user/model"
	"creativematrix.com/beyondreading/app/common/po"
	"creativematrix.com/beyondreading/pkg/logger"
	pb "creativematrix.com/beyondreading/proto/user"
)

// RegisterBySms 手机短信注册
func (s *UserSvc) RegisterBySms(ctx context.Context, req *pb.RegisterBySmsReq) (*pb.RegisterBySmsResp, error) {
	// 验证短信验证码
	isValid, err := s.dao.VerifySmsCode(ctx, req.Phone, req.SmsCode, model.SmsCodeTypeRegister)
	if err != nil {
		logger.LogErrorf("Failed to verify sms code: %v", err)
		return &pb.RegisterBySmsResp{
			Code:    500,
			Message: "Failed to verify sms code",
		}, nil
	}

	if !isValid {
		return &pb.RegisterBySmsResp{
			Code:    400,
			Message: model.ErrSmsCodeInvalid,
		}, nil
	}

	// 检查用户是否已存在
	existingUser, err := s.dao.GetUserByPhone(ctx, req.Phone)
	if err != nil {
		logger.LogErrorf("Failed to check existing user: %v", err)
		return &pb.RegisterBySmsResp{
			Code:    500,
			Message: "Failed to check existing user",
		}, nil
	}

	if existingUser != nil {
		return &pb.RegisterBySmsResp{
			Code:    400,
			Message: model.ErrUserAlreadyExists,
		}, nil
	}

	// 创建用户
	user := &po.User{
		Phone:       req.Phone,
		Nickname:    req.Nickname,
		Status:      model.UserStatusNormal,
		LoginType:   model.LoginTypeSms,
		LastLoginIp: req.ClientIp,
	}

	if user.Nickname == "" {
		user.Nickname = fmt.Sprintf("User_%s", req.Phone[len(req.Phone)-4:])
	}

	now := time.Now()
	user.LastLoginAt = &now

	createdUser, err := s.dao.CreateUser(ctx, user)
	if err != nil {
		logger.LogErrorf("Failed to create user: %v", err)
		return &pb.RegisterBySmsResp{
			Code:    500,
			Message: "Failed to create user",
		}, nil
	}

	// 生成JWT token
	token, err := s.jwtService.GenerateToken(createdUser)
	if err != nil {
		logger.LogErrorf("Failed to generate token: %v", err)
		return &pb.RegisterBySmsResp{
			Code:    500,
			Message: "Failed to generate token",
		}, nil
	}

	// 记录登录日志
	loginLog := &po.LoginLog{
		UserId:      createdUser.UserId,
		LoginType:   model.LoginTypeSms,
		LoginIp:     req.ClientIp,
		UserAgent:   req.UserAgent,
		DeviceId:    req.DeviceId,
		LoginResult: model.LoginResultSuccess,
	}

	err = s.dao.CreateLoginLog(ctx, loginLog)
	if err != nil {
		logger.LogErrorf("Failed to create login log: %v", err)
	}

	return &pb.RegisterBySmsResp{
		Code:    200,
		Message: "Registration successful",
		User:    s.convertUserToPB(createdUser),
		Token:   token,
	}, nil
}

// RegisterByGoogle Google账户注册
func (s *UserSvc) RegisterByGoogle(ctx context.Context, req *pb.RegisterByGoogleReq) (*pb.RegisterByGoogleResp, error) {
	// 验证Google token
	tokenInfo, err := s.googleService.VerifyToken(ctx, req.GoogleToken)
	if err != nil {
		logger.LogErrorf("Failed to verify google token: %v", err)
		return &pb.RegisterByGoogleResp{
			Code:    400,
			Message: model.ErrGoogleTokenInvalid,
		}, nil
	}

	// 检查用户是否已存在
	existingUser, err := s.dao.GetUserByGoogleId(ctx, req.GoogleId)
	if err != nil {
		logger.LogErrorf("Failed to check existing user: %v", err)
		return &pb.RegisterByGoogleResp{
			Code:    500,
			Message: "Failed to check existing user",
		}, nil
	}

	if existingUser != nil {
		return &pb.RegisterByGoogleResp{
			Code:    400,
			Message: model.ErrUserAlreadyExists,
		}, nil
	}

	// 创建用户
	user := &po.User{
		Email:       req.Email,
		Nickname:    req.Nickname,
		Avatar:      req.Avatar,
		Status:      model.UserStatusNormal,
		LoginType:   model.LoginTypeGoogle,
		GoogleId:    req.GoogleId,
		LastLoginIp: req.ClientIp,
	}

	if user.Email == "" {
		user.Email = tokenInfo.Email
	}
	if user.Nickname == "" {
		user.Nickname = tokenInfo.Name
	}
	if user.Avatar == "" {
		user.Avatar = tokenInfo.Picture
	}

	now := time.Now()
	user.LastLoginAt = &now

	createdUser, err := s.dao.CreateUser(ctx, user)
	if err != nil {
		logger.LogErrorf("Failed to create user: %v", err)
		return &pb.RegisterByGoogleResp{
			Code:    500,
			Message: "Failed to create user",
		}, nil
	}

	// 生成JWT token
	token, err := s.jwtService.GenerateToken(createdUser)
	if err != nil {
		logger.LogErrorf("Failed to generate token: %v", err)
		return &pb.RegisterByGoogleResp{
			Code:    500,
			Message: "Failed to generate token",
		}, nil
	}

	// 记录登录日志
	loginLog := &po.LoginLog{
		UserId:      createdUser.UserId,
		LoginType:   model.LoginTypeGoogle,
		LoginIp:     req.ClientIp,
		UserAgent:   req.UserAgent,
		DeviceId:    req.DeviceId,
		LoginResult: model.LoginResultSuccess,
	}

	err = s.dao.CreateLoginLog(ctx, loginLog)
	if err != nil {
		logger.LogErrorf("Failed to create login log: %v", err)
	}

	return &pb.RegisterByGoogleResp{
		Code:    200,
		Message: "Registration successful",
		User:    s.convertUserToPB(createdUser),
		Token:   token,
	}, nil
}

// RegisterByApple Apple账户注册
func (s *UserSvc) RegisterByApple(ctx context.Context, req *pb.RegisterByAppleReq) (*pb.RegisterByAppleResp, error) {
	// 验证Apple token
	tokenClaims, err := s.appleService.VerifyToken(ctx, req.AppleToken)
	if err != nil {
		logger.LogErrorf("Failed to verify apple token: %v", err)
		return &pb.RegisterByAppleResp{
			Code:    400,
			Message: model.ErrAppleTokenInvalid,
		}, nil
	}

	// 检查用户是否已存在
	existingUser, err := s.dao.GetUserByAppleId(ctx, req.AppleId)
	if err != nil {
		logger.LogErrorf("Failed to check existing user: %v", err)
		return &pb.RegisterByAppleResp{
			Code:    500,
			Message: "Failed to check existing user",
		}, nil
	}

	if existingUser != nil {
		return &pb.RegisterByAppleResp{
			Code:    400,
			Message: model.ErrUserAlreadyExists,
		}, nil
	}

	// 创建用户
	user := &po.User{
		Email:       req.Email,
		Nickname:    req.Nickname,
		Status:      model.UserStatusNormal,
		LoginType:   model.LoginTypeApple,
		AppleId:     req.AppleId,
		LastLoginIp: req.ClientIp,
	}

	if user.Email == "" {
		user.Email = tokenClaims.Email
	}
	if user.Nickname == "" {
		user.Nickname = fmt.Sprintf("Apple_User_%s", req.AppleId[len(req.AppleId)-6:])
	}

	now := time.Now()
	user.LastLoginAt = &now

	createdUser, err := s.dao.CreateUser(ctx, user)
	if err != nil {
		logger.LogErrorf("Failed to create user: %v", err)
		return &pb.RegisterByAppleResp{
			Code:    500,
			Message: "Failed to create user",
		}, nil
	}

	// 生成JWT token
	token, err := s.jwtService.GenerateToken(createdUser)
	if err != nil {
		logger.LogErrorf("Failed to generate token: %v", err)
		return &pb.RegisterByAppleResp{
			Code:    500,
			Message: "Failed to generate token",
		}, nil
	}

	// 记录登录日志
	loginLog := &po.LoginLog{
		UserId:      createdUser.UserId,
		LoginType:   model.LoginTypeApple,
		LoginIp:     req.ClientIp,
		UserAgent:   req.UserAgent,
		DeviceId:    req.DeviceId,
		LoginResult: model.LoginResultSuccess,
	}

	err = s.dao.CreateLoginLog(ctx, loginLog)
	if err != nil {
		logger.LogErrorf("Failed to create login log: %v", err)
	}

	return &pb.RegisterByAppleResp{
		Code:    200,
		Message: "Registration successful",
		User:    s.convertUserToPB(createdUser),
		Token:   token,
	}, nil
}

// LoginBySms 手机短信登录
func (s *UserSvc) LoginBySms(ctx context.Context, req *pb.LoginBySmsReq) (*pb.LoginBySmsResp, error) {
	// 验证短信验证码
	isValid, err := s.dao.VerifySmsCode(ctx, req.Phone, req.SmsCode, model.SmsCodeTypeLogin)
	if err != nil {
		logger.LogErrorf("Failed to verify sms code: %v", err)
		return &pb.LoginBySmsResp{
			Code:    500,
			Message: "Failed to verify sms code",
		}, nil
	}

	if !isValid {
		return &pb.LoginBySmsResp{
			Code:    400,
			Message: model.ErrSmsCodeInvalid,
		}, nil
	}

	// 获取用户信息
	user, err := s.dao.GetUserByPhone(ctx, req.Phone)
	if err != nil {
		logger.LogErrorf("Failed to get user: %v", err)
		return &pb.LoginBySmsResp{
			Code:    500,
			Message: "Failed to get user",
		}, nil
	}

	if user == nil {
		return &pb.LoginBySmsResp{
			Code:    404,
			Message: model.ErrUserNotFound,
		}, nil
	}

	// 检查用户状态
	if user.Status == model.UserStatusFrozen {
		return &pb.LoginBySmsResp{
			Code:    403,
			Message: model.ErrUserFrozen,
		}, nil
	}

	if user.Status == model.UserStatusDeleted {
		return &pb.LoginBySmsResp{
			Code:    403,
			Message: model.ErrUserDeleted,
		}, nil
	}

	// 更新登录信息
	err = s.dao.UpdateUserLoginInfo(ctx, user.UserId, req.ClientIp)
	if err != nil {
		logger.LogErrorf("Failed to update user login info: %v", err)
	}

	// 生成JWT token
	token, err := s.jwtService.GenerateToken(user)
	if err != nil {
		logger.LogErrorf("Failed to generate token: %v", err)
		return &pb.LoginBySmsResp{
			Code:    500,
			Message: "Failed to generate token",
		}, nil
	}

	// 记录登录日志
	loginLog := &po.LoginLog{
		UserId:      user.UserId,
		LoginType:   model.LoginTypeSms,
		LoginIp:     req.ClientIp,
		UserAgent:   req.UserAgent,
		DeviceId:    req.DeviceId,
		LoginResult: model.LoginResultSuccess,
	}

	err = s.dao.CreateLoginLog(ctx, loginLog)
	if err != nil {
		logger.LogErrorf("Failed to create login log: %v", err)
	}

	return &pb.LoginBySmsResp{
		Code:    200,
		Message: "Login successful",
		User:    s.convertUserToPB(user),
		Token:   token,
	}, nil
}

// LoginByGoogle Google账户登录
func (s *UserSvc) LoginByGoogle(ctx context.Context, req *pb.LoginByGoogleReq) (*pb.LoginByGoogleResp, error) {
	// 验证Google token
	_, err := s.googleService.VerifyToken(ctx, req.GoogleToken)
	if err != nil {
		logger.LogErrorf("Failed to verify google token: %v", err)
		return &pb.LoginByGoogleResp{
			Code:    400,
			Message: model.ErrGoogleTokenInvalid,
		}, nil
	}

	// 获取用户信息
	user, err := s.dao.GetUserByGoogleId(ctx, req.GoogleId)
	if err != nil {
		logger.LogErrorf("Failed to get user: %v", err)
		return &pb.LoginByGoogleResp{
			Code:    500,
			Message: "Failed to get user",
		}, nil
	}

	if user == nil {
		return &pb.LoginByGoogleResp{
			Code:    404,
			Message: model.ErrUserNotFound,
		}, nil
	}

	// 检查用户状态
	if user.Status == model.UserStatusFrozen {
		return &pb.LoginByGoogleResp{
			Code:    403,
			Message: model.ErrUserFrozen,
		}, nil
	}

	if user.Status == model.UserStatusDeleted {
		return &pb.LoginByGoogleResp{
			Code:    403,
			Message: model.ErrUserDeleted,
		}, nil
	}

	// 更新登录信息
	err = s.dao.UpdateUserLoginInfo(ctx, user.UserId, req.ClientIp)
	if err != nil {
		logger.LogErrorf("Failed to update user login info: %v", err)
	}

	// 生成JWT token
	token, err := s.jwtService.GenerateToken(user)
	if err != nil {
		logger.LogErrorf("Failed to generate token: %v", err)
		return &pb.LoginByGoogleResp{
			Code:    500,
			Message: "Failed to generate token",
		}, nil
	}

	// 记录登录日志
	loginLog := &po.LoginLog{
		UserId:      user.UserId,
		LoginType:   model.LoginTypeGoogle,
		LoginIp:     req.ClientIp,
		UserAgent:   req.UserAgent,
		DeviceId:    req.DeviceId,
		LoginResult: model.LoginResultSuccess,
	}

	err = s.dao.CreateLoginLog(ctx, loginLog)
	if err != nil {
		logger.LogErrorf("Failed to create login log: %v", err)
	}

	return &pb.LoginByGoogleResp{
		Code:    200,
		Message: "Login successful",
		User:    s.convertUserToPB(user),
		Token:   token,
	}, nil
}

// LoginByApple Apple账户登录
func (s *UserSvc) LoginByApple(ctx context.Context, req *pb.LoginByAppleReq) (*pb.LoginByAppleResp, error) {
	// 验证Apple token
	_, err := s.appleService.VerifyToken(ctx, req.AppleToken)
	if err != nil {
		logger.LogErrorf("Failed to verify apple token: %v", err)
		return &pb.LoginByAppleResp{
			Code:    400,
			Message: model.ErrAppleTokenInvalid,
		}, nil
	}

	// 获取用户信息
	user, err := s.dao.GetUserByAppleId(ctx, req.AppleId)
	if err != nil {
		logger.LogErrorf("Failed to get user: %v", err)
		return &pb.LoginByAppleResp{
			Code:    500,
			Message: "Failed to get user",
		}, nil
	}

	if user == nil {
		return &pb.LoginByAppleResp{
			Code:    404,
			Message: model.ErrUserNotFound,
		}, nil
	}

	// 检查用户状态
	if user.Status == model.UserStatusFrozen {
		return &pb.LoginByAppleResp{
			Code:    403,
			Message: model.ErrUserFrozen,
		}, nil
	}

	if user.Status == model.UserStatusDeleted {
		return &pb.LoginByAppleResp{
			Code:    403,
			Message: model.ErrUserDeleted,
		}, nil
	}

	// 更新登录信息
	err = s.dao.UpdateUserLoginInfo(ctx, user.UserId, req.ClientIp)
	if err != nil {
		logger.LogErrorf("Failed to update user login info: %v", err)
	}

	// 生成JWT token
	token, err := s.jwtService.GenerateToken(user)
	if err != nil {
		logger.LogErrorf("Failed to generate token: %v", err)
		return &pb.LoginByAppleResp{
			Code:    500,
			Message: "Failed to generate token",
		}, nil
	}

	// 记录登录日志
	loginLog := &po.LoginLog{
		UserId:      user.UserId,
		LoginType:   model.LoginTypeApple,
		LoginIp:     req.ClientIp,
		UserAgent:   req.UserAgent,
		DeviceId:    req.DeviceId,
		LoginResult: model.LoginResultSuccess,
	}

	err = s.dao.CreateLoginLog(ctx, loginLog)
	if err != nil {
		logger.LogErrorf("Failed to create login log: %v", err)
	}

	return &pb.LoginByAppleResp{
		Code:    200,
		Message: "Login successful",
		User:    s.convertUserToPB(user),
		Token:   token,
	}, nil
}

// GetUserInfo 获取用户信息
func (s *UserSvc) GetUserInfo(ctx context.Context, req *pb.GetUserInfoReq) (*pb.GetUserInfoResp, error) {
	user, err := s.dao.GetUserById(ctx, req.UserId)
	if err != nil {
		logger.LogErrorf("Failed to get user: %v", err)
		return &pb.GetUserInfoResp{
			Code:    500,
			Message: "Failed to get user",
		}, nil
	}

	if user == nil {
		return &pb.GetUserInfoResp{
			Code:    404,
			Message: model.ErrUserNotFound,
		}, nil
	}

	return &pb.GetUserInfoResp{
		Code:    200,
		Message: "Success",
		User:    s.convertUserToPB(user),
	}, nil
}

// UpdateUserInfo 更新用户信息
func (s *UserSvc) UpdateUserInfo(ctx context.Context, req *pb.UpdateUserInfoReq) (*pb.UpdateUserInfoResp, error) {
	// 先获取原用户信息
	user, err := s.dao.GetUserById(ctx, req.UserId)
	if err != nil {
		logger.LogErrorf("Failed to get user: %v", err)
		return &pb.UpdateUserInfoResp{
			Code:    500,
			Message: "Failed to get user",
		}, nil
	}

	if user == nil {
		return &pb.UpdateUserInfoResp{
			Code:    404,
			Message: model.ErrUserNotFound,
		}, nil
	}

	// 更新字段
	if req.Nickname != "" {
		user.Nickname = req.Nickname
	}
	if req.Avatar != "" {
		user.Avatar = req.Avatar
	}
	if req.Gender > 0 {
		user.Gender = req.Gender
	}
	if req.Birthday != "" {
		user.Birthday = req.Birthday
	}
	if req.Location != "" {
		user.Location = req.Location
	}

	// 更新到数据库
	updatedUser, err := s.dao.UpdateUser(ctx, user)
	if err != nil {
		logger.LogErrorf("Failed to update user: %v", err)
		return &pb.UpdateUserInfoResp{
			Code:    500,
			Message: "Failed to update user",
		}, nil
	}

	return &pb.UpdateUserInfoResp{
		Code:    200,
		Message: "Success",
		User:    s.convertUserToPB(updatedUser),
	}, nil
}

// GetLoginLogs 获取登录日志
func (s *UserSvc) GetLoginLogs(ctx context.Context, req *pb.GetLoginLogsReq) (*pb.GetLoginLogsResp, error) {
	// 设置默认分页参数
	page := req.Page
	pageSize := req.PageSize
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	logs, total, err := s.dao.GetLoginLogs(ctx, req.UserId, page, pageSize)
	if err != nil {
		logger.LogErrorf("Failed to get login logs: %v", err)
		return &pb.GetLoginLogsResp{
			Code:    500,
			Message: "Failed to get login logs",
		}, nil
	}

	var pbLogs []*pb.LoginLog
	for _, log := range logs {
		pbLogs = append(pbLogs, s.convertLoginLogToPB(log))
	}

	return &pb.GetLoginLogsResp{
		Code:    200,
		Message: "Success",
		Logs:    pbLogs,
		Total:   total,
	}, nil
}

// SendSmsCode 发送短信验证码
func (s *UserSvc) SendSmsCode(ctx context.Context, req *pb.SendSmsCodeReq) (*pb.SendSmsCodeResp, error) {
	// 检查发送频率限制
	count, err := s.dao.GetSmsCodeSendCount(ctx, req.Phone, time.Now().Add(-time.Hour))
	if err != nil {
		logger.LogErrorf("Failed to get sms send count: %v", err)
		return &pb.SendSmsCodeResp{
			Code:    500,
			Message: "Failed to check send limit",
		}, nil
	}

	if count >= model.SmsMaxSendPerHour {
		return &pb.SendSmsCodeResp{
			Code:    429,
			Message: model.ErrTooManyRequests,
		}, nil
	}

	// 生成验证码
	code := s.smsService.GenerateSmsCode()

	// 发送短信
	err = s.smsService.SendSmsCode(ctx, req.Phone, code, req.CodeType)
	if err != nil {
		logger.LogErrorf("Failed to send sms: %v", err)
		return &pb.SendSmsCodeResp{
			Code:    500,
			Message: "Failed to send sms",
		}, nil
	}

	// 保存验证码到数据库
	smsCode := &po.SmsCode{
		Phone:    req.Phone,
		Code:     code,
		CodeType: req.CodeType,
	}

	err = s.dao.CreateSmsCode(ctx, smsCode)
	if err != nil {
		logger.LogErrorf("Failed to save sms code: %v", err)
		return &pb.SendSmsCodeResp{
			Code:    500,
			Message: "Failed to save sms code",
		}, nil
	}

	return &pb.SendSmsCodeResp{
		Code:    200,
		Message: "SMS code sent successfully",
	}, nil
}

// VerifySmsCode 验证短信验证码
func (s *UserSvc) VerifySmsCode(ctx context.Context, req *pb.VerifySmsCodeReq) (*pb.VerifySmsCodeResp, error) {
	isValid, err := s.dao.VerifySmsCode(ctx, req.Phone, req.SmsCode, req.CodeType)
	if err != nil {
		logger.LogErrorf("Failed to verify sms code: %v", err)
		return &pb.VerifySmsCodeResp{
			Code:    500,
			Message: "Failed to verify sms code",
		}, nil
	}

	if !isValid {
		return &pb.VerifySmsCodeResp{
			Code:    400,
			Message: model.ErrSmsCodeInvalid,
			IsValid: false,
		}, nil
	}

	return &pb.VerifySmsCodeResp{
		Code:    200,
		Message: "SMS code is valid",
		IsValid: true,
	}, nil
}
