package svc

import (
	"context"
	"encoding/json"
	"fmt"
	mrand "math/rand"
	"time"

	"creativematrix.com/beyondreading/app/admin/zt/model"
	pb "creativematrix.com/beyondreading/app/base/user/api"
	um "creativematrix.com/beyondreading/app/base/user/model"
	"creativematrix.com/beyondreading/pkg/ecode"
	"creativematrix.com/beyondreading/pkg/encrypt"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/utils"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func (s *Svc) GetUsersByIds(ctx context.Context, req *pb.UserIdsReq) (*pb.UserInfoRsp, error) {
	if len(req.Ids) == 0 {
		return &pb.UserInfoRsp{}, nil
	}

	rsps, err := s.dao.GetUsersByIds(ctx, req)
	if err != nil {
		logger.LogErrorw("Base User GetUsersByIds", "error", err, "ids", req.Ids)
		return nil, err
	}

	userInfos := make([]*pb.UserInfo, 0)
	for _, v := range rsps {
		userInfo := new(pb.UserInfo)
		err = utils.JsonCopy(v, userInfo)
		if err != nil {
			logger.LogErrorf("GetUsersByIds JsonCopy", "error", err, "rsps", rsps)
			return nil, err
		}
		if userInfo.RegisterStatus == um.UserLogoff {
			userInfo.AvatarUrl = um.DefaultMale
			userInfo.NickName = um.DefaultName
			userInfo.CheckAvatar = userInfo.AvatarUrl
		}
		userInfos = append(userInfos, userInfo)
	}

	return &pb.UserInfoRsp{Users: userInfos}, nil
}

func (s *Svc) GetUsersByMongoIds(ctx context.Context, req *pb.MongoIdsReq) (*pb.UserInfoRsp, error) {
	rsps, err := s.dao.GetUsersByMongoIds(ctx, req)
	if err != nil {
		logger.LogErrorw("Base User GetUsersByMongoIds", "error", err, "ids", req.Ids)
		return nil, err
	}

	userInfos := make([]*pb.UserInfo, 0)
	for _, v := range rsps {
		userInfo := new(pb.UserInfo)
		err = utils.JsonCopy(v, userInfo)
		if err != nil {
			logger.LogErrorf("GetUsersByMongoIds JsonCopy", "error", err, "rsps", rsps)
			return nil, err
		}
		userInfos = append(userInfos, userInfo)
	}

	return &pb.UserInfoRsp{Users: userInfos}, nil
}

func (s *Svc) GetUsersByMsgIds(ctx context.Context, req *pb.MsgIdsReq) (*pb.UserInfoRsp, error) {
	rsps, err := s.dao.GetUsersByMsgIds(ctx, req)
	if err != nil {
		logger.LogErrorw("Base User GetUsersByMsgIds", "error", err, "ids", req.Ids)
		return nil, err
	}

	userInfos := make([]*pb.UserInfo, 0)
	for _, v := range rsps {
		userInfo := new(pb.UserInfo)
		err = utils.JsonCopy(v, userInfo)
		if err != nil {
			logger.LogErrorf("GetUsersByMsgIds JsonCopy", "error", err, "rsps", rsps)
			return nil, err
		}
		userInfos = append(userInfos, userInfo)
	}

	return &pb.UserInfoRsp{Users: userInfos}, nil
}

// UpdateUserInfo 更新用户信息
func (s *Svc) UpdateUserInfo(ctx context.Context, req *pb.UpdateUserReq) (*pb.UserInfo, error) {
	param := make(bson.M)
	err := json.Unmarshal(req.Data, &param)
	if err != nil {
		return nil, err
	}

	info, err := s.dao.UpdateUserInfo(ctx, req.Id, param)
	if err != nil {
		return nil, err
	}

	userInfo := new(pb.UserInfo)
	err = utils.JsonCopy(info, userInfo)
	if err != nil {
		logger.LogErrorf("UpdateUserInfo JsonCopy", "error", err)
		return nil, err
	}

	return userInfo, nil
}

// IsUserExistByNoId 更新用户信息
func (s *Svc) IsUserExistByNoId(ctx context.Context, req *pb.UserExistReq) (*pb.UserExistRsp, error) {
	isExist, err := s.dao.IsUserExistByNoId(ctx, req.NoId)
	return &pb.UserExistRsp{Exist: isExist}, err
}

func (s *Svc) PullUser(ctx context.Context, req *pb.PullUserReq) (*pb.PullUserRsp, error) {
	var query bson.M

	err := json.Unmarshal(req.Query, &query)
	if err != nil {
		logger.LogErrorw("PullUser Unmarshal", "error", err)
		return nil, err
	}

	users, err := s.dao.PullUser(ctx, query, int(req.Limit), int(req.IsRand), req.Cols...)
	if err != nil {
		logger.LogErrorw("PullUser", "error", err)
		return nil, err
	}

	userInfos := make([]*pb.UserInfo, 0)
	for _, v := range users {
		var userInfo *pb.UserInfo
		err = utils.JsonCopy(&v, &userInfo)
		if err != nil {
			logger.LogErrorf("GetUsersByIds JsonCopy", "error", err, "userInfo", v)
			return nil, err
		}
		userInfos = append(userInfos, userInfo)
	}
	return &pb.PullUserRsp{
		Users: userInfos,
	}, nil
}

func (s *Svc) GetUserRemark(ctx context.Context, req *pb.UserRemarkReq) (*pb.UserRemarkRsp, error) {
	remark, err := s.dao.GetUserRemark(ctx, req.NoId, req.Ids)

	if err != nil {
		return nil, err
	}

	return &pb.UserRemarkRsp{UserRemark: remark}, nil
}

func (s *Svc) GetOnline(ctx context.Context, req *pb.GetOnlineRsq) (*pb.GetOnlineRsp, error) {
	var (
		err   error
		users []string
	)

	if req.Gender == "female" {
		users, err = s.dao.GetOnlineFemale(ctx)

	} else if req.Gender == "male" {
		users, err = s.dao.GetOnlineMale(ctx)

	} else {
		return nil, ecode.ErrorArgument
	}

	if err != nil {
		return nil, err
	}

	return &pb.GetOnlineRsp{Users: users}, nil
}

func (s *Svc) UpdateAlbumStatus(ctx context.Context, req *pb.UpdateAlbumReq) (*pb.UpdateAlbumRsp, error) {
	id, _ := primitive.ObjectIDFromHex(req.UserId)

	err := s.dao.UpdateAlbumStatus(ctx, id, req.Album)
	if err != nil {
		return nil, err
	}

	return &pb.UpdateAlbumRsp{Status: true}, nil
}

func (s *Svc) SetOnline(ctx context.Context, req *pb.SetOnlineUserInfoReq) (*pb.SetOnlineUserInfoReqRsp, error) {
	var err error

	if req.Gender == "female" {
		err = s.dao.GetOnlineFemaleSet(ctx, req.NoId, req.User)

	} else if req.Gender == "male" {
		err = s.dao.GetOnlineMaleSet(ctx, req.NoId, req.User)

	} else {
		return nil, ecode.ErrorArgument
	}

	if err != nil {
		return nil, err
	}

	return &pb.SetOnlineUserInfoReqRsp{Status: true}, nil
}

func (s *Svc) RobotAdd(ctx context.Context, req *pb.RobotAddReq) (rsp *pb.RobotAddRsp, err error) {
	if req.Mode == 0 {
		return s.robotOneAdd(ctx, req)
	} else {
		return s.robotManyAdd(ctx, req)
	}
}

func (s *Svc) robotOneAdd(ctx context.Context, req *pb.RobotAddReq) (rsp *pb.RobotAddRsp, err error) {
	var (
		noId string
	)

	for {
		noId, err = s.dao.GetNoId(ctx)
		if err != nil {
			return nil, err
		}

		if utils.SkipNum[noId] == "" {
			break
		}
	}

	msgId := encrypt.EecryptRC4([]byte(s.conf.MsgKey.Key), []byte(noId))
	if msgId == "" {
		return nil, err
	}

	user := model.TUserInfo{
		NoId:         noId,
		MsgId:        msgId,
		NickName:     req.Robot.NickName,
		Gender:       req.Robot.Gender,
		Born:         req.Robot.Born,
		City:         req.Robot.City,
		StarSign:     req.Robot.StarSign,
		Height:       int(req.Robot.Height),
		Weight:       int(req.Robot.Weight),
		AvatarUrl:    req.Robot.AvatarUrl,
		AvatarStatus: "Pass",
		Status:       1,
		Robot:        "robot",
	}

	if req.Robot.WealthLevel > 0 {
		user.Wealth = utils.RandWealth(utils.WealthExp[req.Robot.WealthLevel-1], utils.WealthExp[req.Robot.WealthLevel])
	}

	err = s.dao.RobotAdd(ctx, []interface{}{user})
	if err != nil {
		return nil, err
	}

	return &pb.RobotAddRsp{
		Status:  true,
		Success: 1,
		RobotInfos: []*pb.RobotInfo{
			{
				MsgId: user.MsgId,
				NoId:  user.NoId,
			},
		},
	}, nil
}

func (s *Svc) robotManyAdd(ctx context.Context, req *pb.RobotAddReq) (rsp *pb.RobotAddRsp, err error) {
	rsp = &pb.RobotAddRsp{
		RobotInfos: []*pb.RobotInfo{},
	}

	users := make([]*model.TUserInfo, 0)

	for i := 0; i < int(req.RobotConfig.Male); i++ {

		loginName, err := s.LoginName(ctx)
		if err != nil {
			return nil, err
		}

		users = append(users, &model.TUserInfo{
			NickName:     loginName,
			Gender:       "male",
			Born:         utils.RandBorn(),
			City:         utils.RandCity(),
			Wealth:       utils.RandWealth(int64(req.RobotConfig.WealthMin), int64(req.RobotConfig.WealthMax)),
			StarSign:     utils.RandStarSign(),
			Height:       utils.RandHeight("male"),
			Weight:       utils.RandWeight("male"),
			AvatarStatus: "Pass",
			Status:       1,
			Robot:        "robot",
		})

	}

	insertRobots := make([]interface{}, 0)

	for i := 0; i < int(req.RobotConfig.Female); i++ {
		loginName, err := s.LoginName(ctx)
		if err != nil {
			return nil, err
		}

		users = append(users, &model.TUserInfo{
			NickName:     loginName,
			Gender:       "female",
			Born:         utils.RandBorn(),
			City:         utils.RandCity(),
			Wealth:       utils.RandWealth(int64(req.RobotConfig.WealthMin), int64(req.RobotConfig.WealthMax)),
			StarSign:     utils.RandStarSign(),
			Height:       utils.RandHeight("female"),
			Weight:       utils.RandWeight("female"),
			AvatarStatus: "Pass",
			Status:       1,
			Robot:        "robot",
		})
	}

	for _, v := range users {
		var noId string
		for {
			noId, err = s.dao.GetNoId(ctx)
			if err != nil {
				return nil, err
			}

			if utils.SkipNum[noId] == "" {
				break
			}
		}

		msgId := encrypt.EecryptRC4([]byte(s.conf.MsgKey.Key), []byte(noId))
		if msgId == "" {
			logger.LogErrorw("InsertUser EecryptRC4", "error", err, "noId", noId)
			return nil, ecode.ServerErr
		}

		v.NoId = noId
		v.MsgId = msgId

		v.AvatarUrl = s.randomImgUrl(v.Gender)

		insertRobots = append(insertRobots, v)
		rsp.RobotInfos = append(rsp.RobotInfos, &pb.RobotInfo{
			MsgId: v.MsgId,
			NoId:  v.NoId,
		})
		rsp.Success += 1
	}

	err = s.dao.RobotAdd(ctx, insertRobots)
	if err != nil {
		return nil, err
	}

	rsp.Status = true

	return rsp, err
}

func (s *Svc) randomImgUrl(gender string) string {
	mrand.Seed(time.Now().UnixNano())

	if gender == "male" {
		n := len(utils.MaleList)
		index := mrand.Intn(n)
		return utils.MaleList[index]

	} else {
		n := len(utils.FemaleList)
		index := mrand.Intn(n)
		return utils.FemaleList[index]
	}
}

func (s *Svc) LoginName(ctx context.Context) (string, error) {

	ln, err := s.dao.GetNickName(ctx, "noun")
	if err != nil {
		return "", err
	}
	rln := utils.RandNum(len(ln))
	noun := ""
	if rln != -1 {
		noun = ln[rln].Name
	}

	lp, err := s.dao.GetNickName(ctx, "particle")
	if err != nil {
		return "", err
	}
	rlp := utils.RandNum(len(lp))
	particle := ""
	if rlp != -1 {
		particle = lp[rlp].Name
	}

	la, err := s.dao.GetNickName(ctx, "adjective")
	if err != nil {
		return "", err
	}
	rla := utils.RandNum(len(la))
	adjective := ""
	if rla != -1 {
		adjective = la[rla].Name
	}

	return fmt.Sprintf("%s%s%s", adjective, particle, noun), nil
}

func (s *Svc) OnlineNum(ctx context.Context, req *pb.OnlineNumReq) (*pb.OnlineNumRsp, error) {
	count := s.dao.OnlineNum(ctx)

	return &pb.OnlineNumRsp{Count: count}, nil
}

func (s *Svc) GetUserFansList(ctx context.Context, req *pb.GetUserFansListReq) (*pb.GetUserFansListRsp, error) {
	var (
		rsp  = new(pb.GetUserFansListRsp)
		list = make([]*pb.SessionInfo, 0)
	)
	results, err := s.dao.GetUserFansList(ctx, req.NoId, req.PageNum, req.PageSize)
	if err != nil {
		return rsp, err
	}
	for _, res := range results {
		list = append(list, &pb.SessionInfo{
			FromId:   res.From,
			ToId:     res.To,
			Relation: string(res.Relation),
		})
	}

	rsp.List = list
	return rsp, nil
}

func (s *Svc) UpdateSwitchSetting(ctx context.Context, req *pb.SetUserSwitchReq) (*pb.SetUserSwitchRsp, error) {
	data := &um.TUserSwitchSetting{
		NoId:                req.NoId,
		PrivateChatPush:     um.SwitchType(req.PrivateChatPush),
		OpenLivePush:        um.SwitchType(req.OpenLivePush),
		AutoRefuseMultiChat: um.SwitchType(req.AutoRefuseMultiChat),
		AnonymousBrowse:     um.SwitchType(req.AnonymousBrowse),
		CreateTime:          time.Now(),
	}
	if data.AutoRefuseMultiChat == um.SwitchOn {
		data.AutoRefuseTime = time.Now()
	}
	err := s.dao.UpsertSwitchSetting(ctx, data)
	return &pb.SetUserSwitchRsp{}, err
}

func (s *Svc) GetSwitchSetting(ctx context.Context, req *pb.GetUserSwitchReq) (*pb.GetUserSwitchRsp, error) {
	var (
		resultMap = make(map[string]*um.TUserSwitchSetting)
		rsp       = new(pb.GetUserSwitchRsp)
		list      = make([]*pb.SwitchInfo, 0)
	)
	results, err := s.dao.GetSwitchSetting(ctx, req.NoIds)
	if err != nil {
		return rsp, err
	}

	for _, res := range results {
		resultMap[res.NoId] = res
	}

	for _, noId := range req.NoIds {
		var (
			autoRefuseTime                                                                time.Time
			position, privateChatPush, openLivePush, autoRefuseMultiChat, anonymousBrowse um.SwitchType
		)
		res, ok := resultMap[noId]
		if ok && res != nil {
			position = res.Position
			privateChatPush = res.PrivateChatPush
			openLivePush = res.OpenLivePush
			autoRefuseMultiChat = res.AutoRefuseMultiChat
			autoRefuseTime = res.AutoRefuseTime
			anonymousBrowse = res.AnonymousBrowse
		}
		list = append(list, &pb.SwitchInfo{
			NoId:                noId,
			Position:            genSwitch(position, um.SwitchOn),
			PrivateChatPush:     genSwitch(privateChatPush, um.SwitchOn),
			OpenLivePush:        genSwitch(openLivePush, um.SwitchOn),
			AutoRefuseMultiChat: genSwitch(autoRefuseMultiChat, um.SwitchOff),
			AutoRefuseTime:      genSwitchTime(autoRefuseTime),
			AnonymousBrowse:     genSwitch(anonymousBrowse, um.SwitchOff),
		})
	}

	rsp.List = list
	return rsp, nil
}

func genSwitch(sw, defVal um.SwitchType) string {
	if len(sw) > 0 {
		return string(sw)
	}
	return string(defVal)
}

func genSwitchTime(t time.Time) string {
	if t.IsZero() {
		return ""
	}
	return t.Format(utils.TimeFormat2)
}
