package grpc

import (
	"net"

	"creativematrix.com/beyondreading/app/base/user/conf"
	"creativematrix.com/beyondreading/app/base/user/svc"
	"creativematrix.com/beyondreading/pkg/gm"
	pb "creativematrix.com/beyondreading/proto/user"
	"google.golang.org/grpc"
	"google.golang.org/grpc/keepalive"
	"time"
)

func Start(c *conf.Config, svc *svc.UserSvc) (*grpc.Server, error) {
	s := grpc.NewServer(
		grpc.KeepaliveParams(keepalive.ServerParameters{
			MaxConnectionIdle:     time.Minute * 5,
			MaxConnectionAge:      time.Hour,
			MaxConnectionAgeGrace: time.Minute * 5,
			Time:                  time.Minute * 10,
			Timeout:               time.Second * 3,
		}),
		grpc.UnaryInterceptor(gm.UnaryServerInterceptor()),
	)

	pb.RegisterUserServiceServer(s, svc)
	lis, err := net.Listen("tcp", c.Port.GRPC)
	if err != nil {
		return nil, err
	}
	go func() {
		if err := s.Serve(lis); err != nil {
			panic(err)
		}
	}()
	return s, nil
}
