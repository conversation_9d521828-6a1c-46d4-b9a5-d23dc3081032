package grpc

import (
	"context"
	"net"

	"google.golang.org/grpc"

	pb "creativematrix.com/beyondreading/app/base/user/api"
	"creativematrix.com/beyondreading/app/base/user/conf"
	"creativematrix.com/beyondreading/app/base/user/svc"
	"creativematrix.com/beyondreading/pkg/gm"
)

func Start(c *conf.Config, svc *svc.Svc) (*grpc.Server, error) {
	s := grpc.NewServer(grpc.UnaryInterceptor(gm.UnaryServerInterceptor()))

	pb.RegisterUserServer(s, &server{as: svc})
	lis, err := net.Listen("tcp", c.Port.GRPC)
	if err != nil {
		return nil, err
	}
	go func() {
		if err := s.Serve(lis); err != nil {
			panic(err)
		}
	}()
	return s, nil
}

type server struct {
	as *svc.Svc
}

func (s *server) TimeoutModifyMobile(ctx context.Context, req *pb.TimeoutModifyMobileReq) (*pb.TimeoutModifyMobileRsp, error) {
	return s.as.TimeoutModifyMobile(ctx, req)
}

func (s *server) GetAndCheckMobile(ctx context.Context, req *pb.GetAndCheckMobileReq) (*pb.GetAndCheckMobileRsp, error) {
	return s.as.GetAndCheckMobile(ctx, req)
}

func (s *server) ApplyModifyMobile(ctx context.Context, req *pb.ApplyModifyMobileReq) (*pb.EmptyRsp, error) {
	return &pb.EmptyRsp{}, s.as.ApplyModifyMobile(ctx, req)
}

func (s *server) GetModifyMobileInfo(ctx context.Context, req *pb.GetModifyMobileInfoReq) (*pb.GetModifyMobileInfoRsp, error) {
	return s.as.GetLatestModifyRecord(ctx, req)
}

func (s *server) GetModifyMobileList(ctx context.Context, req *pb.GetModifyMobileListReq) (*pb.GetModifyMobileListRsp, error) {
	return s.as.GetModifyMobileList(ctx, req)
}

func (s *server) SetPushStatus(ctx context.Context, req *pb.SetPushStatusReq) (*pb.EmptyRsp, error) {
	return &pb.EmptyRsp{}, s.as.SetPushStatus(ctx, req)
}

func (s *server) BindPushRelation(ctx context.Context, req *pb.BindPushRelationReq) (*pb.EmptyRsp, error) {
	return &pb.EmptyRsp{}, s.as.UpsertPushRelation(ctx, req)
}

func (s *server) UnBindPushRelation(ctx context.Context, req *pb.UnBindPushRelationReq) (*pb.EmptyRsp, error) {
	return &pb.EmptyRsp{}, s.as.UnBindPushRelation(ctx, req)
}

func (s *server) PushByAlias(ctx context.Context, req *pb.PushByAliasReq) (*pb.EmptyRsp, error) {
	return &pb.EmptyRsp{}, s.as.PushByAlias(ctx, req)
}

func (s *server) UpdateAlbumStatus(ctx context.Context, req *pb.UpdateAlbumReq) (*pb.UpdateAlbumRsp, error) {
	return s.as.UpdateAlbumStatus(ctx, req)
}

func (s *server) TokenToID(ctx context.Context, req *pb.TokenReq) (*pb.TokenRes, error) {
	id, err := s.as.TokenToID(ctx, req.Token)
	if err != nil {
		return nil, err
	}
	return &pb.TokenRes{Id: id}, nil
}

func (s *server) GetUserInfo(ctx context.Context, req *pb.UserReq) (*pb.UserInfoRes, error) {
	return &pb.UserInfoRes{Id: "12", Nickname: "t", Born: "2000-01-01"}, nil
}

// GetUsersByIds 通过noId获取指定列
func (s *server) GetUsersByIds(ctx context.Context, req *pb.UserIdsReq) (*pb.UserInfoRsp, error) {
	return s.as.GetUsersByIds(ctx, req)
}

// GetUsersByMongoIds 根据_id 获取用户信息
func (s *server) GetUsersByMongoIds(ctx context.Context, req *pb.MongoIdsReq) (*pb.UserInfoRsp, error) {
	return s.as.GetUsersByMongoIds(ctx, req)
}

// GetUsersByMsgIds 根据 msgId 获取用户信息
func (s *server) GetUsersByMsgIds(ctx context.Context, req *pb.MsgIdsReq) (*pb.UserInfoRsp, error) {
	return s.as.GetUsersByMsgIds(ctx, req)
}

// UpdateUserInfo 更新用户信息
func (s *server) UpdateUserInfo(ctx context.Context, req *pb.UpdateUserReq) (*pb.UserInfo, error) {
	return s.as.UpdateUserInfo(ctx, req)
}

// IsUserExist 根据noId查询用户是否存在
func (s *server) IsUserExist(ctx context.Context, req *pb.UserExistReq) (*pb.UserExistRsp, error) {
	return s.as.IsUserExistByNoId(ctx, req)
}

func (s *server) PullUser(ctx context.Context, req *pb.PullUserReq) (rsp *pb.PullUserRsp, err error) {
	return s.as.PullUser(ctx, req)
}

// GetUserRemark 获取用户备注
func (s *server) GetUserRemark(ctx context.Context, req *pb.UserRemarkReq) (*pb.UserRemarkRsp, error) {
	return s.as.GetUserRemark(ctx, req)
}

func (s *server) GetOnline(ctx context.Context, req *pb.GetOnlineRsq) (*pb.GetOnlineRsp, error) {
	return s.as.GetOnline(ctx, req)
}

func (s *server) SetOnline(ctx context.Context, req *pb.SetOnlineUserInfoReq) (*pb.SetOnlineUserInfoReqRsp, error) {
	return s.as.SetOnline(ctx, req)
}

func (s *server) RobotAdd(ctx context.Context, req *pb.RobotAddReq) (*pb.RobotAddRsp, error) {
	return s.as.RobotAdd(ctx, req)
}

func (s *server) OnlineNum(ctx context.Context, req *pb.OnlineNumReq) (*pb.OnlineNumRsp, error) {
	return s.as.OnlineNum(ctx, req)
}

func (s *server) ValidHeader(ctx context.Context, req *pb.ValidHeadReq) (*pb.ValidHeadRsp, error) {
	return s.as.ValidHead(ctx, req)
}

func (s *server) ValidByImei(ctx context.Context, req *pb.ValidHeadReq) (*pb.ValidHeadRsp, error) {
	return s.as.ValidImei(ctx, req)
}

func (s *server) ValidByIP(ctx context.Context, req *pb.ValidHeadReq) (*pb.ValidHeadRsp, error) {
	return s.as.ValidByIP(ctx, req)
}

func (s *server) GetUserFansList(ctx context.Context, req *pb.GetUserFansListReq) (*pb.GetUserFansListRsp, error) {
	return s.as.GetUserFansList(ctx, req)
}

func (s *server) SetUserSwitch(ctx context.Context, req *pb.SetUserSwitchReq) (*pb.SetUserSwitchRsp, error) {
	return s.as.UpdateSwitchSetting(ctx, req)
}

func (s *server) GetUserSwitch(ctx context.Context, req *pb.GetUserSwitchReq) (*pb.GetUserSwitchRsp, error) {
	return s.as.GetSwitchSetting(ctx, req)
}

func (s *server) ReviewTextInfo(ctx context.Context, req *pb.ReviewInfoReq) (*pb.ReviewInfoRsp, error) {
	return s.as.ReviewInfo(ctx, req)
}

func (s *server) GetIPLocation(ctx context.Context, req *pb.IPLocReq) (*pb.IPLocRsp, error) {
	return s.as.GetLocationFromIP(ctx, req.Ip)
}

func (s *server) GetUserInterest(ctx context.Context, req *pb.GetUserInterestReq) (*pb.GetUserInterestRsp, error) {
	return s.as.GetUserInterest(ctx, req)
}

func (s *server) GetUserInterests(ctx context.Context, req *pb.GetUserInterestsReq) (*pb.GetUserInterestsRsp, error) {
	return s.as.GetUserInterests(ctx, req)
}
