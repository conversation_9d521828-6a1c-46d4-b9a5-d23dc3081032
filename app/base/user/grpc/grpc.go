package grpc

import (
	"context"
	"net"

	"creativematrix.com/beyondreading/app/base/user/conf"
	"creativematrix.com/beyondreading/app/base/user/svc"
	"creativematrix.com/beyondreading/pkg/gm"
	pb "creativematrix.com/beyondreading/proto/user"
	"google.golang.org/grpc"
	"google.golang.org/grpc/keepalive"
	"time"
)

func Start(c *conf.Config, svc *svc.UserSvc) (*grpc.Server, error) {
	s := grpc.NewServer(
		grpc.KeepaliveParams(keepalive.ServerParameters{
			MaxConnectionIdle:     time.Minute * 5,
			MaxConnectionAge:      time.Hour,
			MaxConnectionAgeGrace: time.Minute * 5,
			Time:                  time.Minute * 10,
			Timeout:               time.Second * 3,
		}),
		grpc.UnaryInterceptor(gm.UnaryServerInterceptor()),
	)

	pb.RegisterUserServiceServer(s, &server{as: svc})
	lis, err := net.Listen("tcp", c.Port.GRPC)
	if err != nil {
		return nil, err
	}
	go func() {
		if err := s.Serve(lis); err != nil {
			panic(err)
		}
	}()
	return s, nil
}

type server struct {
	as *svc.UserSvc
}

func (s server) RegisterBySms(ctx context.Context, req *pb.RegisterBySmsReq) (*pb.RegisterBySmsResp, error) {
	//TODO implement me
	panic("implement me")
}

func (s server) RegisterByGoogle(ctx context.Context, req *pb.RegisterByGoogleReq) (*pb.RegisterByGoogleResp, error) {
	//TODO implement me
	panic("implement me")
}

func (s server) RegisterByApple(ctx context.Context, req *pb.RegisterByAppleReq) (*pb.RegisterByAppleResp, error) {
	//TODO implement me
	panic("implement me")
}

func (s server) LoginBySms(ctx context.Context, req *pb.LoginBySmsReq) (*pb.LoginBySmsResp, error) {
	//TODO implement me
	panic("implement me")
}

func (s server) LoginByGoogle(ctx context.Context, req *pb.LoginByGoogleReq) (*pb.LoginByGoogleResp, error) {
	//TODO implement me
	panic("implement me")
}

func (s server) LoginByApple(ctx context.Context, req *pb.LoginByAppleReq) (*pb.LoginByAppleResp, error) {
	//TODO implement me
	panic("implement me")
}

func (s server) GetUserInfo(ctx context.Context, req *pb.GetUserInfoReq) (*pb.GetUserInfoResp, error) {
	//TODO implement me
	panic("implement me")
}

func (s server) UpdateUserInfo(ctx context.Context, req *pb.UpdateUserInfoReq) (*pb.UpdateUserInfoResp, error) {
	//TODO implement me
	panic("implement me")
}

func (s server) GetLoginLogs(ctx context.Context, req *pb.GetLoginLogsReq) (*pb.GetLoginLogsResp, error) {
	//TODO implement me
	panic("implement me")
}

func (s server) SendSmsCode(ctx context.Context, req *pb.SendSmsCodeReq) (*pb.SendSmsCodeResp, error) {
	//TODO implement me
	panic("implement me")
}

func (s server) VerifySmsCode(ctx context.Context, req *pb.VerifySmsCodeReq) (*pb.VerifySmsCodeResp, error) {
	//TODO implement me
	panic("implement me")
}
