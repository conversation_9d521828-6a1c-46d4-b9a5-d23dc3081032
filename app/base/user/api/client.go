package api

import (
	"fmt"

	"google.golang.org/grpc/resolver"

	"google.golang.org/grpc"
	"google.golang.org/grpc/balancer/roundrobin"

	"creativematrix.com/beyondreading/pkg/config"
	"creativematrix.com/beyondreading/pkg/discovery"
	"creativematrix.com/beyondreading/pkg/gm"
	"creativematrix.com/beyondreading/pkg/logger"
)

const App = "base-user"

func NewClient(c config.Base, opts ...grpc.DialOption) (UserClient, error) {
	options := []grpc.DialOption{
		grpc.WithInsecure(),
		grpc.WithDefaultServiceConfig(fmt.Sprintf(`{"LoadBalancingPolicy": "%s"}`, roundrobin.Name)),
		grpc.WithUnaryInterceptor(gm.UnaryClientInterceptor()),
	}

	r := discovery.NewResolver(c.Etcd.Addrs, logger.Log)
	resolver.Register(r)

	conn, err := grpc.Dial("etcd:///"+App, options...)
	//conn, err := grpc.Dial("192.168.3.62:32333", options...)
	if err != nil {
		return nil, err
	}
	return NewUserClient(conn), nil
}
