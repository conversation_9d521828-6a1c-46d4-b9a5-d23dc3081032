// Code generated by protoc-gen-go. DO NOT EDIT.
// source: user.proto

package api

import (
	context "context"
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type ModifyType int32

const (
	ModifyType_NoneType  ModifyType = 0
	ModifyType_UserSelf  ModifyType = 1
	ModifyType_AdminHelp ModifyType = 2
	ModifyType_AdminAuth ModifyType = 3
)

var ModifyType_name = map[int32]string{
	0: "NoneType",
	1: "UserSelf",
	2: "AdminHelp",
	3: "AdminAuth",
}

var ModifyType_value = map[string]int32{
	"NoneType":  0,
	"UserSelf":  1,
	"AdminHelp": 2,
	"AdminAuth": 3,
}

func (x ModifyType) String() string {
	return proto.EnumName(ModifyType_name, int32(x))
}

func (ModifyType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{0}
}

type ModifyStatus int32

const (
	ModifyStatus_VoidStatus ModifyStatus = 0
	ModifyStatus_Pass       ModifyStatus = 1
	ModifyStatus_Apply      ModifyStatus = 2
	ModifyStatus_Reject     ModifyStatus = 3
	ModifyStatus_Timeout    ModifyStatus = 4
)

var ModifyStatus_name = map[int32]string{
	0: "VoidStatus",
	1: "Pass",
	2: "Apply",
	3: "Reject",
	4: "Timeout",
}

var ModifyStatus_value = map[string]int32{
	"VoidStatus": 0,
	"Pass":       1,
	"Apply":      2,
	"Reject":     3,
	"Timeout":    4,
}

func (x ModifyStatus) String() string {
	return proto.EnumName(ModifyStatus_name, int32(x))
}

func (ModifyStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{1}
}

type FilterParam int32

const (
	FilterParam_NoneFilter       FilterParam = 0
	FilterParam_FilterPass       FilterParam = 1
	FilterParam_FilterSelfModify FilterParam = 2
	FilterParam_FilterTimeout    FilterParam = 3
	FilterParam_FilterReject     FilterParam = 4
	FilterParam_FilterAdminAuth  FilterParam = 5
	FilterParam_FilterApply      FilterParam = 6
)

var FilterParam_name = map[int32]string{
	0: "NoneFilter",
	1: "FilterPass",
	2: "FilterSelfModify",
	3: "FilterTimeout",
	4: "FilterReject",
	5: "FilterAdminAuth",
	6: "FilterApply",
}

var FilterParam_value = map[string]int32{
	"NoneFilter":       0,
	"FilterPass":       1,
	"FilterSelfModify": 2,
	"FilterTimeout":    3,
	"FilterReject":     4,
	"FilterAdminAuth":  5,
	"FilterApply":      6,
}

func (x FilterParam) String() string {
	return proto.EnumName(FilterParam_name, int32(x))
}

func (FilterParam) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{2}
}

type CheckType int32

const (
	CheckType_NoneCheck       CheckType = 0
	CheckType_CheckConflict   CheckType = 1
	CheckType_CheckLimitTimes CheckType = 2
)

var CheckType_name = map[int32]string{
	0: "NoneCheck",
	1: "CheckConflict",
	2: "CheckLimitTimes",
}

var CheckType_value = map[string]int32{
	"NoneCheck":       0,
	"CheckConflict":   1,
	"CheckLimitTimes": 2,
}

func (x CheckType) String() string {
	return proto.EnumName(CheckType_name, int32(x))
}

func (CheckType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{3}
}

type PushType int32

const (
	PushType_Unknown       PushType = 0
	PushType_PrivateMsg    PushType = 1
	PushType_PrivateGift   PushType = 2
	PushType_HeartGift     PushType = 3
	PushType_AccostGift    PushType = 4
	PushType_FollowersOpen PushType = 5
)

var PushType_name = map[int32]string{
	0: "Unknown",
	1: "PrivateMsg",
	2: "PrivateGift",
	3: "HeartGift",
	4: "AccostGift",
	5: "FollowersOpen",
}

var PushType_value = map[string]int32{
	"Unknown":       0,
	"PrivateMsg":    1,
	"PrivateGift":   2,
	"HeartGift":     3,
	"AccostGift":    4,
	"FollowersOpen": 5,
}

func (x PushType) String() string {
	return proto.EnumName(PushType_name, int32(x))
}

func (PushType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{4}
}

type TargetType int32

const (
	TargetType_None        TargetType = 0
	TargetType_OpenLive    TargetType = 1
	TargetType_ChatMessage TargetType = 2
)

var TargetType_name = map[int32]string{
	0: "None",
	1: "OpenLive",
	2: "ChatMessage",
}

var TargetType_value = map[string]int32{
	"None":        0,
	"OpenLive":    1,
	"ChatMessage": 2,
}

func (x TargetType) String() string {
	return proto.EnumName(TargetType_name, int32(x))
}

func (TargetType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{5}
}

type TimeoutModifyMobileReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TimeoutModifyMobileReq) Reset()         { *m = TimeoutModifyMobileReq{} }
func (m *TimeoutModifyMobileReq) String() string { return proto.CompactTextString(m) }
func (*TimeoutModifyMobileReq) ProtoMessage()    {}
func (*TimeoutModifyMobileReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{0}
}

func (m *TimeoutModifyMobileReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TimeoutModifyMobileReq.Unmarshal(m, b)
}
func (m *TimeoutModifyMobileReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TimeoutModifyMobileReq.Marshal(b, m, deterministic)
}
func (m *TimeoutModifyMobileReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TimeoutModifyMobileReq.Merge(m, src)
}
func (m *TimeoutModifyMobileReq) XXX_Size() int {
	return xxx_messageInfo_TimeoutModifyMobileReq.Size(m)
}
func (m *TimeoutModifyMobileReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TimeoutModifyMobileReq.DiscardUnknown(m)
}

var xxx_messageInfo_TimeoutModifyMobileReq proto.InternalMessageInfo

type TimeoutModifyMobileRsp struct {
	TMap                 map[string]string `protobuf:"bytes,1,rep,name=tMap,proto3" json:"tMap,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *TimeoutModifyMobileRsp) Reset()         { *m = TimeoutModifyMobileRsp{} }
func (m *TimeoutModifyMobileRsp) String() string { return proto.CompactTextString(m) }
func (*TimeoutModifyMobileRsp) ProtoMessage()    {}
func (*TimeoutModifyMobileRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{1}
}

func (m *TimeoutModifyMobileRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TimeoutModifyMobileRsp.Unmarshal(m, b)
}
func (m *TimeoutModifyMobileRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TimeoutModifyMobileRsp.Marshal(b, m, deterministic)
}
func (m *TimeoutModifyMobileRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TimeoutModifyMobileRsp.Merge(m, src)
}
func (m *TimeoutModifyMobileRsp) XXX_Size() int {
	return xxx_messageInfo_TimeoutModifyMobileRsp.Size(m)
}
func (m *TimeoutModifyMobileRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_TimeoutModifyMobileRsp.DiscardUnknown(m)
}

var xxx_messageInfo_TimeoutModifyMobileRsp proto.InternalMessageInfo

func (m *TimeoutModifyMobileRsp) GetTMap() map[string]string {
	if m != nil {
		return m.TMap
	}
	return nil
}

type GetAndCheckMobileReq struct {
	NoId                 string    `protobuf:"bytes,1,opt,name=noId,proto3" json:"noId,omitempty"`
	ToMobile             string    `protobuf:"bytes,2,opt,name=toMobile,proto3" json:"toMobile,omitempty"`
	Type                 CheckType `protobuf:"varint,3,opt,name=type,proto3,enum=user.base.CheckType" json:"type,omitempty"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-"`
	XXX_unrecognized     []byte    `json:"-"`
	XXX_sizecache        int32     `json:"-"`
}

func (m *GetAndCheckMobileReq) Reset()         { *m = GetAndCheckMobileReq{} }
func (m *GetAndCheckMobileReq) String() string { return proto.CompactTextString(m) }
func (*GetAndCheckMobileReq) ProtoMessage()    {}
func (*GetAndCheckMobileReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{2}
}

func (m *GetAndCheckMobileReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAndCheckMobileReq.Unmarshal(m, b)
}
func (m *GetAndCheckMobileReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAndCheckMobileReq.Marshal(b, m, deterministic)
}
func (m *GetAndCheckMobileReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAndCheckMobileReq.Merge(m, src)
}
func (m *GetAndCheckMobileReq) XXX_Size() int {
	return xxx_messageInfo_GetAndCheckMobileReq.Size(m)
}
func (m *GetAndCheckMobileReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAndCheckMobileReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetAndCheckMobileReq proto.InternalMessageInfo

func (m *GetAndCheckMobileReq) GetNoId() string {
	if m != nil {
		return m.NoId
	}
	return ""
}

func (m *GetAndCheckMobileReq) GetToMobile() string {
	if m != nil {
		return m.ToMobile
	}
	return ""
}

func (m *GetAndCheckMobileReq) GetType() CheckType {
	if m != nil {
		return m.Type
	}
	return CheckType_NoneCheck
}

type GetAndCheckMobileRsp struct {
	IsConflict           bool            `protobuf:"varint,1,opt,name=isConflict,proto3" json:"isConflict,omitempty"`
	From                 *MobileUserInfo `protobuf:"bytes,2,opt,name=from,proto3" json:"from,omitempty"`
	To                   *MobileUserInfo `protobuf:"bytes,3,opt,name=to,proto3" json:"to,omitempty"`
	IsOverTimes          bool            `protobuf:"varint,4,opt,name=isOverTimes,proto3" json:"isOverTimes,omitempty"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-"`
	XXX_unrecognized     []byte          `json:"-"`
	XXX_sizecache        int32           `json:"-"`
}

func (m *GetAndCheckMobileRsp) Reset()         { *m = GetAndCheckMobileRsp{} }
func (m *GetAndCheckMobileRsp) String() string { return proto.CompactTextString(m) }
func (*GetAndCheckMobileRsp) ProtoMessage()    {}
func (*GetAndCheckMobileRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{3}
}

func (m *GetAndCheckMobileRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetAndCheckMobileRsp.Unmarshal(m, b)
}
func (m *GetAndCheckMobileRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetAndCheckMobileRsp.Marshal(b, m, deterministic)
}
func (m *GetAndCheckMobileRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetAndCheckMobileRsp.Merge(m, src)
}
func (m *GetAndCheckMobileRsp) XXX_Size() int {
	return xxx_messageInfo_GetAndCheckMobileRsp.Size(m)
}
func (m *GetAndCheckMobileRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetAndCheckMobileRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetAndCheckMobileRsp proto.InternalMessageInfo

func (m *GetAndCheckMobileRsp) GetIsConflict() bool {
	if m != nil {
		return m.IsConflict
	}
	return false
}

func (m *GetAndCheckMobileRsp) GetFrom() *MobileUserInfo {
	if m != nil {
		return m.From
	}
	return nil
}

func (m *GetAndCheckMobileRsp) GetTo() *MobileUserInfo {
	if m != nil {
		return m.To
	}
	return nil
}

func (m *GetAndCheckMobileRsp) GetIsOverTimes() bool {
	if m != nil {
		return m.IsOverTimes
	}
	return false
}

type MobileUserInfo struct {
	NoId                 string   `protobuf:"bytes,1,opt,name=noId,proto3" json:"noId,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Avatar               string   `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`
	WealthLevel          int64    `protobuf:"varint,4,opt,name=wealthLevel,proto3" json:"wealthLevel,omitempty"`
	CharmLevel           int64    `protobuf:"varint,5,opt,name=charmLevel,proto3" json:"charmLevel,omitempty"`
	Mobile               string   `protobuf:"bytes,6,opt,name=mobile,proto3" json:"mobile,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MobileUserInfo) Reset()         { *m = MobileUserInfo{} }
func (m *MobileUserInfo) String() string { return proto.CompactTextString(m) }
func (*MobileUserInfo) ProtoMessage()    {}
func (*MobileUserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{4}
}

func (m *MobileUserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MobileUserInfo.Unmarshal(m, b)
}
func (m *MobileUserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MobileUserInfo.Marshal(b, m, deterministic)
}
func (m *MobileUserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MobileUserInfo.Merge(m, src)
}
func (m *MobileUserInfo) XXX_Size() int {
	return xxx_messageInfo_MobileUserInfo.Size(m)
}
func (m *MobileUserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MobileUserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MobileUserInfo proto.InternalMessageInfo

func (m *MobileUserInfo) GetNoId() string {
	if m != nil {
		return m.NoId
	}
	return ""
}

func (m *MobileUserInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *MobileUserInfo) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *MobileUserInfo) GetWealthLevel() int64 {
	if m != nil {
		return m.WealthLevel
	}
	return 0
}

func (m *MobileUserInfo) GetCharmLevel() int64 {
	if m != nil {
		return m.CharmLevel
	}
	return 0
}

func (m *MobileUserInfo) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

type GetModifyMobileListReq struct {
	NoId                 string      `protobuf:"bytes,1,opt,name=noId,proto3" json:"noId,omitempty"`
	StartTime            string      `protobuf:"bytes,2,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime              string      `protobuf:"bytes,3,opt,name=endTime,proto3" json:"endTime,omitempty"`
	FilterParam          FilterParam `protobuf:"varint,4,opt,name=filterParam,proto3,enum=user.base.FilterParam" json:"filterParam,omitempty"`
	Page                 int64       `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	PageSize             int64       `protobuf:"varint,6,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *GetModifyMobileListReq) Reset()         { *m = GetModifyMobileListReq{} }
func (m *GetModifyMobileListReq) String() string { return proto.CompactTextString(m) }
func (*GetModifyMobileListReq) ProtoMessage()    {}
func (*GetModifyMobileListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{5}
}

func (m *GetModifyMobileListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetModifyMobileListReq.Unmarshal(m, b)
}
func (m *GetModifyMobileListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetModifyMobileListReq.Marshal(b, m, deterministic)
}
func (m *GetModifyMobileListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetModifyMobileListReq.Merge(m, src)
}
func (m *GetModifyMobileListReq) XXX_Size() int {
	return xxx_messageInfo_GetModifyMobileListReq.Size(m)
}
func (m *GetModifyMobileListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetModifyMobileListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetModifyMobileListReq proto.InternalMessageInfo

func (m *GetModifyMobileListReq) GetNoId() string {
	if m != nil {
		return m.NoId
	}
	return ""
}

func (m *GetModifyMobileListReq) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *GetModifyMobileListReq) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *GetModifyMobileListReq) GetFilterParam() FilterParam {
	if m != nil {
		return m.FilterParam
	}
	return FilterParam_NoneFilter
}

func (m *GetModifyMobileListReq) GetPage() int64 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GetModifyMobileListReq) GetPageSize() int64 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetModifyMobileListRsp struct {
	List                 []*ModifyMobileData `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Count                int64               `protobuf:"varint,2,opt,name=Count,proto3" json:"Count,omitempty"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-"`
	XXX_unrecognized     []byte              `json:"-"`
	XXX_sizecache        int32               `json:"-"`
}

func (m *GetModifyMobileListRsp) Reset()         { *m = GetModifyMobileListRsp{} }
func (m *GetModifyMobileListRsp) String() string { return proto.CompactTextString(m) }
func (*GetModifyMobileListRsp) ProtoMessage()    {}
func (*GetModifyMobileListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{6}
}

func (m *GetModifyMobileListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetModifyMobileListRsp.Unmarshal(m, b)
}
func (m *GetModifyMobileListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetModifyMobileListRsp.Marshal(b, m, deterministic)
}
func (m *GetModifyMobileListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetModifyMobileListRsp.Merge(m, src)
}
func (m *GetModifyMobileListRsp) XXX_Size() int {
	return xxx_messageInfo_GetModifyMobileListRsp.Size(m)
}
func (m *GetModifyMobileListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetModifyMobileListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetModifyMobileListRsp proto.InternalMessageInfo

func (m *GetModifyMobileListRsp) GetList() []*ModifyMobileData {
	if m != nil {
		return m.List
	}
	return nil
}

func (m *GetModifyMobileListRsp) GetCount() int64 {
	if m != nil {
		return m.Count
	}
	return 0
}

type ModifyMobileData struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	NoId                 string   `protobuf:"bytes,2,opt,name=noId,proto3" json:"noId,omitempty"`
	UpdateTime           string   `protobuf:"bytes,3,opt,name=updateTime,proto3" json:"updateTime,omitempty"`
	FromMobile           string   `protobuf:"bytes,4,opt,name=fromMobile,proto3" json:"fromMobile,omitempty"`
	ToMobile             string   `protobuf:"bytes,5,opt,name=toMobile,proto3" json:"toMobile,omitempty"`
	ApplyReason          string   `protobuf:"bytes,6,opt,name=applyReason,proto3" json:"applyReason,omitempty"`
	AdminReason          string   `protobuf:"bytes,7,opt,name=adminReason,proto3" json:"adminReason,omitempty"`
	OperatorName         string   `protobuf:"bytes,8,opt,name=operatorName,proto3" json:"operatorName,omitempty"`
	ApplyTime            string   `protobuf:"bytes,9,opt,name=applyTime,proto3" json:"applyTime,omitempty"`
	ProveImg             []string `protobuf:"bytes,10,rep,name=proveImg,proto3" json:"proveImg,omitempty"`
	Status               string   `protobuf:"bytes,11,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ModifyMobileData) Reset()         { *m = ModifyMobileData{} }
func (m *ModifyMobileData) String() string { return proto.CompactTextString(m) }
func (*ModifyMobileData) ProtoMessage()    {}
func (*ModifyMobileData) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{7}
}

func (m *ModifyMobileData) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModifyMobileData.Unmarshal(m, b)
}
func (m *ModifyMobileData) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModifyMobileData.Marshal(b, m, deterministic)
}
func (m *ModifyMobileData) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModifyMobileData.Merge(m, src)
}
func (m *ModifyMobileData) XXX_Size() int {
	return xxx_messageInfo_ModifyMobileData.Size(m)
}
func (m *ModifyMobileData) XXX_DiscardUnknown() {
	xxx_messageInfo_ModifyMobileData.DiscardUnknown(m)
}

var xxx_messageInfo_ModifyMobileData proto.InternalMessageInfo

func (m *ModifyMobileData) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *ModifyMobileData) GetNoId() string {
	if m != nil {
		return m.NoId
	}
	return ""
}

func (m *ModifyMobileData) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *ModifyMobileData) GetFromMobile() string {
	if m != nil {
		return m.FromMobile
	}
	return ""
}

func (m *ModifyMobileData) GetToMobile() string {
	if m != nil {
		return m.ToMobile
	}
	return ""
}

func (m *ModifyMobileData) GetApplyReason() string {
	if m != nil {
		return m.ApplyReason
	}
	return ""
}

func (m *ModifyMobileData) GetAdminReason() string {
	if m != nil {
		return m.AdminReason
	}
	return ""
}

func (m *ModifyMobileData) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

func (m *ModifyMobileData) GetApplyTime() string {
	if m != nil {
		return m.ApplyTime
	}
	return ""
}

func (m *ModifyMobileData) GetProveImg() []string {
	if m != nil {
		return m.ProveImg
	}
	return nil
}

func (m *ModifyMobileData) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

type GetModifyMobileInfoReq struct {
	NoId                 string   `protobuf:"bytes,1,opt,name=noId,proto3" json:"noId,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetModifyMobileInfoReq) Reset()         { *m = GetModifyMobileInfoReq{} }
func (m *GetModifyMobileInfoReq) String() string { return proto.CompactTextString(m) }
func (*GetModifyMobileInfoReq) ProtoMessage()    {}
func (*GetModifyMobileInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{8}
}

func (m *GetModifyMobileInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetModifyMobileInfoReq.Unmarshal(m, b)
}
func (m *GetModifyMobileInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetModifyMobileInfoReq.Marshal(b, m, deterministic)
}
func (m *GetModifyMobileInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetModifyMobileInfoReq.Merge(m, src)
}
func (m *GetModifyMobileInfoReq) XXX_Size() int {
	return xxx_messageInfo_GetModifyMobileInfoReq.Size(m)
}
func (m *GetModifyMobileInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetModifyMobileInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetModifyMobileInfoReq proto.InternalMessageInfo

func (m *GetModifyMobileInfoReq) GetNoId() string {
	if m != nil {
		return m.NoId
	}
	return ""
}

type GetModifyMobileInfoRsp struct {
	NoId                 string   `protobuf:"bytes,1,opt,name=noId,proto3" json:"noId,omitempty"`
	FromMobile           string   `protobuf:"bytes,2,opt,name=fromMobile,proto3" json:"fromMobile,omitempty"`
	ToMobile             string   `protobuf:"bytes,3,opt,name=toMobile,proto3" json:"toMobile,omitempty"`
	ApplyReason          string   `protobuf:"bytes,4,opt,name=applyReason,proto3" json:"applyReason,omitempty"`
	ProveImg             []string `protobuf:"bytes,5,rep,name=proveImg,proto3" json:"proveImg,omitempty"`
	ModifyType           string   `protobuf:"bytes,6,opt,name=modifyType,proto3" json:"modifyType,omitempty"`
	Status               string   `protobuf:"bytes,7,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetModifyMobileInfoRsp) Reset()         { *m = GetModifyMobileInfoRsp{} }
func (m *GetModifyMobileInfoRsp) String() string { return proto.CompactTextString(m) }
func (*GetModifyMobileInfoRsp) ProtoMessage()    {}
func (*GetModifyMobileInfoRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{9}
}

func (m *GetModifyMobileInfoRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetModifyMobileInfoRsp.Unmarshal(m, b)
}
func (m *GetModifyMobileInfoRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetModifyMobileInfoRsp.Marshal(b, m, deterministic)
}
func (m *GetModifyMobileInfoRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetModifyMobileInfoRsp.Merge(m, src)
}
func (m *GetModifyMobileInfoRsp) XXX_Size() int {
	return xxx_messageInfo_GetModifyMobileInfoRsp.Size(m)
}
func (m *GetModifyMobileInfoRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetModifyMobileInfoRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetModifyMobileInfoRsp proto.InternalMessageInfo

func (m *GetModifyMobileInfoRsp) GetNoId() string {
	if m != nil {
		return m.NoId
	}
	return ""
}

func (m *GetModifyMobileInfoRsp) GetFromMobile() string {
	if m != nil {
		return m.FromMobile
	}
	return ""
}

func (m *GetModifyMobileInfoRsp) GetToMobile() string {
	if m != nil {
		return m.ToMobile
	}
	return ""
}

func (m *GetModifyMobileInfoRsp) GetApplyReason() string {
	if m != nil {
		return m.ApplyReason
	}
	return ""
}

func (m *GetModifyMobileInfoRsp) GetProveImg() []string {
	if m != nil {
		return m.ProveImg
	}
	return nil
}

func (m *GetModifyMobileInfoRsp) GetModifyType() string {
	if m != nil {
		return m.ModifyType
	}
	return ""
}

func (m *GetModifyMobileInfoRsp) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

type ApplyModifyMobileReq struct {
	NoId                 string       `protobuf:"bytes,1,opt,name=noId,proto3" json:"noId,omitempty"`
	FromMobile           string       `protobuf:"bytes,2,opt,name=fromMobile,proto3" json:"fromMobile,omitempty"`
	ToMobile             string       `protobuf:"bytes,3,opt,name=toMobile,proto3" json:"toMobile,omitempty"`
	OperatorId           string       `protobuf:"bytes,4,opt,name=operatorId,proto3" json:"operatorId,omitempty"`
	OperatorName         string       `protobuf:"bytes,5,opt,name=operatorName,proto3" json:"operatorName,omitempty"`
	ModifyType           ModifyType   `protobuf:"varint,6,opt,name=modifyType,proto3,enum=user.base.ModifyType" json:"modifyType,omitempty"`
	ApplyReason          string       `protobuf:"bytes,7,opt,name=applyReason,proto3" json:"applyReason,omitempty"`
	AdminReason          string       `protobuf:"bytes,8,opt,name=adminReason,proto3" json:"adminReason,omitempty"`
	ProveImg             []string     `protobuf:"bytes,9,rep,name=proveImg,proto3" json:"proveImg,omitempty"`
	ModifyStatus         ModifyStatus `protobuf:"varint,10,opt,name=modifyStatus,proto3,enum=user.base.ModifyStatus" json:"modifyStatus,omitempty"`
	Id                   string       `protobuf:"bytes,11,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *ApplyModifyMobileReq) Reset()         { *m = ApplyModifyMobileReq{} }
func (m *ApplyModifyMobileReq) String() string { return proto.CompactTextString(m) }
func (*ApplyModifyMobileReq) ProtoMessage()    {}
func (*ApplyModifyMobileReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{10}
}

func (m *ApplyModifyMobileReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ApplyModifyMobileReq.Unmarshal(m, b)
}
func (m *ApplyModifyMobileReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ApplyModifyMobileReq.Marshal(b, m, deterministic)
}
func (m *ApplyModifyMobileReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ApplyModifyMobileReq.Merge(m, src)
}
func (m *ApplyModifyMobileReq) XXX_Size() int {
	return xxx_messageInfo_ApplyModifyMobileReq.Size(m)
}
func (m *ApplyModifyMobileReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ApplyModifyMobileReq.DiscardUnknown(m)
}

var xxx_messageInfo_ApplyModifyMobileReq proto.InternalMessageInfo

func (m *ApplyModifyMobileReq) GetNoId() string {
	if m != nil {
		return m.NoId
	}
	return ""
}

func (m *ApplyModifyMobileReq) GetFromMobile() string {
	if m != nil {
		return m.FromMobile
	}
	return ""
}

func (m *ApplyModifyMobileReq) GetToMobile() string {
	if m != nil {
		return m.ToMobile
	}
	return ""
}

func (m *ApplyModifyMobileReq) GetOperatorId() string {
	if m != nil {
		return m.OperatorId
	}
	return ""
}

func (m *ApplyModifyMobileReq) GetOperatorName() string {
	if m != nil {
		return m.OperatorName
	}
	return ""
}

func (m *ApplyModifyMobileReq) GetModifyType() ModifyType {
	if m != nil {
		return m.ModifyType
	}
	return ModifyType_NoneType
}

func (m *ApplyModifyMobileReq) GetApplyReason() string {
	if m != nil {
		return m.ApplyReason
	}
	return ""
}

func (m *ApplyModifyMobileReq) GetAdminReason() string {
	if m != nil {
		return m.AdminReason
	}
	return ""
}

func (m *ApplyModifyMobileReq) GetProveImg() []string {
	if m != nil {
		return m.ProveImg
	}
	return nil
}

func (m *ApplyModifyMobileReq) GetModifyStatus() ModifyStatus {
	if m != nil {
		return m.ModifyStatus
	}
	return ModifyStatus_VoidStatus
}

func (m *ApplyModifyMobileReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type SetPushStatusReq struct {
	NoId                 string   `protobuf:"bytes,1,opt,name=noId,proto3" json:"noId,omitempty"`
	Status               string   `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetPushStatusReq) Reset()         { *m = SetPushStatusReq{} }
func (m *SetPushStatusReq) String() string { return proto.CompactTextString(m) }
func (*SetPushStatusReq) ProtoMessage()    {}
func (*SetPushStatusReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{11}
}

func (m *SetPushStatusReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetPushStatusReq.Unmarshal(m, b)
}
func (m *SetPushStatusReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetPushStatusReq.Marshal(b, m, deterministic)
}
func (m *SetPushStatusReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetPushStatusReq.Merge(m, src)
}
func (m *SetPushStatusReq) XXX_Size() int {
	return xxx_messageInfo_SetPushStatusReq.Size(m)
}
func (m *SetPushStatusReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetPushStatusReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetPushStatusReq proto.InternalMessageInfo

func (m *SetPushStatusReq) GetNoId() string {
	if m != nil {
		return m.NoId
	}
	return ""
}

func (m *SetPushStatusReq) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

type UnBindPushRelationReq struct {
	NoId                 string   `protobuf:"bytes,1,opt,name=noId,proto3" json:"noId,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UnBindPushRelationReq) Reset()         { *m = UnBindPushRelationReq{} }
func (m *UnBindPushRelationReq) String() string { return proto.CompactTextString(m) }
func (*UnBindPushRelationReq) ProtoMessage()    {}
func (*UnBindPushRelationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{12}
}

func (m *UnBindPushRelationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UnBindPushRelationReq.Unmarshal(m, b)
}
func (m *UnBindPushRelationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UnBindPushRelationReq.Marshal(b, m, deterministic)
}
func (m *UnBindPushRelationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UnBindPushRelationReq.Merge(m, src)
}
func (m *UnBindPushRelationReq) XXX_Size() int {
	return xxx_messageInfo_UnBindPushRelationReq.Size(m)
}
func (m *UnBindPushRelationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UnBindPushRelationReq.DiscardUnknown(m)
}

var xxx_messageInfo_UnBindPushRelationReq proto.InternalMessageInfo

func (m *UnBindPushRelationReq) GetNoId() string {
	if m != nil {
		return m.NoId
	}
	return ""
}

type BindPushRelationReq struct {
	// 用户ID
	NoId string `protobuf:"bytes,1,opt,name=noId,proto3" json:"noId,omitempty"`
	// 个推clientId
	Cid string `protobuf:"bytes,2,opt,name=cid,proto3" json:"cid,omitempty"`
	// 别名 本系统以用户的msgId作为别名
	Alias string `protobuf:"bytes,3,opt,name=alias,proto3" json:"alias,omitempty"`
	// 平台 ios/android
	Platform             string   `protobuf:"bytes,4,opt,name=platform,proto3" json:"platform,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *BindPushRelationReq) Reset()         { *m = BindPushRelationReq{} }
func (m *BindPushRelationReq) String() string { return proto.CompactTextString(m) }
func (*BindPushRelationReq) ProtoMessage()    {}
func (*BindPushRelationReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{13}
}

func (m *BindPushRelationReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BindPushRelationReq.Unmarshal(m, b)
}
func (m *BindPushRelationReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BindPushRelationReq.Marshal(b, m, deterministic)
}
func (m *BindPushRelationReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BindPushRelationReq.Merge(m, src)
}
func (m *BindPushRelationReq) XXX_Size() int {
	return xxx_messageInfo_BindPushRelationReq.Size(m)
}
func (m *BindPushRelationReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BindPushRelationReq.DiscardUnknown(m)
}

var xxx_messageInfo_BindPushRelationReq proto.InternalMessageInfo

func (m *BindPushRelationReq) GetNoId() string {
	if m != nil {
		return m.NoId
	}
	return ""
}

func (m *BindPushRelationReq) GetCid() string {
	if m != nil {
		return m.Cid
	}
	return ""
}

func (m *BindPushRelationReq) GetAlias() string {
	if m != nil {
		return m.Alias
	}
	return ""
}

func (m *BindPushRelationReq) GetPlatform() string {
	if m != nil {
		return m.Platform
	}
	return ""
}

type PushByAliasReq struct {
	// 发起推送者
	FromId string `protobuf:"bytes,1,opt,name=fromId,proto3" json:"fromId,omitempty"`
	// 推送目标的别名
	ToIds []string `protobuf:"bytes,2,rep,name=toIds,proto3" json:"toIds,omitempty"`
	// 跳转
	TargetType           TargetType  `protobuf:"varint,3,opt,name=targetType,proto3,enum=user.base.TargetType" json:"targetType,omitempty"`
	PushType             PushType    `protobuf:"varint,4,opt,name=pushType,proto3,enum=user.base.PushType" json:"pushType,omitempty"`
	Attach               *PushAttach `protobuf:"bytes,5,opt,name=attach,proto3" json:"attach,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *PushByAliasReq) Reset()         { *m = PushByAliasReq{} }
func (m *PushByAliasReq) String() string { return proto.CompactTextString(m) }
func (*PushByAliasReq) ProtoMessage()    {}
func (*PushByAliasReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{14}
}

func (m *PushByAliasReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushByAliasReq.Unmarshal(m, b)
}
func (m *PushByAliasReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushByAliasReq.Marshal(b, m, deterministic)
}
func (m *PushByAliasReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushByAliasReq.Merge(m, src)
}
func (m *PushByAliasReq) XXX_Size() int {
	return xxx_messageInfo_PushByAliasReq.Size(m)
}
func (m *PushByAliasReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PushByAliasReq.DiscardUnknown(m)
}

var xxx_messageInfo_PushByAliasReq proto.InternalMessageInfo

func (m *PushByAliasReq) GetFromId() string {
	if m != nil {
		return m.FromId
	}
	return ""
}

func (m *PushByAliasReq) GetToIds() []string {
	if m != nil {
		return m.ToIds
	}
	return nil
}

func (m *PushByAliasReq) GetTargetType() TargetType {
	if m != nil {
		return m.TargetType
	}
	return TargetType_None
}

func (m *PushByAliasReq) GetPushType() PushType {
	if m != nil {
		return m.PushType
	}
	return PushType_Unknown
}

func (m *PushByAliasReq) GetAttach() *PushAttach {
	if m != nil {
		return m.Attach
	}
	return nil
}

type PushAttach struct {
	LiveType             string   `protobuf:"bytes,1,opt,name=liveType,proto3" json:"liveType,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PushAttach) Reset()         { *m = PushAttach{} }
func (m *PushAttach) String() string { return proto.CompactTextString(m) }
func (*PushAttach) ProtoMessage()    {}
func (*PushAttach) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{15}
}

func (m *PushAttach) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PushAttach.Unmarshal(m, b)
}
func (m *PushAttach) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PushAttach.Marshal(b, m, deterministic)
}
func (m *PushAttach) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PushAttach.Merge(m, src)
}
func (m *PushAttach) XXX_Size() int {
	return xxx_messageInfo_PushAttach.Size(m)
}
func (m *PushAttach) XXX_DiscardUnknown() {
	xxx_messageInfo_PushAttach.DiscardUnknown(m)
}

var xxx_messageInfo_PushAttach proto.InternalMessageInfo

func (m *PushAttach) GetLiveType() string {
	if m != nil {
		return m.LiveType
	}
	return ""
}

type EmptyRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *EmptyRsp) Reset()         { *m = EmptyRsp{} }
func (m *EmptyRsp) String() string { return proto.CompactTextString(m) }
func (*EmptyRsp) ProtoMessage()    {}
func (*EmptyRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{16}
}

func (m *EmptyRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EmptyRsp.Unmarshal(m, b)
}
func (m *EmptyRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EmptyRsp.Marshal(b, m, deterministic)
}
func (m *EmptyRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EmptyRsp.Merge(m, src)
}
func (m *EmptyRsp) XXX_Size() int {
	return xxx_messageInfo_EmptyRsp.Size(m)
}
func (m *EmptyRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_EmptyRsp.DiscardUnknown(m)
}

var xxx_messageInfo_EmptyRsp proto.InternalMessageInfo

type GetUserSwitchReq struct {
	NoIds                []string `protobuf:"bytes,1,rep,name=noIds,proto3" json:"noIds,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserSwitchReq) Reset()         { *m = GetUserSwitchReq{} }
func (m *GetUserSwitchReq) String() string { return proto.CompactTextString(m) }
func (*GetUserSwitchReq) ProtoMessage()    {}
func (*GetUserSwitchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{17}
}

func (m *GetUserSwitchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSwitchReq.Unmarshal(m, b)
}
func (m *GetUserSwitchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSwitchReq.Marshal(b, m, deterministic)
}
func (m *GetUserSwitchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSwitchReq.Merge(m, src)
}
func (m *GetUserSwitchReq) XXX_Size() int {
	return xxx_messageInfo_GetUserSwitchReq.Size(m)
}
func (m *GetUserSwitchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSwitchReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSwitchReq proto.InternalMessageInfo

func (m *GetUserSwitchReq) GetNoIds() []string {
	if m != nil {
		return m.NoIds
	}
	return nil
}

type GetUserSwitchRsp struct {
	List                 []*SwitchInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-"`
	XXX_unrecognized     []byte        `json:"-"`
	XXX_sizecache        int32         `json:"-"`
}

func (m *GetUserSwitchRsp) Reset()         { *m = GetUserSwitchRsp{} }
func (m *GetUserSwitchRsp) String() string { return proto.CompactTextString(m) }
func (*GetUserSwitchRsp) ProtoMessage()    {}
func (*GetUserSwitchRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{18}
}

func (m *GetUserSwitchRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserSwitchRsp.Unmarshal(m, b)
}
func (m *GetUserSwitchRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserSwitchRsp.Marshal(b, m, deterministic)
}
func (m *GetUserSwitchRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserSwitchRsp.Merge(m, src)
}
func (m *GetUserSwitchRsp) XXX_Size() int {
	return xxx_messageInfo_GetUserSwitchRsp.Size(m)
}
func (m *GetUserSwitchRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserSwitchRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserSwitchRsp proto.InternalMessageInfo

func (m *GetUserSwitchRsp) GetList() []*SwitchInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type SwitchInfo struct {
	NoId string `protobuf:"bytes,1,opt,name=noId,proto3" json:"noId,omitempty"`
	// 位置开关 on/off
	Position string `protobuf:"bytes,2,opt,name=position,proto3" json:"position,omitempty"`
	// 私聊推送开关 on/off
	PrivateChatPush string `protobuf:"bytes,3,opt,name=privateChatPush,proto3" json:"privateChatPush,omitempty"`
	// 开播推送开关 on/off
	OpenLivePush string `protobuf:"bytes,4,opt,name=openLivePush,proto3" json:"openLivePush,omitempty"`
	// 自动拒绝连麦
	AutoRefuseMultiChat string `protobuf:"bytes,5,opt,name=autoRefuseMultiChat,proto3" json:"autoRefuseMultiChat,omitempty"`
	// 设置拒绝时间
	AutoRefuseTime string `protobuf:"bytes,6,opt,name=autoRefuseTime,proto3" json:"autoRefuseTime,omitempty"`
	// 管理隐身开关 on/off
	AnonymousBrowse      string   `protobuf:"bytes,7,opt,name=anonymousBrowse,proto3" json:"anonymousBrowse,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SwitchInfo) Reset()         { *m = SwitchInfo{} }
func (m *SwitchInfo) String() string { return proto.CompactTextString(m) }
func (*SwitchInfo) ProtoMessage()    {}
func (*SwitchInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{19}
}

func (m *SwitchInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SwitchInfo.Unmarshal(m, b)
}
func (m *SwitchInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SwitchInfo.Marshal(b, m, deterministic)
}
func (m *SwitchInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SwitchInfo.Merge(m, src)
}
func (m *SwitchInfo) XXX_Size() int {
	return xxx_messageInfo_SwitchInfo.Size(m)
}
func (m *SwitchInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SwitchInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SwitchInfo proto.InternalMessageInfo

func (m *SwitchInfo) GetNoId() string {
	if m != nil {
		return m.NoId
	}
	return ""
}

func (m *SwitchInfo) GetPosition() string {
	if m != nil {
		return m.Position
	}
	return ""
}

func (m *SwitchInfo) GetPrivateChatPush() string {
	if m != nil {
		return m.PrivateChatPush
	}
	return ""
}

func (m *SwitchInfo) GetOpenLivePush() string {
	if m != nil {
		return m.OpenLivePush
	}
	return ""
}

func (m *SwitchInfo) GetAutoRefuseMultiChat() string {
	if m != nil {
		return m.AutoRefuseMultiChat
	}
	return ""
}

func (m *SwitchInfo) GetAutoRefuseTime() string {
	if m != nil {
		return m.AutoRefuseTime
	}
	return ""
}

func (m *SwitchInfo) GetAnonymousBrowse() string {
	if m != nil {
		return m.AnonymousBrowse
	}
	return ""
}

type SetUserSwitchReq struct {
	NoId string `protobuf:"bytes,1,opt,name=noId,proto3" json:"noId,omitempty"`
	// 位置开关 on/off
	Position string `protobuf:"bytes,2,opt,name=position,proto3" json:"position,omitempty"`
	// 私聊推送开关 on/off
	PrivateChatPush string `protobuf:"bytes,3,opt,name=privateChatPush,proto3" json:"privateChatPush,omitempty"`
	// 开播推送开关 on/off
	OpenLivePush string `protobuf:"bytes,4,opt,name=openLivePush,proto3" json:"openLivePush,omitempty"`
	// 自动拒绝连麦
	AutoRefuseMultiChat string `protobuf:"bytes,5,opt,name=autoRefuseMultiChat,proto3" json:"autoRefuseMultiChat,omitempty"`
	// 管理隐身开关 on/off
	AnonymousBrowse      string   `protobuf:"bytes,6,opt,name=anonymousBrowse,proto3" json:"anonymousBrowse,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserSwitchReq) Reset()         { *m = SetUserSwitchReq{} }
func (m *SetUserSwitchReq) String() string { return proto.CompactTextString(m) }
func (*SetUserSwitchReq) ProtoMessage()    {}
func (*SetUserSwitchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{20}
}

func (m *SetUserSwitchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserSwitchReq.Unmarshal(m, b)
}
func (m *SetUserSwitchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserSwitchReq.Marshal(b, m, deterministic)
}
func (m *SetUserSwitchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserSwitchReq.Merge(m, src)
}
func (m *SetUserSwitchReq) XXX_Size() int {
	return xxx_messageInfo_SetUserSwitchReq.Size(m)
}
func (m *SetUserSwitchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserSwitchReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserSwitchReq proto.InternalMessageInfo

func (m *SetUserSwitchReq) GetNoId() string {
	if m != nil {
		return m.NoId
	}
	return ""
}

func (m *SetUserSwitchReq) GetPosition() string {
	if m != nil {
		return m.Position
	}
	return ""
}

func (m *SetUserSwitchReq) GetPrivateChatPush() string {
	if m != nil {
		return m.PrivateChatPush
	}
	return ""
}

func (m *SetUserSwitchReq) GetOpenLivePush() string {
	if m != nil {
		return m.OpenLivePush
	}
	return ""
}

func (m *SetUserSwitchReq) GetAutoRefuseMultiChat() string {
	if m != nil {
		return m.AutoRefuseMultiChat
	}
	return ""
}

func (m *SetUserSwitchReq) GetAnonymousBrowse() string {
	if m != nil {
		return m.AnonymousBrowse
	}
	return ""
}

type SetUserSwitchRsp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetUserSwitchRsp) Reset()         { *m = SetUserSwitchRsp{} }
func (m *SetUserSwitchRsp) String() string { return proto.CompactTextString(m) }
func (*SetUserSwitchRsp) ProtoMessage()    {}
func (*SetUserSwitchRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{21}
}

func (m *SetUserSwitchRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetUserSwitchRsp.Unmarshal(m, b)
}
func (m *SetUserSwitchRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetUserSwitchRsp.Marshal(b, m, deterministic)
}
func (m *SetUserSwitchRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetUserSwitchRsp.Merge(m, src)
}
func (m *SetUserSwitchRsp) XXX_Size() int {
	return xxx_messageInfo_SetUserSwitchRsp.Size(m)
}
func (m *SetUserSwitchRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetUserSwitchRsp.DiscardUnknown(m)
}

var xxx_messageInfo_SetUserSwitchRsp proto.InternalMessageInfo

type GetUserFansListReq struct {
	NoId                 string   `protobuf:"bytes,1,opt,name=noId,proto3" json:"noId,omitempty"`
	PageNum              int64    `protobuf:"varint,2,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize             int64    `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserFansListReq) Reset()         { *m = GetUserFansListReq{} }
func (m *GetUserFansListReq) String() string { return proto.CompactTextString(m) }
func (*GetUserFansListReq) ProtoMessage()    {}
func (*GetUserFansListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{22}
}

func (m *GetUserFansListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserFansListReq.Unmarshal(m, b)
}
func (m *GetUserFansListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserFansListReq.Marshal(b, m, deterministic)
}
func (m *GetUserFansListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserFansListReq.Merge(m, src)
}
func (m *GetUserFansListReq) XXX_Size() int {
	return xxx_messageInfo_GetUserFansListReq.Size(m)
}
func (m *GetUserFansListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserFansListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserFansListReq proto.InternalMessageInfo

func (m *GetUserFansListReq) GetNoId() string {
	if m != nil {
		return m.NoId
	}
	return ""
}

func (m *GetUserFansListReq) GetPageNum() int64 {
	if m != nil {
		return m.PageNum
	}
	return 0
}

func (m *GetUserFansListReq) GetPageSize() int64 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type GetUserFansListRsp struct {
	List                 []*SessionInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-"`
	XXX_unrecognized     []byte         `json:"-"`
	XXX_sizecache        int32          `json:"-"`
}

func (m *GetUserFansListRsp) Reset()         { *m = GetUserFansListRsp{} }
func (m *GetUserFansListRsp) String() string { return proto.CompactTextString(m) }
func (*GetUserFansListRsp) ProtoMessage()    {}
func (*GetUserFansListRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{23}
}

func (m *GetUserFansListRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserFansListRsp.Unmarshal(m, b)
}
func (m *GetUserFansListRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserFansListRsp.Marshal(b, m, deterministic)
}
func (m *GetUserFansListRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserFansListRsp.Merge(m, src)
}
func (m *GetUserFansListRsp) XXX_Size() int {
	return xxx_messageInfo_GetUserFansListRsp.Size(m)
}
func (m *GetUserFansListRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserFansListRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserFansListRsp proto.InternalMessageInfo

func (m *GetUserFansListRsp) GetList() []*SessionInfo {
	if m != nil {
		return m.List
	}
	return nil
}

type SessionInfo struct {
	FromId               string   `protobuf:"bytes,1,opt,name=fromId,proto3" json:"fromId,omitempty"`
	ToId                 string   `protobuf:"bytes,2,opt,name=toId,proto3" json:"toId,omitempty"`
	Relation             string   `protobuf:"bytes,3,opt,name=relation,proto3" json:"relation,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SessionInfo) Reset()         { *m = SessionInfo{} }
func (m *SessionInfo) String() string { return proto.CompactTextString(m) }
func (*SessionInfo) ProtoMessage()    {}
func (*SessionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{24}
}

func (m *SessionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SessionInfo.Unmarshal(m, b)
}
func (m *SessionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SessionInfo.Marshal(b, m, deterministic)
}
func (m *SessionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SessionInfo.Merge(m, src)
}
func (m *SessionInfo) XXX_Size() int {
	return xxx_messageInfo_SessionInfo.Size(m)
}
func (m *SessionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SessionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SessionInfo proto.InternalMessageInfo

func (m *SessionInfo) GetFromId() string {
	if m != nil {
		return m.FromId
	}
	return ""
}

func (m *SessionInfo) GetToId() string {
	if m != nil {
		return m.ToId
	}
	return ""
}

func (m *SessionInfo) GetRelation() string {
	if m != nil {
		return m.Relation
	}
	return ""
}

type OnlineNumReq struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OnlineNumReq) Reset()         { *m = OnlineNumReq{} }
func (m *OnlineNumReq) String() string { return proto.CompactTextString(m) }
func (*OnlineNumReq) ProtoMessage()    {}
func (*OnlineNumReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{25}
}

func (m *OnlineNumReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OnlineNumReq.Unmarshal(m, b)
}
func (m *OnlineNumReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OnlineNumReq.Marshal(b, m, deterministic)
}
func (m *OnlineNumReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OnlineNumReq.Merge(m, src)
}
func (m *OnlineNumReq) XXX_Size() int {
	return xxx_messageInfo_OnlineNumReq.Size(m)
}
func (m *OnlineNumReq) XXX_DiscardUnknown() {
	xxx_messageInfo_OnlineNumReq.DiscardUnknown(m)
}

var xxx_messageInfo_OnlineNumReq proto.InternalMessageInfo

type OnlineNumRsp struct {
	Count                int64    `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *OnlineNumRsp) Reset()         { *m = OnlineNumRsp{} }
func (m *OnlineNumRsp) String() string { return proto.CompactTextString(m) }
func (*OnlineNumRsp) ProtoMessage()    {}
func (*OnlineNumRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{26}
}

func (m *OnlineNumRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OnlineNumRsp.Unmarshal(m, b)
}
func (m *OnlineNumRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OnlineNumRsp.Marshal(b, m, deterministic)
}
func (m *OnlineNumRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OnlineNumRsp.Merge(m, src)
}
func (m *OnlineNumRsp) XXX_Size() int {
	return xxx_messageInfo_OnlineNumRsp.Size(m)
}
func (m *OnlineNumRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_OnlineNumRsp.DiscardUnknown(m)
}

var xxx_messageInfo_OnlineNumRsp proto.InternalMessageInfo

func (m *OnlineNumRsp) GetCount() int64 {
	if m != nil {
		return m.Count
	}
	return 0
}

type RobotAddReq struct {
	Robot                *Robot       `protobuf:"bytes,1,opt,name=robot,proto3" json:"robot,omitempty"`
	Mode                 int32        `protobuf:"varint,2,opt,name=mode,proto3" json:"mode,omitempty"`
	RobotConfig          *RobotConfig `protobuf:"bytes,3,opt,name=robotConfig,proto3" json:"robotConfig,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *RobotAddReq) Reset()         { *m = RobotAddReq{} }
func (m *RobotAddReq) String() string { return proto.CompactTextString(m) }
func (*RobotAddReq) ProtoMessage()    {}
func (*RobotAddReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{27}
}

func (m *RobotAddReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RobotAddReq.Unmarshal(m, b)
}
func (m *RobotAddReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RobotAddReq.Marshal(b, m, deterministic)
}
func (m *RobotAddReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RobotAddReq.Merge(m, src)
}
func (m *RobotAddReq) XXX_Size() int {
	return xxx_messageInfo_RobotAddReq.Size(m)
}
func (m *RobotAddReq) XXX_DiscardUnknown() {
	xxx_messageInfo_RobotAddReq.DiscardUnknown(m)
}

var xxx_messageInfo_RobotAddReq proto.InternalMessageInfo

func (m *RobotAddReq) GetRobot() *Robot {
	if m != nil {
		return m.Robot
	}
	return nil
}

func (m *RobotAddReq) GetMode() int32 {
	if m != nil {
		return m.Mode
	}
	return 0
}

func (m *RobotAddReq) GetRobotConfig() *RobotConfig {
	if m != nil {
		return m.RobotConfig
	}
	return nil
}

type RobotConfig struct {
	Num                  int32    `protobuf:"varint,1,opt,name=num,proto3" json:"num,omitempty"`
	WealthMin            int32    `protobuf:"varint,2,opt,name=wealthMin,proto3" json:"wealthMin,omitempty"`
	WealthMax            int32    `protobuf:"varint,3,opt,name=wealthMax,proto3" json:"wealthMax,omitempty"`
	Male                 int32    `protobuf:"varint,4,opt,name=male,proto3" json:"male,omitempty"`
	Female               int32    `protobuf:"varint,5,opt,name=female,proto3" json:"female,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RobotConfig) Reset()         { *m = RobotConfig{} }
func (m *RobotConfig) String() string { return proto.CompactTextString(m) }
func (*RobotConfig) ProtoMessage()    {}
func (*RobotConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{28}
}

func (m *RobotConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RobotConfig.Unmarshal(m, b)
}
func (m *RobotConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RobotConfig.Marshal(b, m, deterministic)
}
func (m *RobotConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RobotConfig.Merge(m, src)
}
func (m *RobotConfig) XXX_Size() int {
	return xxx_messageInfo_RobotConfig.Size(m)
}
func (m *RobotConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_RobotConfig.DiscardUnknown(m)
}

var xxx_messageInfo_RobotConfig proto.InternalMessageInfo

func (m *RobotConfig) GetNum() int32 {
	if m != nil {
		return m.Num
	}
	return 0
}

func (m *RobotConfig) GetWealthMin() int32 {
	if m != nil {
		return m.WealthMin
	}
	return 0
}

func (m *RobotConfig) GetWealthMax() int32 {
	if m != nil {
		return m.WealthMax
	}
	return 0
}

func (m *RobotConfig) GetMale() int32 {
	if m != nil {
		return m.Male
	}
	return 0
}

func (m *RobotConfig) GetFemale() int32 {
	if m != nil {
		return m.Female
	}
	return 0
}

type Robot struct {
	NickName             string   `protobuf:"bytes,1,opt,name=nickName,proto3" json:"nickName,omitempty"`
	Gender               string   `protobuf:"bytes,2,opt,name=gender,proto3" json:"gender,omitempty"`
	Born                 string   `protobuf:"bytes,3,opt,name=born,proto3" json:"born,omitempty"`
	City                 string   `protobuf:"bytes,4,opt,name=city,proto3" json:"city,omitempty"`
	WealthLevel          int32    `protobuf:"varint,5,opt,name=wealthLevel,proto3" json:"wealthLevel,omitempty"`
	StarSign             string   `protobuf:"bytes,6,opt,name=starSign,proto3" json:"starSign,omitempty"`
	Height               int32    `protobuf:"varint,7,opt,name=height,proto3" json:"height,omitempty"`
	Weight               int32    `protobuf:"varint,8,opt,name=weight,proto3" json:"weight,omitempty"`
	AvatarUrl            string   `protobuf:"bytes,9,opt,name=avatarUrl,proto3" json:"avatarUrl,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *Robot) Reset()         { *m = Robot{} }
func (m *Robot) String() string { return proto.CompactTextString(m) }
func (*Robot) ProtoMessage()    {}
func (*Robot) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{29}
}

func (m *Robot) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Robot.Unmarshal(m, b)
}
func (m *Robot) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Robot.Marshal(b, m, deterministic)
}
func (m *Robot) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Robot.Merge(m, src)
}
func (m *Robot) XXX_Size() int {
	return xxx_messageInfo_Robot.Size(m)
}
func (m *Robot) XXX_DiscardUnknown() {
	xxx_messageInfo_Robot.DiscardUnknown(m)
}

var xxx_messageInfo_Robot proto.InternalMessageInfo

func (m *Robot) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *Robot) GetGender() string {
	if m != nil {
		return m.Gender
	}
	return ""
}

func (m *Robot) GetBorn() string {
	if m != nil {
		return m.Born
	}
	return ""
}

func (m *Robot) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *Robot) GetWealthLevel() int32 {
	if m != nil {
		return m.WealthLevel
	}
	return 0
}

func (m *Robot) GetStarSign() string {
	if m != nil {
		return m.StarSign
	}
	return ""
}

func (m *Robot) GetHeight() int32 {
	if m != nil {
		return m.Height
	}
	return 0
}

func (m *Robot) GetWeight() int32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *Robot) GetAvatarUrl() string {
	if m != nil {
		return m.AvatarUrl
	}
	return ""
}

type RobotAddRsp struct {
	Status               bool         `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Success              int32        `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	Failure              int32        `protobuf:"varint,3,opt,name=failure,proto3" json:"failure,omitempty"`
	RobotInfos           []*RobotInfo `protobuf:"bytes,4,rep,name=robotInfos,proto3" json:"robotInfos,omitempty"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-"`
	XXX_unrecognized     []byte       `json:"-"`
	XXX_sizecache        int32        `json:"-"`
}

func (m *RobotAddRsp) Reset()         { *m = RobotAddRsp{} }
func (m *RobotAddRsp) String() string { return proto.CompactTextString(m) }
func (*RobotAddRsp) ProtoMessage()    {}
func (*RobotAddRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{30}
}

func (m *RobotAddRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RobotAddRsp.Unmarshal(m, b)
}
func (m *RobotAddRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RobotAddRsp.Marshal(b, m, deterministic)
}
func (m *RobotAddRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RobotAddRsp.Merge(m, src)
}
func (m *RobotAddRsp) XXX_Size() int {
	return xxx_messageInfo_RobotAddRsp.Size(m)
}
func (m *RobotAddRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_RobotAddRsp.DiscardUnknown(m)
}

var xxx_messageInfo_RobotAddRsp proto.InternalMessageInfo

func (m *RobotAddRsp) GetStatus() bool {
	if m != nil {
		return m.Status
	}
	return false
}

func (m *RobotAddRsp) GetSuccess() int32 {
	if m != nil {
		return m.Success
	}
	return 0
}

func (m *RobotAddRsp) GetFailure() int32 {
	if m != nil {
		return m.Failure
	}
	return 0
}

func (m *RobotAddRsp) GetRobotInfos() []*RobotInfo {
	if m != nil {
		return m.RobotInfos
	}
	return nil
}

type RobotInfo struct {
	MsgId                string   `protobuf:"bytes,1,opt,name=msgId,proto3" json:"msgId,omitempty"`
	NoId                 string   `protobuf:"bytes,2,opt,name=noId,proto3" json:"noId,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *RobotInfo) Reset()         { *m = RobotInfo{} }
func (m *RobotInfo) String() string { return proto.CompactTextString(m) }
func (*RobotInfo) ProtoMessage()    {}
func (*RobotInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{31}
}

func (m *RobotInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_RobotInfo.Unmarshal(m, b)
}
func (m *RobotInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_RobotInfo.Marshal(b, m, deterministic)
}
func (m *RobotInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RobotInfo.Merge(m, src)
}
func (m *RobotInfo) XXX_Size() int {
	return xxx_messageInfo_RobotInfo.Size(m)
}
func (m *RobotInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RobotInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RobotInfo proto.InternalMessageInfo

func (m *RobotInfo) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

func (m *RobotInfo) GetNoId() string {
	if m != nil {
		return m.NoId
	}
	return ""
}

type SetOnlineUserInfoReq struct {
	Gender               string   `protobuf:"bytes,1,opt,name=gender,proto3" json:"gender,omitempty"`
	NoId                 string   `protobuf:"bytes,2,opt,name=noId,proto3" json:"noId,omitempty"`
	User                 string   `protobuf:"bytes,3,opt,name=user,proto3" json:"user,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetOnlineUserInfoReq) Reset()         { *m = SetOnlineUserInfoReq{} }
func (m *SetOnlineUserInfoReq) String() string { return proto.CompactTextString(m) }
func (*SetOnlineUserInfoReq) ProtoMessage()    {}
func (*SetOnlineUserInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{32}
}

func (m *SetOnlineUserInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetOnlineUserInfoReq.Unmarshal(m, b)
}
func (m *SetOnlineUserInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetOnlineUserInfoReq.Marshal(b, m, deterministic)
}
func (m *SetOnlineUserInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetOnlineUserInfoReq.Merge(m, src)
}
func (m *SetOnlineUserInfoReq) XXX_Size() int {
	return xxx_messageInfo_SetOnlineUserInfoReq.Size(m)
}
func (m *SetOnlineUserInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SetOnlineUserInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_SetOnlineUserInfoReq proto.InternalMessageInfo

func (m *SetOnlineUserInfoReq) GetGender() string {
	if m != nil {
		return m.Gender
	}
	return ""
}

func (m *SetOnlineUserInfoReq) GetNoId() string {
	if m != nil {
		return m.NoId
	}
	return ""
}

func (m *SetOnlineUserInfoReq) GetUser() string {
	if m != nil {
		return m.User
	}
	return ""
}

type SetOnlineUserInfoReqRsp struct {
	Status               bool     `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *SetOnlineUserInfoReqRsp) Reset()         { *m = SetOnlineUserInfoReqRsp{} }
func (m *SetOnlineUserInfoReqRsp) String() string { return proto.CompactTextString(m) }
func (*SetOnlineUserInfoReqRsp) ProtoMessage()    {}
func (*SetOnlineUserInfoReqRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{33}
}

func (m *SetOnlineUserInfoReqRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SetOnlineUserInfoReqRsp.Unmarshal(m, b)
}
func (m *SetOnlineUserInfoReqRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SetOnlineUserInfoReqRsp.Marshal(b, m, deterministic)
}
func (m *SetOnlineUserInfoReqRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SetOnlineUserInfoReqRsp.Merge(m, src)
}
func (m *SetOnlineUserInfoReqRsp) XXX_Size() int {
	return xxx_messageInfo_SetOnlineUserInfoReqRsp.Size(m)
}
func (m *SetOnlineUserInfoReqRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_SetOnlineUserInfoReqRsp.DiscardUnknown(m)
}

var xxx_messageInfo_SetOnlineUserInfoReqRsp proto.InternalMessageInfo

func (m *SetOnlineUserInfoReqRsp) GetStatus() bool {
	if m != nil {
		return m.Status
	}
	return false
}

//token获取 _id
type TokenReq struct {
	Token                string   `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TokenReq) Reset()         { *m = TokenReq{} }
func (m *TokenReq) String() string { return proto.CompactTextString(m) }
func (*TokenReq) ProtoMessage()    {}
func (*TokenReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{34}
}

func (m *TokenReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TokenReq.Unmarshal(m, b)
}
func (m *TokenReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TokenReq.Marshal(b, m, deterministic)
}
func (m *TokenReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TokenReq.Merge(m, src)
}
func (m *TokenReq) XXX_Size() int {
	return xxx_messageInfo_TokenReq.Size(m)
}
func (m *TokenReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TokenReq.DiscardUnknown(m)
}

var xxx_messageInfo_TokenReq proto.InternalMessageInfo

func (m *TokenReq) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

type TokenRes struct {
	Id                   string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *TokenRes) Reset()         { *m = TokenRes{} }
func (m *TokenRes) String() string { return proto.CompactTextString(m) }
func (*TokenRes) ProtoMessage()    {}
func (*TokenRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{35}
}

func (m *TokenRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TokenRes.Unmarshal(m, b)
}
func (m *TokenRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TokenRes.Marshal(b, m, deterministic)
}
func (m *TokenRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TokenRes.Merge(m, src)
}
func (m *TokenRes) XXX_Size() int {
	return xxx_messageInfo_TokenRes.Size(m)
}
func (m *TokenRes) XXX_DiscardUnknown() {
	xxx_messageInfo_TokenRes.DiscardUnknown(m)
}

var xxx_messageInfo_TokenRes proto.InternalMessageInfo

func (m *TokenRes) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type UserReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=Id,proto3" json:"Id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserReq) Reset()         { *m = UserReq{} }
func (m *UserReq) String() string { return proto.CompactTextString(m) }
func (*UserReq) ProtoMessage()    {}
func (*UserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{36}
}

func (m *UserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserReq.Unmarshal(m, b)
}
func (m *UserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserReq.Marshal(b, m, deterministic)
}
func (m *UserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserReq.Merge(m, src)
}
func (m *UserReq) XXX_Size() int {
	return xxx_messageInfo_UserReq.Size(m)
}
func (m *UserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserReq proto.InternalMessageInfo

func (m *UserReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

type UserInfoRes struct {
	Id                   string   `protobuf:"bytes,1,opt,name=Id,proto3" json:"Id,omitempty"`
	Nickname             string   `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Born                 string   `protobuf:"bytes,3,opt,name=born,proto3" json:"born,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserInfoRes) Reset()         { *m = UserInfoRes{} }
func (m *UserInfoRes) String() string { return proto.CompactTextString(m) }
func (*UserInfoRes) ProtoMessage()    {}
func (*UserInfoRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{37}
}

func (m *UserInfoRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfoRes.Unmarshal(m, b)
}
func (m *UserInfoRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfoRes.Marshal(b, m, deterministic)
}
func (m *UserInfoRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfoRes.Merge(m, src)
}
func (m *UserInfoRes) XXX_Size() int {
	return xxx_messageInfo_UserInfoRes.Size(m)
}
func (m *UserInfoRes) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfoRes.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfoRes proto.InternalMessageInfo

func (m *UserInfoRes) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *UserInfoRes) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *UserInfoRes) GetBorn() string {
	if m != nil {
		return m.Born
	}
	return ""
}

//通过"noId"获取用户详情信息
type UserIdsReq struct {
	Ids                  []string `protobuf:"bytes,1,rep,name=Ids,proto3" json:"Ids,omitempty"`
	Cols                 []string `protobuf:"bytes,2,rep,name=cols,proto3" json:"cols,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserIdsReq) Reset()         { *m = UserIdsReq{} }
func (m *UserIdsReq) String() string { return proto.CompactTextString(m) }
func (*UserIdsReq) ProtoMessage()    {}
func (*UserIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{38}
}

func (m *UserIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserIdsReq.Unmarshal(m, b)
}
func (m *UserIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserIdsReq.Marshal(b, m, deterministic)
}
func (m *UserIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserIdsReq.Merge(m, src)
}
func (m *UserIdsReq) XXX_Size() int {
	return xxx_messageInfo_UserIdsReq.Size(m)
}
func (m *UserIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserIdsReq proto.InternalMessageInfo

func (m *UserIdsReq) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

func (m *UserIdsReq) GetCols() []string {
	if m != nil {
		return m.Cols
	}
	return nil
}

//通过"_id"获取用户详情信息
type MongoIdsReq struct {
	Ids                  []string `protobuf:"bytes,1,rep,name=Ids,proto3" json:"Ids,omitempty"`
	Cols                 []string `protobuf:"bytes,2,rep,name=cols,proto3" json:"cols,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MongoIdsReq) Reset()         { *m = MongoIdsReq{} }
func (m *MongoIdsReq) String() string { return proto.CompactTextString(m) }
func (*MongoIdsReq) ProtoMessage()    {}
func (*MongoIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{39}
}

func (m *MongoIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MongoIdsReq.Unmarshal(m, b)
}
func (m *MongoIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MongoIdsReq.Marshal(b, m, deterministic)
}
func (m *MongoIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MongoIdsReq.Merge(m, src)
}
func (m *MongoIdsReq) XXX_Size() int {
	return xxx_messageInfo_MongoIdsReq.Size(m)
}
func (m *MongoIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MongoIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_MongoIdsReq proto.InternalMessageInfo

func (m *MongoIdsReq) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

func (m *MongoIdsReq) GetCols() []string {
	if m != nil {
		return m.Cols
	}
	return nil
}

//通过"msgId"获取用户详情信息
type MsgIdsReq struct {
	Ids                  []string `protobuf:"bytes,1,rep,name=Ids,proto3" json:"Ids,omitempty"`
	Cols                 []string `protobuf:"bytes,2,rep,name=cols,proto3" json:"cols,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *MsgIdsReq) Reset()         { *m = MsgIdsReq{} }
func (m *MsgIdsReq) String() string { return proto.CompactTextString(m) }
func (*MsgIdsReq) ProtoMessage()    {}
func (*MsgIdsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{40}
}

func (m *MsgIdsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MsgIdsReq.Unmarshal(m, b)
}
func (m *MsgIdsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MsgIdsReq.Marshal(b, m, deterministic)
}
func (m *MsgIdsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MsgIdsReq.Merge(m, src)
}
func (m *MsgIdsReq) XXX_Size() int {
	return xxx_messageInfo_MsgIdsReq.Size(m)
}
func (m *MsgIdsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MsgIdsReq.DiscardUnknown(m)
}

var xxx_messageInfo_MsgIdsReq proto.InternalMessageInfo

func (m *MsgIdsReq) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

func (m *MsgIdsReq) GetCols() []string {
	if m != nil {
		return m.Cols
	}
	return nil
}

type UserInfoRsp struct {
	Users                []*UserInfo `protobuf:"bytes,1,rep,name=Users,proto3" json:"Users,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *UserInfoRsp) Reset()         { *m = UserInfoRsp{} }
func (m *UserInfoRsp) String() string { return proto.CompactTextString(m) }
func (*UserInfoRsp) ProtoMessage()    {}
func (*UserInfoRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{41}
}

func (m *UserInfoRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfoRsp.Unmarshal(m, b)
}
func (m *UserInfoRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfoRsp.Marshal(b, m, deterministic)
}
func (m *UserInfoRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfoRsp.Merge(m, src)
}
func (m *UserInfoRsp) XXX_Size() int {
	return xxx_messageInfo_UserInfoRsp.Size(m)
}
func (m *UserInfoRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfoRsp.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfoRsp proto.InternalMessageInfo

func (m *UserInfoRsp) GetUsers() []*UserInfo {
	if m != nil {
		return m.Users
	}
	return nil
}

//更新用户信息
type UpdateUserReq struct {
	Id                   string   `protobuf:"bytes,1,opt,name=Id,proto3" json:"Id,omitempty"`
	Data                 []byte   `protobuf:"bytes,2,opt,name=Data,proto3" json:"Data,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateUserReq) Reset()         { *m = UpdateUserReq{} }
func (m *UpdateUserReq) String() string { return proto.CompactTextString(m) }
func (*UpdateUserReq) ProtoMessage()    {}
func (*UpdateUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{42}
}

func (m *UpdateUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateUserReq.Unmarshal(m, b)
}
func (m *UpdateUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateUserReq.Marshal(b, m, deterministic)
}
func (m *UpdateUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateUserReq.Merge(m, src)
}
func (m *UpdateUserReq) XXX_Size() int {
	return xxx_messageInfo_UpdateUserReq.Size(m)
}
func (m *UpdateUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateUserReq proto.InternalMessageInfo

func (m *UpdateUserReq) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *UpdateUserReq) GetData() []byte {
	if m != nil {
		return m.Data
	}
	return nil
}

//查询用户是否存在
type UserExistReq struct {
	NoId                 string   `protobuf:"bytes,1,opt,name=NoId,proto3" json:"NoId,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserExistReq) Reset()         { *m = UserExistReq{} }
func (m *UserExistReq) String() string { return proto.CompactTextString(m) }
func (*UserExistReq) ProtoMessage()    {}
func (*UserExistReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{43}
}

func (m *UserExistReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserExistReq.Unmarshal(m, b)
}
func (m *UserExistReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserExistReq.Marshal(b, m, deterministic)
}
func (m *UserExistReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserExistReq.Merge(m, src)
}
func (m *UserExistReq) XXX_Size() int {
	return xxx_messageInfo_UserExistReq.Size(m)
}
func (m *UserExistReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserExistReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserExistReq proto.InternalMessageInfo

func (m *UserExistReq) GetNoId() string {
	if m != nil {
		return m.NoId
	}
	return ""
}

type UserExistRsp struct {
	Exist                int32    `protobuf:"varint,1,opt,name=Exist,proto3" json:"Exist,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserExistRsp) Reset()         { *m = UserExistRsp{} }
func (m *UserExistRsp) String() string { return proto.CompactTextString(m) }
func (*UserExistRsp) ProtoMessage()    {}
func (*UserExistRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{44}
}

func (m *UserExistRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserExistRsp.Unmarshal(m, b)
}
func (m *UserExistRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserExistRsp.Marshal(b, m, deterministic)
}
func (m *UserExistRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserExistRsp.Merge(m, src)
}
func (m *UserExistRsp) XXX_Size() int {
	return xxx_messageInfo_UserExistRsp.Size(m)
}
func (m *UserExistRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserExistRsp.DiscardUnknown(m)
}

var xxx_messageInfo_UserExistRsp proto.InternalMessageInfo

func (m *UserExistRsp) GetExist() int32 {
	if m != nil {
		return m.Exist
	}
	return 0
}

//详情见User模型
type UserInfo struct {
	Id                   string     `protobuf:"bytes,1,opt,name=Id,proto3" json:"Id,omitempty"`
	NoId                 string     `protobuf:"bytes,2,opt,name=NoId,proto3" json:"NoId,omitempty"`
	Status               int32      `protobuf:"varint,3,opt,name=Status,proto3" json:"Status,omitempty"`
	NickName             string     `protobuf:"bytes,4,opt,name=NickName,proto3" json:"NickName,omitempty"`
	Gender               string     `protobuf:"bytes,5,opt,name=Gender,proto3" json:"Gender,omitempty"`
	Age                  int32      `protobuf:"varint,6,opt,name=Age,proto3" json:"Age,omitempty"`
	Wealth               int64      `protobuf:"varint,7,opt,name=Wealth,proto3" json:"Wealth,omitempty"`
	Charm                int64      `protobuf:"varint,8,opt,name=Charm,proto3" json:"Charm,omitempty"`
	Like                 int32      `protobuf:"varint,9,opt,name=Like,proto3" json:"Like,omitempty"`
	StarSign             string     `protobuf:"bytes,10,opt,name=StarSign,proto3" json:"StarSign,omitempty"`
	Birthday             string     `protobuf:"bytes,11,opt,name=Birthday,proto3" json:"Birthday,omitempty"`
	Feeling              string     `protobuf:"bytes,13,opt,name=Feeling,proto3" json:"Feeling,omitempty"`
	Height               int32      `protobuf:"varint,14,opt,name=Height,proto3" json:"Height,omitempty"`
	Weight               int32      `protobuf:"varint,15,opt,name=Weight,proto3" json:"Weight,omitempty"`
	ImgList              string     `protobuf:"bytes,16,opt,name=ImgList,proto3" json:"ImgList,omitempty"`
	RealStatus           string     `protobuf:"bytes,17,opt,name=RealStatus,proto3" json:"RealStatus,omitempty"`
	AvatarUrl            string     `protobuf:"bytes,18,opt,name=AvatarUrl,proto3" json:"AvatarUrl,omitempty"`
	AudioUrl             string     `protobuf:"bytes,19,opt,name=AudioUrl,proto3" json:"AudioUrl,omitempty"`
	Education            string     `protobuf:"bytes,20,opt,name=Education,proto3" json:"Education,omitempty"`
	Job                  string     `protobuf:"bytes,21,opt,name=Job,proto3" json:"Job,omitempty"`
	Income               string     `protobuf:"bytes,22,opt,name=Income,proto3" json:"Income,omitempty"`
	Province             string     `protobuf:"bytes,23,opt,name=Province,proto3" json:"Province,omitempty"`
	City                 string     `protobuf:"bytes,24,opt,name=City,proto3" json:"City,omitempty"`
	Area                 string     `protobuf:"bytes,25,opt,name=Area,proto3" json:"Area,omitempty"`
	HomeTown             string     `protobuf:"bytes,26,opt,name=HomeTown,proto3" json:"HomeTown,omitempty"`
	RealNameStatus       string     `protobuf:"bytes,27,opt,name=RealNameStatus,proto3" json:"RealNameStatus,omitempty"`
	Intro                string     `protobuf:"bytes,28,opt,name=Intro,proto3" json:"Intro,omitempty"`
	Lon                  float64    `protobuf:"fixed64,29,opt,name=Lon,proto3" json:"Lon,omitempty"`
	Lat                  float64    `protobuf:"fixed64,30,opt,name=Lat,proto3" json:"Lat,omitempty"`
	IsOnline             int32      `protobuf:"varint,31,opt,name=IsOnline,proto3" json:"IsOnline,omitempty"`
	LastLoginTime        string     `protobuf:"bytes,32,opt,name=LastLoginTime,proto3" json:"LastLoginTime,omitempty"`
	CharmLevel           int32      `protobuf:"varint,33,opt,name=CharmLevel,proto3" json:"CharmLevel,omitempty"`
	RegistryType         string     `protobuf:"bytes,34,opt,name=RegistryType,proto3" json:"RegistryType,omitempty"`
	Uid                  string     `protobuf:"bytes,35,opt,name=Uid,proto3" json:"Uid,omitempty"`
	UnionId              string     `protobuf:"bytes,36,opt,name=UnionId,proto3" json:"UnionId,omitempty"`
	RegisterStatus       string     `protobuf:"bytes,37,opt,name=RegisterStatus,proto3" json:"RegisterStatus,omitempty"`
	InviteCode           string     `protobuf:"bytes,38,opt,name=InviteCode,proto3" json:"InviteCode,omitempty"`
	Born                 string     `protobuf:"bytes,39,opt,name=Born,proto3" json:"Born,omitempty"`
	MsgId                string     `protobuf:"bytes,40,opt,name=MsgId,proto3" json:"MsgId,omitempty"`
	RcToken              string     `protobuf:"bytes,41,opt,name=RcToken,proto3" json:"RcToken,omitempty"`
	WealthLevel          int32      `protobuf:"varint,42,opt,name=WealthLevel,proto3" json:"WealthLevel,omitempty"`
	Album                []*UserImg `protobuf:"bytes,43,rep,name=Album,proto3" json:"Album,omitempty"`
	Duration             int32      `protobuf:"varint,44,opt,name=Duration,proto3" json:"Duration,omitempty"`
	AvatarStatus         string     `protobuf:"bytes,45,opt,name=AvatarStatus,proto3" json:"AvatarStatus,omitempty"`
	AudioStatus          string     `protobuf:"bytes,46,opt,name=AudioStatus,proto3" json:"AudioStatus,omitempty"`
	GroupCode            string     `protobuf:"bytes,47,opt,name=GroupCode,proto3" json:"GroupCode,omitempty"`
	AvatarLevel          string     `protobuf:"bytes,48,opt,name=AvatarLevel,proto3" json:"AvatarLevel,omitempty"`
	SMResult             string     `protobuf:"bytes,49,opt,name=SMResult,proto3" json:"SMResult,omitempty"`
	SMReason             string     `protobuf:"bytes,50,opt,name=SMReason,proto3" json:"SMReason,omitempty"`
	IdName               string     `protobuf:"bytes,51,opt,name=IdName,proto3" json:"IdName,omitempty"`
	ContractSignStatus   string     `protobuf:"bytes,52,opt,name=ContractSignStatus,proto3" json:"ContractSignStatus,omitempty"`
	SuperAdmin           string     `protobuf:"bytes,53,opt,name=SuperAdmin,proto3" json:"SuperAdmin,omitempty"`
	IdCard               string     `protobuf:"bytes,54,opt,name=IdCard,proto3" json:"IdCard,omitempty"`
	Mobile               string     `protobuf:"bytes,55,opt,name=Mobile,proto3" json:"Mobile,omitempty"`
	CheckAvatar          string     `protobuf:"bytes,56,opt,name=CheckAvatar,proto3" json:"CheckAvatar,omitempty"`
	Robot                string     `protobuf:"bytes,57,opt,name=Robot,proto3" json:"Robot,omitempty"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-"`
	XXX_unrecognized     []byte     `json:"-"`
	XXX_sizecache        int32      `json:"-"`
}

func (m *UserInfo) Reset()         { *m = UserInfo{} }
func (m *UserInfo) String() string { return proto.CompactTextString(m) }
func (*UserInfo) ProtoMessage()    {}
func (*UserInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{45}
}

func (m *UserInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfo.Unmarshal(m, b)
}
func (m *UserInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfo.Marshal(b, m, deterministic)
}
func (m *UserInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfo.Merge(m, src)
}
func (m *UserInfo) XXX_Size() int {
	return xxx_messageInfo_UserInfo.Size(m)
}
func (m *UserInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfo.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfo proto.InternalMessageInfo

func (m *UserInfo) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *UserInfo) GetNoId() string {
	if m != nil {
		return m.NoId
	}
	return ""
}

func (m *UserInfo) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *UserInfo) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *UserInfo) GetGender() string {
	if m != nil {
		return m.Gender
	}
	return ""
}

func (m *UserInfo) GetAge() int32 {
	if m != nil {
		return m.Age
	}
	return 0
}

func (m *UserInfo) GetWealth() int64 {
	if m != nil {
		return m.Wealth
	}
	return 0
}

func (m *UserInfo) GetCharm() int64 {
	if m != nil {
		return m.Charm
	}
	return 0
}

func (m *UserInfo) GetLike() int32 {
	if m != nil {
		return m.Like
	}
	return 0
}

func (m *UserInfo) GetStarSign() string {
	if m != nil {
		return m.StarSign
	}
	return ""
}

func (m *UserInfo) GetBirthday() string {
	if m != nil {
		return m.Birthday
	}
	return ""
}

func (m *UserInfo) GetFeeling() string {
	if m != nil {
		return m.Feeling
	}
	return ""
}

func (m *UserInfo) GetHeight() int32 {
	if m != nil {
		return m.Height
	}
	return 0
}

func (m *UserInfo) GetWeight() int32 {
	if m != nil {
		return m.Weight
	}
	return 0
}

func (m *UserInfo) GetImgList() string {
	if m != nil {
		return m.ImgList
	}
	return ""
}

func (m *UserInfo) GetRealStatus() string {
	if m != nil {
		return m.RealStatus
	}
	return ""
}

func (m *UserInfo) GetAvatarUrl() string {
	if m != nil {
		return m.AvatarUrl
	}
	return ""
}

func (m *UserInfo) GetAudioUrl() string {
	if m != nil {
		return m.AudioUrl
	}
	return ""
}

func (m *UserInfo) GetEducation() string {
	if m != nil {
		return m.Education
	}
	return ""
}

func (m *UserInfo) GetJob() string {
	if m != nil {
		return m.Job
	}
	return ""
}

func (m *UserInfo) GetIncome() string {
	if m != nil {
		return m.Income
	}
	return ""
}

func (m *UserInfo) GetProvince() string {
	if m != nil {
		return m.Province
	}
	return ""
}

func (m *UserInfo) GetCity() string {
	if m != nil {
		return m.City
	}
	return ""
}

func (m *UserInfo) GetArea() string {
	if m != nil {
		return m.Area
	}
	return ""
}

func (m *UserInfo) GetHomeTown() string {
	if m != nil {
		return m.HomeTown
	}
	return ""
}

func (m *UserInfo) GetRealNameStatus() string {
	if m != nil {
		return m.RealNameStatus
	}
	return ""
}

func (m *UserInfo) GetIntro() string {
	if m != nil {
		return m.Intro
	}
	return ""
}

func (m *UserInfo) GetLon() float64 {
	if m != nil {
		return m.Lon
	}
	return 0
}

func (m *UserInfo) GetLat() float64 {
	if m != nil {
		return m.Lat
	}
	return 0
}

func (m *UserInfo) GetIsOnline() int32 {
	if m != nil {
		return m.IsOnline
	}
	return 0
}

func (m *UserInfo) GetLastLoginTime() string {
	if m != nil {
		return m.LastLoginTime
	}
	return ""
}

func (m *UserInfo) GetCharmLevel() int32 {
	if m != nil {
		return m.CharmLevel
	}
	return 0
}

func (m *UserInfo) GetRegistryType() string {
	if m != nil {
		return m.RegistryType
	}
	return ""
}

func (m *UserInfo) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *UserInfo) GetUnionId() string {
	if m != nil {
		return m.UnionId
	}
	return ""
}

func (m *UserInfo) GetRegisterStatus() string {
	if m != nil {
		return m.RegisterStatus
	}
	return ""
}

func (m *UserInfo) GetInviteCode() string {
	if m != nil {
		return m.InviteCode
	}
	return ""
}

func (m *UserInfo) GetBorn() string {
	if m != nil {
		return m.Born
	}
	return ""
}

func (m *UserInfo) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

func (m *UserInfo) GetRcToken() string {
	if m != nil {
		return m.RcToken
	}
	return ""
}

func (m *UserInfo) GetWealthLevel() int32 {
	if m != nil {
		return m.WealthLevel
	}
	return 0
}

func (m *UserInfo) GetAlbum() []*UserImg {
	if m != nil {
		return m.Album
	}
	return nil
}

func (m *UserInfo) GetDuration() int32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *UserInfo) GetAvatarStatus() string {
	if m != nil {
		return m.AvatarStatus
	}
	return ""
}

func (m *UserInfo) GetAudioStatus() string {
	if m != nil {
		return m.AudioStatus
	}
	return ""
}

func (m *UserInfo) GetGroupCode() string {
	if m != nil {
		return m.GroupCode
	}
	return ""
}

func (m *UserInfo) GetAvatarLevel() string {
	if m != nil {
		return m.AvatarLevel
	}
	return ""
}

func (m *UserInfo) GetSMResult() string {
	if m != nil {
		return m.SMResult
	}
	return ""
}

func (m *UserInfo) GetSMReason() string {
	if m != nil {
		return m.SMReason
	}
	return ""
}

func (m *UserInfo) GetIdName() string {
	if m != nil {
		return m.IdName
	}
	return ""
}

func (m *UserInfo) GetContractSignStatus() string {
	if m != nil {
		return m.ContractSignStatus
	}
	return ""
}

func (m *UserInfo) GetSuperAdmin() string {
	if m != nil {
		return m.SuperAdmin
	}
	return ""
}

func (m *UserInfo) GetIdCard() string {
	if m != nil {
		return m.IdCard
	}
	return ""
}

func (m *UserInfo) GetMobile() string {
	if m != nil {
		return m.Mobile
	}
	return ""
}

func (m *UserInfo) GetCheckAvatar() string {
	if m != nil {
		return m.CheckAvatar
	}
	return ""
}

func (m *UserInfo) GetRobot() string {
	if m != nil {
		return m.Robot
	}
	return ""
}

//相册
type UserImg struct {
	ImgUrl               string   `protobuf:"bytes,1,opt,name=ImgUrl,proto3" json:"ImgUrl,omitempty"`
	Status               string   `protobuf:"bytes,2,opt,name=Status,proto3" json:"Status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserImg) Reset()         { *m = UserImg{} }
func (m *UserImg) String() string { return proto.CompactTextString(m) }
func (*UserImg) ProtoMessage()    {}
func (*UserImg) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{46}
}

func (m *UserImg) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserImg.Unmarshal(m, b)
}
func (m *UserImg) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserImg.Marshal(b, m, deterministic)
}
func (m *UserImg) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserImg.Merge(m, src)
}
func (m *UserImg) XXX_Size() int {
	return xxx_messageInfo_UserImg.Size(m)
}
func (m *UserImg) XXX_DiscardUnknown() {
	xxx_messageInfo_UserImg.DiscardUnknown(m)
}

var xxx_messageInfo_UserImg proto.InternalMessageInfo

func (m *UserImg) GetImgUrl() string {
	if m != nil {
		return m.ImgUrl
	}
	return ""
}

func (m *UserImg) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

type PullUserReq struct {
	Query                []byte   `protobuf:"bytes,1,opt,name=Query,proto3" json:"Query,omitempty"`
	Cols                 []string `protobuf:"bytes,2,rep,name=Cols,proto3" json:"Cols,omitempty"`
	Limit                int32    `protobuf:"varint,3,opt,name=Limit,proto3" json:"Limit,omitempty"`
	IsRand               int32    `protobuf:"varint,4,opt,name=IsRand,proto3" json:"IsRand,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *PullUserReq) Reset()         { *m = PullUserReq{} }
func (m *PullUserReq) String() string { return proto.CompactTextString(m) }
func (*PullUserReq) ProtoMessage()    {}
func (*PullUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{47}
}

func (m *PullUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PullUserReq.Unmarshal(m, b)
}
func (m *PullUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PullUserReq.Marshal(b, m, deterministic)
}
func (m *PullUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PullUserReq.Merge(m, src)
}
func (m *PullUserReq) XXX_Size() int {
	return xxx_messageInfo_PullUserReq.Size(m)
}
func (m *PullUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PullUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_PullUserReq proto.InternalMessageInfo

func (m *PullUserReq) GetQuery() []byte {
	if m != nil {
		return m.Query
	}
	return nil
}

func (m *PullUserReq) GetCols() []string {
	if m != nil {
		return m.Cols
	}
	return nil
}

func (m *PullUserReq) GetLimit() int32 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *PullUserReq) GetIsRand() int32 {
	if m != nil {
		return m.IsRand
	}
	return 0
}

type PullUserRsp struct {
	Users                []*UserInfo `protobuf:"bytes,1,rep,name=Users,proto3" json:"Users,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *PullUserRsp) Reset()         { *m = PullUserRsp{} }
func (m *PullUserRsp) String() string { return proto.CompactTextString(m) }
func (*PullUserRsp) ProtoMessage()    {}
func (*PullUserRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{48}
}

func (m *PullUserRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PullUserRsp.Unmarshal(m, b)
}
func (m *PullUserRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PullUserRsp.Marshal(b, m, deterministic)
}
func (m *PullUserRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PullUserRsp.Merge(m, src)
}
func (m *PullUserRsp) XXX_Size() int {
	return xxx_messageInfo_PullUserRsp.Size(m)
}
func (m *PullUserRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_PullUserRsp.DiscardUnknown(m)
}

var xxx_messageInfo_PullUserRsp proto.InternalMessageInfo

func (m *PullUserRsp) GetUsers() []*UserInfo {
	if m != nil {
		return m.Users
	}
	return nil
}

type UserRemarkReq struct {
	NoId                 string   `protobuf:"bytes,1,opt,name=NoId,proto3" json:"NoId,omitempty"`
	Ids                  []string `protobuf:"bytes,2,rep,name=Ids,proto3" json:"Ids,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserRemarkReq) Reset()         { *m = UserRemarkReq{} }
func (m *UserRemarkReq) String() string { return proto.CompactTextString(m) }
func (*UserRemarkReq) ProtoMessage()    {}
func (*UserRemarkReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{49}
}

func (m *UserRemarkReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserRemarkReq.Unmarshal(m, b)
}
func (m *UserRemarkReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserRemarkReq.Marshal(b, m, deterministic)
}
func (m *UserRemarkReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserRemarkReq.Merge(m, src)
}
func (m *UserRemarkReq) XXX_Size() int {
	return xxx_messageInfo_UserRemarkReq.Size(m)
}
func (m *UserRemarkReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserRemarkReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserRemarkReq proto.InternalMessageInfo

func (m *UserRemarkReq) GetNoId() string {
	if m != nil {
		return m.NoId
	}
	return ""
}

func (m *UserRemarkReq) GetIds() []string {
	if m != nil {
		return m.Ids
	}
	return nil
}

type UserRemarkRsp struct {
	UserRemark           map[string]string `protobuf:"bytes,1,rep,name=UserRemark,proto3" json:"UserRemark,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UserRemarkRsp) Reset()         { *m = UserRemarkRsp{} }
func (m *UserRemarkRsp) String() string { return proto.CompactTextString(m) }
func (*UserRemarkRsp) ProtoMessage()    {}
func (*UserRemarkRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{50}
}

func (m *UserRemarkRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserRemarkRsp.Unmarshal(m, b)
}
func (m *UserRemarkRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserRemarkRsp.Marshal(b, m, deterministic)
}
func (m *UserRemarkRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserRemarkRsp.Merge(m, src)
}
func (m *UserRemarkRsp) XXX_Size() int {
	return xxx_messageInfo_UserRemarkRsp.Size(m)
}
func (m *UserRemarkRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserRemarkRsp.DiscardUnknown(m)
}

var xxx_messageInfo_UserRemarkRsp proto.InternalMessageInfo

func (m *UserRemarkRsp) GetUserRemark() map[string]string {
	if m != nil {
		return m.UserRemark
	}
	return nil
}

type GetOnlineRsq struct {
	Gender               string   `protobuf:"bytes,1,opt,name=gender,proto3" json:"gender,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOnlineRsq) Reset()         { *m = GetOnlineRsq{} }
func (m *GetOnlineRsq) String() string { return proto.CompactTextString(m) }
func (*GetOnlineRsq) ProtoMessage()    {}
func (*GetOnlineRsq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{51}
}

func (m *GetOnlineRsq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOnlineRsq.Unmarshal(m, b)
}
func (m *GetOnlineRsq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOnlineRsq.Marshal(b, m, deterministic)
}
func (m *GetOnlineRsq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOnlineRsq.Merge(m, src)
}
func (m *GetOnlineRsq) XXX_Size() int {
	return xxx_messageInfo_GetOnlineRsq.Size(m)
}
func (m *GetOnlineRsq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOnlineRsq.DiscardUnknown(m)
}

var xxx_messageInfo_GetOnlineRsq proto.InternalMessageInfo

func (m *GetOnlineRsq) GetGender() string {
	if m != nil {
		return m.Gender
	}
	return ""
}

type GetOnlineRsp struct {
	Users                []string `protobuf:"bytes,1,rep,name=Users,proto3" json:"Users,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetOnlineRsp) Reset()         { *m = GetOnlineRsp{} }
func (m *GetOnlineRsp) String() string { return proto.CompactTextString(m) }
func (*GetOnlineRsp) ProtoMessage()    {}
func (*GetOnlineRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{52}
}

func (m *GetOnlineRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetOnlineRsp.Unmarshal(m, b)
}
func (m *GetOnlineRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetOnlineRsp.Marshal(b, m, deterministic)
}
func (m *GetOnlineRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetOnlineRsp.Merge(m, src)
}
func (m *GetOnlineRsp) XXX_Size() int {
	return xxx_messageInfo_GetOnlineRsp.Size(m)
}
func (m *GetOnlineRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetOnlineRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetOnlineRsp proto.InternalMessageInfo

func (m *GetOnlineRsp) GetUsers() []string {
	if m != nil {
		return m.Users
	}
	return nil
}

type UpdateAlbumReq struct {
	UserId               string            `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId,omitempty"`
	Album                map[string]string `protobuf:"bytes,2,rep,name=album,proto3" json:"album,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-"`
	XXX_unrecognized     []byte            `json:"-"`
	XXX_sizecache        int32             `json:"-"`
}

func (m *UpdateAlbumReq) Reset()         { *m = UpdateAlbumReq{} }
func (m *UpdateAlbumReq) String() string { return proto.CompactTextString(m) }
func (*UpdateAlbumReq) ProtoMessage()    {}
func (*UpdateAlbumReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{53}
}

func (m *UpdateAlbumReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAlbumReq.Unmarshal(m, b)
}
func (m *UpdateAlbumReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAlbumReq.Marshal(b, m, deterministic)
}
func (m *UpdateAlbumReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAlbumReq.Merge(m, src)
}
func (m *UpdateAlbumReq) XXX_Size() int {
	return xxx_messageInfo_UpdateAlbumReq.Size(m)
}
func (m *UpdateAlbumReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAlbumReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAlbumReq proto.InternalMessageInfo

func (m *UpdateAlbumReq) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *UpdateAlbumReq) GetAlbum() map[string]string {
	if m != nil {
		return m.Album
	}
	return nil
}

type UpdateAlbumRsp struct {
	Status               bool     `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UpdateAlbumRsp) Reset()         { *m = UpdateAlbumRsp{} }
func (m *UpdateAlbumRsp) String() string { return proto.CompactTextString(m) }
func (*UpdateAlbumRsp) ProtoMessage()    {}
func (*UpdateAlbumRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{54}
}

func (m *UpdateAlbumRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateAlbumRsp.Unmarshal(m, b)
}
func (m *UpdateAlbumRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateAlbumRsp.Marshal(b, m, deterministic)
}
func (m *UpdateAlbumRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateAlbumRsp.Merge(m, src)
}
func (m *UpdateAlbumRsp) XXX_Size() int {
	return xxx_messageInfo_UpdateAlbumRsp.Size(m)
}
func (m *UpdateAlbumRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateAlbumRsp.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateAlbumRsp proto.InternalMessageInfo

func (m *UpdateAlbumRsp) GetStatus() bool {
	if m != nil {
		return m.Status
	}
	return false
}

type ValidHeadReq struct {
	NoId                 string   `protobuf:"bytes,1,opt,name=noId,proto3" json:"noId,omitempty"`
	Imei                 string   `protobuf:"bytes,2,opt,name=imei,proto3" json:"imei,omitempty"`
	Ip                   string   `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip,omitempty"`
	Lon                  float32  `protobuf:"fixed32,4,opt,name=lon,proto3" json:"lon,omitempty"`
	Lat                  float32  `protobuf:"fixed32,5,opt,name=lat,proto3" json:"lat,omitempty"`
	Url                  string   `protobuf:"bytes,6,opt,name=url,proto3" json:"url,omitempty"`
	Status               string   `protobuf:"bytes,7,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ValidHeadReq) Reset()         { *m = ValidHeadReq{} }
func (m *ValidHeadReq) String() string { return proto.CompactTextString(m) }
func (*ValidHeadReq) ProtoMessage()    {}
func (*ValidHeadReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{55}
}

func (m *ValidHeadReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ValidHeadReq.Unmarshal(m, b)
}
func (m *ValidHeadReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ValidHeadReq.Marshal(b, m, deterministic)
}
func (m *ValidHeadReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ValidHeadReq.Merge(m, src)
}
func (m *ValidHeadReq) XXX_Size() int {
	return xxx_messageInfo_ValidHeadReq.Size(m)
}
func (m *ValidHeadReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ValidHeadReq.DiscardUnknown(m)
}

var xxx_messageInfo_ValidHeadReq proto.InternalMessageInfo

func (m *ValidHeadReq) GetNoId() string {
	if m != nil {
		return m.NoId
	}
	return ""
}

func (m *ValidHeadReq) GetImei() string {
	if m != nil {
		return m.Imei
	}
	return ""
}

func (m *ValidHeadReq) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *ValidHeadReq) GetLon() float32 {
	if m != nil {
		return m.Lon
	}
	return 0
}

func (m *ValidHeadReq) GetLat() float32 {
	if m != nil {
		return m.Lat
	}
	return 0
}

func (m *ValidHeadReq) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *ValidHeadReq) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

type ValidHeadRsp struct {
	Code                 int32    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ValidHeadRsp) Reset()         { *m = ValidHeadRsp{} }
func (m *ValidHeadRsp) String() string { return proto.CompactTextString(m) }
func (*ValidHeadRsp) ProtoMessage()    {}
func (*ValidHeadRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{56}
}

func (m *ValidHeadRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ValidHeadRsp.Unmarshal(m, b)
}
func (m *ValidHeadRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ValidHeadRsp.Marshal(b, m, deterministic)
}
func (m *ValidHeadRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ValidHeadRsp.Merge(m, src)
}
func (m *ValidHeadRsp) XXX_Size() int {
	return xxx_messageInfo_ValidHeadRsp.Size(m)
}
func (m *ValidHeadRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ValidHeadRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ValidHeadRsp proto.InternalMessageInfo

func (m *ValidHeadRsp) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

type ReviewInfoReq struct {
	Event                string   `protobuf:"bytes,1,opt,name=event,proto3" json:"event,omitempty"`
	RuleType             string   `protobuf:"bytes,2,opt,name=ruleType,proto3" json:"ruleType,omitempty"`
	ReqText              string   `protobuf:"bytes,3,opt,name=reqText,proto3" json:"reqText,omitempty"`
	RawText              string   `protobuf:"bytes,4,opt,name=rawText,proto3" json:"rawText,omitempty"`
	NoId                 string   `protobuf:"bytes,5,opt,name=noId,proto3" json:"noId,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReviewInfoReq) Reset()         { *m = ReviewInfoReq{} }
func (m *ReviewInfoReq) String() string { return proto.CompactTextString(m) }
func (*ReviewInfoReq) ProtoMessage()    {}
func (*ReviewInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{57}
}

func (m *ReviewInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReviewInfoReq.Unmarshal(m, b)
}
func (m *ReviewInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReviewInfoReq.Marshal(b, m, deterministic)
}
func (m *ReviewInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReviewInfoReq.Merge(m, src)
}
func (m *ReviewInfoReq) XXX_Size() int {
	return xxx_messageInfo_ReviewInfoReq.Size(m)
}
func (m *ReviewInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReviewInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReviewInfoReq proto.InternalMessageInfo

func (m *ReviewInfoReq) GetEvent() string {
	if m != nil {
		return m.Event
	}
	return ""
}

func (m *ReviewInfoReq) GetRuleType() string {
	if m != nil {
		return m.RuleType
	}
	return ""
}

func (m *ReviewInfoReq) GetReqText() string {
	if m != nil {
		return m.ReqText
	}
	return ""
}

func (m *ReviewInfoReq) GetRawText() string {
	if m != nil {
		return m.RawText
	}
	return ""
}

func (m *ReviewInfoReq) GetNoId() string {
	if m != nil {
		return m.NoId
	}
	return ""
}

type ReviewInfoRsp struct {
	Status               string   `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ReviewInfoRsp) Reset()         { *m = ReviewInfoRsp{} }
func (m *ReviewInfoRsp) String() string { return proto.CompactTextString(m) }
func (*ReviewInfoRsp) ProtoMessage()    {}
func (*ReviewInfoRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{58}
}

func (m *ReviewInfoRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReviewInfoRsp.Unmarshal(m, b)
}
func (m *ReviewInfoRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReviewInfoRsp.Marshal(b, m, deterministic)
}
func (m *ReviewInfoRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReviewInfoRsp.Merge(m, src)
}
func (m *ReviewInfoRsp) XXX_Size() int {
	return xxx_messageInfo_ReviewInfoRsp.Size(m)
}
func (m *ReviewInfoRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_ReviewInfoRsp.DiscardUnknown(m)
}

var xxx_messageInfo_ReviewInfoRsp proto.InternalMessageInfo

func (m *ReviewInfoRsp) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

type IPLocReq struct {
	Ip                   string   `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IPLocReq) Reset()         { *m = IPLocReq{} }
func (m *IPLocReq) String() string { return proto.CompactTextString(m) }
func (*IPLocReq) ProtoMessage()    {}
func (*IPLocReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{59}
}

func (m *IPLocReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IPLocReq.Unmarshal(m, b)
}
func (m *IPLocReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IPLocReq.Marshal(b, m, deterministic)
}
func (m *IPLocReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IPLocReq.Merge(m, src)
}
func (m *IPLocReq) XXX_Size() int {
	return xxx_messageInfo_IPLocReq.Size(m)
}
func (m *IPLocReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IPLocReq.DiscardUnknown(m)
}

var xxx_messageInfo_IPLocReq proto.InternalMessageInfo

func (m *IPLocReq) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

type IPLocRsp struct {
	Province             string   `protobuf:"bytes,1,opt,name=province,proto3" json:"province,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *IPLocRsp) Reset()         { *m = IPLocRsp{} }
func (m *IPLocRsp) String() string { return proto.CompactTextString(m) }
func (*IPLocRsp) ProtoMessage()    {}
func (*IPLocRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{60}
}

func (m *IPLocRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IPLocRsp.Unmarshal(m, b)
}
func (m *IPLocRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IPLocRsp.Marshal(b, m, deterministic)
}
func (m *IPLocRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IPLocRsp.Merge(m, src)
}
func (m *IPLocRsp) XXX_Size() int {
	return xxx_messageInfo_IPLocRsp.Size(m)
}
func (m *IPLocRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_IPLocRsp.DiscardUnknown(m)
}

var xxx_messageInfo_IPLocRsp proto.InternalMessageInfo

func (m *IPLocRsp) GetProvince() string {
	if m != nil {
		return m.Province
	}
	return ""
}

type UserInterestTag struct {
	TagId                string   `protobuf:"bytes,1,opt,name=tagId,proto3" json:"tagId,omitempty"`
	TagName              string   `protobuf:"bytes,2,opt,name=tagName,proto3" json:"tagName,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserInterestTag) Reset()         { *m = UserInterestTag{} }
func (m *UserInterestTag) String() string { return proto.CompactTextString(m) }
func (*UserInterestTag) ProtoMessage()    {}
func (*UserInterestTag) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{61}
}

func (m *UserInterestTag) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInterestTag.Unmarshal(m, b)
}
func (m *UserInterestTag) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInterestTag.Marshal(b, m, deterministic)
}
func (m *UserInterestTag) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInterestTag.Merge(m, src)
}
func (m *UserInterestTag) XXX_Size() int {
	return xxx_messageInfo_UserInterestTag.Size(m)
}
func (m *UserInterestTag) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInterestTag.DiscardUnknown(m)
}

var xxx_messageInfo_UserInterestTag proto.InternalMessageInfo

func (m *UserInterestTag) GetTagId() string {
	if m != nil {
		return m.TagId
	}
	return ""
}

func (m *UserInterestTag) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

type GetUserInterestReq struct {
	NoId                 string   `protobuf:"bytes,1,opt,name=noId,proto3" json:"noId,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserInterestReq) Reset()         { *m = GetUserInterestReq{} }
func (m *GetUserInterestReq) String() string { return proto.CompactTextString(m) }
func (*GetUserInterestReq) ProtoMessage()    {}
func (*GetUserInterestReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{62}
}

func (m *GetUserInterestReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserInterestReq.Unmarshal(m, b)
}
func (m *GetUserInterestReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserInterestReq.Marshal(b, m, deterministic)
}
func (m *GetUserInterestReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserInterestReq.Merge(m, src)
}
func (m *GetUserInterestReq) XXX_Size() int {
	return xxx_messageInfo_GetUserInterestReq.Size(m)
}
func (m *GetUserInterestReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserInterestReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserInterestReq proto.InternalMessageInfo

func (m *GetUserInterestReq) GetNoId() string {
	if m != nil {
		return m.NoId
	}
	return ""
}

type GetUserInterestRsp struct {
	Interests            []*UserInterestTag `protobuf:"bytes,1,rep,name=interests,proto3" json:"interests,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-"`
	XXX_unrecognized     []byte             `json:"-"`
	XXX_sizecache        int32              `json:"-"`
}

func (m *GetUserInterestRsp) Reset()         { *m = GetUserInterestRsp{} }
func (m *GetUserInterestRsp) String() string { return proto.CompactTextString(m) }
func (*GetUserInterestRsp) ProtoMessage()    {}
func (*GetUserInterestRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{63}
}

func (m *GetUserInterestRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserInterestRsp.Unmarshal(m, b)
}
func (m *GetUserInterestRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserInterestRsp.Marshal(b, m, deterministic)
}
func (m *GetUserInterestRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserInterestRsp.Merge(m, src)
}
func (m *GetUserInterestRsp) XXX_Size() int {
	return xxx_messageInfo_GetUserInterestRsp.Size(m)
}
func (m *GetUserInterestRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserInterestRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserInterestRsp proto.InternalMessageInfo

func (m *GetUserInterestRsp) GetInterests() []*UserInterestTag {
	if m != nil {
		return m.Interests
	}
	return nil
}

type GetUserInterestsReq struct {
	NoIds                []string `protobuf:"bytes,1,rep,name=noIds,proto3" json:"noIds,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *GetUserInterestsReq) Reset()         { *m = GetUserInterestsReq{} }
func (m *GetUserInterestsReq) String() string { return proto.CompactTextString(m) }
func (*GetUserInterestsReq) ProtoMessage()    {}
func (*GetUserInterestsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{64}
}

func (m *GetUserInterestsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserInterestsReq.Unmarshal(m, b)
}
func (m *GetUserInterestsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserInterestsReq.Marshal(b, m, deterministic)
}
func (m *GetUserInterestsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserInterestsReq.Merge(m, src)
}
func (m *GetUserInterestsReq) XXX_Size() int {
	return xxx_messageInfo_GetUserInterestsReq.Size(m)
}
func (m *GetUserInterestsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserInterestsReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserInterestsReq proto.InternalMessageInfo

func (m *GetUserInterestsReq) GetNoIds() []string {
	if m != nil {
		return m.NoIds
	}
	return nil
}

type UserInterestsVO struct {
	Interests            []string `protobuf:"bytes,1,rep,name=interests,proto3" json:"interests,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *UserInterestsVO) Reset()         { *m = UserInterestsVO{} }
func (m *UserInterestsVO) String() string { return proto.CompactTextString(m) }
func (*UserInterestsVO) ProtoMessage()    {}
func (*UserInterestsVO) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{65}
}

func (m *UserInterestsVO) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInterestsVO.Unmarshal(m, b)
}
func (m *UserInterestsVO) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInterestsVO.Marshal(b, m, deterministic)
}
func (m *UserInterestsVO) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInterestsVO.Merge(m, src)
}
func (m *UserInterestsVO) XXX_Size() int {
	return xxx_messageInfo_UserInterestsVO.Size(m)
}
func (m *UserInterestsVO) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInterestsVO.DiscardUnknown(m)
}

var xxx_messageInfo_UserInterestsVO proto.InternalMessageInfo

func (m *UserInterestsVO) GetInterests() []string {
	if m != nil {
		return m.Interests
	}
	return nil
}

type GetUserInterestsRsp struct {
	Ret                  map[string]*UserInterestsVO `protobuf:"bytes,1,rep,name=ret,proto3" json:"ret,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}                    `json:"-"`
	XXX_unrecognized     []byte                      `json:"-"`
	XXX_sizecache        int32                       `json:"-"`
}

func (m *GetUserInterestsRsp) Reset()         { *m = GetUserInterestsRsp{} }
func (m *GetUserInterestsRsp) String() string { return proto.CompactTextString(m) }
func (*GetUserInterestsRsp) ProtoMessage()    {}
func (*GetUserInterestsRsp) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{66}
}

func (m *GetUserInterestsRsp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetUserInterestsRsp.Unmarshal(m, b)
}
func (m *GetUserInterestsRsp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetUserInterestsRsp.Marshal(b, m, deterministic)
}
func (m *GetUserInterestsRsp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetUserInterestsRsp.Merge(m, src)
}
func (m *GetUserInterestsRsp) XXX_Size() int {
	return xxx_messageInfo_GetUserInterestsRsp.Size(m)
}
func (m *GetUserInterestsRsp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetUserInterestsRsp.DiscardUnknown(m)
}

var xxx_messageInfo_GetUserInterestsRsp proto.InternalMessageInfo

func (m *GetUserInterestsRsp) GetRet() map[string]*UserInterestsVO {
	if m != nil {
		return m.Ret
	}
	return nil
}

func init() {
	proto.RegisterEnum("user.base.ModifyType", ModifyType_name, ModifyType_value)
	proto.RegisterEnum("user.base.ModifyStatus", ModifyStatus_name, ModifyStatus_value)
	proto.RegisterEnum("user.base.FilterParam", FilterParam_name, FilterParam_value)
	proto.RegisterEnum("user.base.CheckType", CheckType_name, CheckType_value)
	proto.RegisterEnum("user.base.PushType", PushType_name, PushType_value)
	proto.RegisterEnum("user.base.TargetType", TargetType_name, TargetType_value)
	proto.RegisterType((*TimeoutModifyMobileReq)(nil), "user.base.TimeoutModifyMobileReq")
	proto.RegisterType((*TimeoutModifyMobileRsp)(nil), "user.base.TimeoutModifyMobileRsp")
	proto.RegisterMapType((map[string]string)(nil), "user.base.TimeoutModifyMobileRsp.TMapEntry")
	proto.RegisterType((*GetAndCheckMobileReq)(nil), "user.base.GetAndCheckMobileReq")
	proto.RegisterType((*GetAndCheckMobileRsp)(nil), "user.base.GetAndCheckMobileRsp")
	proto.RegisterType((*MobileUserInfo)(nil), "user.base.MobileUserInfo")
	proto.RegisterType((*GetModifyMobileListReq)(nil), "user.base.GetModifyMobileListReq")
	proto.RegisterType((*GetModifyMobileListRsp)(nil), "user.base.GetModifyMobileListRsp")
	proto.RegisterType((*ModifyMobileData)(nil), "user.base.ModifyMobileData")
	proto.RegisterType((*GetModifyMobileInfoReq)(nil), "user.base.GetModifyMobileInfoReq")
	proto.RegisterType((*GetModifyMobileInfoRsp)(nil), "user.base.GetModifyMobileInfoRsp")
	proto.RegisterType((*ApplyModifyMobileReq)(nil), "user.base.ApplyModifyMobileReq")
	proto.RegisterType((*SetPushStatusReq)(nil), "user.base.SetPushStatusReq")
	proto.RegisterType((*UnBindPushRelationReq)(nil), "user.base.UnBindPushRelationReq")
	proto.RegisterType((*BindPushRelationReq)(nil), "user.base.BindPushRelationReq")
	proto.RegisterType((*PushByAliasReq)(nil), "user.base.PushByAliasReq")
	proto.RegisterType((*PushAttach)(nil), "user.base.PushAttach")
	proto.RegisterType((*EmptyRsp)(nil), "user.base.EmptyRsp")
	proto.RegisterType((*GetUserSwitchReq)(nil), "user.base.GetUserSwitchReq")
	proto.RegisterType((*GetUserSwitchRsp)(nil), "user.base.GetUserSwitchRsp")
	proto.RegisterType((*SwitchInfo)(nil), "user.base.SwitchInfo")
	proto.RegisterType((*SetUserSwitchReq)(nil), "user.base.SetUserSwitchReq")
	proto.RegisterType((*SetUserSwitchRsp)(nil), "user.base.SetUserSwitchRsp")
	proto.RegisterType((*GetUserFansListReq)(nil), "user.base.GetUserFansListReq")
	proto.RegisterType((*GetUserFansListRsp)(nil), "user.base.GetUserFansListRsp")
	proto.RegisterType((*SessionInfo)(nil), "user.base.SessionInfo")
	proto.RegisterType((*OnlineNumReq)(nil), "user.base.OnlineNumReq")
	proto.RegisterType((*OnlineNumRsp)(nil), "user.base.OnlineNumRsp")
	proto.RegisterType((*RobotAddReq)(nil), "user.base.RobotAddReq")
	proto.RegisterType((*RobotConfig)(nil), "user.base.robotConfig")
	proto.RegisterType((*Robot)(nil), "user.base.robot")
	proto.RegisterType((*RobotAddRsp)(nil), "user.base.RobotAddRsp")
	proto.RegisterType((*RobotInfo)(nil), "user.base.robotInfo")
	proto.RegisterType((*SetOnlineUserInfoReq)(nil), "user.base.SetOnlineUserInfoReq")
	proto.RegisterType((*SetOnlineUserInfoReqRsp)(nil), "user.base.SetOnlineUserInfoReqRsp")
	proto.RegisterType((*TokenReq)(nil), "user.base.TokenReq")
	proto.RegisterType((*TokenRes)(nil), "user.base.TokenRes")
	proto.RegisterType((*UserReq)(nil), "user.base.UserReq")
	proto.RegisterType((*UserInfoRes)(nil), "user.base.UserInfoRes")
	proto.RegisterType((*UserIdsReq)(nil), "user.base.UserIdsReq")
	proto.RegisterType((*MongoIdsReq)(nil), "user.base.MongoIdsReq")
	proto.RegisterType((*MsgIdsReq)(nil), "user.base.MsgIdsReq")
	proto.RegisterType((*UserInfoRsp)(nil), "user.base.UserInfoRsp")
	proto.RegisterType((*UpdateUserReq)(nil), "user.base.UpdateUserReq")
	proto.RegisterType((*UserExistReq)(nil), "user.base.UserExistReq")
	proto.RegisterType((*UserExistRsp)(nil), "user.base.UserExistRsp")
	proto.RegisterType((*UserInfo)(nil), "user.base.UserInfo")
	proto.RegisterType((*UserImg)(nil), "user.base.UserImg")
	proto.RegisterType((*PullUserReq)(nil), "user.base.PullUserReq")
	proto.RegisterType((*PullUserRsp)(nil), "user.base.PullUserRsp")
	proto.RegisterType((*UserRemarkReq)(nil), "user.base.UserRemarkReq")
	proto.RegisterType((*UserRemarkRsp)(nil), "user.base.UserRemarkRsp")
	proto.RegisterMapType((map[string]string)(nil), "user.base.UserRemarkRsp.UserRemarkEntry")
	proto.RegisterType((*GetOnlineRsq)(nil), "user.base.GetOnlineRsq")
	proto.RegisterType((*GetOnlineRsp)(nil), "user.base.GetOnlineRsp")
	proto.RegisterType((*UpdateAlbumReq)(nil), "user.base.UpdateAlbumReq")
	proto.RegisterMapType((map[string]string)(nil), "user.base.UpdateAlbumReq.AlbumEntry")
	proto.RegisterType((*UpdateAlbumRsp)(nil), "user.base.UpdateAlbumRsp")
	proto.RegisterType((*ValidHeadReq)(nil), "user.base.ValidHeadReq")
	proto.RegisterType((*ValidHeadRsp)(nil), "user.base.ValidHeadRsp")
	proto.RegisterType((*ReviewInfoReq)(nil), "user.base.ReviewInfoReq")
	proto.RegisterType((*ReviewInfoRsp)(nil), "user.base.ReviewInfoRsp")
	proto.RegisterType((*IPLocReq)(nil), "user.base.IPLocReq")
	proto.RegisterType((*IPLocRsp)(nil), "user.base.IPLocRsp")
	proto.RegisterType((*UserInterestTag)(nil), "user.base.UserInterestTag")
	proto.RegisterType((*GetUserInterestReq)(nil), "user.base.GetUserInterestReq")
	proto.RegisterType((*GetUserInterestRsp)(nil), "user.base.GetUserInterestRsp")
	proto.RegisterType((*GetUserInterestsReq)(nil), "user.base.GetUserInterestsReq")
	proto.RegisterType((*UserInterestsVO)(nil), "user.base.UserInterestsVO")
	proto.RegisterType((*GetUserInterestsRsp)(nil), "user.base.GetUserInterestsRsp")
	proto.RegisterMapType((map[string]*UserInterestsVO)(nil), "user.base.GetUserInterestsRsp.RetEntry")
}

func init() { proto.RegisterFile("user.proto", fileDescriptor_116e343673f7ffaf) }

var fileDescriptor_116e343673f7ffaf = []byte{
	// 3559 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x3a, 0x5d, 0x73, 0x1b, 0x47,
	0x72, 0xc6, 0x97, 0x44, 0x34, 0x48, 0x0a, 0x1e, 0xd1, 0xf4, 0x1a, 0xbe, 0xd3, 0xd1, 0x1b, 0x47,
	0xa6, 0xe5, 0x33, 0x6d, 0xd3, 0xa7, 0x9c, 0xec, 0x2b, 0x9d, 0x8f, 0xa4, 0x6c, 0x0a, 0x17, 0x92,
	0x62, 0x96, 0xa4, 0x5c, 0x95, 0x54, 0x25, 0xb5, 0x02, 0x86, 0xe0, 0x9e, 0x80, 0xdd, 0xf5, 0xce,
	0x80, 0x14, 0xf3, 0x98, 0x3c, 0xa4, 0xf2, 0xe2, 0x54, 0xa5, 0xf2, 0x90, 0xa7, 0x3c, 0xe5, 0x17,
	0xe4, 0x2d, 0xff, 0x21, 0x95, 0xb7, 0xbc, 0xe6, 0x17, 0xa4, 0x2a, 0x7f, 0x21, 0xd5, 0xdd, 0x33,
	0xbb, 0xb3, 0xc0, 0x82, 0x3a, 0xa5, 0xf2, 0x92, 0x27, 0x4c, 0xf7, 0xf4, 0xf4, 0x74, 0xf7, 0xf4,
	0xf4, 0xc7, 0x2c, 0x00, 0xa6, 0x4a, 0x66, 0x5b, 0x69, 0x96, 0xe8, 0x44, 0xb4, 0x69, 0xfc, 0x22,
	0x54, 0xd2, 0xf7, 0x60, 0xfd, 0x34, 0x9a, 0xc8, 0x64, 0xaa, 0x0f, 0x93, 0x61, 0x74, 0x7e, 0x7d,
	0x98, 0xbc, 0x88, 0xc6, 0x32, 0x90, 0x3f, 0xf8, 0x7f, 0x5f, 0xab, 0x9e, 0x52, 0xa9, 0xf8, 0x06,
	0x9a, 0xfa, 0x30, 0x4c, 0xbd, 0xda, 0x46, 0x63, 0xb3, 0xb3, 0xfd, 0xc9, 0x56, 0xce, 0x6e, 0xab,
	0x7a, 0xc1, 0xd6, 0xe9, 0x61, 0x98, 0x7e, 0x1b, 0xeb, 0xec, 0x3a, 0xa0, 0x85, 0xbd, 0x5f, 0x42,
	0x3b, 0x47, 0x89, 0x2e, 0x34, 0x5e, 0xca, 0x6b, 0xaf, 0xb6, 0x51, 0xdb, 0x6c, 0x07, 0x38, 0x14,
	0x6b, 0xd0, 0xba, 0x0c, 0xc7, 0x53, 0xe9, 0xd5, 0x09, 0xc7, 0xc0, 0xd7, 0xf5, 0x47, 0x35, 0x3f,
	0x85, 0xb5, 0x7d, 0xa9, 0x77, 0xe2, 0xe1, 0xde, 0x85, 0x1c, 0xbc, 0xcc, 0x85, 0x15, 0x02, 0x9a,
	0x71, 0xd2, 0x1f, 0x1a, 0x26, 0x34, 0x16, 0x3d, 0x58, 0xd2, 0x09, 0x93, 0x18, 0x46, 0x39, 0x2c,
	0x36, 0xa1, 0xa9, 0xaf, 0x53, 0xe9, 0x35, 0x36, 0x6a, 0x9b, 0xab, 0xdb, 0x6b, 0x8e, 0x06, 0xc4,
	0xf8, 0xf4, 0x3a, 0x95, 0x01, 0x51, 0xf8, 0xff, 0x52, 0xab, 0xda, 0x52, 0xa5, 0xe2, 0x1e, 0x40,
	0xa4, 0xf6, 0x92, 0xf8, 0x7c, 0x1c, 0x0d, 0x34, 0x6d, 0xbc, 0x14, 0x38, 0x18, 0xf1, 0x29, 0x34,
	0xcf, 0xb3, 0x64, 0x42, 0x5b, 0x77, 0xb6, 0xdf, 0x73, 0xb6, 0x60, 0x1e, 0x67, 0x4a, 0x66, 0xfd,
	0xf8, 0x3c, 0x09, 0x88, 0x4c, 0x7c, 0x0c, 0x75, 0x9d, 0x90, 0x3c, 0x37, 0x12, 0xd7, 0x75, 0x22,
	0x36, 0xa0, 0x13, 0xa9, 0x67, 0x97, 0x32, 0x43, 0x6b, 0x2b, 0xaf, 0x49, 0x5b, 0xbb, 0x28, 0x14,
	0x7a, 0xb5, 0xbc, 0x70, 0x91, 0x85, 0xe2, 0x68, 0xf0, 0x32, 0x0e, 0x27, 0xb9, 0x85, 0x2c, 0x2c,
	0xd6, 0xe1, 0x56, 0x78, 0x19, 0xea, 0x30, 0x23, 0x99, 0xda, 0x81, 0x81, 0x70, 0xf3, 0x2b, 0x19,
	0x8e, 0xf5, 0xc5, 0x81, 0xbc, 0x94, 0x63, 0xda, 0xbc, 0x11, 0xb8, 0x28, 0x34, 0xcc, 0xe0, 0x22,
	0xcc, 0x26, 0x4c, 0xd0, 0x22, 0x02, 0x07, 0x83, 0x9c, 0x27, 0x7c, 0x2a, 0xb7, 0x98, 0x33, 0x43,
	0xfe, 0xbf, 0xd7, 0x60, 0x7d, 0x5f, 0x96, 0x7c, 0xe7, 0x20, 0x52, 0x7a, 0xd1, 0xf1, 0xfe, 0x04,
	0xda, 0x4a, 0x87, 0x99, 0x46, 0x8d, 0x8d, 0xf4, 0x05, 0x42, 0x78, 0x70, 0x5b, 0xc6, 0x43, 0x9a,
	0x63, 0xf9, 0x2d, 0x28, 0x1e, 0x41, 0xe7, 0x3c, 0x1a, 0x6b, 0x99, 0x1d, 0x87, 0x59, 0x38, 0x21,
	0x05, 0x56, 0xb7, 0xd7, 0x1d, 0x8b, 0x7f, 0x57, 0xcc, 0x06, 0x2e, 0x29, 0x4a, 0x91, 0x86, 0x23,
	0x69, 0x54, 0xa2, 0x31, 0x9a, 0x10, 0x7f, 0x4f, 0xa2, 0xbf, 0x64, 0x75, 0x1a, 0x41, 0x0e, 0xfb,
	0x7f, 0x51, 0xad, 0x8f, 0x4a, 0xc5, 0x67, 0xd0, 0x1c, 0x47, 0x4a, 0x9b, 0x0b, 0xf4, 0x7e, 0xe9,
	0xb8, 0x0b, 0xea, 0x27, 0xa1, 0x0e, 0x03, 0x22, 0xc4, 0x1b, 0xb1, 0x97, 0x4c, 0x63, 0x4d, 0x8a,
	0x36, 0x02, 0x06, 0xfc, 0x7f, 0xab, 0x43, 0x77, 0x76, 0x81, 0x58, 0x85, 0x7a, 0x64, 0x2d, 0x55,
	0x8f, 0x86, 0xb9, 0xed, 0xea, 0x8e, 0xed, 0xee, 0x01, 0x4c, 0xd3, 0x61, 0xa8, 0xa5, 0x63, 0x20,
	0x07, 0x83, 0xf3, 0xe8, 0x94, 0xe6, 0xf2, 0x34, 0x79, 0xbe, 0xc0, 0x94, 0xae, 0x56, 0x6b, 0xe6,
	0x6a, 0x6d, 0x40, 0x27, 0x4c, 0xd3, 0xf1, 0x75, 0x20, 0x43, 0x95, 0xc4, 0xe6, 0x8c, 0x5d, 0x14,
	0x51, 0x0c, 0x27, 0x51, 0x6c, 0x28, 0x6e, 0x1b, 0x8a, 0x02, 0x25, 0x7c, 0x58, 0x4e, 0x52, 0x99,
	0x85, 0x3a, 0xc9, 0x8e, 0xd0, 0x39, 0x97, 0x88, 0xa4, 0x84, 0xc3, 0xf3, 0x27, 0xa6, 0xa4, 0x42,
	0x9b, 0xcf, 0x3f, 0x47, 0xd0, 0xb9, 0x64, 0xc9, 0xa5, 0xec, 0x4f, 0x46, 0x1e, 0x6c, 0x34, 0x50,
	0x42, 0x0b, 0xa3, 0x03, 0x2a, 0x1d, 0xea, 0xa9, 0xf2, 0x3a, 0xec, 0x80, 0x0c, 0xf9, 0x3f, 0x9f,
	0x3b, 0x2f, 0xba, 0x72, 0xd5, 0xfe, 0xe7, 0xff, 0x67, 0xad, 0x9a, 0x5c, 0xa5, 0x95, 0xee, 0x5a,
	0x36, 0x69, 0xfd, 0x46, 0x93, 0x36, 0x6e, 0x36, 0x69, 0x73, 0xde, 0xa4, 0xae, 0xba, 0xad, 0x19,
	0x75, 0xef, 0x01, 0x4c, 0x48, 0x48, 0x8c, 0x6a, 0xe6, 0x3c, 0x1c, 0x8c, 0x63, 0x8e, 0xdb, 0x25,
	0x73, 0xfc, 0xd8, 0x80, 0xb5, 0x1d, 0xdc, 0x63, 0x26, 0x33, 0xfc, 0x9f, 0xab, 0x77, 0x0f, 0xc0,
	0x9e, 0x6c, 0x7f, 0x68, 0xbd, 0xad, 0xc0, 0xcc, 0x79, 0x43, 0xab, 0xc2, 0x1b, 0x1e, 0xce, 0x29,
	0xb9, 0xba, 0xfd, 0xce, 0xdc, 0xbd, 0xa2, 0xb8, 0xee, 0xea, 0x3e, 0x63, 0xd9, 0xdb, 0xaf, 0x75,
	0xd6, 0xa5, 0x79, 0x67, 0x75, 0x6d, 0xdf, 0x9e, 0xb1, 0xfd, 0xaf, 0x60, 0x99, 0x77, 0x3b, 0x61,
	0x0b, 0x03, 0x09, 0xf6, 0xee, 0x9c, 0x60, 0x3c, 0x1d, 0x94, 0x88, 0xcd, 0x4d, 0xee, 0xd8, 0x9b,
	0xec, 0xff, 0x1a, 0xba, 0x27, 0x52, 0x1f, 0x4f, 0xd5, 0x85, 0x21, 0x5f, 0x70, 0x16, 0xc5, 0x81,
	0xd6, 0x4b, 0x07, 0xfa, 0x09, 0xbc, 0x73, 0x16, 0xef, 0x46, 0xf1, 0x10, 0x59, 0x04, 0x72, 0x1c,
	0xea, 0x28, 0x89, 0x17, 0xb9, 0xf7, 0x04, 0xee, 0xfe, 0x9e, 0xa4, 0x98, 0xc0, 0x07, 0x91, 0x0d,
	0x30, 0x38, 0xc4, 0x70, 0x15, 0x8e, 0xa3, 0x50, 0x99, 0xa3, 0x66, 0x80, 0x0c, 0x35, 0x0e, 0xf5,
	0x79, 0x92, 0x4d, 0xcc, 0x29, 0xe7, 0xb0, 0xff, 0x1f, 0x35, 0x58, 0xc5, 0xbd, 0x76, 0xaf, 0x77,
	0x90, 0x16, 0xb7, 0x5a, 0x87, 0x5b, 0xe8, 0x40, 0xf9, 0x66, 0x06, 0x42, 0xe6, 0x3a, 0xe9, 0x0f,
	0x51, 0x3b, 0x34, 0x36, 0x03, 0xe8, 0x00, 0x3a, 0xcc, 0x46, 0x52, 0x9f, 0x16, 0x79, 0xdd, 0x75,
	0x80, 0xd3, 0x7c, 0x32, 0x70, 0x08, 0xc5, 0x67, 0xb0, 0x94, 0x4e, 0xd5, 0x05, 0x2d, 0xe2, 0x54,
	0x70, 0xd7, 0x59, 0x74, 0x6c, 0xa6, 0x82, 0x9c, 0x48, 0x7c, 0x0a, 0xb7, 0x42, 0xad, 0xc3, 0xc1,
	0x05, 0xb9, 0x61, 0xa7, 0xb4, 0x07, 0x92, 0xef, 0xd0, 0x64, 0x60, 0x88, 0xfc, 0x4d, 0x80, 0x02,
	0x8b, 0x16, 0x18, 0x47, 0x97, 0x92, 0x76, 0x63, 0xa5, 0x72, 0xd8, 0x07, 0x58, 0xfa, 0x76, 0x92,
	0xea, 0xeb, 0x40, 0xa5, 0xfe, 0x26, 0x74, 0xf7, 0xa5, 0xc6, 0xdc, 0x7d, 0x72, 0x15, 0xe9, 0xc1,
	0x05, 0x9a, 0x63, 0x0d, 0x5a, 0x31, 0xa9, 0x5d, 0x63, 0xb5, 0x09, 0xf0, 0x1f, 0xcf, 0x52, 0xaa,
	0x54, 0x7c, 0x5c, 0xca, 0x2e, 0xae, 0x80, 0x4c, 0xc3, 0x55, 0x07, 0x92, 0xf8, 0x3f, 0xd6, 0x01,
	0x0a, 0xe4, 0xa2, 0x22, 0x21, 0x4d, 0x54, 0x84, 0x0e, 0x60, 0x8b, 0x04, 0x0b, 0x8b, 0x4d, 0xb8,
	0x93, 0x66, 0xd1, 0x65, 0xa8, 0xe5, 0xde, 0x45, 0x48, 0x9e, 0x69, 0x4e, 0x7c, 0x16, 0x6d, 0xee,
	0x70, 0x7c, 0x10, 0x5d, 0x4a, 0x22, 0x6b, 0xe6, 0x77, 0x38, 0xc7, 0x89, 0xcf, 0xe1, 0x6e, 0x38,
	0xd5, 0x49, 0x20, 0xcf, 0xa7, 0x4a, 0x1e, 0x4e, 0xc7, 0x3a, 0xc2, 0xe5, 0xe6, 0xba, 0x57, 0x4d,
	0x89, 0xfb, 0xb0, 0x5a, 0xa0, 0x29, 0x11, 0x70, 0x78, 0x9b, 0xc1, 0xa2, 0x9c, 0x61, 0x9c, 0xc4,
	0xd7, 0x93, 0x64, 0xaa, 0x76, 0xb3, 0xe4, 0x4a, 0x49, 0x73, 0xd5, 0x67, 0xd1, 0xfe, 0x7f, 0xd5,
	0xe8, 0x92, 0x95, 0x4d, 0xff, 0xff, 0xcd, 0x2c, 0x15, 0xea, 0xde, 0xaa, 0x56, 0x57, 0xcc, 0x6a,
	0xab, 0x52, 0xff, 0xcf, 0x41, 0x18, 0x97, 0xfa, 0x2e, 0x8c, 0xd5, 0x4d, 0x25, 0x98, 0x07, 0xb7,
	0xb1, 0xd8, 0x39, 0x9a, 0x4e, 0x4c, 0x5d, 0x62, 0xc1, 0x52, 0x59, 0xd4, 0x98, 0x29, 0x8b, 0x7e,
	0x33, 0xcf, 0x5f, 0xa5, 0xe2, 0x41, 0xc9, 0x69, 0xdd, 0x7a, 0xec, 0x44, 0x2a, 0x15, 0x25, 0xb1,
	0xe3, 0xb5, 0x67, 0xd0, 0x71, 0x90, 0x0b, 0x03, 0x85, 0x80, 0xa6, 0x76, 0x2a, 0x1f, 0x6d, 0x8e,
	0x2d, 0x33, 0xe1, 0xcc, 0xe6, 0x21, 0x0b, 0xfb, 0xab, 0xb0, 0xfc, 0x2c, 0x1e, 0x47, 0x31, 0x6a,
	0x80, 0x1d, 0xd0, 0x87, 0x2e, 0xac, 0x52, 0xbc, 0x81, 0x03, 0x2a, 0xc2, 0x6a, 0x5c, 0x84, 0x11,
	0xe0, 0xff, 0x75, 0x0d, 0x3a, 0x41, 0xf2, 0x22, 0xd1, 0x3b, 0xc3, 0x21, 0x1a, 0xea, 0x3e, 0xb4,
	0x32, 0x04, 0x89, 0xaa, 0xb3, 0xdd, 0x75, 0x34, 0x21, 0x7c, 0xc0, 0xd3, 0x28, 0xdd, 0x24, 0x19,
	0x72, 0xae, 0x6c, 0x05, 0x34, 0xc6, 0xda, 0x94, 0x26, 0xb1, 0x89, 0x88, 0x46, 0xa6, 0x1b, 0x58,
	0x9f, 0xe5, 0xc0, 0xb3, 0x81, 0x4b, 0xea, 0xff, 0x4d, 0xad, 0xb4, 0x14, 0x63, 0x72, 0x3c, 0x9d,
	0x90, 0x0c, 0xad, 0x00, 0x87, 0x58, 0x2f, 0x71, 0x95, 0x7e, 0x18, 0xc5, 0x66, 0xd3, 0x02, 0xe1,
	0xcc, 0x86, 0xaf, 0x68, 0xdf, 0x62, 0x36, 0x7c, 0x45, 0xb2, 0x86, 0xa6, 0x12, 0x44, 0x59, 0xc3,
	0x31, 0x95, 0x0d, 0xe7, 0x92, 0xb0, 0x2d, 0xc2, 0x1a, 0xc8, 0xff, 0xef, 0x9a, 0x31, 0x80, 0x6d,
	0x2f, 0x28, 0x67, 0xd7, 0x8a, 0xf6, 0xe2, 0xc8, 0xb4, 0x17, 0x23, 0x19, 0x0f, 0x65, 0x66, 0x73,
	0x14, 0x43, 0xb8, 0xd3, 0x8b, 0x24, 0xb3, 0x67, 0x43, 0x63, 0xc4, 0x0d, 0x22, 0x7d, 0x6d, 0x2e,
	0x07, 0x8d, 0x67, 0xdb, 0x10, 0x16, 0xa1, 0xd4, 0x86, 0xf4, 0x60, 0x09, 0xdb, 0x81, 0x93, 0x68,
	0x64, 0x8b, 0xd0, 0x1c, 0xc6, 0xdd, 0x2f, 0x64, 0x34, 0xba, 0xd0, 0x14, 0x06, 0x5a, 0x81, 0x81,
	0x10, 0x7f, 0xc5, 0xf8, 0x25, 0xc6, 0x33, 0x44, 0xb5, 0x26, 0xb5, 0x3f, 0x67, 0xd9, 0x38, 0xaf,
	0x35, 0x2d, 0xc2, 0xff, 0x3b, 0xd7, 0x03, 0x54, 0xea, 0xe4, 0x5f, 0xee, 0x0a, 0x0d, 0x84, 0xd7,
	0x45, 0x4d, 0x07, 0x03, 0xa9, 0x94, 0xb1, 0xbf, 0x05, 0x71, 0xe6, 0x3c, 0x8c, 0xc6, 0xd3, 0x4c,
	0x1a, 0xdb, 0x5b, 0x50, 0xfc, 0x02, 0x80, 0x8c, 0x89, 0x8e, 0x8e, 0xad, 0x1e, 0x5e, 0x8e, 0xb5,
	0x59, 0x87, 0xa0, 0xab, 0xe1, 0xd0, 0xf9, 0x0f, 0xa1, 0x9d, 0x43, 0xe8, 0xb6, 0x13, 0x35, 0xca,
	0x6f, 0x07, 0x03, 0x55, 0x6d, 0x81, 0xff, 0x1c, 0xd6, 0x4e, 0xa4, 0x66, 0x9f, 0xcf, 0x3b, 0x4e,
	0xce, 0xc4, 0xe6, 0xb0, 0x6a, 0xb3, 0x87, 0x35, 0xd7, 0x5a, 0x08, 0x68, 0xa2, 0x74, 0xf6, 0x00,
	0x71, 0xec, 0x7f, 0x01, 0xef, 0x56, 0xf1, 0xbd, 0xc1, 0x56, 0xfe, 0x06, 0x2c, 0x9d, 0x26, 0x2f,
	0x65, 0x6c, 0x32, 0x9f, 0xc6, 0xb1, 0x55, 0x80, 0x00, 0xbf, 0x97, 0x53, 0xa8, 0xd9, 0x9e, 0xc7,
	0x7f, 0x0f, 0x6e, 0xe3, 0x3e, 0xb8, 0x78, 0x15, 0xea, 0xb9, 0xea, 0xf5, 0xfe, 0xd0, 0x3f, 0x84,
	0x4e, 0x21, 0x82, 0x9a, 0x9d, 0xbe, 0xb1, 0x25, 0xae, 0xf0, 0x4d, 0x7f, 0x1b, 0x80, 0xd8, 0x0d,
	0xa9, 0x64, 0xe9, 0x42, 0xa3, 0xc8, 0xd0, 0x38, 0x24, 0xdf, 0x4d, 0xc6, 0xb6, 0x56, 0xa1, 0xb1,
	0xff, 0x25, 0x74, 0x0e, 0x93, 0x78, 0x94, 0xbc, 0xd1, 0xa2, 0x2f, 0xa0, 0x7d, 0x88, 0x07, 0xf7,
	0x06, 0x4b, 0x1e, 0x39, 0xaa, 0x52, 0x59, 0xd0, 0x42, 0x50, 0x99, 0x10, 0xeb, 0xd6, 0x39, 0x39,
	0x19, 0x53, 0xf8, 0x5f, 0xc2, 0xca, 0x19, 0x75, 0x83, 0x0b, 0xac, 0x88, 0xdb, 0x61, 0xb3, 0x49,
	0x26, 0x5a, 0x0e, 0x68, 0xec, 0xfb, 0xb0, 0x8c, 0xe4, 0xdf, 0xbe, 0x2a, 0x32, 0xc6, 0x91, 0x93,
	0x31, 0x70, 0x8c, 0x21, 0xb5, 0xa0, 0xe1, 0x90, 0x4a, 0x63, 0x13, 0xa8, 0x18, 0xf0, 0xff, 0x6a,
	0x19, 0x96, 0xf2, 0x87, 0x8b, 0x8a, 0xad, 0x8f, 0x1c, 0xa7, 0x3b, 0x32, 0x15, 0xaf, 0x29, 0xb0,
	0xf9, 0xfa, 0x18, 0x08, 0x4f, 0xf3, 0xc8, 0x46, 0x20, 0x53, 0x71, 0x1e, 0x39, 0x11, 0x68, 0x9f,
	0x9d, 0x9a, 0x33, 0xa9, 0x81, 0xd0, 0xb6, 0x3b, 0x23, 0x4e, 0x98, 0xad, 0x00, 0x87, 0x48, 0xf9,
	0x3d, 0x05, 0x16, 0x8a, 0x16, 0x8d, 0xc0, 0x40, 0xd4, 0x94, 0x5f, 0x84, 0xd9, 0x84, 0x82, 0x05,
	0x36, 0xe5, 0x08, 0xa0, 0x7c, 0x07, 0xd1, 0x4b, 0x6e, 0x49, 0x5b, 0x01, 0x8d, 0x51, 0x8e, 0x13,
	0x1b, 0x8b, 0x80, 0xe5, 0xb0, 0x30, 0xce, 0xed, 0x46, 0x99, 0xbe, 0x18, 0x86, 0xd7, 0xa6, 0xd6,
	0xcf, 0x61, 0x8c, 0x0b, 0xdf, 0x49, 0x39, 0x8e, 0xe2, 0x91, 0xb7, 0xc2, 0xaf, 0x18, 0x06, 0x44,
	0x99, 0x9e, 0x72, 0xa4, 0x5a, 0x65, 0x8d, 0x9f, 0xe6, 0x11, 0xec, 0x7b, 0xc6, 0xdf, 0x61, 0x3c,
	0x43, 0xc8, 0xa9, 0x3f, 0x19, 0x61, 0xb2, 0xf5, 0xba, 0xcc, 0xc9, 0x80, 0xd8, 0x7d, 0x05, 0x32,
	0x1c, 0x1b, 0xfb, 0xbd, 0xcd, 0xdd, 0x57, 0x81, 0xc1, 0xd8, 0xb7, 0x93, 0xc7, 0x3e, 0xc1, 0xb1,
	0x2f, 0x47, 0xa0, 0xf4, 0x3b, 0xd3, 0x61, 0x94, 0xe0, 0xe4, 0x5d, 0x96, 0xde, 0xc2, 0xb8, 0xf2,
	0xdb, 0xe1, 0x74, 0xc0, 0xc9, 0x76, 0x8d, 0x57, 0xe6, 0x08, 0xb4, 0xf3, 0x6f, 0x93, 0x17, 0xde,
	0x3b, 0xdc, 0x35, 0xfc, 0x36, 0x79, 0x81, 0xb2, 0xf7, 0xe3, 0x41, 0x32, 0x91, 0xde, 0x3a, 0x9f,
	0x08, 0x43, 0xb8, 0xc7, 0x71, 0x96, 0x5c, 0x46, 0xf1, 0x40, 0x7a, 0xef, 0xf2, 0x1e, 0x16, 0x46,
	0x6b, 0xef, 0x61, 0x6e, 0xf0, 0xd8, 0x1b, 0x70, 0x8c, 0xb8, 0x9d, 0x4c, 0x86, 0xde, 0x7b, 0x8c,
	0xc3, 0x31, 0xf2, 0x78, 0x9a, 0x4c, 0xe4, 0x69, 0x72, 0x15, 0x7b, 0x3d, 0xe6, 0x61, 0x61, 0xac,
	0x22, 0x51, 0x5f, 0xf4, 0x0a, 0x63, 0x85, 0xf7, 0xb9, 0x8a, 0x2c, 0x63, 0xf1, 0xbc, 0xfb, 0xb1,
	0xce, 0x12, 0xef, 0x27, 0x1c, 0x87, 0x08, 0x40, 0x3d, 0x0e, 0x92, 0xd8, 0xfb, 0xe9, 0x46, 0x6d,
	0xb3, 0x16, 0xe0, 0x90, 0x30, 0xa1, 0xf6, 0xee, 0x19, 0x4c, 0x48, 0x99, 0xb0, 0xaf, 0x38, 0xfe,
	0x79, 0x3f, 0xa3, 0x73, 0xc9, 0x61, 0xf1, 0x21, 0xac, 0x1c, 0x84, 0x4a, 0x1f, 0x24, 0xa3, 0x28,
	0xa6, 0x12, 0x76, 0x83, 0xb8, 0x97, 0x91, 0x78, 0x4a, 0x7b, 0xc5, 0xa3, 0xda, 0x07, 0xc4, 0xc3,
	0xc1, 0x60, 0x21, 0x19, 0xc8, 0x51, 0xa4, 0x74, 0xc6, 0x1d, 0xb0, 0xcf, 0x85, 0xa4, 0x8b, 0x43,
	0xb9, 0xce, 0xa2, 0xa1, 0xf7, 0x07, 0x6c, 0xf1, 0xb3, 0x88, 0x0a, 0xb8, 0xb3, 0x18, 0xcb, 0xa8,
	0xa1, 0xf7, 0x21, 0x7b, 0x85, 0x01, 0xd9, 0x26, 0xb8, 0x56, 0x66, 0xc6, 0x26, 0x7f, 0x68, 0x6d,
	0xe2, 0x62, 0x51, 0xae, 0x7e, 0x7c, 0x19, 0x69, 0xb9, 0x87, 0xb5, 0xcc, 0x7d, 0xf6, 0x9e, 0x02,
	0x83, 0x67, 0xb1, 0x8b, 0x31, 0xf3, 0x23, 0x3e, 0x0b, 0x1c, 0xa3, 0x1d, 0x29, 0x94, 0x79, 0x9b,
	0x6c, 0x47, 0x02, 0x50, 0x96, 0x60, 0x40, 0x11, 0xdd, 0xfb, 0x98, 0x65, 0x31, 0x20, 0xe6, 0xfa,
	0xef, 0x9d, 0x5c, 0xff, 0x80, 0x73, 0xbd, 0x83, 0x12, 0x9b, 0xd0, 0xda, 0x19, 0xbf, 0x98, 0x4e,
	0xbc, 0x4f, 0x28, 0xb4, 0x89, 0xd9, 0xd0, 0x36, 0x19, 0x05, 0x4c, 0x80, 0x27, 0xf1, 0x64, 0x9a,
	0xb1, 0x4b, 0xfe, 0x9c, 0x4f, 0xc2, 0xc2, 0x68, 0x43, 0x76, 0x6c, 0xa3, 0xf1, 0xa7, 0x6c, 0x43,
	0x17, 0x87, 0xb2, 0x90, 0x7f, 0x1b, 0x92, 0x2d, 0x7e, 0x0e, 0x70, 0x50, 0xe8, 0xf5, 0xfb, 0x59,
	0x32, 0x4d, 0xc9, 0x20, 0x9f, 0xb1, 0xd7, 0xe7, 0x08, 0x5a, 0x4f, 0xfc, 0x58, 0x97, 0xcf, 0xcd,
	0xfa, 0x02, 0x45, 0xb1, 0xe2, 0x30, 0x90, 0x6a, 0x3a, 0xd6, 0xde, 0x17, 0x26, 0x56, 0x18, 0xd8,
	0xce, 0xd1, 0x4b, 0xc4, 0x76, 0x31, 0x47, 0xcf, 0x10, 0x78, 0x7b, 0x86, 0x14, 0xe9, 0xbe, 0x34,
	0xb7, 0x87, 0x20, 0xb1, 0x05, 0x62, 0x2f, 0x89, 0x75, 0x16, 0x0e, 0x34, 0xc6, 0x1b, 0x23, 0xf8,
	0x2f, 0x88, 0xa6, 0x62, 0x06, 0x4f, 0xf4, 0x64, 0x9a, 0xca, 0x6c, 0x67, 0x38, 0x89, 0x62, 0xef,
	0x21, 0x9f, 0x68, 0x81, 0xe1, 0x7d, 0xf6, 0xc2, 0x6c, 0xe8, 0xfd, 0x91, 0xdd, 0x07, 0x21, 0xc4,
	0x9b, 0xf7, 0x9d, 0x5f, 0x32, 0xbe, 0x78, 0xbc, 0xa2, 0x97, 0x73, 0xd6, 0xd1, 0x7b, 0xc4, 0x1a,
	0x3b, 0x28, 0xf4, 0x07, 0x2a, 0x9f, 0xbc, 0xaf, 0xd8, 0x1f, 0x08, 0xf0, 0xbf, 0xe2, 0x1c, 0x6e,
	0x1e, 0xec, 0xfa, 0x93, 0x11, 0x86, 0x18, 0x53, 0x7f, 0x30, 0xe4, 0x84, 0x7d, 0x53, 0x44, 0x32,
	0xe4, 0x4b, 0xe8, 0x1c, 0x4f, 0xc7, 0x63, 0x9b, 0xbc, 0xd6, 0xa0, 0xf5, 0x27, 0x53, 0x99, 0xf1,
	0x27, 0x86, 0xe5, 0x80, 0x01, 0x8a, 0x1c, 0x4e, 0xc6, 0xc4, 0x31, 0x52, 0x1e, 0x44, 0x93, 0x48,
	0x9b, 0x34, 0xc2, 0x00, 0x6d, 0xaf, 0x82, 0x30, 0x1e, 0x9a, 0xfa, 0xd7, 0x40, 0x98, 0x5f, 0xf3,
	0x6d, 0xde, 0x2c, 0xbf, 0x3e, 0x84, 0x15, 0x16, 0x6e, 0x12, 0x66, 0x2f, 0x17, 0xe4, 0x4a, 0x9b,
	0xe4, 0xeb, 0x79, 0x92, 0xf7, 0xff, 0xb1, 0x56, 0x5a, 0xa7, 0x52, 0xf1, 0x94, 0xcb, 0x0f, 0x46,
	0x98, 0x8d, 0x37, 0x67, 0x36, 0xce, 0xa9, 0x1d, 0x88, 0x3f, 0xc6, 0x38, 0x6b, 0x7b, 0x8f, 0xe1,
	0xce, 0xcc, 0xf4, 0x1b, 0x7d, 0x98, 0xb9, 0x0f, 0xcb, 0xfb, 0xb6, 0xc4, 0x0b, 0xd4, 0xc2, 0x92,
	0x11, 0x0b, 0x00, 0x87, 0x8e, 0x0a, 0x80, 0xc2, 0x68, 0x6d, 0x6b, 0x9f, 0x7f, 0xaa, 0xc1, 0x2a,
	0x17, 0x20, 0x74, 0x6b, 0x4d, 0x0d, 0x3a, 0xa5, 0x42, 0xcb, 0x32, 0x64, 0x48, 0x7c, 0x0d, 0xad,
	0x90, 0xae, 0x7e, 0x9d, 0x94, 0xff, 0xd0, 0x55, 0xbe, 0xc4, 0x61, 0x8b, 0x06, 0xac, 0x38, 0x2f,
	0xe9, 0x3d, 0x02, 0x28, 0x90, 0x6f, 0xa4, 0xee, 0x66, 0x59, 0xbe, 0x1b, 0x0a, 0xd9, 0x7f, 0xa8,
	0xc1, 0xf2, 0xf3, 0x70, 0x1c, 0x0d, 0x9f, 0xca, 0x70, 0xb8, 0xa8, 0x91, 0x16, 0xd0, 0x8c, 0x26,
	0x32, 0xb2, 0x35, 0x0d, 0x8e, 0xa9, 0xa6, 0x4d, 0x4d, 0xad, 0x59, 0x8f, 0x52, 0x14, 0x6f, 0x6c,
	0x1e, 0x7f, 0xeb, 0x01, 0x0e, 0x09, 0x63, 0x1e, 0x02, 0x10, 0x13, 0x6a, 0xc4, 0x4c, 0xb3, 0xb1,
	0x69, 0x77, 0x70, 0xb8, 0xf0, 0x71, 0xd7, 0x77, 0xa5, 0xe2, 0x27, 0xeb, 0x01, 0x06, 0x2c, 0xae,
	0xc3, 0x68, 0xec, 0xff, 0x6d, 0x0d, 0x56, 0x02, 0x79, 0x19, 0xc9, 0x2b, 0xdb, 0x08, 0xac, 0x41,
	0x4b, 0x5e, 0x4a, 0xd3, 0x01, 0xb7, 0x03, 0x06, 0xa8, 0xa7, 0x9e, 0x8e, 0xf9, 0x55, 0xcb, 0xd4,
	0xcc, 0x16, 0xc6, 0xa8, 0x9e, 0xc9, 0x1f, 0x4e, 0xe5, 0x2b, 0x6d, 0xbf, 0xc3, 0x18, 0x90, 0x66,
	0xc2, 0x2b, 0x9a, 0x69, 0x9a, 0x19, 0x06, 0x73, 0x0b, 0xb5, 0x9c, 0xd6, 0xe4, 0xa3, 0x92, 0x28,
	0x73, 0xf6, 0x2e, 0x14, 0xc3, 0x54, 0x7b, 0x7c, 0x90, 0x0c, 0x4c, 0xd5, 0x1a, 0xa5, 0x79, 0x5b,
	0x90, 0xfa, 0xf7, 0xed, 0x9c, 0x4a, 0xed, 0xab, 0x2d, 0x15, 0x15, 0xa6, 0x39, 0xb5, 0xb0, 0xbf,
	0xc3, 0x77, 0xa1, 0x1f, 0x6b, 0x99, 0x49, 0xa5, 0x4f, 0xc3, 0x11, 0xf5, 0x20, 0xa1, 0xd3, 0x44,
	0x11, 0x80, 0x3a, 0xe8, 0x70, 0x74, 0x54, 0x34, 0x0b, 0x16, 0xf4, 0x37, 0xf3, 0x47, 0x0e, 0xcb,
	0x65, 0xd1, 0x43, 0xeb, 0xd1, 0x3c, 0xa5, 0x4a, 0xc5, 0x23, 0x68, 0x47, 0x06, 0xb4, 0x01, 0xa5,
	0x37, 0x17, 0x50, 0x72, 0xf1, 0x82, 0x82, 0xd8, 0xff, 0x04, 0xee, 0xce, 0xf0, 0x53, 0x8b, 0x9f,
	0x0f, 0x3f, 0x2b, 0x6b, 0xaa, 0x9e, 0x3f, 0xc3, 0xfc, 0x55, 0xde, 0xb9, 0xed, 0x72, 0xff, 0xe7,
	0x5a, 0x05, 0x7b, 0x95, 0x8a, 0xaf, 0xa0, 0x91, 0x49, 0xfb, 0x7a, 0xf3, 0x91, 0x23, 0x69, 0x05,
	0xf1, 0x56, 0x20, 0x35, 0xdf, 0x43, 0x5c, 0xd3, 0x0b, 0x60, 0xc9, 0x22, 0x2a, 0xee, 0xe0, 0xe7,
	0xee, 0x1d, 0x5c, 0x6c, 0x04, 0xf5, 0xfc, 0x99, 0x73, 0x3f, 0x1f, 0x7c, 0x07, 0x50, 0xbc, 0xf8,
	0x8b, 0x65, 0x58, 0x3a, 0x4a, 0x62, 0x72, 0xc8, 0xee, 0x5b, 0xc2, 0x34, 0x17, 0x27, 0x72, 0x7c,
	0xde, 0xad, 0x89, 0x15, 0x68, 0x53, 0x5e, 0x7b, 0x2a, 0xc7, 0x69, 0xb7, 0x9e, 0x83, 0x3b, 0x53,
	0x7d, 0xd1, 0x6d, 0x3c, 0x38, 0x80, 0xe5, 0xc3, 0xf2, 0x93, 0x3c, 0x3c, 0x4f, 0xa2, 0x21, 0x43,
	0xdd, 0xb7, 0xc4, 0x12, 0x34, 0x8f, 0x43, 0xa5, 0xba, 0x35, 0xd1, 0x86, 0x16, 0x7d, 0x2c, 0xe9,
	0xd6, 0x05, 0xc0, 0xad, 0x40, 0xfe, 0x4e, 0x0e, 0x74, 0xb7, 0x21, 0x3a, 0x70, 0xdb, 0x7c, 0x13,
	0xef, 0x36, 0x1f, 0xfc, 0x58, 0x83, 0x8e, 0xf3, 0x75, 0x11, 0xb9, 0xa1, 0x5c, 0x8c, 0xea, 0xbe,
	0x85, 0xb0, 0x9d, 0x26, 0x9e, 0x6b, 0xd0, 0x65, 0x18, 0x65, 0x65, 0x39, 0xba, 0x75, 0xf1, 0x36,
	0xac, 0x30, 0xd6, 0x32, 0x6e, 0x88, 0x2e, 0x2c, 0x33, 0xca, 0xec, 0xdb, 0x14, 0x77, 0xe1, 0x0e,
	0x63, 0x0a, 0x6d, 0x5a, 0xe2, 0x8e, 0xdd, 0x9e, 0x25, 0xbd, 0xf5, 0x60, 0x17, 0xda, 0xf9, 0xf7,
	0x6e, 0x54, 0x1d, 0xa5, 0x21, 0x44, 0xf7, 0x2d, 0xdc, 0x86, 0x86, 0xf6, 0x83, 0x76, 0xb7, 0x86,
	0x4c, 0x09, 0x45, 0x69, 0x91, 0xbe, 0x34, 0x77, 0xeb, 0x0f, 0x7e, 0x07, 0x4b, 0xf6, 0x99, 0x1c,
	0xb5, 0x3d, 0x8b, 0x5f, 0xc6, 0xc9, 0x55, 0xcc, 0xda, 0x1c, 0xf3, 0x73, 0xe7, 0xa1, 0x1a, 0x75,
	0x6b, 0xb8, 0xbb, 0x81, 0xf7, 0xa3, 0x73, 0xcd, 0xb6, 0x7e, 0x2a, 0xc3, 0x4c, 0x13, 0xd8, 0x40,
	0xfa, 0x9d, 0xc1, 0x20, 0x51, 0x0c, 0x37, 0x49, 0xcf, 0x64, 0x3c, 0x4e, 0xae, 0x64, 0xa6, 0x9e,
	0xa5, 0x32, 0xee, 0xb6, 0x1e, 0x3c, 0x04, 0x28, 0xde, 0xf1, 0xd1, 0xf8, 0x28, 0x30, 0x1f, 0xe9,
	0x33, 0xf3, 0x64, 0xca, 0x1b, 0xed, 0x5d, 0x84, 0xfa, 0x50, 0x2a, 0x15, 0x8e, 0x64, 0xb7, 0xbe,
	0xfd, 0xaf, 0x02, 0x9a, 0x78, 0xe4, 0xe2, 0x21, 0xb4, 0xa9, 0xa4, 0x3c, 0x4d, 0xfa, 0x4f, 0x84,
	0x9b, 0xa0, 0xed, 0x5b, 0x43, 0xaf, 0x02, 0xa9, 0xc4, 0x57, 0xd0, 0xc9, 0xdd, 0xf8, 0x3c, 0x11,
	0x62, 0x2e, 0xc1, 0xfe, 0xd0, 0x5b, 0xaf, 0xca, 0xf6, 0x52, 0x89, 0x5f, 0xc3, 0x8a, 0x59, 0xaa,
	0x76, 0xaf, 0xb1, 0x51, 0x7f, 0x67, 0x96, 0x90, 0x3a, 0xfa, 0xea, 0xf5, 0x2a, 0x15, 0x4f, 0xf2,
	0xe8, 0xa0, 0x76, 0xaf, 0xed, 0xab, 0x81, 0x58, 0x2f, 0x7d, 0x40, 0xca, 0x9f, 0x12, 0x16, 0x72,
	0xf9, 0x4d, 0xfe, 0x95, 0x00, 0xb9, 0xd0, 0x33, 0x82, 0x70, 0x5f, 0x91, 0xf2, 0x97, 0x85, 0x85,
	0x1c, 0xbe, 0xb1, 0x09, 0x2f, 0xb7, 0x82, 0x37, 0x97, 0x69, 0xad, 0x2d, 0xaa, 0x2a, 0x1f, 0xf1,
	0x0d, 0x74, 0xfa, 0x2a, 0xef, 0xfd, 0xc5, 0xbb, 0x33, 0x34, 0xf6, 0xd5, 0xa0, 0x57, 0x3d, 0xa1,
	0x52, 0xf1, 0x35, 0xfa, 0x19, 0x57, 0x5b, 0x25, 0xfd, 0x9d, 0x4a, 0xaf, 0x57, 0x89, 0x57, 0xa9,
	0xd8, 0xcb, 0x4f, 0x81, 0xeb, 0x9b, 0xb2, 0xf0, 0x6e, 0x25, 0xd6, 0xf3, 0x16, 0x55, 0x4f, 0xe2,
	0x31, 0xb4, 0xf3, 0xd2, 0xa5, 0x24, 0xbf, 0x5b, 0xf8, 0xf4, 0x16, 0x4c, 0xa4, 0xa2, 0x0f, 0x6f,
	0x3b, 0x25, 0x83, 0x89, 0x27, 0xef, 0x2d, 0x2c, 0x57, 0x7a, 0x8b, 0xa6, 0x54, 0x2a, 0x8e, 0xa1,
	0x9d, 0xbf, 0xa7, 0x89, 0x9f, 0x95, 0x9e, 0xca, 0xe7, 0x5f, 0xd9, 0x7a, 0xfe, 0x6b, 0x08, 0x8c,
	0x71, 0xed, 0x0b, 0x66, 0xc9, 0xb8, 0xce, 0xc3, 0x76, 0xaf, 0x12, 0xcf, 0x76, 0xc9, 0x9f, 0xc9,
	0x4b, 0x76, 0x71, 0x1f, 0xd3, 0x7b, 0xd5, 0x13, 0xe4, 0x59, 0x9d, 0xbc, 0x12, 0x91, 0x59, 0x89,
	0x81, 0x5b, 0x37, 0xf5, 0xaa, 0x27, 0x1c, 0x06, 0xbb, 0xd7, 0x7d, 0xac, 0x9b, 0xde, 0x9c, 0xc1,
	0x63, 0x68, 0x5b, 0x06, 0xc7, 0xff, 0x8b, 0xe5, 0x7d, 0x58, 0x29, 0x7d, 0x43, 0x11, 0xef, 0x97,
	0x0d, 0x5e, 0xfa, 0x96, 0xd4, 0x5b, 0x3c, 0xc9, 0xac, 0xf6, 0x17, 0xb2, 0xda, 0xbf, 0x89, 0xd5,
	0xdc, 0x47, 0xc0, 0x67, 0x70, 0x67, 0xe6, 0x2b, 0x8b, 0xf8, 0xe9, 0x3c, 0xbd, 0xf3, 0x85, 0xa7,
	0x77, 0xd3, 0xb4, 0x4a, 0xc5, 0x3e, 0x74, 0x67, 0x3f, 0x08, 0x8b, 0x7b, 0xce, 0x92, 0x8a, 0xaf,
	0xc5, 0xa5, 0x48, 0x60, 0x3f, 0x6e, 0x8a, 0x3f, 0x06, 0x31, 0xff, 0x19, 0x5a, 0x6c, 0xb8, 0xee,
	0x1e, 0xff, 0xde, 0xcc, 0x1e, 0x63, 0x0f, 0x96, 0x7f, 0x36, 0x2e, 0xdd, 0xa7, 0xf2, 0xe7, 0xe4,
	0xea, 0xe5, 0x3b, 0x74, 0x76, 0xc5, 0x27, 0xf5, 0xd9, 0xb3, 0x2b, 0x7d, 0x6c, 0xaf, 0x66, 0xf1,
	0x04, 0x56, 0xb9, 0x32, 0xc5, 0xda, 0x75, 0x2e, 0x32, 0x96, 0xea, 0xe7, 0xde, 0x82, 0x19, 0xaa,
	0x9f, 0xf0, 0xe4, 0xa9, 0x3a, 0x65, 0x7b, 0xb8, 0x7b, 0xd9, 0x82, 0xb6, 0x37, 0x8f, 0xe4, 0xc0,
	0x32, 0xf7, 0x37, 0x8d, 0x52, 0x54, 0xa8, 0xfa, 0x13, 0x47, 0xb5, 0x2e, 0x7f, 0x46, 0xc5, 0xdd,
	0xec, 0x5f, 0x5a, 0xc4, 0x07, 0x65, 0xcf, 0xa8, 0xf8, 0x87, 0x4c, 0xef, 0x75, 0x24, 0x95, 0xcc,
	0xc9, 0x2b, 0x6f, 0x58, 0x69, 0x3d, 0xf3, 0x75, 0x24, 0x2a, 0x15, 0x67, 0xf0, 0xf6, 0xdc, 0xbf,
	0xf4, 0x4a, 0x46, 0xa8, 0xfa, 0xdb, 0x60, 0xef, 0x66, 0x02, 0x96, 0xb9, 0xe2, 0x2f, 0x8d, 0x25,
	0x99, 0xab, 0xff, 0x3e, 0xd9, 0xfb, 0xe0, 0xb5, 0xff, 0x8a, 0x74, 0xae, 0xa8, 0xad, 0x62, 0xab,
	0xae, 0xa8, 0xd3, 0x3f, 0xf4, 0x6e, 0x9a, 0x56, 0xa9, 0x08, 0xf2, 0x34, 0x9f, 0x97, 0xc5, 0xa5,
	0x2b, 0x5a, 0xd1, 0x17, 0xf4, 0xee, 0xdd, 0x5c, 0xab, 0xef, 0xde, 0xfe, 0xd3, 0xd6, 0xd6, 0xaf,
	0xc2, 0x34, 0x7a, 0x71, 0x8b, 0xfe, 0x3b, 0xfa, 0xe5, 0xff, 0x04, 0x00, 0x00, 0xff, 0xff, 0xf4,
	0x57, 0x40, 0x89, 0x49, 0x2a, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// UserClient is the client API for User service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UserClient interface {
	TokenToID(ctx context.Context, in *TokenReq, opts ...grpc.CallOption) (*TokenRes, error)
	GetUserInfo(ctx context.Context, in *UserReq, opts ...grpc.CallOption) (*UserInfoRes, error)
	GetUsersByIds(ctx context.Context, in *UserIdsReq, opts ...grpc.CallOption) (*UserInfoRsp, error)
	GetUsersByMongoIds(ctx context.Context, in *MongoIdsReq, opts ...grpc.CallOption) (*UserInfoRsp, error)
	GetUsersByMsgIds(ctx context.Context, in *MsgIdsReq, opts ...grpc.CallOption) (*UserInfoRsp, error)
	UpdateUserInfo(ctx context.Context, in *UpdateUserReq, opts ...grpc.CallOption) (*UserInfo, error)
	IsUserExist(ctx context.Context, in *UserExistReq, opts ...grpc.CallOption) (*UserExistRsp, error)
	//指定条件拉取用户数据
	PullUser(ctx context.Context, in *PullUserReq, opts ...grpc.CallOption) (*PullUserRsp, error)
	//查询用户备注
	GetUserRemark(ctx context.Context, in *UserRemarkReq, opts ...grpc.CallOption) (*UserRemarkRsp, error)
	GetOnline(ctx context.Context, in *GetOnlineRsq, opts ...grpc.CallOption) (*GetOnlineRsp, error)
	UpdateAlbumStatus(ctx context.Context, in *UpdateAlbumReq, opts ...grpc.CallOption) (*UpdateAlbumRsp, error)
	//设置用户在线信息
	SetOnline(ctx context.Context, in *SetOnlineUserInfoReq, opts ...grpc.CallOption) (*SetOnlineUserInfoReqRsp, error)
	//新增机器人
	RobotAdd(ctx context.Context, in *RobotAddReq, opts ...grpc.CallOption) (*RobotAddRsp, error)
	OnlineNum(ctx context.Context, in *OnlineNumReq, opts ...grpc.CallOption) (*OnlineNumRsp, error)
	ValidHeader(ctx context.Context, in *ValidHeadReq, opts ...grpc.CallOption) (*ValidHeadRsp, error)
	ValidByImei(ctx context.Context, in *ValidHeadReq, opts ...grpc.CallOption) (*ValidHeadRsp, error)
	ValidByIP(ctx context.Context, in *ValidHeadReq, opts ...grpc.CallOption) (*ValidHeadRsp, error)
	// 设置用户开关
	SetUserSwitch(ctx context.Context, in *SetUserSwitchReq, opts ...grpc.CallOption) (*SetUserSwitchRsp, error)
	// 获取用户开关状态
	GetUserSwitch(ctx context.Context, in *GetUserSwitchReq, opts ...grpc.CallOption) (*GetUserSwitchRsp, error)
	// 获取用户粉丝列表
	GetUserFansList(ctx context.Context, in *GetUserFansListReq, opts ...grpc.CallOption) (*GetUserFansListRsp, error)
	// 绑定用户个推关系
	BindPushRelation(ctx context.Context, in *BindPushRelationReq, opts ...grpc.CallOption) (*EmptyRsp, error)
	// 解绑用户个推关系
	UnBindPushRelation(ctx context.Context, in *UnBindPushRelationReq, opts ...grpc.CallOption) (*EmptyRsp, error)
	// 根据个推别名推送
	PushByAlias(ctx context.Context, in *PushByAliasReq, opts ...grpc.CallOption) (*EmptyRsp, error)
	SetPushStatus(ctx context.Context, in *SetPushStatusReq, opts ...grpc.CallOption) (*EmptyRsp, error)
	//审核文字
	ReviewTextInfo(ctx context.Context, in *ReviewInfoReq, opts ...grpc.CallOption) (*ReviewInfoRsp, error)
	GetIPLocation(ctx context.Context, in *IPLocReq, opts ...grpc.CallOption) (*IPLocRsp, error)
	ApplyModifyMobile(ctx context.Context, in *ApplyModifyMobileReq, opts ...grpc.CallOption) (*EmptyRsp, error)
	GetModifyMobileInfo(ctx context.Context, in *GetModifyMobileInfoReq, opts ...grpc.CallOption) (*GetModifyMobileInfoRsp, error)
	GetModifyMobileList(ctx context.Context, in *GetModifyMobileListReq, opts ...grpc.CallOption) (*GetModifyMobileListRsp, error)
	GetAndCheckMobile(ctx context.Context, in *GetAndCheckMobileReq, opts ...grpc.CallOption) (*GetAndCheckMobileRsp, error)
	TimeoutModifyMobile(ctx context.Context, in *TimeoutModifyMobileReq, opts ...grpc.CallOption) (*TimeoutModifyMobileRsp, error)
	GetUserInterest(ctx context.Context, in *GetUserInterestReq, opts ...grpc.CallOption) (*GetUserInterestRsp, error)
	GetUserInterests(ctx context.Context, in *GetUserInterestsReq, opts ...grpc.CallOption) (*GetUserInterestsRsp, error)
}

type userClient struct {
	cc *grpc.ClientConn
}

func NewUserClient(cc *grpc.ClientConn) UserClient {
	return &userClient{cc}
}

func (c *userClient) TokenToID(ctx context.Context, in *TokenReq, opts ...grpc.CallOption) (*TokenRes, error) {
	out := new(TokenRes)
	err := c.cc.Invoke(ctx, "/user.base.User/TokenToID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) GetUserInfo(ctx context.Context, in *UserReq, opts ...grpc.CallOption) (*UserInfoRes, error) {
	out := new(UserInfoRes)
	err := c.cc.Invoke(ctx, "/user.base.User/GetUserInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) GetUsersByIds(ctx context.Context, in *UserIdsReq, opts ...grpc.CallOption) (*UserInfoRsp, error) {
	out := new(UserInfoRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/GetUsersByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) GetUsersByMongoIds(ctx context.Context, in *MongoIdsReq, opts ...grpc.CallOption) (*UserInfoRsp, error) {
	out := new(UserInfoRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/GetUsersByMongoIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) GetUsersByMsgIds(ctx context.Context, in *MsgIdsReq, opts ...grpc.CallOption) (*UserInfoRsp, error) {
	out := new(UserInfoRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/GetUsersByMsgIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) UpdateUserInfo(ctx context.Context, in *UpdateUserReq, opts ...grpc.CallOption) (*UserInfo, error) {
	out := new(UserInfo)
	err := c.cc.Invoke(ctx, "/user.base.User/UpdateUserInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) IsUserExist(ctx context.Context, in *UserExistReq, opts ...grpc.CallOption) (*UserExistRsp, error) {
	out := new(UserExistRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/IsUserExist", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) PullUser(ctx context.Context, in *PullUserReq, opts ...grpc.CallOption) (*PullUserRsp, error) {
	out := new(PullUserRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/PullUser", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) GetUserRemark(ctx context.Context, in *UserRemarkReq, opts ...grpc.CallOption) (*UserRemarkRsp, error) {
	out := new(UserRemarkRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/GetUserRemark", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) GetOnline(ctx context.Context, in *GetOnlineRsq, opts ...grpc.CallOption) (*GetOnlineRsp, error) {
	out := new(GetOnlineRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/GetOnline", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) UpdateAlbumStatus(ctx context.Context, in *UpdateAlbumReq, opts ...grpc.CallOption) (*UpdateAlbumRsp, error) {
	out := new(UpdateAlbumRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/UpdateAlbumStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) SetOnline(ctx context.Context, in *SetOnlineUserInfoReq, opts ...grpc.CallOption) (*SetOnlineUserInfoReqRsp, error) {
	out := new(SetOnlineUserInfoReqRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/SetOnline", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) RobotAdd(ctx context.Context, in *RobotAddReq, opts ...grpc.CallOption) (*RobotAddRsp, error) {
	out := new(RobotAddRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/RobotAdd", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) OnlineNum(ctx context.Context, in *OnlineNumReq, opts ...grpc.CallOption) (*OnlineNumRsp, error) {
	out := new(OnlineNumRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/OnlineNum", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) ValidHeader(ctx context.Context, in *ValidHeadReq, opts ...grpc.CallOption) (*ValidHeadRsp, error) {
	out := new(ValidHeadRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/ValidHeader", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) ValidByImei(ctx context.Context, in *ValidHeadReq, opts ...grpc.CallOption) (*ValidHeadRsp, error) {
	out := new(ValidHeadRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/ValidByImei", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) ValidByIP(ctx context.Context, in *ValidHeadReq, opts ...grpc.CallOption) (*ValidHeadRsp, error) {
	out := new(ValidHeadRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/ValidByIP", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) SetUserSwitch(ctx context.Context, in *SetUserSwitchReq, opts ...grpc.CallOption) (*SetUserSwitchRsp, error) {
	out := new(SetUserSwitchRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/SetUserSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) GetUserSwitch(ctx context.Context, in *GetUserSwitchReq, opts ...grpc.CallOption) (*GetUserSwitchRsp, error) {
	out := new(GetUserSwitchRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/GetUserSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) GetUserFansList(ctx context.Context, in *GetUserFansListReq, opts ...grpc.CallOption) (*GetUserFansListRsp, error) {
	out := new(GetUserFansListRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/GetUserFansList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) BindPushRelation(ctx context.Context, in *BindPushRelationReq, opts ...grpc.CallOption) (*EmptyRsp, error) {
	out := new(EmptyRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/BindPushRelation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) UnBindPushRelation(ctx context.Context, in *UnBindPushRelationReq, opts ...grpc.CallOption) (*EmptyRsp, error) {
	out := new(EmptyRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/UnBindPushRelation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) PushByAlias(ctx context.Context, in *PushByAliasReq, opts ...grpc.CallOption) (*EmptyRsp, error) {
	out := new(EmptyRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/PushByAlias", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) SetPushStatus(ctx context.Context, in *SetPushStatusReq, opts ...grpc.CallOption) (*EmptyRsp, error) {
	out := new(EmptyRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/SetPushStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) ReviewTextInfo(ctx context.Context, in *ReviewInfoReq, opts ...grpc.CallOption) (*ReviewInfoRsp, error) {
	out := new(ReviewInfoRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/ReviewTextInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) GetIPLocation(ctx context.Context, in *IPLocReq, opts ...grpc.CallOption) (*IPLocRsp, error) {
	out := new(IPLocRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/GetIPLocation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) ApplyModifyMobile(ctx context.Context, in *ApplyModifyMobileReq, opts ...grpc.CallOption) (*EmptyRsp, error) {
	out := new(EmptyRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/ApplyModifyMobile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) GetModifyMobileInfo(ctx context.Context, in *GetModifyMobileInfoReq, opts ...grpc.CallOption) (*GetModifyMobileInfoRsp, error) {
	out := new(GetModifyMobileInfoRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/GetModifyMobileInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) GetModifyMobileList(ctx context.Context, in *GetModifyMobileListReq, opts ...grpc.CallOption) (*GetModifyMobileListRsp, error) {
	out := new(GetModifyMobileListRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/GetModifyMobileList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) GetAndCheckMobile(ctx context.Context, in *GetAndCheckMobileReq, opts ...grpc.CallOption) (*GetAndCheckMobileRsp, error) {
	out := new(GetAndCheckMobileRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/GetAndCheckMobile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) TimeoutModifyMobile(ctx context.Context, in *TimeoutModifyMobileReq, opts ...grpc.CallOption) (*TimeoutModifyMobileRsp, error) {
	out := new(TimeoutModifyMobileRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/TimeoutModifyMobile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) GetUserInterest(ctx context.Context, in *GetUserInterestReq, opts ...grpc.CallOption) (*GetUserInterestRsp, error) {
	out := new(GetUserInterestRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/GetUserInterest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) GetUserInterests(ctx context.Context, in *GetUserInterestsReq, opts ...grpc.CallOption) (*GetUserInterestsRsp, error) {
	out := new(GetUserInterestsRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/GetUserInterests", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserServer is the server API for User service.
type UserServer interface {
	TokenToID(context.Context, *TokenReq) (*TokenRes, error)
	GetUserInfo(context.Context, *UserReq) (*UserInfoRes, error)
	GetUsersByIds(context.Context, *UserIdsReq) (*UserInfoRsp, error)
	GetUsersByMongoIds(context.Context, *MongoIdsReq) (*UserInfoRsp, error)
	GetUsersByMsgIds(context.Context, *MsgIdsReq) (*UserInfoRsp, error)
	UpdateUserInfo(context.Context, *UpdateUserReq) (*UserInfo, error)
	IsUserExist(context.Context, *UserExistReq) (*UserExistRsp, error)
	//指定条件拉取用户数据
	PullUser(context.Context, *PullUserReq) (*PullUserRsp, error)
	//查询用户备注
	GetUserRemark(context.Context, *UserRemarkReq) (*UserRemarkRsp, error)
	GetOnline(context.Context, *GetOnlineRsq) (*GetOnlineRsp, error)
	UpdateAlbumStatus(context.Context, *UpdateAlbumReq) (*UpdateAlbumRsp, error)
	//设置用户在线信息
	SetOnline(context.Context, *SetOnlineUserInfoReq) (*SetOnlineUserInfoReqRsp, error)
	//新增机器人
	RobotAdd(context.Context, *RobotAddReq) (*RobotAddRsp, error)
	OnlineNum(context.Context, *OnlineNumReq) (*OnlineNumRsp, error)
	ValidHeader(context.Context, *ValidHeadReq) (*ValidHeadRsp, error)
	ValidByImei(context.Context, *ValidHeadReq) (*ValidHeadRsp, error)
	ValidByIP(context.Context, *ValidHeadReq) (*ValidHeadRsp, error)
	// 设置用户开关
	SetUserSwitch(context.Context, *SetUserSwitchReq) (*SetUserSwitchRsp, error)
	// 获取用户开关状态
	GetUserSwitch(context.Context, *GetUserSwitchReq) (*GetUserSwitchRsp, error)
	// 获取用户粉丝列表
	GetUserFansList(context.Context, *GetUserFansListReq) (*GetUserFansListRsp, error)
	// 绑定用户个推关系
	BindPushRelation(context.Context, *BindPushRelationReq) (*EmptyRsp, error)
	// 解绑用户个推关系
	UnBindPushRelation(context.Context, *UnBindPushRelationReq) (*EmptyRsp, error)
	// 根据个推别名推送
	PushByAlias(context.Context, *PushByAliasReq) (*EmptyRsp, error)
	SetPushStatus(context.Context, *SetPushStatusReq) (*EmptyRsp, error)
	//审核文字
	ReviewTextInfo(context.Context, *ReviewInfoReq) (*ReviewInfoRsp, error)
	GetIPLocation(context.Context, *IPLocReq) (*IPLocRsp, error)
	ApplyModifyMobile(context.Context, *ApplyModifyMobileReq) (*EmptyRsp, error)
	GetModifyMobileInfo(context.Context, *GetModifyMobileInfoReq) (*GetModifyMobileInfoRsp, error)
	GetModifyMobileList(context.Context, *GetModifyMobileListReq) (*GetModifyMobileListRsp, error)
	GetAndCheckMobile(context.Context, *GetAndCheckMobileReq) (*GetAndCheckMobileRsp, error)
	TimeoutModifyMobile(context.Context, *TimeoutModifyMobileReq) (*TimeoutModifyMobileRsp, error)
	GetUserInterest(context.Context, *GetUserInterestReq) (*GetUserInterestRsp, error)
	GetUserInterests(context.Context, *GetUserInterestsReq) (*GetUserInterestsRsp, error)
}

// UnimplementedUserServer can be embedded to have forward compatible implementations.
type UnimplementedUserServer struct {
}

func (*UnimplementedUserServer) TokenToID(ctx context.Context, req *TokenReq) (*TokenRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TokenToID not implemented")
}
func (*UnimplementedUserServer) GetUserInfo(ctx context.Context, req *UserReq) (*UserInfoRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserInfo not implemented")
}
func (*UnimplementedUserServer) GetUsersByIds(ctx context.Context, req *UserIdsReq) (*UserInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUsersByIds not implemented")
}
func (*UnimplementedUserServer) GetUsersByMongoIds(ctx context.Context, req *MongoIdsReq) (*UserInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUsersByMongoIds not implemented")
}
func (*UnimplementedUserServer) GetUsersByMsgIds(ctx context.Context, req *MsgIdsReq) (*UserInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUsersByMsgIds not implemented")
}
func (*UnimplementedUserServer) UpdateUserInfo(ctx context.Context, req *UpdateUserReq) (*UserInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserInfo not implemented")
}
func (*UnimplementedUserServer) IsUserExist(ctx context.Context, req *UserExistReq) (*UserExistRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsUserExist not implemented")
}
func (*UnimplementedUserServer) PullUser(ctx context.Context, req *PullUserReq) (*PullUserRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PullUser not implemented")
}
func (*UnimplementedUserServer) GetUserRemark(ctx context.Context, req *UserRemarkReq) (*UserRemarkRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserRemark not implemented")
}
func (*UnimplementedUserServer) GetOnline(ctx context.Context, req *GetOnlineRsq) (*GetOnlineRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOnline not implemented")
}
func (*UnimplementedUserServer) UpdateAlbumStatus(ctx context.Context, req *UpdateAlbumReq) (*UpdateAlbumRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAlbumStatus not implemented")
}
func (*UnimplementedUserServer) SetOnline(ctx context.Context, req *SetOnlineUserInfoReq) (*SetOnlineUserInfoReqRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetOnline not implemented")
}
func (*UnimplementedUserServer) RobotAdd(ctx context.Context, req *RobotAddReq) (*RobotAddRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RobotAdd not implemented")
}
func (*UnimplementedUserServer) OnlineNum(ctx context.Context, req *OnlineNumReq) (*OnlineNumRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OnlineNum not implemented")
}
func (*UnimplementedUserServer) ValidHeader(ctx context.Context, req *ValidHeadReq) (*ValidHeadRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidHeader not implemented")
}
func (*UnimplementedUserServer) ValidByImei(ctx context.Context, req *ValidHeadReq) (*ValidHeadRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidByImei not implemented")
}
func (*UnimplementedUserServer) ValidByIP(ctx context.Context, req *ValidHeadReq) (*ValidHeadRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidByIP not implemented")
}
func (*UnimplementedUserServer) SetUserSwitch(ctx context.Context, req *SetUserSwitchReq) (*SetUserSwitchRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetUserSwitch not implemented")
}
func (*UnimplementedUserServer) GetUserSwitch(ctx context.Context, req *GetUserSwitchReq) (*GetUserSwitchRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserSwitch not implemented")
}
func (*UnimplementedUserServer) GetUserFansList(ctx context.Context, req *GetUserFansListReq) (*GetUserFansListRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserFansList not implemented")
}
func (*UnimplementedUserServer) BindPushRelation(ctx context.Context, req *BindPushRelationReq) (*EmptyRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BindPushRelation not implemented")
}
func (*UnimplementedUserServer) UnBindPushRelation(ctx context.Context, req *UnBindPushRelationReq) (*EmptyRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnBindPushRelation not implemented")
}
func (*UnimplementedUserServer) PushByAlias(ctx context.Context, req *PushByAliasReq) (*EmptyRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushByAlias not implemented")
}
func (*UnimplementedUserServer) SetPushStatus(ctx context.Context, req *SetPushStatusReq) (*EmptyRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetPushStatus not implemented")
}
func (*UnimplementedUserServer) ReviewTextInfo(ctx context.Context, req *ReviewInfoReq) (*ReviewInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReviewTextInfo not implemented")
}
func (*UnimplementedUserServer) GetIPLocation(ctx context.Context, req *IPLocReq) (*IPLocRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIPLocation not implemented")
}
func (*UnimplementedUserServer) ApplyModifyMobile(ctx context.Context, req *ApplyModifyMobileReq) (*EmptyRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ApplyModifyMobile not implemented")
}
func (*UnimplementedUserServer) GetModifyMobileInfo(ctx context.Context, req *GetModifyMobileInfoReq) (*GetModifyMobileInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetModifyMobileInfo not implemented")
}
func (*UnimplementedUserServer) GetModifyMobileList(ctx context.Context, req *GetModifyMobileListReq) (*GetModifyMobileListRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetModifyMobileList not implemented")
}
func (*UnimplementedUserServer) GetAndCheckMobile(ctx context.Context, req *GetAndCheckMobileReq) (*GetAndCheckMobileRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAndCheckMobile not implemented")
}
func (*UnimplementedUserServer) TimeoutModifyMobile(ctx context.Context, req *TimeoutModifyMobileReq) (*TimeoutModifyMobileRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TimeoutModifyMobile not implemented")
}
func (*UnimplementedUserServer) GetUserInterest(ctx context.Context, req *GetUserInterestReq) (*GetUserInterestRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserInterest not implemented")
}
func (*UnimplementedUserServer) GetUserInterests(ctx context.Context, req *GetUserInterestsReq) (*GetUserInterestsRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserInterests not implemented")
}

func RegisterUserServer(s *grpc.Server, srv UserServer) {
	s.RegisterService(&_User_serviceDesc, srv)
}

func _User_TokenToID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).TokenToID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/TokenToID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).TokenToID(ctx, req.(*TokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_GetUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).GetUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/GetUserInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).GetUserInfo(ctx, req.(*UserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_GetUsersByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).GetUsersByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/GetUsersByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).GetUsersByIds(ctx, req.(*UserIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_GetUsersByMongoIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MongoIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).GetUsersByMongoIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/GetUsersByMongoIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).GetUsersByMongoIds(ctx, req.(*MongoIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_GetUsersByMsgIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MsgIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).GetUsersByMsgIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/GetUsersByMsgIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).GetUsersByMsgIds(ctx, req.(*MsgIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_UpdateUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).UpdateUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/UpdateUserInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).UpdateUserInfo(ctx, req.(*UpdateUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_IsUserExist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserExistReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).IsUserExist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/IsUserExist",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).IsUserExist(ctx, req.(*UserExistReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_PullUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PullUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).PullUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/PullUser",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).PullUser(ctx, req.(*PullUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_GetUserRemark_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserRemarkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).GetUserRemark(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/GetUserRemark",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).GetUserRemark(ctx, req.(*UserRemarkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_GetOnline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOnlineRsq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).GetOnline(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/GetOnline",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).GetOnline(ctx, req.(*GetOnlineRsq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_UpdateAlbumStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAlbumReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).UpdateAlbumStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/UpdateAlbumStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).UpdateAlbumStatus(ctx, req.(*UpdateAlbumReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_SetOnline_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetOnlineUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).SetOnline(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/SetOnline",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).SetOnline(ctx, req.(*SetOnlineUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_RobotAdd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RobotAddReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).RobotAdd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/RobotAdd",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).RobotAdd(ctx, req.(*RobotAddReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_OnlineNum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OnlineNumReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).OnlineNum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/OnlineNum",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).OnlineNum(ctx, req.(*OnlineNumReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_ValidHeader_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidHeadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).ValidHeader(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/ValidHeader",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).ValidHeader(ctx, req.(*ValidHeadReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_ValidByImei_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidHeadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).ValidByImei(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/ValidByImei",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).ValidByImei(ctx, req.(*ValidHeadReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_ValidByIP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidHeadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).ValidByIP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/ValidByIP",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).ValidByIP(ctx, req.(*ValidHeadReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_SetUserSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).SetUserSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/SetUserSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).SetUserSwitch(ctx, req.(*SetUserSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_GetUserSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).GetUserSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/GetUserSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).GetUserSwitch(ctx, req.(*GetUserSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_GetUserFansList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserFansListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).GetUserFansList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/GetUserFansList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).GetUserFansList(ctx, req.(*GetUserFansListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_BindPushRelation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BindPushRelationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).BindPushRelation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/BindPushRelation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).BindPushRelation(ctx, req.(*BindPushRelationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_UnBindPushRelation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnBindPushRelationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).UnBindPushRelation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/UnBindPushRelation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).UnBindPushRelation(ctx, req.(*UnBindPushRelationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_PushByAlias_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushByAliasReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).PushByAlias(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/PushByAlias",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).PushByAlias(ctx, req.(*PushByAliasReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_SetPushStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetPushStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).SetPushStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/SetPushStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).SetPushStatus(ctx, req.(*SetPushStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_ReviewTextInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReviewInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).ReviewTextInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/ReviewTextInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).ReviewTextInfo(ctx, req.(*ReviewInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_GetIPLocation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IPLocReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).GetIPLocation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/GetIPLocation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).GetIPLocation(ctx, req.(*IPLocReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_ApplyModifyMobile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplyModifyMobileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).ApplyModifyMobile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/ApplyModifyMobile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).ApplyModifyMobile(ctx, req.(*ApplyModifyMobileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_GetModifyMobileInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetModifyMobileInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).GetModifyMobileInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/GetModifyMobileInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).GetModifyMobileInfo(ctx, req.(*GetModifyMobileInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_GetModifyMobileList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetModifyMobileListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).GetModifyMobileList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/GetModifyMobileList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).GetModifyMobileList(ctx, req.(*GetModifyMobileListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_GetAndCheckMobile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAndCheckMobileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).GetAndCheckMobile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/GetAndCheckMobile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).GetAndCheckMobile(ctx, req.(*GetAndCheckMobileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_TimeoutModifyMobile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimeoutModifyMobileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).TimeoutModifyMobile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/TimeoutModifyMobile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).TimeoutModifyMobile(ctx, req.(*TimeoutModifyMobileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_GetUserInterest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserInterestReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).GetUserInterest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/GetUserInterest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).GetUserInterest(ctx, req.(*GetUserInterestReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_GetUserInterests_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserInterestsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).GetUserInterests(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/GetUserInterests",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).GetUserInterests(ctx, req.(*GetUserInterestsReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _User_serviceDesc = grpc.ServiceDesc{
	ServiceName: "user.base.User",
	HandlerType: (*UserServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "TokenToID",
			Handler:    _User_TokenToID_Handler,
		},
		{
			MethodName: "GetUserInfo",
			Handler:    _User_GetUserInfo_Handler,
		},
		{
			MethodName: "GetUsersByIds",
			Handler:    _User_GetUsersByIds_Handler,
		},
		{
			MethodName: "GetUsersByMongoIds",
			Handler:    _User_GetUsersByMongoIds_Handler,
		},
		{
			MethodName: "GetUsersByMsgIds",
			Handler:    _User_GetUsersByMsgIds_Handler,
		},
		{
			MethodName: "UpdateUserInfo",
			Handler:    _User_UpdateUserInfo_Handler,
		},
		{
			MethodName: "IsUserExist",
			Handler:    _User_IsUserExist_Handler,
		},
		{
			MethodName: "PullUser",
			Handler:    _User_PullUser_Handler,
		},
		{
			MethodName: "GetUserRemark",
			Handler:    _User_GetUserRemark_Handler,
		},
		{
			MethodName: "GetOnline",
			Handler:    _User_GetOnline_Handler,
		},
		{
			MethodName: "UpdateAlbumStatus",
			Handler:    _User_UpdateAlbumStatus_Handler,
		},
		{
			MethodName: "SetOnline",
			Handler:    _User_SetOnline_Handler,
		},
		{
			MethodName: "RobotAdd",
			Handler:    _User_RobotAdd_Handler,
		},
		{
			MethodName: "OnlineNum",
			Handler:    _User_OnlineNum_Handler,
		},
		{
			MethodName: "ValidHeader",
			Handler:    _User_ValidHeader_Handler,
		},
		{
			MethodName: "ValidByImei",
			Handler:    _User_ValidByImei_Handler,
		},
		{
			MethodName: "ValidByIP",
			Handler:    _User_ValidByIP_Handler,
		},
		{
			MethodName: "SetUserSwitch",
			Handler:    _User_SetUserSwitch_Handler,
		},
		{
			MethodName: "GetUserSwitch",
			Handler:    _User_GetUserSwitch_Handler,
		},
		{
			MethodName: "GetUserFansList",
			Handler:    _User_GetUserFansList_Handler,
		},
		{
			MethodName: "BindPushRelation",
			Handler:    _User_BindPushRelation_Handler,
		},
		{
			MethodName: "UnBindPushRelation",
			Handler:    _User_UnBindPushRelation_Handler,
		},
		{
			MethodName: "PushByAlias",
			Handler:    _User_PushByAlias_Handler,
		},
		{
			MethodName: "SetPushStatus",
			Handler:    _User_SetPushStatus_Handler,
		},
		{
			MethodName: "ReviewTextInfo",
			Handler:    _User_ReviewTextInfo_Handler,
		},
		{
			MethodName: "GetIPLocation",
			Handler:    _User_GetIPLocation_Handler,
		},
		{
			MethodName: "ApplyModifyMobile",
			Handler:    _User_ApplyModifyMobile_Handler,
		},
		{
			MethodName: "GetModifyMobileInfo",
			Handler:    _User_GetModifyMobileInfo_Handler,
		},
		{
			MethodName: "GetModifyMobileList",
			Handler:    _User_GetModifyMobileList_Handler,
		},
		{
			MethodName: "GetAndCheckMobile",
			Handler:    _User_GetAndCheckMobile_Handler,
		},
		{
			MethodName: "TimeoutModifyMobile",
			Handler:    _User_TimeoutModifyMobile_Handler,
		},
		{
			MethodName: "GetUserInterest",
			Handler:    _User_GetUserInterest_Handler,
		},
		{
			MethodName: "GetUserInterests",
			Handler:    _User_GetUserInterests_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "user.proto",
}
