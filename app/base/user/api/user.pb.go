// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.33.0
// 	protoc        v3.19.4
// source: user.proto

package api

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ModifyType int32

const (
	ModifyType_NoneType  ModifyType = 0
	ModifyType_UserSelf  ModifyType = 1 // 用户自己修改
	ModifyType_AdminHelp ModifyType = 2 // 用户申请后管理员协助修改
	ModifyType_AdminAuth ModifyType = 3 // 管理员特许修改
)

// Enum value maps for ModifyType.
var (
	ModifyType_name = map[int32]string{
		0: "NoneType",
		1: "UserSelf",
		2: "AdminHelp",
		3: "AdminAuth",
	}
	ModifyType_value = map[string]int32{
		"NoneType":  0,
		"UserSelf":  1,
		"AdminHelp": 2,
		"AdminAuth": 3,
	}
)

func (x ModifyType) Enum() *ModifyType {
	p := new(ModifyType)
	*p = x
	return p
}

func (x ModifyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModifyType) Descriptor() protoreflect.EnumDescriptor {
	return file_user_proto_enumTypes[0].Descriptor()
}

func (ModifyType) Type() protoreflect.EnumType {
	return &file_user_proto_enumTypes[0]
}

func (x ModifyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModifyType.Descriptor instead.
func (ModifyType) EnumDescriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{0}
}

type ModifyStatus int32

const (
	ModifyStatus_VoidStatus ModifyStatus = 0
	ModifyStatus_Pass       ModifyStatus = 1
	ModifyStatus_Apply      ModifyStatus = 2
	ModifyStatus_Reject     ModifyStatus = 3
	ModifyStatus_Timeout    ModifyStatus = 4
)

// Enum value maps for ModifyStatus.
var (
	ModifyStatus_name = map[int32]string{
		0: "VoidStatus",
		1: "Pass",
		2: "Apply",
		3: "Reject",
		4: "Timeout",
	}
	ModifyStatus_value = map[string]int32{
		"VoidStatus": 0,
		"Pass":       1,
		"Apply":      2,
		"Reject":     3,
		"Timeout":    4,
	}
)

func (x ModifyStatus) Enum() *ModifyStatus {
	p := new(ModifyStatus)
	*p = x
	return p
}

func (x ModifyStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ModifyStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_user_proto_enumTypes[1].Descriptor()
}

func (ModifyStatus) Type() protoreflect.EnumType {
	return &file_user_proto_enumTypes[1]
}

func (x ModifyStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ModifyStatus.Descriptor instead.
func (ModifyStatus) EnumDescriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{1}
}

type FilterParam int32

const (
	FilterParam_NoneFilter       FilterParam = 0
	FilterParam_FilterPass       FilterParam = 1
	FilterParam_FilterSelfModify FilterParam = 2
	FilterParam_FilterTimeout    FilterParam = 3
	FilterParam_FilterReject     FilterParam = 4
	FilterParam_FilterAdminAuth  FilterParam = 5
	FilterParam_FilterApply      FilterParam = 6
)

// Enum value maps for FilterParam.
var (
	FilterParam_name = map[int32]string{
		0: "NoneFilter",
		1: "FilterPass",
		2: "FilterSelfModify",
		3: "FilterTimeout",
		4: "FilterReject",
		5: "FilterAdminAuth",
		6: "FilterApply",
	}
	FilterParam_value = map[string]int32{
		"NoneFilter":       0,
		"FilterPass":       1,
		"FilterSelfModify": 2,
		"FilterTimeout":    3,
		"FilterReject":     4,
		"FilterAdminAuth":  5,
		"FilterApply":      6,
	}
)

func (x FilterParam) Enum() *FilterParam {
	p := new(FilterParam)
	*p = x
	return p
}

func (x FilterParam) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FilterParam) Descriptor() protoreflect.EnumDescriptor {
	return file_user_proto_enumTypes[2].Descriptor()
}

func (FilterParam) Type() protoreflect.EnumType {
	return &file_user_proto_enumTypes[2]
}

func (x FilterParam) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FilterParam.Descriptor instead.
func (FilterParam) EnumDescriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{2}
}

type CheckType int32

const (
	CheckType_NoneCheck       CheckType = 0
	CheckType_CheckConflict   CheckType = 1
	CheckType_CheckLimitTimes CheckType = 2
)

// Enum value maps for CheckType.
var (
	CheckType_name = map[int32]string{
		0: "NoneCheck",
		1: "CheckConflict",
		2: "CheckLimitTimes",
	}
	CheckType_value = map[string]int32{
		"NoneCheck":       0,
		"CheckConflict":   1,
		"CheckLimitTimes": 2,
	}
)

func (x CheckType) Enum() *CheckType {
	p := new(CheckType)
	*p = x
	return p
}

func (x CheckType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CheckType) Descriptor() protoreflect.EnumDescriptor {
	return file_user_proto_enumTypes[3].Descriptor()
}

func (CheckType) Type() protoreflect.EnumType {
	return &file_user_proto_enumTypes[3]
}

func (x CheckType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CheckType.Descriptor instead.
func (CheckType) EnumDescriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{3}
}

type PushType int32

const (
	PushType_Unknown       PushType = 0
	PushType_PrivateMsg    PushType = 1 // 私信聊天
	PushType_PrivateGift   PushType = 2 // 私信送礼
	PushType_HeartGift     PushType = 3 // 心动
	PushType_AccostGift    PushType = 4 // 搭讪
	PushType_FollowersOpen PushType = 5 // 关注者开播
)

// Enum value maps for PushType.
var (
	PushType_name = map[int32]string{
		0: "Unknown",
		1: "PrivateMsg",
		2: "PrivateGift",
		3: "HeartGift",
		4: "AccostGift",
		5: "FollowersOpen",
	}
	PushType_value = map[string]int32{
		"Unknown":       0,
		"PrivateMsg":    1,
		"PrivateGift":   2,
		"HeartGift":     3,
		"AccostGift":    4,
		"FollowersOpen": 5,
	}
)

func (x PushType) Enum() *PushType {
	p := new(PushType)
	*p = x
	return p
}

func (x PushType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PushType) Descriptor() protoreflect.EnumDescriptor {
	return file_user_proto_enumTypes[4].Descriptor()
}

func (PushType) Type() protoreflect.EnumType {
	return &file_user_proto_enumTypes[4]
}

func (x PushType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PushType.Descriptor instead.
func (PushType) EnumDescriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{4}
}

type TargetType int32

const (
	TargetType_None        TargetType = 0
	TargetType_OpenLive    TargetType = 1 // 直播
	TargetType_ChatMessage TargetType = 2 // 私聊
)

// Enum value maps for TargetType.
var (
	TargetType_name = map[int32]string{
		0: "None",
		1: "OpenLive",
		2: "ChatMessage",
	}
	TargetType_value = map[string]int32{
		"None":        0,
		"OpenLive":    1,
		"ChatMessage": 2,
	}
)

func (x TargetType) Enum() *TargetType {
	p := new(TargetType)
	*p = x
	return p
}

func (x TargetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TargetType) Descriptor() protoreflect.EnumDescriptor {
	return file_user_proto_enumTypes[5].Descriptor()
}

func (TargetType) Type() protoreflect.EnumType {
	return &file_user_proto_enumTypes[5]
}

func (x TargetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TargetType.Descriptor instead.
func (TargetType) EnumDescriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{5}
}

type TimeoutModifyMobileReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *TimeoutModifyMobileReq) Reset() {
	*x = TimeoutModifyMobileReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeoutModifyMobileReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeoutModifyMobileReq) ProtoMessage() {}

func (x *TimeoutModifyMobileReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeoutModifyMobileReq.ProtoReflect.Descriptor instead.
func (*TimeoutModifyMobileReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{0}
}

type TimeoutModifyMobileRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TMap map[string]string `protobuf:"bytes,1,rep,name=tMap,proto3" json:"tMap,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *TimeoutModifyMobileRsp) Reset() {
	*x = TimeoutModifyMobileRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeoutModifyMobileRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeoutModifyMobileRsp) ProtoMessage() {}

func (x *TimeoutModifyMobileRsp) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeoutModifyMobileRsp.ProtoReflect.Descriptor instead.
func (*TimeoutModifyMobileRsp) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{1}
}

func (x *TimeoutModifyMobileRsp) GetTMap() map[string]string {
	if x != nil {
		return x.TMap
	}
	return nil
}

type GetAndCheckMobileReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NoId     string    `protobuf:"bytes,1,opt,name=noId,proto3" json:"noId,omitempty"`
	ToMobile string    `protobuf:"bytes,2,opt,name=toMobile,proto3" json:"toMobile,omitempty"`
	Type     CheckType `protobuf:"varint,3,opt,name=type,proto3,enum=user.base.CheckType" json:"type,omitempty"`
}

func (x *GetAndCheckMobileReq) Reset() {
	*x = GetAndCheckMobileReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAndCheckMobileReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAndCheckMobileReq) ProtoMessage() {}

func (x *GetAndCheckMobileReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAndCheckMobileReq.ProtoReflect.Descriptor instead.
func (*GetAndCheckMobileReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{2}
}

func (x *GetAndCheckMobileReq) GetNoId() string {
	if x != nil {
		return x.NoId
	}
	return ""
}

func (x *GetAndCheckMobileReq) GetToMobile() string {
	if x != nil {
		return x.ToMobile
	}
	return ""
}

func (x *GetAndCheckMobileReq) GetType() CheckType {
	if x != nil {
		return x.Type
	}
	return CheckType_NoneCheck
}

type GetAndCheckMobileRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IsConflict  bool            `protobuf:"varint,1,opt,name=isConflict,proto3" json:"isConflict,omitempty"`
	From        *MobileUserInfo `protobuf:"bytes,2,opt,name=from,proto3" json:"from,omitempty"`
	To          *MobileUserInfo `protobuf:"bytes,3,opt,name=to,proto3" json:"to,omitempty"`
	IsOverTimes bool            `protobuf:"varint,4,opt,name=isOverTimes,proto3" json:"isOverTimes,omitempty"`
}

func (x *GetAndCheckMobileRsp) Reset() {
	*x = GetAndCheckMobileRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAndCheckMobileRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAndCheckMobileRsp) ProtoMessage() {}

func (x *GetAndCheckMobileRsp) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAndCheckMobileRsp.ProtoReflect.Descriptor instead.
func (*GetAndCheckMobileRsp) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{3}
}

func (x *GetAndCheckMobileRsp) GetIsConflict() bool {
	if x != nil {
		return x.IsConflict
	}
	return false
}

func (x *GetAndCheckMobileRsp) GetFrom() *MobileUserInfo {
	if x != nil {
		return x.From
	}
	return nil
}

func (x *GetAndCheckMobileRsp) GetTo() *MobileUserInfo {
	if x != nil {
		return x.To
	}
	return nil
}

func (x *GetAndCheckMobileRsp) GetIsOverTimes() bool {
	if x != nil {
		return x.IsOverTimes
	}
	return false
}

type MobileUserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NoId        string `protobuf:"bytes,1,opt,name=noId,proto3" json:"noId,omitempty"`
	Nickname    string `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Avatar      string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`
	WealthLevel int64  `protobuf:"varint,4,opt,name=wealthLevel,proto3" json:"wealthLevel,omitempty"`
	CharmLevel  int64  `protobuf:"varint,5,opt,name=charmLevel,proto3" json:"charmLevel,omitempty"`
	Mobile      string `protobuf:"bytes,6,opt,name=mobile,proto3" json:"mobile,omitempty"`
}

func (x *MobileUserInfo) Reset() {
	*x = MobileUserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MobileUserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MobileUserInfo) ProtoMessage() {}

func (x *MobileUserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MobileUserInfo.ProtoReflect.Descriptor instead.
func (*MobileUserInfo) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{4}
}

func (x *MobileUserInfo) GetNoId() string {
	if x != nil {
		return x.NoId
	}
	return ""
}

func (x *MobileUserInfo) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *MobileUserInfo) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *MobileUserInfo) GetWealthLevel() int64 {
	if x != nil {
		return x.WealthLevel
	}
	return 0
}

func (x *MobileUserInfo) GetCharmLevel() int64 {
	if x != nil {
		return x.CharmLevel
	}
	return 0
}

func (x *MobileUserInfo) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

type GetModifyMobileListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NoId        string      `protobuf:"bytes,1,opt,name=noId,proto3" json:"noId,omitempty"`
	StartTime   string      `protobuf:"bytes,2,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime     string      `protobuf:"bytes,3,opt,name=endTime,proto3" json:"endTime,omitempty"`
	FilterParam FilterParam `protobuf:"varint,4,opt,name=filterParam,proto3,enum=user.base.FilterParam" json:"filterParam,omitempty"`
	Page        int64       `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	PageSize    int64       `protobuf:"varint,6,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
}

func (x *GetModifyMobileListReq) Reset() {
	*x = GetModifyMobileListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetModifyMobileListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetModifyMobileListReq) ProtoMessage() {}

func (x *GetModifyMobileListReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetModifyMobileListReq.ProtoReflect.Descriptor instead.
func (*GetModifyMobileListReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{5}
}

func (x *GetModifyMobileListReq) GetNoId() string {
	if x != nil {
		return x.NoId
	}
	return ""
}

func (x *GetModifyMobileListReq) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *GetModifyMobileListReq) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *GetModifyMobileListReq) GetFilterParam() FilterParam {
	if x != nil {
		return x.FilterParam
	}
	return FilterParam_NoneFilter
}

func (x *GetModifyMobileListReq) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetModifyMobileListReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type GetModifyMobileListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*ModifyMobileData `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Count int64               `protobuf:"varint,2,opt,name=Count,proto3" json:"Count,omitempty"`
}

func (x *GetModifyMobileListRsp) Reset() {
	*x = GetModifyMobileListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetModifyMobileListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetModifyMobileListRsp) ProtoMessage() {}

func (x *GetModifyMobileListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetModifyMobileListRsp.ProtoReflect.Descriptor instead.
func (*GetModifyMobileListRsp) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{6}
}

func (x *GetModifyMobileListRsp) GetList() []*ModifyMobileData {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *GetModifyMobileListRsp) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type ModifyMobileData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           string   `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	NoId         string   `protobuf:"bytes,2,opt,name=noId,proto3" json:"noId,omitempty"`
	UpdateTime   string   `protobuf:"bytes,3,opt,name=updateTime,proto3" json:"updateTime,omitempty"`
	FromMobile   string   `protobuf:"bytes,4,opt,name=fromMobile,proto3" json:"fromMobile,omitempty"`
	ToMobile     string   `protobuf:"bytes,5,opt,name=toMobile,proto3" json:"toMobile,omitempty"`
	ApplyReason  string   `protobuf:"bytes,6,opt,name=applyReason,proto3" json:"applyReason,omitempty"`
	AdminReason  string   `protobuf:"bytes,7,opt,name=adminReason,proto3" json:"adminReason,omitempty"`
	OperatorName string   `protobuf:"bytes,8,opt,name=operatorName,proto3" json:"operatorName,omitempty"`
	ApplyTime    string   `protobuf:"bytes,9,opt,name=applyTime,proto3" json:"applyTime,omitempty"`
	ProveImg     []string `protobuf:"bytes,10,rep,name=proveImg,proto3" json:"proveImg,omitempty"`
	Status       string   `protobuf:"bytes,11,opt,name=status,proto3" json:"status,omitempty"` // 处理结果
}

func (x *ModifyMobileData) Reset() {
	*x = ModifyMobileData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyMobileData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyMobileData) ProtoMessage() {}

func (x *ModifyMobileData) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyMobileData.ProtoReflect.Descriptor instead.
func (*ModifyMobileData) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{7}
}

func (x *ModifyMobileData) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ModifyMobileData) GetNoId() string {
	if x != nil {
		return x.NoId
	}
	return ""
}

func (x *ModifyMobileData) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *ModifyMobileData) GetFromMobile() string {
	if x != nil {
		return x.FromMobile
	}
	return ""
}

func (x *ModifyMobileData) GetToMobile() string {
	if x != nil {
		return x.ToMobile
	}
	return ""
}

func (x *ModifyMobileData) GetApplyReason() string {
	if x != nil {
		return x.ApplyReason
	}
	return ""
}

func (x *ModifyMobileData) GetAdminReason() string {
	if x != nil {
		return x.AdminReason
	}
	return ""
}

func (x *ModifyMobileData) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *ModifyMobileData) GetApplyTime() string {
	if x != nil {
		return x.ApplyTime
	}
	return ""
}

func (x *ModifyMobileData) GetProveImg() []string {
	if x != nil {
		return x.ProveImg
	}
	return nil
}

func (x *ModifyMobileData) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type GetModifyMobileInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NoId string `protobuf:"bytes,1,opt,name=noId,proto3" json:"noId,omitempty"`
}

func (x *GetModifyMobileInfoReq) Reset() {
	*x = GetModifyMobileInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetModifyMobileInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetModifyMobileInfoReq) ProtoMessage() {}

func (x *GetModifyMobileInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetModifyMobileInfoReq.ProtoReflect.Descriptor instead.
func (*GetModifyMobileInfoReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{8}
}

func (x *GetModifyMobileInfoReq) GetNoId() string {
	if x != nil {
		return x.NoId
	}
	return ""
}

type GetModifyMobileInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NoId        string   `protobuf:"bytes,1,opt,name=noId,proto3" json:"noId,omitempty"`
	FromMobile  string   `protobuf:"bytes,2,opt,name=fromMobile,proto3" json:"fromMobile,omitempty"`   // 原手机号
	ToMobile    string   `protobuf:"bytes,3,opt,name=toMobile,proto3" json:"toMobile,omitempty"`       // 新手机号
	ApplyReason string   `protobuf:"bytes,4,opt,name=applyReason,proto3" json:"applyReason,omitempty"` // 用户申请理由
	ProveImg    []string `protobuf:"bytes,5,rep,name=proveImg,proto3" json:"proveImg,omitempty"`       // 证明材料
	ModifyType  string   `protobuf:"bytes,6,opt,name=modifyType,proto3" json:"modifyType,omitempty"`   // 修改类型
	Status      string   `protobuf:"bytes,7,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *GetModifyMobileInfoRsp) Reset() {
	*x = GetModifyMobileInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetModifyMobileInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetModifyMobileInfoRsp) ProtoMessage() {}

func (x *GetModifyMobileInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetModifyMobileInfoRsp.ProtoReflect.Descriptor instead.
func (*GetModifyMobileInfoRsp) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{9}
}

func (x *GetModifyMobileInfoRsp) GetNoId() string {
	if x != nil {
		return x.NoId
	}
	return ""
}

func (x *GetModifyMobileInfoRsp) GetFromMobile() string {
	if x != nil {
		return x.FromMobile
	}
	return ""
}

func (x *GetModifyMobileInfoRsp) GetToMobile() string {
	if x != nil {
		return x.ToMobile
	}
	return ""
}

func (x *GetModifyMobileInfoRsp) GetApplyReason() string {
	if x != nil {
		return x.ApplyReason
	}
	return ""
}

func (x *GetModifyMobileInfoRsp) GetProveImg() []string {
	if x != nil {
		return x.ProveImg
	}
	return nil
}

func (x *GetModifyMobileInfoRsp) GetModifyType() string {
	if x != nil {
		return x.ModifyType
	}
	return ""
}

func (x *GetModifyMobileInfoRsp) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type ApplyModifyMobileReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NoId         string       `protobuf:"bytes,1,opt,name=noId,proto3" json:"noId,omitempty"`
	FromMobile   string       `protobuf:"bytes,2,opt,name=fromMobile,proto3" json:"fromMobile,omitempty"`                                   // 原手机号
	ToMobile     string       `protobuf:"bytes,3,opt,name=toMobile,proto3" json:"toMobile,omitempty"`                                       // 新手机号
	OperatorId   string       `protobuf:"bytes,4,opt,name=operatorId,proto3" json:"operatorId,omitempty"`                                   // 操作人ID(后台登录账号ID)
	OperatorName string       `protobuf:"bytes,5,opt,name=operatorName,proto3" json:"operatorName,omitempty"`                               // 操作人名称(后台实际操作人名称)
	ModifyType   ModifyType   `protobuf:"varint,6,opt,name=modifyType,proto3,enum=user.base.ModifyType" json:"modifyType,omitempty"`        // 修改类型
	ApplyReason  string       `protobuf:"bytes,7,opt,name=applyReason,proto3" json:"applyReason,omitempty"`                                 // 用户申请理由
	AdminReason  string       `protobuf:"bytes,8,opt,name=adminReason,proto3" json:"adminReason,omitempty"`                                 // 操作人拒绝/其他理由
	ProveImg     []string     `protobuf:"bytes,9,rep,name=proveImg,proto3" json:"proveImg,omitempty"`                                       // 证明材料
	ModifyStatus ModifyStatus `protobuf:"varint,10,opt,name=modifyStatus,proto3,enum=user.base.ModifyStatus" json:"modifyStatus,omitempty"` // 操作状态
	Id           string       `protobuf:"bytes,11,opt,name=id,proto3" json:"id,omitempty"`                                                  // 记录ID
}

func (x *ApplyModifyMobileReq) Reset() {
	*x = ApplyModifyMobileReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplyModifyMobileReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyModifyMobileReq) ProtoMessage() {}

func (x *ApplyModifyMobileReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyModifyMobileReq.ProtoReflect.Descriptor instead.
func (*ApplyModifyMobileReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{10}
}

func (x *ApplyModifyMobileReq) GetNoId() string {
	if x != nil {
		return x.NoId
	}
	return ""
}

func (x *ApplyModifyMobileReq) GetFromMobile() string {
	if x != nil {
		return x.FromMobile
	}
	return ""
}

func (x *ApplyModifyMobileReq) GetToMobile() string {
	if x != nil {
		return x.ToMobile
	}
	return ""
}

func (x *ApplyModifyMobileReq) GetOperatorId() string {
	if x != nil {
		return x.OperatorId
	}
	return ""
}

func (x *ApplyModifyMobileReq) GetOperatorName() string {
	if x != nil {
		return x.OperatorName
	}
	return ""
}

func (x *ApplyModifyMobileReq) GetModifyType() ModifyType {
	if x != nil {
		return x.ModifyType
	}
	return ModifyType_NoneType
}

func (x *ApplyModifyMobileReq) GetApplyReason() string {
	if x != nil {
		return x.ApplyReason
	}
	return ""
}

func (x *ApplyModifyMobileReq) GetAdminReason() string {
	if x != nil {
		return x.AdminReason
	}
	return ""
}

func (x *ApplyModifyMobileReq) GetProveImg() []string {
	if x != nil {
		return x.ProveImg
	}
	return nil
}

func (x *ApplyModifyMobileReq) GetModifyStatus() ModifyStatus {
	if x != nil {
		return x.ModifyStatus
	}
	return ModifyStatus_VoidStatus
}

func (x *ApplyModifyMobileReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type SetPushStatusReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NoId   string `protobuf:"bytes,1,opt,name=noId,proto3" json:"noId,omitempty"`
	Status string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *SetPushStatusReq) Reset() {
	*x = SetPushStatusReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetPushStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetPushStatusReq) ProtoMessage() {}

func (x *SetPushStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetPushStatusReq.ProtoReflect.Descriptor instead.
func (*SetPushStatusReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{11}
}

func (x *SetPushStatusReq) GetNoId() string {
	if x != nil {
		return x.NoId
	}
	return ""
}

func (x *SetPushStatusReq) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type UnBindPushRelationReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NoId string `protobuf:"bytes,1,opt,name=noId,proto3" json:"noId,omitempty"`
}

func (x *UnBindPushRelationReq) Reset() {
	*x = UnBindPushRelationReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UnBindPushRelationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnBindPushRelationReq) ProtoMessage() {}

func (x *UnBindPushRelationReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnBindPushRelationReq.ProtoReflect.Descriptor instead.
func (*UnBindPushRelationReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{12}
}

func (x *UnBindPushRelationReq) GetNoId() string {
	if x != nil {
		return x.NoId
	}
	return ""
}

type BindPushRelationReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 用户ID
	NoId string `protobuf:"bytes,1,opt,name=noId,proto3" json:"noId,omitempty"`
	// 个推clientId
	Cid string `protobuf:"bytes,2,opt,name=cid,proto3" json:"cid,omitempty"`
	// 别名 本系统以用户的msgId作为别名
	Alias string `protobuf:"bytes,3,opt,name=alias,proto3" json:"alias,omitempty"`
	// 平台 ios/android
	Platform string `protobuf:"bytes,4,opt,name=platform,proto3" json:"platform,omitempty"`
}

func (x *BindPushRelationReq) Reset() {
	*x = BindPushRelationReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BindPushRelationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BindPushRelationReq) ProtoMessage() {}

func (x *BindPushRelationReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BindPushRelationReq.ProtoReflect.Descriptor instead.
func (*BindPushRelationReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{13}
}

func (x *BindPushRelationReq) GetNoId() string {
	if x != nil {
		return x.NoId
	}
	return ""
}

func (x *BindPushRelationReq) GetCid() string {
	if x != nil {
		return x.Cid
	}
	return ""
}

func (x *BindPushRelationReq) GetAlias() string {
	if x != nil {
		return x.Alias
	}
	return ""
}

func (x *BindPushRelationReq) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

type PushByAliasReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 发起推送者
	FromId string `protobuf:"bytes,1,opt,name=fromId,proto3" json:"fromId,omitempty"`
	// 推送目标的别名
	ToIds []string `protobuf:"bytes,2,rep,name=toIds,proto3" json:"toIds,omitempty"`
	// 跳转
	TargetType TargetType  `protobuf:"varint,3,opt,name=targetType,proto3,enum=user.base.TargetType" json:"targetType,omitempty"`
	PushType   PushType    `protobuf:"varint,4,opt,name=pushType,proto3,enum=user.base.PushType" json:"pushType,omitempty"`
	Attach     *PushAttach `protobuf:"bytes,5,opt,name=attach,proto3" json:"attach,omitempty"`
}

func (x *PushByAliasReq) Reset() {
	*x = PushByAliasReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushByAliasReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushByAliasReq) ProtoMessage() {}

func (x *PushByAliasReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushByAliasReq.ProtoReflect.Descriptor instead.
func (*PushByAliasReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{14}
}

func (x *PushByAliasReq) GetFromId() string {
	if x != nil {
		return x.FromId
	}
	return ""
}

func (x *PushByAliasReq) GetToIds() []string {
	if x != nil {
		return x.ToIds
	}
	return nil
}

func (x *PushByAliasReq) GetTargetType() TargetType {
	if x != nil {
		return x.TargetType
	}
	return TargetType_None
}

func (x *PushByAliasReq) GetPushType() PushType {
	if x != nil {
		return x.PushType
	}
	return PushType_Unknown
}

func (x *PushByAliasReq) GetAttach() *PushAttach {
	if x != nil {
		return x.Attach
	}
	return nil
}

type PushAttach struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LiveType string `protobuf:"bytes,1,opt,name=liveType,proto3" json:"liveType,omitempty"` // 直播分类 Live，Voice
}

func (x *PushAttach) Reset() {
	*x = PushAttach{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PushAttach) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushAttach) ProtoMessage() {}

func (x *PushAttach) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushAttach.ProtoReflect.Descriptor instead.
func (*PushAttach) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{15}
}

func (x *PushAttach) GetLiveType() string {
	if x != nil {
		return x.LiveType
	}
	return ""
}

type EmptyRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *EmptyRsp) Reset() {
	*x = EmptyRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmptyRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyRsp) ProtoMessage() {}

func (x *EmptyRsp) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyRsp.ProtoReflect.Descriptor instead.
func (*EmptyRsp) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{16}
}

type GetUserSwitchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NoIds []string `protobuf:"bytes,1,rep,name=noIds,proto3" json:"noIds,omitempty"`
}

func (x *GetUserSwitchReq) Reset() {
	*x = GetUserSwitchReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserSwitchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserSwitchReq) ProtoMessage() {}

func (x *GetUserSwitchReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserSwitchReq.ProtoReflect.Descriptor instead.
func (*GetUserSwitchReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{17}
}

func (x *GetUserSwitchReq) GetNoIds() []string {
	if x != nil {
		return x.NoIds
	}
	return nil
}

type GetUserSwitchRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*SwitchInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *GetUserSwitchRsp) Reset() {
	*x = GetUserSwitchRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserSwitchRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserSwitchRsp) ProtoMessage() {}

func (x *GetUserSwitchRsp) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserSwitchRsp.ProtoReflect.Descriptor instead.
func (*GetUserSwitchRsp) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{18}
}

func (x *GetUserSwitchRsp) GetList() []*SwitchInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type SwitchInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NoId string `protobuf:"bytes,1,opt,name=noId,proto3" json:"noId,omitempty"`
	// 位置开关 on/off
	Position string `protobuf:"bytes,2,opt,name=position,proto3" json:"position,omitempty"`
	// 私聊推送开关 on/off
	PrivateChatPush string `protobuf:"bytes,3,opt,name=privateChatPush,proto3" json:"privateChatPush,omitempty"`
	// 开播推送开关 on/off
	OpenLivePush string `protobuf:"bytes,4,opt,name=openLivePush,proto3" json:"openLivePush,omitempty"`
	// 自动拒绝连麦
	AutoRefuseMultiChat string `protobuf:"bytes,5,opt,name=autoRefuseMultiChat,proto3" json:"autoRefuseMultiChat,omitempty"`
	// 设置拒绝时间
	AutoRefuseTime string `protobuf:"bytes,6,opt,name=autoRefuseTime,proto3" json:"autoRefuseTime,omitempty"`
	// 管理隐身开关 on/off
	AnonymousBrowse string `protobuf:"bytes,7,opt,name=anonymousBrowse,proto3" json:"anonymousBrowse,omitempty"`
}

func (x *SwitchInfo) Reset() {
	*x = SwitchInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SwitchInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SwitchInfo) ProtoMessage() {}

func (x *SwitchInfo) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SwitchInfo.ProtoReflect.Descriptor instead.
func (*SwitchInfo) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{19}
}

func (x *SwitchInfo) GetNoId() string {
	if x != nil {
		return x.NoId
	}
	return ""
}

func (x *SwitchInfo) GetPosition() string {
	if x != nil {
		return x.Position
	}
	return ""
}

func (x *SwitchInfo) GetPrivateChatPush() string {
	if x != nil {
		return x.PrivateChatPush
	}
	return ""
}

func (x *SwitchInfo) GetOpenLivePush() string {
	if x != nil {
		return x.OpenLivePush
	}
	return ""
}

func (x *SwitchInfo) GetAutoRefuseMultiChat() string {
	if x != nil {
		return x.AutoRefuseMultiChat
	}
	return ""
}

func (x *SwitchInfo) GetAutoRefuseTime() string {
	if x != nil {
		return x.AutoRefuseTime
	}
	return ""
}

func (x *SwitchInfo) GetAnonymousBrowse() string {
	if x != nil {
		return x.AnonymousBrowse
	}
	return ""
}

type SetUserSwitchReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NoId string `protobuf:"bytes,1,opt,name=noId,proto3" json:"noId,omitempty"`
	// 位置开关 on/off
	Position string `protobuf:"bytes,2,opt,name=position,proto3" json:"position,omitempty"`
	// 私聊推送开关 on/off
	PrivateChatPush string `protobuf:"bytes,3,opt,name=privateChatPush,proto3" json:"privateChatPush,omitempty"`
	// 开播推送开关 on/off
	OpenLivePush string `protobuf:"bytes,4,opt,name=openLivePush,proto3" json:"openLivePush,omitempty"`
	// 自动拒绝连麦
	AutoRefuseMultiChat string `protobuf:"bytes,5,opt,name=autoRefuseMultiChat,proto3" json:"autoRefuseMultiChat,omitempty"`
	// 管理隐身开关 on/off
	AnonymousBrowse string `protobuf:"bytes,6,opt,name=anonymousBrowse,proto3" json:"anonymousBrowse,omitempty"`
}

func (x *SetUserSwitchReq) Reset() {
	*x = SetUserSwitchReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetUserSwitchReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetUserSwitchReq) ProtoMessage() {}

func (x *SetUserSwitchReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetUserSwitchReq.ProtoReflect.Descriptor instead.
func (*SetUserSwitchReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{20}
}

func (x *SetUserSwitchReq) GetNoId() string {
	if x != nil {
		return x.NoId
	}
	return ""
}

func (x *SetUserSwitchReq) GetPosition() string {
	if x != nil {
		return x.Position
	}
	return ""
}

func (x *SetUserSwitchReq) GetPrivateChatPush() string {
	if x != nil {
		return x.PrivateChatPush
	}
	return ""
}

func (x *SetUserSwitchReq) GetOpenLivePush() string {
	if x != nil {
		return x.OpenLivePush
	}
	return ""
}

func (x *SetUserSwitchReq) GetAutoRefuseMultiChat() string {
	if x != nil {
		return x.AutoRefuseMultiChat
	}
	return ""
}

func (x *SetUserSwitchReq) GetAnonymousBrowse() string {
	if x != nil {
		return x.AnonymousBrowse
	}
	return ""
}

type SetUserSwitchRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *SetUserSwitchRsp) Reset() {
	*x = SetUserSwitchRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetUserSwitchRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetUserSwitchRsp) ProtoMessage() {}

func (x *SetUserSwitchRsp) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetUserSwitchRsp.ProtoReflect.Descriptor instead.
func (*SetUserSwitchRsp) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{21}
}

type GetUserFansListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NoId     string `protobuf:"bytes,1,opt,name=noId,proto3" json:"noId,omitempty"`
	PageNum  int64  `protobuf:"varint,2,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize int64  `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
}

func (x *GetUserFansListReq) Reset() {
	*x = GetUserFansListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserFansListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserFansListReq) ProtoMessage() {}

func (x *GetUserFansListReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserFansListReq.ProtoReflect.Descriptor instead.
func (*GetUserFansListReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{22}
}

func (x *GetUserFansListReq) GetNoId() string {
	if x != nil {
		return x.NoId
	}
	return ""
}

func (x *GetUserFansListReq) GetPageNum() int64 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *GetUserFansListReq) GetPageSize() int64 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type GetUserFansListRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*SessionInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *GetUserFansListRsp) Reset() {
	*x = GetUserFansListRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserFansListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserFansListRsp) ProtoMessage() {}

func (x *GetUserFansListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserFansListRsp.ProtoReflect.Descriptor instead.
func (*GetUserFansListRsp) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{23}
}

func (x *GetUserFansListRsp) GetList() []*SessionInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type SessionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FromId   string `protobuf:"bytes,1,opt,name=fromId,proto3" json:"fromId,omitempty"`
	ToId     string `protobuf:"bytes,2,opt,name=toId,proto3" json:"toId,omitempty"`
	Relation string `protobuf:"bytes,3,opt,name=relation,proto3" json:"relation,omitempty"`
}

func (x *SessionInfo) Reset() {
	*x = SessionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SessionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SessionInfo) ProtoMessage() {}

func (x *SessionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SessionInfo.ProtoReflect.Descriptor instead.
func (*SessionInfo) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{24}
}

func (x *SessionInfo) GetFromId() string {
	if x != nil {
		return x.FromId
	}
	return ""
}

func (x *SessionInfo) GetToId() string {
	if x != nil {
		return x.ToId
	}
	return ""
}

func (x *SessionInfo) GetRelation() string {
	if x != nil {
		return x.Relation
	}
	return ""
}

type OnlineNumReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *OnlineNumReq) Reset() {
	*x = OnlineNumReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OnlineNumReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnlineNumReq) ProtoMessage() {}

func (x *OnlineNumReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnlineNumReq.ProtoReflect.Descriptor instead.
func (*OnlineNumReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{25}
}

type OnlineNumRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Count int64 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
}

func (x *OnlineNumRsp) Reset() {
	*x = OnlineNumRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OnlineNumRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OnlineNumRsp) ProtoMessage() {}

func (x *OnlineNumRsp) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OnlineNumRsp.ProtoReflect.Descriptor instead.
func (*OnlineNumRsp) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{26}
}

func (x *OnlineNumRsp) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

type RobotAddReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Robot       *Robot       `protobuf:"bytes,1,opt,name=robot,proto3" json:"robot,omitempty"`
	Mode        int32        `protobuf:"varint,2,opt,name=mode,proto3" json:"mode,omitempty"`              //默认0 添加一个 不为0 批量添加
	RobotConfig *RobotConfig `protobuf:"bytes,3,opt,name=robotConfig,proto3" json:"robotConfig,omitempty"` //机器人配置
}

func (x *RobotAddReq) Reset() {
	*x = RobotAddReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RobotAddReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RobotAddReq) ProtoMessage() {}

func (x *RobotAddReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RobotAddReq.ProtoReflect.Descriptor instead.
func (*RobotAddReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{27}
}

func (x *RobotAddReq) GetRobot() *Robot {
	if x != nil {
		return x.Robot
	}
	return nil
}

func (x *RobotAddReq) GetMode() int32 {
	if x != nil {
		return x.Mode
	}
	return 0
}

func (x *RobotAddReq) GetRobotConfig() *RobotConfig {
	if x != nil {
		return x.RobotConfig
	}
	return nil
}

type RobotConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Num       int32 `protobuf:"varint,1,opt,name=num,proto3" json:"num,omitempty"`             //添加数目
	WealthMin int32 `protobuf:"varint,2,opt,name=wealthMin,proto3" json:"wealthMin,omitempty"` //最小财富等级
	WealthMax int32 `protobuf:"varint,3,opt,name=wealthMax,proto3" json:"wealthMax,omitempty"` //最大财富等级
	Male      int32 `protobuf:"varint,4,opt,name=male,proto3" json:"male,omitempty"`           //男占比
	Female    int32 `protobuf:"varint,5,opt,name=female,proto3" json:"female,omitempty"`       //女占比
}

func (x *RobotConfig) Reset() {
	*x = RobotConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RobotConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RobotConfig) ProtoMessage() {}

func (x *RobotConfig) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RobotConfig.ProtoReflect.Descriptor instead.
func (*RobotConfig) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{28}
}

func (x *RobotConfig) GetNum() int32 {
	if x != nil {
		return x.Num
	}
	return 0
}

func (x *RobotConfig) GetWealthMin() int32 {
	if x != nil {
		return x.WealthMin
	}
	return 0
}

func (x *RobotConfig) GetWealthMax() int32 {
	if x != nil {
		return x.WealthMax
	}
	return 0
}

func (x *RobotConfig) GetMale() int32 {
	if x != nil {
		return x.Male
	}
	return 0
}

func (x *RobotConfig) GetFemale() int32 {
	if x != nil {
		return x.Female
	}
	return 0
}

type Robot struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NickName    string `protobuf:"bytes,1,opt,name=nickName,proto3" json:"nickName,omitempty"`
	Gender      string `protobuf:"bytes,2,opt,name=gender,proto3" json:"gender,omitempty"`
	Born        string `protobuf:"bytes,3,opt,name=born,proto3" json:"born,omitempty"`
	City        string `protobuf:"bytes,4,opt,name=city,proto3" json:"city,omitempty"`
	WealthLevel int32  `protobuf:"varint,5,opt,name=wealthLevel,proto3" json:"wealthLevel,omitempty"`
	StarSign    string `protobuf:"bytes,6,opt,name=starSign,proto3" json:"starSign,omitempty"`
	Height      int32  `protobuf:"varint,7,opt,name=height,proto3" json:"height,omitempty"`
	Weight      int32  `protobuf:"varint,8,opt,name=weight,proto3" json:"weight,omitempty"`
	AvatarUrl   string `protobuf:"bytes,9,opt,name=avatarUrl,proto3" json:"avatarUrl,omitempty"`
}

func (x *Robot) Reset() {
	*x = Robot{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Robot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Robot) ProtoMessage() {}

func (x *Robot) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Robot.ProtoReflect.Descriptor instead.
func (*Robot) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{29}
}

func (x *Robot) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *Robot) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *Robot) GetBorn() string {
	if x != nil {
		return x.Born
	}
	return ""
}

func (x *Robot) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *Robot) GetWealthLevel() int32 {
	if x != nil {
		return x.WealthLevel
	}
	return 0
}

func (x *Robot) GetStarSign() string {
	if x != nil {
		return x.StarSign
	}
	return ""
}

func (x *Robot) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *Robot) GetWeight() int32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *Robot) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

type RobotAddRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     bool         `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	Success    int32        `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"` //成功数目
	Failure    int32        `protobuf:"varint,3,opt,name=failure,proto3" json:"failure,omitempty"` //失败数目
	RobotInfos []*RobotInfo `protobuf:"bytes,4,rep,name=robotInfos,proto3" json:"robotInfos,omitempty"`
}

func (x *RobotAddRsp) Reset() {
	*x = RobotAddRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RobotAddRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RobotAddRsp) ProtoMessage() {}

func (x *RobotAddRsp) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RobotAddRsp.ProtoReflect.Descriptor instead.
func (*RobotAddRsp) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{30}
}

func (x *RobotAddRsp) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *RobotAddRsp) GetSuccess() int32 {
	if x != nil {
		return x.Success
	}
	return 0
}

func (x *RobotAddRsp) GetFailure() int32 {
	if x != nil {
		return x.Failure
	}
	return 0
}

func (x *RobotAddRsp) GetRobotInfos() []*RobotInfo {
	if x != nil {
		return x.RobotInfos
	}
	return nil
}

type RobotInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MsgId string `protobuf:"bytes,1,opt,name=msgId,proto3" json:"msgId,omitempty"`
	NoId  string `protobuf:"bytes,2,opt,name=noId,proto3" json:"noId,omitempty"`
}

func (x *RobotInfo) Reset() {
	*x = RobotInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RobotInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RobotInfo) ProtoMessage() {}

func (x *RobotInfo) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RobotInfo.ProtoReflect.Descriptor instead.
func (*RobotInfo) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{31}
}

func (x *RobotInfo) GetMsgId() string {
	if x != nil {
		return x.MsgId
	}
	return ""
}

func (x *RobotInfo) GetNoId() string {
	if x != nil {
		return x.NoId
	}
	return ""
}

type SetOnlineUserInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Gender string `protobuf:"bytes,1,opt,name=gender,proto3" json:"gender,omitempty"`
	NoId   string `protobuf:"bytes,2,opt,name=noId,proto3" json:"noId,omitempty"`
	User   string `protobuf:"bytes,3,opt,name=user,proto3" json:"user,omitempty"`
}

func (x *SetOnlineUserInfoReq) Reset() {
	*x = SetOnlineUserInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetOnlineUserInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetOnlineUserInfoReq) ProtoMessage() {}

func (x *SetOnlineUserInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetOnlineUserInfoReq.ProtoReflect.Descriptor instead.
func (*SetOnlineUserInfoReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{32}
}

func (x *SetOnlineUserInfoReq) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *SetOnlineUserInfoReq) GetNoId() string {
	if x != nil {
		return x.NoId
	}
	return ""
}

func (x *SetOnlineUserInfoReq) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

type SetOnlineUserInfoReqRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status bool `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *SetOnlineUserInfoReqRsp) Reset() {
	*x = SetOnlineUserInfoReqRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetOnlineUserInfoReqRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetOnlineUserInfoReqRsp) ProtoMessage() {}

func (x *SetOnlineUserInfoReqRsp) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetOnlineUserInfoReqRsp.ProtoReflect.Descriptor instead.
func (*SetOnlineUserInfoReqRsp) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{33}
}

func (x *SetOnlineUserInfoReqRsp) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

// token获取 _id
type TokenReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *TokenReq) Reset() {
	*x = TokenReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenReq) ProtoMessage() {}

func (x *TokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenReq.ProtoReflect.Descriptor instead.
func (*TokenReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{34}
}

func (x *TokenReq) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type TokenRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *TokenRes) Reset() {
	*x = TokenRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TokenRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenRes) ProtoMessage() {}

func (x *TokenRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenRes.ProtoReflect.Descriptor instead.
func (*TokenRes) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{35}
}

func (x *TokenRes) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type UserReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=Id,proto3" json:"Id,omitempty"`
}

func (x *UserReq) Reset() {
	*x = UserReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserReq) ProtoMessage() {}

func (x *UserReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserReq.ProtoReflect.Descriptor instead.
func (*UserReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{36}
}

func (x *UserReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type UserInfoRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       string `protobuf:"bytes,1,opt,name=Id,proto3" json:"Id,omitempty"`
	Nickname string `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Born     string `protobuf:"bytes,3,opt,name=born,proto3" json:"born,omitempty"`
}

func (x *UserInfoRes) Reset() {
	*x = UserInfoRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfoRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfoRes) ProtoMessage() {}

func (x *UserInfoRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfoRes.ProtoReflect.Descriptor instead.
func (*UserInfoRes) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{37}
}

func (x *UserInfoRes) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UserInfoRes) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *UserInfoRes) GetBorn() string {
	if x != nil {
		return x.Born
	}
	return ""
}

// 通过"noId"获取用户详情信息
type UserIdsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids  []string `protobuf:"bytes,1,rep,name=Ids,proto3" json:"Ids,omitempty"`   //id
	Cols []string `protobuf:"bytes,2,rep,name=cols,proto3" json:"cols,omitempty"` //指定列
}

func (x *UserIdsReq) Reset() {
	*x = UserIdsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserIdsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIdsReq) ProtoMessage() {}

func (x *UserIdsReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIdsReq.ProtoReflect.Descriptor instead.
func (*UserIdsReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{38}
}

func (x *UserIdsReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *UserIdsReq) GetCols() []string {
	if x != nil {
		return x.Cols
	}
	return nil
}

// 通过"_id"获取用户详情信息
type MongoIdsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids  []string `protobuf:"bytes,1,rep,name=Ids,proto3" json:"Ids,omitempty"`   //id
	Cols []string `protobuf:"bytes,2,rep,name=cols,proto3" json:"cols,omitempty"` //指定列
}

func (x *MongoIdsReq) Reset() {
	*x = MongoIdsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MongoIdsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MongoIdsReq) ProtoMessage() {}

func (x *MongoIdsReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MongoIdsReq.ProtoReflect.Descriptor instead.
func (*MongoIdsReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{39}
}

func (x *MongoIdsReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *MongoIdsReq) GetCols() []string {
	if x != nil {
		return x.Cols
	}
	return nil
}

// 通过"msgId"获取用户详情信息
type MsgIdsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ids  []string `protobuf:"bytes,1,rep,name=Ids,proto3" json:"Ids,omitempty"`   //id
	Cols []string `protobuf:"bytes,2,rep,name=cols,proto3" json:"cols,omitempty"` //指定列
}

func (x *MsgIdsReq) Reset() {
	*x = MsgIdsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MsgIdsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MsgIdsReq) ProtoMessage() {}

func (x *MsgIdsReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MsgIdsReq.ProtoReflect.Descriptor instead.
func (*MsgIdsReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{40}
}

func (x *MsgIdsReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *MsgIdsReq) GetCols() []string {
	if x != nil {
		return x.Cols
	}
	return nil
}

type UserInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Users []*UserInfo `protobuf:"bytes,1,rep,name=Users,proto3" json:"Users,omitempty"`
}

func (x *UserInfoRsp) Reset() {
	*x = UserInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfoRsp) ProtoMessage() {}

func (x *UserInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfoRsp.ProtoReflect.Descriptor instead.
func (*UserInfoRsp) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{41}
}

func (x *UserInfoRsp) GetUsers() []*UserInfo {
	if x != nil {
		return x.Users
	}
	return nil
}

// 更新用户信息
type UpdateUserReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   string `protobuf:"bytes,1,opt,name=Id,proto3" json:"Id,omitempty"`
	Data []byte `protobuf:"bytes,2,opt,name=Data,proto3" json:"Data,omitempty"`
}

func (x *UpdateUserReq) Reset() {
	*x = UpdateUserReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUserReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserReq) ProtoMessage() {}

func (x *UpdateUserReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserReq.ProtoReflect.Descriptor instead.
func (*UpdateUserReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{42}
}

func (x *UpdateUserReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateUserReq) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

// 查询用户是否存在
type UserExistReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NoId string `protobuf:"bytes,1,opt,name=NoId,proto3" json:"NoId,omitempty"`
}

func (x *UserExistReq) Reset() {
	*x = UserExistReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserExistReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserExistReq) ProtoMessage() {}

func (x *UserExistReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserExistReq.ProtoReflect.Descriptor instead.
func (*UserExistReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{43}
}

func (x *UserExistReq) GetNoId() string {
	if x != nil {
		return x.NoId
	}
	return ""
}

type UserExistRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Exist int32 `protobuf:"varint,1,opt,name=Exist,proto3" json:"Exist,omitempty"`
}

func (x *UserExistRsp) Reset() {
	*x = UserExistRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserExistRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserExistRsp) ProtoMessage() {}

func (x *UserExistRsp) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserExistRsp.ProtoReflect.Descriptor instead.
func (*UserExistRsp) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{44}
}

func (x *UserExistRsp) GetExist() int32 {
	if x != nil {
		return x.Exist
	}
	return 0
}

// 详情见User模型
type UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             string     `protobuf:"bytes,1,opt,name=Id,proto3" json:"Id,omitempty"`
	NickName       string     `protobuf:"bytes,2,opt,name=NickName,proto3" json:"NickName,omitempty"`
	NoId           string     `protobuf:"bytes,3,opt,name=NoId,proto3" json:"NoId,omitempty"`
	MsgId          string     `protobuf:"bytes,4,opt,name=MsgId,proto3" json:"MsgId,omitempty"`
	RcToken        string     `protobuf:"bytes,5,opt,name=RcToken,proto3" json:"RcToken,omitempty"`
	Status         int32      `protobuf:"varint,6,opt,name=Status,proto3" json:"Status,omitempty"`
	Mobile         string     `protobuf:"bytes,7,opt,name=Mobile,proto3" json:"Mobile,omitempty"`
	DemanderStatus int32      `protobuf:"varint,8,opt,name=DemanderStatus,proto3" json:"DemanderStatus,omitempty"`
	SupplierStatus int32      `protobuf:"varint,9,opt,name=SupplierStatus,proto3" json:"SupplierStatus,omitempty"`
	Gender         string     `protobuf:"bytes,10,opt,name=Gender,proto3" json:"Gender,omitempty"`
	Age            int32      `protobuf:"varint,11,opt,name=Age,proto3" json:"Age,omitempty"`
	FinishedOrders int64      `protobuf:"varint,12,opt,name=FinishedOrders,proto3" json:"FinishedOrders,omitempty"`
	Height         int32      `protobuf:"varint,13,opt,name=Height,proto3" json:"Height,omitempty"`
	Weight         int32      `protobuf:"varint,14,opt,name=Weight,proto3" json:"Weight,omitempty"`
	Album          []*UserImg `protobuf:"bytes,15,rep,name=Album,proto3" json:"Album,omitempty"`
	AvatarUrl      string     `protobuf:"bytes,16,opt,name=AvatarUrl,proto3" json:"AvatarUrl,omitempty"`
	CheckAvatar    string     `protobuf:"bytes,17,opt,name=CheckAvatar,proto3" json:"CheckAvatar,omitempty"`
	AvatarStatus   string     `protobuf:"bytes,18,opt,name=AvatarStatus,proto3" json:"AvatarStatus,omitempty"`
	AvatarLevel    string     `protobuf:"bytes,19,opt,name=AvatarLevel,proto3" json:"AvatarLevel,omitempty"`
	AudioUrl       string     `protobuf:"bytes,20,opt,name=AudioUrl,proto3" json:"AudioUrl,omitempty"`
	AudioStatus    string     `protobuf:"bytes,21,opt,name=AudioStatus,proto3" json:"AudioStatus,omitempty"`
	Duration       int64      `protobuf:"varint,22,opt,name=Duration,proto3" json:"Duration,omitempty"`
	Education      string     `protobuf:"bytes,23,opt,name=Education,proto3" json:"Education,omitempty"`
	Province       string     `protobuf:"bytes,24,opt,name=Province,proto3" json:"Province,omitempty"`
	City           string     `protobuf:"bytes,25,opt,name=City,proto3" json:"City,omitempty"`
	Area           string     `protobuf:"bytes,26,opt,name=Area,proto3" json:"Area,omitempty"`
	HomeTown       string     `protobuf:"bytes,27,opt,name=HomeTown,proto3" json:"HomeTown,omitempty"`
	RealStatus     string     `protobuf:"bytes,28,opt,name=RealStatus,proto3" json:"RealStatus,omitempty"`
	RealNameStatus string     `protobuf:"bytes,29,opt,name=RealNameStatus,proto3" json:"RealNameStatus,omitempty"`
	Intro          string     `protobuf:"bytes,30,opt,name=Intro,proto3" json:"Intro,omitempty"`
	Lon            float64    `protobuf:"fixed64,31,opt,name=Lon,proto3" json:"Lon,omitempty"`
	Lat            float64    `protobuf:"fixed64,32,opt,name=Lat,proto3" json:"Lat,omitempty"`
	IsOnline       int32      `protobuf:"varint,33,opt,name=IsOnline,proto3" json:"IsOnline,omitempty"`
	StarLevel      float64    `protobuf:"fixed64,34,opt,name=StarLevel,proto3" json:"StarLevel,omitempty"`
	LastLoginTime  string     `protobuf:"bytes,35,opt,name=LastLoginTime,proto3" json:"LastLoginTime,omitempty"`
	Born           string     `protobuf:"bytes,36,opt,name=Born,proto3" json:"Born,omitempty"`
	Distance       string     `protobuf:"bytes,37,opt,name=Distance,proto3" json:"Distance,omitempty"`
	Days           int32      `protobuf:"varint,38,opt,name=Days,proto3" json:"Days,omitempty"`
	NewStatus      int32      `protobuf:"varint,39,opt,name=NewStatus,proto3" json:"NewStatus,omitempty"`
	FollowStatus   int32      `protobuf:"varint,40,opt,name=FollowStatus,proto3" json:"FollowStatus,omitempty"`
	BlackStatus    int32      `protobuf:"varint,41,opt,name=BlackStatus,proto3" json:"BlackStatus,omitempty"`
	IsBindPhone    bool       `protobuf:"varint,42,opt,name=IsBindPhone,proto3" json:"IsBindPhone,omitempty"`
	Remark         string     `protobuf:"bytes,43,opt,name=Remark,proto3" json:"Remark,omitempty"`
	ForbidDays     int32      `protobuf:"varint,44,opt,name=ForbidDays,proto3" json:"ForbidDays,omitempty"`
	ForbidTime     int64      `protobuf:"varint,45,opt,name=ForbidTime,proto3" json:"ForbidTime,omitempty"`
	Uid            string     `protobuf:"bytes,46,opt,name=Uid,proto3" json:"Uid,omitempty"`
	UnionId        string     `protobuf:"bytes,47,opt,name=UnionId,proto3" json:"UnionId,omitempty"`
	RegistryType   string     `protobuf:"bytes,48,opt,name=RegistryType,proto3" json:"RegistryType,omitempty"`
	RegisterStatus string     `protobuf:"bytes,49,opt,name=RegisterStatus,proto3" json:"RegisterStatus,omitempty"`
	InviteCode     string     `protobuf:"bytes,50,opt,name=InviteCode,proto3" json:"InviteCode,omitempty"`
	GroupCode      string     `protobuf:"bytes,51,opt,name=GroupCode,proto3" json:"GroupCode,omitempty"`
	ImgList        string     `protobuf:"bytes,52,opt,name=ImgList,proto3" json:"ImgList,omitempty"`
	SMResult       string     `protobuf:"bytes,53,opt,name=SMResult,proto3" json:"SMResult,omitempty"`
	SMReason       string     `protobuf:"bytes,54,opt,name=SMReason,proto3" json:"SMReason,omitempty"`
	IdName         string     `protobuf:"bytes,55,opt,name=IdName,proto3" json:"IdName,omitempty"`
	SuperAdmin     string     `protobuf:"bytes,56,opt,name=SuperAdmin,proto3" json:"SuperAdmin,omitempty"`
	IdCard         string     `protobuf:"bytes,57,opt,name=IdCard,proto3" json:"IdCard,omitempty"`
	Robot          string     `protobuf:"bytes,58,opt,name=Robot,proto3" json:"Robot,omitempty"`
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{45}
}

func (x *UserInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UserInfo) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *UserInfo) GetNoId() string {
	if x != nil {
		return x.NoId
	}
	return ""
}

func (x *UserInfo) GetMsgId() string {
	if x != nil {
		return x.MsgId
	}
	return ""
}

func (x *UserInfo) GetRcToken() string {
	if x != nil {
		return x.RcToken
	}
	return ""
}

func (x *UserInfo) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *UserInfo) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *UserInfo) GetDemanderStatus() int32 {
	if x != nil {
		return x.DemanderStatus
	}
	return 0
}

func (x *UserInfo) GetSupplierStatus() int32 {
	if x != nil {
		return x.SupplierStatus
	}
	return 0
}

func (x *UserInfo) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *UserInfo) GetAge() int32 {
	if x != nil {
		return x.Age
	}
	return 0
}

func (x *UserInfo) GetFinishedOrders() int64 {
	if x != nil {
		return x.FinishedOrders
	}
	return 0
}

func (x *UserInfo) GetHeight() int32 {
	if x != nil {
		return x.Height
	}
	return 0
}

func (x *UserInfo) GetWeight() int32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *UserInfo) GetAlbum() []*UserImg {
	if x != nil {
		return x.Album
	}
	return nil
}

func (x *UserInfo) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

func (x *UserInfo) GetCheckAvatar() string {
	if x != nil {
		return x.CheckAvatar
	}
	return ""
}

func (x *UserInfo) GetAvatarStatus() string {
	if x != nil {
		return x.AvatarStatus
	}
	return ""
}

func (x *UserInfo) GetAvatarLevel() string {
	if x != nil {
		return x.AvatarLevel
	}
	return ""
}

func (x *UserInfo) GetAudioUrl() string {
	if x != nil {
		return x.AudioUrl
	}
	return ""
}

func (x *UserInfo) GetAudioStatus() string {
	if x != nil {
		return x.AudioStatus
	}
	return ""
}

func (x *UserInfo) GetDuration() int64 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *UserInfo) GetEducation() string {
	if x != nil {
		return x.Education
	}
	return ""
}

func (x *UserInfo) GetProvince() string {
	if x != nil {
		return x.Province
	}
	return ""
}

func (x *UserInfo) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *UserInfo) GetArea() string {
	if x != nil {
		return x.Area
	}
	return ""
}

func (x *UserInfo) GetHomeTown() string {
	if x != nil {
		return x.HomeTown
	}
	return ""
}

func (x *UserInfo) GetRealStatus() string {
	if x != nil {
		return x.RealStatus
	}
	return ""
}

func (x *UserInfo) GetRealNameStatus() string {
	if x != nil {
		return x.RealNameStatus
	}
	return ""
}

func (x *UserInfo) GetIntro() string {
	if x != nil {
		return x.Intro
	}
	return ""
}

func (x *UserInfo) GetLon() float64 {
	if x != nil {
		return x.Lon
	}
	return 0
}

func (x *UserInfo) GetLat() float64 {
	if x != nil {
		return x.Lat
	}
	return 0
}

func (x *UserInfo) GetIsOnline() int32 {
	if x != nil {
		return x.IsOnline
	}
	return 0
}

func (x *UserInfo) GetStarLevel() float64 {
	if x != nil {
		return x.StarLevel
	}
	return 0
}

func (x *UserInfo) GetLastLoginTime() string {
	if x != nil {
		return x.LastLoginTime
	}
	return ""
}

func (x *UserInfo) GetBorn() string {
	if x != nil {
		return x.Born
	}
	return ""
}

func (x *UserInfo) GetDistance() string {
	if x != nil {
		return x.Distance
	}
	return ""
}

func (x *UserInfo) GetDays() int32 {
	if x != nil {
		return x.Days
	}
	return 0
}

func (x *UserInfo) GetNewStatus() int32 {
	if x != nil {
		return x.NewStatus
	}
	return 0
}

func (x *UserInfo) GetFollowStatus() int32 {
	if x != nil {
		return x.FollowStatus
	}
	return 0
}

func (x *UserInfo) GetBlackStatus() int32 {
	if x != nil {
		return x.BlackStatus
	}
	return 0
}

func (x *UserInfo) GetIsBindPhone() bool {
	if x != nil {
		return x.IsBindPhone
	}
	return false
}

func (x *UserInfo) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *UserInfo) GetForbidDays() int32 {
	if x != nil {
		return x.ForbidDays
	}
	return 0
}

func (x *UserInfo) GetForbidTime() int64 {
	if x != nil {
		return x.ForbidTime
	}
	return 0
}

func (x *UserInfo) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *UserInfo) GetUnionId() string {
	if x != nil {
		return x.UnionId
	}
	return ""
}

func (x *UserInfo) GetRegistryType() string {
	if x != nil {
		return x.RegistryType
	}
	return ""
}

func (x *UserInfo) GetRegisterStatus() string {
	if x != nil {
		return x.RegisterStatus
	}
	return ""
}

func (x *UserInfo) GetInviteCode() string {
	if x != nil {
		return x.InviteCode
	}
	return ""
}

func (x *UserInfo) GetGroupCode() string {
	if x != nil {
		return x.GroupCode
	}
	return ""
}

func (x *UserInfo) GetImgList() string {
	if x != nil {
		return x.ImgList
	}
	return ""
}

func (x *UserInfo) GetSMResult() string {
	if x != nil {
		return x.SMResult
	}
	return ""
}

func (x *UserInfo) GetSMReason() string {
	if x != nil {
		return x.SMReason
	}
	return ""
}

func (x *UserInfo) GetIdName() string {
	if x != nil {
		return x.IdName
	}
	return ""
}

func (x *UserInfo) GetSuperAdmin() string {
	if x != nil {
		return x.SuperAdmin
	}
	return ""
}

func (x *UserInfo) GetIdCard() string {
	if x != nil {
		return x.IdCard
	}
	return ""
}

func (x *UserInfo) GetRobot() string {
	if x != nil {
		return x.Robot
	}
	return ""
}

// 相册
type UserImg struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ImgUrl string `protobuf:"bytes,1,opt,name=ImgUrl,proto3" json:"ImgUrl,omitempty"`
	Status string `protobuf:"bytes,2,opt,name=Status,proto3" json:"Status,omitempty"`
}

func (x *UserImg) Reset() {
	*x = UserImg{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserImg) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserImg) ProtoMessage() {}

func (x *UserImg) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserImg.ProtoReflect.Descriptor instead.
func (*UserImg) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{46}
}

func (x *UserImg) GetImgUrl() string {
	if x != nil {
		return x.ImgUrl
	}
	return ""
}

func (x *UserImg) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type PullUserReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Query  []byte   `protobuf:"bytes,1,opt,name=Query,proto3" json:"Query,omitempty"`
	Cols   []string `protobuf:"bytes,2,rep,name=Cols,proto3" json:"Cols,omitempty"`
	Limit  int32    `protobuf:"varint,3,opt,name=Limit,proto3" json:"Limit,omitempty"`   //抓取数量
	IsRand int32    `protobuf:"varint,4,opt,name=IsRand,proto3" json:"IsRand,omitempty"` //是否随机
}

func (x *PullUserReq) Reset() {
	*x = PullUserReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PullUserReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PullUserReq) ProtoMessage() {}

func (x *PullUserReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PullUserReq.ProtoReflect.Descriptor instead.
func (*PullUserReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{47}
}

func (x *PullUserReq) GetQuery() []byte {
	if x != nil {
		return x.Query
	}
	return nil
}

func (x *PullUserReq) GetCols() []string {
	if x != nil {
		return x.Cols
	}
	return nil
}

func (x *PullUserReq) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

func (x *PullUserReq) GetIsRand() int32 {
	if x != nil {
		return x.IsRand
	}
	return 0
}

type PullUserRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Users []*UserInfo `protobuf:"bytes,1,rep,name=Users,proto3" json:"Users,omitempty"`
}

func (x *PullUserRsp) Reset() {
	*x = PullUserRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PullUserRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PullUserRsp) ProtoMessage() {}

func (x *PullUserRsp) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PullUserRsp.ProtoReflect.Descriptor instead.
func (*PullUserRsp) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{48}
}

func (x *PullUserRsp) GetUsers() []*UserInfo {
	if x != nil {
		return x.Users
	}
	return nil
}

type UserRemarkReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NoId string   `protobuf:"bytes,1,opt,name=NoId,proto3" json:"NoId,omitempty"`
	Ids  []string `protobuf:"bytes,2,rep,name=Ids,proto3" json:"Ids,omitempty"`
}

func (x *UserRemarkReq) Reset() {
	*x = UserRemarkReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserRemarkReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserRemarkReq) ProtoMessage() {}

func (x *UserRemarkReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserRemarkReq.ProtoReflect.Descriptor instead.
func (*UserRemarkReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{49}
}

func (x *UserRemarkReq) GetNoId() string {
	if x != nil {
		return x.NoId
	}
	return ""
}

func (x *UserRemarkReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type UserRemarkRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserRemark map[string]string `protobuf:"bytes,1,rep,name=UserRemark,proto3" json:"UserRemark,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *UserRemarkRsp) Reset() {
	*x = UserRemarkRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserRemarkRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserRemarkRsp) ProtoMessage() {}

func (x *UserRemarkRsp) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserRemarkRsp.ProtoReflect.Descriptor instead.
func (*UserRemarkRsp) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{50}
}

func (x *UserRemarkRsp) GetUserRemark() map[string]string {
	if x != nil {
		return x.UserRemark
	}
	return nil
}

type GetOnlineRsq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Gender string `protobuf:"bytes,1,opt,name=gender,proto3" json:"gender,omitempty"`
}

func (x *GetOnlineRsq) Reset() {
	*x = GetOnlineRsq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOnlineRsq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOnlineRsq) ProtoMessage() {}

func (x *GetOnlineRsq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOnlineRsq.ProtoReflect.Descriptor instead.
func (*GetOnlineRsq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{51}
}

func (x *GetOnlineRsq) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

type GetOnlineRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Users []string `protobuf:"bytes,1,rep,name=Users,proto3" json:"Users,omitempty"`
}

func (x *GetOnlineRsp) Reset() {
	*x = GetOnlineRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetOnlineRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOnlineRsp) ProtoMessage() {}

func (x *GetOnlineRsp) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOnlineRsp.ProtoReflect.Descriptor instead.
func (*GetOnlineRsp) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{52}
}

func (x *GetOnlineRsp) GetUsers() []string {
	if x != nil {
		return x.Users
	}
	return nil
}

type UpdateAlbumReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserId string            `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId,omitempty"`
	Album  map[string]string `protobuf:"bytes,2,rep,name=album,proto3" json:"album,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *UpdateAlbumReq) Reset() {
	*x = UpdateAlbumReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAlbumReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAlbumReq) ProtoMessage() {}

func (x *UpdateAlbumReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAlbumReq.ProtoReflect.Descriptor instead.
func (*UpdateAlbumReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{53}
}

func (x *UpdateAlbumReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UpdateAlbumReq) GetAlbum() map[string]string {
	if x != nil {
		return x.Album
	}
	return nil
}

type UpdateAlbumRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status bool `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateAlbumRsp) Reset() {
	*x = UpdateAlbumRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateAlbumRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAlbumRsp) ProtoMessage() {}

func (x *UpdateAlbumRsp) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAlbumRsp.ProtoReflect.Descriptor instead.
func (*UpdateAlbumRsp) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{54}
}

func (x *UpdateAlbumRsp) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

type ValidHeadReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NoId   string  `protobuf:"bytes,1,opt,name=noId,proto3" json:"noId,omitempty"`
	Imei   string  `protobuf:"bytes,2,opt,name=imei,proto3" json:"imei,omitempty"`
	Ip     string  `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip,omitempty"`
	Lon    float32 `protobuf:"fixed32,4,opt,name=lon,proto3" json:"lon,omitempty"`
	Lat    float32 `protobuf:"fixed32,5,opt,name=lat,proto3" json:"lat,omitempty"`
	Url    string  `protobuf:"bytes,6,opt,name=url,proto3" json:"url,omitempty"`
	Status string  `protobuf:"bytes,7,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ValidHeadReq) Reset() {
	*x = ValidHeadReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidHeadReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidHeadReq) ProtoMessage() {}

func (x *ValidHeadReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidHeadReq.ProtoReflect.Descriptor instead.
func (*ValidHeadReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{55}
}

func (x *ValidHeadReq) GetNoId() string {
	if x != nil {
		return x.NoId
	}
	return ""
}

func (x *ValidHeadReq) GetImei() string {
	if x != nil {
		return x.Imei
	}
	return ""
}

func (x *ValidHeadReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *ValidHeadReq) GetLon() float32 {
	if x != nil {
		return x.Lon
	}
	return 0
}

func (x *ValidHeadReq) GetLat() float32 {
	if x != nil {
		return x.Lat
	}
	return 0
}

func (x *ValidHeadReq) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *ValidHeadReq) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type ValidHeadRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *ValidHeadRsp) Reset() {
	*x = ValidHeadRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[56]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidHeadRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidHeadRsp) ProtoMessage() {}

func (x *ValidHeadRsp) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[56]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidHeadRsp.ProtoReflect.Descriptor instead.
func (*ValidHeadRsp) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{56}
}

func (x *ValidHeadRsp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

type ReviewInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Event    string `protobuf:"bytes,1,opt,name=event,proto3" json:"event,omitempty"`
	RuleType string `protobuf:"bytes,2,opt,name=ruleType,proto3" json:"ruleType,omitempty"`
	ReqText  string `protobuf:"bytes,3,opt,name=reqText,proto3" json:"reqText,omitempty"`
	RawText  string `protobuf:"bytes,4,opt,name=rawText,proto3" json:"rawText,omitempty"`
	NoId     string `protobuf:"bytes,5,opt,name=noId,proto3" json:"noId,omitempty"`
}

func (x *ReviewInfoReq) Reset() {
	*x = ReviewInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[57]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReviewInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewInfoReq) ProtoMessage() {}

func (x *ReviewInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[57]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewInfoReq.ProtoReflect.Descriptor instead.
func (*ReviewInfoReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{57}
}

func (x *ReviewInfoReq) GetEvent() string {
	if x != nil {
		return x.Event
	}
	return ""
}

func (x *ReviewInfoReq) GetRuleType() string {
	if x != nil {
		return x.RuleType
	}
	return ""
}

func (x *ReviewInfoReq) GetReqText() string {
	if x != nil {
		return x.ReqText
	}
	return ""
}

func (x *ReviewInfoReq) GetRawText() string {
	if x != nil {
		return x.RawText
	}
	return ""
}

func (x *ReviewInfoReq) GetNoId() string {
	if x != nil {
		return x.NoId
	}
	return ""
}

type ReviewInfoRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status string `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ReviewInfoRsp) Reset() {
	*x = ReviewInfoRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[58]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReviewInfoRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReviewInfoRsp) ProtoMessage() {}

func (x *ReviewInfoRsp) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[58]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReviewInfoRsp.ProtoReflect.Descriptor instead.
func (*ReviewInfoRsp) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{58}
}

func (x *ReviewInfoRsp) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type IPLocReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ip string `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *IPLocReq) Reset() {
	*x = IPLocReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[59]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IPLocReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IPLocReq) ProtoMessage() {}

func (x *IPLocReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[59]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IPLocReq.ProtoReflect.Descriptor instead.
func (*IPLocReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{59}
}

func (x *IPLocReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

type IPLocRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Province string `protobuf:"bytes,1,opt,name=province,proto3" json:"province,omitempty"`
}

func (x *IPLocRsp) Reset() {
	*x = IPLocRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[60]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IPLocRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IPLocRsp) ProtoMessage() {}

func (x *IPLocRsp) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[60]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IPLocRsp.ProtoReflect.Descriptor instead.
func (*IPLocRsp) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{60}
}

func (x *IPLocRsp) GetProvince() string {
	if x != nil {
		return x.Province
	}
	return ""
}

type UserInterestTag struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TagId   string `protobuf:"bytes,1,opt,name=tagId,proto3" json:"tagId,omitempty"`
	TagName string `protobuf:"bytes,2,opt,name=tagName,proto3" json:"tagName,omitempty"`
}

func (x *UserInterestTag) Reset() {
	*x = UserInterestTag{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[61]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInterestTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInterestTag) ProtoMessage() {}

func (x *UserInterestTag) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[61]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInterestTag.ProtoReflect.Descriptor instead.
func (*UserInterestTag) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{61}
}

func (x *UserInterestTag) GetTagId() string {
	if x != nil {
		return x.TagId
	}
	return ""
}

func (x *UserInterestTag) GetTagName() string {
	if x != nil {
		return x.TagName
	}
	return ""
}

type GetUserInterestReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NoId string `protobuf:"bytes,1,opt,name=noId,proto3" json:"noId,omitempty"`
}

func (x *GetUserInterestReq) Reset() {
	*x = GetUserInterestReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[62]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserInterestReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInterestReq) ProtoMessage() {}

func (x *GetUserInterestReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[62]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInterestReq.ProtoReflect.Descriptor instead.
func (*GetUserInterestReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{62}
}

func (x *GetUserInterestReq) GetNoId() string {
	if x != nil {
		return x.NoId
	}
	return ""
}

type GetUserInterestRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Interests []*UserInterestTag `protobuf:"bytes,1,rep,name=interests,proto3" json:"interests,omitempty"`
}

func (x *GetUserInterestRsp) Reset() {
	*x = GetUserInterestRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[63]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserInterestRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInterestRsp) ProtoMessage() {}

func (x *GetUserInterestRsp) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[63]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInterestRsp.ProtoReflect.Descriptor instead.
func (*GetUserInterestRsp) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{63}
}

func (x *GetUserInterestRsp) GetInterests() []*UserInterestTag {
	if x != nil {
		return x.Interests
	}
	return nil
}

type GetUserInterestsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NoIds []string `protobuf:"bytes,1,rep,name=noIds,proto3" json:"noIds,omitempty"`
}

func (x *GetUserInterestsReq) Reset() {
	*x = GetUserInterestsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[64]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserInterestsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInterestsReq) ProtoMessage() {}

func (x *GetUserInterestsReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[64]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInterestsReq.ProtoReflect.Descriptor instead.
func (*GetUserInterestsReq) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{64}
}

func (x *GetUserInterestsReq) GetNoIds() []string {
	if x != nil {
		return x.NoIds
	}
	return nil
}

type UserInterestsVO struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Interests []string `protobuf:"bytes,1,rep,name=interests,proto3" json:"interests,omitempty"`
}

func (x *UserInterestsVO) Reset() {
	*x = UserInterestsVO{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[65]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInterestsVO) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInterestsVO) ProtoMessage() {}

func (x *UserInterestsVO) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[65]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInterestsVO.ProtoReflect.Descriptor instead.
func (*UserInterestsVO) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{65}
}

func (x *UserInterestsVO) GetInterests() []string {
	if x != nil {
		return x.Interests
	}
	return nil
}

type GetUserInterestsRsp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ret map[string]*UserInterestsVO `protobuf:"bytes,1,rep,name=ret,proto3" json:"ret,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *GetUserInterestsRsp) Reset() {
	*x = GetUserInterestsRsp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_proto_msgTypes[66]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetUserInterestsRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInterestsRsp) ProtoMessage() {}

func (x *GetUserInterestsRsp) ProtoReflect() protoreflect.Message {
	mi := &file_user_proto_msgTypes[66]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInterestsRsp.ProtoReflect.Descriptor instead.
func (*GetUserInterestsRsp) Descriptor() ([]byte, []int) {
	return file_user_proto_rawDescGZIP(), []int{66}
}

func (x *GetUserInterestsRsp) GetRet() map[string]*UserInterestsVO {
	if x != nil {
		return x.Ret
	}
	return nil
}

var File_user_proto protoreflect.FileDescriptor

var file_user_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x22, 0x18, 0x0a, 0x16, 0x54, 0x69, 0x6d, 0x65, 0x6f,
	0x75, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x22, 0x92, 0x01, 0x0a, 0x16, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x4d, 0x6f, 0x64,
	0x69, 0x66, 0x79, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x04,
	0x74, 0x4d, 0x61, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x4d, 0x6f,
	0x64, 0x69, 0x66, 0x79, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x52, 0x73, 0x70, 0x2e, 0x54, 0x4d,
	0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x74, 0x4d, 0x61, 0x70, 0x1a, 0x37, 0x0a,
	0x09, 0x54, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x70, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x64,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x6f, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f,
	0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x6f, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x6f, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x28,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0xb2, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74,
	0x41, 0x6e, 0x64, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x52, 0x73,
	0x70, 0x12, 0x1e, 0x0a, 0x0a, 0x69, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x6c, 0x69, 0x63, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a, 0x69, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x6c, 0x69, 0x63,
	0x74, 0x12, 0x2d, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x19, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x4d, 0x6f, 0x62, 0x69,
	0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d,
	0x12, 0x29, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x02, 0x74, 0x6f, 0x12, 0x20, 0x0a, 0x0b, 0x69,
	0x73, 0x4f, 0x76, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x0b, 0x69, 0x73, 0x4f, 0x76, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x22, 0xb2, 0x01,
	0x0a, 0x0e, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x6f, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x20, 0x0a, 0x0b, 0x77, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x77,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x68,
	0x61, 0x72, 0x6d, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x63, 0x68, 0x61, 0x72, 0x6d, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x6f,
	0x62, 0x69, 0x6c, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x6f, 0x62, 0x69,
	0x6c, 0x65, 0x22, 0xce, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79,
	0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x6f, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f, 0x49,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x38, 0x0a, 0x0b, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x0b, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x22, 0x5f, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79,
	0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x2f, 0x0a,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d, 0x6f,
	0x62, 0x69, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x43,
	0x6f, 0x75, 0x6e, 0x74, 0x22, 0xcc, 0x02, 0x0a, 0x10, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d,
	0x6f, 0x62, 0x69, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f, 0x49, 0x64, 0x12, 0x1e, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e, 0x0a,
	0x0a, 0x66, 0x72, 0x6f, 0x6d, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x66, 0x72, 0x6f, 0x6d, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x74, 0x6f, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x74, 0x6f, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x70, 0x70,
	0x6c, 0x79, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x61, 0x70, 0x70, 0x6c, 0x79, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x22, 0x0a,
	0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1c, 0x0a, 0x09, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x49, 0x6d, 0x67, 0x18, 0x0a, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x49, 0x6d, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x2c, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79,
	0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x6f, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f, 0x49,
	0x64, 0x22, 0xde, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d,
	0x6f, 0x62, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x6f, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f, 0x49, 0x64,
	0x12, 0x1e, 0x0a, 0x0a, 0x66, 0x72, 0x6f, 0x6d, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x72, 0x6f, 0x6d, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x74, 0x6f, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x74, 0x6f, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x61, 0x70, 0x70, 0x6c, 0x79, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x49, 0x6d, 0x67, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x49, 0x6d, 0x67, 0x12, 0x1e, 0x0a, 0x0a, 0x6d, 0x6f,
	0x64, 0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x22, 0x8e, 0x03, 0x0a, 0x14, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4d, 0x6f, 0x64, 0x69,
	0x66, 0x79, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x6f, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f, 0x49, 0x64, 0x12,
	0x1e, 0x0a, 0x0a, 0x66, 0x72, 0x6f, 0x6d, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x66, 0x72, 0x6f, 0x6d, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12,
	0x1a, 0x0a, 0x08, 0x74, 0x6f, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x74, 0x6f, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x35, 0x0a, 0x0a, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e,
	0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x69,
	0x66, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x52,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x70, 0x70,
	0x6c, 0x79, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x64, 0x6d, 0x69,
	0x6e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x49, 0x6d, 0x67, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x49, 0x6d, 0x67, 0x12, 0x3b, 0x0a, 0x0c, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x64, 0x22, 0x3e, 0x0a, 0x10, 0x53, 0x65, 0x74, 0x50, 0x75, 0x73, 0x68, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x2b, 0x0a, 0x15, 0x55, 0x6e, 0x42, 0x69, 0x6e, 0x64, 0x50, 0x75, 0x73,
	0x68, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x6f, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f, 0x49, 0x64,
	0x22, 0x6d, 0x0a, 0x13, 0x42, 0x69, 0x6e, 0x64, 0x50, 0x75, 0x73, 0x68, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x63,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x63, 0x69, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x6c,
	0x69, 0x61, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x22,
	0xd5, 0x01, 0x0a, 0x0e, 0x50, 0x75, 0x73, 0x68, 0x42, 0x79, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x52,
	0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x72, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x66, 0x72, 0x6f, 0x6d, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x49, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x49, 0x64, 0x73,
	0x12, 0x35, 0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65,
	0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2f, 0x0a, 0x08, 0x70, 0x75, 0x73, 0x68, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08,
	0x70, 0x75, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2d, 0x0a, 0x06, 0x61, 0x74, 0x74, 0x61,
	0x63, 0x68, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x62, 0x61, 0x73, 0x65, 0x2e, 0x50, 0x75, 0x73, 0x68, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x52,
	0x06, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x22, 0x28, 0x0a, 0x0a, 0x50, 0x75, 0x73, 0x68, 0x41,
	0x74, 0x74, 0x61, 0x63, 0x68, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x69, 0x76, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x69, 0x76, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x22, 0x0a, 0x0a, 0x08, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x52, 0x73, 0x70, 0x22, 0x28, 0x0a,
	0x10, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65,
	0x71, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x05, 0x6e, 0x6f, 0x49, 0x64, 0x73, 0x22, 0x3d, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x73, 0x70, 0x12, 0x29, 0x0a, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x8e, 0x02, 0x0a, 0x0a, 0x53, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x6f, 0x73,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x0f, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65,
	0x43, 0x68, 0x61, 0x74, 0x50, 0x75, 0x73, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x50, 0x75, 0x73, 0x68, 0x12,
	0x22, 0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x6e, 0x4c, 0x69, 0x76, 0x65, 0x50, 0x75, 0x73, 0x68, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x70, 0x65, 0x6e, 0x4c, 0x69, 0x76, 0x65, 0x50,
	0x75, 0x73, 0x68, 0x12, 0x30, 0x0a, 0x13, 0x61, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x66, 0x75, 0x73,
	0x65, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x43, 0x68, 0x61, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x13, 0x61, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x66, 0x75, 0x73, 0x65, 0x4d, 0x75, 0x6c, 0x74,
	0x69, 0x43, 0x68, 0x61, 0x74, 0x12, 0x26, 0x0a, 0x0e, 0x61, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x66,
	0x75, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x61,
	0x75, 0x74, 0x6f, 0x52, 0x65, 0x66, 0x75, 0x73, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x28, 0x0a,
	0x0f, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x42, 0x72, 0x6f, 0x77, 0x73, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75,
	0x73, 0x42, 0x72, 0x6f, 0x77, 0x73, 0x65, 0x22, 0xec, 0x01, 0x0a, 0x10, 0x53, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x6f, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f, 0x49, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x0f,
	0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x43, 0x68, 0x61, 0x74, 0x50, 0x75, 0x73, 0x68, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x43, 0x68,
	0x61, 0x74, 0x50, 0x75, 0x73, 0x68, 0x12, 0x22, 0x0a, 0x0c, 0x6f, 0x70, 0x65, 0x6e, 0x4c, 0x69,
	0x76, 0x65, 0x50, 0x75, 0x73, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x70,
	0x65, 0x6e, 0x4c, 0x69, 0x76, 0x65, 0x50, 0x75, 0x73, 0x68, 0x12, 0x30, 0x0a, 0x13, 0x61, 0x75,
	0x74, 0x6f, 0x52, 0x65, 0x66, 0x75, 0x73, 0x65, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x43, 0x68, 0x61,
	0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x61, 0x75, 0x74, 0x6f, 0x52, 0x65, 0x66,
	0x75, 0x73, 0x65, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x43, 0x68, 0x61, 0x74, 0x12, 0x28, 0x0a, 0x0f,
	0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73, 0x42, 0x72, 0x6f, 0x77, 0x73, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x61, 0x6e, 0x6f, 0x6e, 0x79, 0x6d, 0x6f, 0x75, 0x73,
	0x42, 0x72, 0x6f, 0x77, 0x73, 0x65, 0x22, 0x12, 0x0a, 0x10, 0x53, 0x65, 0x74, 0x55, 0x73, 0x65,
	0x72, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x73, 0x70, 0x22, 0x5e, 0x0a, 0x12, 0x47, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x46, 0x61, 0x6e, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x6f, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x70, 0x61, 0x67, 0x65, 0x4e, 0x75, 0x6d, 0x12, 0x1a,
	0x0a, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x40, 0x0a, 0x12, 0x47, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x46, 0x61, 0x6e, 0x73, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70,
	0x12, 0x2a, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x55, 0x0a, 0x0b,
	0x53, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x66,
	0x72, 0x6f, 0x6d, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x72, 0x6f,
	0x6d, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x6f, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x6f, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x0e, 0x0a, 0x0c, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4e, 0x75, 0x6d,
	0x52, 0x65, 0x71, 0x22, 0x24, 0x0a, 0x0c, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x4e, 0x75, 0x6d,
	0x52, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x83, 0x01, 0x0a, 0x0b, 0x52, 0x6f,
	0x62, 0x6f, 0x74, 0x41, 0x64, 0x64, 0x52, 0x65, 0x71, 0x12, 0x26, 0x0a, 0x05, 0x72, 0x6f, 0x62,
	0x6f, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x10, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x62, 0x61, 0x73, 0x65, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x05, 0x72, 0x6f, 0x62, 0x6f,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x6d, 0x6f, 0x64, 0x65, 0x12, 0x38, 0x0a, 0x0b, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x0b, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22,
	0x87, 0x01, 0x0a, 0x0b, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x10, 0x0a, 0x03, 0x6e, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6e, 0x75,
	0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x4d, 0x69, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x4d, 0x69, 0x6e, 0x12,
	0x1c, 0x0a, 0x09, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x4d, 0x61, 0x78, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x4d, 0x61, 0x78, 0x12, 0x12, 0x0a,
	0x04, 0x6d, 0x61, 0x6c, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x6d, 0x61, 0x6c,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x65, 0x6d, 0x61, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x06, 0x66, 0x65, 0x6d, 0x61, 0x6c, 0x65, 0x22, 0xef, 0x01, 0x0a, 0x05, 0x72, 0x6f,
	0x62, 0x6f, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x6f, 0x72, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x62, 0x6f, 0x72, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12,
	0x20, 0x0a, 0x0b, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x77, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x4c, 0x65, 0x76, 0x65,
	0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x61, 0x72, 0x53, 0x69, 0x67, 0x6e, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x61, 0x72, 0x53, 0x69, 0x67, 0x6e, 0x12, 0x16, 0x0a,
	0x06, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x68,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x1c, 0x0a,
	0x09, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x55, 0x72, 0x6c, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x55, 0x72, 0x6c, 0x22, 0x8f, 0x01, 0x0a, 0x0b,
	0x52, 0x6f, 0x62, 0x6f, 0x74, 0x41, 0x64, 0x64, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x18, 0x0a,
	0x07, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07,
	0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x12, 0x34, 0x0a, 0x0a, 0x72, 0x6f, 0x62, 0x6f, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x0a, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x22, 0x35, 0x0a,
	0x09, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x73,
	0x67, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6d, 0x73, 0x67, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x6f, 0x49, 0x64, 0x22, 0x56, 0x0a, 0x14, 0x53, 0x65, 0x74, 0x4f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06,
	0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x22, 0x31, 0x0a, 0x17,
	0x53, 0x65, 0x74, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x71, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22,
	0x20, 0x0a, 0x08, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x22, 0x1a, 0x0a, 0x08, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x19, 0x0a,
	0x07, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x49, 0x64, 0x22, 0x4d, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x62, 0x6f, 0x72, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x62, 0x6f, 0x72, 0x6e, 0x22, 0x32, 0x0a, 0x0a, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x73, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x03, 0x49, 0x64, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x6c, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x6c, 0x73, 0x22, 0x33, 0x0a, 0x0b, 0x4d,
	0x6f, 0x6e, 0x67, 0x6f, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x49, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x49, 0x64, 0x73, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x6c, 0x73,
	0x22, 0x31, 0x0a, 0x09, 0x4d, 0x73, 0x67, 0x49, 0x64, 0x73, 0x52, 0x65, 0x71, 0x12, 0x10, 0x0a,
	0x03, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x03, 0x49, 0x64, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x63,
	0x6f, 0x6c, 0x73, 0x22, 0x38, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x73, 0x70, 0x12, 0x29, 0x0a, 0x05, 0x55, 0x73, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x13, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x55, 0x73, 0x65, 0x72, 0x73, 0x22, 0x33, 0x0a,
	0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x0e,
	0x0a, 0x02, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x44, 0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x44, 0x61,
	0x74, 0x61, 0x22, 0x22, 0x0a, 0x0c, 0x55, 0x73, 0x65, 0x72, 0x45, 0x78, 0x69, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x6f, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x4e, 0x6f, 0x49, 0x64, 0x22, 0x24, 0x0a, 0x0c, 0x55, 0x73, 0x65, 0x72, 0x45, 0x78,
	0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x45, 0x78, 0x69, 0x73, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x45, 0x78, 0x69, 0x73, 0x74, 0x22, 0xf6, 0x0c, 0x0a,
	0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x4e, 0x69, 0x63,
	0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4e, 0x69, 0x63,
	0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x6f, 0x49, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x6f, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x4d, 0x73, 0x67,
	0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x4d, 0x73, 0x67, 0x49, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x52, 0x63, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x52, 0x63, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x16, 0x0a, 0x06, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x44, 0x65, 0x6d,
	0x61, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0e, 0x44, 0x65, 0x6d, 0x61, 0x6e, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x26, 0x0a, 0x0e, 0x53, 0x75, 0x70, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x53, 0x75, 0x70, 0x70, 0x6c,
	0x69, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x47, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x47, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x12, 0x10, 0x0a, 0x03, 0x41, 0x67, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03,
	0x41, 0x67, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x46, 0x69, 0x6e,
	0x69, 0x73, 0x68, 0x65, 0x64, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x48,
	0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x48, 0x65, 0x69,
	0x67, 0x68, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x0e, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x28, 0x0a, 0x05, 0x41,
	0x6c, 0x62, 0x75, 0x6d, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6d, 0x67, 0x52, 0x05,
	0x41, 0x6c, 0x62, 0x75, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x55,
	0x72, 0x6c, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x55, 0x72, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41, 0x76, 0x61, 0x74,
	0x61, 0x72, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x41,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x41, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x41, 0x76, 0x61,
	0x74, 0x61, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x41,
	0x75, 0x64, 0x69, 0x6f, 0x55, 0x72, 0x6c, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x41,
	0x75, 0x64, 0x69, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x20, 0x0a, 0x0b, 0x41, 0x75, 0x64, 0x69, 0x6f,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x41, 0x75,
	0x64, 0x69, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x44, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x16, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x44, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x45, 0x64, 0x75, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x45, 0x64, 0x75, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x18,
	0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x43, 0x69, 0x74, 0x79, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x43,
	0x69, 0x74, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x41, 0x72, 0x65, 0x61, 0x18, 0x1a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x41, 0x72, 0x65, 0x61, 0x12, 0x1a, 0x0a, 0x08, 0x48, 0x6f, 0x6d, 0x65, 0x54,
	0x6f, 0x77, 0x6e, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x48, 0x6f, 0x6d, 0x65, 0x54,
	0x6f, 0x77, 0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x52, 0x65, 0x61, 0x6c, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x52, 0x65, 0x61, 0x6c, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x26, 0x0a, 0x0e, 0x52, 0x65, 0x61, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x52, 0x65, 0x61,
	0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x49,
	0x6e, 0x74, 0x72, 0x6f, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x49, 0x6e, 0x74, 0x72,
	0x6f, 0x12, 0x10, 0x0a, 0x03, 0x4c, 0x6f, 0x6e, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03,
	0x4c, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x4c, 0x61, 0x74, 0x18, 0x20, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x03, 0x4c, 0x61, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x49, 0x73, 0x4f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x18, 0x21, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x49, 0x73, 0x4f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x74, 0x61, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x22,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x09, 0x53, 0x74, 0x61, 0x72, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12,
	0x24, 0x0a, 0x0d, 0x4c, 0x61, 0x73, 0x74, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x23, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x4c, 0x61, 0x73, 0x74, 0x4c, 0x6f, 0x67, 0x69,
	0x6e, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x42, 0x6f, 0x72, 0x6e, 0x18, 0x24, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x42, 0x6f, 0x72, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x44, 0x69, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x25, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x44, 0x69, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x44, 0x61, 0x79, 0x73, 0x18, 0x26, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x44, 0x61, 0x79, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x4e, 0x65, 0x77,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x27, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x4e, 0x65,
	0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x0c, 0x46, 0x6f, 0x6c, 0x6c, 0x6f,
	0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x28, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x46,
	0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x42,
	0x6c, 0x61, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x29, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0b, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x0a,
	0x0b, 0x49, 0x73, 0x42, 0x69, 0x6e, 0x64, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x2a, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0b, 0x49, 0x73, 0x42, 0x69, 0x6e, 0x64, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x12, 0x1e, 0x0a, 0x0a, 0x46, 0x6f, 0x72, 0x62, 0x69,
	0x64, 0x44, 0x61, 0x79, 0x73, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x46, 0x6f, 0x72,
	0x62, 0x69, 0x64, 0x44, 0x61, 0x79, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x46, 0x6f, 0x72, 0x62, 0x69,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x46, 0x6f, 0x72,
	0x62, 0x69, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x69, 0x64, 0x18, 0x2e,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x55, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x55, 0x6e, 0x69,
	0x6f, 0x6e, 0x49, 0x64, 0x18, 0x2f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x55, 0x6e, 0x69, 0x6f,
	0x6e, 0x49, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x18, 0x30, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x52, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x72, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0e, 0x52, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x31, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1e, 0x0a, 0x0a, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x32, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x49, 0x6e, 0x76, 0x69, 0x74, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x33, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x49, 0x6d, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x18, 0x34, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x49, 0x6d, 0x67, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x53, 0x4d, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x18, 0x35, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x53, 0x4d, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x53, 0x4d, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18,
	0x36, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x53, 0x4d, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12,
	0x16, 0x0a, 0x06, 0x49, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x37, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x49, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x53, 0x75, 0x70, 0x65, 0x72,
	0x41, 0x64, 0x6d, 0x69, 0x6e, 0x18, 0x38, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x53, 0x75, 0x70,
	0x65, 0x72, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x49, 0x64, 0x43, 0x61, 0x72,
	0x64, 0x18, 0x39, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x49, 0x64, 0x43, 0x61, 0x72, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x18, 0x3a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x52, 0x6f, 0x62, 0x6f, 0x74, 0x22, 0x39, 0x0a, 0x07, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6d, 0x67,
	0x12, 0x16, 0x0a, 0x06, 0x49, 0x6d, 0x67, 0x55, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x49, 0x6d, 0x67, 0x55, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0x65, 0x0a, 0x0b, 0x50, 0x75, 0x6c, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12,
	0x14, 0x0a, 0x05, 0x51, 0x75, 0x65, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x43, 0x6f, 0x6c, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x04, 0x43, 0x6f, 0x6c, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12,
	0x16, 0x0a, 0x06, 0x49, 0x73, 0x52, 0x61, 0x6e, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x49, 0x73, 0x52, 0x61, 0x6e, 0x64, 0x22, 0x38, 0x0a, 0x0b, 0x50, 0x75, 0x6c, 0x6c, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x73, 0x70, 0x12, 0x29, 0x0a, 0x05, 0x55, 0x73, 0x65, 0x72, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73,
	0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x05, 0x55, 0x73, 0x65, 0x72,
	0x73, 0x22, 0x35, 0x0a, 0x0d, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x52,
	0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x6f, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x4e, 0x6f, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x49, 0x64, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x03, 0x49, 0x64, 0x73, 0x22, 0x98, 0x01, 0x0a, 0x0d, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x52, 0x73, 0x70, 0x12, 0x48, 0x0a, 0x0a, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x28,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x65, 0x6d, 0x61, 0x72, 0x6b, 0x52, 0x73, 0x70, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x6d,
	0x61, 0x72, 0x6b, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65,
	0x6d, 0x61, 0x72, 0x6b, 0x1a, 0x3d, 0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x6d, 0x61,
	0x72, 0x6b, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x26, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x52, 0x73, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x22, 0x24, 0x0a, 0x0c, 0x47,
	0x65, 0x74, 0x4f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x55,
	0x73, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x55, 0x73, 0x65, 0x72,
	0x73, 0x22, 0x9e, 0x01, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x6c, 0x62, 0x75,
	0x6d, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x05,
	0x61, 0x6c, 0x62, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x6c,
	0x62, 0x75, 0x6d, 0x52, 0x65, 0x71, 0x2e, 0x41, 0x6c, 0x62, 0x75, 0x6d, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x05, 0x61, 0x6c, 0x62, 0x75, 0x6d, 0x1a, 0x38, 0x0a, 0x0a, 0x41, 0x6c, 0x62, 0x75,
	0x6d, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x28, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x6c, 0x62, 0x75,
	0x6d, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x94, 0x01, 0x0a,
	0x0c, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x48, 0x65, 0x61, 0x64, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x6f, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x6d, 0x65, 0x69, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x69, 0x6d, 0x65, 0x69, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x02, 0x52, 0x03, 0x6c, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x6c, 0x61, 0x74, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x02, 0x52, 0x03, 0x6c, 0x61, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x22, 0x0a, 0x0c, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x48, 0x65, 0x61, 0x64,
	0x52, 0x73, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x89, 0x01, 0x0a, 0x0d, 0x52, 0x65, 0x76, 0x69,
	0x65, 0x77, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x12,
	0x1a, 0x0a, 0x08, 0x72, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x72, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x72,
	0x65, 0x71, 0x54, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x65,
	0x71, 0x54, 0x65, 0x78, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x61, 0x77, 0x54, 0x65, 0x78, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72, 0x61, 0x77, 0x54, 0x65, 0x78, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x6f, 0x49, 0x64, 0x22, 0x27, 0x0a, 0x0d, 0x52, 0x65, 0x76, 0x69, 0x65, 0x77, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x1a, 0x0a, 0x08,
	0x49, 0x50, 0x4c, 0x6f, 0x63, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x22, 0x26, 0x0a, 0x08, 0x49, 0x50, 0x4c, 0x6f,
	0x63, 0x52, 0x73, 0x70, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x6e, 0x63, 0x65,
	0x22, 0x41, 0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74,
	0x54, 0x61, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x61, 0x67, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x74, 0x61, 0x67, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x61, 0x67,
	0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x74, 0x61, 0x67, 0x4e,
	0x61, 0x6d, 0x65, 0x22, 0x28, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x6f, 0x49,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x6f, 0x49, 0x64, 0x22, 0x4e, 0x0a,
	0x12, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74,
	0x52, 0x73, 0x70, 0x12, 0x38, 0x0a, 0x09, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x62, 0x61,
	0x73, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x54,
	0x61, 0x67, 0x52, 0x09, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x73, 0x22, 0x2b, 0x0a,
	0x13, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74,
	0x73, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x6f, 0x49, 0x64, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f, 0x49, 0x64, 0x73, 0x22, 0x2f, 0x0a, 0x0f, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x73, 0x56, 0x4f, 0x12, 0x1c, 0x0a,
	0x09, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x09, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x73, 0x22, 0xa4, 0x01, 0x0a, 0x13,
	0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x73,
	0x52, 0x73, 0x70, 0x12, 0x39, 0x0a, 0x03, 0x72, 0x65, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x47, 0x65, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x73, 0x52, 0x73, 0x70,
	0x2e, 0x52, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x03, 0x72, 0x65, 0x74, 0x1a, 0x52,
	0x0a, 0x08, 0x52, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x30, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x65, 0x73, 0x74, 0x73, 0x56, 0x4f, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x2a, 0x46, 0x0a, 0x0a, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x0c, 0x0a, 0x08, 0x4e, 0x6f, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x10, 0x00, 0x12, 0x0c,
	0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x6c, 0x66, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09,
	0x41, 0x64, 0x6d, 0x69, 0x6e, 0x48, 0x65, 0x6c, 0x70, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x41,
	0x64, 0x6d, 0x69, 0x6e, 0x41, 0x75, 0x74, 0x68, 0x10, 0x03, 0x2a, 0x4c, 0x0a, 0x0c, 0x4d, 0x6f,
	0x64, 0x69, 0x66, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0e, 0x0a, 0x0a, 0x56, 0x6f,
	0x69, 0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x50, 0x61,
	0x73, 0x73, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x10, 0x02, 0x12,
	0x0a, 0x0a, 0x06, 0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x54,
	0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x10, 0x04, 0x2a, 0x8e, 0x01, 0x0a, 0x0b, 0x46, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x0e, 0x0a, 0x0a, 0x4e, 0x6f, 0x6e, 0x65,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x50, 0x61, 0x73, 0x73, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x53, 0x65, 0x6c, 0x66, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x10, 0x02, 0x12, 0x11,
	0x0a, 0x0d, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x10,
	0x03, 0x12, 0x10, 0x0a, 0x0c, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x65, 0x6a, 0x65, 0x63,
	0x74, 0x10, 0x04, 0x12, 0x13, 0x0a, 0x0f, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x41, 0x64, 0x6d,
	0x69, 0x6e, 0x41, 0x75, 0x74, 0x68, 0x10, 0x05, 0x12, 0x0f, 0x0a, 0x0b, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x10, 0x06, 0x2a, 0x42, 0x0a, 0x09, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x6f, 0x6e, 0x65, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f,
	0x6e, 0x66, 0x6c, 0x69, 0x63, 0x74, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x10, 0x02, 0x2a, 0x6a, 0x0a,
	0x08, 0x50, 0x75, 0x73, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x6e, 0x6b,
	0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74,
	0x65, 0x4d, 0x73, 0x67, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74,
	0x65, 0x47, 0x69, 0x66, 0x74, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x48, 0x65, 0x61, 0x72, 0x74,
	0x47, 0x69, 0x66, 0x74, 0x10, 0x03, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x63, 0x63, 0x6f, 0x73, 0x74,
	0x47, 0x69, 0x66, 0x74, 0x10, 0x04, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x6f, 0x6c, 0x6c, 0x6f, 0x77,
	0x65, 0x72, 0x73, 0x4f, 0x70, 0x65, 0x6e, 0x10, 0x05, 0x2a, 0x35, 0x0a, 0x0a, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x6f, 0x6e, 0x65, 0x10,
	0x00, 0x12, 0x0c, 0x0a, 0x08, 0x4f, 0x70, 0x65, 0x6e, 0x4c, 0x69, 0x76, 0x65, 0x10, 0x01, 0x12,
	0x0f, 0x0a, 0x0b, 0x43, 0x68, 0x61, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x10, 0x02,
	0x32, 0xcf, 0x08, 0x0a, 0x04, 0x55, 0x73, 0x65, 0x72, 0x12, 0x35, 0x0a, 0x09, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x54, 0x6f, 0x49, 0x44, 0x12, 0x13, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x62, 0x61,
	0x73, 0x65, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73,
	0x12, 0x3f, 0x0a, 0x0b, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x42, 0x79, 0x49, 0x6d, 0x65, 0x69, 0x12,
	0x17, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x56, 0x61, 0x6c, 0x69,
	0x64, 0x48, 0x65, 0x61, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x62, 0x61, 0x73, 0x65, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x48, 0x65, 0x61, 0x64, 0x52, 0x73,
	0x70, 0x12, 0x3d, 0x0a, 0x09, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x42, 0x79, 0x49, 0x50, 0x12, 0x17,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64,
	0x48, 0x65, 0x61, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x62,
	0x61, 0x73, 0x65, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x48, 0x65, 0x61, 0x64, 0x52, 0x73, 0x70,
	0x12, 0x49, 0x0a, 0x0d, 0x53, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x77, 0x69, 0x74, 0x63,
	0x68, 0x12, 0x1b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x53, 0x65,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x1b,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x53, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x73, 0x70, 0x12, 0x49, 0x0a, 0x0d, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x1b, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x53, 0x77, 0x69,
	0x74, 0x63, 0x68, 0x52, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x0b, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x17, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73,
	0x65, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x48, 0x65, 0x61, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x17,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64,
	0x48, 0x65, 0x61, 0x64, 0x52, 0x73, 0x70, 0x12, 0x44, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x55, 0x73,
	0x65, 0x72, 0x73, 0x42, 0x79, 0x4d, 0x6f, 0x6e, 0x67, 0x6f, 0x49, 0x64, 0x73, 0x12, 0x16, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x67, 0x6f, 0x49,
	0x64, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73,
	0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x3e, 0x0a,
	0x0d, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x42, 0x79, 0x49, 0x64, 0x73, 0x12, 0x15,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73,
	0x65, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73, 0x70, 0x12, 0x39, 0x0a,
	0x0d, 0x47, 0x65, 0x74, 0x49, 0x50, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x13,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x49, 0x50, 0x4c, 0x6f, 0x63,
	0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e,
	0x49, 0x50, 0x4c, 0x6f, 0x63, 0x52, 0x73, 0x70, 0x12, 0x3f, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x1a, 0x13, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x49, 0x0a, 0x11, 0x41, 0x70, 0x70,
	0x6c, 0x79, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x1f,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x79,
	0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a,
	0x13, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x52, 0x73, 0x70, 0x12, 0x5b, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66,
	0x79, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66,
	0x79, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a, 0x21,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f,
	0x64, 0x69, 0x66, 0x79, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x73,
	0x70, 0x12, 0x5b, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d, 0x6f,
	0x62, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x21, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x62, 0x61, 0x73, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d, 0x6f,
	0x62, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66,
	0x79, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x73, 0x70, 0x12, 0x55,
	0x0a, 0x11, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x64, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x6f, 0x62,
	0x69, 0x6c, 0x65, 0x12, 0x1f, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e,
	0x47, 0x65, 0x74, 0x41, 0x6e, 0x64, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x6f, 0x62, 0x69, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x1a, 0x1f, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x6e, 0x64, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x6f, 0x62, 0x69,
	0x6c, 0x65, 0x52, 0x73, 0x70, 0x12, 0x5b, 0x0a, 0x13, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74,
	0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x12, 0x21, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74,
	0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x1a,
	0x21, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x6f, 0x75, 0x74, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x52,
	0x73, 0x70, 0x42, 0x07, 0x5a, 0x05, 0x2e, 0x3b, 0x61, 0x70, 0x69, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_user_proto_rawDescOnce sync.Once
	file_user_proto_rawDescData = file_user_proto_rawDesc
)

func file_user_proto_rawDescGZIP() []byte {
	file_user_proto_rawDescOnce.Do(func() {
		file_user_proto_rawDescData = protoimpl.X.CompressGZIP(file_user_proto_rawDescData)
	})
	return file_user_proto_rawDescData
}

var file_user_proto_enumTypes = make([]protoimpl.EnumInfo, 6)
var file_user_proto_msgTypes = make([]protoimpl.MessageInfo, 71)
var file_user_proto_goTypes = []interface{}{
	(ModifyType)(0),                 // 0: user.base.ModifyType
	(ModifyStatus)(0),               // 1: user.base.ModifyStatus
	(FilterParam)(0),                // 2: user.base.FilterParam
	(CheckType)(0),                  // 3: user.base.CheckType
	(PushType)(0),                   // 4: user.base.PushType
	(TargetType)(0),                 // 5: user.base.TargetType
	(*TimeoutModifyMobileReq)(nil),  // 6: user.base.TimeoutModifyMobileReq
	(*TimeoutModifyMobileRsp)(nil),  // 7: user.base.TimeoutModifyMobileRsp
	(*GetAndCheckMobileReq)(nil),    // 8: user.base.GetAndCheckMobileReq
	(*GetAndCheckMobileRsp)(nil),    // 9: user.base.GetAndCheckMobileRsp
	(*MobileUserInfo)(nil),          // 10: user.base.MobileUserInfo
	(*GetModifyMobileListReq)(nil),  // 11: user.base.GetModifyMobileListReq
	(*GetModifyMobileListRsp)(nil),  // 12: user.base.GetModifyMobileListRsp
	(*ModifyMobileData)(nil),        // 13: user.base.ModifyMobileData
	(*GetModifyMobileInfoReq)(nil),  // 14: user.base.GetModifyMobileInfoReq
	(*GetModifyMobileInfoRsp)(nil),  // 15: user.base.GetModifyMobileInfoRsp
	(*ApplyModifyMobileReq)(nil),    // 16: user.base.ApplyModifyMobileReq
	(*SetPushStatusReq)(nil),        // 17: user.base.SetPushStatusReq
	(*UnBindPushRelationReq)(nil),   // 18: user.base.UnBindPushRelationReq
	(*BindPushRelationReq)(nil),     // 19: user.base.BindPushRelationReq
	(*PushByAliasReq)(nil),          // 20: user.base.PushByAliasReq
	(*PushAttach)(nil),              // 21: user.base.PushAttach
	(*EmptyRsp)(nil),                // 22: user.base.EmptyRsp
	(*GetUserSwitchReq)(nil),        // 23: user.base.GetUserSwitchReq
	(*GetUserSwitchRsp)(nil),        // 24: user.base.GetUserSwitchRsp
	(*SwitchInfo)(nil),              // 25: user.base.SwitchInfo
	(*SetUserSwitchReq)(nil),        // 26: user.base.SetUserSwitchReq
	(*SetUserSwitchRsp)(nil),        // 27: user.base.SetUserSwitchRsp
	(*GetUserFansListReq)(nil),      // 28: user.base.GetUserFansListReq
	(*GetUserFansListRsp)(nil),      // 29: user.base.GetUserFansListRsp
	(*SessionInfo)(nil),             // 30: user.base.SessionInfo
	(*OnlineNumReq)(nil),            // 31: user.base.OnlineNumReq
	(*OnlineNumRsp)(nil),            // 32: user.base.OnlineNumRsp
	(*RobotAddReq)(nil),             // 33: user.base.RobotAddReq
	(*RobotConfig)(nil),             // 34: user.base.robotConfig
	(*Robot)(nil),                   // 35: user.base.robot
	(*RobotAddRsp)(nil),             // 36: user.base.RobotAddRsp
	(*RobotInfo)(nil),               // 37: user.base.robotInfo
	(*SetOnlineUserInfoReq)(nil),    // 38: user.base.SetOnlineUserInfoReq
	(*SetOnlineUserInfoReqRsp)(nil), // 39: user.base.SetOnlineUserInfoReqRsp
	(*TokenReq)(nil),                // 40: user.base.TokenReq
	(*TokenRes)(nil),                // 41: user.base.TokenRes
	(*UserReq)(nil),                 // 42: user.base.UserReq
	(*UserInfoRes)(nil),             // 43: user.base.UserInfoRes
	(*UserIdsReq)(nil),              // 44: user.base.UserIdsReq
	(*MongoIdsReq)(nil),             // 45: user.base.MongoIdsReq
	(*MsgIdsReq)(nil),               // 46: user.base.MsgIdsReq
	(*UserInfoRsp)(nil),             // 47: user.base.UserInfoRsp
	(*UpdateUserReq)(nil),           // 48: user.base.UpdateUserReq
	(*UserExistReq)(nil),            // 49: user.base.UserExistReq
	(*UserExistRsp)(nil),            // 50: user.base.UserExistRsp
	(*UserInfo)(nil),                // 51: user.base.UserInfo
	(*UserImg)(nil),                 // 52: user.base.UserImg
	(*PullUserReq)(nil),             // 53: user.base.PullUserReq
	(*PullUserRsp)(nil),             // 54: user.base.PullUserRsp
	(*UserRemarkReq)(nil),           // 55: user.base.UserRemarkReq
	(*UserRemarkRsp)(nil),           // 56: user.base.UserRemarkRsp
	(*GetOnlineRsq)(nil),            // 57: user.base.GetOnlineRsq
	(*GetOnlineRsp)(nil),            // 58: user.base.GetOnlineRsp
	(*UpdateAlbumReq)(nil),          // 59: user.base.UpdateAlbumReq
	(*UpdateAlbumRsp)(nil),          // 60: user.base.UpdateAlbumRsp
	(*ValidHeadReq)(nil),            // 61: user.base.ValidHeadReq
	(*ValidHeadRsp)(nil),            // 62: user.base.ValidHeadRsp
	(*ReviewInfoReq)(nil),           // 63: user.base.ReviewInfoReq
	(*ReviewInfoRsp)(nil),           // 64: user.base.ReviewInfoRsp
	(*IPLocReq)(nil),                // 65: user.base.IPLocReq
	(*IPLocRsp)(nil),                // 66: user.base.IPLocRsp
	(*UserInterestTag)(nil),         // 67: user.base.UserInterestTag
	(*GetUserInterestReq)(nil),      // 68: user.base.GetUserInterestReq
	(*GetUserInterestRsp)(nil),      // 69: user.base.GetUserInterestRsp
	(*GetUserInterestsReq)(nil),     // 70: user.base.GetUserInterestsReq
	(*UserInterestsVO)(nil),         // 71: user.base.UserInterestsVO
	(*GetUserInterestsRsp)(nil),     // 72: user.base.GetUserInterestsRsp
	nil,                             // 73: user.base.TimeoutModifyMobileRsp.TMapEntry
	nil,                             // 74: user.base.UserRemarkRsp.UserRemarkEntry
	nil,                             // 75: user.base.UpdateAlbumReq.AlbumEntry
	nil,                             // 76: user.base.GetUserInterestsRsp.RetEntry
}
var file_user_proto_depIdxs = []int32{
	73, // 0: user.base.TimeoutModifyMobileRsp.tMap:type_name -> user.base.TimeoutModifyMobileRsp.TMapEntry
	3,  // 1: user.base.GetAndCheckMobileReq.type:type_name -> user.base.CheckType
	10, // 2: user.base.GetAndCheckMobileRsp.from:type_name -> user.base.MobileUserInfo
	10, // 3: user.base.GetAndCheckMobileRsp.to:type_name -> user.base.MobileUserInfo
	2,  // 4: user.base.GetModifyMobileListReq.filterParam:type_name -> user.base.FilterParam
	13, // 5: user.base.GetModifyMobileListRsp.list:type_name -> user.base.ModifyMobileData
	0,  // 6: user.base.ApplyModifyMobileReq.modifyType:type_name -> user.base.ModifyType
	1,  // 7: user.base.ApplyModifyMobileReq.modifyStatus:type_name -> user.base.ModifyStatus
	5,  // 8: user.base.PushByAliasReq.targetType:type_name -> user.base.TargetType
	4,  // 9: user.base.PushByAliasReq.pushType:type_name -> user.base.PushType
	21, // 10: user.base.PushByAliasReq.attach:type_name -> user.base.PushAttach
	25, // 11: user.base.GetUserSwitchRsp.list:type_name -> user.base.SwitchInfo
	30, // 12: user.base.GetUserFansListRsp.list:type_name -> user.base.SessionInfo
	35, // 13: user.base.RobotAddReq.robot:type_name -> user.base.robot
	34, // 14: user.base.RobotAddReq.robotConfig:type_name -> user.base.robotConfig
	37, // 15: user.base.RobotAddRsp.robotInfos:type_name -> user.base.robotInfo
	51, // 16: user.base.UserInfoRsp.Users:type_name -> user.base.UserInfo
	52, // 17: user.base.UserInfo.Album:type_name -> user.base.UserImg
	51, // 18: user.base.PullUserRsp.Users:type_name -> user.base.UserInfo
	74, // 19: user.base.UserRemarkRsp.UserRemark:type_name -> user.base.UserRemarkRsp.UserRemarkEntry
	75, // 20: user.base.UpdateAlbumReq.album:type_name -> user.base.UpdateAlbumReq.AlbumEntry
	67, // 21: user.base.GetUserInterestRsp.interests:type_name -> user.base.UserInterestTag
	76, // 22: user.base.GetUserInterestsRsp.ret:type_name -> user.base.GetUserInterestsRsp.RetEntry
	71, // 23: user.base.GetUserInterestsRsp.RetEntry.value:type_name -> user.base.UserInterestsVO
	40, // 24: user.base.User.TokenToID:input_type -> user.base.TokenReq
	61, // 25: user.base.User.ValidByImei:input_type -> user.base.ValidHeadReq
	61, // 26: user.base.User.ValidByIP:input_type -> user.base.ValidHeadReq
	26, // 27: user.base.User.SetUserSwitch:input_type -> user.base.SetUserSwitchReq
	23, // 28: user.base.User.GetUserSwitch:input_type -> user.base.GetUserSwitchReq
	61, // 29: user.base.User.ValidHeader:input_type -> user.base.ValidHeadReq
	45, // 30: user.base.User.GetUsersByMongoIds:input_type -> user.base.MongoIdsReq
	44, // 31: user.base.User.GetUsersByIds:input_type -> user.base.UserIdsReq
	65, // 32: user.base.User.GetIPLocation:input_type -> user.base.IPLocReq
	48, // 33: user.base.User.UpdateUserInfo:input_type -> user.base.UpdateUserReq
	16, // 34: user.base.User.ApplyModifyMobile:input_type -> user.base.ApplyModifyMobileReq
	14, // 35: user.base.User.GetModifyMobileInfo:input_type -> user.base.GetModifyMobileInfoReq
	11, // 36: user.base.User.GetModifyMobileList:input_type -> user.base.GetModifyMobileListReq
	8,  // 37: user.base.User.GetAndCheckMobile:input_type -> user.base.GetAndCheckMobileReq
	6,  // 38: user.base.User.TimeoutModifyMobile:input_type -> user.base.TimeoutModifyMobileReq
	41, // 39: user.base.User.TokenToID:output_type -> user.base.TokenRes
	62, // 40: user.base.User.ValidByImei:output_type -> user.base.ValidHeadRsp
	62, // 41: user.base.User.ValidByIP:output_type -> user.base.ValidHeadRsp
	27, // 42: user.base.User.SetUserSwitch:output_type -> user.base.SetUserSwitchRsp
	24, // 43: user.base.User.GetUserSwitch:output_type -> user.base.GetUserSwitchRsp
	62, // 44: user.base.User.ValidHeader:output_type -> user.base.ValidHeadRsp
	47, // 45: user.base.User.GetUsersByMongoIds:output_type -> user.base.UserInfoRsp
	47, // 46: user.base.User.GetUsersByIds:output_type -> user.base.UserInfoRsp
	66, // 47: user.base.User.GetIPLocation:output_type -> user.base.IPLocRsp
	51, // 48: user.base.User.UpdateUserInfo:output_type -> user.base.UserInfo
	22, // 49: user.base.User.ApplyModifyMobile:output_type -> user.base.EmptyRsp
	15, // 50: user.base.User.GetModifyMobileInfo:output_type -> user.base.GetModifyMobileInfoRsp
	12, // 51: user.base.User.GetModifyMobileList:output_type -> user.base.GetModifyMobileListRsp
	9,  // 52: user.base.User.GetAndCheckMobile:output_type -> user.base.GetAndCheckMobileRsp
	7,  // 53: user.base.User.TimeoutModifyMobile:output_type -> user.base.TimeoutModifyMobileRsp
	39, // [39:54] is the sub-list for method output_type
	24, // [24:39] is the sub-list for method input_type
	24, // [24:24] is the sub-list for extension type_name
	24, // [24:24] is the sub-list for extension extendee
	0,  // [0:24] is the sub-list for field type_name
}

func init() { file_user_proto_init() }
func file_user_proto_init() {
	if File_user_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_user_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeoutModifyMobileReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeoutModifyMobileRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAndCheckMobileReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAndCheckMobileRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MobileUserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetModifyMobileListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetModifyMobileListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyMobileData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetModifyMobileInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetModifyMobileInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplyModifyMobileReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetPushStatusReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UnBindPushRelationReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BindPushRelationReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushByAliasReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PushAttach); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmptyRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserSwitchReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserSwitchRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SwitchInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetUserSwitchReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetUserSwitchRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserFansListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserFansListRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SessionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OnlineNumReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OnlineNumRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RobotAddReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RobotConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Robot); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RobotAddRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RobotInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetOnlineUserInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetOnlineUserInfoReqRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TokenReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TokenRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfoRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserIdsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MongoIdsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MsgIdsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUserReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserExistReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserExistRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserImg); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PullUserReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PullUserRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserRemarkReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserRemarkRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOnlineRsq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetOnlineRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAlbumReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateAlbumRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidHeadReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[56].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidHeadRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[57].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReviewInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[58].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReviewInfoRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[59].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IPLocReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[60].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IPLocRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[61].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInterestTag); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[62].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserInterestReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[63].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserInterestRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[64].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserInterestsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[65].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInterestsVO); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_proto_msgTypes[66].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetUserInterestsRsp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_user_proto_rawDesc,
			NumEnums:      6,
			NumMessages:   71,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_user_proto_goTypes,
		DependencyIndexes: file_user_proto_depIdxs,
		EnumInfos:         file_user_proto_enumTypes,
		MessageInfos:      file_user_proto_msgTypes,
	}.Build()
	File_user_proto = out.File
	file_user_proto_rawDesc = nil
	file_user_proto_goTypes = nil
	file_user_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// UserClient is the client API for User service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type UserClient interface {
	TokenToID(ctx context.Context, in *TokenReq, opts ...grpc.CallOption) (*TokenRes, error)
	ValidByImei(ctx context.Context, in *ValidHeadReq, opts ...grpc.CallOption) (*ValidHeadRsp, error)
	ValidByIP(ctx context.Context, in *ValidHeadReq, opts ...grpc.CallOption) (*ValidHeadRsp, error)
	// 设置用户开关
	SetUserSwitch(ctx context.Context, in *SetUserSwitchReq, opts ...grpc.CallOption) (*SetUserSwitchRsp, error)
	// 获取用户开关状态
	GetUserSwitch(ctx context.Context, in *GetUserSwitchReq, opts ...grpc.CallOption) (*GetUserSwitchRsp, error)
	ValidHeader(ctx context.Context, in *ValidHeadReq, opts ...grpc.CallOption) (*ValidHeadRsp, error)
	GetUsersByMongoIds(ctx context.Context, in *MongoIdsReq, opts ...grpc.CallOption) (*UserInfoRsp, error)
	GetUsersByIds(ctx context.Context, in *UserIdsReq, opts ...grpc.CallOption) (*UserInfoRsp, error)
	GetIPLocation(ctx context.Context, in *IPLocReq, opts ...grpc.CallOption) (*IPLocRsp, error)
	UpdateUserInfo(ctx context.Context, in *UpdateUserReq, opts ...grpc.CallOption) (*UserInfo, error)
	ApplyModifyMobile(ctx context.Context, in *ApplyModifyMobileReq, opts ...grpc.CallOption) (*EmptyRsp, error)
	GetModifyMobileInfo(ctx context.Context, in *GetModifyMobileInfoReq, opts ...grpc.CallOption) (*GetModifyMobileInfoRsp, error)
	GetModifyMobileList(ctx context.Context, in *GetModifyMobileListReq, opts ...grpc.CallOption) (*GetModifyMobileListRsp, error)
	GetAndCheckMobile(ctx context.Context, in *GetAndCheckMobileReq, opts ...grpc.CallOption) (*GetAndCheckMobileRsp, error)
	TimeoutModifyMobile(ctx context.Context, in *TimeoutModifyMobileReq, opts ...grpc.CallOption) (*TimeoutModifyMobileRsp, error)
}

type userClient struct {
	cc grpc.ClientConnInterface
}

func NewUserClient(cc grpc.ClientConnInterface) UserClient {
	return &userClient{cc}
}

func (c *userClient) TokenToID(ctx context.Context, in *TokenReq, opts ...grpc.CallOption) (*TokenRes, error) {
	out := new(TokenRes)
	err := c.cc.Invoke(ctx, "/user.base.User/TokenToID", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) ValidByImei(ctx context.Context, in *ValidHeadReq, opts ...grpc.CallOption) (*ValidHeadRsp, error) {
	out := new(ValidHeadRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/ValidByImei", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) ValidByIP(ctx context.Context, in *ValidHeadReq, opts ...grpc.CallOption) (*ValidHeadRsp, error) {
	out := new(ValidHeadRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/ValidByIP", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) SetUserSwitch(ctx context.Context, in *SetUserSwitchReq, opts ...grpc.CallOption) (*SetUserSwitchRsp, error) {
	out := new(SetUserSwitchRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/SetUserSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) GetUserSwitch(ctx context.Context, in *GetUserSwitchReq, opts ...grpc.CallOption) (*GetUserSwitchRsp, error) {
	out := new(GetUserSwitchRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/GetUserSwitch", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) ValidHeader(ctx context.Context, in *ValidHeadReq, opts ...grpc.CallOption) (*ValidHeadRsp, error) {
	out := new(ValidHeadRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/ValidHeader", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) GetUsersByMongoIds(ctx context.Context, in *MongoIdsReq, opts ...grpc.CallOption) (*UserInfoRsp, error) {
	out := new(UserInfoRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/GetUsersByMongoIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) GetUsersByIds(ctx context.Context, in *UserIdsReq, opts ...grpc.CallOption) (*UserInfoRsp, error) {
	out := new(UserInfoRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/GetUsersByIds", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) GetIPLocation(ctx context.Context, in *IPLocReq, opts ...grpc.CallOption) (*IPLocRsp, error) {
	out := new(IPLocRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/GetIPLocation", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) UpdateUserInfo(ctx context.Context, in *UpdateUserReq, opts ...grpc.CallOption) (*UserInfo, error) {
	out := new(UserInfo)
	err := c.cc.Invoke(ctx, "/user.base.User/UpdateUserInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) ApplyModifyMobile(ctx context.Context, in *ApplyModifyMobileReq, opts ...grpc.CallOption) (*EmptyRsp, error) {
	out := new(EmptyRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/ApplyModifyMobile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) GetModifyMobileInfo(ctx context.Context, in *GetModifyMobileInfoReq, opts ...grpc.CallOption) (*GetModifyMobileInfoRsp, error) {
	out := new(GetModifyMobileInfoRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/GetModifyMobileInfo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) GetModifyMobileList(ctx context.Context, in *GetModifyMobileListReq, opts ...grpc.CallOption) (*GetModifyMobileListRsp, error) {
	out := new(GetModifyMobileListRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/GetModifyMobileList", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) GetAndCheckMobile(ctx context.Context, in *GetAndCheckMobileReq, opts ...grpc.CallOption) (*GetAndCheckMobileRsp, error) {
	out := new(GetAndCheckMobileRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/GetAndCheckMobile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userClient) TimeoutModifyMobile(ctx context.Context, in *TimeoutModifyMobileReq, opts ...grpc.CallOption) (*TimeoutModifyMobileRsp, error) {
	out := new(TimeoutModifyMobileRsp)
	err := c.cc.Invoke(ctx, "/user.base.User/TimeoutModifyMobile", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserServer is the server API for User service.
type UserServer interface {
	TokenToID(context.Context, *TokenReq) (*TokenRes, error)
	ValidByImei(context.Context, *ValidHeadReq) (*ValidHeadRsp, error)
	ValidByIP(context.Context, *ValidHeadReq) (*ValidHeadRsp, error)
	// 设置用户开关
	SetUserSwitch(context.Context, *SetUserSwitchReq) (*SetUserSwitchRsp, error)
	// 获取用户开关状态
	GetUserSwitch(context.Context, *GetUserSwitchReq) (*GetUserSwitchRsp, error)
	ValidHeader(context.Context, *ValidHeadReq) (*ValidHeadRsp, error)
	GetUsersByMongoIds(context.Context, *MongoIdsReq) (*UserInfoRsp, error)
	GetUsersByIds(context.Context, *UserIdsReq) (*UserInfoRsp, error)
	GetIPLocation(context.Context, *IPLocReq) (*IPLocRsp, error)
	UpdateUserInfo(context.Context, *UpdateUserReq) (*UserInfo, error)
	ApplyModifyMobile(context.Context, *ApplyModifyMobileReq) (*EmptyRsp, error)
	GetModifyMobileInfo(context.Context, *GetModifyMobileInfoReq) (*GetModifyMobileInfoRsp, error)
	GetModifyMobileList(context.Context, *GetModifyMobileListReq) (*GetModifyMobileListRsp, error)
	GetAndCheckMobile(context.Context, *GetAndCheckMobileReq) (*GetAndCheckMobileRsp, error)
	TimeoutModifyMobile(context.Context, *TimeoutModifyMobileReq) (*TimeoutModifyMobileRsp, error)
}

// UnimplementedUserServer can be embedded to have forward compatible implementations.
type UnimplementedUserServer struct {
}

func (*UnimplementedUserServer) TokenToID(context.Context, *TokenReq) (*TokenRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TokenToID not implemented")
}
func (*UnimplementedUserServer) ValidByImei(context.Context, *ValidHeadReq) (*ValidHeadRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidByImei not implemented")
}
func (*UnimplementedUserServer) ValidByIP(context.Context, *ValidHeadReq) (*ValidHeadRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidByIP not implemented")
}
func (*UnimplementedUserServer) SetUserSwitch(context.Context, *SetUserSwitchReq) (*SetUserSwitchRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetUserSwitch not implemented")
}
func (*UnimplementedUserServer) GetUserSwitch(context.Context, *GetUserSwitchReq) (*GetUserSwitchRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserSwitch not implemented")
}
func (*UnimplementedUserServer) ValidHeader(context.Context, *ValidHeadReq) (*ValidHeadRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidHeader not implemented")
}
func (*UnimplementedUserServer) GetUsersByMongoIds(context.Context, *MongoIdsReq) (*UserInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUsersByMongoIds not implemented")
}
func (*UnimplementedUserServer) GetUsersByIds(context.Context, *UserIdsReq) (*UserInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUsersByIds not implemented")
}
func (*UnimplementedUserServer) GetIPLocation(context.Context, *IPLocReq) (*IPLocRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIPLocation not implemented")
}
func (*UnimplementedUserServer) UpdateUserInfo(context.Context, *UpdateUserReq) (*UserInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserInfo not implemented")
}
func (*UnimplementedUserServer) ApplyModifyMobile(context.Context, *ApplyModifyMobileReq) (*EmptyRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ApplyModifyMobile not implemented")
}
func (*UnimplementedUserServer) GetModifyMobileInfo(context.Context, *GetModifyMobileInfoReq) (*GetModifyMobileInfoRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetModifyMobileInfo not implemented")
}
func (*UnimplementedUserServer) GetModifyMobileList(context.Context, *GetModifyMobileListReq) (*GetModifyMobileListRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetModifyMobileList not implemented")
}
func (*UnimplementedUserServer) GetAndCheckMobile(context.Context, *GetAndCheckMobileReq) (*GetAndCheckMobileRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAndCheckMobile not implemented")
}
func (*UnimplementedUserServer) TimeoutModifyMobile(context.Context, *TimeoutModifyMobileReq) (*TimeoutModifyMobileRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TimeoutModifyMobile not implemented")
}

func RegisterUserServer(s *grpc.Server, srv UserServer) {
	s.RegisterService(&_User_serviceDesc, srv)
}

func _User_TokenToID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).TokenToID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/TokenToID",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).TokenToID(ctx, req.(*TokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_ValidByImei_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidHeadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).ValidByImei(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/ValidByImei",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).ValidByImei(ctx, req.(*ValidHeadReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_ValidByIP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidHeadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).ValidByIP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/ValidByIP",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).ValidByIP(ctx, req.(*ValidHeadReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_SetUserSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetUserSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).SetUserSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/SetUserSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).SetUserSwitch(ctx, req.(*SetUserSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_GetUserSwitch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserSwitchReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).GetUserSwitch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/GetUserSwitch",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).GetUserSwitch(ctx, req.(*GetUserSwitchReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_ValidHeader_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidHeadReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).ValidHeader(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/ValidHeader",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).ValidHeader(ctx, req.(*ValidHeadReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_GetUsersByMongoIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MongoIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).GetUsersByMongoIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/GetUsersByMongoIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).GetUsersByMongoIds(ctx, req.(*MongoIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_GetUsersByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).GetUsersByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/GetUsersByIds",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).GetUsersByIds(ctx, req.(*UserIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_GetIPLocation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IPLocReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).GetIPLocation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/GetIPLocation",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).GetIPLocation(ctx, req.(*IPLocReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_UpdateUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).UpdateUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/UpdateUserInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).UpdateUserInfo(ctx, req.(*UpdateUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_ApplyModifyMobile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplyModifyMobileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).ApplyModifyMobile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/ApplyModifyMobile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).ApplyModifyMobile(ctx, req.(*ApplyModifyMobileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_GetModifyMobileInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetModifyMobileInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).GetModifyMobileInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/GetModifyMobileInfo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).GetModifyMobileInfo(ctx, req.(*GetModifyMobileInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_GetModifyMobileList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetModifyMobileListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).GetModifyMobileList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/GetModifyMobileList",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).GetModifyMobileList(ctx, req.(*GetModifyMobileListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_GetAndCheckMobile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAndCheckMobileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).GetAndCheckMobile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/GetAndCheckMobile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).GetAndCheckMobile(ctx, req.(*GetAndCheckMobileReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _User_TimeoutModifyMobile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TimeoutModifyMobileReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).TimeoutModifyMobile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/user.base.User/TimeoutModifyMobile",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).TimeoutModifyMobile(ctx, req.(*TimeoutModifyMobileReq))
	}
	return interceptor(ctx, in, info, handler)
}

var _User_serviceDesc = grpc.ServiceDesc{
	ServiceName: "user.base.User",
	HandlerType: (*UserServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "TokenToID",
			Handler:    _User_TokenToID_Handler,
		},
		{
			MethodName: "ValidByImei",
			Handler:    _User_ValidByImei_Handler,
		},
		{
			MethodName: "ValidByIP",
			Handler:    _User_ValidByIP_Handler,
		},
		{
			MethodName: "SetUserSwitch",
			Handler:    _User_SetUserSwitch_Handler,
		},
		{
			MethodName: "GetUserSwitch",
			Handler:    _User_GetUserSwitch_Handler,
		},
		{
			MethodName: "ValidHeader",
			Handler:    _User_ValidHeader_Handler,
		},
		{
			MethodName: "GetUsersByMongoIds",
			Handler:    _User_GetUsersByMongoIds_Handler,
		},
		{
			MethodName: "GetUsersByIds",
			Handler:    _User_GetUsersByIds_Handler,
		},
		{
			MethodName: "GetIPLocation",
			Handler:    _User_GetIPLocation_Handler,
		},
		{
			MethodName: "UpdateUserInfo",
			Handler:    _User_UpdateUserInfo_Handler,
		},
		{
			MethodName: "ApplyModifyMobile",
			Handler:    _User_ApplyModifyMobile_Handler,
		},
		{
			MethodName: "GetModifyMobileInfo",
			Handler:    _User_GetModifyMobileInfo_Handler,
		},
		{
			MethodName: "GetModifyMobileList",
			Handler:    _User_GetModifyMobileList_Handler,
		},
		{
			MethodName: "GetAndCheckMobile",
			Handler:    _User_GetAndCheckMobile_Handler,
		},
		{
			MethodName: "TimeoutModifyMobile",
			Handler:    _User_TimeoutModifyMobile_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "user.proto",
}
