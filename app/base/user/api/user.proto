syntax = "proto3";
package user.base;
option go_package = ".;api";

service User {
  rpc TokenToID(TokenReq) returns (TokenRes);
  rpc GetUserInfo(UserReq) returns (UserInfoRes);
  rpc GetUsersByIds(UserIdsReq)returns(UserInfoRsp);
  rpc GetUsersByMongoIds(MongoIdsReq)returns(UserInfoRsp);
  rpc GetUsersByMsgIds(MsgIdsReq)returns(UserInfoRsp);
  rpc UpdateUserInfo(UpdateUserReq)returns(UserInfo);

  rpc IsUserExist(UserExistReq)returns(UserExistRsp);

  //指定条件拉取用户数据
  rpc PullUser(PullUserReq)returns(PullUserRsp);

  //查询用户备注
  rpc GetUserRemark(UserRemarkReq)returns(UserRemarkRsp);

  rpc GetOnline(GetOnlineRsq)returns(GetOnlineRsp);

  rpc UpdateAlbumStatus(UpdateAlbumReq)returns(UpdateAlbumRsp);


  //设置用户在线信息
  rpc SetOnline(SetOnlineUserInfoReq)returns(SetOnlineUserInfoReqRsp);

  //新增机器人
  rpc RobotAdd(RobotAddReq)returns(RobotAddRsp);


  rpc OnlineNum(OnlineNumReq)returns(OnlineNumRsp);

  rpc ValidHeader(ValidHeadReq)returns(ValidHeadRsp);
  rpc ValidByImei(ValidHeadReq)returns(ValidHeadRsp);
  rpc ValidByIP(ValidHeadReq)returns(ValidHeadRsp);

  // 设置用户开关
  rpc SetUserSwitch(SetUserSwitchReq)returns(SetUserSwitchRsp);
  // 获取用户开关状态
  rpc GetUserSwitch(GetUserSwitchReq)returns(GetUserSwitchRsp);

  // 获取用户粉丝列表
  rpc GetUserFansList(GetUserFansListReq)returns(GetUserFansListRsp);

  // 绑定用户个推关系
  rpc BindPushRelation(BindPushRelationReq)returns(EmptyRsp);
  // 解绑用户个推关系
  rpc UnBindPushRelation(UnBindPushRelationReq)returns(EmptyRsp);
  // 根据个推别名推送
  rpc PushByAlias(PushByAliasReq)returns(EmptyRsp);
  rpc SetPushStatus(SetPushStatusReq)returns(EmptyRsp);
  //审核文字
  rpc ReviewTextInfo(ReviewInfoReq)returns(ReviewInfoRsp);
  rpc GetIPLocation(IPLocReq)returns(IPLocRsp);

  rpc ApplyModifyMobile(ApplyModifyMobileReq)returns(EmptyRsp);
  rpc GetModifyMobileInfo(GetModifyMobileInfoReq)returns(GetModifyMobileInfoRsp);
  rpc GetModifyMobileList(GetModifyMobileListReq)returns(GetModifyMobileListRsp);
  rpc GetAndCheckMobile(GetAndCheckMobileReq)returns(GetAndCheckMobileRsp);
  rpc TimeoutModifyMobile(TimeoutModifyMobileReq)returns(TimeoutModifyMobileRsp);

  rpc GetUserInterest(GetUserInterestReq) returns(GetUserInterestRsp); // 获取用户兴趣标签
  rpc GetUserInterests(GetUserInterestsReq) returns(GetUserInterestsRsp); // 批量获取用户兴趣标签
}

enum ModifyType{
  NoneType = 0;
  UserSelf = 1; // 用户自己修改
  AdminHelp = 2; // 用户申请后管理员协助修改
  AdminAuth = 3; // 管理员特许修改
}

enum ModifyStatus {
  VoidStatus = 0;
  Pass = 1;
  Apply = 2;
  Reject = 3;
  Timeout = 4;
}

enum FilterParam {
  NoneFilter = 0;
  FilterPass = 1;
  FilterSelfModify = 2;
  FilterTimeout = 3;
  FilterReject = 4;
  FilterAdminAuth = 5;
  FilterApply = 6;
}

enum CheckType {
  NoneCheck = 0;
  CheckConflict = 1;
  CheckLimitTimes = 2;
}

message TimeoutModifyMobileReq {}

message TimeoutModifyMobileRsp {
  map<string, string> tMap = 1;
}

message GetAndCheckMobileReq {
  string noId = 1;
  string toMobile = 2;
  CheckType type = 3;
}

message GetAndCheckMobileRsp {
  bool isConflict = 1;
  MobileUserInfo from = 2;
  MobileUserInfo to = 3;
  bool isOverTimes = 4;
}

message MobileUserInfo {
  string noId = 1;
  string nickname = 2;
  string avatar = 3;
  int64 wealthLevel = 4;
  int64 charmLevel = 5;
  string mobile = 6;
}

message GetModifyMobileListReq {
  string noId = 1;
  string startTime = 2;
  string endTime = 3;
  FilterParam filterParam = 4;
  int64 page = 5;
  int64 pageSize = 6;
}

message GetModifyMobileListRsp {
  repeated ModifyMobileData list = 1;
  int64 Count = 2;
}

message ModifyMobileData {
  string id = 1;
  string noId = 2;
  string updateTime = 3;
  string fromMobile = 4;
  string toMobile = 5;
  string applyReason = 6;
  string adminReason = 7;
  string operatorName = 8;
  string applyTime = 9;
  repeated string proveImg = 10;
  string status = 11; // 处理结果
}

message GetModifyMobileInfoReq {
  string noId = 1;
}

message GetModifyMobileInfoRsp {
  string noId = 1;
  string fromMobile = 2; // 原手机号
  string toMobile = 3; // 新手机号
  string applyReason = 4; // 用户申请理由
  repeated string proveImg = 5; // 证明材料
  string modifyType = 6; // 修改类型
  string status = 7;
}

message ApplyModifyMobileReq {
  string noId = 1;
  string fromMobile = 2; // 原手机号
  string toMobile = 3; // 新手机号
  string operatorId = 4; // 操作人ID(后台登录账号ID)
  string operatorName = 5; // 操作人名称(后台实际操作人名称)
  ModifyType modifyType = 6; // 修改类型
  string applyReason = 7; // 用户申请理由
  string adminReason = 8; // 操作人拒绝/其他理由
  repeated string proveImg = 9; // 证明材料
  ModifyStatus modifyStatus = 10; // 操作状态
  string id = 11; // 记录ID
}

message SetPushStatusReq {
  string noId = 1;
  string status = 2;
}

enum PushType{
  Unknown = 0;
  PrivateMsg = 1; // 私信聊天
  PrivateGift = 2; // 私信送礼
  HeartGift = 3; // 心动
  AccostGift = 4; // 搭讪
  FollowersOpen = 5; // 关注者开播
}

message UnBindPushRelationReq{
  string noId = 1;
}

message BindPushRelationReq{
  // 用户ID
  string noId = 1;
  // 个推clientId
  string cid = 2;
  // 别名 本系统以用户的msgId作为别名
  string alias = 3;
  // 平台 ios/android
  string platform = 4;
}

message PushByAliasReq {
  // 发起推送者
  string fromId = 1;
  // 推送目标的别名
  repeated string toIds = 2;
  // 跳转
  TargetType targetType = 3;
  PushType pushType = 4;
  PushAttach attach = 5;
}

enum TargetType{
  None = 0;
  OpenLive = 1; // 直播
  ChatMessage = 2; // 私聊
}

message PushAttach {
  string liveType = 1; // 直播分类 Live，Voice
}

message EmptyRsp {
}

message GetUserSwitchReq {
  repeated string noIds = 1;
}

message GetUserSwitchRsp {
  repeated SwitchInfo list = 1;
}

message SwitchInfo {
  string noId = 1;
  // 位置开关 on/off
  string position = 2;
  // 私聊推送开关 on/off
  string privateChatPush = 3;
  // 开播推送开关 on/off
  string openLivePush = 4;
  // 自动拒绝连麦
  string autoRefuseMultiChat = 5;
  // 设置拒绝时间
  string autoRefuseTime = 6;
  // 管理隐身开关 on/off
  string anonymousBrowse = 7;
}

message SetUserSwitchReq {
  string noId = 1;
  // 位置开关 on/off
  string position = 2;
  // 私聊推送开关 on/off
  string privateChatPush = 3;
  // 开播推送开关 on/off
  string openLivePush = 4;
  // 自动拒绝连麦
  string autoRefuseMultiChat = 5;
  // 管理隐身开关 on/off
  string anonymousBrowse = 6;
}

message SetUserSwitchRsp {}

message GetUserFansListReq {
  string noId = 1;
  int64 pageNum = 2;
  int64 pageSize = 3;
}

message GetUserFansListRsp {
  repeated SessionInfo list = 1;
}

message SessionInfo {
  string fromId = 1;
  string toId = 2;
  string relation = 3;
}

message OnlineNumReq{

}

message OnlineNumRsp{
  int64 count = 1;
}

message RobotAddReq{
  robot robot = 1;
  int32 mode = 2; //默认0 添加一个 不为0 批量添加
  robotConfig robotConfig = 3;//机器人配置
}

message robotConfig{
  int32 num = 1; //添加数目
  int32 wealthMin = 2;//最小财富等级
  int32 wealthMax = 3;//最大财富等级
  int32 male = 4; //男占比
  int32 female = 5;//女占比
}

message robot{
  string nickName = 1;
  string gender = 2;
  string born = 3;
  string city = 4;
  int32 wealthLevel = 5;
  string starSign = 6;
  int32 height = 7;
  int32 weight = 8;
  string avatarUrl = 9;
}

message RobotAddRsp{
  bool status = 1;
  int32 success = 2; //成功数目
  int32 failure = 3; //失败数目
  repeated robotInfo robotInfos = 4;
}


message robotInfo{
  string msgId = 1;
  string noId = 2;
}

message SetOnlineUserInfoReq{
  string gender = 1;
  string noId = 2;
  string user = 3;
}

message SetOnlineUserInfoReqRsp{
  bool status = 1;
}


//token获取 _id
message TokenReq {
  string token = 1;
}

message TokenRes {
  string id = 1;
}

message UserReq{
  string Id = 1;
}
message UserInfoRes {
  string Id = 1;
  string nickname = 2;
  string born = 3;
}


//通过"noId"获取用户详情信息
message UserIdsReq{
  repeated string Ids = 1; //id
  repeated string cols = 2; //指定列
}

//通过"_id"获取用户详情信息
message MongoIdsReq{
  repeated string Ids = 1; //id
  repeated string cols = 2; //指定列
}

//通过"msgId"获取用户详情信息
message MsgIdsReq{
  repeated string Ids = 1; //id
  repeated string cols = 2; //指定列
}

message UserInfoRsp{
  repeated UserInfo Users = 1;
}

//更新用户信息
message UpdateUserReq{
  string Id = 1;
  bytes Data = 2;
}

//查询用户是否存在
message UserExistReq{
  string NoId = 1;
}

message UserExistRsp{
  int32 Exist = 1;
}

//详情见User模型
message UserInfo{
  string  Id = 1;
  string  NoId = 2;
  int32  Status = 3;
  string  NickName = 4;
  string  Gender = 5;
  int32  Age = 6;
  int64  Wealth = 7;
  int64  Charm = 8;
  int32  Like = 9;
  string  StarSign = 10;
  string Birthday = 11;
  string Feeling = 13;
  int32 Height = 14;
  int32 Weight = 15;
  string ImgList = 16;
  string RealStatus = 17;
  string AvatarUrl = 18;
  string AudioUrl = 19;
  string Education = 20;
  string Job = 21;
  string Income = 22;
  string Province = 23;
  string City = 24;
  string Area = 25;
  string HomeTown = 26;
  string RealNameStatus = 27;
  string Intro = 28;
  double Lon = 29;
  double Lat = 30;
  int32 IsOnline = 31;
  string LastLoginTime = 32;
  int32 CharmLevel = 33;
  string RegistryType = 34;
  string Uid = 35;
  string UnionId = 36;
  string RegisterStatus = 37;
  string InviteCode = 38;
  string Born = 39;
  string MsgId = 40;
  string RcToken = 41;
  int32 WealthLevel = 42;
  repeated UserImg Album = 43;
  int32 Duration = 44;
  string AvatarStatus = 45;
  string AudioStatus = 46;
  string GroupCode = 47;
  string AvatarLevel = 48;
  string SMResult = 49;
  string SMReason = 50;
  string IdName = 51;
  string ContractSignStatus = 52;
  string SuperAdmin = 53;
  string IdCard = 54;
  string Mobile = 55;
  string CheckAvatar = 56;
  string Robot = 57;
}

//相册
message UserImg {
  string ImgUrl = 1;
  string Status = 2;
}


message PullUserReq {
  bytes Query = 1;
  repeated string Cols = 2;
  int32 Limit = 3; //抓取数量
  int32 IsRand = 4;//是否随机
}

message PullUserRsp {
  repeated UserInfo Users = 1;
}

message UserRemarkReq {
  string NoId = 1;
  repeated string Ids = 2;
}

message UserRemarkRsp {
  map<string, string> UserRemark = 1;
}

message GetOnlineRsq{
  string gender = 1;
}

message GetOnlineRsp{
  repeated string Users = 1;
}

message UpdateAlbumReq{
  string userId = 1;
  map<string, string> album = 2;
}

message UpdateAlbumRsp{
  bool status = 1;
}

message ValidHeadReq {
  string noId = 1;
  string imei = 2;
  string ip = 3;
  float lon = 4;
  float lat = 5;
  string url = 6;
  string status = 7;
}

message ValidHeadRsp {
  int32 code = 1;
}

message ReviewInfoReq {
  string event = 1;
  string ruleType = 2;
  string reqText = 3;
  string rawText = 4;
  string noId = 5;
}

message ReviewInfoRsp {
  string status = 1;
}

message IPLocReq {
  string ip = 1;
}

message IPLocRsp {
  string province = 1;
}

message UserInterestTag {
  string tagId = 1;
  string tagName = 2;
}

message GetUserInterestReq {
  string noId = 1;
}

message GetUserInterestRsp {
  repeated UserInterestTag interests = 1;
}

message GetUserInterestsReq {
  repeated string noIds = 1;
}

message UserInterestsVO {
  repeated string interests = 1;
}

message GetUserInterestsRsp {
  map<string, UserInterestsVO> ret = 1;
}