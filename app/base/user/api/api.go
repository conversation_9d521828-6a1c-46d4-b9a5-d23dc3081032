package api

import (
	"creativematrix.com/beyondreading/pkg/config"
	"fmt"
	"google.golang.org/grpc/balancer/roundrobin"
	"google.golang.org/grpc/resolver"

	"creativematrix.com/beyondreading/pkg/discovery"
	"creativematrix.com/beyondreading/pkg/gm"
	"creativematrix.com/beyondreading/pkg/logger"
	pb "creativematrix.com/beyondreading/proto/user"
	"google.golang.org/grpc"
)

const App = "base-user"

func NewClient(c config.Base) (pb.UserServiceClient, error) {
	options := []grpc.DialOption{
		grpc.WithInsecure(),
		grpc.WithDefaultServiceConfig(fmt.Sprintf(`{"LoadBalancingPolicy": "%s"}`, roundrobin.Name)),
		grpc.WithUnaryInterceptor(gm.UnaryClientInterceptor()),
	}

	r := discovery.NewResolver(c.Etcd.Addrs, logger.Log)
	resolver.Register(r)

	conn, err := grpc.NewClient("etcd:///"+App, options...)
	//conn, err := grpc.Dial("192.168.3.62:32333", options...)
	if err != nil {
		return nil, err
	}
	return pb.NewUserServiceClient(conn), nil
}
