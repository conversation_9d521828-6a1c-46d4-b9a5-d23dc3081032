package model

import "creativematrix.com/beyondreading/pkg/ecode"

// 用户历史消息缓存
var (
	NothingFound = ecode.New(-404, "未找到")
	ServerErr    = ecode.New(-500, "服务器错误")

	ReviewForbidErr = ecode.New(-16101, "系统维护中，暂时无法更新名称")
	ReviewTextErr   = ecode.New(-16015, "发现敏感词请重新填写")

	OverChangeMobileLimitErr  = ecode.New(-13055, "7天内，只允许修改1次手机号哦~")
	FromMobileNotExistErr     = ecode.New(-13056, "原手机号未在平台注册过")
	ToMobileHasExistErr       = ecode.New(-13057, "无法完成本次操作，此手机号已绑定其他账号")
	FromToMobileIsSameErr     = ecode.New(-13058, "新手机号与原手机号相同")
	ModifyHasApplyErr         = ecode.New(-13059, "审核中，请耐心等待")
	AuthModifyHasApplyErr     = ecode.New(-13060, "该用户已提交申请，不可重复提交")
	MobileNotSelfErr          = ecode.New(-13061, "用户绑定手机号与输入原手机号不一致")
	MobileUserNotRealNameErr  = ecode.New(-13062, "该ID尚未实名认证，无法修改")
	ReviewToMobileHasExistErr = ecode.New(-13063, "无法完成本次操作，申请绑定的手机号已在平台注册")
)
