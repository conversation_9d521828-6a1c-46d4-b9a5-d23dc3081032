package model

import pb "creativematrix.com/beyondreading/app/base/user/api"

type PayLoad struct {
	PushType string  `json:"pushType,omitempty"`
	Attach   *Attach `json:"attach,omitempty"`
}

type Attach struct {
	NoId    string `json:"noId,omitempty"`
	SubType string `json:"subType,omitempty"`
}

type NotificationParam struct {
	Title      string
	Body       string
	IosTitle   string
	IosBody    string
	LogoUrl    string
	FromId     string
	LiveType   string // 直播分类 Live：看看 Voice：听听
	TargetType pb.TargetType
}
