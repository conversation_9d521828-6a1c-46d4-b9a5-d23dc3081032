package service

import (
	"fmt"
	"time"

	"creativematrix.com/beyondreading/app/base/user/conf"
	"creativematrix.com/beyondreading/app/common/po"
	"github.com/golang-jwt/jwt/v5"
)

type JwtService struct {
	config *conf.Config
}

type Claims struct {
	UserId    uint64 `json:"userId"`
	Phone     string `json:"phone"`
	Email     string `json:"email"`
	LoginType int32  `json:"loginType"`
	jwt.RegisteredClaims
}

func NewJwtService(config *conf.Config) *JwtService {
	return &JwtService{
		config: config,
	}
}

// GenerateToken 生成JWT token
func (j *JwtService) GenerateToken(user *po.User) (string, error) {
	now := time.Now()
	expireTime := now.Add(time.Duration(j.config.JWT.ExpireTime) * time.Second)

	claims := &Claims{
		UserId:    user.UserId,
		Phone:     user.Phone,
		Email:     user.Email,
		LoginType: user.LoginType,
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    j.config.JWT.Issuer,
			Subject:   fmt.Sprintf("%d", user.UserId),
			IssuedAt:  jwt.NewNumericDate(now),
			ExpiresAt: jwt.NewNumericDate(expireTime),
			NotBefore: jwt.NewNumericDate(now),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(j.config.JWT.Secret))
	if err != nil {
		return "", fmt.Errorf("failed to generate token: %w", err)
	}

	return tokenString, nil
}

// ParseToken 解析JWT token
func (j *JwtService) ParseToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(j.config.JWT.Secret), nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	if claims, ok := token.Claims.(*Claims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token")
}

// ValidateToken 验证JWT token
func (j *JwtService) ValidateToken(tokenString string) (*Claims, error) {
	claims, err := j.ParseToken(tokenString)
	if err != nil {
		return nil, err
	}

	// 检查token是否过期
	if claims.ExpiresAt != nil && claims.ExpiresAt.Before(time.Now()) {
		return nil, fmt.Errorf("token is expired")
	}

	// 检查token是否还未生效
	if claims.NotBefore != nil && claims.NotBefore.After(time.Now()) {
		return nil, fmt.Errorf("token is not valid yet")
	}

	return claims, nil
}

// RefreshToken 刷新JWT token
func (j *JwtService) RefreshToken(tokenString string) (string, error) {
	claims, err := j.ParseToken(tokenString)
	if err != nil {
		return "", err
	}

	// 检查token是否在刷新期内（过期前1小时内可以刷新）
	if claims.ExpiresAt != nil && claims.ExpiresAt.Before(time.Now().Add(-time.Hour)) {
		return "", fmt.Errorf("token is too old to refresh")
	}

	// 生成新的token
	now := time.Now()
	expireTime := now.Add(time.Duration(j.config.JWT.ExpireTime) * time.Second)

	newClaims := &Claims{
		UserId:    claims.UserId,
		Phone:     claims.Phone,
		Email:     claims.Email,
		LoginType: claims.LoginType,
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    j.config.JWT.Issuer,
			Subject:   claims.Subject,
			IssuedAt:  jwt.NewNumericDate(now),
			ExpiresAt: jwt.NewNumericDate(expireTime),
			NotBefore: jwt.NewNumericDate(now),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, newClaims)
	newTokenString, err := token.SignedString([]byte(j.config.JWT.Secret))
	if err != nil {
		return "", fmt.Errorf("failed to generate new token: %w", err)
	}

	return newTokenString, nil
}
