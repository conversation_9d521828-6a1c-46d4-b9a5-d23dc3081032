package dao

import (
	"context"
	"creativematrix.com/beyondreading/pkg/mysql"
	"fmt"

	"creativematrix.com/beyondreading/app/base/payment/conf"
	"creativematrix.com/beyondreading/app/base/payment/model"
	"creativematrix.com/beyondreading/pkg/mongo"
	"creativematrix.com/beyondreading/pkg/rabbitmq"
	"creativematrix.com/beyondreading/pkg/redis"
)

type Dao struct {
	conf        *conf.Config
	cache       redis.Redis
	schema      map[string]*mongo.Model
	rabbitMQ    *rabbitmq.Broker
	paymentConn *mongo.Connection
	mysql       mysql.Mysqler
}

var paymentTables = []string{
	model.TablePaymentOrders,
	model.TablePaymentCallbacks,
	model.TablePaymentRefunds,
	model.TablePaymentLogs,
}

func Load(c *conf.Config) *Dao {
	// MongoDB连接
	paymentConn := mongo.Connect(c.MongodbPayment)

	// 初始化MongoDB模型
	schema := make(map[string]*mongo.Model)
	for _, name := range paymentTables {
		schema[name] = paymentConn.Model(name)
	}

	// MySQL连接
	mysqlConn := mysql.New(c.Mysql)

	// Redis连接
	redisConn := redis.Load(c.RedisPayment)

	// RabbitMQ连接
	rabbitMQConn := rabbitmq.NewBroker(
		c.RabbitMQ.URL,
		c.RabbitMQ.MatrixEx)

	return &Dao{
		conf:        c,
		cache:       redisConn,
		schema:      schema,
		rabbitMQ:    rabbitMQConn,
		paymentConn: paymentConn,
		mysql:       mysqlConn,
	}
}

func (d *Dao) Ping(ctx context.Context) error {
	// 检查MySQL连接
	for _, db := range d.mysql.All() {
		if err := db.PingContext(ctx); err != nil {
			return fmt.Errorf("mysql ping failed: %w", err)
		}
	}

	// 检查Redis连接
	if _, err := d.cache.RDo(ctx, "PING"); err != nil {
		return fmt.Errorf("redis ping failed: %w", err)
	}

	return nil
}

func (d *Dao) Close() {

}

// PublishMessage 发布消息到RabbitMQ
func (d *Dao) PublishMessage(ctx context.Context, routingKey string, message interface{}) error {
	return d.rabbitMQ.Publish(routingKey, message)
}
