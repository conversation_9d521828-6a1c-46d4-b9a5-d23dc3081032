package dao

import (
	"context"
	"creativematrix.com/beyondreading/pkg/ecode"
	"errors"
	"time"

	"creativematrix.com/beyondreading/app/base/payment/model"
	"creativematrix.com/beyondreading/app/common/po"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/mongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// CreatePaymentRefund 创建退款记录
func (d *Dao) CreatePaymentRefund(ctx context.Context, refund *po.PaymentRefund) error {
	refund.CreatedAt = time.Now()
	refund.UpdatedAt = refund.CreatedAt

	insertedId, err := d.schema[model.TablePaymentRefunds].Insert(refund)
	if err != nil {
		logger.LogErrorf("Failed to create payment refund: %v", err)
		return err
	}

	// 获取插入的ID
	if oid, ok := insertedId.(primitive.ObjectID); ok {
		refund.ID = oid
	}

	return nil
}

// GetPaymentRefundById 根据退款ID获取退款记录
func (d *Dao) GetPaymentRefundById(ctx context.Context, refundId string) (*po.PaymentRefund, error) {
	filter := bson.M{"refundId": refundId}

	var result po.PaymentRefund
	err := d.schema[model.TablePaymentRefunds].FindOne(filter, &result)
	if err != nil {
		if errors.Is(err, ecode.NothingFound) {
			return nil, nil
		}
		logger.LogErrorf("Failed to get payment refund: %v", err)
		return nil, err
	}

	return &result, nil
}

// GetPaymentRefundsByOrderId 根据订单ID获取退款记录列表
func (d *Dao) GetPaymentRefundsByOrderId(ctx context.Context, orderId string) ([]*po.PaymentRefund, error) {
	filter := bson.M{"orderId": orderId}

	var refunds []*po.PaymentRefund
	err := d.schema[model.TablePaymentRefunds].Find(filter, &refunds,
		mongo.Context(ctx),
		mongo.Sort(bson.M{"createdAt": -1}))
	if err != nil {
		logger.LogErrorf("Failed to find refunds by order id: %v", err)
		return nil, err
	}

	return refunds, nil
}

// GetPaymentRefundsByUserId 根据用户ID获取退款记录列表
func (d *Dao) GetPaymentRefundsByUserId(ctx context.Context, userId uint64, page, pageSize int32) ([]*po.PaymentRefund, int64, error) {
	filter := bson.M{"userId": userId}

	// 获取总数
	total := d.schema[model.TablePaymentRefunds].Count(filter)

	// 分页查询
	skip := (page - 1) * pageSize
	var refunds []*po.PaymentRefund
	err := d.schema[model.TablePaymentRefunds].Find(filter, &refunds,
		mongo.Context(ctx),
		mongo.Skip(int64(skip)),
		mongo.Limit(int64(pageSize)),
		mongo.Sort(bson.M{"createdAt": -1}))
	if err != nil {
		logger.LogErrorf("Failed to find payment refunds: %v", err)
		return nil, 0, err
	}

	return refunds, total, nil
}

// UpdatePaymentRefund 更新退款记录
func (d *Dao) UpdatePaymentRefund(ctx context.Context, refundId string, updates bson.M) error {
	updates["updatedAt"] = time.Now()
	filter := bson.M{"refundId": refundId}
	update := bson.M{"$set": updates}

	_, err := d.schema[model.TablePaymentRefunds].Update(filter, update)
	if err != nil {
		logger.LogErrorf("Failed to update payment refund: %v", err)
		return err
	}

	return nil
}

// UpdatePaymentRefundStatus 更新退款状态
func (d *Dao) UpdatePaymentRefundStatus(ctx context.Context, refundId string, status int32) error {
	updates := bson.M{
		"status": status,
	}

	if status == po.RefundStatusSuccess || status == po.RefundStatusFailed {
		updates["processedAt"] = time.Now()
	}

	return d.UpdatePaymentRefund(ctx, refundId, updates)
}
