package conf

import (
	"creativematrix.com/beyondreading/pkg/rabbitmq"
	"creativematrix.com/beyondreading/pkg/redis"
	"fmt"

	"creativematrix.com/beyondreading/pkg/config"
)

type Config struct {
	config.Base

	RedisAccount *redis.Config

	Log struct {
		Level string
	}

	RabbitMQ struct {
		URL      string
		MatrixEx *rabbitmq.ExchangeConfig
	}
}

func Load(app string) *Config {
	var conf = new(Config)
	if err := config.Load(app, conf); err != nil {
		panic(fmt.Sprintf("config load failed: %v", err))
	}
	return conf
}
