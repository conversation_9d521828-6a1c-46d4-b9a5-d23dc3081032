package svc

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"creativematrix.com/beyondreading/app/common"
	"creativematrix.com/beyondreading/app/common/po"
	"creativematrix.com/beyondreading/pkg/logger"
	pb "creativematrix.com/beyondreading/proto/account"
)

// GetAccount 获取账户信息
func (s *AccountSvc) GetAccount(ctx context.Context, req *pb.GetAccountReq) (*pb.GetAccountResp, error) {
	account, err := s.dao.GetAccountByUserId(ctx, req.UserId)
	if err != nil {
		if err == sql.ErrNoRows {
			return &pb.GetAccountResp{
				Code:    404,
				Message: "Account not found",
			}, nil
		}
		logger.LogErrorf("Failed to get account: %v", err)
		return &pb.GetAccountResp{
			Code:    500,
			Message: "Internal server error",
		}, nil
	}

	return &pb.GetAccountResp{
		Code:    200,
		Message: "Success",
		Account: s.convertAccountToPB(account),
	}, nil
}

// CreateAccount 创建账户
func (s *AccountSvc) CreateAccount(ctx context.Context, req *pb.CreateAccountReq) (*pb.CreateAccountResp, error) {
	// 检查账户是否已存在
	existingAccount, err := s.dao.GetAccountByUserId(ctx, req.UserId)
	if err == nil && existingAccount != nil {

		return &pb.CreateAccountResp{
			Code:    200,
			Message: "Account already exists",
			Account: s.convertAccountToPB(existingAccount),
		}, nil
	}

	// 创建新账户
	account, err := s.dao.CreateAccount(ctx, req.UserId)
	if err != nil {
		logger.LogErrorf("Failed to create account: %v", err)
		return &pb.CreateAccountResp{
			Code:    500,
			Message: "Failed to create account",
		}, nil
	}

	return &pb.CreateAccountResp{
		Code:    200,
		Message: "Account created successfully",
		Account: s.convertAccountToPB(account),
	}, nil
}

// Recharge 充值
func (s *AccountSvc) Recharge(ctx context.Context, req *pb.RechargeReq) (*pb.RechargeResp, error) {
	// 计算书币数量
	coinAmount := req.Amount * float64(req.ExchangeRate)
	if req.ExchangeRate == 0 {
		coinAmount = req.Amount // 默认1:1兑换
	}

	// 获取账户信息
	account, err := s.dao.GetAccountByUserId(ctx, req.UserId)
	if err != nil {
		logger.LogErrorf("Failed to get account: %v", err)
		return &pb.RechargeResp{
			Code:    500,
			Message: "Failed to get account",
		}, nil
	}

	if account == nil {
		return &pb.RechargeResp{
			Code:    404,
			Message: "Account not found",
		}, nil
	}

	// 生成充值订单
	now := time.Now()
	orderId := fmt.Sprintf("RO_%d_%d", req.UserId, now.Unix())

	rechargeOrder := &po.RechargeOrder{
		OrderId:        orderId,
		AccountId:      account.AccountId,
		UserId:         req.UserId,
		Amount:         req.Amount,
		CoinAmount:     coinAmount,
		ExchangeRate:   req.ExchangeRate,
		PaymentMethod:  req.PaymentMethod,
		PaymentOrderId: "",
		Status:         2, // 直接设置为支付成功（简化处理）
		PaidAt:         &now,
		CreatedAt:      now,
		UpdatedAt:      now,
	}

	// 创建账户日志
	accountLog := &po.AccountLog{
		AccountId:       account.AccountId,
		UserId:          req.UserId,
		TransactionType: common.TransactionTypeRecharge,
		Amount:          coinAmount,
		BalanceBefore:   account.CoinBalance,
		BalanceAfter:    account.CoinBalance + coinAmount,
		OrderId:         orderId,
		Description:     "",
		CreatedAt:       now,
	}

	// 处理充值（事务）
	err = s.dao.ProcessRecharge(ctx, req.UserId, rechargeOrder, accountLog)
	if err != nil {
		logger.LogErrorf("Failed to process recharge: %v", err)
		return &pb.RechargeResp{
			Code:    500,
			Message: "Failed to process recharge",
		}, nil
	}

	return &pb.RechargeResp{
		Code:    200,
		Message: "Recharge successful",
		Order:   s.convertRechargeOrderToPB(rechargeOrder),
	}, nil
}

// GetAccountLogs 获取账户日志
func (s *AccountSvc) GetAccountLogs(ctx context.Context, req *pb.GetAccountLogsReq) (*pb.GetAccountLogsResp, error) {
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	logs, total, err := s.dao.GetAccountLogs(ctx, req.UserId, req.Page, req.PageSize, req.TransactionType)
	if err != nil {
		logger.LogErrorf("Failed to get account logs: %v", err)
		return &pb.GetAccountLogsResp{
			Code:    500,
			Message: "Failed to get account logs",
		}, nil
	}

	var pbLogs []*pb.AccountLog
	for _, log := range logs {
		pbLogs = append(pbLogs, s.convertAccountLogToPB(log))
	}

	return &pb.GetAccountLogsResp{
		Code:    200,
		Message: "Success",
		Logs:    pbLogs,
		Total:   total,
	}, nil
}

// UpdateUserStatus 更新用户状态
func (s *AccountSvc) UpdateUserStatus(ctx context.Context, req *pb.UpdateUserStatusReq) (*pb.UpdateUserStatusResp, error) {
	var vipExpireTime, monthlyExpireTime *time.Time

	if req.VipExpireTime > 0 {
		t := time.Unix(req.VipExpireTime, 0)
		vipExpireTime = &t
	}

	if req.MonthlyExpireTime > 0 {
		t := time.Unix(req.MonthlyExpireTime, 0)
		monthlyExpireTime = &t
	}

	account, err := s.dao.UpdateUserStatus(ctx, req.UserId, req.UserType, vipExpireTime, monthlyExpireTime)
	if err != nil {
		logger.LogErrorf("Failed to update user status: %v", err)
		return &pb.UpdateUserStatusResp{
			Code:    500,
			Message: "Failed to update user status",
		}, nil
	}

	return &pb.UpdateUserStatusResp{
		Code:    200,
		Message: "User status updated successfully",
		Account: s.convertAccountToPB(account),
	}, nil
}

// DeductCoins 扣除书币
func (s *AccountSvc) DeductCoins(ctx context.Context, req *pb.DeductCoinsReq) (*pb.DeductCoinsResp, error) {
	// 更新账户余额（扣除书币）
	account, err := s.dao.UpdateAccountBalance(ctx, req.UserId, -req.Amount, req.TransactionType)
	if err != nil {
		logger.LogErrorf("Failed to deduct coins: %v", err)
		return &pb.DeductCoinsResp{
			Code:    500,
			Message: fmt.Sprintf("Failed to deduct coins: %v", err),
		}, nil
	}

	// 创建账户日志
	log := &po.AccountLog{
		AccountId:       account.AccountId,
		UserId:          req.UserId,
		TransactionType: req.TransactionType,
		Amount:          -req.Amount,
		BalanceBefore:   account.CoinBalance + req.Amount,
		BalanceAfter:    account.CoinBalance,
		OrderId:         req.OrderId,
		BookId:          req.BookId,
		ChapterId:       req.ChapterId,
		Description:     req.Description,
		CreatedAt:       time.Now(),
	}

	err = s.dao.CreateAccountLog(ctx, log)
	if err != nil {
		logger.LogErrorf("Failed to create account log: %v", err)
	}

	return &pb.DeductCoinsResp{
		Code:    200,
		Message: "Coins deducted successfully",
		Account: s.convertAccountToPB(account),
	}, nil
}

// CheckUserStatus 检查用户状态
func (s *AccountSvc) CheckUserStatus(ctx context.Context, req *pb.CheckUserStatusReq) (*pb.CheckUserStatusResp, error) {
	account, err := s.dao.GetAccountByUserId(ctx, req.UserId)
	if err != nil {
		logger.LogErrorf("Failed to get account: %v", err)
		return &pb.CheckUserStatusResp{
			Code:    500,
			Message: "Failed to check user status",
		}, nil
	}

	if account == nil {
		return &pb.CheckUserStatusResp{
			Code:    404,
			Message: "Account not found",
		}, nil
	}

	// 检查VIP状态
	hasVip := false
	if account.VipExpireTime != nil && account.VipExpireTime.After(time.Now()) {
		hasVip = true
	}

	// 检查包月状态
	hasMonthly := false
	if account.MonthlyExpireTime != nil && account.MonthlyExpireTime.After(time.Now()) {
		hasMonthly = true
	}

	return &pb.CheckUserStatusResp{
		Code:       200,
		Message:    "Success",
		Account:    s.convertAccountToPB(account),
		HasVip:     hasVip,
		HasMonthly: hasMonthly,
	}, nil
}
