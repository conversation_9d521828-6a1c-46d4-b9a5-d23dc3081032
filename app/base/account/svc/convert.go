package svc

import (
	"creativematrix.com/beyondreading/app/common/po"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/utils"
	pb "creativematrix.com/beyondreading/proto/account"
)

// convertAccountToPB 转换Account为protobuf格式
func (s *AccountSvc) convertAccountToPB(account *po.Account) *pb.AccountInfo {
	if account == nil {
		return nil
	}

	pbAccount := &pb.AccountInfo{}
	if err := utils.JsonCopy(account, pbAccount); err != nil {
		logger.LogErrorf(err.Error())
		return nil
	}

	//pbAccount := &pb.AccountInfo{
	//	AccountId:      account.AccountId,
	//	UserId:         account.UserId,
	//	CoinBalance:    account.CoinBalance,
	//	TotalRecharged: account.TotalRecharged,
	//	TotalConsumed:  account.TotalConsumed,
	//	Status:         account.Status,
	//	UserType:       account.UserType,
	//	UserLevel:      account.UserLevel,
	//	CreatedAt:      account.CreatedAt.Unix(),
	//	UpdatedAt:      account.UpdatedAt.Unix(),
	//}

	pbAccount.CreatedAt = account.CreatedAt.Unix()
	pbAccount.UpdatedAt = account.UpdatedAt.Unix()
	
	if account.VipExpireTime != nil {
		pbAccount.VipExpireTime = account.VipExpireTime.Unix()
	}

	if account.MonthlyExpireTime != nil {
		pbAccount.MonthlyExpireTime = account.MonthlyExpireTime.Unix()
	}

	return pbAccount
}

// convertAccountLogToPB 转换AccountLog为protobuf格式
func (s *AccountSvc) convertAccountLogToPB(log *po.AccountLog) *pb.AccountLog {
	if log == nil {
		return nil
	}

	pbLog := &pb.AccountLog{
		LogId:           log.LogId,
		AccountId:       log.AccountId,
		UserId:          log.UserId,
		TransactionType: log.TransactionType,
		Amount:          log.Amount,
		BalanceBefore:   log.BalanceBefore,
		BalanceAfter:    log.BalanceAfter,
		CreatedAt:       log.CreatedAt.Unix(),
	}

	if log.OrderId != nil {
		pbLog.OrderId = *log.OrderId
	}

	if log.BookId != nil {
		pbLog.BookId = *log.BookId
	}

	if log.ChapterId != nil {
		pbLog.ChapterId = *log.ChapterId
	}

	if log.Description != nil {
		pbLog.Description = *log.Description
	}

	if log.ExtraData != nil {
		pbLog.ExtraData = *log.ExtraData
	}

	return pbLog
}

// convertRechargeOrderToPB 转换RechargeOrder为protobuf格式
func (s *AccountSvc) convertRechargeOrderToPB(order *po.RechargeOrder) *pb.RechargeOrder {
	if order == nil {
		return nil
	}

	pbOrder := &pb.RechargeOrder{
		OrderId:       order.OrderId,
		AccountId:     order.AccountId,
		UserId:        order.UserId,
		Amount:        order.Amount,
		CoinAmount:    order.CoinAmount,
		ExchangeRate:  float32(order.ExchangeRate),
		PaymentMethod: order.PaymentMethod,
		Status:        order.Status,
		CreatedAt:     order.CreatedAt.Unix(),
		UpdatedAt:     order.UpdatedAt.Unix(),
	}

	if order.PaymentOrderId != nil {
		pbOrder.PaymentOrderId = *order.PaymentOrderId
	}

	if order.PaidAt != nil {
		pbOrder.PaidAt = order.PaidAt.Unix()
	}

	return pbOrder
}
