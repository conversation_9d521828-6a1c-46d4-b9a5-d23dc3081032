package main

import (
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"creativematrix.com/beyondreading/app/base/account/conf"
	"creativematrix.com/beyondreading/app/base/account/consumer"
	"creativematrix.com/beyondreading/app/base/account/dao"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/mq"
	"creativematrix.com/beyondreading/pkg/tracer"
)

func main() {
	// 加载配置
	config := conf.Load("base-account")
	
	// 初始化日志
	logger.InitLog()
	
	// 初始化链路追踪
	tracer.InitTracing()
	
	// 初始化DAO
	accountDao := dao.Load(config)
	defer accountDao.Close()
	
	// 初始化消费者
	paymentConsumer := consumer.NewPaymentConsumer(accountDao)
	
	// 创建MQ消费者
	mqConsumer := mq.NewConsumer(&mq.ConsumerOptions{
		ExchangeOpt: &mq.ExchangeOption{
			Name: "payment",
			Type: "topic",
		},
		BrokerURL:      config.RabbitMQ.URL,
		MonitorAddress: ":8080",
	})
	
	// 启动消费者任务
	go func() {
		logger.LogInfof("Starting VIP purchase consumer...")
		mqConsumer.LaunchJob("payment.vip.purchased", "account.vip.purchase", paymentConsumer.HandleVipPurchase)
	}()
	
	go func() {
		logger.LogInfof("Starting monthly subscription consumer...")
		mqConsumer.LaunchJob("payment.subscription.activated", "account.monthly.subscription", paymentConsumer.HandleMonthlySubscription)
	}()
	
	go func() {
		logger.LogInfof("Starting coin recharge consumer...")
		mqConsumer.LaunchJob("payment.coin.recharged", "account.coin.recharge", paymentConsumer.HandleCoinRecharge)
	}()
	
	logger.LogInfof("Account payment consumer started successfully")
	
	// 等待信号
	c := make(chan os.Signal, 1)
	signal.Notify(c, syscall.SIGTERM, syscall.SIGINT)
	<-c
	
	logger.LogInfof("Account payment consumer shutting down...")
}
