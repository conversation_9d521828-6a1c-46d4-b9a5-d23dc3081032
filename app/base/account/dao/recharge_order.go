package dao

import (
	"context"
	"creativematrix.com/beyondreading/app/common"
	"database/sql"
	"fmt"
	"time"

	"creativematrix.com/beyondreading/app/common/po"
)

// CreateRechargeOrder 创建充值订单
func (d *Dao) CreateRechargeOrder(ctx context.Context, order *po.RechargeOrder) error {
	db, err := d.GetDB(order.UserId)
	if err != nil {
		return err
	}

	query := `INSERT INTO %s (order_id, account_id, user_id, amount, coin_amount, exchange_rate,
			  payment_method, payment_order_id, status, paid_at, created_at, updated_at)
			  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

	query = d.setRechargeOrderTable(query, order.UserId)
	_, err = db.Exec(query, order.OrderId, order.AccountId, order.UserId, order.Amount,
		order.CoinAmount, order.ExchangeRate, order.PaymentMethod, order.PaymentOrderId,
		order.Status, order.PaidAt, order.CreatedAt, order.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to create recharge order: %w", err)
	}

	return nil
}

// GetRechargeOrderById 根据订单ID获取充值订单
func (d *Dao) GetRechargeOrderById(ctx context.Context, userId uint64, orderId string) (*po.RechargeOrder, error) {
	db, err := d.GetDB(userId)
	if err != nil {
		return nil, err
	}

	var order po.RechargeOrder
	query := `SELECT order_id, account_id, user_id, amount, coin_amount, exchange_rate,
			  payment_method, payment_order_id, status, paid_at, created_at, updated_at
			  FROM %s WHERE order_id = ?`

	query = d.setRechargeOrderTable(query, userId)
	err = db.Get(&order, query, orderId)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil // 订单不存在
		}
		return nil, fmt.Errorf("failed to get recharge order: %w", err)
	}

	return &order, nil
}

// GetRechargeOrdersByUserId 根据用户ID获取充值订单列表
func (d *Dao) GetRechargeOrdersByUserId(ctx context.Context, userId uint64, page, pageSize int32, status int32) ([]*po.RechargeOrder, int64, error) {
	db, err := d.GetDB(userId)
	if err != nil {
		return nil, 0, err
	}

	// 构建查询条件
	whereClause := "WHERE user_id = ?"
	args := []interface{}{userId}

	if status > 0 {
		whereClause += " AND status = ?"
		args = append(args, status)
	}

	// 获取总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s %s", "%s", whereClause)
	countQuery = d.setRechargeOrderTable(countQuery, userId)
	var total int64
	err = db.Get(&total, countQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get recharge orders count: %w", err)
	}

	// 获取分页数据
	offset := (page - 1) * pageSize
	dataQuery := fmt.Sprintf(`SELECT order_id, account_id, user_id, amount, coin_amount, exchange_rate,
							  payment_method, payment_order_id, status, paid_at, created_at, updated_at
							  FROM %s %s ORDER BY created_at DESC LIMIT ? OFFSET ?`, "%s", whereClause)
	dataQuery = d.setRechargeOrderTable(dataQuery, userId)
	args = append(args, pageSize, offset)

	var orders []*po.RechargeOrder
	err = db.Select(&orders, dataQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get recharge orders: %w", err)
	}

	return orders, total, nil
}

// UpdateRechargeOrderStatus 更新充值订单状态
func (d *Dao) UpdateRechargeOrderStatus(ctx context.Context, userId uint64, orderId string, status int32, paymentOrderId *string, paidAt *time.Time) error {
	db, err := d.GetDB(userId)
	if err != nil {
		return err
	}

	query := `UPDATE %s SET status = ?, payment_order_id = ?, paid_at = ?, updated_at = ?
			  WHERE order_id = ?`

	query = d.setRechargeOrderTable(query, userId)
	_, err = db.Exec(query, status, paymentOrderId, paidAt, time.Now(), orderId)
	if err != nil {
		return fmt.Errorf("failed to update recharge order status: %w", err)
	}

	return nil
}

// GetRechargeOrdersByPaymentOrderId 根据第三方支付订单ID获取充值订单
func (d *Dao) GetRechargeOrdersByPaymentOrderId(ctx context.Context, userId uint64, paymentOrderId string) (*po.RechargeOrder, error) {
	db, err := d.GetDB(userId)
	if err != nil {
		return nil, err
	}

	var order po.RechargeOrder
	query := `SELECT order_id, account_id, user_id, amount, coin_amount, exchange_rate,
			  payment_method, payment_order_id, status, paid_at, created_at, updated_at
			  FROM %s WHERE payment_order_id = ?`

	query = d.setRechargeOrderTable(query, userId)
	err = db.Get(&order, query, paymentOrderId)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil // 订单不存在
		}
		return nil, fmt.Errorf("failed to get recharge order by payment order id: %w", err)
	}

	return &order, nil
}

// GetRechargeOrdersByTimeRange 根据时间范围获取充值订单
func (d *Dao) GetRechargeOrdersByTimeRange(ctx context.Context, userId uint64, startTime, endTime time.Time, page, pageSize int32) ([]*po.RechargeOrder, int64, error) {
	db, err := d.GetDB(userId)
	if err != nil {
		return nil, 0, err
	}

	whereClause := "WHERE user_id = ? AND created_at >= ? AND created_at <= ?"
	args := []interface{}{userId, startTime, endTime}

	// 获取总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s %s", "%s", whereClause)
	countQuery = d.setRechargeOrderTable(countQuery, userId)
	var total int64
	err = db.Get(&total, countQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get recharge orders count by time range: %w", err)
	}

	// 获取分页数据
	offset := (page - 1) * pageSize
	dataQuery := fmt.Sprintf(`SELECT order_id, account_id, user_id, amount, coin_amount, exchange_rate,
							  payment_method, payment_order_id, status, paid_at, created_at, updated_at
							  FROM %s %s ORDER BY created_at DESC LIMIT ? OFFSET ?`, "%s", whereClause)
	dataQuery = d.setRechargeOrderTable(dataQuery, userId)
	args = append(args, pageSize, offset)

	var orders []*po.RechargeOrder
	err = db.Select(&orders, dataQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get recharge orders by time range: %w", err)
	}

	return orders, total, nil
}

// setRechargeOrderTable 设置充值订单分表名称
func (d *Dao) setRechargeOrderTable(query string, userId uint64) string {
	shardIndex := userId % common.RechargeOrderTableShardNum
	tableName := fmt.Sprintf("recharge_order%02d", shardIndex)
	return fmt.Sprintf(query, tableName)
}
