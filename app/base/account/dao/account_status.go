package dao

import (
	"context"
	"fmt"
	"time"

	"creativematrix.com/beyondreading/pkg/logger"
)

// UpdateAccountVipStatus 更新账户VIP状态
func (d *Dao) UpdateAccountVipStatus(ctx context.Context, userId uint64, vipExpireTime *time.Time) error {
	db, err := d.GetDB(userId)
	if err != nil {
		return err
	}

	// 开启事务
	tx, err := db.Beginx()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// 更新VIP状态
	query := `UPDATE %s SET vip_expire_time = ?, updated_at = ? WHERE user_id = ?`
	query = d.setAccountTable(query, userId)

	now := time.Now()
	_, err = tx.Exec(query, vipExpireTime, now, userId)
	if err != nil {
		return fmt.E<PERSON><PERSON>("failed to update VIP status: %w", err)
	}

	// 提交事务
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	// 删除缓存
	err = d.deleteAccountCache(ctx, userId)
	if err != nil {
		logger.LogErrorf("Failed to delete account cache: %v", err)
	}

	logger.LogInfof("Updated VIP status for user %d, expire time: %s", userId, vipExpireTime.Format("2006-01-02 15:04:05"))
	return nil
}

// UpdateAccountMonthlyStatus 更新账户包月状态
func (d *Dao) UpdateAccountMonthlyStatus(ctx context.Context, userId uint64, monthlyExpireTime *time.Time) error {
	db, err := d.GetDB(userId)
	if err != nil {
		return err
	}

	// 开启事务
	tx, err := db.Beginx()
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// 更新包月状态
	query := `UPDATE %s SET monthly_expire_time = ?, updated_at = ? WHERE user_id = ?`
	query = d.setAccountTable(query, userId)

	now := time.Now()
	_, err = tx.Exec(query, monthlyExpireTime, now, userId)
	if err != nil {
		return fmt.Errorf("failed to update monthly status: %w", err)
	}

	// 提交事务
	if err = tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	// 删除缓存
	err = d.deleteAccountCache(ctx, userId)
	if err != nil {
		logger.LogErrorf("Failed to delete account cache: %v", err)
	}

	logger.LogInfof("Updated monthly status for user %d, expire time: %s", userId, monthlyExpireTime.Format("2006-01-02 15:04:05"))
	return nil
}

// CheckUserVipStatus 检查用户VIP状态
func (d *Dao) CheckUserVipStatus(ctx context.Context, userId uint64) (bool, *time.Time, error) {
	account, err := d.GetAccountByUserId(ctx, userId)
	if err != nil {
		return false, nil, err
	}

	if account == nil {
		return false, nil, nil
	}

	now := time.Now()
	if account.VipExpireTime != nil && account.VipExpireTime.After(now) {
		return true, account.VipExpireTime, nil
	}

	return false, account.VipExpireTime, nil
}

// CheckUserMonthlyStatus 检查用户包月状态
func (d *Dao) CheckUserMonthlyStatus(ctx context.Context, userId uint64) (bool, *time.Time, error) {
	account, err := d.GetAccountByUserId(ctx, userId)
	if err != nil {
		return false, nil, err
	}

	if account == nil {
		return false, nil, nil
	}

	now := time.Now()
	if account.MonthlyExpireTime != nil && account.MonthlyExpireTime.After(now) {
		return true, account.MonthlyExpireTime, nil
	}

	return false, account.MonthlyExpireTime, nil
}

// GetExpiredVipUsers 获取VIP过期的用户
func (d *Dao) GetExpiredVipUsers(ctx context.Context, limit int) ([]uint64, error) {
	// 这里需要查询所有分片表，简化处理只查询一个分片
	db, err := d.GetDB(1) // 使用第一个分片
	if err != nil {
		return nil, err
	}

	query := `SELECT user_id FROM %s WHERE vip_expire_time IS NOT NULL AND vip_expire_time < ? LIMIT ?`
	query = d.setAccountTable(query, 1)

	var userIds []uint64
	err = db.Select(&userIds, query, time.Now(), limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get expired VIP users: %w", err)
	}

	return userIds, nil
}

// GetExpiredMonthlyUsers 获取包月过期的用户
func (d *Dao) GetExpiredMonthlyUsers(ctx context.Context, limit int) ([]uint64, error) {
	// 这里需要查询所有分片表，简化处理只查询一个分片
	db, err := d.GetDB(1) // 使用第一个分片
	if err != nil {
		return nil, err
	}

	query := `SELECT user_id FROM %s WHERE monthly_expire_time IS NOT NULL AND monthly_expire_time < ? LIMIT ?`
	query = d.setAccountTable(query, 1)

	var userIds []uint64
	err = db.Select(&userIds, query, time.Now(), limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get expired monthly users: %w", err)
	}

	return userIds, nil
}
