package dao

import (
	"context"
	"fmt"

	"creativematrix.com/beyondreading/app/base/account/conf"
	"creativematrix.com/beyondreading/pkg/mysql"
	"creativematrix.com/beyondreading/pkg/redis"
	"github.com/jmoiron/sqlx"
)

type Dao struct {
	msshard mysql.Mysqler
	cache   redis.Redis
	conf    *conf.Config
}

func Load(c *conf.Config) *Dao {
	// 初始化MySQL连接
	mysqlConn := mysql.New(c.Mysql)

	// 初始化Redis连接
	redisConn := redis.Load(c.Cache)

	return &Dao{
		msshard: mysqlConn,
		cache:   redisConn,
		conf:    c,
	}
}

func (d *Dao) Ping(ctx context.Context) error {
	// 检查MySQL连接
	for _, db := range d.msshard.All() {
		if err := db.PingContext(ctx); err != nil {
			return fmt.Errorf("mysql ping failed: %w", err)
		}
	}

	// 检查Redis连接
	if _, err := d.cache.RDo(ctx, "PING"); err != nil {
		return fmt.Errorf("redis ping failed: %w", err)
	}

	return nil
}

func (d *Dao) Close() {
	// 关闭Redis连接
	if err := d.cache.RClose(); err != nil {
		fmt.Printf("Failed to close redis connection: %v\n", err)
	}
}

// GetDB 获取数据库连接
func (d *Dao) GetDB(userId uint64) (*sqlx.DB, error) {
	userIdStr := fmt.Sprintf("%d", userId)
	db, err := d.msshard.DB(userIdStr)
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}
	return db, nil
}
