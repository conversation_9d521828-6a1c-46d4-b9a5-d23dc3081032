package model

import (
	"time"
)

// 表名常量
const (
	TableAccount       = "account"
	TableAccountLog    = "account_log"
	TableRechargeOrder = "recharge_order"
	TablePurchaseOrder = "purchase_order"
)

// 缓存key常量
const (
	CacheKeyAccount       = "account:user:%s"           // 用户账户缓存
	CacheKeyAccountLock   = "account:lock:user:%s"      // 用户账户锁
	CacheKeyRechargeOrder = "recharge:order:%s"         // 充值订单缓存
	CacheKeyPurchaseOrder = "purchase:order:%s"         // 购买订单缓存
	CacheKeyChapterPurchased = "chapter:purchased:%s:%s:%s" // 章节购买状态缓存 user:book:chapter
	CacheKeyMonthlyStatus = "monthly:status:%s:%s"      // 包月状态缓存 user:book
)

// 缓存过期时间常量
const (
	CacheExpireAccount       = 3600  // 账户信息缓存1小时
	CacheExpireOrder         = 1800  // 订单信息缓存30分钟
	CacheExpireChapter       = 7200  // 章节购买状态缓存2小时
	CacheExpireMonthly       = 3600  // 包月状态缓存1小时
	CacheExpireLock          = 30    // 锁过期时间30秒
)

// 交易类型常量
const (
	TransactionTypeRecharge        = "recharge"         // 充值
	TransactionTypePurchaseChapter = "purchase_chapter" // 购买章节
	TransactionTypePurchaseMonthly = "purchase_monthly" // 购买包月
	TransactionTypePurchaseVip     = "purchase_vip"     // 购买VIP
)

// 订单类型常量
const (
	OrderTypeChapter = "chapter" // 章节订单
	OrderTypeMonthly = "monthly" // 包月订单
	OrderTypeVip     = "vip"     // VIP订单
)

// 账户状态常量
const (
	AccountStatusNormal  = 1 // 正常
	AccountStatusFrozen  = 2 // 冻结
	AccountStatusClosed  = 3 // 注销
)

// 订单状态常量
const (
	OrderStatusPending = 1 // 待支付
	OrderStatusPaid    = 2 // 支付成功
	OrderStatusFailed  = 3 // 支付失败
	OrderStatusRefund  = 4 // 已退款（仅充值订单）
)

// 支付方式常量
const (
	PaymentMethodAlipay = "alipay" // 支付宝
	PaymentMethodWechat = "wechat" // 微信支付
	PaymentMethodApple  = "apple"  // 苹果支付
)

// Account 账户模型
type Account struct {
	AccountId      uint64    `db:"account_id" json:"account_id"`
	UserId         string    `db:"user_id" json:"user_id"`
	CoinBalance    string    `db:"coin_balance" json:"coin_balance"`
	TotalRecharged string    `db:"total_recharged" json:"total_recharged"`
	TotalConsumed  string    `db:"total_consumed" json:"total_consumed"`
	Status         int       `db:"status" json:"status"`
	CreatedAt      time.Time `db:"created_at" json:"created_at"`
	UpdatedAt      time.Time `db:"updated_at" json:"updated_at"`
}

// AccountLog 账户日志模型
type AccountLog struct {
	LogId           uint64    `db:"log_id" json:"log_id"`
	AccountId       uint64    `db:"account_id" json:"account_id"`
	UserId          string    `db:"user_id" json:"user_id"`
	TransactionType string    `db:"transaction_type" json:"transaction_type"`
	Amount          string    `db:"amount" json:"amount"`
	BalanceBefore   string    `db:"balance_before" json:"balance_before"`
	BalanceAfter    string    `db:"balance_after" json:"balance_after"`
	OrderId         *string   `db:"order_id" json:"order_id"`
	BookId          *string   `db:"book_id" json:"book_id"`
	ChapterId       *string   `db:"chapter_id" json:"chapter_id"`
	Description     *string   `db:"description" json:"description"`
	ExtraData       *string   `db:"extra_data" json:"extra_data"`
	CreatedAt       time.Time `db:"created_at" json:"created_at"`
}

// RechargeOrder 充值订单模型
type RechargeOrder struct {
	OrderId        string     `db:"order_id" json:"order_id"`
	AccountId      uint64     `db:"account_id" json:"account_id"`
	UserId         string     `db:"user_id" json:"user_id"`
	Amount         string     `db:"amount" json:"amount"`
	CoinAmount     string     `db:"coin_amount" json:"coin_amount"`
	ExchangeRate   string     `db:"exchange_rate" json:"exchange_rate"`
	PaymentMethod  string     `db:"payment_method" json:"payment_method"`
	PaymentOrderId *string    `db:"payment_order_id" json:"payment_order_id"`
	Status         int        `db:"status" json:"status"`
	PaidAt         *time.Time `db:"paid_at" json:"paid_at"`
	CreatedAt      time.Time  `db:"created_at" json:"created_at"`
	UpdatedAt      time.Time  `db:"updated_at" json:"updated_at"`
}

// PurchaseOrder 购买订单模型
type PurchaseOrder struct {
	OrderId      string     `db:"order_id" json:"order_id"`
	AccountId    uint64     `db:"account_id" json:"account_id"`
	UserId       string     `db:"user_id" json:"user_id"`
	OrderType    string     `db:"order_type" json:"order_type"`
	BookId       *string    `db:"book_id" json:"book_id"`
	BookName     *string    `db:"book_name" json:"book_name"`
	ChapterId    *string    `db:"chapter_id" json:"chapter_id"`
	ChapterTitle *string    `db:"chapter_title" json:"chapter_title"`
	ChapterOrder *uint32    `db:"chapter_order" json:"chapter_order"`
	CoinAmount   string     `db:"coin_amount" json:"coin_amount"`
	DurationDays *int       `db:"duration_days" json:"duration_days"`
	StartTime    *time.Time `db:"start_time" json:"start_time"`
	EndTime      *time.Time `db:"end_time" json:"end_time"`
	Status       int        `db:"status" json:"status"`
	CreatedAt    time.Time  `db:"created_at" json:"created_at"`
	UpdatedAt    time.Time  `db:"updated_at" json:"updated_at"`
}

// AccountBalance 账户余额查询结果
type AccountBalance struct {
	AccountId   uint64 `db:"account_id" json:"account_id"`
	UserId      string `db:"user_id" json:"user_id"`
	CoinBalance string `db:"coin_balance" json:"coin_balance"`
}

// ChapterPurchaseStatus 章节购买状态
type ChapterPurchaseStatus struct {
	UserId      string    `json:"user_id"`
	BookId      string    `json:"book_id"`
	ChapterId   string    `json:"chapter_id"`
	IsPurchased bool      `json:"is_purchased"`
	PurchasedAt time.Time `json:"purchased_at"`
}

// MonthlyStatus 包月状态
type MonthlyStatus struct {
	UserId    string    `json:"user_id"`
	BookId    string    `json:"book_id"`
	IsActive  bool      `json:"is_active"`
	StartTime time.Time `json:"start_time"`
	EndTime   time.Time `json:"end_time"`
}

// TransactionRequest 交易请求
type TransactionRequest struct {
	UserId          string  `json:"user_id"`
	Amount          string  `json:"amount"`
	TransactionType string  `json:"transaction_type"`
	OrderId         *string `json:"order_id"`
	BookId          *string `json:"book_id"`
	ChapterId       *string `json:"chapter_id"`
	Description     *string `json:"description"`
	ExtraData       *string `json:"extra_data"`
}
