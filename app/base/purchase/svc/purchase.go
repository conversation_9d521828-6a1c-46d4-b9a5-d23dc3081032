package svc

import (
	"context"
	"creativematrix.com/beyondreading/app/common"
	"creativematrix.com/beyondreading/app/common/po"
	"creativematrix.com/beyondreading/pkg/logger"
	accountpb "creativematrix.com/beyondreading/proto/account"
	pb "creativematrix.com/beyondreading/proto/purchase"
	"fmt"
	"time"
)

// PurchaseChapter 购买章节（支持批量购买）
func (s *PurchaseSvc) PurchaseChapter(ctx context.Context, req *pb.PurchaseChapterReq) (*pb.PurchaseChapterResp, error) {
	if len(req.Chapters) == 0 {
		return &pb.PurchaseChapterResp{
			Code:    400,
			Message: "No chapters to purchase",
		}, nil
	}

	// 提取章节序号和金额
	var chapterOrders []uint32
	var coinAmounts []float64
	totalAmount := 0.0

	for _, chapter := range req.Chapters {
		chapterOrders = append(chapterOrders, chapter.ChapterOrder)
		coinAmounts = append(coinAmounts, chapter.CoinAmount)
		totalAmount += chapter.CoinAmount
	}

	// 检查用户余额
	accountResp, err := s.accountClient.GetAccount(ctx, &accountpb.GetAccountReq{
		UserId: req.UserId,
	})
	if err != nil {
		logger.LogErrorf("Failed to get account: %v", err)
		return &pb.PurchaseChapterResp{
			Code:    500,
			Message: "Failed to get account information",
		}, nil
	}

	if accountResp.Code != 200 {
		return &pb.PurchaseChapterResp{
			Code:    accountResp.Code,
			Message: accountResp.Message,
		}, nil
	}

	if accountResp.Account.CoinBalance < totalAmount {
		return &pb.PurchaseChapterResp{
			Code:    400,
			Message: fmt.Sprintf("Insufficient balance: required=%.2f, available=%.2f", totalAmount, accountResp.Account.CoinBalance),
		}, nil
	}

	// 先扣除书币
	deductReq := &accountpb.DeductCoinsReq{
		UserId:          req.UserId,
		Amount:          totalAmount,
		TransactionType: "purchase_chapter",
		Description:     fmt.Sprintf("Purchase chapters for book %s", req.BookId),
	}

	deductResp, err := s.accountClient.DeductCoins(ctx, deductReq)
	if err != nil {
		logger.LogErrorf("Failed to deduct coins: %v", err)
		return &pb.PurchaseChapterResp{
			Code:    500,
			Message: "Failed to deduct coins",
		}, nil
	}

	if deductResp.Code != 200 {
		return &pb.PurchaseChapterResp{
			Code:    deductResp.Code,
			Message: deductResp.Message,
		}, nil
	}

	// 创建购买订单
	var pbOrders []*pb.PurchaseOrder
	now := time.Now()

	for i, chapterOrder := range chapterOrders {
		orderId := fmt.Sprintf("PO_%d_%s_%d_%d", req.UserId, req.BookId, chapterOrder, now.Unix())

		order := &po.PurchaseOrder{
			OrderId:      orderId,
			AccountId:    accountResp.Account.AccountId,
			UserId:       req.UserId,
			OrderType:    common.OrderTypeChapter,
			BookId:       req.BookId,
			ChapterOrder: chapterOrder,
			CoinAmount:   coinAmounts[i],
			Status:       common.OrderStatusSuccess,
			CreatedAt:    now,
			UpdatedAt:    now,
		}

		// 创建订单记录
		err = s.dao.CreatePurchaseOrder(ctx, order)
		if err != nil {
			logger.LogErrorf("Failed to create purchase order: %v", err)
			return &pb.PurchaseChapterResp{
				Code:    500,
				Message: "Failed to create purchase orders",
			}, nil
		}

		pbOrders = append(pbOrders, s.convertPurchaseOrderToPB(order))
	}

	return &pb.PurchaseChapterResp{
		Code:    200,
		Message: "Purchase successful",
		Orders:  pbOrders,
	}, nil
}

// PurchaseMonthly 购买包月
func (s *PurchaseSvc) PurchaseMonthly(ctx context.Context, req *pb.PurchaseMonthlyReq) (*pb.PurchaseMonthlyResp, error) {
	if req.DurationDays <= 0 {
		req.DurationDays = 30 // 默认30天
	}

	// 检查用户余额
	accountResp, err := s.accountClient.GetAccount(ctx, &accountpb.GetAccountReq{
		UserId: req.UserId,
	})
	if err != nil {
		logger.LogErrorf("Failed to get account: %v", err)
		return &pb.PurchaseMonthlyResp{
			Code:    500,
			Message: "Failed to get account information",
		}, nil
	}

	if accountResp.Code != 200 {
		return &pb.PurchaseMonthlyResp{
			Code:    accountResp.Code,
			Message: accountResp.Message,
		}, nil
	}

	if accountResp.Account.CoinBalance < req.CoinAmount {
		return &pb.PurchaseMonthlyResp{
			Code:    400,
			Message: fmt.Sprintf("Insufficient balance: required=%.2f, available=%.2f", req.CoinAmount, accountResp.Account.CoinBalance),
		}, nil
	}

	// 创建包月订单
	now := time.Now()
	startTime := now
	endTime := now.AddDate(0, 0, int(req.DurationDays))
	orderId := fmt.Sprintf("MO_%d_%d", req.UserId, now.Unix())

	order := &po.VipMonthlyOrder{
		OrderId:      orderId,
		AccountId:    accountResp.Account.AccountId,
		UserId:       req.UserId,
		OrderType:    common.OrderTypeMonthly,
		CoinAmount:   req.CoinAmount,
		DurationDays: req.DurationDays,
		StartTime:    &startTime,
		EndTime:      &endTime,
		Status:       common.OrderStatusSuccess,
		CreatedAt:    now,
		UpdatedAt:    now,
	}

	err = s.dao.CreateVipMonthlyOrder(ctx, order)
	if err != nil {
		logger.LogErrorf("Failed to create monthly order: %v", err)
		return &pb.PurchaseMonthlyResp{
			Code:    500,
			Message: "Failed to create monthly order",
		}, nil
	}

	// 扣除书币
	deductReq := &accountpb.DeductCoinsReq{
		UserId:          req.UserId,
		Amount:          req.CoinAmount,
		OrderId:         order.OrderId,
		TransactionType: "purchase_monthly",
		Description:     fmt.Sprintf("Purchase monthly subscription for %d days", req.DurationDays),
	}

	deductResp, err := s.accountClient.DeductCoins(ctx, deductReq)
	if err != nil {
		logger.LogErrorf("Failed to deduct coins: %v", err)
		return &pb.PurchaseMonthlyResp{
			Code:    500,
			Message: "Failed to deduct coins",
		}, nil
	}

	if deductResp.Code != 200 {
		return &pb.PurchaseMonthlyResp{
			Code:    deductResp.Code,
			Message: deductResp.Message,
		}, nil
	}

	// 更新用户状态
	updateReq := &accountpb.UpdateUserStatusReq{
		UserId:            req.UserId,
		UserType:          3, // 包月用户
		MonthlyExpireTime: order.EndTime.Unix(),
	}

	_, err = s.accountClient.UpdateUserStatus(ctx, updateReq)
	if err != nil {
		logger.LogErrorf("Failed to update user status: %v", err)
	}

	return &pb.PurchaseMonthlyResp{
		Code:    200,
		Message: "Monthly subscription purchased successfully",
		Order:   s.convertVipMonthlyOrderToPB(order),
	}, nil
}

// PurchaseVip 购买VIP
func (s *PurchaseSvc) PurchaseVip(ctx context.Context, req *pb.PurchaseVipReq) (*pb.PurchaseVipResp, error) {
	if req.DurationDays <= 0 {
		req.DurationDays = 30 // 默认30天
	}

	// 检查用户余额
	accountResp, err := s.accountClient.GetAccount(ctx, &accountpb.GetAccountReq{
		UserId: req.UserId,
	})
	if err != nil {
		logger.LogErrorf("Failed to get account: %v", err)
		return &pb.PurchaseVipResp{
			Code:    500,
			Message: "Failed to get account information",
		}, nil
	}

	if accountResp.Code != 200 {
		return &pb.PurchaseVipResp{
			Code:    accountResp.Code,
			Message: accountResp.Message,
		}, nil
	}

	if accountResp.Account.CoinBalance < req.CoinAmount {
		return &pb.PurchaseVipResp{
			Code:    400,
			Message: fmt.Sprintf("Insufficient balance: required=%.2f, available=%.2f", req.CoinAmount, accountResp.Account.CoinBalance),
		}, nil
	}

	// 创建VIP订单
	now := time.Now()
	startTime := now
	endTime := now.AddDate(0, 0, int(req.DurationDays))
	orderId := fmt.Sprintf("VO_%d_%d", req.UserId, now.Unix())

	order := &po.VipMonthlyOrder{
		OrderId:      orderId,
		AccountId:    accountResp.Account.AccountId,
		UserId:       req.UserId,
		OrderType:    common.OrderTypeVip,
		CoinAmount:   req.CoinAmount,
		DurationDays: req.DurationDays,
		StartTime:    &startTime,
		EndTime:      &endTime,
		Status:       common.OrderStatusSuccess,
		CreatedAt:    now,
		UpdatedAt:    now,
	}

	err = s.dao.CreateVipMonthlyOrder(ctx, order)
	if err != nil {
		logger.LogErrorf("Failed to create VIP order: %v", err)
		return &pb.PurchaseVipResp{
			Code:    500,
			Message: "Failed to create VIP order",
		}, nil
	}

	// 扣除书币
	deductReq := &accountpb.DeductCoinsReq{
		UserId:          req.UserId,
		Amount:          req.CoinAmount,
		OrderId:         order.OrderId,
		TransactionType: "purchase_vip",
		Description:     fmt.Sprintf("Purchase VIP subscription for %d days", req.DurationDays),
	}

	deductResp, err := s.accountClient.DeductCoins(ctx, deductReq)
	if err != nil {
		logger.LogErrorf("Failed to deduct coins: %v", err)
		return &pb.PurchaseVipResp{
			Code:    500,
			Message: "Failed to deduct coins",
		}, nil
	}

	if deductResp.Code != 200 {
		return &pb.PurchaseVipResp{
			Code:    deductResp.Code,
			Message: deductResp.Message,
		}, nil
	}

	// 更新用户状态
	updateReq := &accountpb.UpdateUserStatusReq{
		UserId:        req.UserId,
		UserType:      2, // VIP用户
		VipExpireTime: order.EndTime.Unix(),
	}

	_, err = s.accountClient.UpdateUserStatus(ctx, updateReq)
	if err != nil {
		logger.LogErrorf("Failed to update user status: %v", err)
	}

	return &pb.PurchaseVipResp{
		Code:    200,
		Message: "VIP subscription purchased successfully",
		Order:   s.convertVipMonthlyOrderToPB(order),
	}, nil
}

// GetPurchaseOrders 获取购买订单列表
func (s *PurchaseSvc) GetPurchaseOrders(ctx context.Context, req *pb.GetPurchaseOrdersReq) (*pb.GetPurchaseOrdersResp, error) {
	// 获取购买订单列表
	orders, total, err := s.dao.GetPurchaseOrdersByUserId(ctx, req.UserId, req.Page, req.PageSize, req.BookId)
	if err != nil {
		logger.LogErrorf("Failed to get purchase orders: %v", err)
		return &pb.GetPurchaseOrdersResp{
			Code:    500,
			Message: "Failed to get purchase orders",
		}, nil
	}

	// 转换为protobuf格式
	var pbOrders []*pb.PurchaseOrder
	for _, order := range orders {
		pbOrders = append(pbOrders, s.convertPurchaseOrderToPB(order))
	}

	return &pb.GetPurchaseOrdersResp{
		Code:    200,
		Message: "Success",
		Orders:  pbOrders,
		Total:   total,
	}, nil
}

// GetVipMonthlyOrders 获取VIP/包月订单列表
func (s *PurchaseSvc) GetVipMonthlyOrders(ctx context.Context, req *pb.GetVipMonthlyOrdersReq) (*pb.GetVipMonthlyOrdersResp, error) {
	// 获取VIP/包月订单列表
	orders, total, err := s.dao.GetVipMonthlyOrdersByUserId(ctx, req.UserId, req.Page, req.PageSize, req.OrderType)
	if err != nil {
		logger.LogErrorf("Failed to get vip/monthly orders: %v", err)
		return &pb.GetVipMonthlyOrdersResp{
			Code:    500,
			Message: "Failed to get vip/monthly orders",
		}, nil
	}

	// 转换为protobuf格式
	var pbOrders []*pb.VipMonthlyOrder
	for _, order := range orders {
		pbOrders = append(pbOrders, s.convertVipMonthlyOrderToPB(order))
	}

	return &pb.GetVipMonthlyOrdersResp{
		Code:    200,
		Message: "Success",
		Orders:  pbOrders,
		Total:   total,
	}, nil
}

// CheckChapterPurchased 检查章节购买状态
func (s *PurchaseSvc) CheckChapterPurchased(ctx context.Context, req *pb.CheckChapterPurchasedReq) (*pb.CheckChapterPurchasedResp, error) {
	// 检查章节是否已购买
	order, err := s.dao.CheckChapterPurchased(ctx, req.UserId, req.BookId, req.ChapterOrder)
	if err != nil {
		logger.LogErrorf("Failed to check chapter purchased: %v", err)
		return &pb.CheckChapterPurchasedResp{
			Code:    500,
			Message: "Failed to check chapter purchased",
		}, nil
	}

	isPurchased := order != nil
	var purchasedAt int64
	var orderId string

	if isPurchased {
		purchasedAt = order.CreatedAt.Unix()
		orderId = order.OrderId
	}

	// 检查VIP状态
	vipOrder, _ := s.dao.CheckVipStatus(ctx, req.UserId)
	isVip := vipOrder != nil

	// 检查包月状态
	monthlyOrder, _ := s.dao.CheckMonthlyStatus(ctx, req.UserId)
	isMonthly := monthlyOrder != nil

	return &pb.CheckChapterPurchasedResp{
		Code:        200,
		Message:     "Success",
		IsPurchased: isPurchased,
		PurchasedAt: purchasedAt,
		IsMonthly:   isMonthly,
		IsVip:       isVip,
		OrderId:     orderId,
	}, nil
}

// CheckVipStatus 检查VIP状态
func (s *PurchaseSvc) CheckVipStatus(ctx context.Context, req *pb.CheckVipStatusReq) (*pb.CheckVipStatusResp, error) {
	// 检查VIP状态
	order, err := s.dao.CheckVipStatus(ctx, req.UserId)
	if err != nil {
		logger.LogErrorf("Failed to check vip status: %v", err)
		return &pb.CheckVipStatusResp{
			Code:    500,
			Message: "Failed to check vip status",
		}, nil
	}

	isActive := order != nil
	var startTime, endTime int64
	var orderId string

	if isActive {
		if order.StartTime != nil {
			startTime = order.StartTime.Unix()
		}
		if order.EndTime != nil {
			endTime = order.EndTime.Unix()
		}
		orderId = order.OrderId
	}

	return &pb.CheckVipStatusResp{
		Code:      200,
		Message:   "Success",
		IsActive:  isActive,
		StartTime: startTime,
		EndTime:   endTime,
		OrderId:   orderId,
	}, nil
}

// CheckMonthlyStatus 检查包月状态
func (s *PurchaseSvc) CheckMonthlyStatus(ctx context.Context, req *pb.CheckMonthlyStatusReq) (*pb.CheckMonthlyStatusResp, error) {
	// 检查包月状态
	order, err := s.dao.CheckMonthlyStatus(ctx, req.UserId)
	if err != nil {
		logger.LogErrorf("Failed to check monthly status: %v", err)
		return &pb.CheckMonthlyStatusResp{
			Code:    500,
			Message: "Failed to check monthly status",
		}, nil
	}

	isActive := order != nil
	var startTime, endTime int64
	var orderId string

	if isActive {
		if order.StartTime != nil {
			startTime = order.StartTime.Unix()
		}
		if order.EndTime != nil {
			endTime = order.EndTime.Unix()
		}
		orderId = order.OrderId
	}

	return &pb.CheckMonthlyStatusResp{
		Code:      200,
		Message:   "Success",
		IsActive:  isActive,
		StartTime: startTime,
		EndTime:   endTime,
		OrderId:   orderId,
	}, nil
}

// GetPurchasedChapters 获取已购买的章节列表
func (s *PurchaseSvc) GetPurchasedChapters(ctx context.Context, req *pb.GetPurchasedChaptersReq) (*pb.GetPurchasedChaptersResp, error) {
	// 获取已购买的章节列表
	orders, total, err := s.dao.GetPurchasedChaptersByBookId(ctx, req.UserId, req.BookId, req.Page, req.PageSize)
	if err != nil {
		logger.LogErrorf("Failed to get purchased chapters: %v", err)
		return &pb.GetPurchasedChaptersResp{
			Code:    500,
			Message: "Failed to get purchased chapters",
		}, nil
	}

	// 转换为protobuf格式
	var chapters []*pb.ChapterPurchaseInfo
	for _, order := range orders {
		chapter := &pb.ChapterPurchaseInfo{
			OrderId:      order.OrderId,
			ChapterId:    order.ChapterId,
			ChapterTitle: order.ChapterTitle,
			ChapterOrder: order.ChapterOrder,
			CoinAmount:   order.CoinAmount,
			PurchasedAt:  order.CreatedAt.Unix(),
			IsMonthly:    false, // 这里需要根据实际业务逻辑判断
			IsVip:        false, // 这里需要根据实际业务逻辑判断
		}
		chapters = append(chapters, chapter)
	}

	return &pb.GetPurchasedChaptersResp{
		Code:     200,
		Message:  "Success",
		Chapters: chapters,
		Total:    total,
	}, nil
}

// convertPurchaseOrderToPB 转换PurchaseOrder为protobuf格式
func (s *PurchaseSvc) convertPurchaseOrderToPB(order *po.PurchaseOrder) *pb.PurchaseOrder {
	return &pb.PurchaseOrder{
		OrderId:      order.OrderId,
		AccountId:    order.AccountId,
		UserId:       order.UserId,
		OrderType:    order.OrderType,
		BookId:       order.BookId,
		BookName:     order.BookName,
		ChapterId:    order.ChapterId,
		ChapterTitle: order.ChapterTitle,
		ChapterOrder: order.ChapterOrder,
		CoinAmount:   order.CoinAmount,
		Status:       order.Status,
		CreatedAt:    order.CreatedAt.Unix(),
		UpdatedAt:    order.UpdatedAt.Unix(),
	}
}

// convertVipMonthlyOrderToPB 转换VipMonthlyOrder为protobuf格式
func (s *PurchaseSvc) convertVipMonthlyOrderToPB(order *po.VipMonthlyOrder) *pb.VipMonthlyOrder {
	pbOrder := &pb.VipMonthlyOrder{
		OrderId:      order.OrderId,
		AccountId:    order.AccountId,
		UserId:       order.UserId,
		OrderType:    order.OrderType,
		CoinAmount:   order.CoinAmount,
		DurationDays: order.DurationDays,
		Status:       order.Status,
		CreatedAt:    order.CreatedAt.Unix(),
		UpdatedAt:    order.UpdatedAt.Unix(),
	}

	if order.StartTime != nil {
		pbOrder.StartTime = order.StartTime.Unix()
	}

	if order.EndTime != nil {
		pbOrder.EndTime = order.EndTime.Unix()
	}

	return pbOrder
}
