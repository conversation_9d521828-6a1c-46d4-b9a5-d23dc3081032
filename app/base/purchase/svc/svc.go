package svc

import (
	"context"
	accountapi "creativematrix.com/beyondreading/app/base/account/api"
	accountpb "creativematrix.com/beyondreading/proto/account"

	"creativematrix.com/beyondreading/app/base/purchase/conf"
	"creativematrix.com/beyondreading/app/base/purchase/dao"
)

type PurchaseSvc struct {
	conf          *conf.Config
	dao           *dao.Dao
	accountClient accountpb.AccountClient
}

func Load(c *conf.Config) *PurchaseSvc {
	client, err := accountapi.NewClient(c.Base)
	if err != nil {
		panic(err)
	}
	svc := &PurchaseSvc{
		conf:          c,
		dao:           dao.Load(c),
		accountClient: client,
	}

	return svc
}

func (s *PurchaseSvc) Ping(ctx context.Context) error {
	return s.dao.Ping(ctx)
}

func (s *PurchaseSvc) Close() {
	s.dao.Close()
}
