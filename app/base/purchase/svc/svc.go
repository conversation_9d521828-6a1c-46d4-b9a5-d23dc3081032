package svc

import (
	"context"

	"creativematrix.com/beyondreading/app/base/purchase/conf"
	"creativematrix.com/beyondreading/app/base/purchase/dao"
)

type PurchaseSvc struct {
	conf *conf.Config
	dao  *dao.Dao
	accountClient accountpb.AccountClient
}

func Load(c *conf.Config) *PurchaseSvc {
	svc := &PurchaseSvc{
		conf: c,
		dao:  dao.Load(c),
		accountClient := accountapi.NewClient(c.Base)
	}

	return svc
}

func (s *PurchaseSvc) Ping(ctx context.Context) error {
	return s.dao.Ping(ctx)
}

func (s *PurchaseSvc) Close() {
	s.dao.Close()
}
