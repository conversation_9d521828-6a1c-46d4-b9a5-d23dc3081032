mongodbBooks = "mongodb://127.0.0.1:27017/beyondreading"
mongodbBookshelf = "mongodb://127.0.0.1:27017/beyondreading"
mongodbChapters = "mongodb://127.0.0.1:27017/beyondreading"
mongodbPayment = "mongodb://127.0.0.1:27017/beyondreading"

userTokenSecret = "N7j8yySsgps8nAsP"

baseImgUrl = "http://devimg.91quliao.com/"
env = "dev"

isWinDebug = true

aesKey = "lAEhdjtiRbuq2owpi0g5Lm2z6iLbcfbm"
md5Key = "lAEhdjtiRbuq2owpi0g5Lm2z6iLbcfbm"

webAesKey = "ouyPAF6pkXKybWjaAnZvhXcWAsJKcIgx"
webMd5Key = "PGncRld3YLMdF9bRlvFFgcW6Lm3rkHFk"

pcAesKey = "Zs8PpsGNPYIyrz2Dne3Pd6RkPaVVGtTf"
pcMd5Key = "UPBnYZ68sOvqPObVEj5U3ZYZt5uwM7FX"

## 加密明文是否返回开关
lightSwitch = true

## md5 签名开关 （request）
md5Switch = true

## Aes 开关（resposne）
aesSwitch = true

innerKey = "K@ieaYbXqF0SufSCt#I4pIE!5Z!HFjxfP^!qulgxbyQ3Cio9m@5krtOr2dhS@#6E1$E7**uf5lJqBK#!BR!lkoWgnZirtqcKb%x"

beginId = 2021111

## 路由白名单
whiteList = ["/callback/paySingleOrder",
    "/callback/third/duliday",
    "/callback/third/xinxin",
    "/payment/notify/weixinpay",
    "/payment/notify/alipay",
    "/chat/audioFile",
    "/chat/videoFile",
    "/live/robot/joinRoom",
    "/live/robot/leaveRoom",
    "/live/auto/closeRoom",
    "/heroCn/user",
    "/heroCn/pay",
    "/heroCn/order"
]

[port]
http = ":8081"
debug = ":8082"
grpc = ":8083"

[cache]
address = ":6379"
password = "123456"
maxIdle = 3
maxActive = 1000

[jaeger]
collector = "http://************:14268/api/traces"
disabled = false

[etcd]
addrs = ["127.0.0.1:2479","127.0.0.1:2579","127.0.0.1:2679"]

[elasticSearch]
userName = ""
password = ""
[elasticSearch.es]
default = ["http://************:9200"]

[liveES]
userName = ""
password = ""
[liveES.es]
default = ["http://************:9200"]


[Rcould]
appKey = "lmxuhwagl6q8d"
appSecret = "HUtCCS0pBaG"

[GeTui]
appId = "7JJFDxT3iZ6oiDdsq9LAH7"
appKey = "EpzenRCByU7wUubkhloCUA"
appSecret = "DroHXTBxw57WLwgqtZNtm8"
masterSecret = "S0SNZwxbnY5jE0OB6OOpg1"

#数美
[sm]
accessKey = "i6X9AzyT8o0I61X7uIqF"
txtUrl = "http://api-text-sh.fengkongcloud.com/text/v4"
imgUrl = "http://api-img-sh.fengkongcloud.com/v2/saas/anti_fraud/img"

audioCallback = "http://lql-api-chat.dev_golang.91quliao.com/chat/audioFile" ##音频回调
audioUrl = "http://api-audio-sh.fengkongcloud.com/v2/saas/anti_fraud/audio"

videoCallback = "http://lql-api-chat.dev_golang.91quliao.com/chat/videoFile"#视频回调
videoUrl = "http://api-video-sh.fengkongcloud.com/v2/saas/anti_fraud/video"

#网宿
[wangsu]
ak = "WldrUfsKX8n2dFjMbqSNNtt5qLBbUQZ6Ovnv"
sk = "HPsil0HyiTwSBmEOTPlfWfOt0kMDc8VsJTWImYOP5GoWI7LAb6T4g4zsjppsuB0u"
upDomain = "zhuita01.up27.v1.wcsapi.com"
mgrDomain = "zhuita01.mgr27.v1.wcsapi.com"
bucket = "zhuitagolangdev"
logBucket = "zhuitagolangdev-clientlog"
endPoint = "s3-cn-east-1.wcsapi.com"
cdnDomain = "http://devimg.91quliao.com"
pullUrl = [
    "pullws.91quliao.com/live/stream_",
    "pull01ws.91quliao.com/live/stream_",
    "pull02ws.91quliao.com/live/stream_",
    "pull03ws.91quliao.com/live/stream_",
    "pull04ws.91quliao.com/live/stream_",
    "pull05ws.91quliao.com/live/stream_",
    "pull06ws.91quliao.com/live/stream_",
    "pull07ws.91quliao.com/live/stream_",
    "pull08ws.91quliao.com/live/stream_",
    "pull09ws.91quliao.com/live/stream_",
    "pull10ws.91quliao.com/live/stream_",
]

#独立日
[duliday]
appId = "tianjishejiao"
baseUrl = "https://testopenapi.duliday.com"
signUid = 2205100100945676
singlePayCallback = "http://lql-api-purchase.dev_golang.91quliao.com/callback/third/duliday"
privateKeyStr = "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC3ymhvXIoWEkKWjOKemcgM9tQ4JBGrLEWpn4NQb1L1Uau1HN2zKzMieyxk3ZCBZbDJFCFVqLv86PBY4DmU8AVlkOuumSEAEI2eF8Ls6W/g0iHAB5EZq8k/61MdDQ5cYD54WzoBUzP6ijzupEwyJexWI6CBoWHbo+BH54F1R62ImLUOvXO1ZuqA/IEbl9TMCLNUzIW1oVnZz9UvK8kqjmjBAC6QsWaDcrfW0pzK9Y2ibCjHsdeT6MLBdEpBeQBK4MyT5bbUExDO+CI0HWb7QtSOaXtvYp1tHqaw9cPUNBgIrLXq3gSpH6BctMLwcqMHXsFo6Z7Giw5mFE0wpU3GD7+9AgMBAAECggEAEX/UMcY935wdevgvG6rIwXM/AZltlT7B40Ss3Bu9al48WyJEearDU6UmtPPjrm5gRsUD54QJYitLtyqiUOwJl4IU7qrl2Xu6ADOC+8u+O0nGrOILjsYsg0mEwF9d3q3LROpqu3WQnWpMIuzB1ISH3CvIPI+BIciYsj/4s5H/k+HMhJ18T8n0zTMIWbSP/rTiIpjiV2bEZYGTfIQB9jIS+S8xkL6LLTnl+WUWisBgySC7HPy6TpdXUZ7ULrEhlGXr4tz/CF19cZzv+7t11eHr+XsE6oJj60Gix/WIRAGh3IiSuLOghQoSbR51MnS58YNBB22MJXpQj9m1CPUi+AMOgQKBgQD+qyIWhTS2txXhWFODsFgZT1qIG72xty3Fv1a1Tbtkn5UYGsWUqUaavsgSbRzc+AUmysVn5qzSBH9B1x5kqjzYfew+CxgUzQHA57Xgm2oYXcePmjj+Hsc+utoMMKnre3My+M+r63FkVS63VtjZOKLpPjhcue0hhOJjxD8keqx/AwKBgQC4wGggQQFwipBoGgonnsWSfm3ZfKIwKkPTIBloHvTF91G0AGtUpsOc9XQ5onxRwKh5jFsSAIfXbUeAo+Zpg3NeEFvbBtNciWnXwMaj25cElW7JePckp2KlRTFeodIjb+odzll0nGzxj11Au1OzO9AWvnFwh8HHtCNFNif4LkIqPwKBgD5jQDOSKGrGiWauE6+EKODybrQ/T4ZU4LK8AArkYw7whT7leBfGLFTGfg79if+cBu8M57VfbmD41xATGhABIGzM1G4Sm7Ck1FQkr4q5qBEWEEheuK2QbrEwZzBrT4UvtfX7vCxavmmc8Jfbf74fQNGw+2/D4JLAkhFsxycxokZvAoGBAJoVTkJoNM0po1pPLVzx3BFbhD445gOJnFt+j/H4r2hT5j5bgyhslQRY6gAPdeOw7FHDL0kKTEDtEQ9GoqopSZSPzCwVuz2YFUAttP1/7KmT/3vkeSqGt4zi8lPcXifT9Mu7B3xr4ahtSvbrxNjc76WoW2/gqUrm/+t/SUKBQgl9AoGBAPjWSRPus4DkwtTQl7Buj5n//xD6dkGouLI4LXnOIfEYl4ZJ2G+HO27rjgO6THOY12NtRwtcQ8Lb4nsjKR3M9sasapbzg7u1p7AAbHriwxBV2T5jqNG9pfSYSkwJOjq49OODEgPvcOdyMi6PUR9bLq0HJ3OEv75yq08Wz4epZuS7"
publicKeyStr = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAt8pob1yKFhJClozinpnIDPbUOCQRqyxFqZ+DUG9S9VGrtRzdsyszInssZN2QgWWwyRQhVai7/OjwWOA5lPAFZZDrrpkhABCNnhfC7Olv4NIhwAeRGavJP+tTHQ0OXGA+eFs6AVMz+oo87qRMMiXsViOggaFh26PgR+eBdUetiJi1Dr1ztWbqgPyBG5fUzAizVMyFtaFZ2c/VLyvJKo5owQAukLFmg3K31tKcyvWNomwox7HXk+jCwXRKQXkASuDMk+W21BMQzvgiNB1m+0LUjml7b2KdbR6msPXD1DQYCKy16t4EqR+gXLTC8HKjB17BaOmexosOZhRNMKVNxg+/vQIDAQAB"

#薪信
[xinxin]
merchantNo = "MI278377174086815744"
baseUrl = "http://taxpay.yzxxcloud.com/api"
key = "iagkok2ld2ncpsot"
callback = "http://lql-api-purchase.dev_golang.91quliao.com/callback/third/xinxin"
privateKeyStr = "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"
publicKeyStr = "TUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUFwRWQ1bmlkMVAvalF6TG9UdW5rbjB3RmwxYU1xRS9ZVzVwV2pPdHRDd0dvRTdtSng1NHJac3ZQWGY0cUJBYzJmZFZWQWpLbk9MdXhuaVR0alV5Ly9tQ2ZJSzRZRFZ5YnJCcjNwZHNRSHVTNkFWcHM0VXJzcVk0Nzh1alAyWG1NbUhjbDIxZlBpbnJEMDNUK0NMcFhramFJOTFyRHFlWGNFS0dmN0pMSVBkSXVvMisrNUx1SkMrSldoQU1JUVM5WXhsRHI3V3FBMGFxYlhXNXBhdmNuSEkwWFd4ZXZrVmxrZTAzNGpLV3N2TnhWdnk3akNNNDRLTno2YXFXU1V5ZUtUUXptdVU1WDZPd3BXcG8yc2I0V04xMERzTzl4NW9zNmdEcVprRkJkQjlSYjRzaEFmcmpieXFtdExpNGlxUmt3bm42NS9HQ0dTaWRUZ1FZTjRtUXdxL1FJREFRQUI="

[Kafka]
Point = ["************:9192"]
Username = ""
Password = ""

[rabbitmq]
url = "amqp://admin:admin@127.0.0.1:5672"
[rabbitmq.matrixEx]
name = "matrix-exchange"
type = "direct"


[mysql]
driver = "mysql"
maxOpen = 70
maxIdle = 7
maxIdleTime = 5
maxLifetime = 5
queryTimeout = 3
execTimeout = 3
tranTimeout = 3
dsn = [
    "root:123456@tcp(127.0.0.1:3306)/beyondreading?parseTime=true&loc=Asia%2FShanghai&timeout=10s",
]



[systemUser]
msgId = "13"
noId = "1"
mId = "61668aad169d3aecb0ff0af4"


[globalChatRoom]
ids = ["160000"]

[redisBookshelf]
address = "************:6379"
password = "123456"
maxIdle = 3
maxActive = 1000

[redisChapters]
address = "************:6379"
password = "123456"
maxIdle = 3
maxActive = 1000

# 第三方ID加密密钥
[msgKey]
key = "957ec696a"

[dingtalk.rank]
accessToken = "d59df700e68777e41835e50009137930000c29c9cfb5503dc4af4098e697ba70"
accessSecret = "SEC6a98d74be0a91403ce1484d27bea0792d09c7cb1a1a906068d21c64fdc349163"

[tinge.withdraw]
url = "http://**************:19004/withdraw/save"
appId = "xinlian"
appSecret = "2d83b8bf98db419f9bbaf3703e864907"

# 支付跳转地址
[payJump]
full = "/money" # 全屏
half = "/messagePay" # 半屏
domains = [
    { domain = "https://devh5one.17xingxuan.com", channel = "xingxuan", probability = 70 },
    { domain = "https://devh5one.17youni.com", channel = "yijian", probability = 30 },
]

[appVersionLimit]
prettyNo = "2.0.2"
flowCardLimit = "2.0.2"