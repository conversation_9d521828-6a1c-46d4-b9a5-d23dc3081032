syntax = "proto3";
package supplyment;
option go_package = "creativematrix.com/beyondreading/proto/supplyment";
import "proto/pbcommon/common.proto";

//import "google/api/annotations.proto";


service supplyment {
  rpc AddBookChapters(AddBookChaptersReq) returns (BookChaptersResp);
  rpc RemoveBookChapters(RemoveBookChapters) returns(BookChaptersResp);
  rpc addBook(pbcommon.Book) returns (AddBookResp);
}


message AddBookChaptersReq {

}