// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: proto/account/account.proto

package account

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 账户信息
type AccountInfo struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	AccountId         uint64                 `protobuf:"varint,1,opt,name=accountId,proto3" json:"accountId,omitempty"`
	UserId            uint64                 `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`
	CoinBalance       float64                `protobuf:"fixed64,3,opt,name=coinBalance,proto3" json:"coinBalance,omitempty"`             // 书币余额
	TotalRecharged    float64                `protobuf:"fixed64,4,opt,name=totalRecharged,proto3" json:"totalRecharged,omitempty"`       // 总充值金额
	TotalConsumed     float64                `protobuf:"fixed64,5,opt,name=totalConsumed,proto3" json:"totalConsumed,omitempty"`         // 总消费金额
	Status            int32                  `protobuf:"varint,6,opt,name=status,proto3" json:"status,omitempty"`                        // 账户状态：1-正常，2-冻结，3-注销
	UserType          int32                  `protobuf:"varint,7,opt,name=userType,proto3" json:"userType,omitempty"`                    // 用户类型：1-普通用户，2-VIP用户，3-包月用户
	UserLevel         int32                  `protobuf:"varint,8,opt,name=userLevel,proto3" json:"userLevel,omitempty"`                  // 用户等级（根据消费书币计算，1000书币一级）
	VipExpireTime     int64                  `protobuf:"varint,9,opt,name=vipExpireTime,proto3" json:"vipExpireTime,omitempty"`          // VIP过期时间（Unix时间戳）
	MonthlyExpireTime int64                  `protobuf:"varint,10,opt,name=monthlyExpireTime,proto3" json:"monthlyExpireTime,omitempty"` // 包月过期时间（Unix时间戳）
	CreatedAt         int64                  `protobuf:"varint,11,opt,name=createdAt,proto3" json:"createdAt,omitempty"`                 // 创建时间
	UpdatedAt         int64                  `protobuf:"varint,12,opt,name=updatedAt,proto3" json:"updatedAt,omitempty"`                 // 更新时间
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *AccountInfo) Reset() {
	*x = AccountInfo{}
	mi := &file_proto_account_account_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountInfo) ProtoMessage() {}

func (x *AccountInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_account_account_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountInfo.ProtoReflect.Descriptor instead.
func (*AccountInfo) Descriptor() ([]byte, []int) {
	return file_proto_account_account_proto_rawDescGZIP(), []int{0}
}

func (x *AccountInfo) GetAccountId() uint64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *AccountInfo) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *AccountInfo) GetCoinBalance() float64 {
	if x != nil {
		return x.CoinBalance
	}
	return 0
}

func (x *AccountInfo) GetTotalRecharged() float64 {
	if x != nil {
		return x.TotalRecharged
	}
	return 0
}

func (x *AccountInfo) GetTotalConsumed() float64 {
	if x != nil {
		return x.TotalConsumed
	}
	return 0
}

func (x *AccountInfo) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *AccountInfo) GetUserType() int32 {
	if x != nil {
		return x.UserType
	}
	return 0
}

func (x *AccountInfo) GetUserLevel() int32 {
	if x != nil {
		return x.UserLevel
	}
	return 0
}

func (x *AccountInfo) GetVipExpireTime() int64 {
	if x != nil {
		return x.VipExpireTime
	}
	return 0
}

func (x *AccountInfo) GetMonthlyExpireTime() int64 {
	if x != nil {
		return x.MonthlyExpireTime
	}
	return 0
}

func (x *AccountInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *AccountInfo) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

// 账户日志
type AccountLog struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	LogId           uint64                 `protobuf:"varint,1,opt,name=logId,proto3" json:"logId,omitempty"`
	AccountId       uint64                 `protobuf:"varint,2,opt,name=accountId,proto3" json:"accountId,omitempty"`
	UserId          uint64                 `protobuf:"varint,3,opt,name=userId,proto3" json:"userId,omitempty"`
	TransactionType string                 `protobuf:"bytes,4,opt,name=transactionType,proto3" json:"transactionType,omitempty"` // 交易类型：recharge, purchase_chapter, purchase_monthly, purchase_vip
	Amount          float64                `protobuf:"fixed64,5,opt,name=amount,proto3" json:"amount,omitempty"`                 // 变动金额（正数为增加，负数为减少）
	BalanceBefore   float64                `protobuf:"fixed64,6,opt,name=balanceBefore,proto3" json:"balanceBefore,omitempty"`   // 变动前余额
	BalanceAfter    float64                `protobuf:"fixed64,7,opt,name=balanceAfter,proto3" json:"balanceAfter,omitempty"`     // 变动后余额
	OrderId         string                 `protobuf:"bytes,8,opt,name=orderId,proto3" json:"orderId,omitempty"`                 // 关联订单ID
	BookId          string                 `protobuf:"bytes,9,opt,name=bookId,proto3" json:"bookId,omitempty"`                   // 书籍ID
	ChapterId       string                 `protobuf:"bytes,10,opt,name=chapterId,proto3" json:"chapterId,omitempty"`            // 章节ID
	Description     string                 `protobuf:"bytes,11,opt,name=description,proto3" json:"description,omitempty"`        // 描述
	ExtraData       string                 `protobuf:"bytes,12,opt,name=extraData,proto3" json:"extraData,omitempty"`            // 额外数据（JSON格式）
	CreatedAt       int64                  `protobuf:"varint,13,opt,name=createdAt,proto3" json:"createdAt,omitempty"`           // 创建时间
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *AccountLog) Reset() {
	*x = AccountLog{}
	mi := &file_proto_account_account_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountLog) ProtoMessage() {}

func (x *AccountLog) ProtoReflect() protoreflect.Message {
	mi := &file_proto_account_account_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountLog.ProtoReflect.Descriptor instead.
func (*AccountLog) Descriptor() ([]byte, []int) {
	return file_proto_account_account_proto_rawDescGZIP(), []int{1}
}

func (x *AccountLog) GetLogId() uint64 {
	if x != nil {
		return x.LogId
	}
	return 0
}

func (x *AccountLog) GetAccountId() uint64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *AccountLog) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *AccountLog) GetTransactionType() string {
	if x != nil {
		return x.TransactionType
	}
	return ""
}

func (x *AccountLog) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *AccountLog) GetBalanceBefore() float64 {
	if x != nil {
		return x.BalanceBefore
	}
	return 0
}

func (x *AccountLog) GetBalanceAfter() float64 {
	if x != nil {
		return x.BalanceAfter
	}
	return 0
}

func (x *AccountLog) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *AccountLog) GetBookId() string {
	if x != nil {
		return x.BookId
	}
	return ""
}

func (x *AccountLog) GetChapterId() string {
	if x != nil {
		return x.ChapterId
	}
	return ""
}

func (x *AccountLog) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *AccountLog) GetExtraData() string {
	if x != nil {
		return x.ExtraData
	}
	return ""
}

func (x *AccountLog) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

// 充值订单
type RechargeOrder struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	OrderId        string                 `protobuf:"bytes,1,opt,name=orderId,proto3" json:"orderId,omitempty"`
	AccountId      uint64                 `protobuf:"varint,2,opt,name=accountId,proto3" json:"accountId,omitempty"`
	UserId         uint64                 `protobuf:"varint,3,opt,name=userId,proto3" json:"userId,omitempty"`
	Amount         float64                `protobuf:"fixed64,4,opt,name=amount,proto3" json:"amount,omitempty"`               // 充值金额（人民币）
	CoinAmount     float64                `protobuf:"fixed64,5,opt,name=coinAmount,proto3" json:"coinAmount,omitempty"`       // 获得书币数量
	ExchangeRate   float32                `protobuf:"fixed32,6,opt,name=exchangeRate,proto3" json:"exchangeRate,omitempty"`   // 兑换比例
	PaymentMethod  string                 `protobuf:"bytes,7,opt,name=paymentMethod,proto3" json:"paymentMethod,omitempty"`   // 支付方式
	PaymentOrderId string                 `protobuf:"bytes,8,opt,name=paymentOrderId,proto3" json:"paymentOrderId,omitempty"` // 第三方支付订单ID
	Status         int32                  `protobuf:"varint,9,opt,name=status,proto3" json:"status,omitempty"`                // 订单状态：1-待支付，2-支付成功，3-支付失败，4-已退款
	PaidAt         int64                  `protobuf:"varint,10,opt,name=paidAt,proto3" json:"paidAt,omitempty"`               // 支付时间
	CreatedAt      int64                  `protobuf:"varint,11,opt,name=createdAt,proto3" json:"createdAt,omitempty"`         // 创建时间
	UpdatedAt      int64                  `protobuf:"varint,12,opt,name=updatedAt,proto3" json:"updatedAt,omitempty"`         // 更新时间
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *RechargeOrder) Reset() {
	*x = RechargeOrder{}
	mi := &file_proto_account_account_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RechargeOrder) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RechargeOrder) ProtoMessage() {}

func (x *RechargeOrder) ProtoReflect() protoreflect.Message {
	mi := &file_proto_account_account_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RechargeOrder.ProtoReflect.Descriptor instead.
func (*RechargeOrder) Descriptor() ([]byte, []int) {
	return file_proto_account_account_proto_rawDescGZIP(), []int{2}
}

func (x *RechargeOrder) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *RechargeOrder) GetAccountId() uint64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *RechargeOrder) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *RechargeOrder) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *RechargeOrder) GetCoinAmount() float64 {
	if x != nil {
		return x.CoinAmount
	}
	return 0
}

func (x *RechargeOrder) GetExchangeRate() float32 {
	if x != nil {
		return x.ExchangeRate
	}
	return 0
}

func (x *RechargeOrder) GetPaymentMethod() string {
	if x != nil {
		return x.PaymentMethod
	}
	return ""
}

func (x *RechargeOrder) GetPaymentOrderId() string {
	if x != nil {
		return x.PaymentOrderId
	}
	return ""
}

func (x *RechargeOrder) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *RechargeOrder) GetPaidAt() int64 {
	if x != nil {
		return x.PaidAt
	}
	return 0
}

func (x *RechargeOrder) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *RechargeOrder) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

// 获取账户信息
type GetAccountReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        uint64                 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAccountReq) Reset() {
	*x = GetAccountReq{}
	mi := &file_proto_account_account_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountReq) ProtoMessage() {}

func (x *GetAccountReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_account_account_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountReq.ProtoReflect.Descriptor instead.
func (*GetAccountReq) Descriptor() ([]byte, []int) {
	return file_proto_account_account_proto_rawDescGZIP(), []int{3}
}

func (x *GetAccountReq) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type GetAccountResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Account       *AccountInfo           `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAccountResp) Reset() {
	*x = GetAccountResp{}
	mi := &file_proto_account_account_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountResp) ProtoMessage() {}

func (x *GetAccountResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_account_account_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountResp.ProtoReflect.Descriptor instead.
func (*GetAccountResp) Descriptor() ([]byte, []int) {
	return file_proto_account_account_proto_rawDescGZIP(), []int{4}
}

func (x *GetAccountResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetAccountResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetAccountResp) GetAccount() *AccountInfo {
	if x != nil {
		return x.Account
	}
	return nil
}

// 创建账户
type CreateAccountReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        uint64                 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAccountReq) Reset() {
	*x = CreateAccountReq{}
	mi := &file_proto_account_account_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAccountReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAccountReq) ProtoMessage() {}

func (x *CreateAccountReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_account_account_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAccountReq.ProtoReflect.Descriptor instead.
func (*CreateAccountReq) Descriptor() ([]byte, []int) {
	return file_proto_account_account_proto_rawDescGZIP(), []int{5}
}

func (x *CreateAccountReq) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type CreateAccountResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Account       *AccountInfo           `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAccountResp) Reset() {
	*x = CreateAccountResp{}
	mi := &file_proto_account_account_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAccountResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAccountResp) ProtoMessage() {}

func (x *CreateAccountResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_account_account_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAccountResp.ProtoReflect.Descriptor instead.
func (*CreateAccountResp) Descriptor() ([]byte, []int) {
	return file_proto_account_account_proto_rawDescGZIP(), []int{6}
}

func (x *CreateAccountResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CreateAccountResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CreateAccountResp) GetAccount() *AccountInfo {
	if x != nil {
		return x.Account
	}
	return nil
}

// 充值
type RechargeReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        uint64                 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
	Amount        float64                `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`             // 充值金额（人民币）
	PaymentMethod string                 `protobuf:"bytes,3,opt,name=paymentMethod,proto3" json:"paymentMethod,omitempty"` // 支付方式：alipay, wechat, apple, bank
	ExchangeRate  float32                `protobuf:"fixed32,4,opt,name=exchangeRate,proto3" json:"exchangeRate,omitempty"` // 兑换比例，可选，默认1:1
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RechargeReq) Reset() {
	*x = RechargeReq{}
	mi := &file_proto_account_account_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RechargeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RechargeReq) ProtoMessage() {}

func (x *RechargeReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_account_account_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RechargeReq.ProtoReflect.Descriptor instead.
func (*RechargeReq) Descriptor() ([]byte, []int) {
	return file_proto_account_account_proto_rawDescGZIP(), []int{7}
}

func (x *RechargeReq) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *RechargeReq) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *RechargeReq) GetPaymentMethod() string {
	if x != nil {
		return x.PaymentMethod
	}
	return ""
}

func (x *RechargeReq) GetExchangeRate() float32 {
	if x != nil {
		return x.ExchangeRate
	}
	return 0
}

type RechargeResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Order         *RechargeOrder         `protobuf:"bytes,3,opt,name=order,proto3" json:"order,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RechargeResp) Reset() {
	*x = RechargeResp{}
	mi := &file_proto_account_account_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RechargeResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RechargeResp) ProtoMessage() {}

func (x *RechargeResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_account_account_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RechargeResp.ProtoReflect.Descriptor instead.
func (*RechargeResp) Descriptor() ([]byte, []int) {
	return file_proto_account_account_proto_rawDescGZIP(), []int{8}
}

func (x *RechargeResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RechargeResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *RechargeResp) GetOrder() *RechargeOrder {
	if x != nil {
		return x.Order
	}
	return nil
}

// 获取账户日志
type GetAccountLogsReq struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	UserId          uint64                 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
	Page            int32                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize        int32                  `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	TransactionType string                 `protobuf:"bytes,4,opt,name=transactionType,proto3" json:"transactionType,omitempty"` // 可选，筛选交易类型
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GetAccountLogsReq) Reset() {
	*x = GetAccountLogsReq{}
	mi := &file_proto_account_account_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountLogsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountLogsReq) ProtoMessage() {}

func (x *GetAccountLogsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_account_account_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountLogsReq.ProtoReflect.Descriptor instead.
func (*GetAccountLogsReq) Descriptor() ([]byte, []int) {
	return file_proto_account_account_proto_rawDescGZIP(), []int{9}
}

func (x *GetAccountLogsReq) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetAccountLogsReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetAccountLogsReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetAccountLogsReq) GetTransactionType() string {
	if x != nil {
		return x.TransactionType
	}
	return ""
}

type GetAccountLogsResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Logs          []*AccountLog          `protobuf:"bytes,3,rep,name=logs,proto3" json:"logs,omitempty"`
	Total         int64                  `protobuf:"varint,4,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAccountLogsResp) Reset() {
	*x = GetAccountLogsResp{}
	mi := &file_proto_account_account_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAccountLogsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAccountLogsResp) ProtoMessage() {}

func (x *GetAccountLogsResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_account_account_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAccountLogsResp.ProtoReflect.Descriptor instead.
func (*GetAccountLogsResp) Descriptor() ([]byte, []int) {
	return file_proto_account_account_proto_rawDescGZIP(), []int{10}
}

func (x *GetAccountLogsResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetAccountLogsResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetAccountLogsResp) GetLogs() []*AccountLog {
	if x != nil {
		return x.Logs
	}
	return nil
}

func (x *GetAccountLogsResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 更新用户状态
type UpdateUserStatusReq struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	UserId            uint64                 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
	UserType          int32                  `protobuf:"varint,2,opt,name=userType,proto3" json:"userType,omitempty"`                   // 用户类型
	VipExpireTime     int64                  `protobuf:"varint,3,opt,name=vipExpireTime,proto3" json:"vipExpireTime,omitempty"`         // VIP过期时间
	MonthlyExpireTime int64                  `protobuf:"varint,4,opt,name=monthlyExpireTime,proto3" json:"monthlyExpireTime,omitempty"` // 包月过期时间
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *UpdateUserStatusReq) Reset() {
	*x = UpdateUserStatusReq{}
	mi := &file_proto_account_account_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserStatusReq) ProtoMessage() {}

func (x *UpdateUserStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_account_account_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserStatusReq.ProtoReflect.Descriptor instead.
func (*UpdateUserStatusReq) Descriptor() ([]byte, []int) {
	return file_proto_account_account_proto_rawDescGZIP(), []int{11}
}

func (x *UpdateUserStatusReq) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UpdateUserStatusReq) GetUserType() int32 {
	if x != nil {
		return x.UserType
	}
	return 0
}

func (x *UpdateUserStatusReq) GetVipExpireTime() int64 {
	if x != nil {
		return x.VipExpireTime
	}
	return 0
}

func (x *UpdateUserStatusReq) GetMonthlyExpireTime() int64 {
	if x != nil {
		return x.MonthlyExpireTime
	}
	return 0
}

type UpdateUserStatusResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Account       *AccountInfo           `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserStatusResp) Reset() {
	*x = UpdateUserStatusResp{}
	mi := &file_proto_account_account_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserStatusResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserStatusResp) ProtoMessage() {}

func (x *UpdateUserStatusResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_account_account_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserStatusResp.ProtoReflect.Descriptor instead.
func (*UpdateUserStatusResp) Descriptor() ([]byte, []int) {
	return file_proto_account_account_proto_rawDescGZIP(), []int{12}
}

func (x *UpdateUserStatusResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UpdateUserStatusResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *UpdateUserStatusResp) GetAccount() *AccountInfo {
	if x != nil {
		return x.Account
	}
	return nil
}

// 扣除书币
type DeductCoinsReq struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	UserId          uint64                 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
	Amount          float64                `protobuf:"fixed64,2,opt,name=amount,proto3" json:"amount,omitempty"`                 // 扣除金额
	OrderId         string                 `protobuf:"bytes,3,opt,name=orderId,proto3" json:"orderId,omitempty"`                 // 订单ID
	BookId          string                 `protobuf:"bytes,4,opt,name=bookId,proto3" json:"bookId,omitempty"`                   // 书籍ID
	ChapterId       string                 `protobuf:"bytes,5,opt,name=chapterId,proto3" json:"chapterId,omitempty"`             // 章节ID
	TransactionType string                 `protobuf:"bytes,6,opt,name=transactionType,proto3" json:"transactionType,omitempty"` // 交易类型
	Description     string                 `protobuf:"bytes,7,opt,name=description,proto3" json:"description,omitempty"`         // 描述
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *DeductCoinsReq) Reset() {
	*x = DeductCoinsReq{}
	mi := &file_proto_account_account_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeductCoinsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeductCoinsReq) ProtoMessage() {}

func (x *DeductCoinsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_account_account_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeductCoinsReq.ProtoReflect.Descriptor instead.
func (*DeductCoinsReq) Descriptor() ([]byte, []int) {
	return file_proto_account_account_proto_rawDescGZIP(), []int{13}
}

func (x *DeductCoinsReq) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *DeductCoinsReq) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *DeductCoinsReq) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *DeductCoinsReq) GetBookId() string {
	if x != nil {
		return x.BookId
	}
	return ""
}

func (x *DeductCoinsReq) GetChapterId() string {
	if x != nil {
		return x.ChapterId
	}
	return ""
}

func (x *DeductCoinsReq) GetTransactionType() string {
	if x != nil {
		return x.TransactionType
	}
	return ""
}

func (x *DeductCoinsReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type DeductCoinsResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Account       *AccountInfo           `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeductCoinsResp) Reset() {
	*x = DeductCoinsResp{}
	mi := &file_proto_account_account_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeductCoinsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeductCoinsResp) ProtoMessage() {}

func (x *DeductCoinsResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_account_account_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeductCoinsResp.ProtoReflect.Descriptor instead.
func (*DeductCoinsResp) Descriptor() ([]byte, []int) {
	return file_proto_account_account_proto_rawDescGZIP(), []int{14}
}

func (x *DeductCoinsResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DeductCoinsResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *DeductCoinsResp) GetAccount() *AccountInfo {
	if x != nil {
		return x.Account
	}
	return nil
}

// 检查用户状态
type CheckUserStatusReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        uint64                 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckUserStatusReq) Reset() {
	*x = CheckUserStatusReq{}
	mi := &file_proto_account_account_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckUserStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUserStatusReq) ProtoMessage() {}

func (x *CheckUserStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_account_account_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUserStatusReq.ProtoReflect.Descriptor instead.
func (*CheckUserStatusReq) Descriptor() ([]byte, []int) {
	return file_proto_account_account_proto_rawDescGZIP(), []int{15}
}

func (x *CheckUserStatusReq) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type CheckUserStatusResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Account       *AccountInfo           `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	HasVip        bool                   `protobuf:"varint,4,opt,name=hasVip,proto3" json:"hasVip,omitempty"`         // 是否有VIP权限
	HasMonthly    bool                   `protobuf:"varint,5,opt,name=hasMonthly,proto3" json:"hasMonthly,omitempty"` // 是否有包月权限
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckUserStatusResp) Reset() {
	*x = CheckUserStatusResp{}
	mi := &file_proto_account_account_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckUserStatusResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckUserStatusResp) ProtoMessage() {}

func (x *CheckUserStatusResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_account_account_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckUserStatusResp.ProtoReflect.Descriptor instead.
func (*CheckUserStatusResp) Descriptor() ([]byte, []int) {
	return file_proto_account_account_proto_rawDescGZIP(), []int{16}
}

func (x *CheckUserStatusResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CheckUserStatusResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CheckUserStatusResp) GetAccount() *AccountInfo {
	if x != nil {
		return x.Account
	}
	return nil
}

func (x *CheckUserStatusResp) GetHasVip() bool {
	if x != nil {
		return x.HasVip
	}
	return false
}

func (x *CheckUserStatusResp) GetHasMonthly() bool {
	if x != nil {
		return x.HasMonthly
	}
	return false
}

var File_proto_account_account_proto protoreflect.FileDescriptor

const file_proto_account_account_proto_rawDesc = "" +
	"\n" +
	"\x1bproto/account/account.proto\x12\aaccount\"\x95\x03\n" +
	"\vAccountInfo\x12\x1c\n" +
	"\taccountId\x18\x01 \x01(\x04R\taccountId\x12\x16\n" +
	"\x06userId\x18\x02 \x01(\x04R\x06userId\x12 \n" +
	"\vcoinBalance\x18\x03 \x01(\x01R\vcoinBalance\x12&\n" +
	"\x0etotalRecharged\x18\x04 \x01(\x01R\x0etotalRecharged\x12$\n" +
	"\rtotalConsumed\x18\x05 \x01(\x01R\rtotalConsumed\x12\x16\n" +
	"\x06status\x18\x06 \x01(\x05R\x06status\x12\x1a\n" +
	"\buserType\x18\a \x01(\x05R\buserType\x12\x1c\n" +
	"\tuserLevel\x18\b \x01(\x05R\tuserLevel\x12$\n" +
	"\rvipExpireTime\x18\t \x01(\x03R\rvipExpireTime\x12,\n" +
	"\x11monthlyExpireTime\x18\n" +
	" \x01(\x03R\x11monthlyExpireTime\x12\x1c\n" +
	"\tcreatedAt\x18\v \x01(\x03R\tcreatedAt\x12\x1c\n" +
	"\tupdatedAt\x18\f \x01(\x03R\tupdatedAt\"\x92\x03\n" +
	"\n" +
	"AccountLog\x12\x14\n" +
	"\x05logId\x18\x01 \x01(\x04R\x05logId\x12\x1c\n" +
	"\taccountId\x18\x02 \x01(\x04R\taccountId\x12\x16\n" +
	"\x06userId\x18\x03 \x01(\x04R\x06userId\x12(\n" +
	"\x0ftransactionType\x18\x04 \x01(\tR\x0ftransactionType\x12\x16\n" +
	"\x06amount\x18\x05 \x01(\x01R\x06amount\x12$\n" +
	"\rbalanceBefore\x18\x06 \x01(\x01R\rbalanceBefore\x12\"\n" +
	"\fbalanceAfter\x18\a \x01(\x01R\fbalanceAfter\x12\x18\n" +
	"\aorderId\x18\b \x01(\tR\aorderId\x12\x16\n" +
	"\x06bookId\x18\t \x01(\tR\x06bookId\x12\x1c\n" +
	"\tchapterId\x18\n" +
	" \x01(\tR\tchapterId\x12 \n" +
	"\vdescription\x18\v \x01(\tR\vdescription\x12\x1c\n" +
	"\textraData\x18\f \x01(\tR\textraData\x12\x1c\n" +
	"\tcreatedAt\x18\r \x01(\x03R\tcreatedAt\"\xf5\x02\n" +
	"\rRechargeOrder\x12\x18\n" +
	"\aorderId\x18\x01 \x01(\tR\aorderId\x12\x1c\n" +
	"\taccountId\x18\x02 \x01(\x04R\taccountId\x12\x16\n" +
	"\x06userId\x18\x03 \x01(\x04R\x06userId\x12\x16\n" +
	"\x06amount\x18\x04 \x01(\x01R\x06amount\x12\x1e\n" +
	"\n" +
	"coinAmount\x18\x05 \x01(\x01R\n" +
	"coinAmount\x12\"\n" +
	"\fexchangeRate\x18\x06 \x01(\x02R\fexchangeRate\x12$\n" +
	"\rpaymentMethod\x18\a \x01(\tR\rpaymentMethod\x12&\n" +
	"\x0epaymentOrderId\x18\b \x01(\tR\x0epaymentOrderId\x12\x16\n" +
	"\x06status\x18\t \x01(\x05R\x06status\x12\x16\n" +
	"\x06paidAt\x18\n" +
	" \x01(\x03R\x06paidAt\x12\x1c\n" +
	"\tcreatedAt\x18\v \x01(\x03R\tcreatedAt\x12\x1c\n" +
	"\tupdatedAt\x18\f \x01(\x03R\tupdatedAt\"'\n" +
	"\rGetAccountReq\x12\x16\n" +
	"\x06userId\x18\x01 \x01(\x04R\x06userId\"n\n" +
	"\x0eGetAccountResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12.\n" +
	"\aaccount\x18\x03 \x01(\v2\x14.account.AccountInfoR\aaccount\"*\n" +
	"\x10CreateAccountReq\x12\x16\n" +
	"\x06userId\x18\x01 \x01(\x04R\x06userId\"q\n" +
	"\x11CreateAccountResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12.\n" +
	"\aaccount\x18\x03 \x01(\v2\x14.account.AccountInfoR\aaccount\"\x87\x01\n" +
	"\vRechargeReq\x12\x16\n" +
	"\x06userId\x18\x01 \x01(\x04R\x06userId\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\x01R\x06amount\x12$\n" +
	"\rpaymentMethod\x18\x03 \x01(\tR\rpaymentMethod\x12\"\n" +
	"\fexchangeRate\x18\x04 \x01(\x02R\fexchangeRate\"j\n" +
	"\fRechargeResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12,\n" +
	"\x05order\x18\x03 \x01(\v2\x16.account.RechargeOrderR\x05order\"\x85\x01\n" +
	"\x11GetAccountLogsReq\x12\x16\n" +
	"\x06userId\x18\x01 \x01(\x04R\x06userId\x12\x12\n" +
	"\x04page\x18\x02 \x01(\x05R\x04page\x12\x1a\n" +
	"\bpageSize\x18\x03 \x01(\x05R\bpageSize\x12(\n" +
	"\x0ftransactionType\x18\x04 \x01(\tR\x0ftransactionType\"\x81\x01\n" +
	"\x12GetAccountLogsResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12'\n" +
	"\x04logs\x18\x03 \x03(\v2\x13.account.AccountLogR\x04logs\x12\x14\n" +
	"\x05total\x18\x04 \x01(\x03R\x05total\"\x9d\x01\n" +
	"\x13UpdateUserStatusReq\x12\x16\n" +
	"\x06userId\x18\x01 \x01(\x04R\x06userId\x12\x1a\n" +
	"\buserType\x18\x02 \x01(\x05R\buserType\x12$\n" +
	"\rvipExpireTime\x18\x03 \x01(\x03R\rvipExpireTime\x12,\n" +
	"\x11monthlyExpireTime\x18\x04 \x01(\x03R\x11monthlyExpireTime\"t\n" +
	"\x14UpdateUserStatusResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12.\n" +
	"\aaccount\x18\x03 \x01(\v2\x14.account.AccountInfoR\aaccount\"\xdc\x01\n" +
	"\x0eDeductCoinsReq\x12\x16\n" +
	"\x06userId\x18\x01 \x01(\x04R\x06userId\x12\x16\n" +
	"\x06amount\x18\x02 \x01(\x01R\x06amount\x12\x18\n" +
	"\aorderId\x18\x03 \x01(\tR\aorderId\x12\x16\n" +
	"\x06bookId\x18\x04 \x01(\tR\x06bookId\x12\x1c\n" +
	"\tchapterId\x18\x05 \x01(\tR\tchapterId\x12(\n" +
	"\x0ftransactionType\x18\x06 \x01(\tR\x0ftransactionType\x12 \n" +
	"\vdescription\x18\a \x01(\tR\vdescription\"o\n" +
	"\x0fDeductCoinsResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12.\n" +
	"\aaccount\x18\x03 \x01(\v2\x14.account.AccountInfoR\aaccount\",\n" +
	"\x12CheckUserStatusReq\x12\x16\n" +
	"\x06userId\x18\x01 \x01(\x04R\x06userId\"\xab\x01\n" +
	"\x13CheckUserStatusResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12.\n" +
	"\aaccount\x18\x03 \x01(\v2\x14.account.AccountInfoR\aaccount\x12\x16\n" +
	"\x06hasVip\x18\x04 \x01(\bR\x06hasVip\x12\x1e\n" +
	"\n" +
	"hasMonthly\x18\x05 \x01(\bR\n" +
	"hasMonthly2\xf5\x03\n" +
	"\aAccount\x12=\n" +
	"\n" +
	"GetAccount\x12\x16.account.GetAccountReq\x1a\x17.account.GetAccountResp\x12F\n" +
	"\rCreateAccount\x12\x19.account.CreateAccountReq\x1a\x1a.account.CreateAccountResp\x127\n" +
	"\bRecharge\x12\x14.account.RechargeReq\x1a\x15.account.RechargeResp\x12I\n" +
	"\x0eGetAccountLogs\x12\x1a.account.GetAccountLogsReq\x1a\x1b.account.GetAccountLogsResp\x12O\n" +
	"\x10UpdateUserStatus\x12\x1c.account.UpdateUserStatusReq\x1a\x1d.account.UpdateUserStatusResp\x12@\n" +
	"\vDeductCoins\x12\x17.account.DeductCoinsReq\x1a\x18.account.DeductCoinsResp\x12L\n" +
	"\x0fCheckUserStatus\x12\x1b.account.CheckUserStatusReq\x1a\x1c.account.CheckUserStatusRespB5Z3creativematrix.com/beyondreading/gen2/proto/accountb\x06proto3"

var (
	file_proto_account_account_proto_rawDescOnce sync.Once
	file_proto_account_account_proto_rawDescData []byte
)

func file_proto_account_account_proto_rawDescGZIP() []byte {
	file_proto_account_account_proto_rawDescOnce.Do(func() {
		file_proto_account_account_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_account_account_proto_rawDesc), len(file_proto_account_account_proto_rawDesc)))
	})
	return file_proto_account_account_proto_rawDescData
}

var file_proto_account_account_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_proto_account_account_proto_goTypes = []any{
	(*AccountInfo)(nil),          // 0: account.AccountInfo
	(*AccountLog)(nil),           // 1: account.AccountLog
	(*RechargeOrder)(nil),        // 2: account.RechargeOrder
	(*GetAccountReq)(nil),        // 3: account.GetAccountReq
	(*GetAccountResp)(nil),       // 4: account.GetAccountResp
	(*CreateAccountReq)(nil),     // 5: account.CreateAccountReq
	(*CreateAccountResp)(nil),    // 6: account.CreateAccountResp
	(*RechargeReq)(nil),          // 7: account.RechargeReq
	(*RechargeResp)(nil),         // 8: account.RechargeResp
	(*GetAccountLogsReq)(nil),    // 9: account.GetAccountLogsReq
	(*GetAccountLogsResp)(nil),   // 10: account.GetAccountLogsResp
	(*UpdateUserStatusReq)(nil),  // 11: account.UpdateUserStatusReq
	(*UpdateUserStatusResp)(nil), // 12: account.UpdateUserStatusResp
	(*DeductCoinsReq)(nil),       // 13: account.DeductCoinsReq
	(*DeductCoinsResp)(nil),      // 14: account.DeductCoinsResp
	(*CheckUserStatusReq)(nil),   // 15: account.CheckUserStatusReq
	(*CheckUserStatusResp)(nil),  // 16: account.CheckUserStatusResp
}
var file_proto_account_account_proto_depIdxs = []int32{
	0,  // 0: account.GetAccountResp.account:type_name -> account.AccountInfo
	0,  // 1: account.CreateAccountResp.account:type_name -> account.AccountInfo
	2,  // 2: account.RechargeResp.order:type_name -> account.RechargeOrder
	1,  // 3: account.GetAccountLogsResp.logs:type_name -> account.AccountLog
	0,  // 4: account.UpdateUserStatusResp.account:type_name -> account.AccountInfo
	0,  // 5: account.DeductCoinsResp.account:type_name -> account.AccountInfo
	0,  // 6: account.CheckUserStatusResp.account:type_name -> account.AccountInfo
	3,  // 7: account.Account.GetAccount:input_type -> account.GetAccountReq
	5,  // 8: account.Account.CreateAccount:input_type -> account.CreateAccountReq
	7,  // 9: account.Account.Recharge:input_type -> account.RechargeReq
	9,  // 10: account.Account.GetAccountLogs:input_type -> account.GetAccountLogsReq
	11, // 11: account.Account.UpdateUserStatus:input_type -> account.UpdateUserStatusReq
	13, // 12: account.Account.DeductCoins:input_type -> account.DeductCoinsReq
	15, // 13: account.Account.CheckUserStatus:input_type -> account.CheckUserStatusReq
	4,  // 14: account.Account.GetAccount:output_type -> account.GetAccountResp
	6,  // 15: account.Account.CreateAccount:output_type -> account.CreateAccountResp
	8,  // 16: account.Account.Recharge:output_type -> account.RechargeResp
	10, // 17: account.Account.GetAccountLogs:output_type -> account.GetAccountLogsResp
	12, // 18: account.Account.UpdateUserStatus:output_type -> account.UpdateUserStatusResp
	14, // 19: account.Account.DeductCoins:output_type -> account.DeductCoinsResp
	16, // 20: account.Account.CheckUserStatus:output_type -> account.CheckUserStatusResp
	14, // [14:21] is the sub-list for method output_type
	7,  // [7:14] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_proto_account_account_proto_init() }
func file_proto_account_account_proto_init() {
	if File_proto_account_account_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_account_account_proto_rawDesc), len(file_proto_account_account_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_account_account_proto_goTypes,
		DependencyIndexes: file_proto_account_account_proto_depIdxs,
		MessageInfos:      file_proto_account_account_proto_msgTypes,
	}.Build()
	File_proto_account_account_proto = out.File
	file_proto_account_account_proto_goTypes = nil
	file_proto_account_account_proto_depIdxs = nil
}
