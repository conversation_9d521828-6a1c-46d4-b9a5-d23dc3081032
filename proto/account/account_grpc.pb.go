// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.30.2
// source: proto/account/account.proto

package account

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Account_GetAccount_FullMethodName       = "/account.Account/GetAccount"
	Account_CreateAccount_FullMethodName    = "/account.Account/CreateAccount"
	Account_Recharge_FullMethodName         = "/account.Account/Recharge"
	Account_GetAccountLogs_FullMethodName   = "/account.Account/GetAccountLogs"
	Account_UpdateUserStatus_FullMethodName = "/account.Account/UpdateUserStatus"
	Account_DeductCoins_FullMethodName      = "/account.Account/DeductCoins"
	Account_CheckUserStatus_FullMethodName  = "/account.Account/CheckUserStatus"
)

// AccountClient is the client API for Account service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AccountClient interface {
	// 获取账户信息
	GetAccount(ctx context.Context, in *GetAccountReq, opts ...grpc.CallOption) (*GetAccountResp, error)
	// 创建账户
	CreateAccount(ctx context.Context, in *CreateAccountReq, opts ...grpc.CallOption) (*CreateAccountResp, error)
	// 充值
	Recharge(ctx context.Context, in *RechargeReq, opts ...grpc.CallOption) (*RechargeResp, error)
	// 获取账户日志
	GetAccountLogs(ctx context.Context, in *GetAccountLogsReq, opts ...grpc.CallOption) (*GetAccountLogsResp, error)
	// 更新用户状态（VIP、包月等）
	UpdateUserStatus(ctx context.Context, in *UpdateUserStatusReq, opts ...grpc.CallOption) (*UpdateUserStatusResp, error)
	// 扣除书币（内部接口，供purchase服务调用）
	DeductCoins(ctx context.Context, in *DeductCoinsReq, opts ...grpc.CallOption) (*DeductCoinsResp, error)
	// 检查用户状态
	CheckUserStatus(ctx context.Context, in *CheckUserStatusReq, opts ...grpc.CallOption) (*CheckUserStatusResp, error)
}

type accountClient struct {
	cc grpc.ClientConnInterface
}

func NewAccountClient(cc grpc.ClientConnInterface) AccountClient {
	return &accountClient{cc}
}

func (c *accountClient) GetAccount(ctx context.Context, in *GetAccountReq, opts ...grpc.CallOption) (*GetAccountResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAccountResp)
	err := c.cc.Invoke(ctx, Account_GetAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountClient) CreateAccount(ctx context.Context, in *CreateAccountReq, opts ...grpc.CallOption) (*CreateAccountResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateAccountResp)
	err := c.cc.Invoke(ctx, Account_CreateAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountClient) Recharge(ctx context.Context, in *RechargeReq, opts ...grpc.CallOption) (*RechargeResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RechargeResp)
	err := c.cc.Invoke(ctx, Account_Recharge_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountClient) GetAccountLogs(ctx context.Context, in *GetAccountLogsReq, opts ...grpc.CallOption) (*GetAccountLogsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAccountLogsResp)
	err := c.cc.Invoke(ctx, Account_GetAccountLogs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountClient) UpdateUserStatus(ctx context.Context, in *UpdateUserStatusReq, opts ...grpc.CallOption) (*UpdateUserStatusResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateUserStatusResp)
	err := c.cc.Invoke(ctx, Account_UpdateUserStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountClient) DeductCoins(ctx context.Context, in *DeductCoinsReq, opts ...grpc.CallOption) (*DeductCoinsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeductCoinsResp)
	err := c.cc.Invoke(ctx, Account_DeductCoins_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *accountClient) CheckUserStatus(ctx context.Context, in *CheckUserStatusReq, opts ...grpc.CallOption) (*CheckUserStatusResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckUserStatusResp)
	err := c.cc.Invoke(ctx, Account_CheckUserStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AccountServer is the server API for Account service.
// All implementations should embed UnimplementedAccountServer
// for forward compatibility.
type AccountServer interface {
	// 获取账户信息
	GetAccount(context.Context, *GetAccountReq) (*GetAccountResp, error)
	// 创建账户
	CreateAccount(context.Context, *CreateAccountReq) (*CreateAccountResp, error)
	// 充值
	Recharge(context.Context, *RechargeReq) (*RechargeResp, error)
	// 获取账户日志
	GetAccountLogs(context.Context, *GetAccountLogsReq) (*GetAccountLogsResp, error)
	// 更新用户状态（VIP、包月等）
	UpdateUserStatus(context.Context, *UpdateUserStatusReq) (*UpdateUserStatusResp, error)
	// 扣除书币（内部接口，供purchase服务调用）
	DeductCoins(context.Context, *DeductCoinsReq) (*DeductCoinsResp, error)
	// 检查用户状态
	CheckUserStatus(context.Context, *CheckUserStatusReq) (*CheckUserStatusResp, error)
}

// UnimplementedAccountServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAccountServer struct{}

func (UnimplementedAccountServer) GetAccount(context.Context, *GetAccountReq) (*GetAccountResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccount not implemented")
}
func (UnimplementedAccountServer) CreateAccount(context.Context, *CreateAccountReq) (*CreateAccountResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAccount not implemented")
}
func (UnimplementedAccountServer) Recharge(context.Context, *RechargeReq) (*RechargeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Recharge not implemented")
}
func (UnimplementedAccountServer) GetAccountLogs(context.Context, *GetAccountLogsReq) (*GetAccountLogsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAccountLogs not implemented")
}
func (UnimplementedAccountServer) UpdateUserStatus(context.Context, *UpdateUserStatusReq) (*UpdateUserStatusResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserStatus not implemented")
}
func (UnimplementedAccountServer) DeductCoins(context.Context, *DeductCoinsReq) (*DeductCoinsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeductCoins not implemented")
}
func (UnimplementedAccountServer) CheckUserStatus(context.Context, *CheckUserStatusReq) (*CheckUserStatusResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckUserStatus not implemented")
}
func (UnimplementedAccountServer) testEmbeddedByValue() {}

// UnsafeAccountServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AccountServer will
// result in compilation errors.
type UnsafeAccountServer interface {
	mustEmbedUnimplementedAccountServer()
}

func RegisterAccountServer(s grpc.ServiceRegistrar, srv AccountServer) {
	// If the following call pancis, it indicates UnimplementedAccountServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Account_ServiceDesc, srv)
}

func _Account_GetAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServer).GetAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Account_GetAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServer).GetAccount(ctx, req.(*GetAccountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_CreateAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAccountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServer).CreateAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Account_CreateAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServer).CreateAccount(ctx, req.(*CreateAccountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_Recharge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RechargeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServer).Recharge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Account_Recharge_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServer).Recharge(ctx, req.(*RechargeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_GetAccountLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAccountLogsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServer).GetAccountLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Account_GetAccountLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServer).GetAccountLogs(ctx, req.(*GetAccountLogsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_UpdateUserStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServer).UpdateUserStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Account_UpdateUserStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServer).UpdateUserStatus(ctx, req.(*UpdateUserStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_DeductCoins_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeductCoinsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServer).DeductCoins(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Account_DeductCoins_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServer).DeductCoins(ctx, req.(*DeductCoinsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Account_CheckUserStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckUserStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AccountServer).CheckUserStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Account_CheckUserStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AccountServer).CheckUserStatus(ctx, req.(*CheckUserStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Account_ServiceDesc is the grpc.ServiceDesc for Account service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Account_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "account.Account",
	HandlerType: (*AccountServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetAccount",
			Handler:    _Account_GetAccount_Handler,
		},
		{
			MethodName: "CreateAccount",
			Handler:    _Account_CreateAccount_Handler,
		},
		{
			MethodName: "Recharge",
			Handler:    _Account_Recharge_Handler,
		},
		{
			MethodName: "GetAccountLogs",
			Handler:    _Account_GetAccountLogs_Handler,
		},
		{
			MethodName: "UpdateUserStatus",
			Handler:    _Account_UpdateUserStatus_Handler,
		},
		{
			MethodName: "DeductCoins",
			Handler:    _Account_DeductCoins_Handler,
		},
		{
			MethodName: "CheckUserStatus",
			Handler:    _Account_CheckUserStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/account/account.proto",
}
