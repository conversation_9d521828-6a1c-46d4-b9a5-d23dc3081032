syntax = "proto3";
package account;
option go_package = "creativematrix.com/beyondreading/gen2/proto/account";

service Account {
  // 获取账户信息
  rpc GetAccount(GetAccountReq) returns (GetAccountResp);

  // 创建账户
  rpc CreateAccount(CreateAccountReq) returns (CreateAccountResp);

  // 充值
  rpc Recharge(RechargeReq) returns (RechargeResp);

  // 获取账户日志
  rpc GetAccountLogs(GetAccountLogsReq) returns (GetAccountLogsResp);

  // 更新用户状态（VIP、包月等）
  rpc UpdateUserStatus(UpdateUserStatusReq) returns (UpdateUserStatusResp);

  // 扣除书币（内部接口，供purchase服务调用）
  rpc DeductCoins(DeductCoinsReq) returns (DeductCoinsResp);

  // 检查用户状态
  rpc CheckUserStatus(CheckUserStatusReq) returns (CheckUserStatusResp);
}

// 账户信息
message AccountInfo {
  uint64 account_id = 1;
  uint64 user_id = 2;
  double coin_balance = 3;        // 书币余额
  double total_recharged = 4;     // 总充值金额
  double total_consumed = 5;      // 总消费金额
  int32 status = 6;               // 账户状态：1-正常，2-冻结，3-注销
  int32 user_type = 7;            // 用户类型：1-普通用户，2-VIP用户，3-包月用户
  int32 user_level = 8;           // 用户等级（根据消费书币计算，1000书币一级）
  int64 vip_expire_time = 9;      // VIP过期时间（Unix时间戳）
  int64 monthly_expire_time = 10; // 包月过期时间（Unix时间戳）
  int64 created_at = 11;          // 创建时间
  int64 updated_at = 12;          // 更新时间
}

// 账户日志
message AccountLog {
  uint64 log_id = 1;
  uint64 account_id = 2;
  uint64 user_id = 3;
  string transaction_type = 4;    // 交易类型：recharge, purchase_chapter, purchase_monthly, purchase_vip
  double amount = 5;              // 变动金额（正数为增加，负数为减少）
  double balance_before = 6;      // 变动前余额
  double balance_after = 7;       // 变动后余额
  string order_id = 8;            // 关联订单ID
  string book_id = 9;             // 书籍ID
  string chapter_id = 10;         // 章节ID
  string description = 11;        // 描述
  string extra_data = 12;         // 额外数据（JSON格式）
  int64 created_at = 13;          // 创建时间
}

// 充值订单
message RechargeOrder {
  string order_id = 1;
  uint64 account_id = 2;
  uint64 user_id = 3;
  double amount = 4;              // 充值金额（人民币）
  double coin_amount = 5;         // 获得书币数量
  float exchange_rate = 6;       // 兑换比例
  string payment_method = 7;      // 支付方式
  string payment_order_id = 8;    // 第三方支付订单ID
  int32 status = 9;               // 订单状态：1-待支付，2-支付成功，3-支付失败，4-已退款
  int64 paid_at = 10;             // 支付时间
  int64 created_at = 11;          // 创建时间
  int64 updated_at = 12;          // 更新时间
}

// 请求和响应消息

// 获取账户信息
message GetAccountReq {
  uint64 user_id = 1;
}

message GetAccountResp {
  int32 code = 1;
  string message = 2;
  AccountInfo account = 3;
}

// 创建账户
message CreateAccountReq {
  uint64 user_id = 1;
}

message CreateAccountResp {
  int32 code = 1;
  string message = 2;
  AccountInfo account = 3;
}

// 充值
message RechargeReq {
  uint64 user_id = 1;
  double amount = 2;              // 充值金额（人民币）
  string payment_method = 3;      // 支付方式：alipay, wechat, apple, bank
  float exchange_rate = 4;       // 兑换比例，可选，默认1:1
}

message RechargeResp {
  int32 code = 1;
  string message = 2;
  RechargeOrder order = 3;
}

// 获取账户日志
message GetAccountLogsReq {
  uint64 user_id = 1;
  int32 page = 2;
  int32 page_size = 3;
  string transaction_type = 4;    // 可选，筛选交易类型
}

message GetAccountLogsResp {
  int32 code = 1;
  string message = 2;
  repeated AccountLog logs = 3;
  int64 total = 4;
}

// 更新用户状态
message UpdateUserStatusReq {
  uint64 user_id = 1;
  int32 user_type = 2;            // 用户类型
  int64 vip_expire_time = 3;      // VIP过期时间
  int64 monthly_expire_time = 4;  // 包月过期时间
}

message UpdateUserStatusResp {
  int32 code = 1;
  string message = 2;
  AccountInfo account = 3;
}

// 扣除书币
message DeductCoinsReq {
  uint64 user_id = 1;
  double amount = 2;              // 扣除金额
  string order_id = 3;            // 订单ID
  string book_id = 4;             // 书籍ID
  string chapter_id = 5;          // 章节ID
  string transaction_type = 6;    // 交易类型
  string description = 7;         // 描述
}

message DeductCoinsResp {
  int32 code = 1;
  string message = 2;
  AccountInfo account = 3;
}

// 检查用户状态
message CheckUserStatusReq {
  uint64 user_id = 1;
}

message CheckUserStatusResp {
  int32 code = 1;
  string message = 2;
  AccountInfo account = 3;
  bool has_vip = 4;               // 是否有VIP权限
  bool has_monthly = 5;           // 是否有包月权限
}
