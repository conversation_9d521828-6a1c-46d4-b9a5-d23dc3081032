syntax = "proto3";
package account;
option go_package = "creativematrix.com/beyondreading/gen2/proto/account";

service Account {
  // 获取账户信息
  rpc GetAccount(GetAccountReq) returns (GetAccountResp);

  // 创建账户
  rpc CreateAccount(CreateAccountReq) returns (CreateAccountResp);

  // 充值
  rpc Recharge(RechargeReq) returns (RechargeResp);

  // 获取账户日志
  rpc GetAccountLogs(GetAccountLogsReq) returns (GetAccountLogsResp);

  // 更新用户状态（VIP、包月等）
  rpc UpdateUserStatus(UpdateUserStatusReq) returns (UpdateUserStatusResp);

  // 扣除书币（内部接口，供purchase服务调用）
  rpc DeductCoins(DeductCoinsReq) returns (DeductCoinsResp);

  // 检查用户状态
  rpc CheckUserStatus(CheckUserStatusReq) returns (CheckUserStatusResp);
}

// 账户信息
message AccountInfo {
  uint64 accountId = 1;
  uint64 userId = 2;
  double coinBalance = 3;        // 书币余额
  double totalRecharged = 4;     // 总充值金额
  double totalConsumed = 5;      // 总消费金额
  int32 status = 6;               // 账户状态：1-正常，2-冻结，3-注销
  int32 userType = 7;            // 用户类型：1-普通用户，2-VIP用户，3-包月用户
  int32 userLevel = 8;           // 用户等级（根据消费书币计算，1000书币一级）
  int64 vipExpireTime = 9;      // VIP过期时间（Unix时间戳）
  int64 monthlyExpireTime = 10; // 包月过期时间（Unix时间戳）
  int64 createdAt = 11;          // 创建时间
  int64 updatedAt = 12;          // 更新时间
}

// 账户日志
message AccountLog {
  uint64 logId = 1;
  uint64 accountId = 2;
  uint64 userId = 3;
  string transactionType = 4;    // 交易类型：recharge, purchase_chapter, purchase_monthly, purchase_vip
  double amount = 5;              // 变动金额（正数为增加，负数为减少）
  double balanceBefore = 6;      // 变动前余额
  double balanceAfter = 7;       // 变动后余额
  string orderId = 8;            // 关联订单ID
  string bookId = 9;             // 书籍ID
  string chapterId = 10;         // 章节ID
  string description = 11;        // 描述
  string extraData = 12;         // 额外数据（JSON格式）
  int64 createdAt = 13;          // 创建时间
}

// 充值订单
message RechargeOrder {
  string orderId = 1;
  uint64 accountId = 2;
  uint64 userId = 3;
  double amount = 4;              // 充值金额（人民币）
  double coinAmount = 5;         // 获得书币数量
  float exchangeRate = 6;        // 兑换比例
  string paymentMethod = 7;      // 支付方式
  string paymentOrderId = 8;    // 第三方支付订单ID
  int32 status = 9;               // 订单状态：1-待支付，2-支付成功，3-支付失败，4-已退款
  int64 paidAt = 10;             // 支付时间
  int64 createdAt = 11;          // 创建时间
  int64 updatedAt = 12;          // 更新时间
}

// 请求和响应消息

// 获取账户信息
message GetAccountReq {
  uint64 userId = 1;
}

message GetAccountResp {
  int32 code = 1;
  string message = 2;
  AccountInfo account = 3;
}

// 创建账户
message CreateAccountReq {
  uint64 userId = 1;
}

message CreateAccountResp {
  int32 code = 1;
  string message = 2;
  AccountInfo account = 3;
}

// 充值
message RechargeReq {
  uint64 userId = 1;
  double amount = 2;              // 充值金额（人民币）
  string paymentMethod = 3;      // 支付方式：alipay, wechat, apple, bank
  float exchangeRate = 4;        // 兑换比例，可选，默认1:1
}

message RechargeResp {
  int32 code = 1;
  string message = 2;
  RechargeOrder order = 3;
}

// 获取账户日志
message GetAccountLogsReq {
  uint64 userId = 1;
  int32 page = 2;
  int32 pageSize = 3;
  string transactionType = 4;    // 可选，筛选交易类型
}

message GetAccountLogsResp {
  int32 code = 1;
  string message = 2;
  repeated AccountLog logs = 3;
  int64 total = 4;
}

// 更新用户状态
message UpdateUserStatusReq {
  uint64 userId = 1;
  int32 userType = 2;            // 用户类型
  int64 vipExpireTime = 3;      // VIP过期时间
  int64 monthlyExpireTime = 4;  // 包月过期时间
}

message UpdateUserStatusResp {
  int32 code = 1;
  string message = 2;
  AccountInfo account = 3;
}

// 扣除书币
message DeductCoinsReq {
  uint64 userId = 1;
  double amount = 2;              // 扣除金额
  string orderId = 3;            // 订单ID
  string bookId = 4;             // 书籍ID
  string chapterId = 5;          // 章节ID
  string transactionType = 6;    // 交易类型
  string description = 7;         // 描述
}

message DeductCoinsResp {
  int32 code = 1;
  string message = 2;
  AccountInfo account = 3;
}

// 检查用户状态
message CheckUserStatusReq {
  uint64 userId = 1;
}

message CheckUserStatusResp {
  int32 code = 1;
  string message = 2;
  AccountInfo account = 3;
  bool hasVip = 4;               // 是否有VIP权限
  bool hasMonthly = 5;           // 是否有包月权限
}
