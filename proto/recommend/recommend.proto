syntax = "proto3";
package recommend;
option go_package = "creativematrix.com/beyondreading/proto/recommend";
import "proto/pbcommon/common.proto";

service Recommend {
  rpc GetRecommendBookList(RecommendReq) returns(RecommendListResp);
}

enum RecommendType{
  BookshelfRecommend = 0;
  PersonalRecommend = 1;
  RecommendByCurrentBookType = 2;
}

message RecommendReq {
  string userId = 1;
  string currentBookId = 2;
  repeated RecommendType recommendTypes = 3;
}

message RecommendListResp {
  repeated RecommendList recommendList = 1;
}

message RecommendList {
  RecommendType recommendType = 1;
  repeated pbcommon.BookInfo bookList = 2;
}


