// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: proto/recommend/recommend.proto

package recommend

import (
	pbcommon "creativematrix.com/beyondreading/proto/pbcommon"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RecommendType int32

const (
	RecommendType_BookshelfRecommend         RecommendType = 0
	RecommendType_PersonalRecommend          RecommendType = 1
	RecommendType_RecommendByCurrentBookType RecommendType = 2
)

// Enum value maps for RecommendType.
var (
	RecommendType_name = map[int32]string{
		0: "BookshelfRecommend",
		1: "PersonalRecommend",
		2: "RecommendByCurrentBookType",
	}
	RecommendType_value = map[string]int32{
		"BookshelfRecommend":         0,
		"PersonalRecommend":          1,
		"RecommendByCurrentBookType": 2,
	}
)

func (x RecommendType) Enum() *RecommendType {
	p := new(RecommendType)
	*p = x
	return p
}

func (x RecommendType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RecommendType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_recommend_recommend_proto_enumTypes[0].Descriptor()
}

func (RecommendType) Type() protoreflect.EnumType {
	return &file_proto_recommend_recommend_proto_enumTypes[0]
}

func (x RecommendType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RecommendType.Descriptor instead.
func (RecommendType) EnumDescriptor() ([]byte, []int) {
	return file_proto_recommend_recommend_proto_rawDescGZIP(), []int{0}
}

type RecommendReq struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	UserId         string                 `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId,omitempty"`
	CurrentBookId  string                 `protobuf:"bytes,2,opt,name=currentBookId,proto3" json:"currentBookId,omitempty"`
	RecommendTypes []RecommendType        `protobuf:"varint,3,rep,packed,name=recommendTypes,proto3,enum=recommend.RecommendType" json:"recommendTypes,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *RecommendReq) Reset() {
	*x = RecommendReq{}
	mi := &file_proto_recommend_recommend_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecommendReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendReq) ProtoMessage() {}

func (x *RecommendReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_recommend_recommend_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendReq.ProtoReflect.Descriptor instead.
func (*RecommendReq) Descriptor() ([]byte, []int) {
	return file_proto_recommend_recommend_proto_rawDescGZIP(), []int{0}
}

func (x *RecommendReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *RecommendReq) GetCurrentBookId() string {
	if x != nil {
		return x.CurrentBookId
	}
	return ""
}

func (x *RecommendReq) GetRecommendTypes() []RecommendType {
	if x != nil {
		return x.RecommendTypes
	}
	return nil
}

type RecommendListResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RecommendList []*RecommendList       `protobuf:"bytes,1,rep,name=recommendList,proto3" json:"recommendList,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RecommendListResp) Reset() {
	*x = RecommendListResp{}
	mi := &file_proto_recommend_recommend_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecommendListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendListResp) ProtoMessage() {}

func (x *RecommendListResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_recommend_recommend_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendListResp.ProtoReflect.Descriptor instead.
func (*RecommendListResp) Descriptor() ([]byte, []int) {
	return file_proto_recommend_recommend_proto_rawDescGZIP(), []int{1}
}

func (x *RecommendListResp) GetRecommendList() []*RecommendList {
	if x != nil {
		return x.RecommendList
	}
	return nil
}

type RecommendList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RecommendType RecommendType          `protobuf:"varint,1,opt,name=recommendType,proto3,enum=recommend.RecommendType" json:"recommendType,omitempty"`
	BookList      []*pbcommon.BookInfo   `protobuf:"bytes,2,rep,name=bookList,proto3" json:"bookList,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RecommendList) Reset() {
	*x = RecommendList{}
	mi := &file_proto_recommend_recommend_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecommendList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendList) ProtoMessage() {}

func (x *RecommendList) ProtoReflect() protoreflect.Message {
	mi := &file_proto_recommend_recommend_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendList.ProtoReflect.Descriptor instead.
func (*RecommendList) Descriptor() ([]byte, []int) {
	return file_proto_recommend_recommend_proto_rawDescGZIP(), []int{2}
}

func (x *RecommendList) GetRecommendType() RecommendType {
	if x != nil {
		return x.RecommendType
	}
	return RecommendType_BookshelfRecommend
}

func (x *RecommendList) GetBookList() []*pbcommon.BookInfo {
	if x != nil {
		return x.BookList
	}
	return nil
}

var File_proto_recommend_recommend_proto protoreflect.FileDescriptor

const file_proto_recommend_recommend_proto_rawDesc = "" +
	"\n" +
	"\x1fproto/recommend/recommend.proto\x12\trecommend\x1a\x1bproto/pbcommon/common.proto\"\x8e\x01\n" +
	"\fRecommendReq\x12\x16\n" +
	"\x06userId\x18\x01 \x01(\tR\x06userId\x12$\n" +
	"\rcurrentBookId\x18\x02 \x01(\tR\rcurrentBookId\x12@\n" +
	"\x0erecommendTypes\x18\x03 \x03(\x0e2\x18.recommend.RecommendTypeR\x0erecommendTypes\"S\n" +
	"\x11RecommendListResp\x12>\n" +
	"\rrecommendList\x18\x01 \x03(\v2\x18.recommend.RecommendListR\rrecommendList\"\x7f\n" +
	"\rRecommendList\x12>\n" +
	"\rrecommendType\x18\x01 \x01(\x0e2\x18.recommend.RecommendTypeR\rrecommendType\x12.\n" +
	"\bbookList\x18\x02 \x03(\v2\x12.pbcommon.BookInfoR\bbookList*^\n" +
	"\rRecommendType\x12\x16\n" +
	"\x12BookshelfRecommend\x10\x00\x12\x15\n" +
	"\x11PersonalRecommend\x10\x01\x12\x1e\n" +
	"\x1aRecommendByCurrentBookType\x10\x022Z\n" +
	"\tRecommend\x12M\n" +
	"\x14GetRecommendBookList\x12\x17.recommend.RecommendReq\x1a\x1c.recommend.RecommendListRespB2Z0creativematrix.com/beyondreading/proto/recommendb\x06proto3"

var (
	file_proto_recommend_recommend_proto_rawDescOnce sync.Once
	file_proto_recommend_recommend_proto_rawDescData []byte
)

func file_proto_recommend_recommend_proto_rawDescGZIP() []byte {
	file_proto_recommend_recommend_proto_rawDescOnce.Do(func() {
		file_proto_recommend_recommend_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_recommend_recommend_proto_rawDesc), len(file_proto_recommend_recommend_proto_rawDesc)))
	})
	return file_proto_recommend_recommend_proto_rawDescData
}

var file_proto_recommend_recommend_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_proto_recommend_recommend_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_proto_recommend_recommend_proto_goTypes = []any{
	(RecommendType)(0),        // 0: recommend.RecommendType
	(*RecommendReq)(nil),      // 1: recommend.RecommendReq
	(*RecommendListResp)(nil), // 2: recommend.RecommendListResp
	(*RecommendList)(nil),     // 3: recommend.RecommendList
	(*pbcommon.BookInfo)(nil), // 4: pbcommon.BookInfo
}
var file_proto_recommend_recommend_proto_depIdxs = []int32{
	0, // 0: recommend.RecommendReq.recommendTypes:type_name -> recommend.RecommendType
	3, // 1: recommend.RecommendListResp.recommendList:type_name -> recommend.RecommendList
	0, // 2: recommend.RecommendList.recommendType:type_name -> recommend.RecommendType
	4, // 3: recommend.RecommendList.bookList:type_name -> pbcommon.BookInfo
	1, // 4: recommend.Recommend.GetRecommendBookList:input_type -> recommend.RecommendReq
	2, // 5: recommend.Recommend.GetRecommendBookList:output_type -> recommend.RecommendListResp
	5, // [5:6] is the sub-list for method output_type
	4, // [4:5] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_proto_recommend_recommend_proto_init() }
func file_proto_recommend_recommend_proto_init() {
	if File_proto_recommend_recommend_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_recommend_recommend_proto_rawDesc), len(file_proto_recommend_recommend_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_recommend_recommend_proto_goTypes,
		DependencyIndexes: file_proto_recommend_recommend_proto_depIdxs,
		EnumInfos:         file_proto_recommend_recommend_proto_enumTypes,
		MessageInfos:      file_proto_recommend_recommend_proto_msgTypes,
	}.Build()
	File_proto_recommend_recommend_proto = out.File
	file_proto_recommend_recommend_proto_goTypes = nil
	file_proto_recommend_recommend_proto_depIdxs = nil
}
