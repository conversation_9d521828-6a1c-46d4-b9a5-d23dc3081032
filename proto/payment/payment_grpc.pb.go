// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.30.2
// source: proto/payment/payment.proto

package payment

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Payment_CreatePaymentOrder_FullMethodName     = "/payment.Payment/CreatePaymentOrder"
	Payment_ProcessPaymentCallback_FullMethodName = "/payment.Payment/ProcessPaymentCallback"
	Payment_GetPaymentOrder_FullMethodName        = "/payment.Payment/GetPaymentOrder"
	Payment_GetPaymentOrders_FullMethodName       = "/payment.Payment/GetPaymentOrders"
	Payment_CancelPaymentOrder_FullMethodName     = "/payment.Payment/CancelPaymentOrder"
	Payment_RefundPayment_FullMethodName          = "/payment.Payment/RefundPayment"
	Payment_GetPaymentMethods_FullMethodName      = "/payment.Payment/GetPaymentMethods"
	Payment_GetProducts_FullMethodName            = "/payment.Payment/GetProducts"
)

// PaymentClient is the client API for Payment service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PaymentClient interface {
	// 创建支付订单
	CreatePaymentOrder(ctx context.Context, in *CreatePaymentOrderReq, opts ...grpc.CallOption) (*CreatePaymentOrderResp, error)
	// 处理支付回调
	ProcessPaymentCallback(ctx context.Context, in *ProcessPaymentCallbackReq, opts ...grpc.CallOption) (*ProcessPaymentCallbackResp, error)
	// 查询支付订单状态
	GetPaymentOrder(ctx context.Context, in *GetPaymentOrderReq, opts ...grpc.CallOption) (*GetPaymentOrderResp, error)
	// 获取支付订单列表
	GetPaymentOrders(ctx context.Context, in *GetPaymentOrdersReq, opts ...grpc.CallOption) (*GetPaymentOrdersResp, error)
	// 取消支付订单
	CancelPaymentOrder(ctx context.Context, in *CancelPaymentOrderReq, opts ...grpc.CallOption) (*CancelPaymentOrderResp, error)
	// 退款
	RefundPayment(ctx context.Context, in *RefundPaymentReq, opts ...grpc.CallOption) (*RefundPaymentResp, error)
	// 获取支付方式列表
	GetPaymentMethods(ctx context.Context, in *GetPaymentMethodsReq, opts ...grpc.CallOption) (*GetPaymentMethodsResp, error)
	// 获取支付产品列表
	GetProducts(ctx context.Context, in *GetProductsReq, opts ...grpc.CallOption) (*GetProductsResp, error)
}

type paymentClient struct {
	cc grpc.ClientConnInterface
}

func NewPaymentClient(cc grpc.ClientConnInterface) PaymentClient {
	return &paymentClient{cc}
}

func (c *paymentClient) CreatePaymentOrder(ctx context.Context, in *CreatePaymentOrderReq, opts ...grpc.CallOption) (*CreatePaymentOrderResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreatePaymentOrderResp)
	err := c.cc.Invoke(ctx, Payment_CreatePaymentOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentClient) ProcessPaymentCallback(ctx context.Context, in *ProcessPaymentCallbackReq, opts ...grpc.CallOption) (*ProcessPaymentCallbackResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ProcessPaymentCallbackResp)
	err := c.cc.Invoke(ctx, Payment_ProcessPaymentCallback_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentClient) GetPaymentOrder(ctx context.Context, in *GetPaymentOrderReq, opts ...grpc.CallOption) (*GetPaymentOrderResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPaymentOrderResp)
	err := c.cc.Invoke(ctx, Payment_GetPaymentOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentClient) GetPaymentOrders(ctx context.Context, in *GetPaymentOrdersReq, opts ...grpc.CallOption) (*GetPaymentOrdersResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPaymentOrdersResp)
	err := c.cc.Invoke(ctx, Payment_GetPaymentOrders_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentClient) CancelPaymentOrder(ctx context.Context, in *CancelPaymentOrderReq, opts ...grpc.CallOption) (*CancelPaymentOrderResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CancelPaymentOrderResp)
	err := c.cc.Invoke(ctx, Payment_CancelPaymentOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentClient) RefundPayment(ctx context.Context, in *RefundPaymentReq, opts ...grpc.CallOption) (*RefundPaymentResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RefundPaymentResp)
	err := c.cc.Invoke(ctx, Payment_RefundPayment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentClient) GetPaymentMethods(ctx context.Context, in *GetPaymentMethodsReq, opts ...grpc.CallOption) (*GetPaymentMethodsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPaymentMethodsResp)
	err := c.cc.Invoke(ctx, Payment_GetPaymentMethods_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentClient) GetProducts(ctx context.Context, in *GetProductsReq, opts ...grpc.CallOption) (*GetProductsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetProductsResp)
	err := c.cc.Invoke(ctx, Payment_GetProducts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PaymentServer is the server API for Payment service.
// All implementations should embed UnimplementedPaymentServer
// for forward compatibility.
type PaymentServer interface {
	// 创建支付订单
	CreatePaymentOrder(context.Context, *CreatePaymentOrderReq) (*CreatePaymentOrderResp, error)
	// 处理支付回调
	ProcessPaymentCallback(context.Context, *ProcessPaymentCallbackReq) (*ProcessPaymentCallbackResp, error)
	// 查询支付订单状态
	GetPaymentOrder(context.Context, *GetPaymentOrderReq) (*GetPaymentOrderResp, error)
	// 获取支付订单列表
	GetPaymentOrders(context.Context, *GetPaymentOrdersReq) (*GetPaymentOrdersResp, error)
	// 取消支付订单
	CancelPaymentOrder(context.Context, *CancelPaymentOrderReq) (*CancelPaymentOrderResp, error)
	// 退款
	RefundPayment(context.Context, *RefundPaymentReq) (*RefundPaymentResp, error)
	// 获取支付方式列表
	GetPaymentMethods(context.Context, *GetPaymentMethodsReq) (*GetPaymentMethodsResp, error)
	// 获取支付产品列表
	GetProducts(context.Context, *GetProductsReq) (*GetProductsResp, error)
}

// UnimplementedPaymentServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPaymentServer struct{}

func (UnimplementedPaymentServer) CreatePaymentOrder(context.Context, *CreatePaymentOrderReq) (*CreatePaymentOrderResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePaymentOrder not implemented")
}
func (UnimplementedPaymentServer) ProcessPaymentCallback(context.Context, *ProcessPaymentCallbackReq) (*ProcessPaymentCallbackResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessPaymentCallback not implemented")
}
func (UnimplementedPaymentServer) GetPaymentOrder(context.Context, *GetPaymentOrderReq) (*GetPaymentOrderResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPaymentOrder not implemented")
}
func (UnimplementedPaymentServer) GetPaymentOrders(context.Context, *GetPaymentOrdersReq) (*GetPaymentOrdersResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPaymentOrders not implemented")
}
func (UnimplementedPaymentServer) CancelPaymentOrder(context.Context, *CancelPaymentOrderReq) (*CancelPaymentOrderResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelPaymentOrder not implemented")
}
func (UnimplementedPaymentServer) RefundPayment(context.Context, *RefundPaymentReq) (*RefundPaymentResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefundPayment not implemented")
}
func (UnimplementedPaymentServer) GetPaymentMethods(context.Context, *GetPaymentMethodsReq) (*GetPaymentMethodsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPaymentMethods not implemented")
}
func (UnimplementedPaymentServer) GetProducts(context.Context, *GetProductsReq) (*GetProductsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProducts not implemented")
}
func (UnimplementedPaymentServer) testEmbeddedByValue() {}

// UnsafePaymentServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PaymentServer will
// result in compilation errors.
type UnsafePaymentServer interface {
	mustEmbedUnimplementedPaymentServer()
}

func RegisterPaymentServer(s grpc.ServiceRegistrar, srv PaymentServer) {
	// If the following call pancis, it indicates UnimplementedPaymentServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Payment_ServiceDesc, srv)
}

func _Payment_CreatePaymentOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePaymentOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentServer).CreatePaymentOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Payment_CreatePaymentOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentServer).CreatePaymentOrder(ctx, req.(*CreatePaymentOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Payment_ProcessPaymentCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProcessPaymentCallbackReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentServer).ProcessPaymentCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Payment_ProcessPaymentCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentServer).ProcessPaymentCallback(ctx, req.(*ProcessPaymentCallbackReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Payment_GetPaymentOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPaymentOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentServer).GetPaymentOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Payment_GetPaymentOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentServer).GetPaymentOrder(ctx, req.(*GetPaymentOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Payment_GetPaymentOrders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPaymentOrdersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentServer).GetPaymentOrders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Payment_GetPaymentOrders_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentServer).GetPaymentOrders(ctx, req.(*GetPaymentOrdersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Payment_CancelPaymentOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelPaymentOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentServer).CancelPaymentOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Payment_CancelPaymentOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentServer).CancelPaymentOrder(ctx, req.(*CancelPaymentOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Payment_RefundPayment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RefundPaymentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentServer).RefundPayment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Payment_RefundPayment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentServer).RefundPayment(ctx, req.(*RefundPaymentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Payment_GetPaymentMethods_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPaymentMethodsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentServer).GetPaymentMethods(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Payment_GetPaymentMethods_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentServer).GetPaymentMethods(ctx, req.(*GetPaymentMethodsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Payment_GetProducts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProductsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentServer).GetProducts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Payment_GetProducts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentServer).GetProducts(ctx, req.(*GetProductsReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Payment_ServiceDesc is the grpc.ServiceDesc for Payment service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Payment_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "payment.Payment",
	HandlerType: (*PaymentServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreatePaymentOrder",
			Handler:    _Payment_CreatePaymentOrder_Handler,
		},
		{
			MethodName: "ProcessPaymentCallback",
			Handler:    _Payment_ProcessPaymentCallback_Handler,
		},
		{
			MethodName: "GetPaymentOrder",
			Handler:    _Payment_GetPaymentOrder_Handler,
		},
		{
			MethodName: "GetPaymentOrders",
			Handler:    _Payment_GetPaymentOrders_Handler,
		},
		{
			MethodName: "CancelPaymentOrder",
			Handler:    _Payment_CancelPaymentOrder_Handler,
		},
		{
			MethodName: "RefundPayment",
			Handler:    _Payment_RefundPayment_Handler,
		},
		{
			MethodName: "GetPaymentMethods",
			Handler:    _Payment_GetPaymentMethods_Handler,
		},
		{
			MethodName: "GetProducts",
			Handler:    _Payment_GetProducts_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/payment/payment.proto",
}
