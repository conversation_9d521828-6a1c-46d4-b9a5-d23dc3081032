// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: proto/payment/payment.proto

package payment

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 支付方式枚举
type PaymentMethod int32

const (
	PaymentMethod_UNKNOWN_PAYMENT PaymentMethod = 0
	PaymentMethod_GOOGLE_PAY      PaymentMethod = 1
	PaymentMethod_APPLE_PAY       PaymentMethod = 2
	PaymentMethod_PAYPAL          PaymentMethod = 3
	PaymentMethod_ALIPAY          PaymentMethod = 4
	PaymentMethod_WECHAT_PAY      PaymentMethod = 5
)

// Enum value maps for PaymentMethod.
var (
	PaymentMethod_name = map[int32]string{
		0: "UNKNOWN_PAYMENT",
		1: "GOOGLE_PAY",
		2: "APPLE_PAY",
		3: "PAYPAL",
		4: "ALIPAY",
		5: "WECHAT_PAY",
	}
	PaymentMethod_value = map[string]int32{
		"UNKNOWN_PAYMENT": 0,
		"GOOGLE_PAY":      1,
		"APPLE_PAY":       2,
		"PAYPAL":          3,
		"ALIPAY":          4,
		"WECHAT_PAY":      5,
	}
)

func (x PaymentMethod) Enum() *PaymentMethod {
	p := new(PaymentMethod)
	*p = x
	return p
}

func (x PaymentMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_payment_payment_proto_enumTypes[0].Descriptor()
}

func (PaymentMethod) Type() protoreflect.EnumType {
	return &file_proto_payment_payment_proto_enumTypes[0]
}

func (x PaymentMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentMethod.Descriptor instead.
func (PaymentMethod) EnumDescriptor() ([]byte, []int) {
	return file_proto_payment_payment_proto_rawDescGZIP(), []int{0}
}

// 支付类型枚举
type PaymentType int32

const (
	PaymentType_UNKNOWN_TYPE         PaymentType = 0
	PaymentType_VIP_PURCHASE         PaymentType = 1 // VIP购买
	PaymentType_MONTHLY_SUBSCRIPTION PaymentType = 2 // 包月订阅
	PaymentType_COIN_RECHARGE        PaymentType = 3 // 书币充值
)

// Enum value maps for PaymentType.
var (
	PaymentType_name = map[int32]string{
		0: "UNKNOWN_TYPE",
		1: "VIP_PURCHASE",
		2: "MONTHLY_SUBSCRIPTION",
		3: "COIN_RECHARGE",
	}
	PaymentType_value = map[string]int32{
		"UNKNOWN_TYPE":         0,
		"VIP_PURCHASE":         1,
		"MONTHLY_SUBSCRIPTION": 2,
		"COIN_RECHARGE":        3,
	}
)

func (x PaymentType) Enum() *PaymentType {
	p := new(PaymentType)
	*p = x
	return p
}

func (x PaymentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_payment_payment_proto_enumTypes[1].Descriptor()
}

func (PaymentType) Type() protoreflect.EnumType {
	return &file_proto_payment_payment_proto_enumTypes[1]
}

func (x PaymentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentType.Descriptor instead.
func (PaymentType) EnumDescriptor() ([]byte, []int) {
	return file_proto_payment_payment_proto_rawDescGZIP(), []int{1}
}

// 订单状态枚举
type OrderStatus int32

const (
	OrderStatus_UNKNOWN_STATUS OrderStatus = 0
	OrderStatus_PENDING        OrderStatus = 1 // 待支付
	OrderStatus_PAID           OrderStatus = 2 // 已支付
	OrderStatus_CANCELLED      OrderStatus = 3 // 已取消
	OrderStatus_REFUNDED       OrderStatus = 4 // 已退款
	OrderStatus_FAILED         OrderStatus = 5 // 支付失败
)

// Enum value maps for OrderStatus.
var (
	OrderStatus_name = map[int32]string{
		0: "UNKNOWN_STATUS",
		1: "PENDING",
		2: "PAID",
		3: "CANCELLED",
		4: "REFUNDED",
		5: "FAILED",
	}
	OrderStatus_value = map[string]int32{
		"UNKNOWN_STATUS": 0,
		"PENDING":        1,
		"PAID":           2,
		"CANCELLED":      3,
		"REFUNDED":       4,
		"FAILED":         5,
	}
)

func (x OrderStatus) Enum() *OrderStatus {
	p := new(OrderStatus)
	*p = x
	return p
}

func (x OrderStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_payment_payment_proto_enumTypes[2].Descriptor()
}

func (OrderStatus) Type() protoreflect.EnumType {
	return &file_proto_payment_payment_proto_enumTypes[2]
}

func (x OrderStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderStatus.Descriptor instead.
func (OrderStatus) EnumDescriptor() ([]byte, []int) {
	return file_proto_payment_payment_proto_rawDescGZIP(), []int{2}
}

// 创建支付订单请求
type CreatePaymentOrderReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        uint64                 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
	PaymentMethod PaymentMethod          `protobuf:"varint,2,opt,name=paymentMethod,proto3,enum=payment.PaymentMethod" json:"paymentMethod,omitempty"`
	PaymentType   PaymentType            `protobuf:"varint,3,opt,name=paymentType,proto3,enum=payment.PaymentType" json:"paymentType,omitempty"`
	Amount        int64                  `protobuf:"varint,4,opt,name=amount,proto3" json:"amount,omitempty"`                                                                              // 支付金额（分）
	Currency      string                 `protobuf:"bytes,5,opt,name=currency,proto3" json:"currency,omitempty"`                                                                           // 货币类型
	ProductId     string                 `protobuf:"bytes,6,opt,name=productId,proto3" json:"productId,omitempty"`                                                                         // 产品ID
	ProductName   string                 `protobuf:"bytes,7,opt,name=productName,proto3" json:"productName,omitempty"`                                                                     // 产品名称
	Description   string                 `protobuf:"bytes,8,opt,name=description,proto3" json:"description,omitempty"`                                                                     // 订单描述
	Metadata      map[string]string      `protobuf:"bytes,9,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` // 额外元数据
	ClientIp      string                 `protobuf:"bytes,10,opt,name=clientIp,proto3" json:"clientIp,omitempty"`                                                                          // 客户端IP
	UserAgent     string                 `protobuf:"bytes,11,opt,name=userAgent,proto3" json:"userAgent,omitempty"`                                                                        // 用户代理
	ReturnUrl     string                 `protobuf:"bytes,12,opt,name=returnUrl,proto3" json:"returnUrl,omitempty"`                                                                        // 支付成功返回URL
	CancelUrl     string                 `protobuf:"bytes,13,opt,name=cancelUrl,proto3" json:"cancelUrl,omitempty"`                                                                        // 支付取消返回URL
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePaymentOrderReq) Reset() {
	*x = CreatePaymentOrderReq{}
	mi := &file_proto_payment_payment_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePaymentOrderReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePaymentOrderReq) ProtoMessage() {}

func (x *CreatePaymentOrderReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_payment_payment_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePaymentOrderReq.ProtoReflect.Descriptor instead.
func (*CreatePaymentOrderReq) Descriptor() ([]byte, []int) {
	return file_proto_payment_payment_proto_rawDescGZIP(), []int{0}
}

func (x *CreatePaymentOrderReq) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *CreatePaymentOrderReq) GetPaymentMethod() PaymentMethod {
	if x != nil {
		return x.PaymentMethod
	}
	return PaymentMethod_UNKNOWN_PAYMENT
}

func (x *CreatePaymentOrderReq) GetPaymentType() PaymentType {
	if x != nil {
		return x.PaymentType
	}
	return PaymentType_UNKNOWN_TYPE
}

func (x *CreatePaymentOrderReq) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *CreatePaymentOrderReq) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *CreatePaymentOrderReq) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *CreatePaymentOrderReq) GetProductName() string {
	if x != nil {
		return x.ProductName
	}
	return ""
}

func (x *CreatePaymentOrderReq) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreatePaymentOrderReq) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *CreatePaymentOrderReq) GetClientIp() string {
	if x != nil {
		return x.ClientIp
	}
	return ""
}

func (x *CreatePaymentOrderReq) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

func (x *CreatePaymentOrderReq) GetReturnUrl() string {
	if x != nil {
		return x.ReturnUrl
	}
	return ""
}

func (x *CreatePaymentOrderReq) GetCancelUrl() string {
	if x != nil {
		return x.CancelUrl
	}
	return ""
}

// 创建支付订单响应
type CreatePaymentOrderResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Order         *PaymentOrderInfo      `protobuf:"bytes,3,opt,name=order,proto3" json:"order,omitempty"`
	PaymentUrl    string                 `protobuf:"bytes,4,opt,name=paymentUrl,proto3" json:"paymentUrl,omitempty"`   // 支付URL
	PaymentData   string                 `protobuf:"bytes,5,opt,name=paymentData,proto3" json:"paymentData,omitempty"` // 支付数据（如移动端SDK需要的数据）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePaymentOrderResp) Reset() {
	*x = CreatePaymentOrderResp{}
	mi := &file_proto_payment_payment_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePaymentOrderResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePaymentOrderResp) ProtoMessage() {}

func (x *CreatePaymentOrderResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_payment_payment_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePaymentOrderResp.ProtoReflect.Descriptor instead.
func (*CreatePaymentOrderResp) Descriptor() ([]byte, []int) {
	return file_proto_payment_payment_proto_rawDescGZIP(), []int{1}
}

func (x *CreatePaymentOrderResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CreatePaymentOrderResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CreatePaymentOrderResp) GetOrder() *PaymentOrderInfo {
	if x != nil {
		return x.Order
	}
	return nil
}

func (x *CreatePaymentOrderResp) GetPaymentUrl() string {
	if x != nil {
		return x.PaymentUrl
	}
	return ""
}

func (x *CreatePaymentOrderResp) GetPaymentData() string {
	if x != nil {
		return x.PaymentData
	}
	return ""
}

// 处理支付回调请求
type ProcessPaymentCallbackReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PaymentMethod PaymentMethod          `protobuf:"varint,1,opt,name=paymentMethod,proto3,enum=payment.PaymentMethod" json:"paymentMethod,omitempty"`
	OrderId       string                 `protobuf:"bytes,2,opt,name=orderId,proto3" json:"orderId,omitempty"`
	TransactionId string                 `protobuf:"bytes,3,opt,name=transactionId,proto3" json:"transactionId,omitempty"` // 第三方交易ID
	Status        OrderStatus            `protobuf:"varint,4,opt,name=status,proto3,enum=payment.OrderStatus" json:"status,omitempty"`
	Amount        int64                  `protobuf:"varint,5,opt,name=amount,proto3" json:"amount,omitempty"`
	Currency      string                 `protobuf:"bytes,6,opt,name=currency,proto3" json:"currency,omitempty"`
	CallbackData  map[string]string      `protobuf:"bytes,7,rep,name=callbackData,proto3" json:"callbackData,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"` // 回调原始数据
	Signature     string                 `protobuf:"bytes,8,opt,name=signature,proto3" json:"signature,omitempty"`                                                                                 // 签名
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProcessPaymentCallbackReq) Reset() {
	*x = ProcessPaymentCallbackReq{}
	mi := &file_proto_payment_payment_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcessPaymentCallbackReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessPaymentCallbackReq) ProtoMessage() {}

func (x *ProcessPaymentCallbackReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_payment_payment_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessPaymentCallbackReq.ProtoReflect.Descriptor instead.
func (*ProcessPaymentCallbackReq) Descriptor() ([]byte, []int) {
	return file_proto_payment_payment_proto_rawDescGZIP(), []int{2}
}

func (x *ProcessPaymentCallbackReq) GetPaymentMethod() PaymentMethod {
	if x != nil {
		return x.PaymentMethod
	}
	return PaymentMethod_UNKNOWN_PAYMENT
}

func (x *ProcessPaymentCallbackReq) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *ProcessPaymentCallbackReq) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *ProcessPaymentCallbackReq) GetStatus() OrderStatus {
	if x != nil {
		return x.Status
	}
	return OrderStatus_UNKNOWN_STATUS
}

func (x *ProcessPaymentCallbackReq) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *ProcessPaymentCallbackReq) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *ProcessPaymentCallbackReq) GetCallbackData() map[string]string {
	if x != nil {
		return x.CallbackData
	}
	return nil
}

func (x *ProcessPaymentCallbackReq) GetSignature() string {
	if x != nil {
		return x.Signature
	}
	return ""
}

// 处理支付回调响应
type ProcessPaymentCallbackResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Success       bool                   `protobuf:"varint,3,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProcessPaymentCallbackResp) Reset() {
	*x = ProcessPaymentCallbackResp{}
	mi := &file_proto_payment_payment_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProcessPaymentCallbackResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessPaymentCallbackResp) ProtoMessage() {}

func (x *ProcessPaymentCallbackResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_payment_payment_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessPaymentCallbackResp.ProtoReflect.Descriptor instead.
func (*ProcessPaymentCallbackResp) Descriptor() ([]byte, []int) {
	return file_proto_payment_payment_proto_rawDescGZIP(), []int{3}
}

func (x *ProcessPaymentCallbackResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ProcessPaymentCallbackResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ProcessPaymentCallbackResp) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// 获取支付订单请求
type GetPaymentOrderReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrderId       string                 `protobuf:"bytes,1,opt,name=orderId,proto3" json:"orderId,omitempty"`
	UserId        uint64                 `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPaymentOrderReq) Reset() {
	*x = GetPaymentOrderReq{}
	mi := &file_proto_payment_payment_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPaymentOrderReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentOrderReq) ProtoMessage() {}

func (x *GetPaymentOrderReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_payment_payment_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentOrderReq.ProtoReflect.Descriptor instead.
func (*GetPaymentOrderReq) Descriptor() ([]byte, []int) {
	return file_proto_payment_payment_proto_rawDescGZIP(), []int{4}
}

func (x *GetPaymentOrderReq) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *GetPaymentOrderReq) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

// 获取支付订单响应
type GetPaymentOrderResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Order         *PaymentOrderInfo      `protobuf:"bytes,3,opt,name=order,proto3" json:"order,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPaymentOrderResp) Reset() {
	*x = GetPaymentOrderResp{}
	mi := &file_proto_payment_payment_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPaymentOrderResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentOrderResp) ProtoMessage() {}

func (x *GetPaymentOrderResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_payment_payment_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentOrderResp.ProtoReflect.Descriptor instead.
func (*GetPaymentOrderResp) Descriptor() ([]byte, []int) {
	return file_proto_payment_payment_proto_rawDescGZIP(), []int{5}
}

func (x *GetPaymentOrderResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetPaymentOrderResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetPaymentOrderResp) GetOrder() *PaymentOrderInfo {
	if x != nil {
		return x.Order
	}
	return nil
}

// 获取支付订单列表请求
type GetPaymentOrdersReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        uint64                 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
	PaymentMethod PaymentMethod          `protobuf:"varint,2,opt,name=paymentMethod,proto3,enum=payment.PaymentMethod" json:"paymentMethod,omitempty"`
	PaymentType   PaymentType            `protobuf:"varint,3,opt,name=paymentType,proto3,enum=payment.PaymentType" json:"paymentType,omitempty"`
	Status        OrderStatus            `protobuf:"varint,4,opt,name=status,proto3,enum=payment.OrderStatus" json:"status,omitempty"`
	Page          int32                  `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,6,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	StartTime     int64                  `protobuf:"varint,7,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime       int64                  `protobuf:"varint,8,opt,name=endTime,proto3" json:"endTime,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPaymentOrdersReq) Reset() {
	*x = GetPaymentOrdersReq{}
	mi := &file_proto_payment_payment_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPaymentOrdersReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentOrdersReq) ProtoMessage() {}

func (x *GetPaymentOrdersReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_payment_payment_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentOrdersReq.ProtoReflect.Descriptor instead.
func (*GetPaymentOrdersReq) Descriptor() ([]byte, []int) {
	return file_proto_payment_payment_proto_rawDescGZIP(), []int{6}
}

func (x *GetPaymentOrdersReq) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetPaymentOrdersReq) GetPaymentMethod() PaymentMethod {
	if x != nil {
		return x.PaymentMethod
	}
	return PaymentMethod_UNKNOWN_PAYMENT
}

func (x *GetPaymentOrdersReq) GetPaymentType() PaymentType {
	if x != nil {
		return x.PaymentType
	}
	return PaymentType_UNKNOWN_TYPE
}

func (x *GetPaymentOrdersReq) GetStatus() OrderStatus {
	if x != nil {
		return x.Status
	}
	return OrderStatus_UNKNOWN_STATUS
}

func (x *GetPaymentOrdersReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetPaymentOrdersReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetPaymentOrdersReq) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *GetPaymentOrdersReq) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

// 获取支付订单列表响应
type GetPaymentOrdersResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Orders        []*PaymentOrderInfo    `protobuf:"bytes,3,rep,name=orders,proto3" json:"orders,omitempty"`
	Total         int64                  `protobuf:"varint,4,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPaymentOrdersResp) Reset() {
	*x = GetPaymentOrdersResp{}
	mi := &file_proto_payment_payment_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPaymentOrdersResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentOrdersResp) ProtoMessage() {}

func (x *GetPaymentOrdersResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_payment_payment_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentOrdersResp.ProtoReflect.Descriptor instead.
func (*GetPaymentOrdersResp) Descriptor() ([]byte, []int) {
	return file_proto_payment_payment_proto_rawDescGZIP(), []int{7}
}

func (x *GetPaymentOrdersResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetPaymentOrdersResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetPaymentOrdersResp) GetOrders() []*PaymentOrderInfo {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *GetPaymentOrdersResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 取消支付订单请求
type CancelPaymentOrderReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrderId       string                 `protobuf:"bytes,1,opt,name=orderId,proto3" json:"orderId,omitempty"`
	UserId        uint64                 `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`
	Reason        string                 `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CancelPaymentOrderReq) Reset() {
	*x = CancelPaymentOrderReq{}
	mi := &file_proto_payment_payment_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CancelPaymentOrderReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelPaymentOrderReq) ProtoMessage() {}

func (x *CancelPaymentOrderReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_payment_payment_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelPaymentOrderReq.ProtoReflect.Descriptor instead.
func (*CancelPaymentOrderReq) Descriptor() ([]byte, []int) {
	return file_proto_payment_payment_proto_rawDescGZIP(), []int{8}
}

func (x *CancelPaymentOrderReq) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *CancelPaymentOrderReq) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *CancelPaymentOrderReq) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

// 取消支付订单响应
type CancelPaymentOrderResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CancelPaymentOrderResp) Reset() {
	*x = CancelPaymentOrderResp{}
	mi := &file_proto_payment_payment_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CancelPaymentOrderResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelPaymentOrderResp) ProtoMessage() {}

func (x *CancelPaymentOrderResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_payment_payment_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelPaymentOrderResp.ProtoReflect.Descriptor instead.
func (*CancelPaymentOrderResp) Descriptor() ([]byte, []int) {
	return file_proto_payment_payment_proto_rawDescGZIP(), []int{9}
}

func (x *CancelPaymentOrderResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CancelPaymentOrderResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 退款请求
type RefundPaymentReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrderId       string                 `protobuf:"bytes,1,opt,name=orderId,proto3" json:"orderId,omitempty"`
	UserId        uint64                 `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`
	RefundAmount  int64                  `protobuf:"varint,3,opt,name=refundAmount,proto3" json:"refundAmount,omitempty"` // 退款金额（分）
	Reason        string                 `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`              // 退款原因
	RefundId      string                 `protobuf:"bytes,5,opt,name=refundId,proto3" json:"refundId,omitempty"`          // 退款单号
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RefundPaymentReq) Reset() {
	*x = RefundPaymentReq{}
	mi := &file_proto_payment_payment_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RefundPaymentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundPaymentReq) ProtoMessage() {}

func (x *RefundPaymentReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_payment_payment_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundPaymentReq.ProtoReflect.Descriptor instead.
func (*RefundPaymentReq) Descriptor() ([]byte, []int) {
	return file_proto_payment_payment_proto_rawDescGZIP(), []int{10}
}

func (x *RefundPaymentReq) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *RefundPaymentReq) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *RefundPaymentReq) GetRefundAmount() int64 {
	if x != nil {
		return x.RefundAmount
	}
	return 0
}

func (x *RefundPaymentReq) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *RefundPaymentReq) GetRefundId() string {
	if x != nil {
		return x.RefundId
	}
	return ""
}

// 退款响应
type RefundPaymentResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	RefundId      string                 `protobuf:"bytes,3,opt,name=refundId,proto3" json:"refundId,omitempty"`
	Success       bool                   `protobuf:"varint,4,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RefundPaymentResp) Reset() {
	*x = RefundPaymentResp{}
	mi := &file_proto_payment_payment_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RefundPaymentResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefundPaymentResp) ProtoMessage() {}

func (x *RefundPaymentResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_payment_payment_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefundPaymentResp.ProtoReflect.Descriptor instead.
func (*RefundPaymentResp) Descriptor() ([]byte, []int) {
	return file_proto_payment_payment_proto_rawDescGZIP(), []int{11}
}

func (x *RefundPaymentResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RefundPaymentResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *RefundPaymentResp) GetRefundId() string {
	if x != nil {
		return x.RefundId
	}
	return ""
}

func (x *RefundPaymentResp) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// 获取支付方式列表请求
type GetPaymentMethodsReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Platform      string                 `protobuf:"bytes,1,opt,name=platform,proto3" json:"platform,omitempty"` // 平台：ios, android, web
	Region        string                 `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`     // 地区
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPaymentMethodsReq) Reset() {
	*x = GetPaymentMethodsReq{}
	mi := &file_proto_payment_payment_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPaymentMethodsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentMethodsReq) ProtoMessage() {}

func (x *GetPaymentMethodsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_payment_payment_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentMethodsReq.ProtoReflect.Descriptor instead.
func (*GetPaymentMethodsReq) Descriptor() ([]byte, []int) {
	return file_proto_payment_payment_proto_rawDescGZIP(), []int{12}
}

func (x *GetPaymentMethodsReq) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *GetPaymentMethodsReq) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

// 获取支付方式列表响应
type GetPaymentMethodsResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Methods       []*PaymentMethodInfo   `protobuf:"bytes,3,rep,name=methods,proto3" json:"methods,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPaymentMethodsResp) Reset() {
	*x = GetPaymentMethodsResp{}
	mi := &file_proto_payment_payment_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPaymentMethodsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentMethodsResp) ProtoMessage() {}

func (x *GetPaymentMethodsResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_payment_payment_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentMethodsResp.ProtoReflect.Descriptor instead.
func (*GetPaymentMethodsResp) Descriptor() ([]byte, []int) {
	return file_proto_payment_payment_proto_rawDescGZIP(), []int{13}
}

func (x *GetPaymentMethodsResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetPaymentMethodsResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetPaymentMethodsResp) GetMethods() []*PaymentMethodInfo {
	if x != nil {
		return x.Methods
	}
	return nil
}

// 支付订单信息
type PaymentOrderInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrderId       string                 `protobuf:"bytes,1,opt,name=orderId,proto3" json:"orderId,omitempty"`
	UserId        uint64                 `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`
	PaymentMethod PaymentMethod          `protobuf:"varint,3,opt,name=paymentMethod,proto3,enum=payment.PaymentMethod" json:"paymentMethod,omitempty"`
	PaymentType   PaymentType            `protobuf:"varint,4,opt,name=paymentType,proto3,enum=payment.PaymentType" json:"paymentType,omitempty"`
	Amount        int64                  `protobuf:"varint,5,opt,name=amount,proto3" json:"amount,omitempty"`
	Currency      string                 `protobuf:"bytes,6,opt,name=currency,proto3" json:"currency,omitempty"`
	ProductId     string                 `protobuf:"bytes,7,opt,name=productId,proto3" json:"productId,omitempty"`
	ProductName   string                 `protobuf:"bytes,8,opt,name=productName,proto3" json:"productName,omitempty"`
	Description   string                 `protobuf:"bytes,9,opt,name=description,proto3" json:"description,omitempty"`
	Status        OrderStatus            `protobuf:"varint,10,opt,name=status,proto3,enum=payment.OrderStatus" json:"status,omitempty"`
	TransactionId string                 `protobuf:"bytes,11,opt,name=transactionId,proto3" json:"transactionId,omitempty"` // 第三方交易ID
	Metadata      map[string]string      `protobuf:"bytes,12,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	CreatedAt     int64                  `protobuf:"varint,13,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	UpdatedAt     int64                  `protobuf:"varint,14,opt,name=updatedAt,proto3" json:"updatedAt,omitempty"`
	PaidAt        int64                  `protobuf:"varint,15,opt,name=paidAt,proto3" json:"paidAt,omitempty"`
	ExpiredAt     int64                  `protobuf:"varint,16,opt,name=expiredAt,proto3" json:"expiredAt,omitempty"`
	FailureReason string                 `protobuf:"bytes,17,opt,name=failureReason,proto3" json:"failureReason,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PaymentOrderInfo) Reset() {
	*x = PaymentOrderInfo{}
	mi := &file_proto_payment_payment_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PaymentOrderInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentOrderInfo) ProtoMessage() {}

func (x *PaymentOrderInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_payment_payment_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentOrderInfo.ProtoReflect.Descriptor instead.
func (*PaymentOrderInfo) Descriptor() ([]byte, []int) {
	return file_proto_payment_payment_proto_rawDescGZIP(), []int{14}
}

func (x *PaymentOrderInfo) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *PaymentOrderInfo) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *PaymentOrderInfo) GetPaymentMethod() PaymentMethod {
	if x != nil {
		return x.PaymentMethod
	}
	return PaymentMethod_UNKNOWN_PAYMENT
}

func (x *PaymentOrderInfo) GetPaymentType() PaymentType {
	if x != nil {
		return x.PaymentType
	}
	return PaymentType_UNKNOWN_TYPE
}

func (x *PaymentOrderInfo) GetAmount() int64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *PaymentOrderInfo) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *PaymentOrderInfo) GetProductId() string {
	if x != nil {
		return x.ProductId
	}
	return ""
}

func (x *PaymentOrderInfo) GetProductName() string {
	if x != nil {
		return x.ProductName
	}
	return ""
}

func (x *PaymentOrderInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *PaymentOrderInfo) GetStatus() OrderStatus {
	if x != nil {
		return x.Status
	}
	return OrderStatus_UNKNOWN_STATUS
}

func (x *PaymentOrderInfo) GetTransactionId() string {
	if x != nil {
		return x.TransactionId
	}
	return ""
}

func (x *PaymentOrderInfo) GetMetadata() map[string]string {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *PaymentOrderInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *PaymentOrderInfo) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

func (x *PaymentOrderInfo) GetPaidAt() int64 {
	if x != nil {
		return x.PaidAt
	}
	return 0
}

func (x *PaymentOrderInfo) GetExpiredAt() int64 {
	if x != nil {
		return x.ExpiredAt
	}
	return 0
}

func (x *PaymentOrderInfo) GetFailureReason() string {
	if x != nil {
		return x.FailureReason
	}
	return ""
}

// 支付方式信息
type PaymentMethodInfo struct {
	state               protoimpl.MessageState `protogen:"open.v1"`
	Method              PaymentMethod          `protobuf:"varint,1,opt,name=method,proto3,enum=payment.PaymentMethod" json:"method,omitempty"`
	Name                string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	DisplayName         string                 `protobuf:"bytes,3,opt,name=displayName,proto3" json:"displayName,omitempty"`
	Icon                string                 `protobuf:"bytes,4,opt,name=icon,proto3" json:"icon,omitempty"`
	Enabled             bool                   `protobuf:"varint,5,opt,name=enabled,proto3" json:"enabled,omitempty"`
	SupportedCurrencies []string               `protobuf:"bytes,6,rep,name=supportedCurrencies,proto3" json:"supportedCurrencies,omitempty"`
	Config              map[string]string      `protobuf:"bytes,7,rep,name=config,proto3" json:"config,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *PaymentMethodInfo) Reset() {
	*x = PaymentMethodInfo{}
	mi := &file_proto_payment_payment_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PaymentMethodInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PaymentMethodInfo) ProtoMessage() {}

func (x *PaymentMethodInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_payment_payment_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PaymentMethodInfo.ProtoReflect.Descriptor instead.
func (*PaymentMethodInfo) Descriptor() ([]byte, []int) {
	return file_proto_payment_payment_proto_rawDescGZIP(), []int{15}
}

func (x *PaymentMethodInfo) GetMethod() PaymentMethod {
	if x != nil {
		return x.Method
	}
	return PaymentMethod_UNKNOWN_PAYMENT
}

func (x *PaymentMethodInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PaymentMethodInfo) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *PaymentMethodInfo) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *PaymentMethodInfo) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *PaymentMethodInfo) GetSupportedCurrencies() []string {
	if x != nil {
		return x.SupportedCurrencies
	}
	return nil
}

func (x *PaymentMethodInfo) GetConfig() map[string]string {
	if x != nil {
		return x.Config
	}
	return nil
}

var File_proto_payment_payment_proto protoreflect.FileDescriptor

const file_proto_payment_payment_proto_rawDesc = "" +
	"\n" +
	"\x1bproto/payment/payment.proto\x12\apayment\"\xb8\x04\n" +
	"\x15CreatePaymentOrderReq\x12\x16\n" +
	"\x06userId\x18\x01 \x01(\x04R\x06userId\x12<\n" +
	"\rpaymentMethod\x18\x02 \x01(\x0e2\x16.payment.PaymentMethodR\rpaymentMethod\x126\n" +
	"\vpaymentType\x18\x03 \x01(\x0e2\x14.payment.PaymentTypeR\vpaymentType\x12\x16\n" +
	"\x06amount\x18\x04 \x01(\x03R\x06amount\x12\x1a\n" +
	"\bcurrency\x18\x05 \x01(\tR\bcurrency\x12\x1c\n" +
	"\tproductId\x18\x06 \x01(\tR\tproductId\x12 \n" +
	"\vproductName\x18\a \x01(\tR\vproductName\x12 \n" +
	"\vdescription\x18\b \x01(\tR\vdescription\x12H\n" +
	"\bmetadata\x18\t \x03(\v2,.payment.CreatePaymentOrderReq.MetadataEntryR\bmetadata\x12\x1a\n" +
	"\bclientIp\x18\n" +
	" \x01(\tR\bclientIp\x12\x1c\n" +
	"\tuserAgent\x18\v \x01(\tR\tuserAgent\x12\x1c\n" +
	"\treturnUrl\x18\f \x01(\tR\treturnUrl\x12\x1c\n" +
	"\tcancelUrl\x18\r \x01(\tR\tcancelUrl\x1a;\n" +
	"\rMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xb9\x01\n" +
	"\x16CreatePaymentOrderResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12/\n" +
	"\x05order\x18\x03 \x01(\v2\x19.payment.PaymentOrderInfoR\x05order\x12\x1e\n" +
	"\n" +
	"paymentUrl\x18\x04 \x01(\tR\n" +
	"paymentUrl\x12 \n" +
	"\vpaymentData\x18\x05 \x01(\tR\vpaymentData\"\xb4\x03\n" +
	"\x19ProcessPaymentCallbackReq\x12<\n" +
	"\rpaymentMethod\x18\x01 \x01(\x0e2\x16.payment.PaymentMethodR\rpaymentMethod\x12\x18\n" +
	"\aorderId\x18\x02 \x01(\tR\aorderId\x12$\n" +
	"\rtransactionId\x18\x03 \x01(\tR\rtransactionId\x12,\n" +
	"\x06status\x18\x04 \x01(\x0e2\x14.payment.OrderStatusR\x06status\x12\x16\n" +
	"\x06amount\x18\x05 \x01(\x03R\x06amount\x12\x1a\n" +
	"\bcurrency\x18\x06 \x01(\tR\bcurrency\x12X\n" +
	"\fcallbackData\x18\a \x03(\v24.payment.ProcessPaymentCallbackReq.CallbackDataEntryR\fcallbackData\x12\x1c\n" +
	"\tsignature\x18\b \x01(\tR\tsignature\x1a?\n" +
	"\x11CallbackDataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"d\n" +
	"\x1aProcessPaymentCallbackResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x18\n" +
	"\asuccess\x18\x03 \x01(\bR\asuccess\"F\n" +
	"\x12GetPaymentOrderReq\x12\x18\n" +
	"\aorderId\x18\x01 \x01(\tR\aorderId\x12\x16\n" +
	"\x06userId\x18\x02 \x01(\x04R\x06userId\"t\n" +
	"\x13GetPaymentOrderResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12/\n" +
	"\x05order\x18\x03 \x01(\v2\x19.payment.PaymentOrderInfoR\x05order\"\xb9\x02\n" +
	"\x13GetPaymentOrdersReq\x12\x16\n" +
	"\x06userId\x18\x01 \x01(\x04R\x06userId\x12<\n" +
	"\rpaymentMethod\x18\x02 \x01(\x0e2\x16.payment.PaymentMethodR\rpaymentMethod\x126\n" +
	"\vpaymentType\x18\x03 \x01(\x0e2\x14.payment.PaymentTypeR\vpaymentType\x12,\n" +
	"\x06status\x18\x04 \x01(\x0e2\x14.payment.OrderStatusR\x06status\x12\x12\n" +
	"\x04page\x18\x05 \x01(\x05R\x04page\x12\x1a\n" +
	"\bpageSize\x18\x06 \x01(\x05R\bpageSize\x12\x1c\n" +
	"\tstartTime\x18\a \x01(\x03R\tstartTime\x12\x18\n" +
	"\aendTime\x18\b \x01(\x03R\aendTime\"\x8d\x01\n" +
	"\x14GetPaymentOrdersResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x121\n" +
	"\x06orders\x18\x03 \x03(\v2\x19.payment.PaymentOrderInfoR\x06orders\x12\x14\n" +
	"\x05total\x18\x04 \x01(\x03R\x05total\"a\n" +
	"\x15CancelPaymentOrderReq\x12\x18\n" +
	"\aorderId\x18\x01 \x01(\tR\aorderId\x12\x16\n" +
	"\x06userId\x18\x02 \x01(\x04R\x06userId\x12\x16\n" +
	"\x06reason\x18\x03 \x01(\tR\x06reason\"F\n" +
	"\x16CancelPaymentOrderResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\x9c\x01\n" +
	"\x10RefundPaymentReq\x12\x18\n" +
	"\aorderId\x18\x01 \x01(\tR\aorderId\x12\x16\n" +
	"\x06userId\x18\x02 \x01(\x04R\x06userId\x12\"\n" +
	"\frefundAmount\x18\x03 \x01(\x03R\frefundAmount\x12\x16\n" +
	"\x06reason\x18\x04 \x01(\tR\x06reason\x12\x1a\n" +
	"\brefundId\x18\x05 \x01(\tR\brefundId\"w\n" +
	"\x11RefundPaymentResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1a\n" +
	"\brefundId\x18\x03 \x01(\tR\brefundId\x12\x18\n" +
	"\asuccess\x18\x04 \x01(\bR\asuccess\"J\n" +
	"\x14GetPaymentMethodsReq\x12\x1a\n" +
	"\bplatform\x18\x01 \x01(\tR\bplatform\x12\x16\n" +
	"\x06region\x18\x02 \x01(\tR\x06region\"{\n" +
	"\x15GetPaymentMethodsResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x124\n" +
	"\amethods\x18\x03 \x03(\v2\x1a.payment.PaymentMethodInfoR\amethods\"\xbe\x05\n" +
	"\x10PaymentOrderInfo\x12\x18\n" +
	"\aorderId\x18\x01 \x01(\tR\aorderId\x12\x16\n" +
	"\x06userId\x18\x02 \x01(\x04R\x06userId\x12<\n" +
	"\rpaymentMethod\x18\x03 \x01(\x0e2\x16.payment.PaymentMethodR\rpaymentMethod\x126\n" +
	"\vpaymentType\x18\x04 \x01(\x0e2\x14.payment.PaymentTypeR\vpaymentType\x12\x16\n" +
	"\x06amount\x18\x05 \x01(\x03R\x06amount\x12\x1a\n" +
	"\bcurrency\x18\x06 \x01(\tR\bcurrency\x12\x1c\n" +
	"\tproductId\x18\a \x01(\tR\tproductId\x12 \n" +
	"\vproductName\x18\b \x01(\tR\vproductName\x12 \n" +
	"\vdescription\x18\t \x01(\tR\vdescription\x12,\n" +
	"\x06status\x18\n" +
	" \x01(\x0e2\x14.payment.OrderStatusR\x06status\x12$\n" +
	"\rtransactionId\x18\v \x01(\tR\rtransactionId\x12C\n" +
	"\bmetadata\x18\f \x03(\v2'.payment.PaymentOrderInfo.MetadataEntryR\bmetadata\x12\x1c\n" +
	"\tcreatedAt\x18\r \x01(\x03R\tcreatedAt\x12\x1c\n" +
	"\tupdatedAt\x18\x0e \x01(\x03R\tupdatedAt\x12\x16\n" +
	"\x06paidAt\x18\x0f \x01(\x03R\x06paidAt\x12\x1c\n" +
	"\texpiredAt\x18\x10 \x01(\x03R\texpiredAt\x12$\n" +
	"\rfailureReason\x18\x11 \x01(\tR\rfailureReason\x1a;\n" +
	"\rMetadataEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xd4\x02\n" +
	"\x11PaymentMethodInfo\x12.\n" +
	"\x06method\x18\x01 \x01(\x0e2\x16.payment.PaymentMethodR\x06method\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdisplayName\x18\x03 \x01(\tR\vdisplayName\x12\x12\n" +
	"\x04icon\x18\x04 \x01(\tR\x04icon\x12\x18\n" +
	"\aenabled\x18\x05 \x01(\bR\aenabled\x120\n" +
	"\x13supportedCurrencies\x18\x06 \x03(\tR\x13supportedCurrencies\x12>\n" +
	"\x06config\x18\a \x03(\v2&.payment.PaymentMethodInfo.ConfigEntryR\x06config\x1a9\n" +
	"\vConfigEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01*k\n" +
	"\rPaymentMethod\x12\x13\n" +
	"\x0fUNKNOWN_PAYMENT\x10\x00\x12\x0e\n" +
	"\n" +
	"GOOGLE_PAY\x10\x01\x12\r\n" +
	"\tAPPLE_PAY\x10\x02\x12\n" +
	"\n" +
	"\x06PAYPAL\x10\x03\x12\n" +
	"\n" +
	"\x06ALIPAY\x10\x04\x12\x0e\n" +
	"\n" +
	"WECHAT_PAY\x10\x05*^\n" +
	"\vPaymentType\x12\x10\n" +
	"\fUNKNOWN_TYPE\x10\x00\x12\x10\n" +
	"\fVIP_PURCHASE\x10\x01\x12\x18\n" +
	"\x14MONTHLY_SUBSCRIPTION\x10\x02\x12\x11\n" +
	"\rCOIN_RECHARGE\x10\x03*a\n" +
	"\vOrderStatus\x12\x12\n" +
	"\x0eUNKNOWN_STATUS\x10\x00\x12\v\n" +
	"\aPENDING\x10\x01\x12\b\n" +
	"\x04PAID\x10\x02\x12\r\n" +
	"\tCANCELLED\x10\x03\x12\f\n" +
	"\bREFUNDED\x10\x04\x12\n" +
	"\n" +
	"\x06FAILED\x10\x052\xd5\x04\n" +
	"\aPayment\x12U\n" +
	"\x12CreatePaymentOrder\x12\x1e.payment.CreatePaymentOrderReq\x1a\x1f.payment.CreatePaymentOrderResp\x12a\n" +
	"\x16ProcessPaymentCallback\x12\".payment.ProcessPaymentCallbackReq\x1a#.payment.ProcessPaymentCallbackResp\x12L\n" +
	"\x0fGetPaymentOrder\x12\x1b.payment.GetPaymentOrderReq\x1a\x1c.payment.GetPaymentOrderResp\x12O\n" +
	"\x10GetPaymentOrders\x12\x1c.payment.GetPaymentOrdersReq\x1a\x1d.payment.GetPaymentOrdersResp\x12U\n" +
	"\x12CancelPaymentOrder\x12\x1e.payment.CancelPaymentOrderReq\x1a\x1f.payment.CancelPaymentOrderResp\x12F\n" +
	"\rRefundPayment\x12\x19.payment.RefundPaymentReq\x1a\x1a.payment.RefundPaymentResp\x12R\n" +
	"\x11GetPaymentMethods\x12\x1d.payment.GetPaymentMethodsReq\x1a\x1e.payment.GetPaymentMethodsRespB4Z2creativematrix.com/beyondreading/gen/proto/paymentb\x06proto3"

var (
	file_proto_payment_payment_proto_rawDescOnce sync.Once
	file_proto_payment_payment_proto_rawDescData []byte
)

func file_proto_payment_payment_proto_rawDescGZIP() []byte {
	file_proto_payment_payment_proto_rawDescOnce.Do(func() {
		file_proto_payment_payment_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_payment_payment_proto_rawDesc), len(file_proto_payment_payment_proto_rawDesc)))
	})
	return file_proto_payment_payment_proto_rawDescData
}

var file_proto_payment_payment_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_proto_payment_payment_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_proto_payment_payment_proto_goTypes = []any{
	(PaymentMethod)(0),                 // 0: payment.PaymentMethod
	(PaymentType)(0),                   // 1: payment.PaymentType
	(OrderStatus)(0),                   // 2: payment.OrderStatus
	(*CreatePaymentOrderReq)(nil),      // 3: payment.CreatePaymentOrderReq
	(*CreatePaymentOrderResp)(nil),     // 4: payment.CreatePaymentOrderResp
	(*ProcessPaymentCallbackReq)(nil),  // 5: payment.ProcessPaymentCallbackReq
	(*ProcessPaymentCallbackResp)(nil), // 6: payment.ProcessPaymentCallbackResp
	(*GetPaymentOrderReq)(nil),         // 7: payment.GetPaymentOrderReq
	(*GetPaymentOrderResp)(nil),        // 8: payment.GetPaymentOrderResp
	(*GetPaymentOrdersReq)(nil),        // 9: payment.GetPaymentOrdersReq
	(*GetPaymentOrdersResp)(nil),       // 10: payment.GetPaymentOrdersResp
	(*CancelPaymentOrderReq)(nil),      // 11: payment.CancelPaymentOrderReq
	(*CancelPaymentOrderResp)(nil),     // 12: payment.CancelPaymentOrderResp
	(*RefundPaymentReq)(nil),           // 13: payment.RefundPaymentReq
	(*RefundPaymentResp)(nil),          // 14: payment.RefundPaymentResp
	(*GetPaymentMethodsReq)(nil),       // 15: payment.GetPaymentMethodsReq
	(*GetPaymentMethodsResp)(nil),      // 16: payment.GetPaymentMethodsResp
	(*PaymentOrderInfo)(nil),           // 17: payment.PaymentOrderInfo
	(*PaymentMethodInfo)(nil),          // 18: payment.PaymentMethodInfo
	nil,                                // 19: payment.CreatePaymentOrderReq.MetadataEntry
	nil,                                // 20: payment.ProcessPaymentCallbackReq.CallbackDataEntry
	nil,                                // 21: payment.PaymentOrderInfo.MetadataEntry
	nil,                                // 22: payment.PaymentMethodInfo.ConfigEntry
}
var file_proto_payment_payment_proto_depIdxs = []int32{
	0,  // 0: payment.CreatePaymentOrderReq.paymentMethod:type_name -> payment.PaymentMethod
	1,  // 1: payment.CreatePaymentOrderReq.paymentType:type_name -> payment.PaymentType
	19, // 2: payment.CreatePaymentOrderReq.metadata:type_name -> payment.CreatePaymentOrderReq.MetadataEntry
	17, // 3: payment.CreatePaymentOrderResp.order:type_name -> payment.PaymentOrderInfo
	0,  // 4: payment.ProcessPaymentCallbackReq.paymentMethod:type_name -> payment.PaymentMethod
	2,  // 5: payment.ProcessPaymentCallbackReq.status:type_name -> payment.OrderStatus
	20, // 6: payment.ProcessPaymentCallbackReq.callbackData:type_name -> payment.ProcessPaymentCallbackReq.CallbackDataEntry
	17, // 7: payment.GetPaymentOrderResp.order:type_name -> payment.PaymentOrderInfo
	0,  // 8: payment.GetPaymentOrdersReq.paymentMethod:type_name -> payment.PaymentMethod
	1,  // 9: payment.GetPaymentOrdersReq.paymentType:type_name -> payment.PaymentType
	2,  // 10: payment.GetPaymentOrdersReq.status:type_name -> payment.OrderStatus
	17, // 11: payment.GetPaymentOrdersResp.orders:type_name -> payment.PaymentOrderInfo
	18, // 12: payment.GetPaymentMethodsResp.methods:type_name -> payment.PaymentMethodInfo
	0,  // 13: payment.PaymentOrderInfo.paymentMethod:type_name -> payment.PaymentMethod
	1,  // 14: payment.PaymentOrderInfo.paymentType:type_name -> payment.PaymentType
	2,  // 15: payment.PaymentOrderInfo.status:type_name -> payment.OrderStatus
	21, // 16: payment.PaymentOrderInfo.metadata:type_name -> payment.PaymentOrderInfo.MetadataEntry
	0,  // 17: payment.PaymentMethodInfo.method:type_name -> payment.PaymentMethod
	22, // 18: payment.PaymentMethodInfo.config:type_name -> payment.PaymentMethodInfo.ConfigEntry
	3,  // 19: payment.Payment.CreatePaymentOrder:input_type -> payment.CreatePaymentOrderReq
	5,  // 20: payment.Payment.ProcessPaymentCallback:input_type -> payment.ProcessPaymentCallbackReq
	7,  // 21: payment.Payment.GetPaymentOrder:input_type -> payment.GetPaymentOrderReq
	9,  // 22: payment.Payment.GetPaymentOrders:input_type -> payment.GetPaymentOrdersReq
	11, // 23: payment.Payment.CancelPaymentOrder:input_type -> payment.CancelPaymentOrderReq
	13, // 24: payment.Payment.RefundPayment:input_type -> payment.RefundPaymentReq
	15, // 25: payment.Payment.GetPaymentMethods:input_type -> payment.GetPaymentMethodsReq
	4,  // 26: payment.Payment.CreatePaymentOrder:output_type -> payment.CreatePaymentOrderResp
	6,  // 27: payment.Payment.ProcessPaymentCallback:output_type -> payment.ProcessPaymentCallbackResp
	8,  // 28: payment.Payment.GetPaymentOrder:output_type -> payment.GetPaymentOrderResp
	10, // 29: payment.Payment.GetPaymentOrders:output_type -> payment.GetPaymentOrdersResp
	12, // 30: payment.Payment.CancelPaymentOrder:output_type -> payment.CancelPaymentOrderResp
	14, // 31: payment.Payment.RefundPayment:output_type -> payment.RefundPaymentResp
	16, // 32: payment.Payment.GetPaymentMethods:output_type -> payment.GetPaymentMethodsResp
	26, // [26:33] is the sub-list for method output_type
	19, // [19:26] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_proto_payment_payment_proto_init() }
func file_proto_payment_payment_proto_init() {
	if File_proto_payment_payment_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_payment_payment_proto_rawDesc), len(file_proto_payment_payment_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_payment_payment_proto_goTypes,
		DependencyIndexes: file_proto_payment_payment_proto_depIdxs,
		EnumInfos:         file_proto_payment_payment_proto_enumTypes,
		MessageInfos:      file_proto_payment_payment_proto_msgTypes,
	}.Build()
	File_proto_payment_payment_proto = out.File
	file_proto_payment_payment_proto_goTypes = nil
	file_proto_payment_payment_proto_depIdxs = nil
}
