syntax = "proto3";
package books;
option go_package = "creativematrix.com/beyondreading/proto/books";
import "proto/pbcommon/common.proto";

//import "google/api/annotations.proto";

service books {
  rpc findByIds(FindByIdsReq) returns (BooklistResp);

  rpc findBooksInfo(FindByIdsReq) returns (FindBooksInfoResp);

  rpc addBook(pbcommon.Book) returns (AddBookResp);

  rpc updateBook(UpdateBookReq) returns (NormalResponse);

  rpc GetBookCopyrightDesc(GetBookCopyrightDescRequest) returns (GetBookCopyrightDescResponse);

  rpc BatchGetBookCopyrightDesc(BatchGetBookCopyrightDescRequest) returns (BatchGetBookCopyrightDescResponse);

  rpc findCpSourceByBooks(FindCpSourceByBooksRequest) returns (FindCpSourceByBooksResponse);

  rpc AddCpSource(pbcommon.CpSource) returns (NormalResponse);

  rpc ListCopyrightDesc(ListCopyrightDescRequest) returns (ListCopyrightDescResponse);

  rpc CreateCopyrightDesc(CreateCopyrightDescRequest) returns (Empty);

  rpc UpdateCopyrightDesc(UpdateCopyrightDescRequest) returns (Empty);

  rpc DeleteCopyrightDesc(DeleteCopyrightDescRequest) returns (Empty);

  rpc checkBookToShelf(CheckBookToShelfReq)returns(CheckBookToShelfRes);
}

message UpdateBookReq {
  pbcommon.Book book = 1;
  repeated string updateField = 2;
}

message FindByIdReq {
  string id = 1;
}
message BookIds{
  repeated string ids = 1;
}

message FindByIdsReq {
  repeated string ids = 1;
//  repeated string selects = 2;
//  repeated string sort = 3;
}

message BooklistResp {
  string err = 1;
  repeated pbcommon.Book books = 2;
}

message BookResp {
  string err = 1;
  pbcommon.Book book = 2;
}

message FindBooksInfoResp{
  map<string,pbcommon.BookInfo> books = 1;
}

message AddBookResp {
  string id = 1;
}

message NormalResponse {
  string err = 1;
  string message = 2;
}

message FindCpSourceByBooksRequest {
  repeated string books = 1;
}

message FindCpSourceByBooksResponse {
  string Err = 1;
  repeated pbcommon.CpSource cpSources = 2;
}

message Empty {
}

message CopyrightDesc {
  string id = 1;
  string source = 2;
  string text = 3;
}

message GetBookCopyrightDescRequest {
  string bookId = 1;
}

message GetBookCopyrightDescResponse {
  string text = 1;
}

message BatchGetBookCopyrightDescRequest {
  repeated string bookIds = 1;
}

message BatchGetBookCopyrightDescResponse {
  map<string, string> texts = 1;
}

message ListCopyrightDescRequest {
  int32 pageSize = 1;
  int32 pageNum = 2;
}

message ListCopyrightDescResponse {
  repeated CopyrightDesc copyrightDescs = 1;
}

message CreateCopyrightDescRequest {
  CopyrightDesc copyrightDesc = 1;
}

message UpdateCopyrightDescRequest {
  CopyrightDesc copyrightDesc = 1;
}

message DeleteCopyrightDescRequest {
  string id = 1;
}

message CheckBookToShelfReq{
  string appName = 1;
  repeated string books = 2;
}
message CheckBookToShelfRes{
  repeated string notAllow = 1;
  repeated string allow = 2;
}