// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: proto/books/books.proto

package books

import (
	pbcommon "creativematrix.com/beyondreading/proto/pbcommon"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UpdateBookReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Book          *pbcommon.Book         `protobuf:"bytes,1,opt,name=book,proto3" json:"book,omitempty"`
	UpdateField   []string               `protobuf:"bytes,2,rep,name=updateField,proto3" json:"updateField,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateBookReq) Reset() {
	*x = UpdateBookReq{}
	mi := &file_proto_books_books_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateBookReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookReq) ProtoMessage() {}

func (x *UpdateBookReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_books_books_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookReq.ProtoReflect.Descriptor instead.
func (*UpdateBookReq) Descriptor() ([]byte, []int) {
	return file_proto_books_books_proto_rawDescGZIP(), []int{0}
}

func (x *UpdateBookReq) GetBook() *pbcommon.Book {
	if x != nil {
		return x.Book
	}
	return nil
}

func (x *UpdateBookReq) GetUpdateField() []string {
	if x != nil {
		return x.UpdateField
	}
	return nil
}

type FindByIdReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FindByIdReq) Reset() {
	*x = FindByIdReq{}
	mi := &file_proto_books_books_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FindByIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindByIdReq) ProtoMessage() {}

func (x *FindByIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_books_books_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindByIdReq.ProtoReflect.Descriptor instead.
func (*FindByIdReq) Descriptor() ([]byte, []int) {
	return file_proto_books_books_proto_rawDescGZIP(), []int{1}
}

func (x *FindByIdReq) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type BookIds struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ids           []string               `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BookIds) Reset() {
	*x = BookIds{}
	mi := &file_proto_books_books_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BookIds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookIds) ProtoMessage() {}

func (x *BookIds) ProtoReflect() protoreflect.Message {
	mi := &file_proto_books_books_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookIds.ProtoReflect.Descriptor instead.
func (*BookIds) Descriptor() ([]byte, []int) {
	return file_proto_books_books_proto_rawDescGZIP(), []int{2}
}

func (x *BookIds) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type FindByIdsReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ids           []string               `protobuf:"bytes,1,rep,name=ids,proto3" json:"ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FindByIdsReq) Reset() {
	*x = FindByIdsReq{}
	mi := &file_proto_books_books_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FindByIdsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindByIdsReq) ProtoMessage() {}

func (x *FindByIdsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_books_books_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindByIdsReq.ProtoReflect.Descriptor instead.
func (*FindByIdsReq) Descriptor() ([]byte, []int) {
	return file_proto_books_books_proto_rawDescGZIP(), []int{3}
}

func (x *FindByIdsReq) GetIds() []string {
	if x != nil {
		return x.Ids
	}
	return nil
}

type BooklistResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Err           string                 `protobuf:"bytes,1,opt,name=err,proto3" json:"err,omitempty"`
	Books         []*pbcommon.Book       `protobuf:"bytes,2,rep,name=books,proto3" json:"books,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BooklistResp) Reset() {
	*x = BooklistResp{}
	mi := &file_proto_books_books_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BooklistResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BooklistResp) ProtoMessage() {}

func (x *BooklistResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_books_books_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BooklistResp.ProtoReflect.Descriptor instead.
func (*BooklistResp) Descriptor() ([]byte, []int) {
	return file_proto_books_books_proto_rawDescGZIP(), []int{4}
}

func (x *BooklistResp) GetErr() string {
	if x != nil {
		return x.Err
	}
	return ""
}

func (x *BooklistResp) GetBooks() []*pbcommon.Book {
	if x != nil {
		return x.Books
	}
	return nil
}

type BookResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Err           string                 `protobuf:"bytes,1,opt,name=err,proto3" json:"err,omitempty"`
	Book          *pbcommon.Book         `protobuf:"bytes,2,opt,name=book,proto3" json:"book,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BookResp) Reset() {
	*x = BookResp{}
	mi := &file_proto_books_books_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BookResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookResp) ProtoMessage() {}

func (x *BookResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_books_books_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookResp.ProtoReflect.Descriptor instead.
func (*BookResp) Descriptor() ([]byte, []int) {
	return file_proto_books_books_proto_rawDescGZIP(), []int{5}
}

func (x *BookResp) GetErr() string {
	if x != nil {
		return x.Err
	}
	return ""
}

func (x *BookResp) GetBook() *pbcommon.Book {
	if x != nil {
		return x.Book
	}
	return nil
}

type FindBooksInfoResp struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	Books         map[string]*pbcommon.BookInfo `protobuf:"bytes,1,rep,name=books,proto3" json:"books,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FindBooksInfoResp) Reset() {
	*x = FindBooksInfoResp{}
	mi := &file_proto_books_books_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FindBooksInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindBooksInfoResp) ProtoMessage() {}

func (x *FindBooksInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_books_books_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindBooksInfoResp.ProtoReflect.Descriptor instead.
func (*FindBooksInfoResp) Descriptor() ([]byte, []int) {
	return file_proto_books_books_proto_rawDescGZIP(), []int{6}
}

func (x *FindBooksInfoResp) GetBooks() map[string]*pbcommon.BookInfo {
	if x != nil {
		return x.Books
	}
	return nil
}

type AddBookResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddBookResp) Reset() {
	*x = AddBookResp{}
	mi := &file_proto_books_books_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddBookResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddBookResp) ProtoMessage() {}

func (x *AddBookResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_books_books_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddBookResp.ProtoReflect.Descriptor instead.
func (*AddBookResp) Descriptor() ([]byte, []int) {
	return file_proto_books_books_proto_rawDescGZIP(), []int{7}
}

func (x *AddBookResp) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type NormalResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Err           string                 `protobuf:"bytes,1,opt,name=err,proto3" json:"err,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NormalResponse) Reset() {
	*x = NormalResponse{}
	mi := &file_proto_books_books_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NormalResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NormalResponse) ProtoMessage() {}

func (x *NormalResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_books_books_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NormalResponse.ProtoReflect.Descriptor instead.
func (*NormalResponse) Descriptor() ([]byte, []int) {
	return file_proto_books_books_proto_rawDescGZIP(), []int{8}
}

func (x *NormalResponse) GetErr() string {
	if x != nil {
		return x.Err
	}
	return ""
}

func (x *NormalResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type FindCpSourceByBooksRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Books         []string               `protobuf:"bytes,1,rep,name=books,proto3" json:"books,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FindCpSourceByBooksRequest) Reset() {
	*x = FindCpSourceByBooksRequest{}
	mi := &file_proto_books_books_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FindCpSourceByBooksRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindCpSourceByBooksRequest) ProtoMessage() {}

func (x *FindCpSourceByBooksRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_books_books_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindCpSourceByBooksRequest.ProtoReflect.Descriptor instead.
func (*FindCpSourceByBooksRequest) Descriptor() ([]byte, []int) {
	return file_proto_books_books_proto_rawDescGZIP(), []int{9}
}

func (x *FindCpSourceByBooksRequest) GetBooks() []string {
	if x != nil {
		return x.Books
	}
	return nil
}

type FindCpSourceByBooksResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Err           string                 `protobuf:"bytes,1,opt,name=Err,proto3" json:"Err,omitempty"`
	CpSources     []*pbcommon.CpSource   `protobuf:"bytes,2,rep,name=cpSources,proto3" json:"cpSources,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FindCpSourceByBooksResponse) Reset() {
	*x = FindCpSourceByBooksResponse{}
	mi := &file_proto_books_books_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FindCpSourceByBooksResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FindCpSourceByBooksResponse) ProtoMessage() {}

func (x *FindCpSourceByBooksResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_books_books_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FindCpSourceByBooksResponse.ProtoReflect.Descriptor instead.
func (*FindCpSourceByBooksResponse) Descriptor() ([]byte, []int) {
	return file_proto_books_books_proto_rawDescGZIP(), []int{10}
}

func (x *FindCpSourceByBooksResponse) GetErr() string {
	if x != nil {
		return x.Err
	}
	return ""
}

func (x *FindCpSourceByBooksResponse) GetCpSources() []*pbcommon.CpSource {
	if x != nil {
		return x.CpSources
	}
	return nil
}

type Empty struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Empty) Reset() {
	*x = Empty{}
	mi := &file_proto_books_books_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_proto_books_books_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_proto_books_books_proto_rawDescGZIP(), []int{11}
}

type CopyrightDesc struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Source        string                 `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
	Text          string                 `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CopyrightDesc) Reset() {
	*x = CopyrightDesc{}
	mi := &file_proto_books_books_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CopyrightDesc) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyrightDesc) ProtoMessage() {}

func (x *CopyrightDesc) ProtoReflect() protoreflect.Message {
	mi := &file_proto_books_books_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyrightDesc.ProtoReflect.Descriptor instead.
func (*CopyrightDesc) Descriptor() ([]byte, []int) {
	return file_proto_books_books_proto_rawDescGZIP(), []int{12}
}

func (x *CopyrightDesc) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CopyrightDesc) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *CopyrightDesc) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

type GetBookCopyrightDescRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BookId        string                 `protobuf:"bytes,1,opt,name=bookId,proto3" json:"bookId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBookCopyrightDescRequest) Reset() {
	*x = GetBookCopyrightDescRequest{}
	mi := &file_proto_books_books_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBookCopyrightDescRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBookCopyrightDescRequest) ProtoMessage() {}

func (x *GetBookCopyrightDescRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_books_books_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBookCopyrightDescRequest.ProtoReflect.Descriptor instead.
func (*GetBookCopyrightDescRequest) Descriptor() ([]byte, []int) {
	return file_proto_books_books_proto_rawDescGZIP(), []int{13}
}

func (x *GetBookCopyrightDescRequest) GetBookId() string {
	if x != nil {
		return x.BookId
	}
	return ""
}

type GetBookCopyrightDescResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Text          string                 `protobuf:"bytes,1,opt,name=text,proto3" json:"text,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBookCopyrightDescResponse) Reset() {
	*x = GetBookCopyrightDescResponse{}
	mi := &file_proto_books_books_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBookCopyrightDescResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBookCopyrightDescResponse) ProtoMessage() {}

func (x *GetBookCopyrightDescResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_books_books_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBookCopyrightDescResponse.ProtoReflect.Descriptor instead.
func (*GetBookCopyrightDescResponse) Descriptor() ([]byte, []int) {
	return file_proto_books_books_proto_rawDescGZIP(), []int{14}
}

func (x *GetBookCopyrightDescResponse) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

type BatchGetBookCopyrightDescRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BookIds       []string               `protobuf:"bytes,1,rep,name=bookIds,proto3" json:"bookIds,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchGetBookCopyrightDescRequest) Reset() {
	*x = BatchGetBookCopyrightDescRequest{}
	mi := &file_proto_books_books_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchGetBookCopyrightDescRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetBookCopyrightDescRequest) ProtoMessage() {}

func (x *BatchGetBookCopyrightDescRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_books_books_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetBookCopyrightDescRequest.ProtoReflect.Descriptor instead.
func (*BatchGetBookCopyrightDescRequest) Descriptor() ([]byte, []int) {
	return file_proto_books_books_proto_rawDescGZIP(), []int{15}
}

func (x *BatchGetBookCopyrightDescRequest) GetBookIds() []string {
	if x != nil {
		return x.BookIds
	}
	return nil
}

type BatchGetBookCopyrightDescResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Texts         map[string]string      `protobuf:"bytes,1,rep,name=texts,proto3" json:"texts,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchGetBookCopyrightDescResponse) Reset() {
	*x = BatchGetBookCopyrightDescResponse{}
	mi := &file_proto_books_books_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchGetBookCopyrightDescResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchGetBookCopyrightDescResponse) ProtoMessage() {}

func (x *BatchGetBookCopyrightDescResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_books_books_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchGetBookCopyrightDescResponse.ProtoReflect.Descriptor instead.
func (*BatchGetBookCopyrightDescResponse) Descriptor() ([]byte, []int) {
	return file_proto_books_books_proto_rawDescGZIP(), []int{16}
}

func (x *BatchGetBookCopyrightDescResponse) GetTexts() map[string]string {
	if x != nil {
		return x.Texts
	}
	return nil
}

type ListCopyrightDescRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PageSize      int32                  `protobuf:"varint,1,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	PageNum       int32                  `protobuf:"varint,2,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCopyrightDescRequest) Reset() {
	*x = ListCopyrightDescRequest{}
	mi := &file_proto_books_books_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCopyrightDescRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCopyrightDescRequest) ProtoMessage() {}

func (x *ListCopyrightDescRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_books_books_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCopyrightDescRequest.ProtoReflect.Descriptor instead.
func (*ListCopyrightDescRequest) Descriptor() ([]byte, []int) {
	return file_proto_books_books_proto_rawDescGZIP(), []int{17}
}

func (x *ListCopyrightDescRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListCopyrightDescRequest) GetPageNum() int32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

type ListCopyrightDescResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	CopyrightDescs []*CopyrightDesc       `protobuf:"bytes,1,rep,name=copyrightDescs,proto3" json:"copyrightDescs,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ListCopyrightDescResponse) Reset() {
	*x = ListCopyrightDescResponse{}
	mi := &file_proto_books_books_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCopyrightDescResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCopyrightDescResponse) ProtoMessage() {}

func (x *ListCopyrightDescResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_books_books_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCopyrightDescResponse.ProtoReflect.Descriptor instead.
func (*ListCopyrightDescResponse) Descriptor() ([]byte, []int) {
	return file_proto_books_books_proto_rawDescGZIP(), []int{18}
}

func (x *ListCopyrightDescResponse) GetCopyrightDescs() []*CopyrightDesc {
	if x != nil {
		return x.CopyrightDescs
	}
	return nil
}

type CreateCopyrightDescRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CopyrightDesc *CopyrightDesc         `protobuf:"bytes,1,opt,name=copyrightDesc,proto3" json:"copyrightDesc,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCopyrightDescRequest) Reset() {
	*x = CreateCopyrightDescRequest{}
	mi := &file_proto_books_books_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCopyrightDescRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCopyrightDescRequest) ProtoMessage() {}

func (x *CreateCopyrightDescRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_books_books_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCopyrightDescRequest.ProtoReflect.Descriptor instead.
func (*CreateCopyrightDescRequest) Descriptor() ([]byte, []int) {
	return file_proto_books_books_proto_rawDescGZIP(), []int{19}
}

func (x *CreateCopyrightDescRequest) GetCopyrightDesc() *CopyrightDesc {
	if x != nil {
		return x.CopyrightDesc
	}
	return nil
}

type UpdateCopyrightDescRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CopyrightDesc *CopyrightDesc         `protobuf:"bytes,1,opt,name=copyrightDesc,proto3" json:"copyrightDesc,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCopyrightDescRequest) Reset() {
	*x = UpdateCopyrightDescRequest{}
	mi := &file_proto_books_books_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCopyrightDescRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCopyrightDescRequest) ProtoMessage() {}

func (x *UpdateCopyrightDescRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_books_books_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCopyrightDescRequest.ProtoReflect.Descriptor instead.
func (*UpdateCopyrightDescRequest) Descriptor() ([]byte, []int) {
	return file_proto_books_books_proto_rawDescGZIP(), []int{20}
}

func (x *UpdateCopyrightDescRequest) GetCopyrightDesc() *CopyrightDesc {
	if x != nil {
		return x.CopyrightDesc
	}
	return nil
}

type DeleteCopyrightDescRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCopyrightDescRequest) Reset() {
	*x = DeleteCopyrightDescRequest{}
	mi := &file_proto_books_books_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCopyrightDescRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCopyrightDescRequest) ProtoMessage() {}

func (x *DeleteCopyrightDescRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_books_books_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCopyrightDescRequest.ProtoReflect.Descriptor instead.
func (*DeleteCopyrightDescRequest) Descriptor() ([]byte, []int) {
	return file_proto_books_books_proto_rawDescGZIP(), []int{21}
}

func (x *DeleteCopyrightDescRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type CheckBookToShelfReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AppName       string                 `protobuf:"bytes,1,opt,name=appName,proto3" json:"appName,omitempty"`
	Books         []string               `protobuf:"bytes,2,rep,name=books,proto3" json:"books,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckBookToShelfReq) Reset() {
	*x = CheckBookToShelfReq{}
	mi := &file_proto_books_books_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckBookToShelfReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckBookToShelfReq) ProtoMessage() {}

func (x *CheckBookToShelfReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_books_books_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckBookToShelfReq.ProtoReflect.Descriptor instead.
func (*CheckBookToShelfReq) Descriptor() ([]byte, []int) {
	return file_proto_books_books_proto_rawDescGZIP(), []int{22}
}

func (x *CheckBookToShelfReq) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *CheckBookToShelfReq) GetBooks() []string {
	if x != nil {
		return x.Books
	}
	return nil
}

type CheckBookToShelfRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NotAllow      []string               `protobuf:"bytes,1,rep,name=notAllow,proto3" json:"notAllow,omitempty"`
	Allow         []string               `protobuf:"bytes,2,rep,name=allow,proto3" json:"allow,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckBookToShelfRes) Reset() {
	*x = CheckBookToShelfRes{}
	mi := &file_proto_books_books_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckBookToShelfRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckBookToShelfRes) ProtoMessage() {}

func (x *CheckBookToShelfRes) ProtoReflect() protoreflect.Message {
	mi := &file_proto_books_books_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckBookToShelfRes.ProtoReflect.Descriptor instead.
func (*CheckBookToShelfRes) Descriptor() ([]byte, []int) {
	return file_proto_books_books_proto_rawDescGZIP(), []int{23}
}

func (x *CheckBookToShelfRes) GetNotAllow() []string {
	if x != nil {
		return x.NotAllow
	}
	return nil
}

func (x *CheckBookToShelfRes) GetAllow() []string {
	if x != nil {
		return x.Allow
	}
	return nil
}

var File_proto_books_books_proto protoreflect.FileDescriptor

const file_proto_books_books_proto_rawDesc = "" +
	"\n" +
	"\x17proto/books/books.proto\x12\x05books\x1a\x1bproto/pbcommon/common.proto\"U\n" +
	"\rUpdateBookReq\x12\"\n" +
	"\x04book\x18\x01 \x01(\v2\x0e.pbcommon.BookR\x04book\x12 \n" +
	"\vupdateField\x18\x02 \x03(\tR\vupdateField\"\x1d\n" +
	"\vFindByIdReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"\x1b\n" +
	"\aBookIds\x12\x10\n" +
	"\x03ids\x18\x01 \x03(\tR\x03ids\" \n" +
	"\fFindByIdsReq\x12\x10\n" +
	"\x03ids\x18\x01 \x03(\tR\x03ids\"F\n" +
	"\fBooklistResp\x12\x10\n" +
	"\x03err\x18\x01 \x01(\tR\x03err\x12$\n" +
	"\x05books\x18\x02 \x03(\v2\x0e.pbcommon.BookR\x05books\"@\n" +
	"\bBookResp\x12\x10\n" +
	"\x03err\x18\x01 \x01(\tR\x03err\x12\"\n" +
	"\x04book\x18\x02 \x01(\v2\x0e.pbcommon.BookR\x04book\"\x9c\x01\n" +
	"\x11FindBooksInfoResp\x129\n" +
	"\x05books\x18\x01 \x03(\v2#.books.FindBooksInfoResp.BooksEntryR\x05books\x1aL\n" +
	"\n" +
	"BooksEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12(\n" +
	"\x05value\x18\x02 \x01(\v2\x12.pbcommon.BookInfoR\x05value:\x028\x01\"\x1d\n" +
	"\vAddBookResp\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"<\n" +
	"\x0eNormalResponse\x12\x10\n" +
	"\x03err\x18\x01 \x01(\tR\x03err\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"2\n" +
	"\x1aFindCpSourceByBooksRequest\x12\x14\n" +
	"\x05books\x18\x01 \x03(\tR\x05books\"a\n" +
	"\x1bFindCpSourceByBooksResponse\x12\x10\n" +
	"\x03Err\x18\x01 \x01(\tR\x03Err\x120\n" +
	"\tcpSources\x18\x02 \x03(\v2\x12.pbcommon.CpSourceR\tcpSources\"\a\n" +
	"\x05Empty\"K\n" +
	"\rCopyrightDesc\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x16\n" +
	"\x06source\x18\x02 \x01(\tR\x06source\x12\x12\n" +
	"\x04text\x18\x03 \x01(\tR\x04text\"5\n" +
	"\x1bGetBookCopyrightDescRequest\x12\x16\n" +
	"\x06bookId\x18\x01 \x01(\tR\x06bookId\"2\n" +
	"\x1cGetBookCopyrightDescResponse\x12\x12\n" +
	"\x04text\x18\x01 \x01(\tR\x04text\"<\n" +
	" BatchGetBookCopyrightDescRequest\x12\x18\n" +
	"\abookIds\x18\x01 \x03(\tR\abookIds\"\xa8\x01\n" +
	"!BatchGetBookCopyrightDescResponse\x12I\n" +
	"\x05texts\x18\x01 \x03(\v23.books.BatchGetBookCopyrightDescResponse.TextsEntryR\x05texts\x1a8\n" +
	"\n" +
	"TextsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"P\n" +
	"\x18ListCopyrightDescRequest\x12\x1a\n" +
	"\bpageSize\x18\x01 \x01(\x05R\bpageSize\x12\x18\n" +
	"\apageNum\x18\x02 \x01(\x05R\apageNum\"Y\n" +
	"\x19ListCopyrightDescResponse\x12<\n" +
	"\x0ecopyrightDescs\x18\x01 \x03(\v2\x14.books.CopyrightDescR\x0ecopyrightDescs\"X\n" +
	"\x1aCreateCopyrightDescRequest\x12:\n" +
	"\rcopyrightDesc\x18\x01 \x01(\v2\x14.books.CopyrightDescR\rcopyrightDesc\"X\n" +
	"\x1aUpdateCopyrightDescRequest\x12:\n" +
	"\rcopyrightDesc\x18\x01 \x01(\v2\x14.books.CopyrightDescR\rcopyrightDesc\",\n" +
	"\x1aDeleteCopyrightDescRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"E\n" +
	"\x13CheckBookToShelfReq\x12\x18\n" +
	"\aappName\x18\x01 \x01(\tR\aappName\x12\x14\n" +
	"\x05books\x18\x02 \x03(\tR\x05books\"G\n" +
	"\x13CheckBookToShelfRes\x12\x1a\n" +
	"\bnotAllow\x18\x01 \x03(\tR\bnotAllow\x12\x14\n" +
	"\x05allow\x18\x02 \x03(\tR\x05allow2\xcd\a\n" +
	"\x05books\x125\n" +
	"\tfindByIds\x12\x13.books.FindByIdsReq\x1a\x13.books.BooklistResp\x12>\n" +
	"\rfindBooksInfo\x12\x13.books.FindByIdsReq\x1a\x18.books.FindBooksInfoResp\x12-\n" +
	"\aaddBook\x12\x0e.pbcommon.Book\x1a\x12.books.AddBookResp\x129\n" +
	"\n" +
	"updateBook\x12\x14.books.UpdateBookReq\x1a\x15.books.NormalResponse\x12_\n" +
	"\x14GetBookCopyrightDesc\x12\".books.GetBookCopyrightDescRequest\x1a#.books.GetBookCopyrightDescResponse\x12n\n" +
	"\x19BatchGetBookCopyrightDesc\x12'.books.BatchGetBookCopyrightDescRequest\x1a(.books.BatchGetBookCopyrightDescResponse\x12\\\n" +
	"\x13findCpSourceByBooks\x12!.books.FindCpSourceByBooksRequest\x1a\".books.FindCpSourceByBooksResponse\x128\n" +
	"\vAddCpSource\x12\x12.pbcommon.CpSource\x1a\x15.books.NormalResponse\x12V\n" +
	"\x11ListCopyrightDesc\x12\x1f.books.ListCopyrightDescRequest\x1a .books.ListCopyrightDescResponse\x12F\n" +
	"\x13CreateCopyrightDesc\x12!.books.CreateCopyrightDescRequest\x1a\f.books.Empty\x12F\n" +
	"\x13UpdateCopyrightDesc\x12!.books.UpdateCopyrightDescRequest\x1a\f.books.Empty\x12F\n" +
	"\x13DeleteCopyrightDesc\x12!.books.DeleteCopyrightDescRequest\x1a\f.books.Empty\x12J\n" +
	"\x10checkBookToShelf\x12\x1a.books.CheckBookToShelfReq\x1a\x1a.books.CheckBookToShelfResB.Z,creativematrix.com/beyondreading/proto/booksb\x06proto3"

var (
	file_proto_books_books_proto_rawDescOnce sync.Once
	file_proto_books_books_proto_rawDescData []byte
)

func file_proto_books_books_proto_rawDescGZIP() []byte {
	file_proto_books_books_proto_rawDescOnce.Do(func() {
		file_proto_books_books_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_books_books_proto_rawDesc), len(file_proto_books_books_proto_rawDesc)))
	})
	return file_proto_books_books_proto_rawDescData
}

var file_proto_books_books_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_proto_books_books_proto_goTypes = []any{
	(*UpdateBookReq)(nil),                     // 0: books.UpdateBookReq
	(*FindByIdReq)(nil),                       // 1: books.FindByIdReq
	(*BookIds)(nil),                           // 2: books.BookIds
	(*FindByIdsReq)(nil),                      // 3: books.FindByIdsReq
	(*BooklistResp)(nil),                      // 4: books.BooklistResp
	(*BookResp)(nil),                          // 5: books.BookResp
	(*FindBooksInfoResp)(nil),                 // 6: books.FindBooksInfoResp
	(*AddBookResp)(nil),                       // 7: books.AddBookResp
	(*NormalResponse)(nil),                    // 8: books.NormalResponse
	(*FindCpSourceByBooksRequest)(nil),        // 9: books.FindCpSourceByBooksRequest
	(*FindCpSourceByBooksResponse)(nil),       // 10: books.FindCpSourceByBooksResponse
	(*Empty)(nil),                             // 11: books.Empty
	(*CopyrightDesc)(nil),                     // 12: books.CopyrightDesc
	(*GetBookCopyrightDescRequest)(nil),       // 13: books.GetBookCopyrightDescRequest
	(*GetBookCopyrightDescResponse)(nil),      // 14: books.GetBookCopyrightDescResponse
	(*BatchGetBookCopyrightDescRequest)(nil),  // 15: books.BatchGetBookCopyrightDescRequest
	(*BatchGetBookCopyrightDescResponse)(nil), // 16: books.BatchGetBookCopyrightDescResponse
	(*ListCopyrightDescRequest)(nil),          // 17: books.ListCopyrightDescRequest
	(*ListCopyrightDescResponse)(nil),         // 18: books.ListCopyrightDescResponse
	(*CreateCopyrightDescRequest)(nil),        // 19: books.CreateCopyrightDescRequest
	(*UpdateCopyrightDescRequest)(nil),        // 20: books.UpdateCopyrightDescRequest
	(*DeleteCopyrightDescRequest)(nil),        // 21: books.DeleteCopyrightDescRequest
	(*CheckBookToShelfReq)(nil),               // 22: books.CheckBookToShelfReq
	(*CheckBookToShelfRes)(nil),               // 23: books.CheckBookToShelfRes
	nil,                                       // 24: books.FindBooksInfoResp.BooksEntry
	nil,                                       // 25: books.BatchGetBookCopyrightDescResponse.TextsEntry
	(*pbcommon.Book)(nil),                     // 26: pbcommon.Book
	(*pbcommon.CpSource)(nil),                 // 27: pbcommon.CpSource
	(*pbcommon.BookInfo)(nil),                 // 28: pbcommon.BookInfo
}
var file_proto_books_books_proto_depIdxs = []int32{
	26, // 0: books.UpdateBookReq.book:type_name -> pbcommon.Book
	26, // 1: books.BooklistResp.books:type_name -> pbcommon.Book
	26, // 2: books.BookResp.book:type_name -> pbcommon.Book
	24, // 3: books.FindBooksInfoResp.books:type_name -> books.FindBooksInfoResp.BooksEntry
	27, // 4: books.FindCpSourceByBooksResponse.cpSources:type_name -> pbcommon.CpSource
	25, // 5: books.BatchGetBookCopyrightDescResponse.texts:type_name -> books.BatchGetBookCopyrightDescResponse.TextsEntry
	12, // 6: books.ListCopyrightDescResponse.copyrightDescs:type_name -> books.CopyrightDesc
	12, // 7: books.CreateCopyrightDescRequest.copyrightDesc:type_name -> books.CopyrightDesc
	12, // 8: books.UpdateCopyrightDescRequest.copyrightDesc:type_name -> books.CopyrightDesc
	28, // 9: books.FindBooksInfoResp.BooksEntry.value:type_name -> pbcommon.BookInfo
	3,  // 10: books.books.findByIds:input_type -> books.FindByIdsReq
	3,  // 11: books.books.findBooksInfo:input_type -> books.FindByIdsReq
	26, // 12: books.books.addBook:input_type -> pbcommon.Book
	0,  // 13: books.books.updateBook:input_type -> books.UpdateBookReq
	13, // 14: books.books.GetBookCopyrightDesc:input_type -> books.GetBookCopyrightDescRequest
	15, // 15: books.books.BatchGetBookCopyrightDesc:input_type -> books.BatchGetBookCopyrightDescRequest
	9,  // 16: books.books.findCpSourceByBooks:input_type -> books.FindCpSourceByBooksRequest
	27, // 17: books.books.AddCpSource:input_type -> pbcommon.CpSource
	17, // 18: books.books.ListCopyrightDesc:input_type -> books.ListCopyrightDescRequest
	19, // 19: books.books.CreateCopyrightDesc:input_type -> books.CreateCopyrightDescRequest
	20, // 20: books.books.UpdateCopyrightDesc:input_type -> books.UpdateCopyrightDescRequest
	21, // 21: books.books.DeleteCopyrightDesc:input_type -> books.DeleteCopyrightDescRequest
	22, // 22: books.books.checkBookToShelf:input_type -> books.CheckBookToShelfReq
	4,  // 23: books.books.findByIds:output_type -> books.BooklistResp
	6,  // 24: books.books.findBooksInfo:output_type -> books.FindBooksInfoResp
	7,  // 25: books.books.addBook:output_type -> books.AddBookResp
	8,  // 26: books.books.updateBook:output_type -> books.NormalResponse
	14, // 27: books.books.GetBookCopyrightDesc:output_type -> books.GetBookCopyrightDescResponse
	16, // 28: books.books.BatchGetBookCopyrightDesc:output_type -> books.BatchGetBookCopyrightDescResponse
	10, // 29: books.books.findCpSourceByBooks:output_type -> books.FindCpSourceByBooksResponse
	8,  // 30: books.books.AddCpSource:output_type -> books.NormalResponse
	18, // 31: books.books.ListCopyrightDesc:output_type -> books.ListCopyrightDescResponse
	11, // 32: books.books.CreateCopyrightDesc:output_type -> books.Empty
	11, // 33: books.books.UpdateCopyrightDesc:output_type -> books.Empty
	11, // 34: books.books.DeleteCopyrightDesc:output_type -> books.Empty
	23, // 35: books.books.checkBookToShelf:output_type -> books.CheckBookToShelfRes
	23, // [23:36] is the sub-list for method output_type
	10, // [10:23] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_proto_books_books_proto_init() }
func file_proto_books_books_proto_init() {
	if File_proto_books_books_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_books_books_proto_rawDesc), len(file_proto_books_books_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_books_books_proto_goTypes,
		DependencyIndexes: file_proto_books_books_proto_depIdxs,
		MessageInfos:      file_proto_books_books_proto_msgTypes,
	}.Build()
	File_proto_books_books_proto = out.File
	file_proto_books_books_proto_goTypes = nil
	file_proto_books_books_proto_depIdxs = nil
}
