// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.30.2
// source: proto/books/books.proto

package books

import (
	context "context"
	pbcommon "creativematrix.com/beyondreading/proto/pbcommon"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Books_FindByIds_FullMethodName                 = "/books.books/findByIds"
	Books_FindBooksInfo_FullMethodName             = "/books.books/findBooksInfo"
	Books_AddBook_FullMethodName                   = "/books.books/addBook"
	Books_UpdateBook_FullMethodName                = "/books.books/updateBook"
	Books_GetBookCopyrightDesc_FullMethodName      = "/books.books/GetBookCopyrightDesc"
	Books_BatchGetBookCopyrightDesc_FullMethodName = "/books.books/BatchGetBookCopyrightDesc"
	Books_FindCpSourceByBooks_FullMethodName       = "/books.books/findCpSourceByBooks"
	Books_AddCpSource_FullMethodName               = "/books.books/AddCpSource"
	Books_ListCopyrightDesc_FullMethodName         = "/books.books/ListCopyrightDesc"
	Books_CreateCopyrightDesc_FullMethodName       = "/books.books/CreateCopyrightDesc"
	Books_UpdateCopyrightDesc_FullMethodName       = "/books.books/UpdateCopyrightDesc"
	Books_DeleteCopyrightDesc_FullMethodName       = "/books.books/DeleteCopyrightDesc"
	Books_CheckBookToShelf_FullMethodName          = "/books.books/checkBookToShelf"
)

// BooksClient is the client API for Books service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BooksClient interface {
	FindByIds(ctx context.Context, in *FindByIdsReq, opts ...grpc.CallOption) (*BooklistResp, error)
	FindBooksInfo(ctx context.Context, in *FindByIdsReq, opts ...grpc.CallOption) (*FindBooksInfoResp, error)
	AddBook(ctx context.Context, in *pbcommon.Book, opts ...grpc.CallOption) (*AddBookResp, error)
	UpdateBook(ctx context.Context, in *UpdateBookReq, opts ...grpc.CallOption) (*NormalResponse, error)
	GetBookCopyrightDesc(ctx context.Context, in *GetBookCopyrightDescRequest, opts ...grpc.CallOption) (*GetBookCopyrightDescResponse, error)
	BatchGetBookCopyrightDesc(ctx context.Context, in *BatchGetBookCopyrightDescRequest, opts ...grpc.CallOption) (*BatchGetBookCopyrightDescResponse, error)
	FindCpSourceByBooks(ctx context.Context, in *FindCpSourceByBooksRequest, opts ...grpc.CallOption) (*FindCpSourceByBooksResponse, error)
	AddCpSource(ctx context.Context, in *pbcommon.CpSource, opts ...grpc.CallOption) (*NormalResponse, error)
	ListCopyrightDesc(ctx context.Context, in *ListCopyrightDescRequest, opts ...grpc.CallOption) (*ListCopyrightDescResponse, error)
	CreateCopyrightDesc(ctx context.Context, in *CreateCopyrightDescRequest, opts ...grpc.CallOption) (*Empty, error)
	UpdateCopyrightDesc(ctx context.Context, in *UpdateCopyrightDescRequest, opts ...grpc.CallOption) (*Empty, error)
	DeleteCopyrightDesc(ctx context.Context, in *DeleteCopyrightDescRequest, opts ...grpc.CallOption) (*Empty, error)
	CheckBookToShelf(ctx context.Context, in *CheckBookToShelfReq, opts ...grpc.CallOption) (*CheckBookToShelfRes, error)
}

type booksClient struct {
	cc grpc.ClientConnInterface
}

func NewBooksClient(cc grpc.ClientConnInterface) BooksClient {
	return &booksClient{cc}
}

func (c *booksClient) FindByIds(ctx context.Context, in *FindByIdsReq, opts ...grpc.CallOption) (*BooklistResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BooklistResp)
	err := c.cc.Invoke(ctx, Books_FindByIds_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *booksClient) FindBooksInfo(ctx context.Context, in *FindByIdsReq, opts ...grpc.CallOption) (*FindBooksInfoResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FindBooksInfoResp)
	err := c.cc.Invoke(ctx, Books_FindBooksInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *booksClient) AddBook(ctx context.Context, in *pbcommon.Book, opts ...grpc.CallOption) (*AddBookResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddBookResp)
	err := c.cc.Invoke(ctx, Books_AddBook_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *booksClient) UpdateBook(ctx context.Context, in *UpdateBookReq, opts ...grpc.CallOption) (*NormalResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NormalResponse)
	err := c.cc.Invoke(ctx, Books_UpdateBook_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *booksClient) GetBookCopyrightDesc(ctx context.Context, in *GetBookCopyrightDescRequest, opts ...grpc.CallOption) (*GetBookCopyrightDescResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBookCopyrightDescResponse)
	err := c.cc.Invoke(ctx, Books_GetBookCopyrightDesc_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *booksClient) BatchGetBookCopyrightDesc(ctx context.Context, in *BatchGetBookCopyrightDescRequest, opts ...grpc.CallOption) (*BatchGetBookCopyrightDescResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchGetBookCopyrightDescResponse)
	err := c.cc.Invoke(ctx, Books_BatchGetBookCopyrightDesc_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *booksClient) FindCpSourceByBooks(ctx context.Context, in *FindCpSourceByBooksRequest, opts ...grpc.CallOption) (*FindCpSourceByBooksResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FindCpSourceByBooksResponse)
	err := c.cc.Invoke(ctx, Books_FindCpSourceByBooks_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *booksClient) AddCpSource(ctx context.Context, in *pbcommon.CpSource, opts ...grpc.CallOption) (*NormalResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NormalResponse)
	err := c.cc.Invoke(ctx, Books_AddCpSource_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *booksClient) ListCopyrightDesc(ctx context.Context, in *ListCopyrightDescRequest, opts ...grpc.CallOption) (*ListCopyrightDescResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListCopyrightDescResponse)
	err := c.cc.Invoke(ctx, Books_ListCopyrightDesc_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *booksClient) CreateCopyrightDesc(ctx context.Context, in *CreateCopyrightDescRequest, opts ...grpc.CallOption) (*Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Empty)
	err := c.cc.Invoke(ctx, Books_CreateCopyrightDesc_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *booksClient) UpdateCopyrightDesc(ctx context.Context, in *UpdateCopyrightDescRequest, opts ...grpc.CallOption) (*Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Empty)
	err := c.cc.Invoke(ctx, Books_UpdateCopyrightDesc_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *booksClient) DeleteCopyrightDesc(ctx context.Context, in *DeleteCopyrightDescRequest, opts ...grpc.CallOption) (*Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Empty)
	err := c.cc.Invoke(ctx, Books_DeleteCopyrightDesc_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *booksClient) CheckBookToShelf(ctx context.Context, in *CheckBookToShelfReq, opts ...grpc.CallOption) (*CheckBookToShelfRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckBookToShelfRes)
	err := c.cc.Invoke(ctx, Books_CheckBookToShelf_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BooksServer is the server API for Books service.
// All implementations should embed UnimplementedBooksServer
// for forward compatibility.
type BooksServer interface {
	FindByIds(context.Context, *FindByIdsReq) (*BooklistResp, error)
	FindBooksInfo(context.Context, *FindByIdsReq) (*FindBooksInfoResp, error)
	AddBook(context.Context, *pbcommon.Book) (*AddBookResp, error)
	UpdateBook(context.Context, *UpdateBookReq) (*NormalResponse, error)
	GetBookCopyrightDesc(context.Context, *GetBookCopyrightDescRequest) (*GetBookCopyrightDescResponse, error)
	BatchGetBookCopyrightDesc(context.Context, *BatchGetBookCopyrightDescRequest) (*BatchGetBookCopyrightDescResponse, error)
	FindCpSourceByBooks(context.Context, *FindCpSourceByBooksRequest) (*FindCpSourceByBooksResponse, error)
	AddCpSource(context.Context, *pbcommon.CpSource) (*NormalResponse, error)
	ListCopyrightDesc(context.Context, *ListCopyrightDescRequest) (*ListCopyrightDescResponse, error)
	CreateCopyrightDesc(context.Context, *CreateCopyrightDescRequest) (*Empty, error)
	UpdateCopyrightDesc(context.Context, *UpdateCopyrightDescRequest) (*Empty, error)
	DeleteCopyrightDesc(context.Context, *DeleteCopyrightDescRequest) (*Empty, error)
	CheckBookToShelf(context.Context, *CheckBookToShelfReq) (*CheckBookToShelfRes, error)
}

// UnimplementedBooksServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBooksServer struct{}

func (UnimplementedBooksServer) FindByIds(context.Context, *FindByIdsReq) (*BooklistResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindByIds not implemented")
}
func (UnimplementedBooksServer) FindBooksInfo(context.Context, *FindByIdsReq) (*FindBooksInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindBooksInfo not implemented")
}
func (UnimplementedBooksServer) AddBook(context.Context, *pbcommon.Book) (*AddBookResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddBook not implemented")
}
func (UnimplementedBooksServer) UpdateBook(context.Context, *UpdateBookReq) (*NormalResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBook not implemented")
}
func (UnimplementedBooksServer) GetBookCopyrightDesc(context.Context, *GetBookCopyrightDescRequest) (*GetBookCopyrightDescResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBookCopyrightDesc not implemented")
}
func (UnimplementedBooksServer) BatchGetBookCopyrightDesc(context.Context, *BatchGetBookCopyrightDescRequest) (*BatchGetBookCopyrightDescResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchGetBookCopyrightDesc not implemented")
}
func (UnimplementedBooksServer) FindCpSourceByBooks(context.Context, *FindCpSourceByBooksRequest) (*FindCpSourceByBooksResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FindCpSourceByBooks not implemented")
}
func (UnimplementedBooksServer) AddCpSource(context.Context, *pbcommon.CpSource) (*NormalResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddCpSource not implemented")
}
func (UnimplementedBooksServer) ListCopyrightDesc(context.Context, *ListCopyrightDescRequest) (*ListCopyrightDescResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCopyrightDesc not implemented")
}
func (UnimplementedBooksServer) CreateCopyrightDesc(context.Context, *CreateCopyrightDescRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCopyrightDesc not implemented")
}
func (UnimplementedBooksServer) UpdateCopyrightDesc(context.Context, *UpdateCopyrightDescRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCopyrightDesc not implemented")
}
func (UnimplementedBooksServer) DeleteCopyrightDesc(context.Context, *DeleteCopyrightDescRequest) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCopyrightDesc not implemented")
}
func (UnimplementedBooksServer) CheckBookToShelf(context.Context, *CheckBookToShelfReq) (*CheckBookToShelfRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckBookToShelf not implemented")
}
func (UnimplementedBooksServer) testEmbeddedByValue() {}

// UnsafeBooksServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BooksServer will
// result in compilation errors.
type UnsafeBooksServer interface {
	mustEmbedUnimplementedBooksServer()
}

func RegisterBooksServer(s grpc.ServiceRegistrar, srv BooksServer) {
	// If the following call pancis, it indicates UnimplementedBooksServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Books_ServiceDesc, srv)
}

func _Books_FindByIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindByIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BooksServer).FindByIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Books_FindByIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BooksServer).FindByIds(ctx, req.(*FindByIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Books_FindBooksInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindByIdsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BooksServer).FindBooksInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Books_FindBooksInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BooksServer).FindBooksInfo(ctx, req.(*FindByIdsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Books_AddBook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pbcommon.Book)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BooksServer).AddBook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Books_AddBook_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BooksServer).AddBook(ctx, req.(*pbcommon.Book))
	}
	return interceptor(ctx, in, info, handler)
}

func _Books_UpdateBook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBookReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BooksServer).UpdateBook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Books_UpdateBook_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BooksServer).UpdateBook(ctx, req.(*UpdateBookReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Books_GetBookCopyrightDesc_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBookCopyrightDescRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BooksServer).GetBookCopyrightDesc(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Books_GetBookCopyrightDesc_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BooksServer).GetBookCopyrightDesc(ctx, req.(*GetBookCopyrightDescRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Books_BatchGetBookCopyrightDesc_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchGetBookCopyrightDescRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BooksServer).BatchGetBookCopyrightDesc(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Books_BatchGetBookCopyrightDesc_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BooksServer).BatchGetBookCopyrightDesc(ctx, req.(*BatchGetBookCopyrightDescRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Books_FindCpSourceByBooks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FindCpSourceByBooksRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BooksServer).FindCpSourceByBooks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Books_FindCpSourceByBooks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BooksServer).FindCpSourceByBooks(ctx, req.(*FindCpSourceByBooksRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Books_AddCpSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pbcommon.CpSource)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BooksServer).AddCpSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Books_AddCpSource_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BooksServer).AddCpSource(ctx, req.(*pbcommon.CpSource))
	}
	return interceptor(ctx, in, info, handler)
}

func _Books_ListCopyrightDesc_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCopyrightDescRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BooksServer).ListCopyrightDesc(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Books_ListCopyrightDesc_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BooksServer).ListCopyrightDesc(ctx, req.(*ListCopyrightDescRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Books_CreateCopyrightDesc_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCopyrightDescRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BooksServer).CreateCopyrightDesc(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Books_CreateCopyrightDesc_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BooksServer).CreateCopyrightDesc(ctx, req.(*CreateCopyrightDescRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Books_UpdateCopyrightDesc_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCopyrightDescRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BooksServer).UpdateCopyrightDesc(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Books_UpdateCopyrightDesc_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BooksServer).UpdateCopyrightDesc(ctx, req.(*UpdateCopyrightDescRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Books_DeleteCopyrightDesc_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteCopyrightDescRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BooksServer).DeleteCopyrightDesc(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Books_DeleteCopyrightDesc_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BooksServer).DeleteCopyrightDesc(ctx, req.(*DeleteCopyrightDescRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Books_CheckBookToShelf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckBookToShelfReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BooksServer).CheckBookToShelf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Books_CheckBookToShelf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BooksServer).CheckBookToShelf(ctx, req.(*CheckBookToShelfReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Books_ServiceDesc is the grpc.ServiceDesc for Books service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Books_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "books.books",
	HandlerType: (*BooksServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "findByIds",
			Handler:    _Books_FindByIds_Handler,
		},
		{
			MethodName: "findBooksInfo",
			Handler:    _Books_FindBooksInfo_Handler,
		},
		{
			MethodName: "addBook",
			Handler:    _Books_AddBook_Handler,
		},
		{
			MethodName: "updateBook",
			Handler:    _Books_UpdateBook_Handler,
		},
		{
			MethodName: "GetBookCopyrightDesc",
			Handler:    _Books_GetBookCopyrightDesc_Handler,
		},
		{
			MethodName: "BatchGetBookCopyrightDesc",
			Handler:    _Books_BatchGetBookCopyrightDesc_Handler,
		},
		{
			MethodName: "findCpSourceByBooks",
			Handler:    _Books_FindCpSourceByBooks_Handler,
		},
		{
			MethodName: "AddCpSource",
			Handler:    _Books_AddCpSource_Handler,
		},
		{
			MethodName: "ListCopyrightDesc",
			Handler:    _Books_ListCopyrightDesc_Handler,
		},
		{
			MethodName: "CreateCopyrightDesc",
			Handler:    _Books_CreateCopyrightDesc_Handler,
		},
		{
			MethodName: "UpdateCopyrightDesc",
			Handler:    _Books_UpdateCopyrightDesc_Handler,
		},
		{
			MethodName: "DeleteCopyrightDesc",
			Handler:    _Books_DeleteCopyrightDesc_Handler,
		},
		{
			MethodName: "checkBookToShelf",
			Handler:    _Books_CheckBookToShelf_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/books/books.proto",
}
