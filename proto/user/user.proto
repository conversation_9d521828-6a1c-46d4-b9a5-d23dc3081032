syntax = "proto3";

package user;

option go_package = "creativematrix.com/beyondreading/gen/proto/user";

// 用户信息
message UserInfo {
  uint64 userId = 1;
  string phone = 2;                    // 手机号
  string email = 3;                    // 邮箱
  string nickname = 4;                 // 昵称
  string avatar = 5;                   // 头像URL
  int32 gender = 6;                    // 性别：0-未知，1-男，2-女
  string birthday = 7;                 // 生日
  string location = 8;                 // 地区
  int32 status = 9;                    // 状态：1-正常，2-冻结，3-注销
  int32 loginType = 10;                // 注册类型：1-手机，2-Google，3-Apple
  string googleId = 11;                // Google账户ID
  string appleId = 12;                 // Apple账户ID
  int64 lastLoginAt = 13;              // 最后登录时间
  string lastLoginIp = 14;             // 最后登录IP
  int64 createdAt = 15;                // 创建时间
  int64 updatedAt = 16;                // 更新时间
}

// 登录日志
message LoginLog {
  uint64 logId = 1;
  uint64 userId = 2;
  int32 loginType = 3;                 // 登录类型：1-手机短信，2-Google，3-Apple
  string loginIp = 4;                  // 登录IP
  string userAgent = 5;                // 用户代理
  string deviceId = 6;                 // 设备ID
  int32 loginResult = 7;               // 登录结果：1-成功，2-失败
  string failReason = 8;               // 失败原因
  int64 createdAt = 9;                 // 创建时间
}

// 手机短信注册请求
message RegisterBySmsReq {
  string phone = 1;                    // 手机号
  string smsCode = 2;                  // 短信验证码
  string nickname = 3;                 // 昵称（可选）
  string clientIp = 4;                 // 客户端IP
  string userAgent = 5;                // 用户代理
  string deviceId = 6;                 // 设备ID
}

message RegisterBySmsResp {
  int32 code = 1;
  string message = 2;
  UserInfo user = 3;
  string token = 4;                    // JWT token
}

// Google账户注册请求
message RegisterByGoogleReq {
  string googleToken = 1;              // Google OAuth token
  string googleId = 2;                 // Google账户ID
  string email = 3;                    // 邮箱
  string nickname = 4;                 // 昵称
  string avatar = 5;                   // 头像URL
  string clientIp = 6;                 // 客户端IP
  string userAgent = 7;                // 用户代理
  string deviceId = 8;                 // 设备ID
}

message RegisterByGoogleResp {
  int32 code = 1;
  string message = 2;
  UserInfo user = 3;
  string token = 4;                    // JWT token
}

// Apple账户注册请求
message RegisterByAppleReq {
  string appleToken = 1;               // Apple ID token
  string appleId = 2;                  // Apple账户ID
  string email = 3;                    // 邮箱（可选）
  string nickname = 4;                 // 昵称（可选）
  string clientIp = 5;                 // 客户端IP
  string userAgent = 6;                // 用户代理
  string deviceId = 7;                 // 设备ID
}

message RegisterByAppleResp {
  int32 code = 1;
  string message = 2;
  UserInfo user = 3;
  string token = 4;                    // JWT token
}

// 手机短信登录请求
message LoginBySmsReq {
  string phone = 1;                    // 手机号
  string smsCode = 2;                  // 短信验证码
  string clientIp = 3;                 // 客户端IP
  string userAgent = 4;                // 用户代理
  string deviceId = 5;                 // 设备ID
}

message LoginBySmsResp {
  int32 code = 1;
  string message = 2;
  UserInfo user = 3;
  string token = 4;                    // JWT token
}

// Google账户登录请求
message LoginByGoogleReq {
  string googleToken = 1;              // Google OAuth token
  string googleId = 2;                 // Google账户ID
  string clientIp = 3;                 // 客户端IP
  string userAgent = 4;                // 用户代理
  string deviceId = 5;                 // 设备ID
}

message LoginByGoogleResp {
  int32 code = 1;
  string message = 2;
  UserInfo user = 3;
  string token = 4;                    // JWT token
}

// Apple账户登录请求
message LoginByAppleReq {
  string appleToken = 1;               // Apple ID token
  string appleId = 2;                  // Apple账户ID
  string clientIp = 3;                 // 客户端IP
  string userAgent = 4;                // 用户代理
  string deviceId = 5;                 // 设备ID
}

message LoginByAppleResp {
  int32 code = 1;
  string message = 2;
  UserInfo user = 3;
  string token = 4;                    // JWT token
}

// 获取用户信息请求
message GetUserInfoReq {
  uint64 userId = 1;
}

message GetUserInfoResp {
  int32 code = 1;
  string message = 2;
  UserInfo user = 3;
}

// 更新用户信息请求
message UpdateUserInfoReq {
  uint64 userId = 1;
  string nickname = 2;                 // 昵称
  string avatar = 3;                   // 头像URL
  int32 gender = 4;                    // 性别
  string birthday = 5;                 // 生日
  string location = 6;                 // 地区
}

message UpdateUserInfoResp {
  int32 code = 1;
  string message = 2;
  UserInfo user = 3;
}

// 获取登录日志请求
message GetLoginLogsReq {
  uint64 userId = 1;
  int32 page = 2;
  int32 pageSize = 3;
}

message GetLoginLogsResp {
  int32 code = 1;
  string message = 2;
  repeated LoginLog logs = 3;
  int64 total = 4;
}

// 发送短信验证码请求
message SendSmsCodeReq {
  string phone = 1;                    // 手机号
  int32 codeType = 2;                  // 验证码类型：1-注册，2-登录
}

message SendSmsCodeResp {
  int32 code = 1;
  string message = 2;
}

// 验证短信验证码请求
message VerifySmsCodeReq {
  string phone = 1;                    // 手机号
  string smsCode = 2;                  // 短信验证码
  int32 codeType = 3;                  // 验证码类型：1-注册，2-登录
}

message VerifySmsCodeResp {
  int32 code = 1;
  string message = 2;
  bool isValid = 3;                    // 是否有效
}

// User服务定义
service UserService {
  // 注册相关
  rpc RegisterBySms(RegisterBySmsReq) returns (RegisterBySmsResp);
  rpc RegisterByGoogle(RegisterByGoogleReq) returns (RegisterByGoogleResp);
  rpc RegisterByApple(RegisterByAppleReq) returns (RegisterByAppleResp);
  
  // 登录相关
  rpc LoginBySms(LoginBySmsReq) returns (LoginBySmsResp);
  rpc LoginByGoogle(LoginByGoogleReq) returns (LoginByGoogleResp);
  rpc LoginByApple(LoginByAppleReq) returns (LoginByAppleResp);
  
  // 用户信息管理
  rpc GetUserInfo(GetUserInfoReq) returns (GetUserInfoResp);
  rpc UpdateUserInfo(UpdateUserInfoReq) returns (UpdateUserInfoResp);
  
  // 登录日志
  rpc GetLoginLogs(GetLoginLogsReq) returns (GetLoginLogsResp);
  
  // 短信验证码
  rpc SendSmsCode(SendSmsCodeReq) returns (SendSmsCodeResp);
  rpc VerifySmsCode(VerifySmsCodeReq) returns (VerifySmsCodeResp);
}
