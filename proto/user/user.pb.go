// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: proto/user/user.proto

package user

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 用户信息
type UserInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        uint64                 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
	Phone         string                 `protobuf:"bytes,2,opt,name=phone,proto3" json:"phone,omitempty"`               // 手机号
	Email         string                 `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`               // 邮箱
	Nickname      string                 `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`         // 昵称
	Avatar        string                 `protobuf:"bytes,5,opt,name=avatar,proto3" json:"avatar,omitempty"`             // 头像URL
	Gender        int32                  `protobuf:"varint,6,opt,name=gender,proto3" json:"gender,omitempty"`            // 性别：0-未知，1-男，2-女
	Birthday      string                 `protobuf:"bytes,7,opt,name=birthday,proto3" json:"birthday,omitempty"`         // 生日
	Location      string                 `protobuf:"bytes,8,opt,name=location,proto3" json:"location,omitempty"`         // 地区
	Status        int32                  `protobuf:"varint,9,opt,name=status,proto3" json:"status,omitempty"`            // 状态：1-正常，2-冻结，3-注销
	LoginType     int32                  `protobuf:"varint,10,opt,name=loginType,proto3" json:"loginType,omitempty"`     // 注册类型：1-手机，2-Google，3-Apple
	GoogleId      string                 `protobuf:"bytes,11,opt,name=googleId,proto3" json:"googleId,omitempty"`        // Google账户ID
	AppleId       string                 `protobuf:"bytes,12,opt,name=appleId,proto3" json:"appleId,omitempty"`          // Apple账户ID
	LastLoginAt   int64                  `protobuf:"varint,13,opt,name=lastLoginAt,proto3" json:"lastLoginAt,omitempty"` // 最后登录时间
	LastLoginIp   string                 `protobuf:"bytes,14,opt,name=lastLoginIp,proto3" json:"lastLoginIp,omitempty"`  // 最后登录IP
	CreatedAt     int64                  `protobuf:"varint,15,opt,name=createdAt,proto3" json:"createdAt,omitempty"`     // 创建时间
	UpdatedAt     int64                  `protobuf:"varint,16,opt,name=updatedAt,proto3" json:"updatedAt,omitempty"`     // 更新时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	mi := &file_proto_user_user_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_user_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_proto_user_user_proto_rawDescGZIP(), []int{0}
}

func (x *UserInfo) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UserInfo) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *UserInfo) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UserInfo) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *UserInfo) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *UserInfo) GetGender() int32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *UserInfo) GetBirthday() string {
	if x != nil {
		return x.Birthday
	}
	return ""
}

func (x *UserInfo) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *UserInfo) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *UserInfo) GetLoginType() int32 {
	if x != nil {
		return x.LoginType
	}
	return 0
}

func (x *UserInfo) GetGoogleId() string {
	if x != nil {
		return x.GoogleId
	}
	return ""
}

func (x *UserInfo) GetAppleId() string {
	if x != nil {
		return x.AppleId
	}
	return ""
}

func (x *UserInfo) GetLastLoginAt() int64 {
	if x != nil {
		return x.LastLoginAt
	}
	return 0
}

func (x *UserInfo) GetLastLoginIp() string {
	if x != nil {
		return x.LastLoginIp
	}
	return ""
}

func (x *UserInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *UserInfo) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

// 登录日志
type LoginLog struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LogId         uint64                 `protobuf:"varint,1,opt,name=logId,proto3" json:"logId,omitempty"`
	UserId        uint64                 `protobuf:"varint,2,opt,name=userId,proto3" json:"userId,omitempty"`
	LoginType     int32                  `protobuf:"varint,3,opt,name=loginType,proto3" json:"loginType,omitempty"`     // 登录类型：1-手机短信，2-Google，3-Apple
	LoginIp       string                 `protobuf:"bytes,4,opt,name=loginIp,proto3" json:"loginIp,omitempty"`          // 登录IP
	UserAgent     string                 `protobuf:"bytes,5,opt,name=userAgent,proto3" json:"userAgent,omitempty"`      // 用户代理
	DeviceId      string                 `protobuf:"bytes,6,opt,name=deviceId,proto3" json:"deviceId,omitempty"`        // 设备ID
	LoginResult   int32                  `protobuf:"varint,7,opt,name=loginResult,proto3" json:"loginResult,omitempty"` // 登录结果：1-成功，2-失败
	FailReason    string                 `protobuf:"bytes,8,opt,name=failReason,proto3" json:"failReason,omitempty"`    // 失败原因
	CreatedAt     int64                  `protobuf:"varint,9,opt,name=createdAt,proto3" json:"createdAt,omitempty"`     // 创建时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginLog) Reset() {
	*x = LoginLog{}
	mi := &file_proto_user_user_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginLog) ProtoMessage() {}

func (x *LoginLog) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_user_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginLog.ProtoReflect.Descriptor instead.
func (*LoginLog) Descriptor() ([]byte, []int) {
	return file_proto_user_user_proto_rawDescGZIP(), []int{1}
}

func (x *LoginLog) GetLogId() uint64 {
	if x != nil {
		return x.LogId
	}
	return 0
}

func (x *LoginLog) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *LoginLog) GetLoginType() int32 {
	if x != nil {
		return x.LoginType
	}
	return 0
}

func (x *LoginLog) GetLoginIp() string {
	if x != nil {
		return x.LoginIp
	}
	return ""
}

func (x *LoginLog) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

func (x *LoginLog) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *LoginLog) GetLoginResult() int32 {
	if x != nil {
		return x.LoginResult
	}
	return 0
}

func (x *LoginLog) GetFailReason() string {
	if x != nil {
		return x.FailReason
	}
	return ""
}

func (x *LoginLog) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

// 手机短信注册请求
type RegisterBySmsReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Phone         string                 `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`         // 手机号
	SmsCode       string                 `protobuf:"bytes,2,opt,name=smsCode,proto3" json:"smsCode,omitempty"`     // 短信验证码
	Nickname      string                 `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname,omitempty"`   // 昵称（可选）
	ClientIp      string                 `protobuf:"bytes,4,opt,name=clientIp,proto3" json:"clientIp,omitempty"`   // 客户端IP
	UserAgent     string                 `protobuf:"bytes,5,opt,name=userAgent,proto3" json:"userAgent,omitempty"` // 用户代理
	DeviceId      string                 `protobuf:"bytes,6,opt,name=deviceId,proto3" json:"deviceId,omitempty"`   // 设备ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterBySmsReq) Reset() {
	*x = RegisterBySmsReq{}
	mi := &file_proto_user_user_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterBySmsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterBySmsReq) ProtoMessage() {}

func (x *RegisterBySmsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_user_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterBySmsReq.ProtoReflect.Descriptor instead.
func (*RegisterBySmsReq) Descriptor() ([]byte, []int) {
	return file_proto_user_user_proto_rawDescGZIP(), []int{2}
}

func (x *RegisterBySmsReq) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *RegisterBySmsReq) GetSmsCode() string {
	if x != nil {
		return x.SmsCode
	}
	return ""
}

func (x *RegisterBySmsReq) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *RegisterBySmsReq) GetClientIp() string {
	if x != nil {
		return x.ClientIp
	}
	return ""
}

func (x *RegisterBySmsReq) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

func (x *RegisterBySmsReq) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type RegisterBySmsResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	User          *UserInfo              `protobuf:"bytes,3,opt,name=user,proto3" json:"user,omitempty"`
	Token         string                 `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"` // JWT token
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterBySmsResp) Reset() {
	*x = RegisterBySmsResp{}
	mi := &file_proto_user_user_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterBySmsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterBySmsResp) ProtoMessage() {}

func (x *RegisterBySmsResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_user_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterBySmsResp.ProtoReflect.Descriptor instead.
func (*RegisterBySmsResp) Descriptor() ([]byte, []int) {
	return file_proto_user_user_proto_rawDescGZIP(), []int{3}
}

func (x *RegisterBySmsResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RegisterBySmsResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *RegisterBySmsResp) GetUser() *UserInfo {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *RegisterBySmsResp) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// Google账户注册请求
type RegisterByGoogleReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	GoogleToken   string                 `protobuf:"bytes,1,opt,name=googleToken,proto3" json:"googleToken,omitempty"` // Google OAuth token
	GoogleId      string                 `protobuf:"bytes,2,opt,name=googleId,proto3" json:"googleId,omitempty"`       // Google账户ID
	Email         string                 `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`             // 邮箱
	Nickname      string                 `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`       // 昵称
	Avatar        string                 `protobuf:"bytes,5,opt,name=avatar,proto3" json:"avatar,omitempty"`           // 头像URL
	ClientIp      string                 `protobuf:"bytes,6,opt,name=clientIp,proto3" json:"clientIp,omitempty"`       // 客户端IP
	UserAgent     string                 `protobuf:"bytes,7,opt,name=userAgent,proto3" json:"userAgent,omitempty"`     // 用户代理
	DeviceId      string                 `protobuf:"bytes,8,opt,name=deviceId,proto3" json:"deviceId,omitempty"`       // 设备ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterByGoogleReq) Reset() {
	*x = RegisterByGoogleReq{}
	mi := &file_proto_user_user_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterByGoogleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterByGoogleReq) ProtoMessage() {}

func (x *RegisterByGoogleReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_user_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterByGoogleReq.ProtoReflect.Descriptor instead.
func (*RegisterByGoogleReq) Descriptor() ([]byte, []int) {
	return file_proto_user_user_proto_rawDescGZIP(), []int{4}
}

func (x *RegisterByGoogleReq) GetGoogleToken() string {
	if x != nil {
		return x.GoogleToken
	}
	return ""
}

func (x *RegisterByGoogleReq) GetGoogleId() string {
	if x != nil {
		return x.GoogleId
	}
	return ""
}

func (x *RegisterByGoogleReq) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *RegisterByGoogleReq) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *RegisterByGoogleReq) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *RegisterByGoogleReq) GetClientIp() string {
	if x != nil {
		return x.ClientIp
	}
	return ""
}

func (x *RegisterByGoogleReq) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

func (x *RegisterByGoogleReq) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type RegisterByGoogleResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	User          *UserInfo              `protobuf:"bytes,3,opt,name=user,proto3" json:"user,omitempty"`
	Token         string                 `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"` // JWT token
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterByGoogleResp) Reset() {
	*x = RegisterByGoogleResp{}
	mi := &file_proto_user_user_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterByGoogleResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterByGoogleResp) ProtoMessage() {}

func (x *RegisterByGoogleResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_user_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterByGoogleResp.ProtoReflect.Descriptor instead.
func (*RegisterByGoogleResp) Descriptor() ([]byte, []int) {
	return file_proto_user_user_proto_rawDescGZIP(), []int{5}
}

func (x *RegisterByGoogleResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RegisterByGoogleResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *RegisterByGoogleResp) GetUser() *UserInfo {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *RegisterByGoogleResp) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// Apple账户注册请求
type RegisterByAppleReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AppleToken    string                 `protobuf:"bytes,1,opt,name=appleToken,proto3" json:"appleToken,omitempty"` // Apple ID token
	AppleId       string                 `protobuf:"bytes,2,opt,name=appleId,proto3" json:"appleId,omitempty"`       // Apple账户ID
	Email         string                 `protobuf:"bytes,3,opt,name=email,proto3" json:"email,omitempty"`           // 邮箱（可选）
	Nickname      string                 `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`     // 昵称（可选）
	ClientIp      string                 `protobuf:"bytes,5,opt,name=clientIp,proto3" json:"clientIp,omitempty"`     // 客户端IP
	UserAgent     string                 `protobuf:"bytes,6,opt,name=userAgent,proto3" json:"userAgent,omitempty"`   // 用户代理
	DeviceId      string                 `protobuf:"bytes,7,opt,name=deviceId,proto3" json:"deviceId,omitempty"`     // 设备ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterByAppleReq) Reset() {
	*x = RegisterByAppleReq{}
	mi := &file_proto_user_user_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterByAppleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterByAppleReq) ProtoMessage() {}

func (x *RegisterByAppleReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_user_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterByAppleReq.ProtoReflect.Descriptor instead.
func (*RegisterByAppleReq) Descriptor() ([]byte, []int) {
	return file_proto_user_user_proto_rawDescGZIP(), []int{6}
}

func (x *RegisterByAppleReq) GetAppleToken() string {
	if x != nil {
		return x.AppleToken
	}
	return ""
}

func (x *RegisterByAppleReq) GetAppleId() string {
	if x != nil {
		return x.AppleId
	}
	return ""
}

func (x *RegisterByAppleReq) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *RegisterByAppleReq) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *RegisterByAppleReq) GetClientIp() string {
	if x != nil {
		return x.ClientIp
	}
	return ""
}

func (x *RegisterByAppleReq) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

func (x *RegisterByAppleReq) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type RegisterByAppleResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	User          *UserInfo              `protobuf:"bytes,3,opt,name=user,proto3" json:"user,omitempty"`
	Token         string                 `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"` // JWT token
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterByAppleResp) Reset() {
	*x = RegisterByAppleResp{}
	mi := &file_proto_user_user_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterByAppleResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterByAppleResp) ProtoMessage() {}

func (x *RegisterByAppleResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_user_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterByAppleResp.ProtoReflect.Descriptor instead.
func (*RegisterByAppleResp) Descriptor() ([]byte, []int) {
	return file_proto_user_user_proto_rawDescGZIP(), []int{7}
}

func (x *RegisterByAppleResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RegisterByAppleResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *RegisterByAppleResp) GetUser() *UserInfo {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *RegisterByAppleResp) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// 手机短信登录请求
type LoginBySmsReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Phone         string                 `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`         // 手机号
	SmsCode       string                 `protobuf:"bytes,2,opt,name=smsCode,proto3" json:"smsCode,omitempty"`     // 短信验证码
	ClientIp      string                 `protobuf:"bytes,3,opt,name=clientIp,proto3" json:"clientIp,omitempty"`   // 客户端IP
	UserAgent     string                 `protobuf:"bytes,4,opt,name=userAgent,proto3" json:"userAgent,omitempty"` // 用户代理
	DeviceId      string                 `protobuf:"bytes,5,opt,name=deviceId,proto3" json:"deviceId,omitempty"`   // 设备ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginBySmsReq) Reset() {
	*x = LoginBySmsReq{}
	mi := &file_proto_user_user_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginBySmsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginBySmsReq) ProtoMessage() {}

func (x *LoginBySmsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_user_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginBySmsReq.ProtoReflect.Descriptor instead.
func (*LoginBySmsReq) Descriptor() ([]byte, []int) {
	return file_proto_user_user_proto_rawDescGZIP(), []int{8}
}

func (x *LoginBySmsReq) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *LoginBySmsReq) GetSmsCode() string {
	if x != nil {
		return x.SmsCode
	}
	return ""
}

func (x *LoginBySmsReq) GetClientIp() string {
	if x != nil {
		return x.ClientIp
	}
	return ""
}

func (x *LoginBySmsReq) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

func (x *LoginBySmsReq) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type LoginBySmsResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	User          *UserInfo              `protobuf:"bytes,3,opt,name=user,proto3" json:"user,omitempty"`
	Token         string                 `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"` // JWT token
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginBySmsResp) Reset() {
	*x = LoginBySmsResp{}
	mi := &file_proto_user_user_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginBySmsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginBySmsResp) ProtoMessage() {}

func (x *LoginBySmsResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_user_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginBySmsResp.ProtoReflect.Descriptor instead.
func (*LoginBySmsResp) Descriptor() ([]byte, []int) {
	return file_proto_user_user_proto_rawDescGZIP(), []int{9}
}

func (x *LoginBySmsResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *LoginBySmsResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *LoginBySmsResp) GetUser() *UserInfo {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *LoginBySmsResp) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// Google账户登录请求
type LoginByGoogleReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	GoogleToken   string                 `protobuf:"bytes,1,opt,name=googleToken,proto3" json:"googleToken,omitempty"` // Google OAuth token
	GoogleId      string                 `protobuf:"bytes,2,opt,name=googleId,proto3" json:"googleId,omitempty"`       // Google账户ID
	ClientIp      string                 `protobuf:"bytes,3,opt,name=clientIp,proto3" json:"clientIp,omitempty"`       // 客户端IP
	UserAgent     string                 `protobuf:"bytes,4,opt,name=userAgent,proto3" json:"userAgent,omitempty"`     // 用户代理
	DeviceId      string                 `protobuf:"bytes,5,opt,name=deviceId,proto3" json:"deviceId,omitempty"`       // 设备ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginByGoogleReq) Reset() {
	*x = LoginByGoogleReq{}
	mi := &file_proto_user_user_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginByGoogleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginByGoogleReq) ProtoMessage() {}

func (x *LoginByGoogleReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_user_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginByGoogleReq.ProtoReflect.Descriptor instead.
func (*LoginByGoogleReq) Descriptor() ([]byte, []int) {
	return file_proto_user_user_proto_rawDescGZIP(), []int{10}
}

func (x *LoginByGoogleReq) GetGoogleToken() string {
	if x != nil {
		return x.GoogleToken
	}
	return ""
}

func (x *LoginByGoogleReq) GetGoogleId() string {
	if x != nil {
		return x.GoogleId
	}
	return ""
}

func (x *LoginByGoogleReq) GetClientIp() string {
	if x != nil {
		return x.ClientIp
	}
	return ""
}

func (x *LoginByGoogleReq) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

func (x *LoginByGoogleReq) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type LoginByGoogleResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	User          *UserInfo              `protobuf:"bytes,3,opt,name=user,proto3" json:"user,omitempty"`
	Token         string                 `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"` // JWT token
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginByGoogleResp) Reset() {
	*x = LoginByGoogleResp{}
	mi := &file_proto_user_user_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginByGoogleResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginByGoogleResp) ProtoMessage() {}

func (x *LoginByGoogleResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_user_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginByGoogleResp.ProtoReflect.Descriptor instead.
func (*LoginByGoogleResp) Descriptor() ([]byte, []int) {
	return file_proto_user_user_proto_rawDescGZIP(), []int{11}
}

func (x *LoginByGoogleResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *LoginByGoogleResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *LoginByGoogleResp) GetUser() *UserInfo {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *LoginByGoogleResp) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// Apple账户登录请求
type LoginByAppleReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AppleToken    string                 `protobuf:"bytes,1,opt,name=appleToken,proto3" json:"appleToken,omitempty"` // Apple ID token
	AppleId       string                 `protobuf:"bytes,2,opt,name=appleId,proto3" json:"appleId,omitempty"`       // Apple账户ID
	ClientIp      string                 `protobuf:"bytes,3,opt,name=clientIp,proto3" json:"clientIp,omitempty"`     // 客户端IP
	UserAgent     string                 `protobuf:"bytes,4,opt,name=userAgent,proto3" json:"userAgent,omitempty"`   // 用户代理
	DeviceId      string                 `protobuf:"bytes,5,opt,name=deviceId,proto3" json:"deviceId,omitempty"`     // 设备ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginByAppleReq) Reset() {
	*x = LoginByAppleReq{}
	mi := &file_proto_user_user_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginByAppleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginByAppleReq) ProtoMessage() {}

func (x *LoginByAppleReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_user_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginByAppleReq.ProtoReflect.Descriptor instead.
func (*LoginByAppleReq) Descriptor() ([]byte, []int) {
	return file_proto_user_user_proto_rawDescGZIP(), []int{12}
}

func (x *LoginByAppleReq) GetAppleToken() string {
	if x != nil {
		return x.AppleToken
	}
	return ""
}

func (x *LoginByAppleReq) GetAppleId() string {
	if x != nil {
		return x.AppleId
	}
	return ""
}

func (x *LoginByAppleReq) GetClientIp() string {
	if x != nil {
		return x.ClientIp
	}
	return ""
}

func (x *LoginByAppleReq) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

func (x *LoginByAppleReq) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type LoginByAppleResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	User          *UserInfo              `protobuf:"bytes,3,opt,name=user,proto3" json:"user,omitempty"`
	Token         string                 `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"` // JWT token
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginByAppleResp) Reset() {
	*x = LoginByAppleResp{}
	mi := &file_proto_user_user_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginByAppleResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginByAppleResp) ProtoMessage() {}

func (x *LoginByAppleResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_user_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginByAppleResp.ProtoReflect.Descriptor instead.
func (*LoginByAppleResp) Descriptor() ([]byte, []int) {
	return file_proto_user_user_proto_rawDescGZIP(), []int{13}
}

func (x *LoginByAppleResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *LoginByAppleResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *LoginByAppleResp) GetUser() *UserInfo {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *LoginByAppleResp) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

// 获取用户信息请求
type GetUserInfoReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        uint64                 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserInfoReq) Reset() {
	*x = GetUserInfoReq{}
	mi := &file_proto_user_user_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInfoReq) ProtoMessage() {}

func (x *GetUserInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_user_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInfoReq.ProtoReflect.Descriptor instead.
func (*GetUserInfoReq) Descriptor() ([]byte, []int) {
	return file_proto_user_user_proto_rawDescGZIP(), []int{14}
}

func (x *GetUserInfoReq) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type GetUserInfoResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	User          *UserInfo              `protobuf:"bytes,3,opt,name=user,proto3" json:"user,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserInfoResp) Reset() {
	*x = GetUserInfoResp{}
	mi := &file_proto_user_user_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInfoResp) ProtoMessage() {}

func (x *GetUserInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_user_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInfoResp.ProtoReflect.Descriptor instead.
func (*GetUserInfoResp) Descriptor() ([]byte, []int) {
	return file_proto_user_user_proto_rawDescGZIP(), []int{15}
}

func (x *GetUserInfoResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetUserInfoResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetUserInfoResp) GetUser() *UserInfo {
	if x != nil {
		return x.User
	}
	return nil
}

// 更新用户信息请求
type UpdateUserInfoReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        uint64                 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
	Nickname      string                 `protobuf:"bytes,2,opt,name=nickname,proto3" json:"nickname,omitempty"` // 昵称
	Avatar        string                 `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`     // 头像URL
	Gender        int32                  `protobuf:"varint,4,opt,name=gender,proto3" json:"gender,omitempty"`    // 性别
	Birthday      string                 `protobuf:"bytes,5,opt,name=birthday,proto3" json:"birthday,omitempty"` // 生日
	Location      string                 `protobuf:"bytes,6,opt,name=location,proto3" json:"location,omitempty"` // 地区
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserInfoReq) Reset() {
	*x = UpdateUserInfoReq{}
	mi := &file_proto_user_user_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserInfoReq) ProtoMessage() {}

func (x *UpdateUserInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_user_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserInfoReq.ProtoReflect.Descriptor instead.
func (*UpdateUserInfoReq) Descriptor() ([]byte, []int) {
	return file_proto_user_user_proto_rawDescGZIP(), []int{16}
}

func (x *UpdateUserInfoReq) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *UpdateUserInfoReq) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *UpdateUserInfoReq) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *UpdateUserInfoReq) GetGender() int32 {
	if x != nil {
		return x.Gender
	}
	return 0
}

func (x *UpdateUserInfoReq) GetBirthday() string {
	if x != nil {
		return x.Birthday
	}
	return ""
}

func (x *UpdateUserInfoReq) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

type UpdateUserInfoResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	User          *UserInfo              `protobuf:"bytes,3,opt,name=user,proto3" json:"user,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserInfoResp) Reset() {
	*x = UpdateUserInfoResp{}
	mi := &file_proto_user_user_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserInfoResp) ProtoMessage() {}

func (x *UpdateUserInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_user_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserInfoResp.ProtoReflect.Descriptor instead.
func (*UpdateUserInfoResp) Descriptor() ([]byte, []int) {
	return file_proto_user_user_proto_rawDescGZIP(), []int{17}
}

func (x *UpdateUserInfoResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UpdateUserInfoResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *UpdateUserInfoResp) GetUser() *UserInfo {
	if x != nil {
		return x.User
	}
	return nil
}

// 获取登录日志请求
type GetLoginLogsReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        uint64                 `protobuf:"varint,1,opt,name=userId,proto3" json:"userId,omitempty"`
	Page          int32                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLoginLogsReq) Reset() {
	*x = GetLoginLogsReq{}
	mi := &file_proto_user_user_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLoginLogsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoginLogsReq) ProtoMessage() {}

func (x *GetLoginLogsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_user_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoginLogsReq.ProtoReflect.Descriptor instead.
func (*GetLoginLogsReq) Descriptor() ([]byte, []int) {
	return file_proto_user_user_proto_rawDescGZIP(), []int{18}
}

func (x *GetLoginLogsReq) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetLoginLogsReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetLoginLogsReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type GetLoginLogsResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Logs          []*LoginLog            `protobuf:"bytes,3,rep,name=logs,proto3" json:"logs,omitempty"`
	Total         int64                  `protobuf:"varint,4,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLoginLogsResp) Reset() {
	*x = GetLoginLogsResp{}
	mi := &file_proto_user_user_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLoginLogsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoginLogsResp) ProtoMessage() {}

func (x *GetLoginLogsResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_user_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoginLogsResp.ProtoReflect.Descriptor instead.
func (*GetLoginLogsResp) Descriptor() ([]byte, []int) {
	return file_proto_user_user_proto_rawDescGZIP(), []int{19}
}

func (x *GetLoginLogsResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetLoginLogsResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetLoginLogsResp) GetLogs() []*LoginLog {
	if x != nil {
		return x.Logs
	}
	return nil
}

func (x *GetLoginLogsResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 发送短信验证码请求
type SendSmsCodeReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Phone         string                 `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`        // 手机号
	CodeType      int32                  `protobuf:"varint,2,opt,name=codeType,proto3" json:"codeType,omitempty"` // 验证码类型：1-注册，2-登录
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendSmsCodeReq) Reset() {
	*x = SendSmsCodeReq{}
	mi := &file_proto_user_user_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendSmsCodeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendSmsCodeReq) ProtoMessage() {}

func (x *SendSmsCodeReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_user_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendSmsCodeReq.ProtoReflect.Descriptor instead.
func (*SendSmsCodeReq) Descriptor() ([]byte, []int) {
	return file_proto_user_user_proto_rawDescGZIP(), []int{20}
}

func (x *SendSmsCodeReq) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *SendSmsCodeReq) GetCodeType() int32 {
	if x != nil {
		return x.CodeType
	}
	return 0
}

type SendSmsCodeResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendSmsCodeResp) Reset() {
	*x = SendSmsCodeResp{}
	mi := &file_proto_user_user_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendSmsCodeResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendSmsCodeResp) ProtoMessage() {}

func (x *SendSmsCodeResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_user_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendSmsCodeResp.ProtoReflect.Descriptor instead.
func (*SendSmsCodeResp) Descriptor() ([]byte, []int) {
	return file_proto_user_user_proto_rawDescGZIP(), []int{21}
}

func (x *SendSmsCodeResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SendSmsCodeResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 验证短信验证码请求
type VerifySmsCodeReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Phone         string                 `protobuf:"bytes,1,opt,name=phone,proto3" json:"phone,omitempty"`        // 手机号
	SmsCode       string                 `protobuf:"bytes,2,opt,name=smsCode,proto3" json:"smsCode,omitempty"`    // 短信验证码
	CodeType      int32                  `protobuf:"varint,3,opt,name=codeType,proto3" json:"codeType,omitempty"` // 验证码类型：1-注册，2-登录
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VerifySmsCodeReq) Reset() {
	*x = VerifySmsCodeReq{}
	mi := &file_proto_user_user_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifySmsCodeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifySmsCodeReq) ProtoMessage() {}

func (x *VerifySmsCodeReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_user_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifySmsCodeReq.ProtoReflect.Descriptor instead.
func (*VerifySmsCodeReq) Descriptor() ([]byte, []int) {
	return file_proto_user_user_proto_rawDescGZIP(), []int{22}
}

func (x *VerifySmsCodeReq) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *VerifySmsCodeReq) GetSmsCode() string {
	if x != nil {
		return x.SmsCode
	}
	return ""
}

func (x *VerifySmsCodeReq) GetCodeType() int32 {
	if x != nil {
		return x.CodeType
	}
	return 0
}

type VerifySmsCodeResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	IsValid       bool                   `protobuf:"varint,3,opt,name=isValid,proto3" json:"isValid,omitempty"` // 是否有效
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VerifySmsCodeResp) Reset() {
	*x = VerifySmsCodeResp{}
	mi := &file_proto_user_user_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VerifySmsCodeResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifySmsCodeResp) ProtoMessage() {}

func (x *VerifySmsCodeResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_user_user_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifySmsCodeResp.ProtoReflect.Descriptor instead.
func (*VerifySmsCodeResp) Descriptor() ([]byte, []int) {
	return file_proto_user_user_proto_rawDescGZIP(), []int{23}
}

func (x *VerifySmsCodeResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *VerifySmsCodeResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *VerifySmsCodeResp) GetIsValid() bool {
	if x != nil {
		return x.IsValid
	}
	return false
}

var File_proto_user_user_proto protoreflect.FileDescriptor

const file_proto_user_user_proto_rawDesc = "" +
	"\n" +
	"\x15proto/user/user.proto\x12\x04user\"\xbe\x03\n" +
	"\bUserInfo\x12\x16\n" +
	"\x06userId\x18\x01 \x01(\x04R\x06userId\x12\x14\n" +
	"\x05phone\x18\x02 \x01(\tR\x05phone\x12\x14\n" +
	"\x05email\x18\x03 \x01(\tR\x05email\x12\x1a\n" +
	"\bnickname\x18\x04 \x01(\tR\bnickname\x12\x16\n" +
	"\x06avatar\x18\x05 \x01(\tR\x06avatar\x12\x16\n" +
	"\x06gender\x18\x06 \x01(\x05R\x06gender\x12\x1a\n" +
	"\bbirthday\x18\a \x01(\tR\bbirthday\x12\x1a\n" +
	"\blocation\x18\b \x01(\tR\blocation\x12\x16\n" +
	"\x06status\x18\t \x01(\x05R\x06status\x12\x1c\n" +
	"\tloginType\x18\n" +
	" \x01(\x05R\tloginType\x12\x1a\n" +
	"\bgoogleId\x18\v \x01(\tR\bgoogleId\x12\x18\n" +
	"\aappleId\x18\f \x01(\tR\aappleId\x12 \n" +
	"\vlastLoginAt\x18\r \x01(\x03R\vlastLoginAt\x12 \n" +
	"\vlastLoginIp\x18\x0e \x01(\tR\vlastLoginIp\x12\x1c\n" +
	"\tcreatedAt\x18\x0f \x01(\x03R\tcreatedAt\x12\x1c\n" +
	"\tupdatedAt\x18\x10 \x01(\x03R\tupdatedAt\"\x8a\x02\n" +
	"\bLoginLog\x12\x14\n" +
	"\x05logId\x18\x01 \x01(\x04R\x05logId\x12\x16\n" +
	"\x06userId\x18\x02 \x01(\x04R\x06userId\x12\x1c\n" +
	"\tloginType\x18\x03 \x01(\x05R\tloginType\x12\x18\n" +
	"\aloginIp\x18\x04 \x01(\tR\aloginIp\x12\x1c\n" +
	"\tuserAgent\x18\x05 \x01(\tR\tuserAgent\x12\x1a\n" +
	"\bdeviceId\x18\x06 \x01(\tR\bdeviceId\x12 \n" +
	"\vloginResult\x18\a \x01(\x05R\vloginResult\x12\x1e\n" +
	"\n" +
	"failReason\x18\b \x01(\tR\n" +
	"failReason\x12\x1c\n" +
	"\tcreatedAt\x18\t \x01(\x03R\tcreatedAt\"\xb4\x01\n" +
	"\x10RegisterBySmsReq\x12\x14\n" +
	"\x05phone\x18\x01 \x01(\tR\x05phone\x12\x18\n" +
	"\asmsCode\x18\x02 \x01(\tR\asmsCode\x12\x1a\n" +
	"\bnickname\x18\x03 \x01(\tR\bnickname\x12\x1a\n" +
	"\bclientIp\x18\x04 \x01(\tR\bclientIp\x12\x1c\n" +
	"\tuserAgent\x18\x05 \x01(\tR\tuserAgent\x12\x1a\n" +
	"\bdeviceId\x18\x06 \x01(\tR\bdeviceId\"{\n" +
	"\x11RegisterBySmsResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\"\n" +
	"\x04user\x18\x03 \x01(\v2\x0e.user.UserInfoR\x04user\x12\x14\n" +
	"\x05token\x18\x04 \x01(\tR\x05token\"\xf3\x01\n" +
	"\x13RegisterByGoogleReq\x12 \n" +
	"\vgoogleToken\x18\x01 \x01(\tR\vgoogleToken\x12\x1a\n" +
	"\bgoogleId\x18\x02 \x01(\tR\bgoogleId\x12\x14\n" +
	"\x05email\x18\x03 \x01(\tR\x05email\x12\x1a\n" +
	"\bnickname\x18\x04 \x01(\tR\bnickname\x12\x16\n" +
	"\x06avatar\x18\x05 \x01(\tR\x06avatar\x12\x1a\n" +
	"\bclientIp\x18\x06 \x01(\tR\bclientIp\x12\x1c\n" +
	"\tuserAgent\x18\a \x01(\tR\tuserAgent\x12\x1a\n" +
	"\bdeviceId\x18\b \x01(\tR\bdeviceId\"~\n" +
	"\x14RegisterByGoogleResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\"\n" +
	"\x04user\x18\x03 \x01(\v2\x0e.user.UserInfoR\x04user\x12\x14\n" +
	"\x05token\x18\x04 \x01(\tR\x05token\"\xd6\x01\n" +
	"\x12RegisterByAppleReq\x12\x1e\n" +
	"\n" +
	"appleToken\x18\x01 \x01(\tR\n" +
	"appleToken\x12\x18\n" +
	"\aappleId\x18\x02 \x01(\tR\aappleId\x12\x14\n" +
	"\x05email\x18\x03 \x01(\tR\x05email\x12\x1a\n" +
	"\bnickname\x18\x04 \x01(\tR\bnickname\x12\x1a\n" +
	"\bclientIp\x18\x05 \x01(\tR\bclientIp\x12\x1c\n" +
	"\tuserAgent\x18\x06 \x01(\tR\tuserAgent\x12\x1a\n" +
	"\bdeviceId\x18\a \x01(\tR\bdeviceId\"}\n" +
	"\x13RegisterByAppleResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\"\n" +
	"\x04user\x18\x03 \x01(\v2\x0e.user.UserInfoR\x04user\x12\x14\n" +
	"\x05token\x18\x04 \x01(\tR\x05token\"\x95\x01\n" +
	"\rLoginBySmsReq\x12\x14\n" +
	"\x05phone\x18\x01 \x01(\tR\x05phone\x12\x18\n" +
	"\asmsCode\x18\x02 \x01(\tR\asmsCode\x12\x1a\n" +
	"\bclientIp\x18\x03 \x01(\tR\bclientIp\x12\x1c\n" +
	"\tuserAgent\x18\x04 \x01(\tR\tuserAgent\x12\x1a\n" +
	"\bdeviceId\x18\x05 \x01(\tR\bdeviceId\"x\n" +
	"\x0eLoginBySmsResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\"\n" +
	"\x04user\x18\x03 \x01(\v2\x0e.user.UserInfoR\x04user\x12\x14\n" +
	"\x05token\x18\x04 \x01(\tR\x05token\"\xa6\x01\n" +
	"\x10LoginByGoogleReq\x12 \n" +
	"\vgoogleToken\x18\x01 \x01(\tR\vgoogleToken\x12\x1a\n" +
	"\bgoogleId\x18\x02 \x01(\tR\bgoogleId\x12\x1a\n" +
	"\bclientIp\x18\x03 \x01(\tR\bclientIp\x12\x1c\n" +
	"\tuserAgent\x18\x04 \x01(\tR\tuserAgent\x12\x1a\n" +
	"\bdeviceId\x18\x05 \x01(\tR\bdeviceId\"{\n" +
	"\x11LoginByGoogleResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\"\n" +
	"\x04user\x18\x03 \x01(\v2\x0e.user.UserInfoR\x04user\x12\x14\n" +
	"\x05token\x18\x04 \x01(\tR\x05token\"\xa1\x01\n" +
	"\x0fLoginByAppleReq\x12\x1e\n" +
	"\n" +
	"appleToken\x18\x01 \x01(\tR\n" +
	"appleToken\x12\x18\n" +
	"\aappleId\x18\x02 \x01(\tR\aappleId\x12\x1a\n" +
	"\bclientIp\x18\x03 \x01(\tR\bclientIp\x12\x1c\n" +
	"\tuserAgent\x18\x04 \x01(\tR\tuserAgent\x12\x1a\n" +
	"\bdeviceId\x18\x05 \x01(\tR\bdeviceId\"z\n" +
	"\x10LoginByAppleResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\"\n" +
	"\x04user\x18\x03 \x01(\v2\x0e.user.UserInfoR\x04user\x12\x14\n" +
	"\x05token\x18\x04 \x01(\tR\x05token\"(\n" +
	"\x0eGetUserInfoReq\x12\x16\n" +
	"\x06userId\x18\x01 \x01(\x04R\x06userId\"c\n" +
	"\x0fGetUserInfoResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\"\n" +
	"\x04user\x18\x03 \x01(\v2\x0e.user.UserInfoR\x04user\"\xaf\x01\n" +
	"\x11UpdateUserInfoReq\x12\x16\n" +
	"\x06userId\x18\x01 \x01(\x04R\x06userId\x12\x1a\n" +
	"\bnickname\x18\x02 \x01(\tR\bnickname\x12\x16\n" +
	"\x06avatar\x18\x03 \x01(\tR\x06avatar\x12\x16\n" +
	"\x06gender\x18\x04 \x01(\x05R\x06gender\x12\x1a\n" +
	"\bbirthday\x18\x05 \x01(\tR\bbirthday\x12\x1a\n" +
	"\blocation\x18\x06 \x01(\tR\blocation\"f\n" +
	"\x12UpdateUserInfoResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\"\n" +
	"\x04user\x18\x03 \x01(\v2\x0e.user.UserInfoR\x04user\"Y\n" +
	"\x0fGetLoginLogsReq\x12\x16\n" +
	"\x06userId\x18\x01 \x01(\x04R\x06userId\x12\x12\n" +
	"\x04page\x18\x02 \x01(\x05R\x04page\x12\x1a\n" +
	"\bpageSize\x18\x03 \x01(\x05R\bpageSize\"z\n" +
	"\x10GetLoginLogsResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\"\n" +
	"\x04logs\x18\x03 \x03(\v2\x0e.user.LoginLogR\x04logs\x12\x14\n" +
	"\x05total\x18\x04 \x01(\x03R\x05total\"B\n" +
	"\x0eSendSmsCodeReq\x12\x14\n" +
	"\x05phone\x18\x01 \x01(\tR\x05phone\x12\x1a\n" +
	"\bcodeType\x18\x02 \x01(\x05R\bcodeType\"?\n" +
	"\x0fSendSmsCodeResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"^\n" +
	"\x10VerifySmsCodeReq\x12\x14\n" +
	"\x05phone\x18\x01 \x01(\tR\x05phone\x12\x18\n" +
	"\asmsCode\x18\x02 \x01(\tR\asmsCode\x12\x1a\n" +
	"\bcodeType\x18\x03 \x01(\x05R\bcodeType\"[\n" +
	"\x11VerifySmsCodeResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x18\n" +
	"\aisValid\x18\x03 \x01(\bR\aisValid2\xda\x05\n" +
	"\vUserService\x12@\n" +
	"\rRegisterBySms\x12\x16.user.RegisterBySmsReq\x1a\x17.user.RegisterBySmsResp\x12I\n" +
	"\x10RegisterByGoogle\x12\x19.user.RegisterByGoogleReq\x1a\x1a.user.RegisterByGoogleResp\x12F\n" +
	"\x0fRegisterByApple\x12\x18.user.RegisterByAppleReq\x1a\x19.user.RegisterByAppleResp\x127\n" +
	"\n" +
	"LoginBySms\x12\x13.user.LoginBySmsReq\x1a\x14.user.LoginBySmsResp\x12@\n" +
	"\rLoginByGoogle\x12\x16.user.LoginByGoogleReq\x1a\x17.user.LoginByGoogleResp\x12=\n" +
	"\fLoginByApple\x12\x15.user.LoginByAppleReq\x1a\x16.user.LoginByAppleResp\x12:\n" +
	"\vGetUserInfo\x12\x14.user.GetUserInfoReq\x1a\x15.user.GetUserInfoResp\x12C\n" +
	"\x0eUpdateUserInfo\x12\x17.user.UpdateUserInfoReq\x1a\x18.user.UpdateUserInfoResp\x12=\n" +
	"\fGetLoginLogs\x12\x15.user.GetLoginLogsReq\x1a\x16.user.GetLoginLogsResp\x12:\n" +
	"\vSendSmsCode\x12\x14.user.SendSmsCodeReq\x1a\x15.user.SendSmsCodeResp\x12@\n" +
	"\rVerifySmsCode\x12\x16.user.VerifySmsCodeReq\x1a\x17.user.VerifySmsCodeRespB1Z/creativematrix.com/beyondreading/gen/proto/userb\x06proto3"

var (
	file_proto_user_user_proto_rawDescOnce sync.Once
	file_proto_user_user_proto_rawDescData []byte
)

func file_proto_user_user_proto_rawDescGZIP() []byte {
	file_proto_user_user_proto_rawDescOnce.Do(func() {
		file_proto_user_user_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_user_user_proto_rawDesc), len(file_proto_user_user_proto_rawDesc)))
	})
	return file_proto_user_user_proto_rawDescData
}

var file_proto_user_user_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_proto_user_user_proto_goTypes = []any{
	(*UserInfo)(nil),             // 0: user.UserInfo
	(*LoginLog)(nil),             // 1: user.LoginLog
	(*RegisterBySmsReq)(nil),     // 2: user.RegisterBySmsReq
	(*RegisterBySmsResp)(nil),    // 3: user.RegisterBySmsResp
	(*RegisterByGoogleReq)(nil),  // 4: user.RegisterByGoogleReq
	(*RegisterByGoogleResp)(nil), // 5: user.RegisterByGoogleResp
	(*RegisterByAppleReq)(nil),   // 6: user.RegisterByAppleReq
	(*RegisterByAppleResp)(nil),  // 7: user.RegisterByAppleResp
	(*LoginBySmsReq)(nil),        // 8: user.LoginBySmsReq
	(*LoginBySmsResp)(nil),       // 9: user.LoginBySmsResp
	(*LoginByGoogleReq)(nil),     // 10: user.LoginByGoogleReq
	(*LoginByGoogleResp)(nil),    // 11: user.LoginByGoogleResp
	(*LoginByAppleReq)(nil),      // 12: user.LoginByAppleReq
	(*LoginByAppleResp)(nil),     // 13: user.LoginByAppleResp
	(*GetUserInfoReq)(nil),       // 14: user.GetUserInfoReq
	(*GetUserInfoResp)(nil),      // 15: user.GetUserInfoResp
	(*UpdateUserInfoReq)(nil),    // 16: user.UpdateUserInfoReq
	(*UpdateUserInfoResp)(nil),   // 17: user.UpdateUserInfoResp
	(*GetLoginLogsReq)(nil),      // 18: user.GetLoginLogsReq
	(*GetLoginLogsResp)(nil),     // 19: user.GetLoginLogsResp
	(*SendSmsCodeReq)(nil),       // 20: user.SendSmsCodeReq
	(*SendSmsCodeResp)(nil),      // 21: user.SendSmsCodeResp
	(*VerifySmsCodeReq)(nil),     // 22: user.VerifySmsCodeReq
	(*VerifySmsCodeResp)(nil),    // 23: user.VerifySmsCodeResp
}
var file_proto_user_user_proto_depIdxs = []int32{
	0,  // 0: user.RegisterBySmsResp.user:type_name -> user.UserInfo
	0,  // 1: user.RegisterByGoogleResp.user:type_name -> user.UserInfo
	0,  // 2: user.RegisterByAppleResp.user:type_name -> user.UserInfo
	0,  // 3: user.LoginBySmsResp.user:type_name -> user.UserInfo
	0,  // 4: user.LoginByGoogleResp.user:type_name -> user.UserInfo
	0,  // 5: user.LoginByAppleResp.user:type_name -> user.UserInfo
	0,  // 6: user.GetUserInfoResp.user:type_name -> user.UserInfo
	0,  // 7: user.UpdateUserInfoResp.user:type_name -> user.UserInfo
	1,  // 8: user.GetLoginLogsResp.logs:type_name -> user.LoginLog
	2,  // 9: user.UserService.RegisterBySms:input_type -> user.RegisterBySmsReq
	4,  // 10: user.UserService.RegisterByGoogle:input_type -> user.RegisterByGoogleReq
	6,  // 11: user.UserService.RegisterByApple:input_type -> user.RegisterByAppleReq
	8,  // 12: user.UserService.LoginBySms:input_type -> user.LoginBySmsReq
	10, // 13: user.UserService.LoginByGoogle:input_type -> user.LoginByGoogleReq
	12, // 14: user.UserService.LoginByApple:input_type -> user.LoginByAppleReq
	14, // 15: user.UserService.GetUserInfo:input_type -> user.GetUserInfoReq
	16, // 16: user.UserService.UpdateUserInfo:input_type -> user.UpdateUserInfoReq
	18, // 17: user.UserService.GetLoginLogs:input_type -> user.GetLoginLogsReq
	20, // 18: user.UserService.SendSmsCode:input_type -> user.SendSmsCodeReq
	22, // 19: user.UserService.VerifySmsCode:input_type -> user.VerifySmsCodeReq
	3,  // 20: user.UserService.RegisterBySms:output_type -> user.RegisterBySmsResp
	5,  // 21: user.UserService.RegisterByGoogle:output_type -> user.RegisterByGoogleResp
	7,  // 22: user.UserService.RegisterByApple:output_type -> user.RegisterByAppleResp
	9,  // 23: user.UserService.LoginBySms:output_type -> user.LoginBySmsResp
	11, // 24: user.UserService.LoginByGoogle:output_type -> user.LoginByGoogleResp
	13, // 25: user.UserService.LoginByApple:output_type -> user.LoginByAppleResp
	15, // 26: user.UserService.GetUserInfo:output_type -> user.GetUserInfoResp
	17, // 27: user.UserService.UpdateUserInfo:output_type -> user.UpdateUserInfoResp
	19, // 28: user.UserService.GetLoginLogs:output_type -> user.GetLoginLogsResp
	21, // 29: user.UserService.SendSmsCode:output_type -> user.SendSmsCodeResp
	23, // 30: user.UserService.VerifySmsCode:output_type -> user.VerifySmsCodeResp
	20, // [20:31] is the sub-list for method output_type
	9,  // [9:20] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_proto_user_user_proto_init() }
func file_proto_user_user_proto_init() {
	if File_proto_user_user_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_user_user_proto_rawDesc), len(file_proto_user_user_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_user_user_proto_goTypes,
		DependencyIndexes: file_proto_user_user_proto_depIdxs,
		MessageInfos:      file_proto_user_user_proto_msgTypes,
	}.Build()
	File_proto_user_user_proto = out.File
	file_proto_user_user_proto_goTypes = nil
	file_proto_user_user_proto_depIdxs = nil
}
