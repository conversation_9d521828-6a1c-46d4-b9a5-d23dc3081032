// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.30.2
// source: proto/chapters/chapters.proto

package chapters

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Chapters_GetChaptersByOrder_FullMethodName = "/chapters.chapters/GetChaptersByOrder"
	Chapters_GetChaptersById_FullMethodName    = "/chapters.chapters/GetChaptersById"
	Chapters_ChapterList_FullMethodName        = "/chapters.chapters/ChapterList"
	Chapters_GetChaptersContent_FullMethodName = "/chapters.chapters/GetChaptersContent"
)

// ChaptersClient is the client API for Chapters service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ChaptersClient interface {
	GetChaptersByOrder(ctx context.Context, in *GetChaptersByOrderReq, opts ...grpc.CallOption) (*GetChaptersRsp, error)
	GetChaptersById(ctx context.Context, in *GetChaptersByIdReq, opts ...grpc.CallOption) (*GetChaptersRsp, error)
	ChapterList(ctx context.Context, in *ChapterListReq, opts ...grpc.CallOption) (*ChapterListRsp, error)
	GetChaptersContent(ctx context.Context, in *GetChaptersContentReq, opts ...grpc.CallOption) (*GetChaptersContentRsp, error)
}

type chaptersClient struct {
	cc grpc.ClientConnInterface
}

func NewChaptersClient(cc grpc.ClientConnInterface) ChaptersClient {
	return &chaptersClient{cc}
}

func (c *chaptersClient) GetChaptersByOrder(ctx context.Context, in *GetChaptersByOrderReq, opts ...grpc.CallOption) (*GetChaptersRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetChaptersRsp)
	err := c.cc.Invoke(ctx, Chapters_GetChaptersByOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chaptersClient) GetChaptersById(ctx context.Context, in *GetChaptersByIdReq, opts ...grpc.CallOption) (*GetChaptersRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetChaptersRsp)
	err := c.cc.Invoke(ctx, Chapters_GetChaptersById_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chaptersClient) ChapterList(ctx context.Context, in *ChapterListReq, opts ...grpc.CallOption) (*ChapterListRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ChapterListRsp)
	err := c.cc.Invoke(ctx, Chapters_ChapterList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *chaptersClient) GetChaptersContent(ctx context.Context, in *GetChaptersContentReq, opts ...grpc.CallOption) (*GetChaptersContentRsp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetChaptersContentRsp)
	err := c.cc.Invoke(ctx, Chapters_GetChaptersContent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChaptersServer is the server API for Chapters service.
// All implementations should embed UnimplementedChaptersServer
// for forward compatibility.
type ChaptersServer interface {
	GetChaptersByOrder(context.Context, *GetChaptersByOrderReq) (*GetChaptersRsp, error)
	GetChaptersById(context.Context, *GetChaptersByIdReq) (*GetChaptersRsp, error)
	ChapterList(context.Context, *ChapterListReq) (*ChapterListRsp, error)
	GetChaptersContent(context.Context, *GetChaptersContentReq) (*GetChaptersContentRsp, error)
}

// UnimplementedChaptersServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedChaptersServer struct{}

func (UnimplementedChaptersServer) GetChaptersByOrder(context.Context, *GetChaptersByOrderReq) (*GetChaptersRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChaptersByOrder not implemented")
}
func (UnimplementedChaptersServer) GetChaptersById(context.Context, *GetChaptersByIdReq) (*GetChaptersRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChaptersById not implemented")
}
func (UnimplementedChaptersServer) ChapterList(context.Context, *ChapterListReq) (*ChapterListRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChapterList not implemented")
}
func (UnimplementedChaptersServer) GetChaptersContent(context.Context, *GetChaptersContentReq) (*GetChaptersContentRsp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetChaptersContent not implemented")
}
func (UnimplementedChaptersServer) testEmbeddedByValue() {}

// UnsafeChaptersServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ChaptersServer will
// result in compilation errors.
type UnsafeChaptersServer interface {
	mustEmbedUnimplementedChaptersServer()
}

func RegisterChaptersServer(s grpc.ServiceRegistrar, srv ChaptersServer) {
	// If the following call pancis, it indicates UnimplementedChaptersServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Chapters_ServiceDesc, srv)
}

func _Chapters_GetChaptersByOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChaptersByOrderReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChaptersServer).GetChaptersByOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Chapters_GetChaptersByOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChaptersServer).GetChaptersByOrder(ctx, req.(*GetChaptersByOrderReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Chapters_GetChaptersById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChaptersByIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChaptersServer).GetChaptersById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Chapters_GetChaptersById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChaptersServer).GetChaptersById(ctx, req.(*GetChaptersByIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Chapters_ChapterList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChapterListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChaptersServer).ChapterList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Chapters_ChapterList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChaptersServer).ChapterList(ctx, req.(*ChapterListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Chapters_GetChaptersContent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetChaptersContentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChaptersServer).GetChaptersContent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Chapters_GetChaptersContent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChaptersServer).GetChaptersContent(ctx, req.(*GetChaptersContentReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Chapters_ServiceDesc is the grpc.ServiceDesc for Chapters service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Chapters_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "chapters.chapters",
	HandlerType: (*ChaptersServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetChaptersByOrder",
			Handler:    _Chapters_GetChaptersByOrder_Handler,
		},
		{
			MethodName: "GetChaptersById",
			Handler:    _Chapters_GetChaptersById_Handler,
		},
		{
			MethodName: "ChapterList",
			Handler:    _Chapters_ChapterList_Handler,
		},
		{
			MethodName: "GetChaptersContent",
			Handler:    _Chapters_GetChaptersContent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/chapters/chapters.proto",
}
