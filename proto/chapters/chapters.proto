syntax = "proto3";
package chapters;
option go_package = "creativematrix.com/beyondreading/proto/chapters";
import "proto/pbcommon/common.proto";

//import "google/api/annotations.proto";


service chapters {
  rpc GetChaptersByOrder(GetChaptersByOrderReq) returns (GetChaptersRsp);
  rpc GetChaptersById(GetChaptersByIdReq) returns(GetChaptersRsp);
  rpc ChapterList(ChapterListReq)returns(ChapterListRsp);
  rpc GetChaptersContent(GetChaptersContentReq) returns(GetChaptersContentRsp);
}

message chapter {
  string _id =1;
  string bookId = 2;
  float currency = 3;
  uint32 order = 4;
  uint32 contentLength = 5; //
  chapter_type type = 6;
  string title = 7;
  bool isVip = 8;
  bool isFree = 9;
  bool isLimitFree = 10;
  bool isMonthly = 11;
}

enum chapter_type {
  txt = 0;
  img = 1;
}

message GetChaptersByOrderReq{
  string bookId = 1;
  chapter_type type = 2;
  repeated uint32 chapterOrders = 3;
}

message GetChaptersByIdReq{
  string bookId =1;
  chapter_type type = 2;
  repeated string chapterIds = 3;
}

message ChapterListReq{
  string bookId =1;
  uint32 pageNum = 2;
  uint32 pageSize = 3;
}

message ChapterListRsp {
  string bookId = 1;
  repeated chapter chapters = 2;
}

message GetChaptersRsp{
  string bookId = 1;
  uint32 totalChapterCnt = 2;
  map<uint32,chapter> chapters = 3;
}

enum ChapterState {
  Available = 0;
  Unavailable = 1;
  MonthlyExpired = 2;
}

message ChapterContent {
  uint32 order = 1;
  string title = 2;
  chapter_type type = 3;
  uint32 contentLength = 4; //txt: 字数, 声音: 秒数
  string key = 5;
  string fileName = 6; // 文件名
  string path = 7; //存储路径
  bool isVip = 8;
  bool isFree = 9;
  bool isMonthly = 10;
  bool isLimitedFree = 11;
}

message GetChaptersContentReq {
  string bookId = 1;
  chapter_type type = 2;
  repeated uint32 chapterOrders = 3;
}

message GetChaptersContentRsp {
  string bookId = 1;
  uint32 totalChapterCnt = 2;
  repeated ChapterContent chaptersContent = 3;
}