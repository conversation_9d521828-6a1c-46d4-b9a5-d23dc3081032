// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: proto/chapters/chapters.proto

package chapters

import (
	_ "creativematrix.com/beyondreading/proto/pbcommon"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ChapterType int32

const (
	ChapterType_txt ChapterType = 0
	ChapterType_img ChapterType = 1
)

// Enum value maps for ChapterType.
var (
	ChapterType_name = map[int32]string{
		0: "txt",
		1: "img",
	}
	ChapterType_value = map[string]int32{
		"txt": 0,
		"img": 1,
	}
)

func (x ChapterType) Enum() *ChapterType {
	p := new(ChapterType)
	*p = x
	return p
}

func (x ChapterType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChapterType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_chapters_chapters_proto_enumTypes[0].Descriptor()
}

func (ChapterType) Type() protoreflect.EnumType {
	return &file_proto_chapters_chapters_proto_enumTypes[0]
}

func (x ChapterType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChapterType.Descriptor instead.
func (ChapterType) EnumDescriptor() ([]byte, []int) {
	return file_proto_chapters_chapters_proto_rawDescGZIP(), []int{0}
}

type ChapterState int32

const (
	ChapterState_Available      ChapterState = 0
	ChapterState_Unavailable    ChapterState = 1
	ChapterState_MonthlyExpired ChapterState = 2
)

// Enum value maps for ChapterState.
var (
	ChapterState_name = map[int32]string{
		0: "Available",
		1: "Unavailable",
		2: "MonthlyExpired",
	}
	ChapterState_value = map[string]int32{
		"Available":      0,
		"Unavailable":    1,
		"MonthlyExpired": 2,
	}
)

func (x ChapterState) Enum() *ChapterState {
	p := new(ChapterState)
	*p = x
	return p
}

func (x ChapterState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChapterState) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_chapters_chapters_proto_enumTypes[1].Descriptor()
}

func (ChapterState) Type() protoreflect.EnumType {
	return &file_proto_chapters_chapters_proto_enumTypes[1]
}

func (x ChapterState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChapterState.Descriptor instead.
func (ChapterState) EnumDescriptor() ([]byte, []int) {
	return file_proto_chapters_chapters_proto_rawDescGZIP(), []int{1}
}

type Chapter struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	XId           string                 `protobuf:"bytes,1,opt,name=_id,json=Id,proto3" json:"_id,omitempty"`
	BookId        string                 `protobuf:"bytes,2,opt,name=bookId,proto3" json:"bookId,omitempty"`
	Currency      float32                `protobuf:"fixed32,3,opt,name=currency,proto3" json:"currency,omitempty"`
	Order         uint32                 `protobuf:"varint,4,opt,name=order,proto3" json:"order,omitempty"`
	ContentLength uint32                 `protobuf:"varint,5,opt,name=contentLength,proto3" json:"contentLength,omitempty"` //
	Type          ChapterType            `protobuf:"varint,6,opt,name=type,proto3,enum=chapters.ChapterType" json:"type,omitempty"`
	Title         string                 `protobuf:"bytes,7,opt,name=title,proto3" json:"title,omitempty"`
	IsVip         bool                   `protobuf:"varint,8,opt,name=isVip,proto3" json:"isVip,omitempty"`
	IsFree        bool                   `protobuf:"varint,9,opt,name=isFree,proto3" json:"isFree,omitempty"`
	IsLimitFree   bool                   `protobuf:"varint,10,opt,name=isLimitFree,proto3" json:"isLimitFree,omitempty"`
	IsMonthly     bool                   `protobuf:"varint,11,opt,name=isMonthly,proto3" json:"isMonthly,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Chapter) Reset() {
	*x = Chapter{}
	mi := &file_proto_chapters_chapters_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Chapter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Chapter) ProtoMessage() {}

func (x *Chapter) ProtoReflect() protoreflect.Message {
	mi := &file_proto_chapters_chapters_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Chapter.ProtoReflect.Descriptor instead.
func (*Chapter) Descriptor() ([]byte, []int) {
	return file_proto_chapters_chapters_proto_rawDescGZIP(), []int{0}
}

func (x *Chapter) GetXId() string {
	if x != nil {
		return x.XId
	}
	return ""
}

func (x *Chapter) GetBookId() string {
	if x != nil {
		return x.BookId
	}
	return ""
}

func (x *Chapter) GetCurrency() float32 {
	if x != nil {
		return x.Currency
	}
	return 0
}

func (x *Chapter) GetOrder() uint32 {
	if x != nil {
		return x.Order
	}
	return 0
}

func (x *Chapter) GetContentLength() uint32 {
	if x != nil {
		return x.ContentLength
	}
	return 0
}

func (x *Chapter) GetType() ChapterType {
	if x != nil {
		return x.Type
	}
	return ChapterType_txt
}

func (x *Chapter) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Chapter) GetIsVip() bool {
	if x != nil {
		return x.IsVip
	}
	return false
}

func (x *Chapter) GetIsFree() bool {
	if x != nil {
		return x.IsFree
	}
	return false
}

func (x *Chapter) GetIsLimitFree() bool {
	if x != nil {
		return x.IsLimitFree
	}
	return false
}

func (x *Chapter) GetIsMonthly() bool {
	if x != nil {
		return x.IsMonthly
	}
	return false
}

type GetChaptersByOrderReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BookId        string                 `protobuf:"bytes,1,opt,name=bookId,proto3" json:"bookId,omitempty"`
	Type          ChapterType            `protobuf:"varint,2,opt,name=type,proto3,enum=chapters.ChapterType" json:"type,omitempty"`
	ChapterOrders []uint32               `protobuf:"varint,3,rep,packed,name=chapterOrders,proto3" json:"chapterOrders,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetChaptersByOrderReq) Reset() {
	*x = GetChaptersByOrderReq{}
	mi := &file_proto_chapters_chapters_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetChaptersByOrderReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChaptersByOrderReq) ProtoMessage() {}

func (x *GetChaptersByOrderReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_chapters_chapters_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChaptersByOrderReq.ProtoReflect.Descriptor instead.
func (*GetChaptersByOrderReq) Descriptor() ([]byte, []int) {
	return file_proto_chapters_chapters_proto_rawDescGZIP(), []int{1}
}

func (x *GetChaptersByOrderReq) GetBookId() string {
	if x != nil {
		return x.BookId
	}
	return ""
}

func (x *GetChaptersByOrderReq) GetType() ChapterType {
	if x != nil {
		return x.Type
	}
	return ChapterType_txt
}

func (x *GetChaptersByOrderReq) GetChapterOrders() []uint32 {
	if x != nil {
		return x.ChapterOrders
	}
	return nil
}

type GetChaptersByIdReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BookId        string                 `protobuf:"bytes,1,opt,name=bookId,proto3" json:"bookId,omitempty"`
	Type          ChapterType            `protobuf:"varint,2,opt,name=type,proto3,enum=chapters.ChapterType" json:"type,omitempty"`
	ChapterIds    []string               `protobuf:"bytes,3,rep,name=chapterIds,proto3" json:"chapterIds,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetChaptersByIdReq) Reset() {
	*x = GetChaptersByIdReq{}
	mi := &file_proto_chapters_chapters_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetChaptersByIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChaptersByIdReq) ProtoMessage() {}

func (x *GetChaptersByIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_chapters_chapters_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChaptersByIdReq.ProtoReflect.Descriptor instead.
func (*GetChaptersByIdReq) Descriptor() ([]byte, []int) {
	return file_proto_chapters_chapters_proto_rawDescGZIP(), []int{2}
}

func (x *GetChaptersByIdReq) GetBookId() string {
	if x != nil {
		return x.BookId
	}
	return ""
}

func (x *GetChaptersByIdReq) GetType() ChapterType {
	if x != nil {
		return x.Type
	}
	return ChapterType_txt
}

func (x *GetChaptersByIdReq) GetChapterIds() []string {
	if x != nil {
		return x.ChapterIds
	}
	return nil
}

type ChapterListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BookId        string                 `protobuf:"bytes,1,opt,name=bookId,proto3" json:"bookId,omitempty"`
	PageNum       uint32                 `protobuf:"varint,2,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize      uint32                 `protobuf:"varint,3,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChapterListReq) Reset() {
	*x = ChapterListReq{}
	mi := &file_proto_chapters_chapters_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChapterListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChapterListReq) ProtoMessage() {}

func (x *ChapterListReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_chapters_chapters_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChapterListReq.ProtoReflect.Descriptor instead.
func (*ChapterListReq) Descriptor() ([]byte, []int) {
	return file_proto_chapters_chapters_proto_rawDescGZIP(), []int{3}
}

func (x *ChapterListReq) GetBookId() string {
	if x != nil {
		return x.BookId
	}
	return ""
}

func (x *ChapterListReq) GetPageNum() uint32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *ChapterListReq) GetPageSize() uint32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ChapterListRsp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BookId        string                 `protobuf:"bytes,1,opt,name=bookId,proto3" json:"bookId,omitempty"`
	Chapters      []*Chapter             `protobuf:"bytes,2,rep,name=chapters,proto3" json:"chapters,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChapterListRsp) Reset() {
	*x = ChapterListRsp{}
	mi := &file_proto_chapters_chapters_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChapterListRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChapterListRsp) ProtoMessage() {}

func (x *ChapterListRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_chapters_chapters_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChapterListRsp.ProtoReflect.Descriptor instead.
func (*ChapterListRsp) Descriptor() ([]byte, []int) {
	return file_proto_chapters_chapters_proto_rawDescGZIP(), []int{4}
}

func (x *ChapterListRsp) GetBookId() string {
	if x != nil {
		return x.BookId
	}
	return ""
}

func (x *ChapterListRsp) GetChapters() []*Chapter {
	if x != nil {
		return x.Chapters
	}
	return nil
}

type GetChaptersRsp struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	BookId          string                 `protobuf:"bytes,1,opt,name=bookId,proto3" json:"bookId,omitempty"`
	TotalChapterCnt uint32                 `protobuf:"varint,2,opt,name=totalChapterCnt,proto3" json:"totalChapterCnt,omitempty"`
	Chapters        map[uint32]*Chapter    `protobuf:"bytes,3,rep,name=chapters,proto3" json:"chapters,omitempty" protobuf_key:"varint,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GetChaptersRsp) Reset() {
	*x = GetChaptersRsp{}
	mi := &file_proto_chapters_chapters_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetChaptersRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChaptersRsp) ProtoMessage() {}

func (x *GetChaptersRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_chapters_chapters_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChaptersRsp.ProtoReflect.Descriptor instead.
func (*GetChaptersRsp) Descriptor() ([]byte, []int) {
	return file_proto_chapters_chapters_proto_rawDescGZIP(), []int{5}
}

func (x *GetChaptersRsp) GetBookId() string {
	if x != nil {
		return x.BookId
	}
	return ""
}

func (x *GetChaptersRsp) GetTotalChapterCnt() uint32 {
	if x != nil {
		return x.TotalChapterCnt
	}
	return 0
}

func (x *GetChaptersRsp) GetChapters() map[uint32]*Chapter {
	if x != nil {
		return x.Chapters
	}
	return nil
}

type ChapterContent struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Order         uint32                 `protobuf:"varint,1,opt,name=order,proto3" json:"order,omitempty"`
	Title         string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Type          ChapterType            `protobuf:"varint,3,opt,name=type,proto3,enum=chapters.ChapterType" json:"type,omitempty"`
	ContentLength uint32                 `protobuf:"varint,4,opt,name=contentLength,proto3" json:"contentLength,omitempty"` //txt: 字数, 声音: 秒数
	Key           string                 `protobuf:"bytes,5,opt,name=key,proto3" json:"key,omitempty"`
	FileName      string                 `protobuf:"bytes,6,opt,name=fileName,proto3" json:"fileName,omitempty"` // 文件名
	Path          string                 `protobuf:"bytes,7,opt,name=path,proto3" json:"path,omitempty"`         //存储路径
	IsVip         bool                   `protobuf:"varint,8,opt,name=isVip,proto3" json:"isVip,omitempty"`
	IsFree        bool                   `protobuf:"varint,9,opt,name=isFree,proto3" json:"isFree,omitempty"`
	IsMonthly     bool                   `protobuf:"varint,10,opt,name=isMonthly,proto3" json:"isMonthly,omitempty"`
	IsLimitedFree bool                   `protobuf:"varint,11,opt,name=isLimitedFree,proto3" json:"isLimitedFree,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChapterContent) Reset() {
	*x = ChapterContent{}
	mi := &file_proto_chapters_chapters_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChapterContent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChapterContent) ProtoMessage() {}

func (x *ChapterContent) ProtoReflect() protoreflect.Message {
	mi := &file_proto_chapters_chapters_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChapterContent.ProtoReflect.Descriptor instead.
func (*ChapterContent) Descriptor() ([]byte, []int) {
	return file_proto_chapters_chapters_proto_rawDescGZIP(), []int{6}
}

func (x *ChapterContent) GetOrder() uint32 {
	if x != nil {
		return x.Order
	}
	return 0
}

func (x *ChapterContent) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ChapterContent) GetType() ChapterType {
	if x != nil {
		return x.Type
	}
	return ChapterType_txt
}

func (x *ChapterContent) GetContentLength() uint32 {
	if x != nil {
		return x.ContentLength
	}
	return 0
}

func (x *ChapterContent) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *ChapterContent) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *ChapterContent) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *ChapterContent) GetIsVip() bool {
	if x != nil {
		return x.IsVip
	}
	return false
}

func (x *ChapterContent) GetIsFree() bool {
	if x != nil {
		return x.IsFree
	}
	return false
}

func (x *ChapterContent) GetIsMonthly() bool {
	if x != nil {
		return x.IsMonthly
	}
	return false
}

func (x *ChapterContent) GetIsLimitedFree() bool {
	if x != nil {
		return x.IsLimitedFree
	}
	return false
}

type GetChaptersContentReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BookId        string                 `protobuf:"bytes,1,opt,name=bookId,proto3" json:"bookId,omitempty"`
	Type          ChapterType            `protobuf:"varint,2,opt,name=type,proto3,enum=chapters.ChapterType" json:"type,omitempty"`
	ChapterOrders []uint32               `protobuf:"varint,3,rep,packed,name=chapterOrders,proto3" json:"chapterOrders,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetChaptersContentReq) Reset() {
	*x = GetChaptersContentReq{}
	mi := &file_proto_chapters_chapters_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetChaptersContentReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChaptersContentReq) ProtoMessage() {}

func (x *GetChaptersContentReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_chapters_chapters_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChaptersContentReq.ProtoReflect.Descriptor instead.
func (*GetChaptersContentReq) Descriptor() ([]byte, []int) {
	return file_proto_chapters_chapters_proto_rawDescGZIP(), []int{7}
}

func (x *GetChaptersContentReq) GetBookId() string {
	if x != nil {
		return x.BookId
	}
	return ""
}

func (x *GetChaptersContentReq) GetType() ChapterType {
	if x != nil {
		return x.Type
	}
	return ChapterType_txt
}

func (x *GetChaptersContentReq) GetChapterOrders() []uint32 {
	if x != nil {
		return x.ChapterOrders
	}
	return nil
}

type GetChaptersContentRsp struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	BookId          string                 `protobuf:"bytes,1,opt,name=bookId,proto3" json:"bookId,omitempty"`
	TotalChapterCnt uint32                 `protobuf:"varint,2,opt,name=totalChapterCnt,proto3" json:"totalChapterCnt,omitempty"`
	ChaptersContent []*ChapterContent      `protobuf:"bytes,3,rep,name=chaptersContent,proto3" json:"chaptersContent,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GetChaptersContentRsp) Reset() {
	*x = GetChaptersContentRsp{}
	mi := &file_proto_chapters_chapters_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetChaptersContentRsp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChaptersContentRsp) ProtoMessage() {}

func (x *GetChaptersContentRsp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_chapters_chapters_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChaptersContentRsp.ProtoReflect.Descriptor instead.
func (*GetChaptersContentRsp) Descriptor() ([]byte, []int) {
	return file_proto_chapters_chapters_proto_rawDescGZIP(), []int{8}
}

func (x *GetChaptersContentRsp) GetBookId() string {
	if x != nil {
		return x.BookId
	}
	return ""
}

func (x *GetChaptersContentRsp) GetTotalChapterCnt() uint32 {
	if x != nil {
		return x.TotalChapterCnt
	}
	return 0
}

func (x *GetChaptersContentRsp) GetChaptersContent() []*ChapterContent {
	if x != nil {
		return x.ChaptersContent
	}
	return nil
}

var File_proto_chapters_chapters_proto protoreflect.FileDescriptor

const file_proto_chapters_chapters_proto_rawDesc = "" +
	"\n" +
	"\x1dproto/chapters/chapters.proto\x12\bchapters\x1a\x1bproto/pbcommon/common.proto\"\xba\x02\n" +
	"\achapter\x12\x0f\n" +
	"\x03_id\x18\x01 \x01(\tR\x02Id\x12\x16\n" +
	"\x06bookId\x18\x02 \x01(\tR\x06bookId\x12\x1a\n" +
	"\bcurrency\x18\x03 \x01(\x02R\bcurrency\x12\x14\n" +
	"\x05order\x18\x04 \x01(\rR\x05order\x12$\n" +
	"\rcontentLength\x18\x05 \x01(\rR\rcontentLength\x12*\n" +
	"\x04type\x18\x06 \x01(\x0e2\x16.chapters.chapter_typeR\x04type\x12\x14\n" +
	"\x05title\x18\a \x01(\tR\x05title\x12\x14\n" +
	"\x05isVip\x18\b \x01(\bR\x05isVip\x12\x16\n" +
	"\x06isFree\x18\t \x01(\bR\x06isFree\x12 \n" +
	"\visLimitFree\x18\n" +
	" \x01(\bR\visLimitFree\x12\x1c\n" +
	"\tisMonthly\x18\v \x01(\bR\tisMonthly\"\x81\x01\n" +
	"\x15GetChaptersByOrderReq\x12\x16\n" +
	"\x06bookId\x18\x01 \x01(\tR\x06bookId\x12*\n" +
	"\x04type\x18\x02 \x01(\x0e2\x16.chapters.chapter_typeR\x04type\x12$\n" +
	"\rchapterOrders\x18\x03 \x03(\rR\rchapterOrders\"x\n" +
	"\x12GetChaptersByIdReq\x12\x16\n" +
	"\x06bookId\x18\x01 \x01(\tR\x06bookId\x12*\n" +
	"\x04type\x18\x02 \x01(\x0e2\x16.chapters.chapter_typeR\x04type\x12\x1e\n" +
	"\n" +
	"chapterIds\x18\x03 \x03(\tR\n" +
	"chapterIds\"^\n" +
	"\x0eChapterListReq\x12\x16\n" +
	"\x06bookId\x18\x01 \x01(\tR\x06bookId\x12\x18\n" +
	"\apageNum\x18\x02 \x01(\rR\apageNum\x12\x1a\n" +
	"\bpageSize\x18\x03 \x01(\rR\bpageSize\"W\n" +
	"\x0eChapterListRsp\x12\x16\n" +
	"\x06bookId\x18\x01 \x01(\tR\x06bookId\x12-\n" +
	"\bchapters\x18\x02 \x03(\v2\x11.chapters.chapterR\bchapters\"\xe6\x01\n" +
	"\x0eGetChaptersRsp\x12\x16\n" +
	"\x06bookId\x18\x01 \x01(\tR\x06bookId\x12(\n" +
	"\x0ftotalChapterCnt\x18\x02 \x01(\rR\x0ftotalChapterCnt\x12B\n" +
	"\bchapters\x18\x03 \x03(\v2&.chapters.GetChaptersRsp.ChaptersEntryR\bchapters\x1aN\n" +
	"\rChaptersEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\rR\x03key\x12'\n" +
	"\x05value\x18\x02 \x01(\v2\x11.chapters.chapterR\x05value:\x028\x01\"\xc2\x02\n" +
	"\x0eChapterContent\x12\x14\n" +
	"\x05order\x18\x01 \x01(\rR\x05order\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12*\n" +
	"\x04type\x18\x03 \x01(\x0e2\x16.chapters.chapter_typeR\x04type\x12$\n" +
	"\rcontentLength\x18\x04 \x01(\rR\rcontentLength\x12\x10\n" +
	"\x03key\x18\x05 \x01(\tR\x03key\x12\x1a\n" +
	"\bfileName\x18\x06 \x01(\tR\bfileName\x12\x12\n" +
	"\x04path\x18\a \x01(\tR\x04path\x12\x14\n" +
	"\x05isVip\x18\b \x01(\bR\x05isVip\x12\x16\n" +
	"\x06isFree\x18\t \x01(\bR\x06isFree\x12\x1c\n" +
	"\tisMonthly\x18\n" +
	" \x01(\bR\tisMonthly\x12$\n" +
	"\risLimitedFree\x18\v \x01(\bR\risLimitedFree\"\x81\x01\n" +
	"\x15GetChaptersContentReq\x12\x16\n" +
	"\x06bookId\x18\x01 \x01(\tR\x06bookId\x12*\n" +
	"\x04type\x18\x02 \x01(\x0e2\x16.chapters.chapter_typeR\x04type\x12$\n" +
	"\rchapterOrders\x18\x03 \x03(\rR\rchapterOrders\"\x9d\x01\n" +
	"\x15GetChaptersContentRsp\x12\x16\n" +
	"\x06bookId\x18\x01 \x01(\tR\x06bookId\x12(\n" +
	"\x0ftotalChapterCnt\x18\x02 \x01(\rR\x0ftotalChapterCnt\x12B\n" +
	"\x0fchaptersContent\x18\x03 \x03(\v2\x18.chapters.ChapterContentR\x0fchaptersContent* \n" +
	"\fchapter_type\x12\a\n" +
	"\x03txt\x10\x00\x12\a\n" +
	"\x03img\x10\x01*B\n" +
	"\fChapterState\x12\r\n" +
	"\tAvailable\x10\x00\x12\x0f\n" +
	"\vUnavailable\x10\x01\x12\x12\n" +
	"\x0eMonthlyExpired\x10\x022\xc1\x02\n" +
	"\bchapters\x12O\n" +
	"\x12GetChaptersByOrder\x12\x1f.chapters.GetChaptersByOrderReq\x1a\x18.chapters.GetChaptersRsp\x12I\n" +
	"\x0fGetChaptersById\x12\x1c.chapters.GetChaptersByIdReq\x1a\x18.chapters.GetChaptersRsp\x12A\n" +
	"\vChapterList\x12\x18.chapters.ChapterListReq\x1a\x18.chapters.ChapterListRsp\x12V\n" +
	"\x12GetChaptersContent\x12\x1f.chapters.GetChaptersContentReq\x1a\x1f.chapters.GetChaptersContentRspB1Z/creativematrix.com/beyondreading/proto/chaptersb\x06proto3"

var (
	file_proto_chapters_chapters_proto_rawDescOnce sync.Once
	file_proto_chapters_chapters_proto_rawDescData []byte
)

func file_proto_chapters_chapters_proto_rawDescGZIP() []byte {
	file_proto_chapters_chapters_proto_rawDescOnce.Do(func() {
		file_proto_chapters_chapters_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_chapters_chapters_proto_rawDesc), len(file_proto_chapters_chapters_proto_rawDesc)))
	})
	return file_proto_chapters_chapters_proto_rawDescData
}

var file_proto_chapters_chapters_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_proto_chapters_chapters_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_proto_chapters_chapters_proto_goTypes = []any{
	(ChapterType)(0),              // 0: chapters.chapter_type
	(ChapterState)(0),             // 1: chapters.ChapterState
	(*Chapter)(nil),               // 2: chapters.chapter
	(*GetChaptersByOrderReq)(nil), // 3: chapters.GetChaptersByOrderReq
	(*GetChaptersByIdReq)(nil),    // 4: chapters.GetChaptersByIdReq
	(*ChapterListReq)(nil),        // 5: chapters.ChapterListReq
	(*ChapterListRsp)(nil),        // 6: chapters.ChapterListRsp
	(*GetChaptersRsp)(nil),        // 7: chapters.GetChaptersRsp
	(*ChapterContent)(nil),        // 8: chapters.ChapterContent
	(*GetChaptersContentReq)(nil), // 9: chapters.GetChaptersContentReq
	(*GetChaptersContentRsp)(nil), // 10: chapters.GetChaptersContentRsp
	nil,                           // 11: chapters.GetChaptersRsp.ChaptersEntry
}
var file_proto_chapters_chapters_proto_depIdxs = []int32{
	0,  // 0: chapters.chapter.type:type_name -> chapters.chapter_type
	0,  // 1: chapters.GetChaptersByOrderReq.type:type_name -> chapters.chapter_type
	0,  // 2: chapters.GetChaptersByIdReq.type:type_name -> chapters.chapter_type
	2,  // 3: chapters.ChapterListRsp.chapters:type_name -> chapters.chapter
	11, // 4: chapters.GetChaptersRsp.chapters:type_name -> chapters.GetChaptersRsp.ChaptersEntry
	0,  // 5: chapters.ChapterContent.type:type_name -> chapters.chapter_type
	0,  // 6: chapters.GetChaptersContentReq.type:type_name -> chapters.chapter_type
	8,  // 7: chapters.GetChaptersContentRsp.chaptersContent:type_name -> chapters.ChapterContent
	2,  // 8: chapters.GetChaptersRsp.ChaptersEntry.value:type_name -> chapters.chapter
	3,  // 9: chapters.chapters.GetChaptersByOrder:input_type -> chapters.GetChaptersByOrderReq
	4,  // 10: chapters.chapters.GetChaptersById:input_type -> chapters.GetChaptersByIdReq
	5,  // 11: chapters.chapters.ChapterList:input_type -> chapters.ChapterListReq
	9,  // 12: chapters.chapters.GetChaptersContent:input_type -> chapters.GetChaptersContentReq
	7,  // 13: chapters.chapters.GetChaptersByOrder:output_type -> chapters.GetChaptersRsp
	7,  // 14: chapters.chapters.GetChaptersById:output_type -> chapters.GetChaptersRsp
	6,  // 15: chapters.chapters.ChapterList:output_type -> chapters.ChapterListRsp
	10, // 16: chapters.chapters.GetChaptersContent:output_type -> chapters.GetChaptersContentRsp
	13, // [13:17] is the sub-list for method output_type
	9,  // [9:13] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_proto_chapters_chapters_proto_init() }
func file_proto_chapters_chapters_proto_init() {
	if File_proto_chapters_chapters_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_chapters_chapters_proto_rawDesc), len(file_proto_chapters_chapters_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_chapters_chapters_proto_goTypes,
		DependencyIndexes: file_proto_chapters_chapters_proto_depIdxs,
		EnumInfos:         file_proto_chapters_chapters_proto_enumTypes,
		MessageInfos:      file_proto_chapters_chapters_proto_msgTypes,
	}.Build()
	File_proto_chapters_chapters_proto = out.File
	file_proto_chapters_chapters_proto_goTypes = nil
	file_proto_chapters_chapters_proto_depIdxs = nil
}
