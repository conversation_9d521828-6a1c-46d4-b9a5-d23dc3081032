protoc ./proto/pbcommon/common.proto --go_out=paths=source_relative:. --go-grpc_out=paths=source_relative:.
protoc ./proto/recommend/recommend.proto --go_out=paths=source_relative:. --go-grpc_out=paths=source_relative:. --go-grpc_opt=require_unimplemented_servers=false

protoc ./proto/books/books.proto --go_out=paths=source_relative:. --go-grpc_out=paths=source_relative:. --go-grpc_opt=require_unimplemented_servers=false

protoc ./proto/bookshelf/bookshelfsvc.proto --go_out=paths=source_relative:. --go-grpc_out=paths=source_relative:. --go-grpc_opt=require_unimplemented_servers=false

protoc ./proto/chapters/chapters.proto --go_out=paths=source_relative:. --go-grpc_out=paths=source_relative:. --go-grpc_opt=require_unimplemented_servers=false

protoc ./proto/purchase/purchase.proto --go_out=paths=source_relative:. --go-grpc_out=paths=source_relative:. --go-grpc_opt=require_unimplemented_servers=false

protoc ./proto/account/account.proto --go_out=paths=source_relative:. --go-grpc_out=paths=source_relative:. --go-grpc_opt=require_unimplemented_servers=false

protoc ./proto/user/user.proto --go_out=paths=source_relative:. --go-grpc_out=paths=source_relative:. --go-grpc_opt=require_unimplemented_servers=false

protoc ./proto/payment/payment.proto --go_out=paths=source_relative:. --go-grpc_out=paths=source_relative:. --go-grpc_opt=require_unimplemented_servers=false
