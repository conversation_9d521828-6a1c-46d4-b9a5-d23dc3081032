// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: proto/pbcommon/common.proto

package pbcommon

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Book struct {
	state                     protoimpl.MessageState `protogen:"open.v1"`
	Id                        string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Title                     string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Aliases                   string                 `protobuf:"bytes,3,opt,name=aliases,proto3" json:"aliases,omitempty"`
	Author                    string                 `protobuf:"bytes,4,opt,name=author,proto3" json:"author,omitempty"`
	Original                  bool                   `protobuf:"varint,5,opt,name=original,proto3" json:"original,omitempty"`
	Site                      string                 `protobuf:"bytes,6,opt,name=site,proto3" json:"site,omitempty"`
	Cover                     string                 `protobuf:"bytes,7,opt,name=cover,proto3" json:"cover,omitempty"`
	ShortIntro                string                 `protobuf:"bytes,8,opt,name=shortIntro,proto3" json:"shortIntro,omitempty"`
	LongIntro                 string                 `protobuf:"bytes,9,opt,name=longIntro,proto3" json:"longIntro,omitempty"`
	Cat                       string                 `protobuf:"bytes,10,opt,name=cat,proto3" json:"cat,omitempty"`
	Tags                      []string               `protobuf:"bytes,11,rep,name=tags,proto3" json:"tags,omitempty"`
	Categories                []string               `protobuf:"bytes,12,rep,name=categories,proto3" json:"categories,omitempty"`
	Gender                    []string               `protobuf:"bytes,13,rep,name=gender,proto3" json:"gender,omitempty"`
	LastChapter               string                 `protobuf:"bytes,14,opt,name=lastChapter,proto3" json:"lastChapter,omitempty"`
	BookType                  string                 `protobuf:"bytes,15,opt,name=bookType,proto3" json:"bookType,omitempty"`
	ChaptersCount             int64                  `protobuf:"varint,16,opt,name=chaptersCount,proto3" json:"chaptersCount,omitempty"`
	IsSerial                  bool                   `protobuf:"varint,17,opt,name=isSerial,proto3" json:"isSerial,omitempty"`
	Updated                   string                 `protobuf:"bytes,18,opt,name=updated,proto3" json:"updated,omitempty"`
	TocCount                  int64                  `protobuf:"varint,19,opt,name=tocCount,proto3" json:"tocCount,omitempty"`
	IsCharge                  bool                   `protobuf:"varint,20,opt,name=isCharge,proto3" json:"isCharge,omitempty"`
	RetentionRatio            float64                `protobuf:"fixed64,21,opt,name=retentionRatio,proto3" json:"retentionRatio,omitempty"`
	MinRetentionRatio         float64                `protobuf:"fixed64,22,opt,name=minRetentionRatio,proto3" json:"minRetentionRatio,omitempty"`
	MonthRetentionRatio       float64                `protobuf:"fixed64,23,opt,name=monthRetentionRatio,proto3" json:"monthRetentionRatio,omitempty"`
	NearlyMonthRetentionRatio float64                `protobuf:"fixed64,24,opt,name=nearlyMonthRetentionRatio,proto3" json:"nearlyMonthRetentionRatio,omitempty"`
	TotalRetentionRatio       float64                `protobuf:"fixed64,25,opt,name=totalRetentionRatio,proto3" json:"totalRetentionRatio,omitempty"`
	FollowerRank              int64                  `protobuf:"varint,26,opt,name=followerRank,proto3" json:"followerRank,omitempty"`
	RetentionRatioRank        float64                `protobuf:"fixed64,27,opt,name=retentionRatioRank,proto3" json:"retentionRatioRank,omitempty"`
	SerializeWordCount        int64                  `protobuf:"varint,28,opt,name=serializeWordCount,proto3" json:"serializeWordCount,omitempty"`
	WordCount                 int64                  `protobuf:"varint,29,opt,name=wordCount,proto3" json:"wordCount,omitempty"`
	Created                   int64                  `protobuf:"varint,30,opt,name=created,proto3" json:"created,omitempty"`
	FollowerCount             int64                  `protobuf:"varint,31,opt,name=followerCount,proto3" json:"followerCount,omitempty"`
	LatelyFollowerBase        int64                  `protobuf:"varint,32,opt,name=latelyFollowerBase,proto3" json:"latelyFollowerBase,omitempty"`
	LatelyFollower            int64                  `protobuf:"varint,33,opt,name=latelyFollower,proto3" json:"latelyFollower,omitempty"`
	YesterdayFollower         int64                  `protobuf:"varint,34,opt,name=yesterdayFollower,proto3" json:"yesterdayFollower,omitempty"`
	MonthFollower             int64                  `protobuf:"varint,35,opt,name=monthFollower,proto3" json:"monthFollower,omitempty"`
	NearlyMonthFollower       int64                  `protobuf:"varint,36,opt,name=nearlyMonthFollower,proto3" json:"nearlyMonthFollower,omitempty"`
	TotalFollower             int64                  `protobuf:"varint,37,opt,name=totalFollower,proto3" json:"totalFollower,omitempty"`
	IncFollower               int64                  `protobuf:"varint,38,opt,name=incFollower,proto3" json:"incFollower,omitempty"`
	PostCount                 int64                  `protobuf:"varint,39,opt,name=postCount,proto3" json:"postCount,omitempty"`
	ReviewCount               int64                  `protobuf:"varint,40,opt,name=reviewCount,proto3" json:"reviewCount,omitempty"`
	ShortReviewCount          int64                  `protobuf:"varint,41,opt,name=shortReviewCount,proto3" json:"shortReviewCount,omitempty"`
	TotalPoint                int64                  `protobuf:"varint,42,opt,name=totalPoint,proto3" json:"totalPoint,omitempty"`
	WritingPoint              int64                  `protobuf:"varint,43,opt,name=writingPoint,proto3" json:"writingPoint,omitempty"`
	DramaPoint                int64                  `protobuf:"varint,44,opt,name=dramaPoint,proto3" json:"dramaPoint,omitempty"`
	GradeCount                int64                  `protobuf:"varint,45,opt,name=gradeCount,proto3" json:"gradeCount,omitempty"`
	Banned                    int32                  `protobuf:"varint,46,opt,name=banned,proto3" json:"banned,omitempty"`
	ManualUnbanned            bool                   `protobuf:"varint,47,opt,name=manualUnbanned,proto3" json:"manualUnbanned,omitempty"`
	HasNotice                 bool                   `protobuf:"varint,48,opt,name=hasNotice,proto3" json:"hasNotice,omitempty"`
	TocUpdated                string                 `protobuf:"bytes,49,opt,name=tocUpdated,proto3" json:"tocUpdated,omitempty"`
	EndTime                   string                 `protobuf:"bytes,50,opt,name=endTime,proto3" json:"endTime,omitempty"`
	HasCmread                 bool                   `protobuf:"varint,51,opt,name=hasCmread,proto3" json:"hasCmread,omitempty"`
	ThirdFlags                []string               `protobuf:"bytes,52,rep,name=thirdFlags,proto3" json:"thirdFlags,omitempty"`
	ThirdFlagsUpdated         string                 `protobuf:"bytes,53,opt,name=thirdFlagsUpdated,proto3" json:"thirdFlagsUpdated,omitempty"`
	ReferenceSource           string                 `protobuf:"bytes,54,opt,name=referenceSource,proto3" json:"referenceSource,omitempty"`
	UseSource                 string                 `protobuf:"bytes,55,opt,name=useSource,proto3" json:"useSource,omitempty"`
	MajorCate                 string                 `protobuf:"bytes,56,opt,name=majorCate,proto3" json:"majorCate,omitempty"`
	MinorCate                 string                 `protobuf:"bytes,57,opt,name=minorCate,proto3" json:"minorCate,omitempty"`
	Extra                     *Extra                 `protobuf:"bytes,58,opt,name=extra,proto3" json:"extra,omitempty"`
	HasCp                     bool                   `protobuf:"varint,59,opt,name=hasCp,proto3" json:"hasCp,omitempty"`
	CpOnly                    bool                   `protobuf:"varint,60,opt,name=cpOnly,proto3" json:"cpOnly,omitempty"`
	AllowBeanVoucher          bool                   `protobuf:"varint,61,opt,name=allowBeanVoucher,proto3" json:"allowBeanVoucher,omitempty"`
	AllowVoucher              bool                   `protobuf:"varint,62,opt,name=allowVoucher,proto3" json:"allowVoucher,omitempty"`
	AllowMonthly              bool                   `protobuf:"varint,63,opt,name=allowMonthly,proto3" json:"allowMonthly,omitempty"`
	Le                        bool                   `protobuf:"varint,64,opt,name=le,proto3" json:"le,omitempty"`
	AllowAddOtherSource       bool                   `protobuf:"varint,65,opt,name=allowAddOtherSource,proto3" json:"allowAddOtherSource,omitempty"`
	ChargePoint               int64                  `protobuf:"varint,66,opt,name=chargePoint,proto3" json:"chargePoint,omitempty"`
	ContentType               string                 `protobuf:"bytes,67,opt,name=contentType,proto3" json:"contentType,omitempty"`
	Currency                  int64                  `protobuf:"varint,68,opt,name=currency,proto3" json:"currency,omitempty"`
	Superscript               string                 `protobuf:"bytes,69,opt,name=superscript,proto3" json:"superscript,omitempty"`
	SizeType                  int64                  `protobuf:"varint,70,opt,name=sizeType,proto3" json:"sizeType,omitempty"`
	BuyType                   int64                  `protobuf:"varint,71,opt,name=buyType,proto3" json:"buyType,omitempty"`
	RectangleCover            string                 `protobuf:"bytes,72,opt,name=rectangleCover,proto3" json:"rectangleCover,omitempty"`
	BigCover                  string                 `protobuf:"bytes,73,opt,name=bigCover,proto3" json:"bigCover,omitempty"`
	RecommendIntro            string                 `protobuf:"bytes,74,opt,name=recommendIntro,proto3" json:"recommendIntro,omitempty"`
	HasCopyright              bool                   `protobuf:"varint,75,opt,name=hasCopyright,proto3" json:"hasCopyright,omitempty"`
	Rating                    *Rating                `protobuf:"bytes,76,opt,name=rating,proto3" json:"rating,omitempty"`
	Apptype                   []int64                `protobuf:"varint,77,rep,packed,name=apptype,proto3" json:"apptype,omitempty"`
	HiddenPackage             []string               `protobuf:"bytes,78,rep,name=hiddenPackage,proto3" json:"hiddenPackage,omitempty"`
	ExportMark                int64                  `protobuf:"varint,79,opt,name=exportMark,proto3" json:"exportMark,omitempty"`
	MajorCateV2               string                 `protobuf:"bytes,80,opt,name=majorCateV2,proto3" json:"majorCateV2,omitempty"`
	MinorCateV2               string                 `protobuf:"bytes,81,opt,name=minorCateV2,proto3" json:"minorCateV2,omitempty"`
	CategoryTag               []string               `protobuf:"bytes,82,rep,name=categoryTag,proto3" json:"categoryTag,omitempty"`
	ModifyTime                string                 `protobuf:"bytes,83,opt,name=modifyTime,proto3" json:"modifyTime,omitempty"`
	AllowFree                 bool                   `protobuf:"varint,84,opt,name=allowFree,proto3" json:"allowFree,omitempty"`
	// 是否为精品书
	IsFineBook     bool      `protobuf:"varint,85,opt,name=isFineBook,proto3" json:"isFineBook,omitempty"`
	Cps            *CpSource `protobuf:"bytes,86,opt,name=cps,proto3" json:"cps,omitempty"`
	SafeLevel      int32     `protobuf:"varint,87,opt,name=safeLevel,proto3" json:"safeLevel,omitempty"`           // 安全等级 0->无风险 10->暧昧 30->轻微涉黄 50->轻微涉军/涉政 70->严重涉黄/涉政 (书库定义)
	LastChapter1   string    `protobuf:"bytes,88,opt,name=lastChapter1,proto3" json:"lastChapter1,omitempty"`      //阅文停更章节名
	ChaptersCount1 int32     `protobuf:"varint,89,opt,name=chaptersCount1,proto3" json:"chaptersCount1,omitempty"` //阅文停更章节数
	Update1        int64     `protobuf:"varint,90,opt,name=update1,proto3" json:"update1,omitempty"`               //阅文停更时间
	ContentLevel   int32     `protobuf:"varint,91,opt,name=contentLevel,proto3" json:"contentLevel,omitempty"`     // 内容分级 a:0 b:1 c:2 s:3 e:-1
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *Book) Reset() {
	*x = Book{}
	mi := &file_proto_pbcommon_common_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Book) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Book) ProtoMessage() {}

func (x *Book) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pbcommon_common_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Book.ProtoReflect.Descriptor instead.
func (*Book) Descriptor() ([]byte, []int) {
	return file_proto_pbcommon_common_proto_rawDescGZIP(), []int{0}
}

func (x *Book) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Book) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Book) GetAliases() string {
	if x != nil {
		return x.Aliases
	}
	return ""
}

func (x *Book) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *Book) GetOriginal() bool {
	if x != nil {
		return x.Original
	}
	return false
}

func (x *Book) GetSite() string {
	if x != nil {
		return x.Site
	}
	return ""
}

func (x *Book) GetCover() string {
	if x != nil {
		return x.Cover
	}
	return ""
}

func (x *Book) GetShortIntro() string {
	if x != nil {
		return x.ShortIntro
	}
	return ""
}

func (x *Book) GetLongIntro() string {
	if x != nil {
		return x.LongIntro
	}
	return ""
}

func (x *Book) GetCat() string {
	if x != nil {
		return x.Cat
	}
	return ""
}

func (x *Book) GetTags() []string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *Book) GetCategories() []string {
	if x != nil {
		return x.Categories
	}
	return nil
}

func (x *Book) GetGender() []string {
	if x != nil {
		return x.Gender
	}
	return nil
}

func (x *Book) GetLastChapter() string {
	if x != nil {
		return x.LastChapter
	}
	return ""
}

func (x *Book) GetBookType() string {
	if x != nil {
		return x.BookType
	}
	return ""
}

func (x *Book) GetChaptersCount() int64 {
	if x != nil {
		return x.ChaptersCount
	}
	return 0
}

func (x *Book) GetIsSerial() bool {
	if x != nil {
		return x.IsSerial
	}
	return false
}

func (x *Book) GetUpdated() string {
	if x != nil {
		return x.Updated
	}
	return ""
}

func (x *Book) GetTocCount() int64 {
	if x != nil {
		return x.TocCount
	}
	return 0
}

func (x *Book) GetIsCharge() bool {
	if x != nil {
		return x.IsCharge
	}
	return false
}

func (x *Book) GetRetentionRatio() float64 {
	if x != nil {
		return x.RetentionRatio
	}
	return 0
}

func (x *Book) GetMinRetentionRatio() float64 {
	if x != nil {
		return x.MinRetentionRatio
	}
	return 0
}

func (x *Book) GetMonthRetentionRatio() float64 {
	if x != nil {
		return x.MonthRetentionRatio
	}
	return 0
}

func (x *Book) GetNearlyMonthRetentionRatio() float64 {
	if x != nil {
		return x.NearlyMonthRetentionRatio
	}
	return 0
}

func (x *Book) GetTotalRetentionRatio() float64 {
	if x != nil {
		return x.TotalRetentionRatio
	}
	return 0
}

func (x *Book) GetFollowerRank() int64 {
	if x != nil {
		return x.FollowerRank
	}
	return 0
}

func (x *Book) GetRetentionRatioRank() float64 {
	if x != nil {
		return x.RetentionRatioRank
	}
	return 0
}

func (x *Book) GetSerializeWordCount() int64 {
	if x != nil {
		return x.SerializeWordCount
	}
	return 0
}

func (x *Book) GetWordCount() int64 {
	if x != nil {
		return x.WordCount
	}
	return 0
}

func (x *Book) GetCreated() int64 {
	if x != nil {
		return x.Created
	}
	return 0
}

func (x *Book) GetFollowerCount() int64 {
	if x != nil {
		return x.FollowerCount
	}
	return 0
}

func (x *Book) GetLatelyFollowerBase() int64 {
	if x != nil {
		return x.LatelyFollowerBase
	}
	return 0
}

func (x *Book) GetLatelyFollower() int64 {
	if x != nil {
		return x.LatelyFollower
	}
	return 0
}

func (x *Book) GetYesterdayFollower() int64 {
	if x != nil {
		return x.YesterdayFollower
	}
	return 0
}

func (x *Book) GetMonthFollower() int64 {
	if x != nil {
		return x.MonthFollower
	}
	return 0
}

func (x *Book) GetNearlyMonthFollower() int64 {
	if x != nil {
		return x.NearlyMonthFollower
	}
	return 0
}

func (x *Book) GetTotalFollower() int64 {
	if x != nil {
		return x.TotalFollower
	}
	return 0
}

func (x *Book) GetIncFollower() int64 {
	if x != nil {
		return x.IncFollower
	}
	return 0
}

func (x *Book) GetPostCount() int64 {
	if x != nil {
		return x.PostCount
	}
	return 0
}

func (x *Book) GetReviewCount() int64 {
	if x != nil {
		return x.ReviewCount
	}
	return 0
}

func (x *Book) GetShortReviewCount() int64 {
	if x != nil {
		return x.ShortReviewCount
	}
	return 0
}

func (x *Book) GetTotalPoint() int64 {
	if x != nil {
		return x.TotalPoint
	}
	return 0
}

func (x *Book) GetWritingPoint() int64 {
	if x != nil {
		return x.WritingPoint
	}
	return 0
}

func (x *Book) GetDramaPoint() int64 {
	if x != nil {
		return x.DramaPoint
	}
	return 0
}

func (x *Book) GetGradeCount() int64 {
	if x != nil {
		return x.GradeCount
	}
	return 0
}

func (x *Book) GetBanned() int32 {
	if x != nil {
		return x.Banned
	}
	return 0
}

func (x *Book) GetManualUnbanned() bool {
	if x != nil {
		return x.ManualUnbanned
	}
	return false
}

func (x *Book) GetHasNotice() bool {
	if x != nil {
		return x.HasNotice
	}
	return false
}

func (x *Book) GetTocUpdated() string {
	if x != nil {
		return x.TocUpdated
	}
	return ""
}

func (x *Book) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *Book) GetHasCmread() bool {
	if x != nil {
		return x.HasCmread
	}
	return false
}

func (x *Book) GetThirdFlags() []string {
	if x != nil {
		return x.ThirdFlags
	}
	return nil
}

func (x *Book) GetThirdFlagsUpdated() string {
	if x != nil {
		return x.ThirdFlagsUpdated
	}
	return ""
}

func (x *Book) GetReferenceSource() string {
	if x != nil {
		return x.ReferenceSource
	}
	return ""
}

func (x *Book) GetUseSource() string {
	if x != nil {
		return x.UseSource
	}
	return ""
}

func (x *Book) GetMajorCate() string {
	if x != nil {
		return x.MajorCate
	}
	return ""
}

func (x *Book) GetMinorCate() string {
	if x != nil {
		return x.MinorCate
	}
	return ""
}

func (x *Book) GetExtra() *Extra {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *Book) GetHasCp() bool {
	if x != nil {
		return x.HasCp
	}
	return false
}

func (x *Book) GetCpOnly() bool {
	if x != nil {
		return x.CpOnly
	}
	return false
}

func (x *Book) GetAllowBeanVoucher() bool {
	if x != nil {
		return x.AllowBeanVoucher
	}
	return false
}

func (x *Book) GetAllowVoucher() bool {
	if x != nil {
		return x.AllowVoucher
	}
	return false
}

func (x *Book) GetAllowMonthly() bool {
	if x != nil {
		return x.AllowMonthly
	}
	return false
}

func (x *Book) GetLe() bool {
	if x != nil {
		return x.Le
	}
	return false
}

func (x *Book) GetAllowAddOtherSource() bool {
	if x != nil {
		return x.AllowAddOtherSource
	}
	return false
}

func (x *Book) GetChargePoint() int64 {
	if x != nil {
		return x.ChargePoint
	}
	return 0
}

func (x *Book) GetContentType() string {
	if x != nil {
		return x.ContentType
	}
	return ""
}

func (x *Book) GetCurrency() int64 {
	if x != nil {
		return x.Currency
	}
	return 0
}

func (x *Book) GetSuperscript() string {
	if x != nil {
		return x.Superscript
	}
	return ""
}

func (x *Book) GetSizeType() int64 {
	if x != nil {
		return x.SizeType
	}
	return 0
}

func (x *Book) GetBuyType() int64 {
	if x != nil {
		return x.BuyType
	}
	return 0
}

func (x *Book) GetRectangleCover() string {
	if x != nil {
		return x.RectangleCover
	}
	return ""
}

func (x *Book) GetBigCover() string {
	if x != nil {
		return x.BigCover
	}
	return ""
}

func (x *Book) GetRecommendIntro() string {
	if x != nil {
		return x.RecommendIntro
	}
	return ""
}

func (x *Book) GetHasCopyright() bool {
	if x != nil {
		return x.HasCopyright
	}
	return false
}

func (x *Book) GetRating() *Rating {
	if x != nil {
		return x.Rating
	}
	return nil
}

func (x *Book) GetApptype() []int64 {
	if x != nil {
		return x.Apptype
	}
	return nil
}

func (x *Book) GetHiddenPackage() []string {
	if x != nil {
		return x.HiddenPackage
	}
	return nil
}

func (x *Book) GetExportMark() int64 {
	if x != nil {
		return x.ExportMark
	}
	return 0
}

func (x *Book) GetMajorCateV2() string {
	if x != nil {
		return x.MajorCateV2
	}
	return ""
}

func (x *Book) GetMinorCateV2() string {
	if x != nil {
		return x.MinorCateV2
	}
	return ""
}

func (x *Book) GetCategoryTag() []string {
	if x != nil {
		return x.CategoryTag
	}
	return nil
}

func (x *Book) GetModifyTime() string {
	if x != nil {
		return x.ModifyTime
	}
	return ""
}

func (x *Book) GetAllowFree() bool {
	if x != nil {
		return x.AllowFree
	}
	return false
}

func (x *Book) GetIsFineBook() bool {
	if x != nil {
		return x.IsFineBook
	}
	return false
}

func (x *Book) GetCps() *CpSource {
	if x != nil {
		return x.Cps
	}
	return nil
}

func (x *Book) GetSafeLevel() int32 {
	if x != nil {
		return x.SafeLevel
	}
	return 0
}

func (x *Book) GetLastChapter1() string {
	if x != nil {
		return x.LastChapter1
	}
	return ""
}

func (x *Book) GetChaptersCount1() int32 {
	if x != nil {
		return x.ChaptersCount1
	}
	return 0
}

func (x *Book) GetUpdate1() int64 {
	if x != nil {
		return x.Update1
	}
	return 0
}

func (x *Book) GetContentLevel() int32 {
	if x != nil {
		return x.ContentLevel
	}
	return 0
}

type BookInfo struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Title           string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Author          string                 `protobuf:"bytes,3,opt,name=author,proto3" json:"author,omitempty"`
	Cover           string                 `protobuf:"bytes,4,opt,name=cover,proto3" json:"cover,omitempty"`
	LastChapter     string                 `protobuf:"bytes,5,opt,name=lastChapter,proto3" json:"lastChapter,omitempty"`
	Updated         string                 `protobuf:"bytes,6,opt,name=updated,proto3" json:"updated,omitempty"`
	ChaptersCount   int64                  `protobuf:"varint,7,opt,name=ChaptersCount,proto3" json:"ChaptersCount,omitempty"`
	ReferenceSource string                 `protobuf:"bytes,8,opt,name=referenceSource,proto3" json:"referenceSource,omitempty"`
	HasCp           bool                   `protobuf:"varint,9,opt,name=hasCp,proto3" json:"hasCp,omitempty"`
	Le              bool                   `protobuf:"varint,10,opt,name=le,proto3" json:"le,omitempty"`
	AllowVoucher    bool                   `protobuf:"varint,11,opt,name=allowVoucher,proto3" json:"allowVoucher,omitempty"`
	AllowMonthly    bool                   `protobuf:"varint,12,opt,name=allowMonthly,proto3" json:"allowMonthly,omitempty"`
	ContentType     string                 `protobuf:"bytes,13,opt,name=contentType,proto3" json:"contentType,omitempty"`
	Superscript     string                 `protobuf:"bytes,14,opt,name=superscript,proto3" json:"superscript,omitempty"`
	Banned          int32                  `protobuf:"varint,15,opt,name=Banned,proto3" json:"Banned,omitempty"`
	Apptype         []int32                `protobuf:"varint,16,rep,packed,name=apptype,proto3" json:"apptype,omitempty"`
	HiddenPackage   []string               `protobuf:"bytes,17,rep,name=hiddenPackage,proto3" json:"hiddenPackage,omitempty"`
	BuyType         int64                  `protobuf:"varint,18,opt,name=buyType,proto3" json:"buyType,omitempty"`
	ModifyTime      string                 `protobuf:"bytes,19,opt,name=modifyTime,proto3" json:"modifyTime,omitempty"`
	AllowFree       bool                   `protobuf:"varint,20,opt,name=allowFree,proto3" json:"allowFree,omitempty"`
	IsFineBook      bool                   `protobuf:"varint,21,opt,name=IsFineBook,proto3" json:"IsFineBook,omitempty"`
	SizeType        int64                  `protobuf:"varint,22,opt,name=sizeType,proto3" json:"sizeType,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *BookInfo) Reset() {
	*x = BookInfo{}
	mi := &file_proto_pbcommon_common_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BookInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookInfo) ProtoMessage() {}

func (x *BookInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pbcommon_common_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookInfo.ProtoReflect.Descriptor instead.
func (*BookInfo) Descriptor() ([]byte, []int) {
	return file_proto_pbcommon_common_proto_rawDescGZIP(), []int{1}
}

func (x *BookInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BookInfo) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *BookInfo) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *BookInfo) GetCover() string {
	if x != nil {
		return x.Cover
	}
	return ""
}

func (x *BookInfo) GetLastChapter() string {
	if x != nil {
		return x.LastChapter
	}
	return ""
}

func (x *BookInfo) GetUpdated() string {
	if x != nil {
		return x.Updated
	}
	return ""
}

func (x *BookInfo) GetChaptersCount() int64 {
	if x != nil {
		return x.ChaptersCount
	}
	return 0
}

func (x *BookInfo) GetReferenceSource() string {
	if x != nil {
		return x.ReferenceSource
	}
	return ""
}

func (x *BookInfo) GetHasCp() bool {
	if x != nil {
		return x.HasCp
	}
	return false
}

func (x *BookInfo) GetLe() bool {
	if x != nil {
		return x.Le
	}
	return false
}

func (x *BookInfo) GetAllowVoucher() bool {
	if x != nil {
		return x.AllowVoucher
	}
	return false
}

func (x *BookInfo) GetAllowMonthly() bool {
	if x != nil {
		return x.AllowMonthly
	}
	return false
}

func (x *BookInfo) GetContentType() string {
	if x != nil {
		return x.ContentType
	}
	return ""
}

func (x *BookInfo) GetSuperscript() string {
	if x != nil {
		return x.Superscript
	}
	return ""
}

func (x *BookInfo) GetBanned() int32 {
	if x != nil {
		return x.Banned
	}
	return 0
}

func (x *BookInfo) GetApptype() []int32 {
	if x != nil {
		return x.Apptype
	}
	return nil
}

func (x *BookInfo) GetHiddenPackage() []string {
	if x != nil {
		return x.HiddenPackage
	}
	return nil
}

func (x *BookInfo) GetBuyType() int64 {
	if x != nil {
		return x.BuyType
	}
	return 0
}

func (x *BookInfo) GetModifyTime() string {
	if x != nil {
		return x.ModifyTime
	}
	return ""
}

func (x *BookInfo) GetAllowFree() bool {
	if x != nil {
		return x.AllowFree
	}
	return false
}

func (x *BookInfo) GetIsFineBook() bool {
	if x != nil {
		return x.IsFineBook
	}
	return false
}

func (x *BookInfo) GetSizeType() int64 {
	if x != nil {
		return x.SizeType
	}
	return 0
}

type Rating struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Count         int64                  `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	Score         int64                  `protobuf:"varint,2,opt,name=score,proto3" json:"score,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Rating) Reset() {
	*x = Rating{}
	mi := &file_proto_pbcommon_common_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Rating) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Rating) ProtoMessage() {}

func (x *Rating) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pbcommon_common_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Rating.ProtoReflect.Descriptor instead.
func (*Rating) Descriptor() ([]byte, []int) {
	return file_proto_pbcommon_common_proto_rawDescGZIP(), []int{2}
}

func (x *Rating) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *Rating) GetScore() int64 {
	if x != nil {
		return x.Score
	}
	return 0
}

type Extra struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SourceCats    []string               `protobuf:"bytes,1,rep,name=sourceCats,proto3" json:"sourceCats,omitempty"`
	SmTags        []string               `protobuf:"bytes,2,rep,name=smTags,proto3" json:"smTags,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Extra) Reset() {
	*x = Extra{}
	mi := &file_proto_pbcommon_common_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Extra) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Extra) ProtoMessage() {}

func (x *Extra) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pbcommon_common_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Extra.ProtoReflect.Descriptor instead.
func (*Extra) Descriptor() ([]byte, []int) {
	return file_proto_pbcommon_common_proto_rawDescGZIP(), []int{3}
}

func (x *Extra) GetSourceCats() []string {
	if x != nil {
		return x.SourceCats
	}
	return nil
}

func (x *Extra) GetSmTags() []string {
	if x != nil {
		return x.SmTags
	}
	return nil
}

type CpSource struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Book          string                 `protobuf:"bytes,1,opt,name=book,proto3" json:"book,omitempty"`
	Source        string                 `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
	RemoteId      string                 `protobuf:"bytes,3,opt,name=remoteId,proto3" json:"remoteId,omitempty"`
	RemoteTitle   string                 `protobuf:"bytes,4,opt,name=remoteTitle,proto3" json:"remoteTitle,omitempty"`
	Updated       int64                  `protobuf:"varint,5,opt,name=updated,proto3" json:"updated,omitempty"`
	Created       int64                  `protobuf:"varint,6,opt,name=created,proto3" json:"created,omitempty"`
	Chid          string                 `protobuf:"bytes,7,opt,name=chid,proto3" json:"chid,omitempty"`
	ResourceType  string                 `protobuf:"bytes,8,opt,name=resourceType,proto3" json:"resourceType,omitempty"`
	IsLegally     bool                   `protobuf:"varint,9,opt,name=isLegally,proto3" json:"isLegally,omitempty"`
	AgreementType int32                  `protobuf:"varint,10,opt,name=agreementType,proto3" json:"agreementType,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CpSource) Reset() {
	*x = CpSource{}
	mi := &file_proto_pbcommon_common_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CpSource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CpSource) ProtoMessage() {}

func (x *CpSource) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pbcommon_common_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CpSource.ProtoReflect.Descriptor instead.
func (*CpSource) Descriptor() ([]byte, []int) {
	return file_proto_pbcommon_common_proto_rawDescGZIP(), []int{4}
}

func (x *CpSource) GetBook() string {
	if x != nil {
		return x.Book
	}
	return ""
}

func (x *CpSource) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *CpSource) GetRemoteId() string {
	if x != nil {
		return x.RemoteId
	}
	return ""
}

func (x *CpSource) GetRemoteTitle() string {
	if x != nil {
		return x.RemoteTitle
	}
	return ""
}

func (x *CpSource) GetUpdated() int64 {
	if x != nil {
		return x.Updated
	}
	return 0
}

func (x *CpSource) GetCreated() int64 {
	if x != nil {
		return x.Created
	}
	return 0
}

func (x *CpSource) GetChid() string {
	if x != nil {
		return x.Chid
	}
	return ""
}

func (x *CpSource) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *CpSource) GetIsLegally() bool {
	if x != nil {
		return x.IsLegally
	}
	return false
}

func (x *CpSource) GetAgreementType() int32 {
	if x != nil {
		return x.AgreementType
	}
	return 0
}

type Chapter struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	BookId        string                 `protobuf:"bytes,1,opt,name=bookId,proto3" json:"bookId,omitempty"`
	Cp            string                 `protobuf:"bytes,2,opt,name=cp,proto3" json:"cp,omitempty"`
	CpChapterId   string                 `protobuf:"bytes,3,opt,name=cpChapterId,proto3" json:"cpChapterId,omitempty"`
	Title         string                 `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	Order         int32                  `protobuf:"varint,5,opt,name=order,proto3" json:"order,omitempty"`
	ContentLength int32                  `protobuf:"varint,6,opt,name=contentLength,proto3" json:"contentLength,omitempty"` // txt 字数, 音视频 秒, picture 图片数
	IsVip         bool                   `protobuf:"varint,7,opt,name=isVip,proto3" json:"isVip,omitempty"`
	Created       string                 `protobuf:"bytes,8,opt,name=created,proto3" json:"created,omitempty"`
	Updated       string                 `protobuf:"bytes,9,opt,name=updated,proto3" json:"updated,omitempty"`
	PublishAt     string                 `protobuf:"bytes,10,opt,name=publishAt,proto3" json:"publishAt,omitempty"`
	Volume        string                 `protobuf:"bytes,11,opt,name=volume,proto3" json:"volume,omitempty"`
	Key           string                 `protobuf:"bytes,12,opt,name=key,proto3" json:"key,omitempty"`
	Content       string                 `protobuf:"bytes,13,opt,name=content,proto3" json:"content,omitempty"`
	Currency      int32                  `protobuf:"varint,14,opt,name=currency,proto3" json:"currency,omitempty"`
	Version       int32                  `protobuf:"varint,15,opt,name=version,proto3" json:"version,omitempty"`
	SourceFn      string                 `protobuf:"bytes,16,opt,name=sourceFn,proto3" json:"sourceFn,omitempty"`
	SourceUrl     string                 `protobuf:"bytes,17,opt,name=sourceUrl,proto3" json:"sourceUrl,omitempty"`
	IsBought      bool                   `protobuf:"varint,18,opt,name=isBought,proto3" json:"isBought,omitempty"`
	Bytes         int32                  `protobuf:"varint,19,opt,name=bytes,proto3" json:"bytes,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Chapter) Reset() {
	*x = Chapter{}
	mi := &file_proto_pbcommon_common_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Chapter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Chapter) ProtoMessage() {}

func (x *Chapter) ProtoReflect() protoreflect.Message {
	mi := &file_proto_pbcommon_common_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Chapter.ProtoReflect.Descriptor instead.
func (*Chapter) Descriptor() ([]byte, []int) {
	return file_proto_pbcommon_common_proto_rawDescGZIP(), []int{5}
}

func (x *Chapter) GetBookId() string {
	if x != nil {
		return x.BookId
	}
	return ""
}

func (x *Chapter) GetCp() string {
	if x != nil {
		return x.Cp
	}
	return ""
}

func (x *Chapter) GetCpChapterId() string {
	if x != nil {
		return x.CpChapterId
	}
	return ""
}

func (x *Chapter) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Chapter) GetOrder() int32 {
	if x != nil {
		return x.Order
	}
	return 0
}

func (x *Chapter) GetContentLength() int32 {
	if x != nil {
		return x.ContentLength
	}
	return 0
}

func (x *Chapter) GetIsVip() bool {
	if x != nil {
		return x.IsVip
	}
	return false
}

func (x *Chapter) GetCreated() string {
	if x != nil {
		return x.Created
	}
	return ""
}

func (x *Chapter) GetUpdated() string {
	if x != nil {
		return x.Updated
	}
	return ""
}

func (x *Chapter) GetPublishAt() string {
	if x != nil {
		return x.PublishAt
	}
	return ""
}

func (x *Chapter) GetVolume() string {
	if x != nil {
		return x.Volume
	}
	return ""
}

func (x *Chapter) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *Chapter) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *Chapter) GetCurrency() int32 {
	if x != nil {
		return x.Currency
	}
	return 0
}

func (x *Chapter) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *Chapter) GetSourceFn() string {
	if x != nil {
		return x.SourceFn
	}
	return ""
}

func (x *Chapter) GetSourceUrl() string {
	if x != nil {
		return x.SourceUrl
	}
	return ""
}

func (x *Chapter) GetIsBought() bool {
	if x != nil {
		return x.IsBought
	}
	return false
}

func (x *Chapter) GetBytes() int32 {
	if x != nil {
		return x.Bytes
	}
	return 0
}

var File_proto_pbcommon_common_proto protoreflect.FileDescriptor

const file_proto_pbcommon_common_proto_rawDesc = "" +
	"\n" +
	"\x1bproto/pbcommon/common.proto\x12\bpbcommon\"\xf9\x17\n" +
	"\x04Book\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12\x18\n" +
	"\aaliases\x18\x03 \x01(\tR\aaliases\x12\x16\n" +
	"\x06author\x18\x04 \x01(\tR\x06author\x12\x1a\n" +
	"\boriginal\x18\x05 \x01(\bR\boriginal\x12\x12\n" +
	"\x04site\x18\x06 \x01(\tR\x04site\x12\x14\n" +
	"\x05cover\x18\a \x01(\tR\x05cover\x12\x1e\n" +
	"\n" +
	"shortIntro\x18\b \x01(\tR\n" +
	"shortIntro\x12\x1c\n" +
	"\tlongIntro\x18\t \x01(\tR\tlongIntro\x12\x10\n" +
	"\x03cat\x18\n" +
	" \x01(\tR\x03cat\x12\x12\n" +
	"\x04tags\x18\v \x03(\tR\x04tags\x12\x1e\n" +
	"\n" +
	"categories\x18\f \x03(\tR\n" +
	"categories\x12\x16\n" +
	"\x06gender\x18\r \x03(\tR\x06gender\x12 \n" +
	"\vlastChapter\x18\x0e \x01(\tR\vlastChapter\x12\x1a\n" +
	"\bbookType\x18\x0f \x01(\tR\bbookType\x12$\n" +
	"\rchaptersCount\x18\x10 \x01(\x03R\rchaptersCount\x12\x1a\n" +
	"\bisSerial\x18\x11 \x01(\bR\bisSerial\x12\x18\n" +
	"\aupdated\x18\x12 \x01(\tR\aupdated\x12\x1a\n" +
	"\btocCount\x18\x13 \x01(\x03R\btocCount\x12\x1a\n" +
	"\bisCharge\x18\x14 \x01(\bR\bisCharge\x12&\n" +
	"\x0eretentionRatio\x18\x15 \x01(\x01R\x0eretentionRatio\x12,\n" +
	"\x11minRetentionRatio\x18\x16 \x01(\x01R\x11minRetentionRatio\x120\n" +
	"\x13monthRetentionRatio\x18\x17 \x01(\x01R\x13monthRetentionRatio\x12<\n" +
	"\x19nearlyMonthRetentionRatio\x18\x18 \x01(\x01R\x19nearlyMonthRetentionRatio\x120\n" +
	"\x13totalRetentionRatio\x18\x19 \x01(\x01R\x13totalRetentionRatio\x12\"\n" +
	"\ffollowerRank\x18\x1a \x01(\x03R\ffollowerRank\x12.\n" +
	"\x12retentionRatioRank\x18\x1b \x01(\x01R\x12retentionRatioRank\x12.\n" +
	"\x12serializeWordCount\x18\x1c \x01(\x03R\x12serializeWordCount\x12\x1c\n" +
	"\twordCount\x18\x1d \x01(\x03R\twordCount\x12\x18\n" +
	"\acreated\x18\x1e \x01(\x03R\acreated\x12$\n" +
	"\rfollowerCount\x18\x1f \x01(\x03R\rfollowerCount\x12.\n" +
	"\x12latelyFollowerBase\x18  \x01(\x03R\x12latelyFollowerBase\x12&\n" +
	"\x0elatelyFollower\x18! \x01(\x03R\x0elatelyFollower\x12,\n" +
	"\x11yesterdayFollower\x18\" \x01(\x03R\x11yesterdayFollower\x12$\n" +
	"\rmonthFollower\x18# \x01(\x03R\rmonthFollower\x120\n" +
	"\x13nearlyMonthFollower\x18$ \x01(\x03R\x13nearlyMonthFollower\x12$\n" +
	"\rtotalFollower\x18% \x01(\x03R\rtotalFollower\x12 \n" +
	"\vincFollower\x18& \x01(\x03R\vincFollower\x12\x1c\n" +
	"\tpostCount\x18' \x01(\x03R\tpostCount\x12 \n" +
	"\vreviewCount\x18( \x01(\x03R\vreviewCount\x12*\n" +
	"\x10shortReviewCount\x18) \x01(\x03R\x10shortReviewCount\x12\x1e\n" +
	"\n" +
	"totalPoint\x18* \x01(\x03R\n" +
	"totalPoint\x12\"\n" +
	"\fwritingPoint\x18+ \x01(\x03R\fwritingPoint\x12\x1e\n" +
	"\n" +
	"dramaPoint\x18, \x01(\x03R\n" +
	"dramaPoint\x12\x1e\n" +
	"\n" +
	"gradeCount\x18- \x01(\x03R\n" +
	"gradeCount\x12\x16\n" +
	"\x06banned\x18. \x01(\x05R\x06banned\x12&\n" +
	"\x0emanualUnbanned\x18/ \x01(\bR\x0emanualUnbanned\x12\x1c\n" +
	"\thasNotice\x180 \x01(\bR\thasNotice\x12\x1e\n" +
	"\n" +
	"tocUpdated\x181 \x01(\tR\n" +
	"tocUpdated\x12\x18\n" +
	"\aendTime\x182 \x01(\tR\aendTime\x12\x1c\n" +
	"\thasCmread\x183 \x01(\bR\thasCmread\x12\x1e\n" +
	"\n" +
	"thirdFlags\x184 \x03(\tR\n" +
	"thirdFlags\x12,\n" +
	"\x11thirdFlagsUpdated\x185 \x01(\tR\x11thirdFlagsUpdated\x12(\n" +
	"\x0freferenceSource\x186 \x01(\tR\x0freferenceSource\x12\x1c\n" +
	"\tuseSource\x187 \x01(\tR\tuseSource\x12\x1c\n" +
	"\tmajorCate\x188 \x01(\tR\tmajorCate\x12\x1c\n" +
	"\tminorCate\x189 \x01(\tR\tminorCate\x12%\n" +
	"\x05extra\x18: \x01(\v2\x0f.pbcommon.ExtraR\x05extra\x12\x14\n" +
	"\x05hasCp\x18; \x01(\bR\x05hasCp\x12\x16\n" +
	"\x06cpOnly\x18< \x01(\bR\x06cpOnly\x12*\n" +
	"\x10allowBeanVoucher\x18= \x01(\bR\x10allowBeanVoucher\x12\"\n" +
	"\fallowVoucher\x18> \x01(\bR\fallowVoucher\x12\"\n" +
	"\fallowMonthly\x18? \x01(\bR\fallowMonthly\x12\x0e\n" +
	"\x02le\x18@ \x01(\bR\x02le\x120\n" +
	"\x13allowAddOtherSource\x18A \x01(\bR\x13allowAddOtherSource\x12 \n" +
	"\vchargePoint\x18B \x01(\x03R\vchargePoint\x12 \n" +
	"\vcontentType\x18C \x01(\tR\vcontentType\x12\x1a\n" +
	"\bcurrency\x18D \x01(\x03R\bcurrency\x12 \n" +
	"\vsuperscript\x18E \x01(\tR\vsuperscript\x12\x1a\n" +
	"\bsizeType\x18F \x01(\x03R\bsizeType\x12\x18\n" +
	"\abuyType\x18G \x01(\x03R\abuyType\x12&\n" +
	"\x0erectangleCover\x18H \x01(\tR\x0erectangleCover\x12\x1a\n" +
	"\bbigCover\x18I \x01(\tR\bbigCover\x12&\n" +
	"\x0erecommendIntro\x18J \x01(\tR\x0erecommendIntro\x12\"\n" +
	"\fhasCopyright\x18K \x01(\bR\fhasCopyright\x12(\n" +
	"\x06rating\x18L \x01(\v2\x10.pbcommon.RatingR\x06rating\x12\x18\n" +
	"\aapptype\x18M \x03(\x03R\aapptype\x12$\n" +
	"\rhiddenPackage\x18N \x03(\tR\rhiddenPackage\x12\x1e\n" +
	"\n" +
	"exportMark\x18O \x01(\x03R\n" +
	"exportMark\x12 \n" +
	"\vmajorCateV2\x18P \x01(\tR\vmajorCateV2\x12 \n" +
	"\vminorCateV2\x18Q \x01(\tR\vminorCateV2\x12 \n" +
	"\vcategoryTag\x18R \x03(\tR\vcategoryTag\x12\x1e\n" +
	"\n" +
	"modifyTime\x18S \x01(\tR\n" +
	"modifyTime\x12\x1c\n" +
	"\tallowFree\x18T \x01(\bR\tallowFree\x12\x1e\n" +
	"\n" +
	"isFineBook\x18U \x01(\bR\n" +
	"isFineBook\x12$\n" +
	"\x03cps\x18V \x01(\v2\x12.pbcommon.CpSourceR\x03cps\x12\x1c\n" +
	"\tsafeLevel\x18W \x01(\x05R\tsafeLevel\x12\"\n" +
	"\flastChapter1\x18X \x01(\tR\flastChapter1\x12&\n" +
	"\x0echaptersCount1\x18Y \x01(\x05R\x0echaptersCount1\x12\x18\n" +
	"\aupdate1\x18Z \x01(\x03R\aupdate1\x12\"\n" +
	"\fcontentLevel\x18[ \x01(\x05R\fcontentLevel\"\x88\x05\n" +
	"\bBookInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x14\n" +
	"\x05title\x18\x02 \x01(\tR\x05title\x12\x16\n" +
	"\x06author\x18\x03 \x01(\tR\x06author\x12\x14\n" +
	"\x05cover\x18\x04 \x01(\tR\x05cover\x12 \n" +
	"\vlastChapter\x18\x05 \x01(\tR\vlastChapter\x12\x18\n" +
	"\aupdated\x18\x06 \x01(\tR\aupdated\x12$\n" +
	"\rChaptersCount\x18\a \x01(\x03R\rChaptersCount\x12(\n" +
	"\x0freferenceSource\x18\b \x01(\tR\x0freferenceSource\x12\x14\n" +
	"\x05hasCp\x18\t \x01(\bR\x05hasCp\x12\x0e\n" +
	"\x02le\x18\n" +
	" \x01(\bR\x02le\x12\"\n" +
	"\fallowVoucher\x18\v \x01(\bR\fallowVoucher\x12\"\n" +
	"\fallowMonthly\x18\f \x01(\bR\fallowMonthly\x12 \n" +
	"\vcontentType\x18\r \x01(\tR\vcontentType\x12 \n" +
	"\vsuperscript\x18\x0e \x01(\tR\vsuperscript\x12\x16\n" +
	"\x06Banned\x18\x0f \x01(\x05R\x06Banned\x12\x18\n" +
	"\aapptype\x18\x10 \x03(\x05R\aapptype\x12$\n" +
	"\rhiddenPackage\x18\x11 \x03(\tR\rhiddenPackage\x12\x18\n" +
	"\abuyType\x18\x12 \x01(\x03R\abuyType\x12\x1e\n" +
	"\n" +
	"modifyTime\x18\x13 \x01(\tR\n" +
	"modifyTime\x12\x1c\n" +
	"\tallowFree\x18\x14 \x01(\bR\tallowFree\x12\x1e\n" +
	"\n" +
	"IsFineBook\x18\x15 \x01(\bR\n" +
	"IsFineBook\x12\x1a\n" +
	"\bsizeType\x18\x16 \x01(\x03R\bsizeType\"4\n" +
	"\x06Rating\x12\x14\n" +
	"\x05count\x18\x01 \x01(\x03R\x05count\x12\x14\n" +
	"\x05score\x18\x02 \x01(\x03R\x05score\"?\n" +
	"\x05Extra\x12\x1e\n" +
	"\n" +
	"sourceCats\x18\x01 \x03(\tR\n" +
	"sourceCats\x12\x16\n" +
	"\x06smTags\x18\x02 \x03(\tR\x06smTags\"\xa4\x02\n" +
	"\bCpSource\x12\x12\n" +
	"\x04book\x18\x01 \x01(\tR\x04book\x12\x16\n" +
	"\x06source\x18\x02 \x01(\tR\x06source\x12\x1a\n" +
	"\bremoteId\x18\x03 \x01(\tR\bremoteId\x12 \n" +
	"\vremoteTitle\x18\x04 \x01(\tR\vremoteTitle\x12\x18\n" +
	"\aupdated\x18\x05 \x01(\x03R\aupdated\x12\x18\n" +
	"\acreated\x18\x06 \x01(\x03R\acreated\x12\x12\n" +
	"\x04chid\x18\a \x01(\tR\x04chid\x12\"\n" +
	"\fresourceType\x18\b \x01(\tR\fresourceType\x12\x1c\n" +
	"\tisLegally\x18\t \x01(\bR\tisLegally\x12$\n" +
	"\ragreementType\x18\n" +
	" \x01(\x05R\ragreementType\"\xf3\x03\n" +
	"\achapter\x12\x16\n" +
	"\x06bookId\x18\x01 \x01(\tR\x06bookId\x12\x0e\n" +
	"\x02cp\x18\x02 \x01(\tR\x02cp\x12 \n" +
	"\vcpChapterId\x18\x03 \x01(\tR\vcpChapterId\x12\x14\n" +
	"\x05title\x18\x04 \x01(\tR\x05title\x12\x14\n" +
	"\x05order\x18\x05 \x01(\x05R\x05order\x12$\n" +
	"\rcontentLength\x18\x06 \x01(\x05R\rcontentLength\x12\x14\n" +
	"\x05isVip\x18\a \x01(\bR\x05isVip\x12\x18\n" +
	"\acreated\x18\b \x01(\tR\acreated\x12\x18\n" +
	"\aupdated\x18\t \x01(\tR\aupdated\x12\x1c\n" +
	"\tpublishAt\x18\n" +
	" \x01(\tR\tpublishAt\x12\x16\n" +
	"\x06volume\x18\v \x01(\tR\x06volume\x12\x10\n" +
	"\x03key\x18\f \x01(\tR\x03key\x12\x18\n" +
	"\acontent\x18\r \x01(\tR\acontent\x12\x1a\n" +
	"\bcurrency\x18\x0e \x01(\x05R\bcurrency\x12\x18\n" +
	"\aversion\x18\x0f \x01(\x05R\aversion\x12\x1a\n" +
	"\bsourceFn\x18\x10 \x01(\tR\bsourceFn\x12\x1c\n" +
	"\tsourceUrl\x18\x11 \x01(\tR\tsourceUrl\x12\x1a\n" +
	"\bisBought\x18\x12 \x01(\bR\bisBought\x12\x14\n" +
	"\x05bytes\x18\x13 \x01(\x05R\x05bytesB1Z/creativematrix.com/beyondreading/proto/pbcommonb\x06proto3"

var (
	file_proto_pbcommon_common_proto_rawDescOnce sync.Once
	file_proto_pbcommon_common_proto_rawDescData []byte
)

func file_proto_pbcommon_common_proto_rawDescGZIP() []byte {
	file_proto_pbcommon_common_proto_rawDescOnce.Do(func() {
		file_proto_pbcommon_common_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_pbcommon_common_proto_rawDesc), len(file_proto_pbcommon_common_proto_rawDesc)))
	})
	return file_proto_pbcommon_common_proto_rawDescData
}

var file_proto_pbcommon_common_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_proto_pbcommon_common_proto_goTypes = []any{
	(*Book)(nil),     // 0: pbcommon.Book
	(*BookInfo)(nil), // 1: pbcommon.BookInfo
	(*Rating)(nil),   // 2: pbcommon.Rating
	(*Extra)(nil),    // 3: pbcommon.Extra
	(*CpSource)(nil), // 4: pbcommon.CpSource
	(*Chapter)(nil),  // 5: pbcommon.chapter
}
var file_proto_pbcommon_common_proto_depIdxs = []int32{
	3, // 0: pbcommon.Book.extra:type_name -> pbcommon.Extra
	2, // 1: pbcommon.Book.rating:type_name -> pbcommon.Rating
	4, // 2: pbcommon.Book.cps:type_name -> pbcommon.CpSource
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_proto_pbcommon_common_proto_init() }
func file_proto_pbcommon_common_proto_init() {
	if File_proto_pbcommon_common_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_pbcommon_common_proto_rawDesc), len(file_proto_pbcommon_common_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_proto_pbcommon_common_proto_goTypes,
		DependencyIndexes: file_proto_pbcommon_common_proto_depIdxs,
		MessageInfos:      file_proto_pbcommon_common_proto_msgTypes,
	}.Build()
	File_proto_pbcommon_common_proto = out.File
	file_proto_pbcommon_common_proto_goTypes = nil
	file_proto_pbcommon_common_proto_depIdxs = nil
}
