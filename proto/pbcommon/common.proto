syntax = "proto3";
package pbcommon;
option go_package = "creativematrix.com/beyondreading/proto/pbcommon";


message Book {
  string id = 1;
  string title = 2;
  string aliases = 3;
  string author = 4;
  bool original = 5;
  string site = 6;
  string cover = 7;
  string shortIntro = 8;
  string longIntro = 9;
  string cat = 10;
  repeated string tags = 11;
  repeated string categories = 12;
  repeated string gender = 13;
  string lastChapter = 14;
  string bookType = 15;
  int64 chaptersCount = 16;
  bool isSerial = 17;
  string updated = 18;
  int64 tocCount = 19;
  bool isCharge = 20;
  double retentionRatio = 21;
  double minRetentionRatio = 22;
  double monthRetentionRatio = 23;
  double nearlyMonthRetentionRatio = 24;
  double totalRetentionRatio = 25;
  int64 followerRank = 26;
  double retentionRatioRank = 27;
  int64 serializeWordCount = 28;
  int64 wordCount = 29;
  int64 created = 30;
  int64 followerCount = 31;
  int64 latelyFollowerBase = 32;
  int64 latelyFollower = 33;
  int64 yesterdayFollower = 34;
  int64 monthFollower = 35;
  int64 nearlyMonthFollower = 36;
  int64 totalFollower = 37;
  int64 incFollower = 38;
  int64 postCount = 39;
  int64 reviewCount = 40;
  int64 shortReviewCount = 41;
  int64 totalPoint = 42;
  int64 writingPoint = 43;
  int64 dramaPoint = 44;
  int64 gradeCount = 45;
  int32 banned = 46;
  bool manualUnbanned = 47;
  bool hasNotice = 48;
  string tocUpdated = 49;
  string endTime = 50;
  bool hasCmread = 51;
  repeated string thirdFlags = 52;
  string thirdFlagsUpdated = 53;
  string referenceSource = 54;
  string useSource = 55;
  string majorCate = 56;
  string minorCate = 57;
  Extra extra = 58;
  bool hasCp = 59;
  bool cpOnly = 60;
  bool allowBeanVoucher = 61;
  bool allowVoucher = 62;
  bool allowMonthly = 63;
  bool le = 64;
  bool allowAddOtherSource = 65;
  int64 chargePoint = 66;
  string contentType = 67;
  int64 currency = 68;
  string superscript = 69;
  int64 sizeType = 70;
  int64 buyType = 71;
  string rectangleCover = 72;
  string bigCover = 73;
  string recommendIntro = 74;
  bool hasCopyright = 75;
  Rating rating = 76;
  repeated int64 apptype = 77;
  repeated string hiddenPackage = 78;
  int64 exportMark = 79;
  string majorCateV2 = 80;
  string minorCateV2 = 81;
  repeated string categoryTag = 82;
  string modifyTime = 83;
  bool allowFree = 84;
  // 是否为精品书
  bool isFineBook = 85;
  CpSource cps = 86;
  int32 safeLevel = 87; // 安全等级 0->无风险 10->暧昧 30->轻微涉黄 50->轻微涉军/涉政 70->严重涉黄/涉政 (书库定义)
  string lastChapter1 = 88; //阅文停更章节名
  int32 chaptersCount1 =89; //阅文停更章节数
  int64 update1 =90; //阅文停更时间
  int32 contentLevel =  91;// 内容分级 a:0 b:1 c:2 s:3 e:-1
}

message BookInfo{
  string id = 1;
  string title  =2;
  string author  =3;
  string cover =4;
  string lastChapter = 5;
  string updated = 6;
  int64 ChaptersCount =7;
  string referenceSource =8;
  bool hasCp       =9;
  bool le    =10;
  bool allowVoucher = 11;
  bool allowMonthly     =12;
  string contentType      =13;
  string superscript  =14;
  int32 Banned =15;
  repeated int32 apptype    =16;
  repeated string hiddenPackage  =17;
  int64 buyType   =18;
  string modifyTime   =19;
  bool allowFree =20;
  bool IsFineBook  =21;
  int64 sizeType =22;
}

message Rating {
  int64 count = 1;
  int64 score = 2;
}

message Extra {
  repeated string sourceCats = 1;
  repeated string smTags = 2;
}

message CpSource {
  string book = 1;
  string source = 2;
  string remoteId = 3;
  string remoteTitle = 4;
  int64 updated = 5;
  int64 created = 6;
  string chid = 7;
  string resourceType = 8;
  bool isLegally = 9;
  int32 agreementType = 10;
}

message chapter {
  string bookId = 1;
  string cp = 2;
  string cpChapterId = 3;
  string title = 4;
  int32 order = 5;
  int32 contentLength = 6; // txt 字数, 音视频 秒, picture 图片数
  bool isVip = 7;
  string created = 8;
  string updated = 9;
  string publishAt = 10;
  string volume = 11;
  string key = 12;
  string content = 13;
  int32 currency = 14;
  int32 version = 15;
  string sourceFn = 16;
  string sourceUrl = 17;
  bool isBought = 18;
  int32 bytes = 19;
}