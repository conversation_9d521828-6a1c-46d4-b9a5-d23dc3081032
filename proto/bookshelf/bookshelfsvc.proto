syntax = "proto3";
package bookshelf;
option go_package = "creativematrix.com/beyondreading/proto/bookshelf";

//import "google/api/annotations.proto";

service Bookshelf {
  rpc GetBookshelf(BookshelfRequest) returns (BookshelfListResponse) {}
  rpc ListBookShelf(ListBookShelfRequest) returns(ListBookShelfResponse){}
  rpc AddBookshelf(AddBookshelfRequest) returns (NormalResponse) {}
  rpc UpdateBookshelf(UpdateBookshelfRequest) returns (NormalResponse) {}
  rpc DelBookshelf(BookshelfRequest) returns (NormalResponse) {}
  rpc UpdateReadRecord(UpdateReadRecordRequest) returns (NormalResponse) {}
  rpc UpdateReadRecords(UpdateReadRecordsRequest) returns (NormalResponse) {}
  rpc FilterBooksInMongo(FilterBooksRequest) returns (FilterBooksResponse) {}
  rpc AddUserShelfRead(AddUserReadRecordRequest) returns (NormalResponse) {}
  rpc GetUserShelfReadsByUserId(GetUserShelfReadListRequest) returns (GetUserReadRecordListResponse) {}
  rpc GetUserShelfReadByUserIdAndBook(GetUserShelfReadRequest) returns (GetUserShelfReadResponse) {}
  rpc UpdateUserShelfRead(UpdateUserShelfReadRequest) returns (NormalResponse) {}

  rpc UpdateUserRecord(UpdateReadRecordsRequest) returns (NormalResponse) {}
  rpc UpdateMWebRecord(UpdateMWebReadRecordsRequest) returns (NormalResponse) {}

  rpc GetBookshelfInfo(UserIdAndBookIdsAndAppName) returns (UserShelfBooksResponse) {}

  rpc RemoveBooks4Feeding(UserIdAndBookIds) returns (TimeResponse) {}
  rpc RemoveBooks4FeedingV2(UserIdAndBookIdsAndAppName) returns (TimeResponse) {}
  rpc RemoveBooks4FeedingV3(UserIdAndBookIdsAndAppName) returns (TimeResponse) {}

  rpc RemoveBooks4Shelf(UserIdAndBookIds) returns (TimeResponse) {}
  rpc RemoveBooks4ShelfV2(UserIdAndBookIdsAndAppName) returns (TimeResponse) {}
  rpc RemoveBooks4ShelfV3(UserIdAndBookIdsAndAppName) returns (TimeResponse) {}

  rpc AddBook2Feeding(UserIdAndBookIds) returns (TimeResponse) {}
  rpc AddBook2FeedingV2(UserIdAndBookIdsAndAppName) returns (TimeResponse) {}
  rpc AddBook2FeedingV3(UserIdAndBookIdsAndAppName) returns (TimeResponse) {}

  rpc AddBook2Shelf(UserIdAndBookIdsAndType) returns (TimeResponse) {}
  rpc AddBook2ShelfV2(UserIdAndBookIdsAndAppName) returns (TimeResponse) {}
  rpc AddBook2ShelfV3(UserIdAndBookIdsAndAppName) returns (TimeResponse) {}

  rpc GetBookShelf(UserId) returns (GetBookShelfResponse) {}
  rpc GetBookShelfV2(UserIdAndAppName) returns (GetBookShelfResponse) {}
  rpc GetBookShelfV3(UserIdAndAppName) returns (GetBookShelfV3Response) {}
}

message BookshelfRequest {
  string appName = 1;
  string userId = 2;
  repeated string bookIds = 3;
}

message AddBookshelfRequest {
  string appName = 1;
  repeated BookshelfModel bookshelves = 2;
}

message UpdateBookshelfRequest {
  string appName = 1;
  string userId = 2;
  repeated string bookIds = 3;
  repeated string updateKeys = 4;
  int32 area = 5;
  bool monthly = 6;
}

message NormalResponse {
  string err = 1;
  string message = 2;
}

message ReadRecordModel {
  string tocId = 1;
  string tocName = 2;
  string title = 3;
  int32 order = 4;
  int32 wordIndex = 5;
}
message ReadRecordModelWithBookID {
  string tocId = 1;
  string tocName = 2;
  string title = 3;
  int32 order = 4;
  int32 wordIndex = 5;
  string book = 6;
}
message ReadRecordModelWithBookAndUpdate {
  string tocId = 1;
  string tocName = 2;
  string title = 3;
  int32 order = 4;
  int32 wordIndex = 5;
  int64 update = 6;
  string book = 7;
}
message BookshelfModel {
  string id = 1;
  string user = 2;
  string book = 3;
  int32 area = 4;
  bool monthly = 5;
  int64 created = 6;
  ReadRecordModel readRecord = 7;
  int64 recordUpdated = 8;
  string appName = 9;
  bool allowFree = 10;
  int32 superscript = 11;
}

message UpdateReadRecordRequest {
  string appName = 1;
  string userId = 2;
  string bookId = 3;
  ReadRecordModel readRecord = 4;
}

message UpdateReadRecordsRequest {
  string appName = 1;
  string userId = 2;
  repeated ReadRecordModelWithBookID readRecords = 4;
}

message UpdateMWebReadRecordsRequest {
  string appName = 1;
  string userId = 2;
  repeated ReadRecordModelWithBookID readRecords = 4;
  string from = 5;
}

message BookshelfListResponse {
  string err = 1;
  repeated BookshelfModel bookshelves = 2;
}

message FilterBooksRequest {
  string userId = 1;
  repeated string bookIds = 2;
  string appName = 3;
  int32 version = 4;
}
message FilterBooksResponse {
  string err = 1;
  repeated string addBooks = 2;
  repeated string existBooks = 3;
}

message UpdateUserRecordReq {
  string userId = 1;
  string appName = 2;
  ReadRecordModelWithBookID readRecords = 3;
}

message AddUserReadRecordRequest {
  string userId = 1;
  repeated string bookIds = 2;
  int64 created = 3;
}

message GetUserShelfReadRequest {
  string userId = 1;
  string bookId = 2;
}

message GetUserShelfReadListRequest {
  string userId = 1;
  int64 start = 2;
  int64 limit = 3;
}

message UserShelfReadModel {
  string user = 1;
  string book = 2;
  ReadRecordModel readRecord = 3;
  int64 recordUpdated = 4;
  int64 created = 5;
}

message GetUserReadRecordListResponse {
  string err = 1;
  repeated UserShelfReadModel recoredList = 2;
}
message GetUserShelfReadResponse {
  string err = 1;
  UserShelfReadModel record = 2;
}

message UpdateUserShelfReadRequest {
  string user = 1;
  string book = 2;
  ReadRecordModel readRecord = 3;
  bool upsert = 4;
}

message UserShelfBook {
  string _id = 1;
  string author = 2;
  string cover = 3;
  string title = 4;
  int64 buytype = 5;
  bool allowMonthly = 6;
  bool allowVoucher = 7;
  bool hasCp = 8;
  string referenceSource = 9;
  int64 update = 10;
  int64 chaptersCount = 11;
  string lastChapter = 12;
  int64 created = 13;
  bool _le = 14;
  string contentType = 15;
  string superscript = 16;
  int64 sizeType = 17;
  bool _mm = 18;
  ReadRecordModelWithBookAndUpdate readRecord = 19;
  bool advertRead = 20;
  bool _gg = 21;
  bool _ss = 22;
  int64 modifyTime = 23;
  bool _ff = 24; //是否允许免费看，默认false
  int32 subscript = 25;
}

message UserShelfBooksResponse {
  string Err = 1;
  repeated UserShelfBook books = 2;
}

message UserId {
  string user = 1;
}

message BookShelfV3 {
  string id = 1;
  int64 updated = 2;
  int64 recordUpdated = 3;
  int32 type = 4;
  int64 modifyTime = 5;
}

message UserIdAndAppName {
  string appName = 1;
  string userId = 2;
}
message UserIdAndBookIds {
  string userId = 1;
  repeated string bookIds = 2;
}
message UserIdAndBookIdsAndType {
  string userId = 1;
  repeated string bookIds = 2;
  string type = 3;
}
message UserIdAndBookIdsAndAppName {
  string appName = 1;
  string userId = 2;
  repeated string bookIds = 3;
}
message TimeResponse {
  string Err = 1;
  int64 time = 2;
}

message GetBookShelfResponse {
  string Err = 1;
  repeated UserShelfBook shelfBooks = 2;
  repeated UserShelfBook feedingBooks = 3;
}

message GetBookShelfV3Response {
  string Err = 1;
  repeated BookShelfV3 shelfBooks = 2;
}
/*
  批量获取用户书架
*/
message ListBookShelfRequest{
  string app_name = 1;
  //最大长度支持100
  repeated string user_id = 2;
}

message ListBookShelfResponse{
  map<string,UserBookShelf> user_shelves= 1;
}

message UserBookShelf{
  repeated BookshelfModel bookshelves = 1;
}