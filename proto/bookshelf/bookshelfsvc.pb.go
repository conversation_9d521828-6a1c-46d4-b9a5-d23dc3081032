// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: proto/bookshelf/bookshelfsvc.proto

package bookshelf

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BookshelfRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AppName       string                 `protobuf:"bytes,1,opt,name=appName,proto3" json:"appName,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId,omitempty"`
	BookIds       []string               `protobuf:"bytes,3,rep,name=bookIds,proto3" json:"bookIds,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BookshelfRequest) Reset() {
	*x = BookshelfRequest{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BookshelfRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookshelfRequest) ProtoMessage() {}

func (x *BookshelfRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookshelfRequest.ProtoReflect.Descriptor instead.
func (*BookshelfRequest) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{0}
}

func (x *BookshelfRequest) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *BookshelfRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *BookshelfRequest) GetBookIds() []string {
	if x != nil {
		return x.BookIds
	}
	return nil
}

type AddBookshelfRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AppName       string                 `protobuf:"bytes,1,opt,name=appName,proto3" json:"appName,omitempty"`
	Bookshelves   []*BookshelfModel      `protobuf:"bytes,2,rep,name=bookshelves,proto3" json:"bookshelves,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddBookshelfRequest) Reset() {
	*x = AddBookshelfRequest{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddBookshelfRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddBookshelfRequest) ProtoMessage() {}

func (x *AddBookshelfRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddBookshelfRequest.ProtoReflect.Descriptor instead.
func (*AddBookshelfRequest) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{1}
}

func (x *AddBookshelfRequest) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *AddBookshelfRequest) GetBookshelves() []*BookshelfModel {
	if x != nil {
		return x.Bookshelves
	}
	return nil
}

type UpdateBookshelfRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AppName       string                 `protobuf:"bytes,1,opt,name=appName,proto3" json:"appName,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId,omitempty"`
	BookIds       []string               `protobuf:"bytes,3,rep,name=bookIds,proto3" json:"bookIds,omitempty"`
	UpdateKeys    []string               `protobuf:"bytes,4,rep,name=updateKeys,proto3" json:"updateKeys,omitempty"`
	Area          int32                  `protobuf:"varint,5,opt,name=area,proto3" json:"area,omitempty"`
	Monthly       bool                   `protobuf:"varint,6,opt,name=monthly,proto3" json:"monthly,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateBookshelfRequest) Reset() {
	*x = UpdateBookshelfRequest{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateBookshelfRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBookshelfRequest) ProtoMessage() {}

func (x *UpdateBookshelfRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBookshelfRequest.ProtoReflect.Descriptor instead.
func (*UpdateBookshelfRequest) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateBookshelfRequest) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *UpdateBookshelfRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UpdateBookshelfRequest) GetBookIds() []string {
	if x != nil {
		return x.BookIds
	}
	return nil
}

func (x *UpdateBookshelfRequest) GetUpdateKeys() []string {
	if x != nil {
		return x.UpdateKeys
	}
	return nil
}

func (x *UpdateBookshelfRequest) GetArea() int32 {
	if x != nil {
		return x.Area
	}
	return 0
}

func (x *UpdateBookshelfRequest) GetMonthly() bool {
	if x != nil {
		return x.Monthly
	}
	return false
}

type NormalResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Err           string                 `protobuf:"bytes,1,opt,name=err,proto3" json:"err,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NormalResponse) Reset() {
	*x = NormalResponse{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NormalResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NormalResponse) ProtoMessage() {}

func (x *NormalResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NormalResponse.ProtoReflect.Descriptor instead.
func (*NormalResponse) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{3}
}

func (x *NormalResponse) GetErr() string {
	if x != nil {
		return x.Err
	}
	return ""
}

func (x *NormalResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ReadRecordModel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TocId         string                 `protobuf:"bytes,1,opt,name=tocId,proto3" json:"tocId,omitempty"`
	TocName       string                 `protobuf:"bytes,2,opt,name=tocName,proto3" json:"tocName,omitempty"`
	Title         string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Order         int32                  `protobuf:"varint,4,opt,name=order,proto3" json:"order,omitempty"`
	WordIndex     int32                  `protobuf:"varint,5,opt,name=wordIndex,proto3" json:"wordIndex,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReadRecordModel) Reset() {
	*x = ReadRecordModel{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReadRecordModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadRecordModel) ProtoMessage() {}

func (x *ReadRecordModel) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadRecordModel.ProtoReflect.Descriptor instead.
func (*ReadRecordModel) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{4}
}

func (x *ReadRecordModel) GetTocId() string {
	if x != nil {
		return x.TocId
	}
	return ""
}

func (x *ReadRecordModel) GetTocName() string {
	if x != nil {
		return x.TocName
	}
	return ""
}

func (x *ReadRecordModel) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ReadRecordModel) GetOrder() int32 {
	if x != nil {
		return x.Order
	}
	return 0
}

func (x *ReadRecordModel) GetWordIndex() int32 {
	if x != nil {
		return x.WordIndex
	}
	return 0
}

type ReadRecordModelWithBookID struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TocId         string                 `protobuf:"bytes,1,opt,name=tocId,proto3" json:"tocId,omitempty"`
	TocName       string                 `protobuf:"bytes,2,opt,name=tocName,proto3" json:"tocName,omitempty"`
	Title         string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Order         int32                  `protobuf:"varint,4,opt,name=order,proto3" json:"order,omitempty"`
	WordIndex     int32                  `protobuf:"varint,5,opt,name=wordIndex,proto3" json:"wordIndex,omitempty"`
	Book          string                 `protobuf:"bytes,6,opt,name=book,proto3" json:"book,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReadRecordModelWithBookID) Reset() {
	*x = ReadRecordModelWithBookID{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReadRecordModelWithBookID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadRecordModelWithBookID) ProtoMessage() {}

func (x *ReadRecordModelWithBookID) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadRecordModelWithBookID.ProtoReflect.Descriptor instead.
func (*ReadRecordModelWithBookID) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{5}
}

func (x *ReadRecordModelWithBookID) GetTocId() string {
	if x != nil {
		return x.TocId
	}
	return ""
}

func (x *ReadRecordModelWithBookID) GetTocName() string {
	if x != nil {
		return x.TocName
	}
	return ""
}

func (x *ReadRecordModelWithBookID) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ReadRecordModelWithBookID) GetOrder() int32 {
	if x != nil {
		return x.Order
	}
	return 0
}

func (x *ReadRecordModelWithBookID) GetWordIndex() int32 {
	if x != nil {
		return x.WordIndex
	}
	return 0
}

func (x *ReadRecordModelWithBookID) GetBook() string {
	if x != nil {
		return x.Book
	}
	return ""
}

type ReadRecordModelWithBookAndUpdate struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TocId         string                 `protobuf:"bytes,1,opt,name=tocId,proto3" json:"tocId,omitempty"`
	TocName       string                 `protobuf:"bytes,2,opt,name=tocName,proto3" json:"tocName,omitempty"`
	Title         string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty"`
	Order         int32                  `protobuf:"varint,4,opt,name=order,proto3" json:"order,omitempty"`
	WordIndex     int32                  `protobuf:"varint,5,opt,name=wordIndex,proto3" json:"wordIndex,omitempty"`
	Update        int64                  `protobuf:"varint,6,opt,name=update,proto3" json:"update,omitempty"`
	Book          string                 `protobuf:"bytes,7,opt,name=book,proto3" json:"book,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReadRecordModelWithBookAndUpdate) Reset() {
	*x = ReadRecordModelWithBookAndUpdate{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReadRecordModelWithBookAndUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadRecordModelWithBookAndUpdate) ProtoMessage() {}

func (x *ReadRecordModelWithBookAndUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadRecordModelWithBookAndUpdate.ProtoReflect.Descriptor instead.
func (*ReadRecordModelWithBookAndUpdate) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{6}
}

func (x *ReadRecordModelWithBookAndUpdate) GetTocId() string {
	if x != nil {
		return x.TocId
	}
	return ""
}

func (x *ReadRecordModelWithBookAndUpdate) GetTocName() string {
	if x != nil {
		return x.TocName
	}
	return ""
}

func (x *ReadRecordModelWithBookAndUpdate) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *ReadRecordModelWithBookAndUpdate) GetOrder() int32 {
	if x != nil {
		return x.Order
	}
	return 0
}

func (x *ReadRecordModelWithBookAndUpdate) GetWordIndex() int32 {
	if x != nil {
		return x.WordIndex
	}
	return 0
}

func (x *ReadRecordModelWithBookAndUpdate) GetUpdate() int64 {
	if x != nil {
		return x.Update
	}
	return 0
}

func (x *ReadRecordModelWithBookAndUpdate) GetBook() string {
	if x != nil {
		return x.Book
	}
	return ""
}

type BookshelfModel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	User          string                 `protobuf:"bytes,2,opt,name=user,proto3" json:"user,omitempty"`
	Book          string                 `protobuf:"bytes,3,opt,name=book,proto3" json:"book,omitempty"`
	Area          int32                  `protobuf:"varint,4,opt,name=area,proto3" json:"area,omitempty"`
	Monthly       bool                   `protobuf:"varint,5,opt,name=monthly,proto3" json:"monthly,omitempty"`
	Created       int64                  `protobuf:"varint,6,opt,name=created,proto3" json:"created,omitempty"`
	ReadRecord    *ReadRecordModel       `protobuf:"bytes,7,opt,name=readRecord,proto3" json:"readRecord,omitempty"`
	RecordUpdated int64                  `protobuf:"varint,8,opt,name=recordUpdated,proto3" json:"recordUpdated,omitempty"`
	AppName       string                 `protobuf:"bytes,9,opt,name=appName,proto3" json:"appName,omitempty"`
	AllowFree     bool                   `protobuf:"varint,10,opt,name=allowFree,proto3" json:"allowFree,omitempty"`
	Superscript   int32                  `protobuf:"varint,11,opt,name=superscript,proto3" json:"superscript,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BookshelfModel) Reset() {
	*x = BookshelfModel{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BookshelfModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookshelfModel) ProtoMessage() {}

func (x *BookshelfModel) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookshelfModel.ProtoReflect.Descriptor instead.
func (*BookshelfModel) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{7}
}

func (x *BookshelfModel) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BookshelfModel) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *BookshelfModel) GetBook() string {
	if x != nil {
		return x.Book
	}
	return ""
}

func (x *BookshelfModel) GetArea() int32 {
	if x != nil {
		return x.Area
	}
	return 0
}

func (x *BookshelfModel) GetMonthly() bool {
	if x != nil {
		return x.Monthly
	}
	return false
}

func (x *BookshelfModel) GetCreated() int64 {
	if x != nil {
		return x.Created
	}
	return 0
}

func (x *BookshelfModel) GetReadRecord() *ReadRecordModel {
	if x != nil {
		return x.ReadRecord
	}
	return nil
}

func (x *BookshelfModel) GetRecordUpdated() int64 {
	if x != nil {
		return x.RecordUpdated
	}
	return 0
}

func (x *BookshelfModel) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *BookshelfModel) GetAllowFree() bool {
	if x != nil {
		return x.AllowFree
	}
	return false
}

func (x *BookshelfModel) GetSuperscript() int32 {
	if x != nil {
		return x.Superscript
	}
	return 0
}

type UpdateReadRecordRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AppName       string                 `protobuf:"bytes,1,opt,name=appName,proto3" json:"appName,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId,omitempty"`
	BookId        string                 `protobuf:"bytes,3,opt,name=bookId,proto3" json:"bookId,omitempty"`
	ReadRecord    *ReadRecordModel       `protobuf:"bytes,4,opt,name=readRecord,proto3" json:"readRecord,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateReadRecordRequest) Reset() {
	*x = UpdateReadRecordRequest{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateReadRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateReadRecordRequest) ProtoMessage() {}

func (x *UpdateReadRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateReadRecordRequest.ProtoReflect.Descriptor instead.
func (*UpdateReadRecordRequest) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateReadRecordRequest) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *UpdateReadRecordRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UpdateReadRecordRequest) GetBookId() string {
	if x != nil {
		return x.BookId
	}
	return ""
}

func (x *UpdateReadRecordRequest) GetReadRecord() *ReadRecordModel {
	if x != nil {
		return x.ReadRecord
	}
	return nil
}

type UpdateReadRecordsRequest struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	AppName       string                       `protobuf:"bytes,1,opt,name=appName,proto3" json:"appName,omitempty"`
	UserId        string                       `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId,omitempty"`
	ReadRecords   []*ReadRecordModelWithBookID `protobuf:"bytes,4,rep,name=readRecords,proto3" json:"readRecords,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateReadRecordsRequest) Reset() {
	*x = UpdateReadRecordsRequest{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateReadRecordsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateReadRecordsRequest) ProtoMessage() {}

func (x *UpdateReadRecordsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateReadRecordsRequest.ProtoReflect.Descriptor instead.
func (*UpdateReadRecordsRequest) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateReadRecordsRequest) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *UpdateReadRecordsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UpdateReadRecordsRequest) GetReadRecords() []*ReadRecordModelWithBookID {
	if x != nil {
		return x.ReadRecords
	}
	return nil
}

type UpdateMWebReadRecordsRequest struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	AppName       string                       `protobuf:"bytes,1,opt,name=appName,proto3" json:"appName,omitempty"`
	UserId        string                       `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId,omitempty"`
	ReadRecords   []*ReadRecordModelWithBookID `protobuf:"bytes,4,rep,name=readRecords,proto3" json:"readRecords,omitempty"`
	From          string                       `protobuf:"bytes,5,opt,name=from,proto3" json:"from,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateMWebReadRecordsRequest) Reset() {
	*x = UpdateMWebReadRecordsRequest{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateMWebReadRecordsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMWebReadRecordsRequest) ProtoMessage() {}

func (x *UpdateMWebReadRecordsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMWebReadRecordsRequest.ProtoReflect.Descriptor instead.
func (*UpdateMWebReadRecordsRequest) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateMWebReadRecordsRequest) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *UpdateMWebReadRecordsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UpdateMWebReadRecordsRequest) GetReadRecords() []*ReadRecordModelWithBookID {
	if x != nil {
		return x.ReadRecords
	}
	return nil
}

func (x *UpdateMWebReadRecordsRequest) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

type BookshelfListResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Err           string                 `protobuf:"bytes,1,opt,name=err,proto3" json:"err,omitempty"`
	Bookshelves   []*BookshelfModel      `protobuf:"bytes,2,rep,name=bookshelves,proto3" json:"bookshelves,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BookshelfListResponse) Reset() {
	*x = BookshelfListResponse{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BookshelfListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookshelfListResponse) ProtoMessage() {}

func (x *BookshelfListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookshelfListResponse.ProtoReflect.Descriptor instead.
func (*BookshelfListResponse) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{11}
}

func (x *BookshelfListResponse) GetErr() string {
	if x != nil {
		return x.Err
	}
	return ""
}

func (x *BookshelfListResponse) GetBookshelves() []*BookshelfModel {
	if x != nil {
		return x.Bookshelves
	}
	return nil
}

type FilterBooksRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId,omitempty"`
	BookIds       []string               `protobuf:"bytes,2,rep,name=bookIds,proto3" json:"bookIds,omitempty"`
	AppName       string                 `protobuf:"bytes,3,opt,name=appName,proto3" json:"appName,omitempty"`
	Version       int32                  `protobuf:"varint,4,opt,name=version,proto3" json:"version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterBooksRequest) Reset() {
	*x = FilterBooksRequest{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterBooksRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterBooksRequest) ProtoMessage() {}

func (x *FilterBooksRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterBooksRequest.ProtoReflect.Descriptor instead.
func (*FilterBooksRequest) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{12}
}

func (x *FilterBooksRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *FilterBooksRequest) GetBookIds() []string {
	if x != nil {
		return x.BookIds
	}
	return nil
}

func (x *FilterBooksRequest) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *FilterBooksRequest) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

type FilterBooksResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Err           string                 `protobuf:"bytes,1,opt,name=err,proto3" json:"err,omitempty"`
	AddBooks      []string               `protobuf:"bytes,2,rep,name=addBooks,proto3" json:"addBooks,omitempty"`
	ExistBooks    []string               `protobuf:"bytes,3,rep,name=existBooks,proto3" json:"existBooks,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterBooksResponse) Reset() {
	*x = FilterBooksResponse{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterBooksResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterBooksResponse) ProtoMessage() {}

func (x *FilterBooksResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterBooksResponse.ProtoReflect.Descriptor instead.
func (*FilterBooksResponse) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{13}
}

func (x *FilterBooksResponse) GetErr() string {
	if x != nil {
		return x.Err
	}
	return ""
}

func (x *FilterBooksResponse) GetAddBooks() []string {
	if x != nil {
		return x.AddBooks
	}
	return nil
}

func (x *FilterBooksResponse) GetExistBooks() []string {
	if x != nil {
		return x.ExistBooks
	}
	return nil
}

type UpdateUserRecordReq struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	UserId        string                     `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId,omitempty"`
	AppName       string                     `protobuf:"bytes,2,opt,name=appName,proto3" json:"appName,omitempty"`
	ReadRecords   *ReadRecordModelWithBookID `protobuf:"bytes,3,opt,name=readRecords,proto3" json:"readRecords,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserRecordReq) Reset() {
	*x = UpdateUserRecordReq{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserRecordReq) ProtoMessage() {}

func (x *UpdateUserRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserRecordReq.ProtoReflect.Descriptor instead.
func (*UpdateUserRecordReq) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateUserRecordReq) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UpdateUserRecordReq) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *UpdateUserRecordReq) GetReadRecords() *ReadRecordModelWithBookID {
	if x != nil {
		return x.ReadRecords
	}
	return nil
}

type AddUserReadRecordRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId,omitempty"`
	BookIds       []string               `protobuf:"bytes,2,rep,name=bookIds,proto3" json:"bookIds,omitempty"`
	Created       int64                  `protobuf:"varint,3,opt,name=created,proto3" json:"created,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddUserReadRecordRequest) Reset() {
	*x = AddUserReadRecordRequest{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddUserReadRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddUserReadRecordRequest) ProtoMessage() {}

func (x *AddUserReadRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddUserReadRecordRequest.ProtoReflect.Descriptor instead.
func (*AddUserReadRecordRequest) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{15}
}

func (x *AddUserReadRecordRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *AddUserReadRecordRequest) GetBookIds() []string {
	if x != nil {
		return x.BookIds
	}
	return nil
}

func (x *AddUserReadRecordRequest) GetCreated() int64 {
	if x != nil {
		return x.Created
	}
	return 0
}

type GetUserShelfReadRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId,omitempty"`
	BookId        string                 `protobuf:"bytes,2,opt,name=bookId,proto3" json:"bookId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserShelfReadRequest) Reset() {
	*x = GetUserShelfReadRequest{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserShelfReadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserShelfReadRequest) ProtoMessage() {}

func (x *GetUserShelfReadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserShelfReadRequest.ProtoReflect.Descriptor instead.
func (*GetUserShelfReadRequest) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{16}
}

func (x *GetUserShelfReadRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetUserShelfReadRequest) GetBookId() string {
	if x != nil {
		return x.BookId
	}
	return ""
}

type GetUserShelfReadListRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId,omitempty"`
	Start         int64                  `protobuf:"varint,2,opt,name=start,proto3" json:"start,omitempty"`
	Limit         int64                  `protobuf:"varint,3,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserShelfReadListRequest) Reset() {
	*x = GetUserShelfReadListRequest{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserShelfReadListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserShelfReadListRequest) ProtoMessage() {}

func (x *GetUserShelfReadListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserShelfReadListRequest.ProtoReflect.Descriptor instead.
func (*GetUserShelfReadListRequest) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{17}
}

func (x *GetUserShelfReadListRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *GetUserShelfReadListRequest) GetStart() int64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *GetUserShelfReadListRequest) GetLimit() int64 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type UserShelfReadModel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          string                 `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	Book          string                 `protobuf:"bytes,2,opt,name=book,proto3" json:"book,omitempty"`
	ReadRecord    *ReadRecordModel       `protobuf:"bytes,3,opt,name=readRecord,proto3" json:"readRecord,omitempty"`
	RecordUpdated int64                  `protobuf:"varint,4,opt,name=recordUpdated,proto3" json:"recordUpdated,omitempty"`
	Created       int64                  `protobuf:"varint,5,opt,name=created,proto3" json:"created,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserShelfReadModel) Reset() {
	*x = UserShelfReadModel{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserShelfReadModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserShelfReadModel) ProtoMessage() {}

func (x *UserShelfReadModel) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserShelfReadModel.ProtoReflect.Descriptor instead.
func (*UserShelfReadModel) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{18}
}

func (x *UserShelfReadModel) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *UserShelfReadModel) GetBook() string {
	if x != nil {
		return x.Book
	}
	return ""
}

func (x *UserShelfReadModel) GetReadRecord() *ReadRecordModel {
	if x != nil {
		return x.ReadRecord
	}
	return nil
}

func (x *UserShelfReadModel) GetRecordUpdated() int64 {
	if x != nil {
		return x.RecordUpdated
	}
	return 0
}

func (x *UserShelfReadModel) GetCreated() int64 {
	if x != nil {
		return x.Created
	}
	return 0
}

type GetUserReadRecordListResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Err           string                 `protobuf:"bytes,1,opt,name=err,proto3" json:"err,omitempty"`
	RecoredList   []*UserShelfReadModel  `protobuf:"bytes,2,rep,name=recoredList,proto3" json:"recoredList,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserReadRecordListResponse) Reset() {
	*x = GetUserReadRecordListResponse{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserReadRecordListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserReadRecordListResponse) ProtoMessage() {}

func (x *GetUserReadRecordListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserReadRecordListResponse.ProtoReflect.Descriptor instead.
func (*GetUserReadRecordListResponse) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{19}
}

func (x *GetUserReadRecordListResponse) GetErr() string {
	if x != nil {
		return x.Err
	}
	return ""
}

func (x *GetUserReadRecordListResponse) GetRecoredList() []*UserShelfReadModel {
	if x != nil {
		return x.RecoredList
	}
	return nil
}

type GetUserShelfReadResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Err           string                 `protobuf:"bytes,1,opt,name=err,proto3" json:"err,omitempty"`
	Record        *UserShelfReadModel    `protobuf:"bytes,2,opt,name=record,proto3" json:"record,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserShelfReadResponse) Reset() {
	*x = GetUserShelfReadResponse{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserShelfReadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserShelfReadResponse) ProtoMessage() {}

func (x *GetUserShelfReadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserShelfReadResponse.ProtoReflect.Descriptor instead.
func (*GetUserShelfReadResponse) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{20}
}

func (x *GetUserShelfReadResponse) GetErr() string {
	if x != nil {
		return x.Err
	}
	return ""
}

func (x *GetUserShelfReadResponse) GetRecord() *UserShelfReadModel {
	if x != nil {
		return x.Record
	}
	return nil
}

type UpdateUserShelfReadRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          string                 `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	Book          string                 `protobuf:"bytes,2,opt,name=book,proto3" json:"book,omitempty"`
	ReadRecord    *ReadRecordModel       `protobuf:"bytes,3,opt,name=readRecord,proto3" json:"readRecord,omitempty"`
	Upsert        bool                   `protobuf:"varint,4,opt,name=upsert,proto3" json:"upsert,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserShelfReadRequest) Reset() {
	*x = UpdateUserShelfReadRequest{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserShelfReadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserShelfReadRequest) ProtoMessage() {}

func (x *UpdateUserShelfReadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserShelfReadRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserShelfReadRequest) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{21}
}

func (x *UpdateUserShelfReadRequest) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *UpdateUserShelfReadRequest) GetBook() string {
	if x != nil {
		return x.Book
	}
	return ""
}

func (x *UpdateUserShelfReadRequest) GetReadRecord() *ReadRecordModel {
	if x != nil {
		return x.ReadRecord
	}
	return nil
}

func (x *UpdateUserShelfReadRequest) GetUpsert() bool {
	if x != nil {
		return x.Upsert
	}
	return false
}

type UserShelfBook struct {
	state           protoimpl.MessageState            `protogen:"open.v1"`
	XId             string                            `protobuf:"bytes,1,opt,name=_id,json=Id,proto3" json:"_id,omitempty"`
	Author          string                            `protobuf:"bytes,2,opt,name=author,proto3" json:"author,omitempty"`
	Cover           string                            `protobuf:"bytes,3,opt,name=cover,proto3" json:"cover,omitempty"`
	Title           string                            `protobuf:"bytes,4,opt,name=title,proto3" json:"title,omitempty"`
	Buytype         int64                             `protobuf:"varint,5,opt,name=buytype,proto3" json:"buytype,omitempty"`
	AllowMonthly    bool                              `protobuf:"varint,6,opt,name=allowMonthly,proto3" json:"allowMonthly,omitempty"`
	AllowVoucher    bool                              `protobuf:"varint,7,opt,name=allowVoucher,proto3" json:"allowVoucher,omitempty"`
	HasCp           bool                              `protobuf:"varint,8,opt,name=hasCp,proto3" json:"hasCp,omitempty"`
	ReferenceSource string                            `protobuf:"bytes,9,opt,name=referenceSource,proto3" json:"referenceSource,omitempty"`
	Update          int64                             `protobuf:"varint,10,opt,name=update,proto3" json:"update,omitempty"`
	ChaptersCount   int64                             `protobuf:"varint,11,opt,name=chaptersCount,proto3" json:"chaptersCount,omitempty"`
	LastChapter     string                            `protobuf:"bytes,12,opt,name=lastChapter,proto3" json:"lastChapter,omitempty"`
	Created         int64                             `protobuf:"varint,13,opt,name=created,proto3" json:"created,omitempty"`
	XLe             bool                              `protobuf:"varint,14,opt,name=_le,json=Le,proto3" json:"_le,omitempty"`
	ContentType     string                            `protobuf:"bytes,15,opt,name=contentType,proto3" json:"contentType,omitempty"`
	Superscript     string                            `protobuf:"bytes,16,opt,name=superscript,proto3" json:"superscript,omitempty"`
	SizeType        int64                             `protobuf:"varint,17,opt,name=sizeType,proto3" json:"sizeType,omitempty"`
	XMm             bool                              `protobuf:"varint,18,opt,name=_mm,json=Mm,proto3" json:"_mm,omitempty"`
	ReadRecord      *ReadRecordModelWithBookAndUpdate `protobuf:"bytes,19,opt,name=readRecord,proto3" json:"readRecord,omitempty"`
	AdvertRead      bool                              `protobuf:"varint,20,opt,name=advertRead,proto3" json:"advertRead,omitempty"`
	XGg             bool                              `protobuf:"varint,21,opt,name=_gg,json=Gg,proto3" json:"_gg,omitempty"`
	XSs             bool                              `protobuf:"varint,22,opt,name=_ss,json=Ss,proto3" json:"_ss,omitempty"`
	ModifyTime      int64                             `protobuf:"varint,23,opt,name=modifyTime,proto3" json:"modifyTime,omitempty"`
	XFf             bool                              `protobuf:"varint,24,opt,name=_ff,json=Ff,proto3" json:"_ff,omitempty"` //是否允许免费看，默认false
	Subscript       int32                             `protobuf:"varint,25,opt,name=subscript,proto3" json:"subscript,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *UserShelfBook) Reset() {
	*x = UserShelfBook{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserShelfBook) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserShelfBook) ProtoMessage() {}

func (x *UserShelfBook) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserShelfBook.ProtoReflect.Descriptor instead.
func (*UserShelfBook) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{22}
}

func (x *UserShelfBook) GetXId() string {
	if x != nil {
		return x.XId
	}
	return ""
}

func (x *UserShelfBook) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *UserShelfBook) GetCover() string {
	if x != nil {
		return x.Cover
	}
	return ""
}

func (x *UserShelfBook) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *UserShelfBook) GetBuytype() int64 {
	if x != nil {
		return x.Buytype
	}
	return 0
}

func (x *UserShelfBook) GetAllowMonthly() bool {
	if x != nil {
		return x.AllowMonthly
	}
	return false
}

func (x *UserShelfBook) GetAllowVoucher() bool {
	if x != nil {
		return x.AllowVoucher
	}
	return false
}

func (x *UserShelfBook) GetHasCp() bool {
	if x != nil {
		return x.HasCp
	}
	return false
}

func (x *UserShelfBook) GetReferenceSource() string {
	if x != nil {
		return x.ReferenceSource
	}
	return ""
}

func (x *UserShelfBook) GetUpdate() int64 {
	if x != nil {
		return x.Update
	}
	return 0
}

func (x *UserShelfBook) GetChaptersCount() int64 {
	if x != nil {
		return x.ChaptersCount
	}
	return 0
}

func (x *UserShelfBook) GetLastChapter() string {
	if x != nil {
		return x.LastChapter
	}
	return ""
}

func (x *UserShelfBook) GetCreated() int64 {
	if x != nil {
		return x.Created
	}
	return 0
}

func (x *UserShelfBook) GetXLe() bool {
	if x != nil {
		return x.XLe
	}
	return false
}

func (x *UserShelfBook) GetContentType() string {
	if x != nil {
		return x.ContentType
	}
	return ""
}

func (x *UserShelfBook) GetSuperscript() string {
	if x != nil {
		return x.Superscript
	}
	return ""
}

func (x *UserShelfBook) GetSizeType() int64 {
	if x != nil {
		return x.SizeType
	}
	return 0
}

func (x *UserShelfBook) GetXMm() bool {
	if x != nil {
		return x.XMm
	}
	return false
}

func (x *UserShelfBook) GetReadRecord() *ReadRecordModelWithBookAndUpdate {
	if x != nil {
		return x.ReadRecord
	}
	return nil
}

func (x *UserShelfBook) GetAdvertRead() bool {
	if x != nil {
		return x.AdvertRead
	}
	return false
}

func (x *UserShelfBook) GetXGg() bool {
	if x != nil {
		return x.XGg
	}
	return false
}

func (x *UserShelfBook) GetXSs() bool {
	if x != nil {
		return x.XSs
	}
	return false
}

func (x *UserShelfBook) GetModifyTime() int64 {
	if x != nil {
		return x.ModifyTime
	}
	return 0
}

func (x *UserShelfBook) GetXFf() bool {
	if x != nil {
		return x.XFf
	}
	return false
}

func (x *UserShelfBook) GetSubscript() int32 {
	if x != nil {
		return x.Subscript
	}
	return 0
}

type UserShelfBooksResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Err           string                 `protobuf:"bytes,1,opt,name=Err,proto3" json:"Err,omitempty"`
	Books         []*UserShelfBook       `protobuf:"bytes,2,rep,name=books,proto3" json:"books,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserShelfBooksResponse) Reset() {
	*x = UserShelfBooksResponse{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserShelfBooksResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserShelfBooksResponse) ProtoMessage() {}

func (x *UserShelfBooksResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserShelfBooksResponse.ProtoReflect.Descriptor instead.
func (*UserShelfBooksResponse) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{23}
}

func (x *UserShelfBooksResponse) GetErr() string {
	if x != nil {
		return x.Err
	}
	return ""
}

func (x *UserShelfBooksResponse) GetBooks() []*UserShelfBook {
	if x != nil {
		return x.Books
	}
	return nil
}

type UserId struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          string                 `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserId) Reset() {
	*x = UserId{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserId) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserId) ProtoMessage() {}

func (x *UserId) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserId.ProtoReflect.Descriptor instead.
func (*UserId) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{24}
}

func (x *UserId) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

type BookShelfV3 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Updated       int64                  `protobuf:"varint,2,opt,name=updated,proto3" json:"updated,omitempty"`
	RecordUpdated int64                  `protobuf:"varint,3,opt,name=recordUpdated,proto3" json:"recordUpdated,omitempty"`
	Type          int32                  `protobuf:"varint,4,opt,name=type,proto3" json:"type,omitempty"`
	ModifyTime    int64                  `protobuf:"varint,5,opt,name=modifyTime,proto3" json:"modifyTime,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BookShelfV3) Reset() {
	*x = BookShelfV3{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BookShelfV3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BookShelfV3) ProtoMessage() {}

func (x *BookShelfV3) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BookShelfV3.ProtoReflect.Descriptor instead.
func (*BookShelfV3) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{25}
}

func (x *BookShelfV3) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BookShelfV3) GetUpdated() int64 {
	if x != nil {
		return x.Updated
	}
	return 0
}

func (x *BookShelfV3) GetRecordUpdated() int64 {
	if x != nil {
		return x.RecordUpdated
	}
	return 0
}

func (x *BookShelfV3) GetType() int32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *BookShelfV3) GetModifyTime() int64 {
	if x != nil {
		return x.ModifyTime
	}
	return 0
}

type UserIdAndAppName struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AppName       string                 `protobuf:"bytes,1,opt,name=appName,proto3" json:"appName,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserIdAndAppName) Reset() {
	*x = UserIdAndAppName{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserIdAndAppName) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIdAndAppName) ProtoMessage() {}

func (x *UserIdAndAppName) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIdAndAppName.ProtoReflect.Descriptor instead.
func (*UserIdAndAppName) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{26}
}

func (x *UserIdAndAppName) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *UserIdAndAppName) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type UserIdAndBookIds struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId,omitempty"`
	BookIds       []string               `protobuf:"bytes,2,rep,name=bookIds,proto3" json:"bookIds,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserIdAndBookIds) Reset() {
	*x = UserIdAndBookIds{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserIdAndBookIds) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIdAndBookIds) ProtoMessage() {}

func (x *UserIdAndBookIds) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIdAndBookIds.ProtoReflect.Descriptor instead.
func (*UserIdAndBookIds) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{27}
}

func (x *UserIdAndBookIds) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserIdAndBookIds) GetBookIds() []string {
	if x != nil {
		return x.BookIds
	}
	return nil
}

type UserIdAndBookIdsAndType struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=userId,proto3" json:"userId,omitempty"`
	BookIds       []string               `protobuf:"bytes,2,rep,name=bookIds,proto3" json:"bookIds,omitempty"`
	Type          string                 `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserIdAndBookIdsAndType) Reset() {
	*x = UserIdAndBookIdsAndType{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserIdAndBookIdsAndType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIdAndBookIdsAndType) ProtoMessage() {}

func (x *UserIdAndBookIdsAndType) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIdAndBookIdsAndType.ProtoReflect.Descriptor instead.
func (*UserIdAndBookIdsAndType) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{28}
}

func (x *UserIdAndBookIdsAndType) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserIdAndBookIdsAndType) GetBookIds() []string {
	if x != nil {
		return x.BookIds
	}
	return nil
}

func (x *UserIdAndBookIdsAndType) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type UserIdAndBookIdsAndAppName struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AppName       string                 `protobuf:"bytes,1,opt,name=appName,proto3" json:"appName,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=userId,proto3" json:"userId,omitempty"`
	BookIds       []string               `protobuf:"bytes,3,rep,name=bookIds,proto3" json:"bookIds,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserIdAndBookIdsAndAppName) Reset() {
	*x = UserIdAndBookIdsAndAppName{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserIdAndBookIdsAndAppName) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserIdAndBookIdsAndAppName) ProtoMessage() {}

func (x *UserIdAndBookIdsAndAppName) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserIdAndBookIdsAndAppName.ProtoReflect.Descriptor instead.
func (*UserIdAndBookIdsAndAppName) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{29}
}

func (x *UserIdAndBookIdsAndAppName) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *UserIdAndBookIdsAndAppName) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *UserIdAndBookIdsAndAppName) GetBookIds() []string {
	if x != nil {
		return x.BookIds
	}
	return nil
}

type TimeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Err           string                 `protobuf:"bytes,1,opt,name=Err,proto3" json:"Err,omitempty"`
	Time          int64                  `protobuf:"varint,2,opt,name=time,proto3" json:"time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TimeResponse) Reset() {
	*x = TimeResponse{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TimeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeResponse) ProtoMessage() {}

func (x *TimeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeResponse.ProtoReflect.Descriptor instead.
func (*TimeResponse) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{30}
}

func (x *TimeResponse) GetErr() string {
	if x != nil {
		return x.Err
	}
	return ""
}

func (x *TimeResponse) GetTime() int64 {
	if x != nil {
		return x.Time
	}
	return 0
}

type GetBookShelfResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Err           string                 `protobuf:"bytes,1,opt,name=Err,proto3" json:"Err,omitempty"`
	ShelfBooks    []*UserShelfBook       `protobuf:"bytes,2,rep,name=shelfBooks,proto3" json:"shelfBooks,omitempty"`
	FeedingBooks  []*UserShelfBook       `protobuf:"bytes,3,rep,name=feedingBooks,proto3" json:"feedingBooks,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBookShelfResponse) Reset() {
	*x = GetBookShelfResponse{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBookShelfResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBookShelfResponse) ProtoMessage() {}

func (x *GetBookShelfResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBookShelfResponse.ProtoReflect.Descriptor instead.
func (*GetBookShelfResponse) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{31}
}

func (x *GetBookShelfResponse) GetErr() string {
	if x != nil {
		return x.Err
	}
	return ""
}

func (x *GetBookShelfResponse) GetShelfBooks() []*UserShelfBook {
	if x != nil {
		return x.ShelfBooks
	}
	return nil
}

func (x *GetBookShelfResponse) GetFeedingBooks() []*UserShelfBook {
	if x != nil {
		return x.FeedingBooks
	}
	return nil
}

type GetBookShelfV3Response struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Err           string                 `protobuf:"bytes,1,opt,name=Err,proto3" json:"Err,omitempty"`
	ShelfBooks    []*BookShelfV3         `protobuf:"bytes,2,rep,name=shelfBooks,proto3" json:"shelfBooks,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBookShelfV3Response) Reset() {
	*x = GetBookShelfV3Response{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBookShelfV3Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBookShelfV3Response) ProtoMessage() {}

func (x *GetBookShelfV3Response) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBookShelfV3Response.ProtoReflect.Descriptor instead.
func (*GetBookShelfV3Response) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{32}
}

func (x *GetBookShelfV3Response) GetErr() string {
	if x != nil {
		return x.Err
	}
	return ""
}

func (x *GetBookShelfV3Response) GetShelfBooks() []*BookShelfV3 {
	if x != nil {
		return x.ShelfBooks
	}
	return nil
}

// 批量获取用户书架
type ListBookShelfRequest struct {
	state   protoimpl.MessageState `protogen:"open.v1"`
	AppName string                 `protobuf:"bytes,1,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`
	// 最大长度支持100
	UserId        []string `protobuf:"bytes,2,rep,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListBookShelfRequest) Reset() {
	*x = ListBookShelfRequest{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListBookShelfRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBookShelfRequest) ProtoMessage() {}

func (x *ListBookShelfRequest) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBookShelfRequest.ProtoReflect.Descriptor instead.
func (*ListBookShelfRequest) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{33}
}

func (x *ListBookShelfRequest) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *ListBookShelfRequest) GetUserId() []string {
	if x != nil {
		return x.UserId
	}
	return nil
}

type ListBookShelfResponse struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	UserShelves   map[string]*UserBookShelf `protobuf:"bytes,1,rep,name=user_shelves,json=userShelves,proto3" json:"user_shelves,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListBookShelfResponse) Reset() {
	*x = ListBookShelfResponse{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListBookShelfResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBookShelfResponse) ProtoMessage() {}

func (x *ListBookShelfResponse) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBookShelfResponse.ProtoReflect.Descriptor instead.
func (*ListBookShelfResponse) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{34}
}

func (x *ListBookShelfResponse) GetUserShelves() map[string]*UserBookShelf {
	if x != nil {
		return x.UserShelves
	}
	return nil
}

type UserBookShelf struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Bookshelves   []*BookshelfModel      `protobuf:"bytes,1,rep,name=bookshelves,proto3" json:"bookshelves,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserBookShelf) Reset() {
	*x = UserBookShelf{}
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserBookShelf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserBookShelf) ProtoMessage() {}

func (x *UserBookShelf) ProtoReflect() protoreflect.Message {
	mi := &file_proto_bookshelf_bookshelfsvc_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserBookShelf.ProtoReflect.Descriptor instead.
func (*UserBookShelf) Descriptor() ([]byte, []int) {
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP(), []int{35}
}

func (x *UserBookShelf) GetBookshelves() []*BookshelfModel {
	if x != nil {
		return x.Bookshelves
	}
	return nil
}

var File_proto_bookshelf_bookshelfsvc_proto protoreflect.FileDescriptor

const file_proto_bookshelf_bookshelfsvc_proto_rawDesc = "" +
	"\n" +
	"\"proto/bookshelf/bookshelfsvc.proto\x12\tbookshelf\"^\n" +
	"\x10BookshelfRequest\x12\x18\n" +
	"\aappName\x18\x01 \x01(\tR\aappName\x12\x16\n" +
	"\x06userId\x18\x02 \x01(\tR\x06userId\x12\x18\n" +
	"\abookIds\x18\x03 \x03(\tR\abookIds\"l\n" +
	"\x13AddBookshelfRequest\x12\x18\n" +
	"\aappName\x18\x01 \x01(\tR\aappName\x12;\n" +
	"\vbookshelves\x18\x02 \x03(\v2\x19.bookshelf.BookshelfModelR\vbookshelves\"\xb2\x01\n" +
	"\x16UpdateBookshelfRequest\x12\x18\n" +
	"\aappName\x18\x01 \x01(\tR\aappName\x12\x16\n" +
	"\x06userId\x18\x02 \x01(\tR\x06userId\x12\x18\n" +
	"\abookIds\x18\x03 \x03(\tR\abookIds\x12\x1e\n" +
	"\n" +
	"updateKeys\x18\x04 \x03(\tR\n" +
	"updateKeys\x12\x12\n" +
	"\x04area\x18\x05 \x01(\x05R\x04area\x12\x18\n" +
	"\amonthly\x18\x06 \x01(\bR\amonthly\"<\n" +
	"\x0eNormalResponse\x12\x10\n" +
	"\x03err\x18\x01 \x01(\tR\x03err\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\x8b\x01\n" +
	"\x0fReadRecordModel\x12\x14\n" +
	"\x05tocId\x18\x01 \x01(\tR\x05tocId\x12\x18\n" +
	"\atocName\x18\x02 \x01(\tR\atocName\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12\x14\n" +
	"\x05order\x18\x04 \x01(\x05R\x05order\x12\x1c\n" +
	"\twordIndex\x18\x05 \x01(\x05R\twordIndex\"\xa9\x01\n" +
	"\x19ReadRecordModelWithBookID\x12\x14\n" +
	"\x05tocId\x18\x01 \x01(\tR\x05tocId\x12\x18\n" +
	"\atocName\x18\x02 \x01(\tR\atocName\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12\x14\n" +
	"\x05order\x18\x04 \x01(\x05R\x05order\x12\x1c\n" +
	"\twordIndex\x18\x05 \x01(\x05R\twordIndex\x12\x12\n" +
	"\x04book\x18\x06 \x01(\tR\x04book\"\xc8\x01\n" +
	" ReadRecordModelWithBookAndUpdate\x12\x14\n" +
	"\x05tocId\x18\x01 \x01(\tR\x05tocId\x12\x18\n" +
	"\atocName\x18\x02 \x01(\tR\atocName\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12\x14\n" +
	"\x05order\x18\x04 \x01(\x05R\x05order\x12\x1c\n" +
	"\twordIndex\x18\x05 \x01(\x05R\twordIndex\x12\x16\n" +
	"\x06update\x18\x06 \x01(\x03R\x06update\x12\x12\n" +
	"\x04book\x18\a \x01(\tR\x04book\"\xcc\x02\n" +
	"\x0eBookshelfModel\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04user\x18\x02 \x01(\tR\x04user\x12\x12\n" +
	"\x04book\x18\x03 \x01(\tR\x04book\x12\x12\n" +
	"\x04area\x18\x04 \x01(\x05R\x04area\x12\x18\n" +
	"\amonthly\x18\x05 \x01(\bR\amonthly\x12\x18\n" +
	"\acreated\x18\x06 \x01(\x03R\acreated\x12:\n" +
	"\n" +
	"readRecord\x18\a \x01(\v2\x1a.bookshelf.ReadRecordModelR\n" +
	"readRecord\x12$\n" +
	"\rrecordUpdated\x18\b \x01(\x03R\rrecordUpdated\x12\x18\n" +
	"\aappName\x18\t \x01(\tR\aappName\x12\x1c\n" +
	"\tallowFree\x18\n" +
	" \x01(\bR\tallowFree\x12 \n" +
	"\vsuperscript\x18\v \x01(\x05R\vsuperscript\"\x9f\x01\n" +
	"\x17UpdateReadRecordRequest\x12\x18\n" +
	"\aappName\x18\x01 \x01(\tR\aappName\x12\x16\n" +
	"\x06userId\x18\x02 \x01(\tR\x06userId\x12\x16\n" +
	"\x06bookId\x18\x03 \x01(\tR\x06bookId\x12:\n" +
	"\n" +
	"readRecord\x18\x04 \x01(\v2\x1a.bookshelf.ReadRecordModelR\n" +
	"readRecord\"\x94\x01\n" +
	"\x18UpdateReadRecordsRequest\x12\x18\n" +
	"\aappName\x18\x01 \x01(\tR\aappName\x12\x16\n" +
	"\x06userId\x18\x02 \x01(\tR\x06userId\x12F\n" +
	"\vreadRecords\x18\x04 \x03(\v2$.bookshelf.ReadRecordModelWithBookIDR\vreadRecords\"\xac\x01\n" +
	"\x1cUpdateMWebReadRecordsRequest\x12\x18\n" +
	"\aappName\x18\x01 \x01(\tR\aappName\x12\x16\n" +
	"\x06userId\x18\x02 \x01(\tR\x06userId\x12F\n" +
	"\vreadRecords\x18\x04 \x03(\v2$.bookshelf.ReadRecordModelWithBookIDR\vreadRecords\x12\x12\n" +
	"\x04from\x18\x05 \x01(\tR\x04from\"f\n" +
	"\x15BookshelfListResponse\x12\x10\n" +
	"\x03err\x18\x01 \x01(\tR\x03err\x12;\n" +
	"\vbookshelves\x18\x02 \x03(\v2\x19.bookshelf.BookshelfModelR\vbookshelves\"z\n" +
	"\x12FilterBooksRequest\x12\x16\n" +
	"\x06userId\x18\x01 \x01(\tR\x06userId\x12\x18\n" +
	"\abookIds\x18\x02 \x03(\tR\abookIds\x12\x18\n" +
	"\aappName\x18\x03 \x01(\tR\aappName\x12\x18\n" +
	"\aversion\x18\x04 \x01(\x05R\aversion\"c\n" +
	"\x13FilterBooksResponse\x12\x10\n" +
	"\x03err\x18\x01 \x01(\tR\x03err\x12\x1a\n" +
	"\baddBooks\x18\x02 \x03(\tR\baddBooks\x12\x1e\n" +
	"\n" +
	"existBooks\x18\x03 \x03(\tR\n" +
	"existBooks\"\x8f\x01\n" +
	"\x13UpdateUserRecordReq\x12\x16\n" +
	"\x06userId\x18\x01 \x01(\tR\x06userId\x12\x18\n" +
	"\aappName\x18\x02 \x01(\tR\aappName\x12F\n" +
	"\vreadRecords\x18\x03 \x01(\v2$.bookshelf.ReadRecordModelWithBookIDR\vreadRecords\"f\n" +
	"\x18AddUserReadRecordRequest\x12\x16\n" +
	"\x06userId\x18\x01 \x01(\tR\x06userId\x12\x18\n" +
	"\abookIds\x18\x02 \x03(\tR\abookIds\x12\x18\n" +
	"\acreated\x18\x03 \x01(\x03R\acreated\"I\n" +
	"\x17GetUserShelfReadRequest\x12\x16\n" +
	"\x06userId\x18\x01 \x01(\tR\x06userId\x12\x16\n" +
	"\x06bookId\x18\x02 \x01(\tR\x06bookId\"a\n" +
	"\x1bGetUserShelfReadListRequest\x12\x16\n" +
	"\x06userId\x18\x01 \x01(\tR\x06userId\x12\x14\n" +
	"\x05start\x18\x02 \x01(\x03R\x05start\x12\x14\n" +
	"\x05limit\x18\x03 \x01(\x03R\x05limit\"\xb8\x01\n" +
	"\x12UserShelfReadModel\x12\x12\n" +
	"\x04user\x18\x01 \x01(\tR\x04user\x12\x12\n" +
	"\x04book\x18\x02 \x01(\tR\x04book\x12:\n" +
	"\n" +
	"readRecord\x18\x03 \x01(\v2\x1a.bookshelf.ReadRecordModelR\n" +
	"readRecord\x12$\n" +
	"\rrecordUpdated\x18\x04 \x01(\x03R\rrecordUpdated\x12\x18\n" +
	"\acreated\x18\x05 \x01(\x03R\acreated\"r\n" +
	"\x1dGetUserReadRecordListResponse\x12\x10\n" +
	"\x03err\x18\x01 \x01(\tR\x03err\x12?\n" +
	"\vrecoredList\x18\x02 \x03(\v2\x1d.bookshelf.UserShelfReadModelR\vrecoredList\"c\n" +
	"\x18GetUserShelfReadResponse\x12\x10\n" +
	"\x03err\x18\x01 \x01(\tR\x03err\x125\n" +
	"\x06record\x18\x02 \x01(\v2\x1d.bookshelf.UserShelfReadModelR\x06record\"\x98\x01\n" +
	"\x1aUpdateUserShelfReadRequest\x12\x12\n" +
	"\x04user\x18\x01 \x01(\tR\x04user\x12\x12\n" +
	"\x04book\x18\x02 \x01(\tR\x04book\x12:\n" +
	"\n" +
	"readRecord\x18\x03 \x01(\v2\x1a.bookshelf.ReadRecordModelR\n" +
	"readRecord\x12\x16\n" +
	"\x06upsert\x18\x04 \x01(\bR\x06upsert\"\xe0\x05\n" +
	"\rUserShelfBook\x12\x0f\n" +
	"\x03_id\x18\x01 \x01(\tR\x02Id\x12\x16\n" +
	"\x06author\x18\x02 \x01(\tR\x06author\x12\x14\n" +
	"\x05cover\x18\x03 \x01(\tR\x05cover\x12\x14\n" +
	"\x05title\x18\x04 \x01(\tR\x05title\x12\x18\n" +
	"\abuytype\x18\x05 \x01(\x03R\abuytype\x12\"\n" +
	"\fallowMonthly\x18\x06 \x01(\bR\fallowMonthly\x12\"\n" +
	"\fallowVoucher\x18\a \x01(\bR\fallowVoucher\x12\x14\n" +
	"\x05hasCp\x18\b \x01(\bR\x05hasCp\x12(\n" +
	"\x0freferenceSource\x18\t \x01(\tR\x0freferenceSource\x12\x16\n" +
	"\x06update\x18\n" +
	" \x01(\x03R\x06update\x12$\n" +
	"\rchaptersCount\x18\v \x01(\x03R\rchaptersCount\x12 \n" +
	"\vlastChapter\x18\f \x01(\tR\vlastChapter\x12\x18\n" +
	"\acreated\x18\r \x01(\x03R\acreated\x12\x0f\n" +
	"\x03_le\x18\x0e \x01(\bR\x02Le\x12 \n" +
	"\vcontentType\x18\x0f \x01(\tR\vcontentType\x12 \n" +
	"\vsuperscript\x18\x10 \x01(\tR\vsuperscript\x12\x1a\n" +
	"\bsizeType\x18\x11 \x01(\x03R\bsizeType\x12\x0f\n" +
	"\x03_mm\x18\x12 \x01(\bR\x02Mm\x12K\n" +
	"\n" +
	"readRecord\x18\x13 \x01(\v2+.bookshelf.ReadRecordModelWithBookAndUpdateR\n" +
	"readRecord\x12\x1e\n" +
	"\n" +
	"advertRead\x18\x14 \x01(\bR\n" +
	"advertRead\x12\x0f\n" +
	"\x03_gg\x18\x15 \x01(\bR\x02Gg\x12\x0f\n" +
	"\x03_ss\x18\x16 \x01(\bR\x02Ss\x12\x1e\n" +
	"\n" +
	"modifyTime\x18\x17 \x01(\x03R\n" +
	"modifyTime\x12\x0f\n" +
	"\x03_ff\x18\x18 \x01(\bR\x02Ff\x12\x1c\n" +
	"\tsubscript\x18\x19 \x01(\x05R\tsubscript\"Z\n" +
	"\x16UserShelfBooksResponse\x12\x10\n" +
	"\x03Err\x18\x01 \x01(\tR\x03Err\x12.\n" +
	"\x05books\x18\x02 \x03(\v2\x18.bookshelf.UserShelfBookR\x05books\"\x1c\n" +
	"\x06UserId\x12\x12\n" +
	"\x04user\x18\x01 \x01(\tR\x04user\"\x91\x01\n" +
	"\vBookShelfV3\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x18\n" +
	"\aupdated\x18\x02 \x01(\x03R\aupdated\x12$\n" +
	"\rrecordUpdated\x18\x03 \x01(\x03R\rrecordUpdated\x12\x12\n" +
	"\x04type\x18\x04 \x01(\x05R\x04type\x12\x1e\n" +
	"\n" +
	"modifyTime\x18\x05 \x01(\x03R\n" +
	"modifyTime\"D\n" +
	"\x10UserIdAndAppName\x12\x18\n" +
	"\aappName\x18\x01 \x01(\tR\aappName\x12\x16\n" +
	"\x06userId\x18\x02 \x01(\tR\x06userId\"D\n" +
	"\x10UserIdAndBookIds\x12\x16\n" +
	"\x06userId\x18\x01 \x01(\tR\x06userId\x12\x18\n" +
	"\abookIds\x18\x02 \x03(\tR\abookIds\"_\n" +
	"\x17UserIdAndBookIdsAndType\x12\x16\n" +
	"\x06userId\x18\x01 \x01(\tR\x06userId\x12\x18\n" +
	"\abookIds\x18\x02 \x03(\tR\abookIds\x12\x12\n" +
	"\x04type\x18\x03 \x01(\tR\x04type\"h\n" +
	"\x1aUserIdAndBookIdsAndAppName\x12\x18\n" +
	"\aappName\x18\x01 \x01(\tR\aappName\x12\x16\n" +
	"\x06userId\x18\x02 \x01(\tR\x06userId\x12\x18\n" +
	"\abookIds\x18\x03 \x03(\tR\abookIds\"4\n" +
	"\fTimeResponse\x12\x10\n" +
	"\x03Err\x18\x01 \x01(\tR\x03Err\x12\x12\n" +
	"\x04time\x18\x02 \x01(\x03R\x04time\"\xa0\x01\n" +
	"\x14GetBookShelfResponse\x12\x10\n" +
	"\x03Err\x18\x01 \x01(\tR\x03Err\x128\n" +
	"\n" +
	"shelfBooks\x18\x02 \x03(\v2\x18.bookshelf.UserShelfBookR\n" +
	"shelfBooks\x12<\n" +
	"\ffeedingBooks\x18\x03 \x03(\v2\x18.bookshelf.UserShelfBookR\ffeedingBooks\"b\n" +
	"\x16GetBookShelfV3Response\x12\x10\n" +
	"\x03Err\x18\x01 \x01(\tR\x03Err\x126\n" +
	"\n" +
	"shelfBooks\x18\x02 \x03(\v2\x16.bookshelf.BookShelfV3R\n" +
	"shelfBooks\"J\n" +
	"\x14ListBookShelfRequest\x12\x19\n" +
	"\bapp_name\x18\x01 \x01(\tR\aappName\x12\x17\n" +
	"\auser_id\x18\x02 \x03(\tR\x06userId\"\xc7\x01\n" +
	"\x15ListBookShelfResponse\x12T\n" +
	"\fuser_shelves\x18\x01 \x03(\v21.bookshelf.ListBookShelfResponse.UserShelvesEntryR\vuserShelves\x1aX\n" +
	"\x10UserShelvesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12.\n" +
	"\x05value\x18\x02 \x01(\v2\x18.bookshelf.UserBookShelfR\x05value:\x028\x01\"L\n" +
	"\rUserBookShelf\x12;\n" +
	"\vbookshelves\x18\x01 \x03(\v2\x19.bookshelf.BookshelfModelR\vbookshelves2\xa2\x14\n" +
	"\tBookshelf\x12O\n" +
	"\fGetBookshelf\x12\x1b.bookshelf.BookshelfRequest\x1a .bookshelf.BookshelfListResponse\"\x00\x12T\n" +
	"\rListBookShelf\x12\x1f.bookshelf.ListBookShelfRequest\x1a .bookshelf.ListBookShelfResponse\"\x00\x12K\n" +
	"\fAddBookshelf\x12\x1e.bookshelf.AddBookshelfRequest\x1a\x19.bookshelf.NormalResponse\"\x00\x12Q\n" +
	"\x0fUpdateBookshelf\x12!.bookshelf.UpdateBookshelfRequest\x1a\x19.bookshelf.NormalResponse\"\x00\x12H\n" +
	"\fDelBookshelf\x12\x1b.bookshelf.BookshelfRequest\x1a\x19.bookshelf.NormalResponse\"\x00\x12S\n" +
	"\x10UpdateReadRecord\x12\".bookshelf.UpdateReadRecordRequest\x1a\x19.bookshelf.NormalResponse\"\x00\x12U\n" +
	"\x11UpdateReadRecords\x12#.bookshelf.UpdateReadRecordsRequest\x1a\x19.bookshelf.NormalResponse\"\x00\x12U\n" +
	"\x12FilterBooksInMongo\x12\x1d.bookshelf.FilterBooksRequest\x1a\x1e.bookshelf.FilterBooksResponse\"\x00\x12T\n" +
	"\x10AddUserShelfRead\x12#.bookshelf.AddUserReadRecordRequest\x1a\x19.bookshelf.NormalResponse\"\x00\x12o\n" +
	"\x19GetUserShelfReadsByUserId\x12&.bookshelf.GetUserShelfReadListRequest\x1a(.bookshelf.GetUserReadRecordListResponse\"\x00\x12l\n" +
	"\x1fGetUserShelfReadByUserIdAndBook\x12\".bookshelf.GetUserShelfReadRequest\x1a#.bookshelf.GetUserShelfReadResponse\"\x00\x12Y\n" +
	"\x13UpdateUserShelfRead\x12%.bookshelf.UpdateUserShelfReadRequest\x1a\x19.bookshelf.NormalResponse\"\x00\x12T\n" +
	"\x10UpdateUserRecord\x12#.bookshelf.UpdateReadRecordsRequest\x1a\x19.bookshelf.NormalResponse\"\x00\x12X\n" +
	"\x10UpdateMWebRecord\x12'.bookshelf.UpdateMWebReadRecordsRequest\x1a\x19.bookshelf.NormalResponse\"\x00\x12^\n" +
	"\x10GetBookshelfInfo\x12%.bookshelf.UserIdAndBookIdsAndAppName\x1a!.bookshelf.UserShelfBooksResponse\"\x00\x12M\n" +
	"\x13RemoveBooks4Feeding\x12\x1b.bookshelf.UserIdAndBookIds\x1a\x17.bookshelf.TimeResponse\"\x00\x12Y\n" +
	"\x15RemoveBooks4FeedingV2\x12%.bookshelf.UserIdAndBookIdsAndAppName\x1a\x17.bookshelf.TimeResponse\"\x00\x12Y\n" +
	"\x15RemoveBooks4FeedingV3\x12%.bookshelf.UserIdAndBookIdsAndAppName\x1a\x17.bookshelf.TimeResponse\"\x00\x12K\n" +
	"\x11RemoveBooks4Shelf\x12\x1b.bookshelf.UserIdAndBookIds\x1a\x17.bookshelf.TimeResponse\"\x00\x12W\n" +
	"\x13RemoveBooks4ShelfV2\x12%.bookshelf.UserIdAndBookIdsAndAppName\x1a\x17.bookshelf.TimeResponse\"\x00\x12W\n" +
	"\x13RemoveBooks4ShelfV3\x12%.bookshelf.UserIdAndBookIdsAndAppName\x1a\x17.bookshelf.TimeResponse\"\x00\x12I\n" +
	"\x0fAddBook2Feeding\x12\x1b.bookshelf.UserIdAndBookIds\x1a\x17.bookshelf.TimeResponse\"\x00\x12U\n" +
	"\x11AddBook2FeedingV2\x12%.bookshelf.UserIdAndBookIdsAndAppName\x1a\x17.bookshelf.TimeResponse\"\x00\x12U\n" +
	"\x11AddBook2FeedingV3\x12%.bookshelf.UserIdAndBookIdsAndAppName\x1a\x17.bookshelf.TimeResponse\"\x00\x12N\n" +
	"\rAddBook2Shelf\x12\".bookshelf.UserIdAndBookIdsAndType\x1a\x17.bookshelf.TimeResponse\"\x00\x12S\n" +
	"\x0fAddBook2ShelfV2\x12%.bookshelf.UserIdAndBookIdsAndAppName\x1a\x17.bookshelf.TimeResponse\"\x00\x12S\n" +
	"\x0fAddBook2ShelfV3\x12%.bookshelf.UserIdAndBookIdsAndAppName\x1a\x17.bookshelf.TimeResponse\"\x00\x12D\n" +
	"\fGetBookShelf\x12\x11.bookshelf.UserId\x1a\x1f.bookshelf.GetBookShelfResponse\"\x00\x12P\n" +
	"\x0eGetBookShelfV2\x12\x1b.bookshelf.UserIdAndAppName\x1a\x1f.bookshelf.GetBookShelfResponse\"\x00\x12R\n" +
	"\x0eGetBookShelfV3\x12\x1b.bookshelf.UserIdAndAppName\x1a!.bookshelf.GetBookShelfV3Response\"\x00B2Z0creativematrix.com/beyondreading/proto/bookshelfb\x06proto3"

var (
	file_proto_bookshelf_bookshelfsvc_proto_rawDescOnce sync.Once
	file_proto_bookshelf_bookshelfsvc_proto_rawDescData []byte
)

func file_proto_bookshelf_bookshelfsvc_proto_rawDescGZIP() []byte {
	file_proto_bookshelf_bookshelfsvc_proto_rawDescOnce.Do(func() {
		file_proto_bookshelf_bookshelfsvc_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_bookshelf_bookshelfsvc_proto_rawDesc), len(file_proto_bookshelf_bookshelfsvc_proto_rawDesc)))
	})
	return file_proto_bookshelf_bookshelfsvc_proto_rawDescData
}

var file_proto_bookshelf_bookshelfsvc_proto_msgTypes = make([]protoimpl.MessageInfo, 37)
var file_proto_bookshelf_bookshelfsvc_proto_goTypes = []any{
	(*BookshelfRequest)(nil),                 // 0: bookshelf.BookshelfRequest
	(*AddBookshelfRequest)(nil),              // 1: bookshelf.AddBookshelfRequest
	(*UpdateBookshelfRequest)(nil),           // 2: bookshelf.UpdateBookshelfRequest
	(*NormalResponse)(nil),                   // 3: bookshelf.NormalResponse
	(*ReadRecordModel)(nil),                  // 4: bookshelf.ReadRecordModel
	(*ReadRecordModelWithBookID)(nil),        // 5: bookshelf.ReadRecordModelWithBookID
	(*ReadRecordModelWithBookAndUpdate)(nil), // 6: bookshelf.ReadRecordModelWithBookAndUpdate
	(*BookshelfModel)(nil),                   // 7: bookshelf.BookshelfModel
	(*UpdateReadRecordRequest)(nil),          // 8: bookshelf.UpdateReadRecordRequest
	(*UpdateReadRecordsRequest)(nil),         // 9: bookshelf.UpdateReadRecordsRequest
	(*UpdateMWebReadRecordsRequest)(nil),     // 10: bookshelf.UpdateMWebReadRecordsRequest
	(*BookshelfListResponse)(nil),            // 11: bookshelf.BookshelfListResponse
	(*FilterBooksRequest)(nil),               // 12: bookshelf.FilterBooksRequest
	(*FilterBooksResponse)(nil),              // 13: bookshelf.FilterBooksResponse
	(*UpdateUserRecordReq)(nil),              // 14: bookshelf.UpdateUserRecordReq
	(*AddUserReadRecordRequest)(nil),         // 15: bookshelf.AddUserReadRecordRequest
	(*GetUserShelfReadRequest)(nil),          // 16: bookshelf.GetUserShelfReadRequest
	(*GetUserShelfReadListRequest)(nil),      // 17: bookshelf.GetUserShelfReadListRequest
	(*UserShelfReadModel)(nil),               // 18: bookshelf.UserShelfReadModel
	(*GetUserReadRecordListResponse)(nil),    // 19: bookshelf.GetUserReadRecordListResponse
	(*GetUserShelfReadResponse)(nil),         // 20: bookshelf.GetUserShelfReadResponse
	(*UpdateUserShelfReadRequest)(nil),       // 21: bookshelf.UpdateUserShelfReadRequest
	(*UserShelfBook)(nil),                    // 22: bookshelf.UserShelfBook
	(*UserShelfBooksResponse)(nil),           // 23: bookshelf.UserShelfBooksResponse
	(*UserId)(nil),                           // 24: bookshelf.UserId
	(*BookShelfV3)(nil),                      // 25: bookshelf.BookShelfV3
	(*UserIdAndAppName)(nil),                 // 26: bookshelf.UserIdAndAppName
	(*UserIdAndBookIds)(nil),                 // 27: bookshelf.UserIdAndBookIds
	(*UserIdAndBookIdsAndType)(nil),          // 28: bookshelf.UserIdAndBookIdsAndType
	(*UserIdAndBookIdsAndAppName)(nil),       // 29: bookshelf.UserIdAndBookIdsAndAppName
	(*TimeResponse)(nil),                     // 30: bookshelf.TimeResponse
	(*GetBookShelfResponse)(nil),             // 31: bookshelf.GetBookShelfResponse
	(*GetBookShelfV3Response)(nil),           // 32: bookshelf.GetBookShelfV3Response
	(*ListBookShelfRequest)(nil),             // 33: bookshelf.ListBookShelfRequest
	(*ListBookShelfResponse)(nil),            // 34: bookshelf.ListBookShelfResponse
	(*UserBookShelf)(nil),                    // 35: bookshelf.UserBookShelf
	nil,                                      // 36: bookshelf.ListBookShelfResponse.UserShelvesEntry
}
var file_proto_bookshelf_bookshelfsvc_proto_depIdxs = []int32{
	7,  // 0: bookshelf.AddBookshelfRequest.bookshelves:type_name -> bookshelf.BookshelfModel
	4,  // 1: bookshelf.BookshelfModel.readRecord:type_name -> bookshelf.ReadRecordModel
	4,  // 2: bookshelf.UpdateReadRecordRequest.readRecord:type_name -> bookshelf.ReadRecordModel
	5,  // 3: bookshelf.UpdateReadRecordsRequest.readRecords:type_name -> bookshelf.ReadRecordModelWithBookID
	5,  // 4: bookshelf.UpdateMWebReadRecordsRequest.readRecords:type_name -> bookshelf.ReadRecordModelWithBookID
	7,  // 5: bookshelf.BookshelfListResponse.bookshelves:type_name -> bookshelf.BookshelfModel
	5,  // 6: bookshelf.UpdateUserRecordReq.readRecords:type_name -> bookshelf.ReadRecordModelWithBookID
	4,  // 7: bookshelf.UserShelfReadModel.readRecord:type_name -> bookshelf.ReadRecordModel
	18, // 8: bookshelf.GetUserReadRecordListResponse.recoredList:type_name -> bookshelf.UserShelfReadModel
	18, // 9: bookshelf.GetUserShelfReadResponse.record:type_name -> bookshelf.UserShelfReadModel
	4,  // 10: bookshelf.UpdateUserShelfReadRequest.readRecord:type_name -> bookshelf.ReadRecordModel
	6,  // 11: bookshelf.UserShelfBook.readRecord:type_name -> bookshelf.ReadRecordModelWithBookAndUpdate
	22, // 12: bookshelf.UserShelfBooksResponse.books:type_name -> bookshelf.UserShelfBook
	22, // 13: bookshelf.GetBookShelfResponse.shelfBooks:type_name -> bookshelf.UserShelfBook
	22, // 14: bookshelf.GetBookShelfResponse.feedingBooks:type_name -> bookshelf.UserShelfBook
	25, // 15: bookshelf.GetBookShelfV3Response.shelfBooks:type_name -> bookshelf.BookShelfV3
	36, // 16: bookshelf.ListBookShelfResponse.user_shelves:type_name -> bookshelf.ListBookShelfResponse.UserShelvesEntry
	7,  // 17: bookshelf.UserBookShelf.bookshelves:type_name -> bookshelf.BookshelfModel
	35, // 18: bookshelf.ListBookShelfResponse.UserShelvesEntry.value:type_name -> bookshelf.UserBookShelf
	0,  // 19: bookshelf.Bookshelf.GetBookshelf:input_type -> bookshelf.BookshelfRequest
	33, // 20: bookshelf.Bookshelf.ListBookShelf:input_type -> bookshelf.ListBookShelfRequest
	1,  // 21: bookshelf.Bookshelf.AddBookshelf:input_type -> bookshelf.AddBookshelfRequest
	2,  // 22: bookshelf.Bookshelf.UpdateBookshelf:input_type -> bookshelf.UpdateBookshelfRequest
	0,  // 23: bookshelf.Bookshelf.DelBookshelf:input_type -> bookshelf.BookshelfRequest
	8,  // 24: bookshelf.Bookshelf.UpdateReadRecord:input_type -> bookshelf.UpdateReadRecordRequest
	9,  // 25: bookshelf.Bookshelf.UpdateReadRecords:input_type -> bookshelf.UpdateReadRecordsRequest
	12, // 26: bookshelf.Bookshelf.FilterBooksInMongo:input_type -> bookshelf.FilterBooksRequest
	15, // 27: bookshelf.Bookshelf.AddUserShelfRead:input_type -> bookshelf.AddUserReadRecordRequest
	17, // 28: bookshelf.Bookshelf.GetUserShelfReadsByUserId:input_type -> bookshelf.GetUserShelfReadListRequest
	16, // 29: bookshelf.Bookshelf.GetUserShelfReadByUserIdAndBook:input_type -> bookshelf.GetUserShelfReadRequest
	21, // 30: bookshelf.Bookshelf.UpdateUserShelfRead:input_type -> bookshelf.UpdateUserShelfReadRequest
	9,  // 31: bookshelf.Bookshelf.UpdateUserRecord:input_type -> bookshelf.UpdateReadRecordsRequest
	10, // 32: bookshelf.Bookshelf.UpdateMWebRecord:input_type -> bookshelf.UpdateMWebReadRecordsRequest
	29, // 33: bookshelf.Bookshelf.GetBookshelfInfo:input_type -> bookshelf.UserIdAndBookIdsAndAppName
	27, // 34: bookshelf.Bookshelf.RemoveBooks4Feeding:input_type -> bookshelf.UserIdAndBookIds
	29, // 35: bookshelf.Bookshelf.RemoveBooks4FeedingV2:input_type -> bookshelf.UserIdAndBookIdsAndAppName
	29, // 36: bookshelf.Bookshelf.RemoveBooks4FeedingV3:input_type -> bookshelf.UserIdAndBookIdsAndAppName
	27, // 37: bookshelf.Bookshelf.RemoveBooks4Shelf:input_type -> bookshelf.UserIdAndBookIds
	29, // 38: bookshelf.Bookshelf.RemoveBooks4ShelfV2:input_type -> bookshelf.UserIdAndBookIdsAndAppName
	29, // 39: bookshelf.Bookshelf.RemoveBooks4ShelfV3:input_type -> bookshelf.UserIdAndBookIdsAndAppName
	27, // 40: bookshelf.Bookshelf.AddBook2Feeding:input_type -> bookshelf.UserIdAndBookIds
	29, // 41: bookshelf.Bookshelf.AddBook2FeedingV2:input_type -> bookshelf.UserIdAndBookIdsAndAppName
	29, // 42: bookshelf.Bookshelf.AddBook2FeedingV3:input_type -> bookshelf.UserIdAndBookIdsAndAppName
	28, // 43: bookshelf.Bookshelf.AddBook2Shelf:input_type -> bookshelf.UserIdAndBookIdsAndType
	29, // 44: bookshelf.Bookshelf.AddBook2ShelfV2:input_type -> bookshelf.UserIdAndBookIdsAndAppName
	29, // 45: bookshelf.Bookshelf.AddBook2ShelfV3:input_type -> bookshelf.UserIdAndBookIdsAndAppName
	24, // 46: bookshelf.Bookshelf.GetBookShelf:input_type -> bookshelf.UserId
	26, // 47: bookshelf.Bookshelf.GetBookShelfV2:input_type -> bookshelf.UserIdAndAppName
	26, // 48: bookshelf.Bookshelf.GetBookShelfV3:input_type -> bookshelf.UserIdAndAppName
	11, // 49: bookshelf.Bookshelf.GetBookshelf:output_type -> bookshelf.BookshelfListResponse
	34, // 50: bookshelf.Bookshelf.ListBookShelf:output_type -> bookshelf.ListBookShelfResponse
	3,  // 51: bookshelf.Bookshelf.AddBookshelf:output_type -> bookshelf.NormalResponse
	3,  // 52: bookshelf.Bookshelf.UpdateBookshelf:output_type -> bookshelf.NormalResponse
	3,  // 53: bookshelf.Bookshelf.DelBookshelf:output_type -> bookshelf.NormalResponse
	3,  // 54: bookshelf.Bookshelf.UpdateReadRecord:output_type -> bookshelf.NormalResponse
	3,  // 55: bookshelf.Bookshelf.UpdateReadRecords:output_type -> bookshelf.NormalResponse
	13, // 56: bookshelf.Bookshelf.FilterBooksInMongo:output_type -> bookshelf.FilterBooksResponse
	3,  // 57: bookshelf.Bookshelf.AddUserShelfRead:output_type -> bookshelf.NormalResponse
	19, // 58: bookshelf.Bookshelf.GetUserShelfReadsByUserId:output_type -> bookshelf.GetUserReadRecordListResponse
	20, // 59: bookshelf.Bookshelf.GetUserShelfReadByUserIdAndBook:output_type -> bookshelf.GetUserShelfReadResponse
	3,  // 60: bookshelf.Bookshelf.UpdateUserShelfRead:output_type -> bookshelf.NormalResponse
	3,  // 61: bookshelf.Bookshelf.UpdateUserRecord:output_type -> bookshelf.NormalResponse
	3,  // 62: bookshelf.Bookshelf.UpdateMWebRecord:output_type -> bookshelf.NormalResponse
	23, // 63: bookshelf.Bookshelf.GetBookshelfInfo:output_type -> bookshelf.UserShelfBooksResponse
	30, // 64: bookshelf.Bookshelf.RemoveBooks4Feeding:output_type -> bookshelf.TimeResponse
	30, // 65: bookshelf.Bookshelf.RemoveBooks4FeedingV2:output_type -> bookshelf.TimeResponse
	30, // 66: bookshelf.Bookshelf.RemoveBooks4FeedingV3:output_type -> bookshelf.TimeResponse
	30, // 67: bookshelf.Bookshelf.RemoveBooks4Shelf:output_type -> bookshelf.TimeResponse
	30, // 68: bookshelf.Bookshelf.RemoveBooks4ShelfV2:output_type -> bookshelf.TimeResponse
	30, // 69: bookshelf.Bookshelf.RemoveBooks4ShelfV3:output_type -> bookshelf.TimeResponse
	30, // 70: bookshelf.Bookshelf.AddBook2Feeding:output_type -> bookshelf.TimeResponse
	30, // 71: bookshelf.Bookshelf.AddBook2FeedingV2:output_type -> bookshelf.TimeResponse
	30, // 72: bookshelf.Bookshelf.AddBook2FeedingV3:output_type -> bookshelf.TimeResponse
	30, // 73: bookshelf.Bookshelf.AddBook2Shelf:output_type -> bookshelf.TimeResponse
	30, // 74: bookshelf.Bookshelf.AddBook2ShelfV2:output_type -> bookshelf.TimeResponse
	30, // 75: bookshelf.Bookshelf.AddBook2ShelfV3:output_type -> bookshelf.TimeResponse
	31, // 76: bookshelf.Bookshelf.GetBookShelf:output_type -> bookshelf.GetBookShelfResponse
	31, // 77: bookshelf.Bookshelf.GetBookShelfV2:output_type -> bookshelf.GetBookShelfResponse
	32, // 78: bookshelf.Bookshelf.GetBookShelfV3:output_type -> bookshelf.GetBookShelfV3Response
	49, // [49:79] is the sub-list for method output_type
	19, // [19:49] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_proto_bookshelf_bookshelfsvc_proto_init() }
func file_proto_bookshelf_bookshelfsvc_proto_init() {
	if File_proto_bookshelf_bookshelfsvc_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_bookshelf_bookshelfsvc_proto_rawDesc), len(file_proto_bookshelf_bookshelfsvc_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   37,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_bookshelf_bookshelfsvc_proto_goTypes,
		DependencyIndexes: file_proto_bookshelf_bookshelfsvc_proto_depIdxs,
		MessageInfos:      file_proto_bookshelf_bookshelfsvc_proto_msgTypes,
	}.Build()
	File_proto_bookshelf_bookshelfsvc_proto = out.File
	file_proto_bookshelf_bookshelfsvc_proto_goTypes = nil
	file_proto_bookshelf_bookshelfsvc_proto_depIdxs = nil
}
