// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.30.2
// source: proto/bookshelf/bookshelfsvc.proto

package bookshelf

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Bookshelf_GetBookshelf_FullMethodName                    = "/bookshelf.Bookshelf/GetBookshelf"
	Bookshelf_ListBookShelf_FullMethodName                   = "/bookshelf.Bookshelf/ListBookShelf"
	Bookshelf_AddBookshelf_FullMethodName                    = "/bookshelf.Bookshelf/AddBookshelf"
	Bookshelf_UpdateBookshelf_FullMethodName                 = "/bookshelf.Bookshelf/UpdateBookshelf"
	Bookshelf_DelBookshelf_FullMethodName                    = "/bookshelf.Bookshelf/DelBookshelf"
	Bookshelf_UpdateReadRecord_FullMethodName                = "/bookshelf.Bookshelf/UpdateReadRecord"
	Bookshelf_UpdateReadRecords_FullMethodName               = "/bookshelf.Bookshelf/UpdateReadRecords"
	Bookshelf_FilterBooksInMongo_FullMethodName              = "/bookshelf.Bookshelf/FilterBooksInMongo"
	Bookshelf_AddUserShelfRead_FullMethodName                = "/bookshelf.Bookshelf/AddUserShelfRead"
	Bookshelf_GetUserShelfReadsByUserId_FullMethodName       = "/bookshelf.Bookshelf/GetUserShelfReadsByUserId"
	Bookshelf_GetUserShelfReadByUserIdAndBook_FullMethodName = "/bookshelf.Bookshelf/GetUserShelfReadByUserIdAndBook"
	Bookshelf_UpdateUserShelfRead_FullMethodName             = "/bookshelf.Bookshelf/UpdateUserShelfRead"
	Bookshelf_UpdateUserRecord_FullMethodName                = "/bookshelf.Bookshelf/UpdateUserRecord"
	Bookshelf_UpdateMWebRecord_FullMethodName                = "/bookshelf.Bookshelf/UpdateMWebRecord"
	Bookshelf_GetBookshelfInfo_FullMethodName                = "/bookshelf.Bookshelf/GetBookshelfInfo"
	Bookshelf_RemoveBooks4Feeding_FullMethodName             = "/bookshelf.Bookshelf/RemoveBooks4Feeding"
	Bookshelf_RemoveBooks4FeedingV2_FullMethodName           = "/bookshelf.Bookshelf/RemoveBooks4FeedingV2"
	Bookshelf_RemoveBooks4FeedingV3_FullMethodName           = "/bookshelf.Bookshelf/RemoveBooks4FeedingV3"
	Bookshelf_RemoveBooks4Shelf_FullMethodName               = "/bookshelf.Bookshelf/RemoveBooks4Shelf"
	Bookshelf_RemoveBooks4ShelfV2_FullMethodName             = "/bookshelf.Bookshelf/RemoveBooks4ShelfV2"
	Bookshelf_RemoveBooks4ShelfV3_FullMethodName             = "/bookshelf.Bookshelf/RemoveBooks4ShelfV3"
	Bookshelf_AddBook2Feeding_FullMethodName                 = "/bookshelf.Bookshelf/AddBook2Feeding"
	Bookshelf_AddBook2FeedingV2_FullMethodName               = "/bookshelf.Bookshelf/AddBook2FeedingV2"
	Bookshelf_AddBook2FeedingV3_FullMethodName               = "/bookshelf.Bookshelf/AddBook2FeedingV3"
	Bookshelf_AddBook2Shelf_FullMethodName                   = "/bookshelf.Bookshelf/AddBook2Shelf"
	Bookshelf_AddBook2ShelfV2_FullMethodName                 = "/bookshelf.Bookshelf/AddBook2ShelfV2"
	Bookshelf_AddBook2ShelfV3_FullMethodName                 = "/bookshelf.Bookshelf/AddBook2ShelfV3"
	Bookshelf_GetBookShelf_FullMethodName                    = "/bookshelf.Bookshelf/GetBookShelf"
	Bookshelf_GetBookShelfV2_FullMethodName                  = "/bookshelf.Bookshelf/GetBookShelfV2"
	Bookshelf_GetBookShelfV3_FullMethodName                  = "/bookshelf.Bookshelf/GetBookShelfV3"
)

// BookshelfClient is the client API for Bookshelf service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BookshelfClient interface {
	GetBookshelf(ctx context.Context, in *BookshelfRequest, opts ...grpc.CallOption) (*BookshelfListResponse, error)
	ListBookShelf(ctx context.Context, in *ListBookShelfRequest, opts ...grpc.CallOption) (*ListBookShelfResponse, error)
	AddBookshelf(ctx context.Context, in *AddBookshelfRequest, opts ...grpc.CallOption) (*NormalResponse, error)
	UpdateBookshelf(ctx context.Context, in *UpdateBookshelfRequest, opts ...grpc.CallOption) (*NormalResponse, error)
	DelBookshelf(ctx context.Context, in *BookshelfRequest, opts ...grpc.CallOption) (*NormalResponse, error)
	UpdateReadRecord(ctx context.Context, in *UpdateReadRecordRequest, opts ...grpc.CallOption) (*NormalResponse, error)
	UpdateReadRecords(ctx context.Context, in *UpdateReadRecordsRequest, opts ...grpc.CallOption) (*NormalResponse, error)
	FilterBooksInMongo(ctx context.Context, in *FilterBooksRequest, opts ...grpc.CallOption) (*FilterBooksResponse, error)
	AddUserShelfRead(ctx context.Context, in *AddUserReadRecordRequest, opts ...grpc.CallOption) (*NormalResponse, error)
	GetUserShelfReadsByUserId(ctx context.Context, in *GetUserShelfReadListRequest, opts ...grpc.CallOption) (*GetUserReadRecordListResponse, error)
	GetUserShelfReadByUserIdAndBook(ctx context.Context, in *GetUserShelfReadRequest, opts ...grpc.CallOption) (*GetUserShelfReadResponse, error)
	UpdateUserShelfRead(ctx context.Context, in *UpdateUserShelfReadRequest, opts ...grpc.CallOption) (*NormalResponse, error)
	UpdateUserRecord(ctx context.Context, in *UpdateReadRecordsRequest, opts ...grpc.CallOption) (*NormalResponse, error)
	UpdateMWebRecord(ctx context.Context, in *UpdateMWebReadRecordsRequest, opts ...grpc.CallOption) (*NormalResponse, error)
	GetBookshelfInfo(ctx context.Context, in *UserIdAndBookIdsAndAppName, opts ...grpc.CallOption) (*UserShelfBooksResponse, error)
	RemoveBooks4Feeding(ctx context.Context, in *UserIdAndBookIds, opts ...grpc.CallOption) (*TimeResponse, error)
	RemoveBooks4FeedingV2(ctx context.Context, in *UserIdAndBookIdsAndAppName, opts ...grpc.CallOption) (*TimeResponse, error)
	RemoveBooks4FeedingV3(ctx context.Context, in *UserIdAndBookIdsAndAppName, opts ...grpc.CallOption) (*TimeResponse, error)
	RemoveBooks4Shelf(ctx context.Context, in *UserIdAndBookIds, opts ...grpc.CallOption) (*TimeResponse, error)
	RemoveBooks4ShelfV2(ctx context.Context, in *UserIdAndBookIdsAndAppName, opts ...grpc.CallOption) (*TimeResponse, error)
	RemoveBooks4ShelfV3(ctx context.Context, in *UserIdAndBookIdsAndAppName, opts ...grpc.CallOption) (*TimeResponse, error)
	AddBook2Feeding(ctx context.Context, in *UserIdAndBookIds, opts ...grpc.CallOption) (*TimeResponse, error)
	AddBook2FeedingV2(ctx context.Context, in *UserIdAndBookIdsAndAppName, opts ...grpc.CallOption) (*TimeResponse, error)
	AddBook2FeedingV3(ctx context.Context, in *UserIdAndBookIdsAndAppName, opts ...grpc.CallOption) (*TimeResponse, error)
	AddBook2Shelf(ctx context.Context, in *UserIdAndBookIdsAndType, opts ...grpc.CallOption) (*TimeResponse, error)
	AddBook2ShelfV2(ctx context.Context, in *UserIdAndBookIdsAndAppName, opts ...grpc.CallOption) (*TimeResponse, error)
	AddBook2ShelfV3(ctx context.Context, in *UserIdAndBookIdsAndAppName, opts ...grpc.CallOption) (*TimeResponse, error)
	GetBookShelf(ctx context.Context, in *UserId, opts ...grpc.CallOption) (*GetBookShelfResponse, error)
	GetBookShelfV2(ctx context.Context, in *UserIdAndAppName, opts ...grpc.CallOption) (*GetBookShelfResponse, error)
	GetBookShelfV3(ctx context.Context, in *UserIdAndAppName, opts ...grpc.CallOption) (*GetBookShelfV3Response, error)
}

type bookshelfClient struct {
	cc grpc.ClientConnInterface
}

func NewBookshelfClient(cc grpc.ClientConnInterface) BookshelfClient {
	return &bookshelfClient{cc}
}

func (c *bookshelfClient) GetBookshelf(ctx context.Context, in *BookshelfRequest, opts ...grpc.CallOption) (*BookshelfListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BookshelfListResponse)
	err := c.cc.Invoke(ctx, Bookshelf_GetBookshelf_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookshelfClient) ListBookShelf(ctx context.Context, in *ListBookShelfRequest, opts ...grpc.CallOption) (*ListBookShelfResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListBookShelfResponse)
	err := c.cc.Invoke(ctx, Bookshelf_ListBookShelf_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookshelfClient) AddBookshelf(ctx context.Context, in *AddBookshelfRequest, opts ...grpc.CallOption) (*NormalResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NormalResponse)
	err := c.cc.Invoke(ctx, Bookshelf_AddBookshelf_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookshelfClient) UpdateBookshelf(ctx context.Context, in *UpdateBookshelfRequest, opts ...grpc.CallOption) (*NormalResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NormalResponse)
	err := c.cc.Invoke(ctx, Bookshelf_UpdateBookshelf_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookshelfClient) DelBookshelf(ctx context.Context, in *BookshelfRequest, opts ...grpc.CallOption) (*NormalResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NormalResponse)
	err := c.cc.Invoke(ctx, Bookshelf_DelBookshelf_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookshelfClient) UpdateReadRecord(ctx context.Context, in *UpdateReadRecordRequest, opts ...grpc.CallOption) (*NormalResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NormalResponse)
	err := c.cc.Invoke(ctx, Bookshelf_UpdateReadRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookshelfClient) UpdateReadRecords(ctx context.Context, in *UpdateReadRecordsRequest, opts ...grpc.CallOption) (*NormalResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NormalResponse)
	err := c.cc.Invoke(ctx, Bookshelf_UpdateReadRecords_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookshelfClient) FilterBooksInMongo(ctx context.Context, in *FilterBooksRequest, opts ...grpc.CallOption) (*FilterBooksResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FilterBooksResponse)
	err := c.cc.Invoke(ctx, Bookshelf_FilterBooksInMongo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookshelfClient) AddUserShelfRead(ctx context.Context, in *AddUserReadRecordRequest, opts ...grpc.CallOption) (*NormalResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NormalResponse)
	err := c.cc.Invoke(ctx, Bookshelf_AddUserShelfRead_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookshelfClient) GetUserShelfReadsByUserId(ctx context.Context, in *GetUserShelfReadListRequest, opts ...grpc.CallOption) (*GetUserReadRecordListResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserReadRecordListResponse)
	err := c.cc.Invoke(ctx, Bookshelf_GetUserShelfReadsByUserId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookshelfClient) GetUserShelfReadByUserIdAndBook(ctx context.Context, in *GetUserShelfReadRequest, opts ...grpc.CallOption) (*GetUserShelfReadResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserShelfReadResponse)
	err := c.cc.Invoke(ctx, Bookshelf_GetUserShelfReadByUserIdAndBook_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookshelfClient) UpdateUserShelfRead(ctx context.Context, in *UpdateUserShelfReadRequest, opts ...grpc.CallOption) (*NormalResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NormalResponse)
	err := c.cc.Invoke(ctx, Bookshelf_UpdateUserShelfRead_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookshelfClient) UpdateUserRecord(ctx context.Context, in *UpdateReadRecordsRequest, opts ...grpc.CallOption) (*NormalResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NormalResponse)
	err := c.cc.Invoke(ctx, Bookshelf_UpdateUserRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookshelfClient) UpdateMWebRecord(ctx context.Context, in *UpdateMWebReadRecordsRequest, opts ...grpc.CallOption) (*NormalResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NormalResponse)
	err := c.cc.Invoke(ctx, Bookshelf_UpdateMWebRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookshelfClient) GetBookshelfInfo(ctx context.Context, in *UserIdAndBookIdsAndAppName, opts ...grpc.CallOption) (*UserShelfBooksResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UserShelfBooksResponse)
	err := c.cc.Invoke(ctx, Bookshelf_GetBookshelfInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookshelfClient) RemoveBooks4Feeding(ctx context.Context, in *UserIdAndBookIds, opts ...grpc.CallOption) (*TimeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TimeResponse)
	err := c.cc.Invoke(ctx, Bookshelf_RemoveBooks4Feeding_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookshelfClient) RemoveBooks4FeedingV2(ctx context.Context, in *UserIdAndBookIdsAndAppName, opts ...grpc.CallOption) (*TimeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TimeResponse)
	err := c.cc.Invoke(ctx, Bookshelf_RemoveBooks4FeedingV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookshelfClient) RemoveBooks4FeedingV3(ctx context.Context, in *UserIdAndBookIdsAndAppName, opts ...grpc.CallOption) (*TimeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TimeResponse)
	err := c.cc.Invoke(ctx, Bookshelf_RemoveBooks4FeedingV3_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookshelfClient) RemoveBooks4Shelf(ctx context.Context, in *UserIdAndBookIds, opts ...grpc.CallOption) (*TimeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TimeResponse)
	err := c.cc.Invoke(ctx, Bookshelf_RemoveBooks4Shelf_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookshelfClient) RemoveBooks4ShelfV2(ctx context.Context, in *UserIdAndBookIdsAndAppName, opts ...grpc.CallOption) (*TimeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TimeResponse)
	err := c.cc.Invoke(ctx, Bookshelf_RemoveBooks4ShelfV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookshelfClient) RemoveBooks4ShelfV3(ctx context.Context, in *UserIdAndBookIdsAndAppName, opts ...grpc.CallOption) (*TimeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TimeResponse)
	err := c.cc.Invoke(ctx, Bookshelf_RemoveBooks4ShelfV3_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookshelfClient) AddBook2Feeding(ctx context.Context, in *UserIdAndBookIds, opts ...grpc.CallOption) (*TimeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TimeResponse)
	err := c.cc.Invoke(ctx, Bookshelf_AddBook2Feeding_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookshelfClient) AddBook2FeedingV2(ctx context.Context, in *UserIdAndBookIdsAndAppName, opts ...grpc.CallOption) (*TimeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TimeResponse)
	err := c.cc.Invoke(ctx, Bookshelf_AddBook2FeedingV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookshelfClient) AddBook2FeedingV3(ctx context.Context, in *UserIdAndBookIdsAndAppName, opts ...grpc.CallOption) (*TimeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TimeResponse)
	err := c.cc.Invoke(ctx, Bookshelf_AddBook2FeedingV3_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookshelfClient) AddBook2Shelf(ctx context.Context, in *UserIdAndBookIdsAndType, opts ...grpc.CallOption) (*TimeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TimeResponse)
	err := c.cc.Invoke(ctx, Bookshelf_AddBook2Shelf_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookshelfClient) AddBook2ShelfV2(ctx context.Context, in *UserIdAndBookIdsAndAppName, opts ...grpc.CallOption) (*TimeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TimeResponse)
	err := c.cc.Invoke(ctx, Bookshelf_AddBook2ShelfV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookshelfClient) AddBook2ShelfV3(ctx context.Context, in *UserIdAndBookIdsAndAppName, opts ...grpc.CallOption) (*TimeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TimeResponse)
	err := c.cc.Invoke(ctx, Bookshelf_AddBook2ShelfV3_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookshelfClient) GetBookShelf(ctx context.Context, in *UserId, opts ...grpc.CallOption) (*GetBookShelfResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBookShelfResponse)
	err := c.cc.Invoke(ctx, Bookshelf_GetBookShelf_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookshelfClient) GetBookShelfV2(ctx context.Context, in *UserIdAndAppName, opts ...grpc.CallOption) (*GetBookShelfResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBookShelfResponse)
	err := c.cc.Invoke(ctx, Bookshelf_GetBookShelfV2_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bookshelfClient) GetBookShelfV3(ctx context.Context, in *UserIdAndAppName, opts ...grpc.CallOption) (*GetBookShelfV3Response, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBookShelfV3Response)
	err := c.cc.Invoke(ctx, Bookshelf_GetBookShelfV3_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BookshelfServer is the server API for Bookshelf service.
// All implementations should embed UnimplementedBookshelfServer
// for forward compatibility.
type BookshelfServer interface {
	GetBookshelf(context.Context, *BookshelfRequest) (*BookshelfListResponse, error)
	ListBookShelf(context.Context, *ListBookShelfRequest) (*ListBookShelfResponse, error)
	AddBookshelf(context.Context, *AddBookshelfRequest) (*NormalResponse, error)
	UpdateBookshelf(context.Context, *UpdateBookshelfRequest) (*NormalResponse, error)
	DelBookshelf(context.Context, *BookshelfRequest) (*NormalResponse, error)
	UpdateReadRecord(context.Context, *UpdateReadRecordRequest) (*NormalResponse, error)
	UpdateReadRecords(context.Context, *UpdateReadRecordsRequest) (*NormalResponse, error)
	FilterBooksInMongo(context.Context, *FilterBooksRequest) (*FilterBooksResponse, error)
	AddUserShelfRead(context.Context, *AddUserReadRecordRequest) (*NormalResponse, error)
	GetUserShelfReadsByUserId(context.Context, *GetUserShelfReadListRequest) (*GetUserReadRecordListResponse, error)
	GetUserShelfReadByUserIdAndBook(context.Context, *GetUserShelfReadRequest) (*GetUserShelfReadResponse, error)
	UpdateUserShelfRead(context.Context, *UpdateUserShelfReadRequest) (*NormalResponse, error)
	UpdateUserRecord(context.Context, *UpdateReadRecordsRequest) (*NormalResponse, error)
	UpdateMWebRecord(context.Context, *UpdateMWebReadRecordsRequest) (*NormalResponse, error)
	GetBookshelfInfo(context.Context, *UserIdAndBookIdsAndAppName) (*UserShelfBooksResponse, error)
	RemoveBooks4Feeding(context.Context, *UserIdAndBookIds) (*TimeResponse, error)
	RemoveBooks4FeedingV2(context.Context, *UserIdAndBookIdsAndAppName) (*TimeResponse, error)
	RemoveBooks4FeedingV3(context.Context, *UserIdAndBookIdsAndAppName) (*TimeResponse, error)
	RemoveBooks4Shelf(context.Context, *UserIdAndBookIds) (*TimeResponse, error)
	RemoveBooks4ShelfV2(context.Context, *UserIdAndBookIdsAndAppName) (*TimeResponse, error)
	RemoveBooks4ShelfV3(context.Context, *UserIdAndBookIdsAndAppName) (*TimeResponse, error)
	AddBook2Feeding(context.Context, *UserIdAndBookIds) (*TimeResponse, error)
	AddBook2FeedingV2(context.Context, *UserIdAndBookIdsAndAppName) (*TimeResponse, error)
	AddBook2FeedingV3(context.Context, *UserIdAndBookIdsAndAppName) (*TimeResponse, error)
	AddBook2Shelf(context.Context, *UserIdAndBookIdsAndType) (*TimeResponse, error)
	AddBook2ShelfV2(context.Context, *UserIdAndBookIdsAndAppName) (*TimeResponse, error)
	AddBook2ShelfV3(context.Context, *UserIdAndBookIdsAndAppName) (*TimeResponse, error)
	GetBookShelf(context.Context, *UserId) (*GetBookShelfResponse, error)
	GetBookShelfV2(context.Context, *UserIdAndAppName) (*GetBookShelfResponse, error)
	GetBookShelfV3(context.Context, *UserIdAndAppName) (*GetBookShelfV3Response, error)
}

// UnimplementedBookshelfServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBookshelfServer struct{}

func (UnimplementedBookshelfServer) GetBookshelf(context.Context, *BookshelfRequest) (*BookshelfListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBookshelf not implemented")
}
func (UnimplementedBookshelfServer) ListBookShelf(context.Context, *ListBookShelfRequest) (*ListBookShelfResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBookShelf not implemented")
}
func (UnimplementedBookshelfServer) AddBookshelf(context.Context, *AddBookshelfRequest) (*NormalResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddBookshelf not implemented")
}
func (UnimplementedBookshelfServer) UpdateBookshelf(context.Context, *UpdateBookshelfRequest) (*NormalResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBookshelf not implemented")
}
func (UnimplementedBookshelfServer) DelBookshelf(context.Context, *BookshelfRequest) (*NormalResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DelBookshelf not implemented")
}
func (UnimplementedBookshelfServer) UpdateReadRecord(context.Context, *UpdateReadRecordRequest) (*NormalResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateReadRecord not implemented")
}
func (UnimplementedBookshelfServer) UpdateReadRecords(context.Context, *UpdateReadRecordsRequest) (*NormalResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateReadRecords not implemented")
}
func (UnimplementedBookshelfServer) FilterBooksInMongo(context.Context, *FilterBooksRequest) (*FilterBooksResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FilterBooksInMongo not implemented")
}
func (UnimplementedBookshelfServer) AddUserShelfRead(context.Context, *AddUserReadRecordRequest) (*NormalResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddUserShelfRead not implemented")
}
func (UnimplementedBookshelfServer) GetUserShelfReadsByUserId(context.Context, *GetUserShelfReadListRequest) (*GetUserReadRecordListResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserShelfReadsByUserId not implemented")
}
func (UnimplementedBookshelfServer) GetUserShelfReadByUserIdAndBook(context.Context, *GetUserShelfReadRequest) (*GetUserShelfReadResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserShelfReadByUserIdAndBook not implemented")
}
func (UnimplementedBookshelfServer) UpdateUserShelfRead(context.Context, *UpdateUserShelfReadRequest) (*NormalResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserShelfRead not implemented")
}
func (UnimplementedBookshelfServer) UpdateUserRecord(context.Context, *UpdateReadRecordsRequest) (*NormalResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserRecord not implemented")
}
func (UnimplementedBookshelfServer) UpdateMWebRecord(context.Context, *UpdateMWebReadRecordsRequest) (*NormalResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMWebRecord not implemented")
}
func (UnimplementedBookshelfServer) GetBookshelfInfo(context.Context, *UserIdAndBookIdsAndAppName) (*UserShelfBooksResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBookshelfInfo not implemented")
}
func (UnimplementedBookshelfServer) RemoveBooks4Feeding(context.Context, *UserIdAndBookIds) (*TimeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveBooks4Feeding not implemented")
}
func (UnimplementedBookshelfServer) RemoveBooks4FeedingV2(context.Context, *UserIdAndBookIdsAndAppName) (*TimeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveBooks4FeedingV2 not implemented")
}
func (UnimplementedBookshelfServer) RemoveBooks4FeedingV3(context.Context, *UserIdAndBookIdsAndAppName) (*TimeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveBooks4FeedingV3 not implemented")
}
func (UnimplementedBookshelfServer) RemoveBooks4Shelf(context.Context, *UserIdAndBookIds) (*TimeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveBooks4Shelf not implemented")
}
func (UnimplementedBookshelfServer) RemoveBooks4ShelfV2(context.Context, *UserIdAndBookIdsAndAppName) (*TimeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveBooks4ShelfV2 not implemented")
}
func (UnimplementedBookshelfServer) RemoveBooks4ShelfV3(context.Context, *UserIdAndBookIdsAndAppName) (*TimeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RemoveBooks4ShelfV3 not implemented")
}
func (UnimplementedBookshelfServer) AddBook2Feeding(context.Context, *UserIdAndBookIds) (*TimeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddBook2Feeding not implemented")
}
func (UnimplementedBookshelfServer) AddBook2FeedingV2(context.Context, *UserIdAndBookIdsAndAppName) (*TimeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddBook2FeedingV2 not implemented")
}
func (UnimplementedBookshelfServer) AddBook2FeedingV3(context.Context, *UserIdAndBookIdsAndAppName) (*TimeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddBook2FeedingV3 not implemented")
}
func (UnimplementedBookshelfServer) AddBook2Shelf(context.Context, *UserIdAndBookIdsAndType) (*TimeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddBook2Shelf not implemented")
}
func (UnimplementedBookshelfServer) AddBook2ShelfV2(context.Context, *UserIdAndBookIdsAndAppName) (*TimeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddBook2ShelfV2 not implemented")
}
func (UnimplementedBookshelfServer) AddBook2ShelfV3(context.Context, *UserIdAndBookIdsAndAppName) (*TimeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddBook2ShelfV3 not implemented")
}
func (UnimplementedBookshelfServer) GetBookShelf(context.Context, *UserId) (*GetBookShelfResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBookShelf not implemented")
}
func (UnimplementedBookshelfServer) GetBookShelfV2(context.Context, *UserIdAndAppName) (*GetBookShelfResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBookShelfV2 not implemented")
}
func (UnimplementedBookshelfServer) GetBookShelfV3(context.Context, *UserIdAndAppName) (*GetBookShelfV3Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBookShelfV3 not implemented")
}
func (UnimplementedBookshelfServer) testEmbeddedByValue() {}

// UnsafeBookshelfServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BookshelfServer will
// result in compilation errors.
type UnsafeBookshelfServer interface {
	mustEmbedUnimplementedBookshelfServer()
}

func RegisterBookshelfServer(s grpc.ServiceRegistrar, srv BookshelfServer) {
	// If the following call pancis, it indicates UnimplementedBookshelfServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Bookshelf_ServiceDesc, srv)
}

func _Bookshelf_GetBookshelf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BookshelfRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookshelfServer).GetBookshelf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bookshelf_GetBookshelf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookshelfServer).GetBookshelf(ctx, req.(*BookshelfRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bookshelf_ListBookShelf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBookShelfRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookshelfServer).ListBookShelf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bookshelf_ListBookShelf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookshelfServer).ListBookShelf(ctx, req.(*ListBookShelfRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bookshelf_AddBookshelf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBookshelfRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookshelfServer).AddBookshelf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bookshelf_AddBookshelf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookshelfServer).AddBookshelf(ctx, req.(*AddBookshelfRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bookshelf_UpdateBookshelf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBookshelfRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookshelfServer).UpdateBookshelf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bookshelf_UpdateBookshelf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookshelfServer).UpdateBookshelf(ctx, req.(*UpdateBookshelfRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bookshelf_DelBookshelf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BookshelfRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookshelfServer).DelBookshelf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bookshelf_DelBookshelf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookshelfServer).DelBookshelf(ctx, req.(*BookshelfRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bookshelf_UpdateReadRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateReadRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookshelfServer).UpdateReadRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bookshelf_UpdateReadRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookshelfServer).UpdateReadRecord(ctx, req.(*UpdateReadRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bookshelf_UpdateReadRecords_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateReadRecordsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookshelfServer).UpdateReadRecords(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bookshelf_UpdateReadRecords_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookshelfServer).UpdateReadRecords(ctx, req.(*UpdateReadRecordsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bookshelf_FilterBooksInMongo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FilterBooksRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookshelfServer).FilterBooksInMongo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bookshelf_FilterBooksInMongo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookshelfServer).FilterBooksInMongo(ctx, req.(*FilterBooksRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bookshelf_AddUserShelfRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserReadRecordRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookshelfServer).AddUserShelfRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bookshelf_AddUserShelfRead_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookshelfServer).AddUserShelfRead(ctx, req.(*AddUserReadRecordRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bookshelf_GetUserShelfReadsByUserId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserShelfReadListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookshelfServer).GetUserShelfReadsByUserId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bookshelf_GetUserShelfReadsByUserId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookshelfServer).GetUserShelfReadsByUserId(ctx, req.(*GetUserShelfReadListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bookshelf_GetUserShelfReadByUserIdAndBook_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserShelfReadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookshelfServer).GetUserShelfReadByUserIdAndBook(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bookshelf_GetUserShelfReadByUserIdAndBook_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookshelfServer).GetUserShelfReadByUserIdAndBook(ctx, req.(*GetUserShelfReadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bookshelf_UpdateUserShelfRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserShelfReadRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookshelfServer).UpdateUserShelfRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bookshelf_UpdateUserShelfRead_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookshelfServer).UpdateUserShelfRead(ctx, req.(*UpdateUserShelfReadRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bookshelf_UpdateUserRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateReadRecordsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookshelfServer).UpdateUserRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bookshelf_UpdateUserRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookshelfServer).UpdateUserRecord(ctx, req.(*UpdateReadRecordsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bookshelf_UpdateMWebRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMWebReadRecordsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookshelfServer).UpdateMWebRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bookshelf_UpdateMWebRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookshelfServer).UpdateMWebRecord(ctx, req.(*UpdateMWebReadRecordsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bookshelf_GetBookshelfInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserIdAndBookIdsAndAppName)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookshelfServer).GetBookshelfInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bookshelf_GetBookshelfInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookshelfServer).GetBookshelfInfo(ctx, req.(*UserIdAndBookIdsAndAppName))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bookshelf_RemoveBooks4Feeding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserIdAndBookIds)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookshelfServer).RemoveBooks4Feeding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bookshelf_RemoveBooks4Feeding_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookshelfServer).RemoveBooks4Feeding(ctx, req.(*UserIdAndBookIds))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bookshelf_RemoveBooks4FeedingV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserIdAndBookIdsAndAppName)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookshelfServer).RemoveBooks4FeedingV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bookshelf_RemoveBooks4FeedingV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookshelfServer).RemoveBooks4FeedingV2(ctx, req.(*UserIdAndBookIdsAndAppName))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bookshelf_RemoveBooks4FeedingV3_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserIdAndBookIdsAndAppName)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookshelfServer).RemoveBooks4FeedingV3(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bookshelf_RemoveBooks4FeedingV3_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookshelfServer).RemoveBooks4FeedingV3(ctx, req.(*UserIdAndBookIdsAndAppName))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bookshelf_RemoveBooks4Shelf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserIdAndBookIds)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookshelfServer).RemoveBooks4Shelf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bookshelf_RemoveBooks4Shelf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookshelfServer).RemoveBooks4Shelf(ctx, req.(*UserIdAndBookIds))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bookshelf_RemoveBooks4ShelfV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserIdAndBookIdsAndAppName)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookshelfServer).RemoveBooks4ShelfV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bookshelf_RemoveBooks4ShelfV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookshelfServer).RemoveBooks4ShelfV2(ctx, req.(*UserIdAndBookIdsAndAppName))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bookshelf_RemoveBooks4ShelfV3_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserIdAndBookIdsAndAppName)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookshelfServer).RemoveBooks4ShelfV3(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bookshelf_RemoveBooks4ShelfV3_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookshelfServer).RemoveBooks4ShelfV3(ctx, req.(*UserIdAndBookIdsAndAppName))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bookshelf_AddBook2Feeding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserIdAndBookIds)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookshelfServer).AddBook2Feeding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bookshelf_AddBook2Feeding_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookshelfServer).AddBook2Feeding(ctx, req.(*UserIdAndBookIds))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bookshelf_AddBook2FeedingV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserIdAndBookIdsAndAppName)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookshelfServer).AddBook2FeedingV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bookshelf_AddBook2FeedingV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookshelfServer).AddBook2FeedingV2(ctx, req.(*UserIdAndBookIdsAndAppName))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bookshelf_AddBook2FeedingV3_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserIdAndBookIdsAndAppName)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookshelfServer).AddBook2FeedingV3(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bookshelf_AddBook2FeedingV3_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookshelfServer).AddBook2FeedingV3(ctx, req.(*UserIdAndBookIdsAndAppName))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bookshelf_AddBook2Shelf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserIdAndBookIdsAndType)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookshelfServer).AddBook2Shelf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bookshelf_AddBook2Shelf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookshelfServer).AddBook2Shelf(ctx, req.(*UserIdAndBookIdsAndType))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bookshelf_AddBook2ShelfV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserIdAndBookIdsAndAppName)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookshelfServer).AddBook2ShelfV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bookshelf_AddBook2ShelfV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookshelfServer).AddBook2ShelfV2(ctx, req.(*UserIdAndBookIdsAndAppName))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bookshelf_AddBook2ShelfV3_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserIdAndBookIdsAndAppName)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookshelfServer).AddBook2ShelfV3(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bookshelf_AddBook2ShelfV3_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookshelfServer).AddBook2ShelfV3(ctx, req.(*UserIdAndBookIdsAndAppName))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bookshelf_GetBookShelf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserId)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookshelfServer).GetBookShelf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bookshelf_GetBookShelf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookshelfServer).GetBookShelf(ctx, req.(*UserId))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bookshelf_GetBookShelfV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserIdAndAppName)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookshelfServer).GetBookShelfV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bookshelf_GetBookShelfV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookshelfServer).GetBookShelfV2(ctx, req.(*UserIdAndAppName))
	}
	return interceptor(ctx, in, info, handler)
}

func _Bookshelf_GetBookShelfV3_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserIdAndAppName)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookshelfServer).GetBookShelfV3(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Bookshelf_GetBookShelfV3_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookshelfServer).GetBookShelfV3(ctx, req.(*UserIdAndAppName))
	}
	return interceptor(ctx, in, info, handler)
}

// Bookshelf_ServiceDesc is the grpc.ServiceDesc for Bookshelf service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Bookshelf_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "bookshelf.Bookshelf",
	HandlerType: (*BookshelfServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetBookshelf",
			Handler:    _Bookshelf_GetBookshelf_Handler,
		},
		{
			MethodName: "ListBookShelf",
			Handler:    _Bookshelf_ListBookShelf_Handler,
		},
		{
			MethodName: "AddBookshelf",
			Handler:    _Bookshelf_AddBookshelf_Handler,
		},
		{
			MethodName: "UpdateBookshelf",
			Handler:    _Bookshelf_UpdateBookshelf_Handler,
		},
		{
			MethodName: "DelBookshelf",
			Handler:    _Bookshelf_DelBookshelf_Handler,
		},
		{
			MethodName: "UpdateReadRecord",
			Handler:    _Bookshelf_UpdateReadRecord_Handler,
		},
		{
			MethodName: "UpdateReadRecords",
			Handler:    _Bookshelf_UpdateReadRecords_Handler,
		},
		{
			MethodName: "FilterBooksInMongo",
			Handler:    _Bookshelf_FilterBooksInMongo_Handler,
		},
		{
			MethodName: "AddUserShelfRead",
			Handler:    _Bookshelf_AddUserShelfRead_Handler,
		},
		{
			MethodName: "GetUserShelfReadsByUserId",
			Handler:    _Bookshelf_GetUserShelfReadsByUserId_Handler,
		},
		{
			MethodName: "GetUserShelfReadByUserIdAndBook",
			Handler:    _Bookshelf_GetUserShelfReadByUserIdAndBook_Handler,
		},
		{
			MethodName: "UpdateUserShelfRead",
			Handler:    _Bookshelf_UpdateUserShelfRead_Handler,
		},
		{
			MethodName: "UpdateUserRecord",
			Handler:    _Bookshelf_UpdateUserRecord_Handler,
		},
		{
			MethodName: "UpdateMWebRecord",
			Handler:    _Bookshelf_UpdateMWebRecord_Handler,
		},
		{
			MethodName: "GetBookshelfInfo",
			Handler:    _Bookshelf_GetBookshelfInfo_Handler,
		},
		{
			MethodName: "RemoveBooks4Feeding",
			Handler:    _Bookshelf_RemoveBooks4Feeding_Handler,
		},
		{
			MethodName: "RemoveBooks4FeedingV2",
			Handler:    _Bookshelf_RemoveBooks4FeedingV2_Handler,
		},
		{
			MethodName: "RemoveBooks4FeedingV3",
			Handler:    _Bookshelf_RemoveBooks4FeedingV3_Handler,
		},
		{
			MethodName: "RemoveBooks4Shelf",
			Handler:    _Bookshelf_RemoveBooks4Shelf_Handler,
		},
		{
			MethodName: "RemoveBooks4ShelfV2",
			Handler:    _Bookshelf_RemoveBooks4ShelfV2_Handler,
		},
		{
			MethodName: "RemoveBooks4ShelfV3",
			Handler:    _Bookshelf_RemoveBooks4ShelfV3_Handler,
		},
		{
			MethodName: "AddBook2Feeding",
			Handler:    _Bookshelf_AddBook2Feeding_Handler,
		},
		{
			MethodName: "AddBook2FeedingV2",
			Handler:    _Bookshelf_AddBook2FeedingV2_Handler,
		},
		{
			MethodName: "AddBook2FeedingV3",
			Handler:    _Bookshelf_AddBook2FeedingV3_Handler,
		},
		{
			MethodName: "AddBook2Shelf",
			Handler:    _Bookshelf_AddBook2Shelf_Handler,
		},
		{
			MethodName: "AddBook2ShelfV2",
			Handler:    _Bookshelf_AddBook2ShelfV2_Handler,
		},
		{
			MethodName: "AddBook2ShelfV3",
			Handler:    _Bookshelf_AddBook2ShelfV3_Handler,
		},
		{
			MethodName: "GetBookShelf",
			Handler:    _Bookshelf_GetBookShelf_Handler,
		},
		{
			MethodName: "GetBookShelfV2",
			Handler:    _Bookshelf_GetBookShelfV2_Handler,
		},
		{
			MethodName: "GetBookShelfV3",
			Handler:    _Bookshelf_GetBookShelfV3_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/bookshelf/bookshelfsvc.proto",
}
