// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.30.2
// source: proto/purchase/purchase.proto

package purchase

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Purchase_PurchaseChapter_FullMethodName       = "/purchase.Purchase/PurchaseChapter"
	Purchase_PurchaseMonthly_FullMethodName       = "/purchase.Purchase/PurchaseMonthly"
	Purchase_PurchaseVip_FullMethodName           = "/purchase.Purchase/PurchaseVip"
	Purchase_GetPurchaseOrders_FullMethodName     = "/purchase.Purchase/GetPurchaseOrders"
	Purchase_GetVipMonthlyOrders_FullMethodName   = "/purchase.Purchase/GetVipMonthlyOrders"
	Purchase_CheckChapterPurchased_FullMethodName = "/purchase.Purchase/CheckChapterPurchased"
	Purchase_CheckVipStatus_FullMethodName        = "/purchase.Purchase/CheckVipStatus"
	Purchase_CheckMonthlyStatus_FullMethodName    = "/purchase.Purchase/CheckMonthlyStatus"
	Purchase_GetPurchasedChapters_FullMethodName  = "/purchase.Purchase/GetPurchasedChapters"
)

// PurchaseClient is the client API for Purchase service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PurchaseClient interface {
	// 购买章节
	PurchaseChapter(ctx context.Context, in *PurchaseChapterReq, opts ...grpc.CallOption) (*PurchaseChapterResp, error)
	// 购买包月
	PurchaseMonthly(ctx context.Context, in *PurchaseMonthlyReq, opts ...grpc.CallOption) (*PurchaseMonthlyResp, error)
	// 购买VIP
	PurchaseVip(ctx context.Context, in *PurchaseVipReq, opts ...grpc.CallOption) (*PurchaseVipResp, error)
	// 获取购买订单列表
	GetPurchaseOrders(ctx context.Context, in *GetPurchaseOrdersReq, opts ...grpc.CallOption) (*GetPurchaseOrdersResp, error)
	// 获取VIP/包月订单列表
	GetVipMonthlyOrders(ctx context.Context, in *GetVipMonthlyOrdersReq, opts ...grpc.CallOption) (*GetVipMonthlyOrdersResp, error)
	// 检查章节购买状态
	CheckChapterPurchased(ctx context.Context, in *CheckChapterPurchasedReq, opts ...grpc.CallOption) (*CheckChapterPurchasedResp, error)
	// 检查VIP状态
	CheckVipStatus(ctx context.Context, in *CheckVipStatusReq, opts ...grpc.CallOption) (*CheckVipStatusResp, error)
	// 检查包月状态
	CheckMonthlyStatus(ctx context.Context, in *CheckMonthlyStatusReq, opts ...grpc.CallOption) (*CheckMonthlyStatusResp, error)
	// 获取用户已购买的章节列表
	GetPurchasedChapters(ctx context.Context, in *GetPurchasedChaptersReq, opts ...grpc.CallOption) (*GetPurchasedChaptersResp, error)
}

type purchaseClient struct {
	cc grpc.ClientConnInterface
}

func NewPurchaseClient(cc grpc.ClientConnInterface) PurchaseClient {
	return &purchaseClient{cc}
}

func (c *purchaseClient) PurchaseChapter(ctx context.Context, in *PurchaseChapterReq, opts ...grpc.CallOption) (*PurchaseChapterResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PurchaseChapterResp)
	err := c.cc.Invoke(ctx, Purchase_PurchaseChapter_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *purchaseClient) PurchaseMonthly(ctx context.Context, in *PurchaseMonthlyReq, opts ...grpc.CallOption) (*PurchaseMonthlyResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PurchaseMonthlyResp)
	err := c.cc.Invoke(ctx, Purchase_PurchaseMonthly_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *purchaseClient) PurchaseVip(ctx context.Context, in *PurchaseVipReq, opts ...grpc.CallOption) (*PurchaseVipResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PurchaseVipResp)
	err := c.cc.Invoke(ctx, Purchase_PurchaseVip_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *purchaseClient) GetPurchaseOrders(ctx context.Context, in *GetPurchaseOrdersReq, opts ...grpc.CallOption) (*GetPurchaseOrdersResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPurchaseOrdersResp)
	err := c.cc.Invoke(ctx, Purchase_GetPurchaseOrders_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *purchaseClient) GetVipMonthlyOrders(ctx context.Context, in *GetVipMonthlyOrdersReq, opts ...grpc.CallOption) (*GetVipMonthlyOrdersResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetVipMonthlyOrdersResp)
	err := c.cc.Invoke(ctx, Purchase_GetVipMonthlyOrders_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *purchaseClient) CheckChapterPurchased(ctx context.Context, in *CheckChapterPurchasedReq, opts ...grpc.CallOption) (*CheckChapterPurchasedResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckChapterPurchasedResp)
	err := c.cc.Invoke(ctx, Purchase_CheckChapterPurchased_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *purchaseClient) CheckVipStatus(ctx context.Context, in *CheckVipStatusReq, opts ...grpc.CallOption) (*CheckVipStatusResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckVipStatusResp)
	err := c.cc.Invoke(ctx, Purchase_CheckVipStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *purchaseClient) CheckMonthlyStatus(ctx context.Context, in *CheckMonthlyStatusReq, opts ...grpc.CallOption) (*CheckMonthlyStatusResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckMonthlyStatusResp)
	err := c.cc.Invoke(ctx, Purchase_CheckMonthlyStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *purchaseClient) GetPurchasedChapters(ctx context.Context, in *GetPurchasedChaptersReq, opts ...grpc.CallOption) (*GetPurchasedChaptersResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPurchasedChaptersResp)
	err := c.cc.Invoke(ctx, Purchase_GetPurchasedChapters_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PurchaseServer is the server API for Purchase service.
// All implementations should embed UnimplementedPurchaseServer
// for forward compatibility.
type PurchaseServer interface {
	// 购买章节
	PurchaseChapter(context.Context, *PurchaseChapterReq) (*PurchaseChapterResp, error)
	// 购买包月
	PurchaseMonthly(context.Context, *PurchaseMonthlyReq) (*PurchaseMonthlyResp, error)
	// 购买VIP
	PurchaseVip(context.Context, *PurchaseVipReq) (*PurchaseVipResp, error)
	// 获取购买订单列表
	GetPurchaseOrders(context.Context, *GetPurchaseOrdersReq) (*GetPurchaseOrdersResp, error)
	// 获取VIP/包月订单列表
	GetVipMonthlyOrders(context.Context, *GetVipMonthlyOrdersReq) (*GetVipMonthlyOrdersResp, error)
	// 检查章节购买状态
	CheckChapterPurchased(context.Context, *CheckChapterPurchasedReq) (*CheckChapterPurchasedResp, error)
	// 检查VIP状态
	CheckVipStatus(context.Context, *CheckVipStatusReq) (*CheckVipStatusResp, error)
	// 检查包月状态
	CheckMonthlyStatus(context.Context, *CheckMonthlyStatusReq) (*CheckMonthlyStatusResp, error)
	// 获取用户已购买的章节列表
	GetPurchasedChapters(context.Context, *GetPurchasedChaptersReq) (*GetPurchasedChaptersResp, error)
}

// UnimplementedPurchaseServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPurchaseServer struct{}

func (UnimplementedPurchaseServer) PurchaseChapter(context.Context, *PurchaseChapterReq) (*PurchaseChapterResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PurchaseChapter not implemented")
}
func (UnimplementedPurchaseServer) PurchaseMonthly(context.Context, *PurchaseMonthlyReq) (*PurchaseMonthlyResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PurchaseMonthly not implemented")
}
func (UnimplementedPurchaseServer) PurchaseVip(context.Context, *PurchaseVipReq) (*PurchaseVipResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PurchaseVip not implemented")
}
func (UnimplementedPurchaseServer) GetPurchaseOrders(context.Context, *GetPurchaseOrdersReq) (*GetPurchaseOrdersResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPurchaseOrders not implemented")
}
func (UnimplementedPurchaseServer) GetVipMonthlyOrders(context.Context, *GetVipMonthlyOrdersReq) (*GetVipMonthlyOrdersResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVipMonthlyOrders not implemented")
}
func (UnimplementedPurchaseServer) CheckChapterPurchased(context.Context, *CheckChapterPurchasedReq) (*CheckChapterPurchasedResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckChapterPurchased not implemented")
}
func (UnimplementedPurchaseServer) CheckVipStatus(context.Context, *CheckVipStatusReq) (*CheckVipStatusResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckVipStatus not implemented")
}
func (UnimplementedPurchaseServer) CheckMonthlyStatus(context.Context, *CheckMonthlyStatusReq) (*CheckMonthlyStatusResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckMonthlyStatus not implemented")
}
func (UnimplementedPurchaseServer) GetPurchasedChapters(context.Context, *GetPurchasedChaptersReq) (*GetPurchasedChaptersResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPurchasedChapters not implemented")
}
func (UnimplementedPurchaseServer) testEmbeddedByValue() {}

// UnsafePurchaseServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PurchaseServer will
// result in compilation errors.
type UnsafePurchaseServer interface {
	mustEmbedUnimplementedPurchaseServer()
}

func RegisterPurchaseServer(s grpc.ServiceRegistrar, srv PurchaseServer) {
	// If the following call pancis, it indicates UnimplementedPurchaseServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Purchase_ServiceDesc, srv)
}

func _Purchase_PurchaseChapter_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PurchaseChapterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PurchaseServer).PurchaseChapter(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Purchase_PurchaseChapter_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PurchaseServer).PurchaseChapter(ctx, req.(*PurchaseChapterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Purchase_PurchaseMonthly_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PurchaseMonthlyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PurchaseServer).PurchaseMonthly(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Purchase_PurchaseMonthly_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PurchaseServer).PurchaseMonthly(ctx, req.(*PurchaseMonthlyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Purchase_PurchaseVip_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PurchaseVipReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PurchaseServer).PurchaseVip(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Purchase_PurchaseVip_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PurchaseServer).PurchaseVip(ctx, req.(*PurchaseVipReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Purchase_GetPurchaseOrders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPurchaseOrdersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PurchaseServer).GetPurchaseOrders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Purchase_GetPurchaseOrders_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PurchaseServer).GetPurchaseOrders(ctx, req.(*GetPurchaseOrdersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Purchase_GetVipMonthlyOrders_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVipMonthlyOrdersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PurchaseServer).GetVipMonthlyOrders(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Purchase_GetVipMonthlyOrders_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PurchaseServer).GetVipMonthlyOrders(ctx, req.(*GetVipMonthlyOrdersReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Purchase_CheckChapterPurchased_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckChapterPurchasedReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PurchaseServer).CheckChapterPurchased(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Purchase_CheckChapterPurchased_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PurchaseServer).CheckChapterPurchased(ctx, req.(*CheckChapterPurchasedReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Purchase_CheckVipStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckVipStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PurchaseServer).CheckVipStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Purchase_CheckVipStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PurchaseServer).CheckVipStatus(ctx, req.(*CheckVipStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Purchase_CheckMonthlyStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckMonthlyStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PurchaseServer).CheckMonthlyStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Purchase_CheckMonthlyStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PurchaseServer).CheckMonthlyStatus(ctx, req.(*CheckMonthlyStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Purchase_GetPurchasedChapters_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPurchasedChaptersReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PurchaseServer).GetPurchasedChapters(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Purchase_GetPurchasedChapters_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PurchaseServer).GetPurchasedChapters(ctx, req.(*GetPurchasedChaptersReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Purchase_ServiceDesc is the grpc.ServiceDesc for Purchase service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Purchase_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "purchase.Purchase",
	HandlerType: (*PurchaseServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PurchaseChapter",
			Handler:    _Purchase_PurchaseChapter_Handler,
		},
		{
			MethodName: "PurchaseMonthly",
			Handler:    _Purchase_PurchaseMonthly_Handler,
		},
		{
			MethodName: "PurchaseVip",
			Handler:    _Purchase_PurchaseVip_Handler,
		},
		{
			MethodName: "GetPurchaseOrders",
			Handler:    _Purchase_GetPurchaseOrders_Handler,
		},
		{
			MethodName: "GetVipMonthlyOrders",
			Handler:    _Purchase_GetVipMonthlyOrders_Handler,
		},
		{
			MethodName: "CheckChapterPurchased",
			Handler:    _Purchase_CheckChapterPurchased_Handler,
		},
		{
			MethodName: "CheckVipStatus",
			Handler:    _Purchase_CheckVipStatus_Handler,
		},
		{
			MethodName: "CheckMonthlyStatus",
			Handler:    _Purchase_CheckMonthlyStatus_Handler,
		},
		{
			MethodName: "GetPurchasedChapters",
			Handler:    _Purchase_GetPurchasedChapters_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/purchase/purchase.proto",
}
