// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: proto/purchase/purchase.proto

package purchase

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 购买订单（章节）
type PurchaseOrder struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrderId       string                 `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	AccountId     uint64                 `protobuf:"varint,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	UserId        uint64                 `protobuf:"varint,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	OrderType     string                 `protobuf:"bytes,4,opt,name=order_type,json=orderType,proto3" json:"order_type,omitempty"` // 订单类型：chapter
	BookId        string                 `protobuf:"bytes,5,opt,name=book_id,json=bookId,proto3" json:"book_id,omitempty"`
	BookName      string                 `protobuf:"bytes,6,opt,name=book_name,json=bookName,proto3" json:"book_name,omitempty"`
	ChapterId     string                 `protobuf:"bytes,7,opt,name=chapter_id,json=chapterId,proto3" json:"chapter_id,omitempty"`
	ChapterTitle  string                 `protobuf:"bytes,8,opt,name=chapter_title,json=chapterTitle,proto3" json:"chapter_title,omitempty"`
	ChapterOrder  uint32                 `protobuf:"varint,9,opt,name=chapter_order,json=chapterOrder,proto3" json:"chapter_order,omitempty"`
	CoinAmount    float64                `protobuf:"fixed64,10,opt,name=coin_amount,json=coinAmount,proto3" json:"coin_amount,omitempty"` // 消费书币数量
	Status        int32                  `protobuf:"varint,11,opt,name=status,proto3" json:"status,omitempty"`                            // 订单状态：1-待支付，2-支付成功，3-支付失败
	CreatedAt     int64                  `protobuf:"varint,12,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     int64                  `protobuf:"varint,13,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PurchaseOrder) Reset() {
	*x = PurchaseOrder{}
	mi := &file_proto_purchase_purchase_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PurchaseOrder) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PurchaseOrder) ProtoMessage() {}

func (x *PurchaseOrder) ProtoReflect() protoreflect.Message {
	mi := &file_proto_purchase_purchase_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PurchaseOrder.ProtoReflect.Descriptor instead.
func (*PurchaseOrder) Descriptor() ([]byte, []int) {
	return file_proto_purchase_purchase_proto_rawDescGZIP(), []int{0}
}

func (x *PurchaseOrder) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *PurchaseOrder) GetAccountId() uint64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *PurchaseOrder) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *PurchaseOrder) GetOrderType() string {
	if x != nil {
		return x.OrderType
	}
	return ""
}

func (x *PurchaseOrder) GetBookId() string {
	if x != nil {
		return x.BookId
	}
	return ""
}

func (x *PurchaseOrder) GetBookName() string {
	if x != nil {
		return x.BookName
	}
	return ""
}

func (x *PurchaseOrder) GetChapterId() string {
	if x != nil {
		return x.ChapterId
	}
	return ""
}

func (x *PurchaseOrder) GetChapterTitle() string {
	if x != nil {
		return x.ChapterTitle
	}
	return ""
}

func (x *PurchaseOrder) GetChapterOrder() uint32 {
	if x != nil {
		return x.ChapterOrder
	}
	return 0
}

func (x *PurchaseOrder) GetCoinAmount() float64 {
	if x != nil {
		return x.CoinAmount
	}
	return 0
}

func (x *PurchaseOrder) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *PurchaseOrder) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *PurchaseOrder) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

// VIP/包月订单
type VipMonthlyOrder struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrderId       string                 `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	AccountId     uint64                 `protobuf:"varint,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	UserId        uint64                 `protobuf:"varint,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	OrderType     string                 `protobuf:"bytes,4,opt,name=order_type,json=orderType,proto3" json:"order_type,omitempty"`           // 订单类型：monthly, vip
	CoinAmount    float64                `protobuf:"fixed64,5,opt,name=coin_amount,json=coinAmount,proto3" json:"coin_amount,omitempty"`      // 消费书币数量
	DurationDays  int32                  `protobuf:"varint,6,opt,name=duration_days,json=durationDays,proto3" json:"duration_days,omitempty"` // 有效期天数
	StartTime     int64                  `protobuf:"varint,7,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`          // 开始时间
	EndTime       int64                  `protobuf:"varint,8,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`                // 结束时间
	Status        int32                  `protobuf:"varint,9,opt,name=status,proto3" json:"status,omitempty"`                                 // 订单状态：1-待支付，2-支付成功，3-支付失败
	CreatedAt     int64                  `protobuf:"varint,10,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     int64                  `protobuf:"varint,11,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VipMonthlyOrder) Reset() {
	*x = VipMonthlyOrder{}
	mi := &file_proto_purchase_purchase_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VipMonthlyOrder) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VipMonthlyOrder) ProtoMessage() {}

func (x *VipMonthlyOrder) ProtoReflect() protoreflect.Message {
	mi := &file_proto_purchase_purchase_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VipMonthlyOrder.ProtoReflect.Descriptor instead.
func (*VipMonthlyOrder) Descriptor() ([]byte, []int) {
	return file_proto_purchase_purchase_proto_rawDescGZIP(), []int{1}
}

func (x *VipMonthlyOrder) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *VipMonthlyOrder) GetAccountId() uint64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *VipMonthlyOrder) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *VipMonthlyOrder) GetOrderType() string {
	if x != nil {
		return x.OrderType
	}
	return ""
}

func (x *VipMonthlyOrder) GetCoinAmount() float64 {
	if x != nil {
		return x.CoinAmount
	}
	return 0
}

func (x *VipMonthlyOrder) GetDurationDays() int32 {
	if x != nil {
		return x.DurationDays
	}
	return 0
}

func (x *VipMonthlyOrder) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *VipMonthlyOrder) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *VipMonthlyOrder) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *VipMonthlyOrder) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *VipMonthlyOrder) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

// 章节购买信息
type ChapterPurchaseInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrderId       string                 `protobuf:"bytes,1,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	ChapterId     string                 `protobuf:"bytes,2,opt,name=chapter_id,json=chapterId,proto3" json:"chapter_id,omitempty"`
	ChapterTitle  string                 `protobuf:"bytes,3,opt,name=chapter_title,json=chapterTitle,proto3" json:"chapter_title,omitempty"`
	ChapterOrder  uint32                 `protobuf:"varint,4,opt,name=chapter_order,json=chapterOrder,proto3" json:"chapter_order,omitempty"`
	CoinAmount    float64                `protobuf:"fixed64,5,opt,name=coin_amount,json=coinAmount,proto3" json:"coin_amount,omitempty"`
	PurchasedAt   int64                  `protobuf:"varint,6,opt,name=purchased_at,json=purchasedAt,proto3" json:"purchased_at,omitempty"`
	IsMonthly     bool                   `protobuf:"varint,7,opt,name=is_monthly,json=isMonthly,proto3" json:"is_monthly,omitempty"` // 是否通过包月获得
	IsVip         bool                   `protobuf:"varint,8,opt,name=is_vip,json=isVip,proto3" json:"is_vip,omitempty"`             // 是否通过VIP获得
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChapterPurchaseInfo) Reset() {
	*x = ChapterPurchaseInfo{}
	mi := &file_proto_purchase_purchase_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChapterPurchaseInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChapterPurchaseInfo) ProtoMessage() {}

func (x *ChapterPurchaseInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_purchase_purchase_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChapterPurchaseInfo.ProtoReflect.Descriptor instead.
func (*ChapterPurchaseInfo) Descriptor() ([]byte, []int) {
	return file_proto_purchase_purchase_proto_rawDescGZIP(), []int{2}
}

func (x *ChapterPurchaseInfo) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *ChapterPurchaseInfo) GetChapterId() string {
	if x != nil {
		return x.ChapterId
	}
	return ""
}

func (x *ChapterPurchaseInfo) GetChapterTitle() string {
	if x != nil {
		return x.ChapterTitle
	}
	return ""
}

func (x *ChapterPurchaseInfo) GetChapterOrder() uint32 {
	if x != nil {
		return x.ChapterOrder
	}
	return 0
}

func (x *ChapterPurchaseInfo) GetCoinAmount() float64 {
	if x != nil {
		return x.CoinAmount
	}
	return 0
}

func (x *ChapterPurchaseInfo) GetPurchasedAt() int64 {
	if x != nil {
		return x.PurchasedAt
	}
	return 0
}

func (x *ChapterPurchaseInfo) GetIsMonthly() bool {
	if x != nil {
		return x.IsMonthly
	}
	return false
}

func (x *ChapterPurchaseInfo) GetIsVip() bool {
	if x != nil {
		return x.IsVip
	}
	return false
}

// 批量章节购买项
type ChapterPurchaseItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ChapterOrder  uint32                 `protobuf:"varint,1,opt,name=chapterOrder,proto3" json:"chapterOrder,omitempty"`
	CoinAmount    float64                `protobuf:"fixed64,2,opt,name=coinAmount,proto3" json:"coinAmount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChapterPurchaseItem) Reset() {
	*x = ChapterPurchaseItem{}
	mi := &file_proto_purchase_purchase_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChapterPurchaseItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChapterPurchaseItem) ProtoMessage() {}

func (x *ChapterPurchaseItem) ProtoReflect() protoreflect.Message {
	mi := &file_proto_purchase_purchase_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChapterPurchaseItem.ProtoReflect.Descriptor instead.
func (*ChapterPurchaseItem) Descriptor() ([]byte, []int) {
	return file_proto_purchase_purchase_proto_rawDescGZIP(), []int{3}
}

func (x *ChapterPurchaseItem) GetChapterOrder() uint32 {
	if x != nil {
		return x.ChapterOrder
	}
	return 0
}

func (x *ChapterPurchaseItem) GetCoinAmount() float64 {
	if x != nil {
		return x.CoinAmount
	}
	return 0
}

// 购买章节（支持批量）
type PurchaseChapterReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        uint64                 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	BookId        string                 `protobuf:"bytes,2,opt,name=book_id,json=bookId,proto3" json:"book_id,omitempty"`
	Chapters      []*ChapterPurchaseItem `protobuf:"bytes,3,rep,name=chapters,proto3" json:"chapters,omitempty"` // 支持批量购买
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PurchaseChapterReq) Reset() {
	*x = PurchaseChapterReq{}
	mi := &file_proto_purchase_purchase_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PurchaseChapterReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PurchaseChapterReq) ProtoMessage() {}

func (x *PurchaseChapterReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_purchase_purchase_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PurchaseChapterReq.ProtoReflect.Descriptor instead.
func (*PurchaseChapterReq) Descriptor() ([]byte, []int) {
	return file_proto_purchase_purchase_proto_rawDescGZIP(), []int{4}
}

func (x *PurchaseChapterReq) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *PurchaseChapterReq) GetBookId() string {
	if x != nil {
		return x.BookId
	}
	return ""
}

func (x *PurchaseChapterReq) GetChapters() []*ChapterPurchaseItem {
	if x != nil {
		return x.Chapters
	}
	return nil
}

type PurchaseChapterResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Orders        []*PurchaseOrder       `protobuf:"bytes,3,rep,name=orders,proto3" json:"orders,omitempty"` // 返回多个订单
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PurchaseChapterResp) Reset() {
	*x = PurchaseChapterResp{}
	mi := &file_proto_purchase_purchase_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PurchaseChapterResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PurchaseChapterResp) ProtoMessage() {}

func (x *PurchaseChapterResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_purchase_purchase_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PurchaseChapterResp.ProtoReflect.Descriptor instead.
func (*PurchaseChapterResp) Descriptor() ([]byte, []int) {
	return file_proto_purchase_purchase_proto_rawDescGZIP(), []int{5}
}

func (x *PurchaseChapterResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *PurchaseChapterResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *PurchaseChapterResp) GetOrders() []*PurchaseOrder {
	if x != nil {
		return x.Orders
	}
	return nil
}

// 购买包月
type PurchaseMonthlyReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        uint64                 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	CoinAmount    float64                `protobuf:"fixed64,2,opt,name=coin_amount,json=coinAmount,proto3" json:"coin_amount,omitempty"`      // 消费书币数量
	DurationDays  int32                  `protobuf:"varint,3,opt,name=duration_days,json=durationDays,proto3" json:"duration_days,omitempty"` // 包月天数，默认30天
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PurchaseMonthlyReq) Reset() {
	*x = PurchaseMonthlyReq{}
	mi := &file_proto_purchase_purchase_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PurchaseMonthlyReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PurchaseMonthlyReq) ProtoMessage() {}

func (x *PurchaseMonthlyReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_purchase_purchase_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PurchaseMonthlyReq.ProtoReflect.Descriptor instead.
func (*PurchaseMonthlyReq) Descriptor() ([]byte, []int) {
	return file_proto_purchase_purchase_proto_rawDescGZIP(), []int{6}
}

func (x *PurchaseMonthlyReq) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *PurchaseMonthlyReq) GetCoinAmount() float64 {
	if x != nil {
		return x.CoinAmount
	}
	return 0
}

func (x *PurchaseMonthlyReq) GetDurationDays() int32 {
	if x != nil {
		return x.DurationDays
	}
	return 0
}

type PurchaseMonthlyResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Order         *VipMonthlyOrder       `protobuf:"bytes,3,opt,name=order,proto3" json:"order,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PurchaseMonthlyResp) Reset() {
	*x = PurchaseMonthlyResp{}
	mi := &file_proto_purchase_purchase_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PurchaseMonthlyResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PurchaseMonthlyResp) ProtoMessage() {}

func (x *PurchaseMonthlyResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_purchase_purchase_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PurchaseMonthlyResp.ProtoReflect.Descriptor instead.
func (*PurchaseMonthlyResp) Descriptor() ([]byte, []int) {
	return file_proto_purchase_purchase_proto_rawDescGZIP(), []int{7}
}

func (x *PurchaseMonthlyResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *PurchaseMonthlyResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *PurchaseMonthlyResp) GetOrder() *VipMonthlyOrder {
	if x != nil {
		return x.Order
	}
	return nil
}

// 购买VIP
type PurchaseVipReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        uint64                 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	CoinAmount    float64                `protobuf:"fixed64,2,opt,name=coin_amount,json=coinAmount,proto3" json:"coin_amount,omitempty"`      // 消费书币数量
	DurationDays  int32                  `protobuf:"varint,3,opt,name=duration_days,json=durationDays,proto3" json:"duration_days,omitempty"` // VIP天数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PurchaseVipReq) Reset() {
	*x = PurchaseVipReq{}
	mi := &file_proto_purchase_purchase_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PurchaseVipReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PurchaseVipReq) ProtoMessage() {}

func (x *PurchaseVipReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_purchase_purchase_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PurchaseVipReq.ProtoReflect.Descriptor instead.
func (*PurchaseVipReq) Descriptor() ([]byte, []int) {
	return file_proto_purchase_purchase_proto_rawDescGZIP(), []int{8}
}

func (x *PurchaseVipReq) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *PurchaseVipReq) GetCoinAmount() float64 {
	if x != nil {
		return x.CoinAmount
	}
	return 0
}

func (x *PurchaseVipReq) GetDurationDays() int32 {
	if x != nil {
		return x.DurationDays
	}
	return 0
}

type PurchaseVipResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Order         *VipMonthlyOrder       `protobuf:"bytes,3,opt,name=order,proto3" json:"order,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PurchaseVipResp) Reset() {
	*x = PurchaseVipResp{}
	mi := &file_proto_purchase_purchase_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PurchaseVipResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PurchaseVipResp) ProtoMessage() {}

func (x *PurchaseVipResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_purchase_purchase_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PurchaseVipResp.ProtoReflect.Descriptor instead.
func (*PurchaseVipResp) Descriptor() ([]byte, []int) {
	return file_proto_purchase_purchase_proto_rawDescGZIP(), []int{9}
}

func (x *PurchaseVipResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *PurchaseVipResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *PurchaseVipResp) GetOrder() *VipMonthlyOrder {
	if x != nil {
		return x.Order
	}
	return nil
}

// 获取购买订单列表（章节）
type GetPurchaseOrdersReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        uint64                 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Page          int32                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	BookId        string                 `protobuf:"bytes,4,opt,name=book_id,json=bookId,proto3" json:"book_id,omitempty"` // 可选，筛选特定书籍
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPurchaseOrdersReq) Reset() {
	*x = GetPurchaseOrdersReq{}
	mi := &file_proto_purchase_purchase_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPurchaseOrdersReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPurchaseOrdersReq) ProtoMessage() {}

func (x *GetPurchaseOrdersReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_purchase_purchase_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPurchaseOrdersReq.ProtoReflect.Descriptor instead.
func (*GetPurchaseOrdersReq) Descriptor() ([]byte, []int) {
	return file_proto_purchase_purchase_proto_rawDescGZIP(), []int{10}
}

func (x *GetPurchaseOrdersReq) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetPurchaseOrdersReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetPurchaseOrdersReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetPurchaseOrdersReq) GetBookId() string {
	if x != nil {
		return x.BookId
	}
	return ""
}

type GetPurchaseOrdersResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Orders        []*PurchaseOrder       `protobuf:"bytes,3,rep,name=orders,proto3" json:"orders,omitempty"`
	Total         int64                  `protobuf:"varint,4,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPurchaseOrdersResp) Reset() {
	*x = GetPurchaseOrdersResp{}
	mi := &file_proto_purchase_purchase_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPurchaseOrdersResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPurchaseOrdersResp) ProtoMessage() {}

func (x *GetPurchaseOrdersResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_purchase_purchase_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPurchaseOrdersResp.ProtoReflect.Descriptor instead.
func (*GetPurchaseOrdersResp) Descriptor() ([]byte, []int) {
	return file_proto_purchase_purchase_proto_rawDescGZIP(), []int{11}
}

func (x *GetPurchaseOrdersResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetPurchaseOrdersResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetPurchaseOrdersResp) GetOrders() []*PurchaseOrder {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *GetPurchaseOrdersResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 获取VIP/包月订单列表
type GetVipMonthlyOrdersReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        uint64                 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Page          int32                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	OrderType     string                 `protobuf:"bytes,4,opt,name=order_type,json=orderType,proto3" json:"order_type,omitempty"` // 可选，筛选订单类型：monthly, vip
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVipMonthlyOrdersReq) Reset() {
	*x = GetVipMonthlyOrdersReq{}
	mi := &file_proto_purchase_purchase_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVipMonthlyOrdersReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVipMonthlyOrdersReq) ProtoMessage() {}

func (x *GetVipMonthlyOrdersReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_purchase_purchase_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVipMonthlyOrdersReq.ProtoReflect.Descriptor instead.
func (*GetVipMonthlyOrdersReq) Descriptor() ([]byte, []int) {
	return file_proto_purchase_purchase_proto_rawDescGZIP(), []int{12}
}

func (x *GetVipMonthlyOrdersReq) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetVipMonthlyOrdersReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetVipMonthlyOrdersReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetVipMonthlyOrdersReq) GetOrderType() string {
	if x != nil {
		return x.OrderType
	}
	return ""
}

type GetVipMonthlyOrdersResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Orders        []*VipMonthlyOrder     `protobuf:"bytes,3,rep,name=orders,proto3" json:"orders,omitempty"`
	Total         int64                  `protobuf:"varint,4,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVipMonthlyOrdersResp) Reset() {
	*x = GetVipMonthlyOrdersResp{}
	mi := &file_proto_purchase_purchase_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVipMonthlyOrdersResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVipMonthlyOrdersResp) ProtoMessage() {}

func (x *GetVipMonthlyOrdersResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_purchase_purchase_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVipMonthlyOrdersResp.ProtoReflect.Descriptor instead.
func (*GetVipMonthlyOrdersResp) Descriptor() ([]byte, []int) {
	return file_proto_purchase_purchase_proto_rawDescGZIP(), []int{13}
}

func (x *GetVipMonthlyOrdersResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetVipMonthlyOrdersResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetVipMonthlyOrdersResp) GetOrders() []*VipMonthlyOrder {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *GetVipMonthlyOrdersResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

// 检查章节购买状态
type CheckChapterPurchasedReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        uint64                 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	BookId        string                 `protobuf:"bytes,2,opt,name=book_id,json=bookId,proto3" json:"book_id,omitempty"`
	ChapterOrder  uint32                 `protobuf:"varint,3,opt,name=chapter_order,json=chapterOrder,proto3" json:"chapter_order,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckChapterPurchasedReq) Reset() {
	*x = CheckChapterPurchasedReq{}
	mi := &file_proto_purchase_purchase_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckChapterPurchasedReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckChapterPurchasedReq) ProtoMessage() {}

func (x *CheckChapterPurchasedReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_purchase_purchase_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckChapterPurchasedReq.ProtoReflect.Descriptor instead.
func (*CheckChapterPurchasedReq) Descriptor() ([]byte, []int) {
	return file_proto_purchase_purchase_proto_rawDescGZIP(), []int{14}
}

func (x *CheckChapterPurchasedReq) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *CheckChapterPurchasedReq) GetBookId() string {
	if x != nil {
		return x.BookId
	}
	return ""
}

func (x *CheckChapterPurchasedReq) GetChapterOrder() uint32 {
	if x != nil {
		return x.ChapterOrder
	}
	return 0
}

type CheckChapterPurchasedResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	IsPurchased   bool                   `protobuf:"varint,3,opt,name=is_purchased,json=isPurchased,proto3" json:"is_purchased,omitempty"`
	PurchasedAt   int64                  `protobuf:"varint,4,opt,name=purchased_at,json=purchasedAt,proto3" json:"purchased_at,omitempty"`
	IsMonthly     bool                   `protobuf:"varint,5,opt,name=is_monthly,json=isMonthly,proto3" json:"is_monthly,omitempty"` // 是否通过包月获得
	IsVip         bool                   `protobuf:"varint,6,opt,name=is_vip,json=isVip,proto3" json:"is_vip,omitempty"`             // 是否通过VIP获得
	OrderId       string                 `protobuf:"bytes,7,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckChapterPurchasedResp) Reset() {
	*x = CheckChapterPurchasedResp{}
	mi := &file_proto_purchase_purchase_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckChapterPurchasedResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckChapterPurchasedResp) ProtoMessage() {}

func (x *CheckChapterPurchasedResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_purchase_purchase_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckChapterPurchasedResp.ProtoReflect.Descriptor instead.
func (*CheckChapterPurchasedResp) Descriptor() ([]byte, []int) {
	return file_proto_purchase_purchase_proto_rawDescGZIP(), []int{15}
}

func (x *CheckChapterPurchasedResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CheckChapterPurchasedResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CheckChapterPurchasedResp) GetIsPurchased() bool {
	if x != nil {
		return x.IsPurchased
	}
	return false
}

func (x *CheckChapterPurchasedResp) GetPurchasedAt() int64 {
	if x != nil {
		return x.PurchasedAt
	}
	return 0
}

func (x *CheckChapterPurchasedResp) GetIsMonthly() bool {
	if x != nil {
		return x.IsMonthly
	}
	return false
}

func (x *CheckChapterPurchasedResp) GetIsVip() bool {
	if x != nil {
		return x.IsVip
	}
	return false
}

func (x *CheckChapterPurchasedResp) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

// 检查VIP状态
type CheckVipStatusReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        uint64                 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckVipStatusReq) Reset() {
	*x = CheckVipStatusReq{}
	mi := &file_proto_purchase_purchase_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckVipStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckVipStatusReq) ProtoMessage() {}

func (x *CheckVipStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_purchase_purchase_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckVipStatusReq.ProtoReflect.Descriptor instead.
func (*CheckVipStatusReq) Descriptor() ([]byte, []int) {
	return file_proto_purchase_purchase_proto_rawDescGZIP(), []int{16}
}

func (x *CheckVipStatusReq) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type CheckVipStatusResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	IsActive      bool                   `protobuf:"varint,3,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	StartTime     int64                  `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime       int64                  `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	OrderId       string                 `protobuf:"bytes,6,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckVipStatusResp) Reset() {
	*x = CheckVipStatusResp{}
	mi := &file_proto_purchase_purchase_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckVipStatusResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckVipStatusResp) ProtoMessage() {}

func (x *CheckVipStatusResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_purchase_purchase_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckVipStatusResp.ProtoReflect.Descriptor instead.
func (*CheckVipStatusResp) Descriptor() ([]byte, []int) {
	return file_proto_purchase_purchase_proto_rawDescGZIP(), []int{17}
}

func (x *CheckVipStatusResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CheckVipStatusResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CheckVipStatusResp) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *CheckVipStatusResp) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *CheckVipStatusResp) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *CheckVipStatusResp) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

// 检查包月状态
type CheckMonthlyStatusReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        uint64                 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckMonthlyStatusReq) Reset() {
	*x = CheckMonthlyStatusReq{}
	mi := &file_proto_purchase_purchase_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckMonthlyStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckMonthlyStatusReq) ProtoMessage() {}

func (x *CheckMonthlyStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_purchase_purchase_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckMonthlyStatusReq.ProtoReflect.Descriptor instead.
func (*CheckMonthlyStatusReq) Descriptor() ([]byte, []int) {
	return file_proto_purchase_purchase_proto_rawDescGZIP(), []int{18}
}

func (x *CheckMonthlyStatusReq) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

type CheckMonthlyStatusResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	IsActive      bool                   `protobuf:"varint,3,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	StartTime     int64                  `protobuf:"varint,4,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	EndTime       int64                  `protobuf:"varint,5,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	OrderId       string                 `protobuf:"bytes,6,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckMonthlyStatusResp) Reset() {
	*x = CheckMonthlyStatusResp{}
	mi := &file_proto_purchase_purchase_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckMonthlyStatusResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckMonthlyStatusResp) ProtoMessage() {}

func (x *CheckMonthlyStatusResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_purchase_purchase_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckMonthlyStatusResp.ProtoReflect.Descriptor instead.
func (*CheckMonthlyStatusResp) Descriptor() ([]byte, []int) {
	return file_proto_purchase_purchase_proto_rawDescGZIP(), []int{19}
}

func (x *CheckMonthlyStatusResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CheckMonthlyStatusResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CheckMonthlyStatusResp) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *CheckMonthlyStatusResp) GetStartTime() int64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *CheckMonthlyStatusResp) GetEndTime() int64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *CheckMonthlyStatusResp) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

// 获取用户已购买的章节列表
type GetPurchasedChaptersReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        uint64                 `protobuf:"varint,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	BookId        string                 `protobuf:"bytes,2,opt,name=book_id,json=bookId,proto3" json:"book_id,omitempty"`
	Page          int32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPurchasedChaptersReq) Reset() {
	*x = GetPurchasedChaptersReq{}
	mi := &file_proto_purchase_purchase_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPurchasedChaptersReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPurchasedChaptersReq) ProtoMessage() {}

func (x *GetPurchasedChaptersReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_purchase_purchase_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPurchasedChaptersReq.ProtoReflect.Descriptor instead.
func (*GetPurchasedChaptersReq) Descriptor() ([]byte, []int) {
	return file_proto_purchase_purchase_proto_rawDescGZIP(), []int{20}
}

func (x *GetPurchasedChaptersReq) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *GetPurchasedChaptersReq) GetBookId() string {
	if x != nil {
		return x.BookId
	}
	return ""
}

func (x *GetPurchasedChaptersReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetPurchasedChaptersReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type GetPurchasedChaptersResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Chapters      []*ChapterPurchaseInfo `protobuf:"bytes,3,rep,name=chapters,proto3" json:"chapters,omitempty"`
	Total         int64                  `protobuf:"varint,4,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPurchasedChaptersResp) Reset() {
	*x = GetPurchasedChaptersResp{}
	mi := &file_proto_purchase_purchase_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPurchasedChaptersResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPurchasedChaptersResp) ProtoMessage() {}

func (x *GetPurchasedChaptersResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_purchase_purchase_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPurchasedChaptersResp.ProtoReflect.Descriptor instead.
func (*GetPurchasedChaptersResp) Descriptor() ([]byte, []int) {
	return file_proto_purchase_purchase_proto_rawDescGZIP(), []int{21}
}

func (x *GetPurchasedChaptersResp) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GetPurchasedChaptersResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GetPurchasedChaptersResp) GetChapters() []*ChapterPurchaseInfo {
	if x != nil {
		return x.Chapters
	}
	return nil
}

func (x *GetPurchasedChaptersResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

var File_proto_purchase_purchase_proto protoreflect.FileDescriptor

const file_proto_purchase_purchase_proto_rawDesc = "" +
	"\n" +
	"\x1dproto/purchase/purchase.proto\x12\bpurchase\"\x97\x03\n" +
	"\rPurchaseOrder\x12\x19\n" +
	"\border_id\x18\x01 \x01(\tR\aorderId\x12\x1d\n" +
	"\n" +
	"account_id\x18\x02 \x01(\x04R\taccountId\x12\x17\n" +
	"\auser_id\x18\x03 \x01(\x04R\x06userId\x12\x1d\n" +
	"\n" +
	"order_type\x18\x04 \x01(\tR\torderType\x12\x17\n" +
	"\abook_id\x18\x05 \x01(\tR\x06bookId\x12\x1b\n" +
	"\tbook_name\x18\x06 \x01(\tR\bbookName\x12\x1d\n" +
	"\n" +
	"chapter_id\x18\a \x01(\tR\tchapterId\x12#\n" +
	"\rchapter_title\x18\b \x01(\tR\fchapterTitle\x12#\n" +
	"\rchapter_order\x18\t \x01(\rR\fchapterOrder\x12\x1f\n" +
	"\vcoin_amount\x18\n" +
	" \x01(\x01R\n" +
	"coinAmount\x12\x16\n" +
	"\x06status\x18\v \x01(\x05R\x06status\x12\x1d\n" +
	"\n" +
	"created_at\x18\f \x01(\x03R\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\r \x01(\x03R\tupdatedAt\"\xd9\x02\n" +
	"\x0fVipMonthlyOrder\x12\x19\n" +
	"\border_id\x18\x01 \x01(\tR\aorderId\x12\x1d\n" +
	"\n" +
	"account_id\x18\x02 \x01(\x04R\taccountId\x12\x17\n" +
	"\auser_id\x18\x03 \x01(\x04R\x06userId\x12\x1d\n" +
	"\n" +
	"order_type\x18\x04 \x01(\tR\torderType\x12\x1f\n" +
	"\vcoin_amount\x18\x05 \x01(\x01R\n" +
	"coinAmount\x12#\n" +
	"\rduration_days\x18\x06 \x01(\x05R\fdurationDays\x12\x1d\n" +
	"\n" +
	"start_time\x18\a \x01(\x03R\tstartTime\x12\x19\n" +
	"\bend_time\x18\b \x01(\x03R\aendTime\x12\x16\n" +
	"\x06status\x18\t \x01(\x05R\x06status\x12\x1d\n" +
	"\n" +
	"created_at\x18\n" +
	" \x01(\x03R\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\v \x01(\x03R\tupdatedAt\"\x93\x02\n" +
	"\x13ChapterPurchaseInfo\x12\x19\n" +
	"\border_id\x18\x01 \x01(\tR\aorderId\x12\x1d\n" +
	"\n" +
	"chapter_id\x18\x02 \x01(\tR\tchapterId\x12#\n" +
	"\rchapter_title\x18\x03 \x01(\tR\fchapterTitle\x12#\n" +
	"\rchapter_order\x18\x04 \x01(\rR\fchapterOrder\x12\x1f\n" +
	"\vcoin_amount\x18\x05 \x01(\x01R\n" +
	"coinAmount\x12!\n" +
	"\fpurchased_at\x18\x06 \x01(\x03R\vpurchasedAt\x12\x1d\n" +
	"\n" +
	"is_monthly\x18\a \x01(\bR\tisMonthly\x12\x15\n" +
	"\x06is_vip\x18\b \x01(\bR\x05isVip\"Y\n" +
	"\x13ChapterPurchaseItem\x12\"\n" +
	"\fchapterOrder\x18\x01 \x01(\rR\fchapterOrder\x12\x1e\n" +
	"\n" +
	"coinAmount\x18\x02 \x01(\x01R\n" +
	"coinAmount\"\x81\x01\n" +
	"\x12PurchaseChapterReq\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x04R\x06userId\x12\x17\n" +
	"\abook_id\x18\x02 \x01(\tR\x06bookId\x129\n" +
	"\bchapters\x18\x03 \x03(\v2\x1d.purchase.ChapterPurchaseItemR\bchapters\"t\n" +
	"\x13PurchaseChapterResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12/\n" +
	"\x06orders\x18\x03 \x03(\v2\x17.purchase.PurchaseOrderR\x06orders\"s\n" +
	"\x12PurchaseMonthlyReq\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x04R\x06userId\x12\x1f\n" +
	"\vcoin_amount\x18\x02 \x01(\x01R\n" +
	"coinAmount\x12#\n" +
	"\rduration_days\x18\x03 \x01(\x05R\fdurationDays\"t\n" +
	"\x13PurchaseMonthlyResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12/\n" +
	"\x05order\x18\x03 \x01(\v2\x19.purchase.VipMonthlyOrderR\x05order\"o\n" +
	"\x0ePurchaseVipReq\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x04R\x06userId\x12\x1f\n" +
	"\vcoin_amount\x18\x02 \x01(\x01R\n" +
	"coinAmount\x12#\n" +
	"\rduration_days\x18\x03 \x01(\x05R\fdurationDays\"p\n" +
	"\x0fPurchaseVipResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12/\n" +
	"\x05order\x18\x03 \x01(\v2\x19.purchase.VipMonthlyOrderR\x05order\"y\n" +
	"\x14GetPurchaseOrdersReq\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x04R\x06userId\x12\x12\n" +
	"\x04page\x18\x02 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x03 \x01(\x05R\bpageSize\x12\x17\n" +
	"\abook_id\x18\x04 \x01(\tR\x06bookId\"\x8c\x01\n" +
	"\x15GetPurchaseOrdersResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12/\n" +
	"\x06orders\x18\x03 \x03(\v2\x17.purchase.PurchaseOrderR\x06orders\x12\x14\n" +
	"\x05total\x18\x04 \x01(\x03R\x05total\"\x81\x01\n" +
	"\x16GetVipMonthlyOrdersReq\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x04R\x06userId\x12\x12\n" +
	"\x04page\x18\x02 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x03 \x01(\x05R\bpageSize\x12\x1d\n" +
	"\n" +
	"order_type\x18\x04 \x01(\tR\torderType\"\x90\x01\n" +
	"\x17GetVipMonthlyOrdersResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x121\n" +
	"\x06orders\x18\x03 \x03(\v2\x19.purchase.VipMonthlyOrderR\x06orders\x12\x14\n" +
	"\x05total\x18\x04 \x01(\x03R\x05total\"q\n" +
	"\x18CheckChapterPurchasedReq\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x04R\x06userId\x12\x17\n" +
	"\abook_id\x18\x02 \x01(\tR\x06bookId\x12#\n" +
	"\rchapter_order\x18\x03 \x01(\rR\fchapterOrder\"\xe0\x01\n" +
	"\x19CheckChapterPurchasedResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12!\n" +
	"\fis_purchased\x18\x03 \x01(\bR\visPurchased\x12!\n" +
	"\fpurchased_at\x18\x04 \x01(\x03R\vpurchasedAt\x12\x1d\n" +
	"\n" +
	"is_monthly\x18\x05 \x01(\bR\tisMonthly\x12\x15\n" +
	"\x06is_vip\x18\x06 \x01(\bR\x05isVip\x12\x19\n" +
	"\border_id\x18\a \x01(\tR\aorderId\",\n" +
	"\x11CheckVipStatusReq\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x04R\x06userId\"\xb4\x01\n" +
	"\x12CheckVipStatusResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1b\n" +
	"\tis_active\x18\x03 \x01(\bR\bisActive\x12\x1d\n" +
	"\n" +
	"start_time\x18\x04 \x01(\x03R\tstartTime\x12\x19\n" +
	"\bend_time\x18\x05 \x01(\x03R\aendTime\x12\x19\n" +
	"\border_id\x18\x06 \x01(\tR\aorderId\"0\n" +
	"\x15CheckMonthlyStatusReq\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x04R\x06userId\"\xb8\x01\n" +
	"\x16CheckMonthlyStatusResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1b\n" +
	"\tis_active\x18\x03 \x01(\bR\bisActive\x12\x1d\n" +
	"\n" +
	"start_time\x18\x04 \x01(\x03R\tstartTime\x12\x19\n" +
	"\bend_time\x18\x05 \x01(\x03R\aendTime\x12\x19\n" +
	"\border_id\x18\x06 \x01(\tR\aorderId\"|\n" +
	"\x17GetPurchasedChaptersReq\x12\x17\n" +
	"\auser_id\x18\x01 \x01(\x04R\x06userId\x12\x17\n" +
	"\abook_id\x18\x02 \x01(\tR\x06bookId\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize\"\x99\x01\n" +
	"\x18GetPurchasedChaptersResp\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x129\n" +
	"\bchapters\x18\x03 \x03(\v2\x1d.purchase.ChapterPurchaseInfoR\bchapters\x12\x14\n" +
	"\x05total\x18\x04 \x01(\x03R\x05total2\x87\x06\n" +
	"\bPurchase\x12N\n" +
	"\x0fPurchaseChapter\x12\x1c.purchase.PurchaseChapterReq\x1a\x1d.purchase.PurchaseChapterResp\x12N\n" +
	"\x0fPurchaseMonthly\x12\x1c.purchase.PurchaseMonthlyReq\x1a\x1d.purchase.PurchaseMonthlyResp\x12B\n" +
	"\vPurchaseVip\x12\x18.purchase.PurchaseVipReq\x1a\x19.purchase.PurchaseVipResp\x12T\n" +
	"\x11GetPurchaseOrders\x12\x1e.purchase.GetPurchaseOrdersReq\x1a\x1f.purchase.GetPurchaseOrdersResp\x12Z\n" +
	"\x13GetVipMonthlyOrders\x12 .purchase.GetVipMonthlyOrdersReq\x1a!.purchase.GetVipMonthlyOrdersResp\x12`\n" +
	"\x15CheckChapterPurchased\x12\".purchase.CheckChapterPurchasedReq\x1a#.purchase.CheckChapterPurchasedResp\x12K\n" +
	"\x0eCheckVipStatus\x12\x1b.purchase.CheckVipStatusReq\x1a\x1c.purchase.CheckVipStatusResp\x12W\n" +
	"\x12CheckMonthlyStatus\x12\x1f.purchase.CheckMonthlyStatusReq\x1a .purchase.CheckMonthlyStatusResp\x12]\n" +
	"\x14GetPurchasedChapters\x12!.purchase.GetPurchasedChaptersReq\x1a\".purchase.GetPurchasedChaptersRespB6Z4creativematrix.com/beyondreading/gen2/proto/purchaseb\x06proto3"

var (
	file_proto_purchase_purchase_proto_rawDescOnce sync.Once
	file_proto_purchase_purchase_proto_rawDescData []byte
)

func file_proto_purchase_purchase_proto_rawDescGZIP() []byte {
	file_proto_purchase_purchase_proto_rawDescOnce.Do(func() {
		file_proto_purchase_purchase_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_proto_purchase_purchase_proto_rawDesc), len(file_proto_purchase_purchase_proto_rawDesc)))
	})
	return file_proto_purchase_purchase_proto_rawDescData
}

var file_proto_purchase_purchase_proto_msgTypes = make([]protoimpl.MessageInfo, 22)
var file_proto_purchase_purchase_proto_goTypes = []any{
	(*PurchaseOrder)(nil),             // 0: purchase.PurchaseOrder
	(*VipMonthlyOrder)(nil),           // 1: purchase.VipMonthlyOrder
	(*ChapterPurchaseInfo)(nil),       // 2: purchase.ChapterPurchaseInfo
	(*ChapterPurchaseItem)(nil),       // 3: purchase.ChapterPurchaseItem
	(*PurchaseChapterReq)(nil),        // 4: purchase.PurchaseChapterReq
	(*PurchaseChapterResp)(nil),       // 5: purchase.PurchaseChapterResp
	(*PurchaseMonthlyReq)(nil),        // 6: purchase.PurchaseMonthlyReq
	(*PurchaseMonthlyResp)(nil),       // 7: purchase.PurchaseMonthlyResp
	(*PurchaseVipReq)(nil),            // 8: purchase.PurchaseVipReq
	(*PurchaseVipResp)(nil),           // 9: purchase.PurchaseVipResp
	(*GetPurchaseOrdersReq)(nil),      // 10: purchase.GetPurchaseOrdersReq
	(*GetPurchaseOrdersResp)(nil),     // 11: purchase.GetPurchaseOrdersResp
	(*GetVipMonthlyOrdersReq)(nil),    // 12: purchase.GetVipMonthlyOrdersReq
	(*GetVipMonthlyOrdersResp)(nil),   // 13: purchase.GetVipMonthlyOrdersResp
	(*CheckChapterPurchasedReq)(nil),  // 14: purchase.CheckChapterPurchasedReq
	(*CheckChapterPurchasedResp)(nil), // 15: purchase.CheckChapterPurchasedResp
	(*CheckVipStatusReq)(nil),         // 16: purchase.CheckVipStatusReq
	(*CheckVipStatusResp)(nil),        // 17: purchase.CheckVipStatusResp
	(*CheckMonthlyStatusReq)(nil),     // 18: purchase.CheckMonthlyStatusReq
	(*CheckMonthlyStatusResp)(nil),    // 19: purchase.CheckMonthlyStatusResp
	(*GetPurchasedChaptersReq)(nil),   // 20: purchase.GetPurchasedChaptersReq
	(*GetPurchasedChaptersResp)(nil),  // 21: purchase.GetPurchasedChaptersResp
}
var file_proto_purchase_purchase_proto_depIdxs = []int32{
	3,  // 0: purchase.PurchaseChapterReq.chapters:type_name -> purchase.ChapterPurchaseItem
	0,  // 1: purchase.PurchaseChapterResp.orders:type_name -> purchase.PurchaseOrder
	1,  // 2: purchase.PurchaseMonthlyResp.order:type_name -> purchase.VipMonthlyOrder
	1,  // 3: purchase.PurchaseVipResp.order:type_name -> purchase.VipMonthlyOrder
	0,  // 4: purchase.GetPurchaseOrdersResp.orders:type_name -> purchase.PurchaseOrder
	1,  // 5: purchase.GetVipMonthlyOrdersResp.orders:type_name -> purchase.VipMonthlyOrder
	2,  // 6: purchase.GetPurchasedChaptersResp.chapters:type_name -> purchase.ChapterPurchaseInfo
	4,  // 7: purchase.Purchase.PurchaseChapter:input_type -> purchase.PurchaseChapterReq
	6,  // 8: purchase.Purchase.PurchaseMonthly:input_type -> purchase.PurchaseMonthlyReq
	8,  // 9: purchase.Purchase.PurchaseVip:input_type -> purchase.PurchaseVipReq
	10, // 10: purchase.Purchase.GetPurchaseOrders:input_type -> purchase.GetPurchaseOrdersReq
	12, // 11: purchase.Purchase.GetVipMonthlyOrders:input_type -> purchase.GetVipMonthlyOrdersReq
	14, // 12: purchase.Purchase.CheckChapterPurchased:input_type -> purchase.CheckChapterPurchasedReq
	16, // 13: purchase.Purchase.CheckVipStatus:input_type -> purchase.CheckVipStatusReq
	18, // 14: purchase.Purchase.CheckMonthlyStatus:input_type -> purchase.CheckMonthlyStatusReq
	20, // 15: purchase.Purchase.GetPurchasedChapters:input_type -> purchase.GetPurchasedChaptersReq
	5,  // 16: purchase.Purchase.PurchaseChapter:output_type -> purchase.PurchaseChapterResp
	7,  // 17: purchase.Purchase.PurchaseMonthly:output_type -> purchase.PurchaseMonthlyResp
	9,  // 18: purchase.Purchase.PurchaseVip:output_type -> purchase.PurchaseVipResp
	11, // 19: purchase.Purchase.GetPurchaseOrders:output_type -> purchase.GetPurchaseOrdersResp
	13, // 20: purchase.Purchase.GetVipMonthlyOrders:output_type -> purchase.GetVipMonthlyOrdersResp
	15, // 21: purchase.Purchase.CheckChapterPurchased:output_type -> purchase.CheckChapterPurchasedResp
	17, // 22: purchase.Purchase.CheckVipStatus:output_type -> purchase.CheckVipStatusResp
	19, // 23: purchase.Purchase.CheckMonthlyStatus:output_type -> purchase.CheckMonthlyStatusResp
	21, // 24: purchase.Purchase.GetPurchasedChapters:output_type -> purchase.GetPurchasedChaptersResp
	16, // [16:25] is the sub-list for method output_type
	7,  // [7:16] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_proto_purchase_purchase_proto_init() }
func file_proto_purchase_purchase_proto_init() {
	if File_proto_purchase_purchase_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_proto_purchase_purchase_proto_rawDesc), len(file_proto_purchase_purchase_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   22,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_purchase_purchase_proto_goTypes,
		DependencyIndexes: file_proto_purchase_purchase_proto_depIdxs,
		MessageInfos:      file_proto_purchase_purchase_proto_msgTypes,
	}.Build()
	File_proto_purchase_purchase_proto = out.File
	file_proto_purchase_purchase_proto_goTypes = nil
	file_proto_purchase_purchase_proto_depIdxs = nil
}
