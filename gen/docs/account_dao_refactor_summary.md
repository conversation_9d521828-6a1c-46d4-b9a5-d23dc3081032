# Account DAO重构总结

## 概述

完全按照app\base\account\dao\account.go的实现风格重新实现了gen\app\base\account\dao\account.go，采用了原始项目的查询风格和分表处理方式。

## 关键变更

### 1. 查询风格统一

#### **原始风格**
```go
var account po.Account
query := `SELECT account_id, user_id, coin_balance, total_recharged, total_consumed,
          status, user_type, user_level, monthly_expire_time, vip_expire_time, created_at, updated_at
          FROM %s WHERE user_id = ?`

query = d.setAccountTable(query, userId)
err = db.Get(&account, query, userId)
```

#### **重构前（错误风格）**
```go
tableName := d.setAccountTable(userId)
query := fmt.Sprintf(`SELECT ... FROM %s WHERE user_id = ?`, tableName)
err = db.GetContext(ctx, &account, query, userId)
```

#### **重构后（正确风格）**
```go
var account po.Account
query := `SELECT account_id, user_id, coin_balance, total_recharged, total_consumed,
          status, user_type, user_level, monthly_expire_time, vip_expire_time, created_at, updated_at
          FROM %s WHERE user_id = ?`

query = d.setAccountTable(query, userId)
err = db.Get(&account, query, userId)
```

### 2. 分表方法重构

#### **原始风格的setAccountTable方法**
```go
// 接收query模板和userId，返回完整的SQL语句
func (d *Dao) setAccountTable(query string, userId uint64) string {
    shardIndex := userId % common.AccountTableShardNum
    tableName := fmt.Sprintf("account%02d", shardIndex)
    return fmt.Sprintf(query, tableName)
}
```

#### **重构前（错误风格）**
```go
// 只返回表名
func (d *Dao) setAccountTable(userId uint64) string {
    shardIndex := userId % common.AccountTableShardNum
    return fmt.Sprintf("account%02d", shardIndex)
}
```

### 3. 数据库操作方法

#### **统一使用sqlx的简化方法**
```go
// 查询单条记录
err = db.Get(&account, query, userId)

// 查询多条记录
err = db.Select(&logs, dataQuery, args...)

// 执行更新/插入
_, err = db.Exec(query, args...)

// 事务操作
tx, err := db.Beginx()
defer tx.Rollback()
err = tx.Get(&account, query, userId)
err = tx.Commit()
```

#### **不再使用Context版本的方法**
```go
// 重构前（冗余）
err = db.GetContext(ctx, &account, query, userId)
err = db.SelectContext(ctx, &logs, dataQuery, args...)
err = db.ExecContext(ctx, query, args...)

// 重构后（简化）
err = db.Get(&account, query, userId)
err = db.Select(&logs, dataQuery, args...)
err = db.Exec(query, args...)
```

## 重构的方法列表

### 1. GetAccountByUserId
```go
func (d *Dao) GetAccountByUserId(ctx context.Context, userId uint64) (*po.Account, error) {
    db, err := d.GetDB(userId)
    if err != nil {
        return nil, err
    }

    var account po.Account
    query := `SELECT account_id, user_id, coin_balance, total_recharged, total_consumed,
              status, user_type, user_level, monthly_expire_time, vip_expire_time, created_at, updated_at
              FROM %s WHERE user_id = ?`

    query = d.setAccountTable(query, userId)
    err = db.Get(&account, query, userId)
    if err != nil {
        if err == sql.ErrNoRows {
            return nil, nil // 账户不存在
        }
        return nil, fmt.Errorf("failed to get account: %w", err)
    }

    return &account, nil
}
```

### 2. CreateAccount
```go
func (d *Dao) CreateAccount(ctx context.Context, userId uint64) (*po.Account, error) {
    // 使用模板查询方式
    query := `INSERT INTO %s (user_id, coin_balance, total_recharged, total_consumed,
              status, user_type, user_level, created_at, updated_at)
              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`

    query = d.setAccountTable(query, userId)
    result, err := db.Exec(query, args...)
    // ...
}
```

### 3. UpdateAccountBalance
```go
func (d *Dao) UpdateAccountBalance(ctx context.Context, userId uint64, amount float64, transactionType string) (*po.Account, error) {
    // 事务处理
    tx, err := db.Beginx()
    if err != nil {
        return nil, fmt.Errorf("failed to begin transaction: %w", err)
    }
    defer tx.Rollback()

    // 查询使用模板方式
    query := `SELECT ... FROM %s WHERE user_id = ? FOR UPDATE`
    query = d.setAccountTable(query, userId)
    err = tx.Get(&account, query, userId)
    
    // 更新使用模板方式
    updateQuery := `UPDATE %s SET coin_balance = ?, ... WHERE user_id = ?`
    updateQuery = d.setAccountTable(updateQuery, userId)
    _, err = tx.Exec(updateQuery, args...)
    
    err = tx.Commit()
    // ...
}
```

### 4. CreateAccountLog
```go
func (d *Dao) CreateAccountLog(ctx context.Context, log *po.AccountLog) error {
    query := `INSERT INTO %s (account_id, user_id, transaction_type, amount,
              balance_before, balance_after, order_id, book_id, chapter_id, description,
              extra_data, created_at)
              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

    query = d.setAccountLogTable(query, log.UserId)
    _, err = db.Exec(query, args...)
    // ...
}
```

### 5. GetAccountLogs
```go
func (d *Dao) GetAccountLogs(ctx context.Context, userId uint64, page, pageSize int32, transactionType string) ([]*po.AccountLog, int64, error) {
    // 计数查询
    countQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s %s", "%s", whereClause)
    countQuery = d.setAccountLogTable(countQuery, userId)
    err = db.Get(&total, countQuery, args...)

    // 数据查询
    dataQuery := fmt.Sprintf(`SELECT ... FROM %s %s ORDER BY created_at DESC LIMIT ? OFFSET ?`, "%s", whereClause)
    dataQuery = d.setAccountLogTable(dataQuery, userId)
    err = db.Select(&logs, dataQuery, args...)
    // ...
}
```

### 6. UpdateUserStatus
```go
func (d *Dao) UpdateUserStatus(ctx context.Context, userId uint64, userType int32, vipExpireTime, monthlyExpireTime *time.Time) (*po.Account, error) {
    query := `UPDATE %s SET user_type = ?, vip_expire_time = ?, monthly_expire_time = ?,
              updated_at = ? WHERE user_id = ?`

    query = d.setAccountTable(query, userId)
    _, err = db.Exec(query, userType, vipExpireTime, monthlyExpireTime, time.Now(), userId)
    // ...
}
```

## 新增的辅助方法

### GetDB方法
```go
// GetDB 获取数据库连接
func (d *Dao) GetDB(userId uint64) (*sqlx.DB, error) {
    return d.msshard.GetDB(userId)
}
```

### 重构的分表方法
```go
// setAccountTable 设置账户分表名称
func (d *Dao) setAccountTable(query string, userId uint64) string {
    shardIndex := userId % common.AccountTableShardNum
    tableName := fmt.Sprintf("account%02d", shardIndex)
    return fmt.Sprintf(query, tableName)
}

// setAccountLogTable 设置账户日志分表名称
func (d *Dao) setAccountLogTable(query string, userId uint64) string {
    shardIndex := userId % common.AccountLogTableShardNum
    tableName := fmt.Sprintf("account_log%02d", shardIndex)
    return fmt.Sprintf(query, tableName)
}
```

## 优势

### 1. 代码风格一致性
- ✅ 与原始项目完全一致的查询风格
- ✅ 统一的分表处理方式
- ✅ 相同的错误处理模式

### 2. 性能优化
- ✅ 减少字符串拼接操作
- ✅ 使用sqlx的简化方法
- ✅ 统一的事务处理方式

### 3. 可维护性
- ✅ 清晰的SQL模板结构
- ✅ 统一的分表逻辑
- ✅ 一致的错误处理

### 4. 分表支持
- ✅ 7分表设计（account00-06, account_log00-06）
- ✅ 基于userId的分表路由
- ✅ 统一的分表算法

现在gen\app\base\account\dao\account.go完全符合原始项目的实现风格，提供了高质量、可维护的数据访问层代码。
