# Purchase DAO重构总结

## 概述

按照与Account DAO相同的风格，重新实现了Purchase Base层的DAO和服务层，采用了统一的查询风格、分表处理方式和错误处理模式。

## 关键变更

### 1. 新增Purchase DAO方法

#### **gen/app/base/purchase/dao/purchase.go**
```go
// 核心DAO方法
func (d *Dao) CreatePurchaseOrder(ctx context.Context, order *po.PurchaseOrder) error
func (d *Dao) GetPurchaseOrdersByUserId(ctx context.Context, userId uint64, page, pageSize int32, bookId string) ([]*po.PurchaseOrder, int64, error)
func (d *Dao) CheckChapterPurchased(ctx context.Context, userId uint64, bookId string, chapterOrder uint32) (*po.PurchaseOrder, error)
func (d *Dao) GetPurchasedChaptersByBookId(ctx context.Context, userId uint64, bookId string, page, pageSize int32) ([]*po.PurchaseOrder, int64, error)
func (d *Dao) CreateVipMonthlyOrder(ctx context.Context, order *po.VipMonthlyOrder) error
func (d *Dao) GetVipMonthlyOrdersByUserId(ctx context.Context, userId uint64, page, pageSize int32, orderType string) ([]*po.VipMonthlyOrder, int64, error)
func (d *Dao) CheckVipStatus(ctx context.Context, userId uint64) (*po.VipMonthlyOrder, error)
func (d *Dao) CheckMonthlyStatus(ctx context.Context, userId uint64) (*po.VipMonthlyOrder, error)
```

### 2. 统一的查询风格

#### **原始风格实现**
```go
// CreatePurchaseOrder - 创建购买订单
func (d *Dao) CreatePurchaseOrder(ctx context.Context, order *po.PurchaseOrder) error {
    db, err := d.GetDB(order.UserId)
    if err != nil {
        return err
    }

    query := `INSERT INTO %s (order_id, account_id, user_id, order_type, book_id, book_name,
              chapter_id, chapter_title, chapter_order, coin_amount, status, created_at, updated_at)
              VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

    query = d.setPurchaseOrderTable(query, order.UserId)
    _, err = db.Exec(query, args...)
    return err
}

// GetPurchaseOrdersByUserId - 分页查询订单
func (d *Dao) GetPurchaseOrdersByUserId(ctx context.Context, userId uint64, page, pageSize int32, bookId string) ([]*po.PurchaseOrder, int64, error) {
    // 计数查询
    countQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s %s", "%s", whereClause)
    countQuery = d.setPurchaseOrderTable(countQuery, userId)
    err = db.Get(&total, countQuery, args...)

    // 数据查询
    dataQuery := fmt.Sprintf(`SELECT ... FROM %s %s ORDER BY created_at DESC LIMIT ? OFFSET ?`, "%s", whereClause)
    dataQuery = d.setPurchaseOrderTable(dataQuery, userId)
    err = db.Select(&orders, dataQuery, args...)
}
```

### 3. 分表方法实现

#### **Purchase订单分表**
```go
// setPurchaseOrderTable 设置购买订单分表名称
func (d *Dao) setPurchaseOrderTable(query string, userId uint64) string {
    shardIndex := userId % common.PurchaseOrderTableShardNum
    tableName := fmt.Sprintf("purchase_order%02d", shardIndex)
    return fmt.Sprintf(query, tableName)
}
```

#### **VIP/包月订单分表**
```go
// setVipMonthlyOrderTable 设置VIP/包月订单分表名称
func (d *Dao) setVipMonthlyOrderTable(query string, userId uint64) string {
    shardIndex := userId % common.VipMonthlyOrderTableShardNum
    tableName := fmt.Sprintf("vipmonthly_order%02d", shardIndex)
    return fmt.Sprintf(query, tableName)
}
```

### 4. 服务层重构

#### **业务逻辑优化**
```go
// PurchaseChapter - 购买章节（重构后）
func (s *Service) PurchaseChapter(ctx context.Context, req *pb.PurchaseChapterReq) (*pb.PurchaseChapterResp, error) {
    // 1. 先扣除书币
    deductResp, err := s.accountClient.DeductCoins(ctx, deductReq)
    
    // 2. 批量创建订单
    for i, chapterOrder := range chapterOrders {
        order := &po.PurchaseOrder{
            OrderId:      orderId,
            Status:       common.OrderStatusSuccess, // 直接设置为成功
            // ...
        }
        err = s.dao.CreatePurchaseOrder(ctx, order)
    }
    
    return &pb.PurchaseChapterResp{Orders: pbOrders}, nil
}
```

#### **新增的服务方法**
```go
// 完整的服务方法列表
func (s *Service) PurchaseChapter(ctx context.Context, req *pb.PurchaseChapterReq) (*pb.PurchaseChapterResp, error)
func (s *Service) PurchaseMonthly(ctx context.Context, req *pb.PurchaseMonthlyReq) (*pb.PurchaseMonthlyResp, error)
func (s *Service) PurchaseVip(ctx context.Context, req *pb.PurchaseVipReq) (*pb.PurchaseVipResp, error)
func (s *Service) GetPurchaseOrders(ctx context.Context, req *pb.GetPurchaseOrdersReq) (*pb.GetPurchaseOrdersResp, error)
func (s *Service) GetVipMonthlyOrders(ctx context.Context, req *pb.GetVipMonthlyOrdersReq) (*pb.GetVipMonthlyOrdersResp, error)
func (s *Service) CheckChapterPurchased(ctx context.Context, req *pb.CheckChapterPurchasedReq) (*pb.CheckChapterPurchasedResp, error)
func (s *Service) CheckVipStatus(ctx context.Context, req *pb.CheckVipStatusReq) (*pb.CheckVipStatusResp, error)
func (s *Service) CheckMonthlyStatus(ctx context.Context, req *pb.CheckMonthlyStatusReq) (*pb.CheckMonthlyStatusResp, error)
func (s *Service) GetPurchasedChapters(ctx context.Context, req *pb.GetPurchasedChaptersReq) (*pb.GetPurchasedChaptersResp, error)
```

## 主要功能实现

### 1. 章节购买功能
```go
// 支持批量购买章节
// 先扣除书币，再创建订单记录
// 直接设置订单状态为成功，无需额外更新
```

### 2. VIP/包月购买功能
```go
// 创建VIP/包月订单
// 设置开始时间和结束时间
// 更新用户账户状态
```

### 3. 状态检查功能
```go
// CheckChapterPurchased - 检查章节购买状态
// CheckVipStatus - 检查VIP状态（基于时间有效性）
// CheckMonthlyStatus - 检查包月状态（基于时间有效性）
```

### 4. 订单查询功能
```go
// GetPurchaseOrders - 分页查询购买订单
// GetVipMonthlyOrders - 分页查询VIP/包月订单
// GetPurchasedChapters - 查询已购买章节列表
```

## 分表设计

### 1. 购买订单分表
```sql
-- purchase_order00 到 purchase_order06 (7个分表)
-- 基于userId进行分表路由
```

### 2. VIP/包月订单分表
```sql
-- vipmonthly_order00 到 vipmonthly_order06 (7个分表)
-- 基于userId进行分表路由
```

### 3. 分表算法
```go
shardIndex := userId % common.TableShardNum
tableName := fmt.Sprintf("table_name%02d", shardIndex)
```

## 错误处理

### 1. 统一的错误处理模式
```go
if err == sql.ErrNoRows {
    return nil, nil // 记录不存在
}
return nil, fmt.Errorf("failed to operation: %w", err)
```

### 2. 业务逻辑验证
```go
// 参数验证
if req.UserId == 0 || req.BookId == "" {
    return nil, fmt.Errorf("invalid parameters")
}

// 余额检查
if accountResp.Account.CoinBalance < totalAmount {
    return nil, fmt.Errorf("insufficient balance")
}
```

## 性能优化

### 1. 批量操作
```go
// 批量创建章节购买订单
// 一次性扣除总金额，避免多次账户操作
```

### 2. 索引优化
```sql
-- 基于userId, bookId, chapterOrder的复合索引
-- 基于userId, orderType, status, endTime的复合索引
```

### 3. 分页查询
```go
// 使用LIMIT和OFFSET进行分页
// 先查询总数，再查询数据
```

## 业务特性

### 1. 订单状态管理
```go
const (
    OrderStatusPending = 1  // 待支付
    OrderStatusSuccess = 2  // 支付成功
    OrderStatusFailed  = 3  // 支付失败
)
```

### 2. 订单类型管理
```go
const (
    OrderTypeChapter = "chapter"  // 章节购买
    OrderTypeVip     = "vip"      // VIP购买
    OrderTypeMonthly = "monthly"  // 包月购买
)
```

### 3. 时间有效性检查
```go
// VIP和包月状态检查基于endTime > now()
// 自动过期处理
```

## 优势总结

### 1. 代码一致性
- ✅ 与Account DAO完全一致的实现风格
- ✅ 统一的查询模板和分表处理
- ✅ 相同的错误处理模式

### 2. 功能完整性
- ✅ 支持章节、VIP、包月三种购买类型
- ✅ 完整的订单管理和状态查询
- ✅ 分页查询和条件过滤

### 3. 性能优化
- ✅ 7分表设计，支持高并发
- ✅ 批量操作减少数据库交互
- ✅ 合理的索引设计

### 4. 业务逻辑
- ✅ 先扣费后创建订单的安全模式
- ✅ 基于时间的VIP/包月有效性检查
- ✅ 完整的权限验证机制

现在Purchase模块完全符合原始项目的实现风格，提供了高质量、可维护的购买功能实现。
