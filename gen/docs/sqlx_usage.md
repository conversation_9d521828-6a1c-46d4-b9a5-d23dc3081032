# Account微服务 - sqlx使用说明

## 概述

Account微服务的DAO层已经更新为使用sqlx模式，与现有的purchase模块保持一致。这确保了代码风格和数据库操作模式的统一性。

## sqlx使用模式

### 1. 数据库连接管理

```go
type Dao struct {
    msshard mysql.Mysqler  // 使用mysql.Mysqler接口
    cache   redis.Redis
    conf    *conf.Config
}

// 获取数据库连接
func (d *Dao) GetDB(userId string) (*sqlx.DB, error) {
    db, err := d.msshard.DB(userId)
    if err != nil {
        return nil, fmt.Errorf("failed to get database connection: %w", err)
    }
    return db, nil
}
```

### 2. SQL常量定义

所有SQL语句都定义为常量，便于维护和复用：

```go
// dao/sql.go
const (
    SelectAccountByUserId_SQL = "SELECT account_id, user_id, coin_balance, total_recharged, total_consumed, status, created_at, updated_at FROM account WHERE user_id = ?"
    
    InsertAccount_SQL = "INSERT INTO account (user_id, coin_balance, total_recharged, total_consumed, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?)"
    
    UpdateAccountBalance_SQL = "UPDATE account SET coin_balance = ?, updated_at = ? WHERE account_id = ?"
    
    SelectAccountForUpdate_SQL = "SELECT account_id, user_id, coin_balance, total_recharged, total_consumed, status FROM account WHERE user_id = ? FOR UPDATE"
)
```

### 3. 基本查询操作

#### 单行查询 - 使用 `db.Get()`
```go
func (d *Dao) GetAccountByUserId(ctx context.Context, userId string) (*model.Account, error) {
    db, err := d.GetDB(userId)
    if err != nil {
        return nil, err
    }

    var account model.Account
    err = db.Get(&account, SelectAccountByUserId_SQL, userId)
    if err != nil {
        if err == sql.ErrNoRows {
            return nil, nil // 账户不存在
        }
        return nil, fmt.Errorf("failed to get account: %w", err)
    }

    return &account, nil
}
```

#### 多行查询 - 使用 `db.Select()`
```go
func (d *Dao) GetAccountLogs(ctx context.Context, userId string, page, pageSize int, transactionType string) ([]*model.AccountLog, int64, error) {
    db, err := d.GetDB(userId)
    if err != nil {
        return nil, 0, err
    }

    // 查询总数
    var total int64
    err = db.Get(&total, CountAccountLogs_SQL, userId)
    if err != nil {
        return nil, 0, fmt.Errorf("failed to count account logs: %w", err)
    }

    // 查询数据
    var logs []*model.AccountLog
    err = db.Select(&logs, SelectAccountLogs_SQL, userId, pageSize, offset)
    if err != nil {
        return nil, 0, fmt.Errorf("failed to get account logs: %w", err)
    }

    return logs, total, nil
}
```

#### 插入操作 - 使用 `db.Exec()`
```go
func (d *Dao) CreateAccount(ctx context.Context, userId string) (*model.Account, error) {
    db, err := d.GetDB(userId)
    if err != nil {
        return nil, err
    }

    now := time.Now()
    account := &model.Account{
        UserId:         userId,
        CoinBalance:    "0.00",
        TotalRecharged: "0.00",
        TotalConsumed:  "0.00",
        Status:         model.AccountStatusNormal,
        CreatedAt:      now,
        UpdatedAt:      now,
    }

    result, err := db.Exec(InsertAccount_SQL, account.UserId, account.CoinBalance,
        account.TotalRecharged, account.TotalConsumed, account.Status,
        account.CreatedAt, account.UpdatedAt)
    if err != nil {
        return nil, fmt.Errorf("failed to create account: %w", err)
    }

    accountId, err := result.LastInsertId()
    if err != nil {
        return nil, fmt.Errorf("failed to get account id: %w", err)
    }

    account.AccountId = uint64(accountId)
    return account, nil
}
```

### 4. 事务操作

使用mysql包提供的事务处理函数：

```go
func (d *Dao) UpdateAccountBalance(ctx context.Context, req *model.TransactionRequest) error {
    db, err := d.GetDB(req.UserId)
    if err != nil {
        return err
    }

    // 使用mysql包的事务处理
    return mysql.Transact(db, func(tx *sqlx.Tx) error {
        // 获取当前账户信息（加锁）
        var account model.Account
        err = tx.Get(&account, SelectAccountForUpdate_SQL, req.UserId)
        if err != nil {
            return fmt.Errorf("failed to get account for update: %w", err)
        }

        // 检查账户状态
        if account.Status != model.AccountStatusNormal {
            return fmt.Errorf("account is not active, status: %d", account.Status)
        }

        // 计算新余额
        currentBalance, err := utils.ParseDecimal(account.CoinBalance)
        if err != nil {
            return fmt.Errorf("failed to parse current balance: %w", err)
        }

        amount, err := utils.ParseDecimal(req.Amount)
        if err != nil {
            return fmt.Errorf("failed to parse amount: %w", err)
        }

        newBalance := currentBalance.Add(amount)
        
        // 检查余额是否足够（如果是扣款）
        if amount.IsNegative() && newBalance.IsNegative() {
            return fmt.Errorf("insufficient balance")
        }

        // 更新账户余额
        _, err = tx.Exec(UpdateAccountBalance_SQL, newBalance.String(), time.Now(), account.AccountId)
        if err != nil {
            return fmt.Errorf("failed to update account balance: %w", err)
        }

        // 记录账户日志
        _, err = tx.Exec(InsertAccountLog_SQL, account.AccountId, req.UserId, req.TransactionType,
            req.Amount, account.CoinBalance, newBalance.String(), req.OrderId, req.BookId,
            req.ChapterId, req.Description, req.ExtraData, time.Now())
        if err != nil {
            return fmt.Errorf("failed to insert account log: %w", err)
        }

        // 事务成功，清除缓存
        if d.conf.Account.EnableCache {
            d.deleteAccountFromCache(ctx, req.UserId)
        }

        return nil
    })
}
```

### 5. 分库分表支持

```go
// 获取账户表分片ID
func (d *Dao) getAccountTableShard(userId string) (int, error) {
    // 简化实现，实际可以根据userId进行hash分片
    // 参照purchase模块的分片逻辑
    return 0, nil
}

// 获取订单表分片ID
func (d *Dao) getOrderTableShard(accountId uint64) (int, error) {
    // 参照purchase模块的分片逻辑
    return 0, nil
    
    // 实际分片逻辑（注释掉，因为当前简化实现）
    // tableId := int(accountId % ORDER_TABLE_HASH)
    // return tableId, nil
}
```

## 与purchase模块的一致性

### 1. 相同的连接管理模式
- 使用 `mysql.Mysqler` 接口
- 使用 `d.msshard.DB(userId)` 获取连接
- 返回 `*sqlx.DB` 类型

### 2. 相同的查询模式
- 单行查询：`db.Get(&struct, sql, args...)`
- 多行查询：`db.Select(&slice, sql, args...)`
- 执行操作：`db.Exec(sql, args...)`

### 3. 相同的事务模式
- 使用 `mysql.Transact(db, func(tx *sqlx.Tx) error {...})`
- 事务内使用 `tx.Get()`, `tx.Select()`, `tx.Exec()`

### 4. 相同的错误处理
- 统一的错误包装：`fmt.Errorf("operation failed: %w", err)`
- 统一的空结果处理：检查 `sql.ErrNoRows`

## 优势

1. **一致性**: 与现有purchase模块保持完全一致的代码风格
2. **可维护性**: SQL常量化，便于维护和修改
3. **性能**: sqlx提供了更好的性能和类型安全
4. **事务安全**: 使用统一的事务处理模式
5. **分片支持**: 支持分库分表的扩展

## 注意事项

1. **结构体标签**: 确保model结构体使用正确的`db`标签
2. **SQL占位符**: 使用`?`作为参数占位符
3. **错误处理**: 始终检查`sql.ErrNoRows`来判断记录是否存在
4. **事务回滚**: 使用`mysql.Transact`自动处理事务提交和回滚
5. **连接管理**: 通过`d.msshard.DB(userId)`获取正确的分片连接
