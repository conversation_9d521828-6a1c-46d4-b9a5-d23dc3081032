# Protobuf字段命名修正总结

## 概述

将account.proto和purchase.proto中所有使用下划线"_"的字段改为驼峰命名方式，并更新了相关字段的处理代码，确保整个系统的命名一致性。

## 修正的字段

### 1. **Account.proto字段修正**

#### **AccountInfo消息**
```protobuf
// ❌ 修正前（使用下划线）
message AccountInfo {
  uint64 account_id = 1;
  uint64 user_id = 2;
  double coin_balance = 3;
  double total_recharged = 4;
  double total_consumed = 5;
  int32 user_type = 7;
  int32 user_level = 8;
  int64 vip_expire_time = 9;
  int64 monthly_expire_time = 10;
  int64 created_at = 11;
  int64 updated_at = 12;
}

// ✅ 修正后（使用驼峰命名）
message AccountInfo {
  uint64 accountId = 1;
  uint64 userId = 2;
  double coinBalance = 3;
  double totalRecharged = 4;
  double totalConsumed = 5;
  int32 userType = 7;
  int32 userLevel = 8;
  int64 vipExpireTime = 9;
  int64 monthlyExpireTime = 10;
  int64 createdAt = 11;
  int64 updatedAt = 12;
}
```

#### **AccountLog消息**
```protobuf
// ❌ 修正前
message AccountLog {
  uint64 log_id = 1;
  uint64 account_id = 2;
  uint64 user_id = 3;
  string transaction_type = 4;
  double balance_before = 6;
  double balance_after = 7;
  string order_id = 8;
  string book_id = 9;
  string chapter_id = 10;
  string extra_data = 12;
  int64 created_at = 13;
}

// ✅ 修正后
message AccountLog {
  uint64 logId = 1;
  uint64 accountId = 2;
  uint64 userId = 3;
  string transactionType = 4;
  double balanceBefore = 6;
  double balanceAfter = 7;
  string orderId = 8;
  string bookId = 9;
  string chapterId = 10;
  string extraData = 12;
  int64 createdAt = 13;
}
```

#### **RechargeOrder消息**
```protobuf
// ❌ 修正前
message RechargeOrder {
  string order_id = 1;
  uint64 account_id = 2;
  uint64 user_id = 3;
  double coin_amount = 5;
  float exchange_rate = 6;
  string payment_method = 7;
  string payment_order_id = 8;
  int64 paid_at = 10;
  int64 created_at = 11;
  int64 updated_at = 12;
}

// ✅ 修正后
message RechargeOrder {
  string orderId = 1;
  uint64 accountId = 2;
  uint64 userId = 3;
  double coinAmount = 5;
  float exchangeRate = 6;
  string paymentMethod = 7;
  string paymentOrderId = 8;
  int64 paidAt = 10;
  int64 createdAt = 11;
  int64 updatedAt = 12;
}
```

#### **请求和响应消息**
```protobuf
// ❌ 修正前
message GetAccountReq {
  uint64 user_id = 1;
}

message RechargeReq {
  uint64 user_id = 1;
  string payment_method = 3;
  float exchange_rate = 4;
}

message GetAccountLogsReq {
  uint64 user_id = 1;
  int32 page_size = 3;
  string transaction_type = 4;
}

message UpdateUserStatusReq {
  uint64 user_id = 1;
  int32 user_type = 2;
  int64 vip_expire_time = 3;
  int64 monthly_expire_time = 4;
}

message DeductCoinsReq {
  uint64 user_id = 1;
  string order_id = 3;
  string book_id = 4;
  string chapter_id = 5;
  string transaction_type = 6;
}

message CheckUserStatusReq {
  uint64 user_id = 1;
}

message CheckUserStatusResp {
  bool has_vip = 4;
  bool has_monthly = 5;
}

// ✅ 修正后
message GetAccountReq {
  uint64 userId = 1;
}

message RechargeReq {
  uint64 userId = 1;
  string paymentMethod = 3;
  float exchangeRate = 4;
}

message GetAccountLogsReq {
  uint64 userId = 1;
  int32 pageSize = 3;
  string transactionType = 4;
}

message UpdateUserStatusReq {
  uint64 userId = 1;
  int32 userType = 2;
  int64 vipExpireTime = 3;
  int64 monthlyExpireTime = 4;
}

message DeductCoinsReq {
  uint64 userId = 1;
  string orderId = 3;
  string bookId = 4;
  string chapterId = 5;
  string transactionType = 6;
}

message CheckUserStatusReq {
  uint64 userId = 1;
}

message CheckUserStatusResp {
  bool hasVip = 4;
  bool hasMonthly = 5;
}
```

### 2. **Purchase.proto字段修正**

#### **ChapterOrder消息**
```protobuf
// ❌ 修正前
message ChapterOrder {
  string chapter_id = 1;
  string chapter_title = 2;
  uint32 chapter_order = 3;
  double coin_amount = 4;
}

// ✅ 修正后
message ChapterOrder {
  string chapterId = 1;
  string chapterTitle = 2;
  uint32 chapterOrder = 3;
  double coinAmount = 4;
}
```

#### **PurchaseOrder消息**
```protobuf
// ❌ 修正前
message PurchaseOrder {
  string order_id = 1;
  uint64 account_id = 2;
  uint64 user_id = 3;
  string order_type = 4;
  string book_id = 5;
  string book_name = 6;
  string chapter_id = 7;
  string chapter_title = 8;
  uint32 chapter_order = 9;
  double coin_amount = 10;
  int64 created_at = 12;
  int64 updated_at = 13;
}

// ✅ 修正后
message PurchaseOrder {
  string orderId = 1;
  uint64 accountId = 2;
  uint64 userId = 3;
  string orderType = 4;
  string bookId = 5;
  string bookName = 6;
  string chapterId = 7;
  string chapterTitle = 8;
  uint32 chapterOrder = 9;
  double coinAmount = 10;
  int64 createdAt = 12;
  int64 updatedAt = 13;
}
```

#### **VipMonthlyOrder消息**
```protobuf
// ❌ 修正前
message VipMonthlyOrder {
  string order_id = 1;
  uint64 account_id = 2;
  uint64 user_id = 3;
  string order_type = 4;
  double coin_amount = 5;
  int32 duration_days = 6;
  int64 start_time = 7;
  int64 end_time = 8;
  int64 created_at = 10;
  int64 updated_at = 11;
}

// ✅ 修正后
message VipMonthlyOrder {
  string orderId = 1;
  uint64 accountId = 2;
  uint64 userId = 3;
  string orderType = 4;
  double coinAmount = 5;
  int32 durationDays = 6;
  int64 startTime = 7;
  int64 endTime = 8;
  int64 createdAt = 10;
  int64 updatedAt = 11;
}
```

#### **其他Purchase消息**
```protobuf
// ❌ 修正前
message ChapterPurchaseInfo {
  string order_id = 1;
  string chapter_id = 2;
  string chapter_title = 3;
  uint32 chapter_order = 4;
  double coin_amount = 5;
  int64 purchased_at = 6;
  bool is_monthly = 7;
  bool is_vip = 8;
}

message ChapterPurchaseItem {
  uint32 chapter_order = 1;
  double coin_amount = 2;
}

message PurchaseChapterReq {
  uint64 user_id = 1;
  string book_id = 2;
}

message PurchaseMonthlyReq {
  uint64 user_id = 1;
  double coin_amount = 2;
  int32 duration_days = 3;
}

message PurchaseVipReq {
  uint64 user_id = 1;
  double coin_amount = 2;
  int32 duration_days = 3;
}

// ✅ 修正后
message ChapterPurchaseInfo {
  string orderId = 1;
  string chapterId = 2;
  string chapterTitle = 3;
  uint32 chapterOrder = 4;
  double coinAmount = 5;
  int64 purchasedAt = 6;
  bool isMonthly = 7;
  bool isVip = 8;
}

message ChapterPurchaseItem {
  uint32 chapterOrder = 1;
  double coinAmount = 2;
}

message PurchaseChapterReq {
  uint64 userId = 1;
  string bookId = 2;
}

message PurchaseMonthlyReq {
  uint64 userId = 1;
  double coinAmount = 2;
  int32 durationDays = 3;
}

message PurchaseVipReq {
  uint64 userId = 1;
  double coinAmount = 2;
  int32 durationDays = 3;
}
```

## 更新的处理代码

### 1. **Account API层更新**

#### **gen/app/api/account/svc/convert.go**
```go
// ✅ 更新类型引用
func (s *AccountSvc) convertAccountFromPB(pbAccount *accountpb.AccountInfo) *vo.Account {
    // 字段访问保持不变，因为Go生成的代码会自动处理驼峰命名
    account := &vo.Account{
        AccountId:      pbAccount.AccountId,
        UserId:         pbAccount.UserId,
        CoinBalance:    pbAccount.CoinBalance,
        TotalRecharged: pbAccount.TotalRecharged,
        TotalConsumed:  pbAccount.TotalConsumed,
        // ...
    }
    return account
}
```

#### **gen/app/api/account/svc/account.go**
```go
// ✅ 字段映射保持不变
func (s *AccountSvc) Recharge(ctx context.Context, req *vo.RechargeReq) (*vo.RechargeResp, error) {
    pbReq := &accountpb.RechargeReq{
        UserId:        req.UserId,        // 自动映射到 userId
        Amount:        req.Amount,
        PaymentMethod: req.PaymentMethod, // 自动映射到 paymentMethod
        ExchangeRate:  req.ExchangeRate,  // 自动映射到 exchangeRate
    }
    // ...
}
```

### 2. **Base层更新**

#### **gen/app/base/account/svc/convert.go**
```go
// ✅ 更新返回类型
func (s *AccountSvc) convertAccountToPB(account *po.Account) *pb.AccountInfo {
    pbAccount := &pb.AccountInfo{
        AccountId:      account.AccountId,      // 映射到 accountId
        UserId:         account.UserId,         // 映射到 userId
        CoinBalance:    account.CoinBalance,    // 映射到 coinBalance
        TotalRecharged: account.TotalRecharged, // 映射到 totalRecharged
        TotalConsumed:  account.TotalConsumed,  // 映射到 totalConsumed
        UserType:       account.UserType,       // 映射到 userType
        UserLevel:      account.UserLevel,      // 映射到 userLevel
        CreatedAt:      account.CreatedAt.Unix(), // 映射到 createdAt
        UpdatedAt:      account.UpdatedAt.Unix(), // 映射到 updatedAt
    }

    if account.VipExpireTime != nil {
        pbAccount.VipExpireTime = account.VipExpireTime.Unix() // 映射到 vipExpireTime
    }

    if account.MonthlyExpireTime != nil {
        pbAccount.MonthlyExpireTime = account.MonthlyExpireTime.Unix() // 映射到 monthlyExpireTime
    }

    return pbAccount
}
```

## 命名规范

### 1. **驼峰命名规则**
- ✅ `user_id` → `userId`
- ✅ `account_id` → `accountId`
- ✅ `coin_balance` → `coinBalance`
- ✅ `total_recharged` → `totalRecharged`
- ✅ `total_consumed` → `totalConsumed`
- ✅ `user_type` → `userType`
- ✅ `user_level` → `userLevel`
- ✅ `vip_expire_time` → `vipExpireTime`
- ✅ `monthly_expire_time` → `monthlyExpireTime`
- ✅ `created_at` → `createdAt`
- ✅ `updated_at` → `updatedAt`

### 2. **布尔字段命名**
- ✅ `has_vip` → `hasVip`
- ✅ `has_monthly` → `hasMonthly`
- ✅ `is_purchased` → `isPurchased`
- ✅ `is_monthly` → `isMonthly`
- ✅ `is_vip` → `isVip`
- ✅ `is_active` → `isActive`

### 3. **复合字段命名**
- ✅ `transaction_type` → `transactionType`
- ✅ `balance_before` → `balanceBefore`
- ✅ `balance_after` → `balanceAfter`
- ✅ `order_id` → `orderId`
- ✅ `book_id` → `bookId`
- ✅ `chapter_id` → `chapterId`
- ✅ `payment_method` → `paymentMethod`
- ✅ `payment_order_id` → `paymentOrderId`
- ✅ `exchange_rate` → `exchangeRate`
- ✅ `coin_amount` → `coinAmount`
- ✅ `page_size` → `pageSize`
- ✅ `duration_days` → `durationDays`
- ✅ `start_time` → `startTime`
- ✅ `end_time` → `endTime`
- ✅ `purchased_at` → `purchasedAt`
- ✅ `paid_at` → `paidAt`

## 兼容性处理

### 1. **Go代码自动适配**
- ✅ protobuf生成的Go代码会自动将驼峰命名转换为Go风格的字段名
- ✅ 现有的Go代码访问字段的方式保持不变
- ✅ JSON序列化会使用protobuf中定义的字段名

### 2. **类型引用更新**
- ✅ `*pb.Account` → `*pb.AccountInfo`
- ✅ 保持其他消息类型不变

### 3. **字段访问一致性**
- ✅ Go代码中的字段访问方式保持不变
- ✅ protobuf字段映射自动处理
- ✅ JSON输出使用新的驼峰命名

## 优势总结

### 1. **命名一致性**
- ✅ 所有protobuf字段使用统一的驼峰命名
- ✅ 符合现代API设计规范
- ✅ 提高代码可读性

### 2. **维护性提升**
- ✅ 减少命名混乱
- ✅ 便于前端开发者理解
- ✅ 符合JavaScript/TypeScript命名习惯

### 3. **兼容性保证**
- ✅ Go代码无需大量修改
- ✅ 自动处理字段映射
- ✅ 保持API功能完整性

### 4. **规范性**
- ✅ 符合protobuf最佳实践
- ✅ 符合RESTful API设计规范
- ✅ 便于国际化和多语言支持

现在所有protobuf字段都使用了统一的驼峰命名方式，提供了更好的代码一致性和可维护性！
