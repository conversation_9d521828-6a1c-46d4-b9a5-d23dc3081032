# Account微服务API文档

## 概述

Account微服务负责处理用户账户管理、充值、购买等功能。采用gRPC进行服务间通信，HTTP API对外提供服务。

## 架构设计

### 服务分层
- **API层**: HTTP接口服务 (`app/api/account`)
- **Base层**: gRPC微服务 (`app/base/account`)
- **数据层**: MySQL + Redis

### 数据库设计
- `account`: 用户账户表
- `account_log`: 账户变动日志表
- `recharge_order`: 充值订单表
- `purchase_order`: 购买订单表

## HTTP API接口

### 基础URL
```
http://localhost:8081/account
```

### 1. 获取账户信息
```http
GET /account/info?userId={userId}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "accountId": 1,
    "userId": "61668aad169d3aecb0ff0af4",
    "coinBalance": "1000.00",
    "totalRecharged": "1000.00",
    "totalConsumed": "0.00",
    "status": 1,
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z"
  }
}
```

### 2. 充值
```http
POST /account/recharge
Content-Type: application/json

{
  "userId": "61668aad169d3aecb0ff0af4",
  "amount": "100.00",
  "paymentMethod": "alipay",
  "exchangeRate": "1.0000"
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "充值订单创建成功",
  "data": {
    "orderId": "****************",
    "accountId": 1,
    "userId": "61668aad169d3aecb0ff0af4",
    "amount": "100.00",
    "coinAmount": "100.00",
    "exchangeRate": "1.0000",
    "paymentMethod": "alipay",
    "status": 1,
    "createdAt": "2024-01-01T00:00:00Z"
  }
}
```

### 3. 购买章节
```http
POST /account/purchase/chapter
Content-Type: application/json

{
  "userId": "61668aad169d3aecb0ff0af4",
  "bookId": "68185f28582200008e003f72",
  "bookName": "星际龙神",
  "chapterId": "68236d0f8c3e000052006d6c",
  "chapterTitle": "序幕",
  "chapterOrder": 1,
  "coinAmount": "5.00"
}
```

### 4. 购买包月
```http
POST /account/purchase/monthly
Content-Type: application/json

{
  "userId": "61668aad169d3aecb0ff0af4",
  "bookId": "68185f28582200008e003f72",
  "bookName": "星际龙神",
  "coinAmount": "30.00",
  "durationDays": 30
}
```

### 5. 购买VIP
```http
POST /account/purchase/vip
Content-Type: application/json

{
  "userId": "61668aad169d3aecb0ff0af4",
  "coinAmount": "100.00",
  "durationDays": 30
}
```

### 6. 获取账户日志
```http
GET /account/logs?userId={userId}&page=1&pageSize=20&transactionType=purchase_chapter
```

### 7. 获取购买订单
```http
GET /account/purchase/orders?userId={userId}&page=1&pageSize=20&orderType=chapter
```

### 8. 检查章节购买状态
```http
GET /account/check/chapter?userId={userId}&bookId={bookId}&chapterId={chapterId}
```

### 9. 检查包月状态
```http
GET /account/check/monthly?userId={userId}&bookId={bookId}
```

## gRPC接口

### 服务定义
```protobuf
service Account {
  rpc GetAccount(GetAccountReq) returns (GetAccountResp);
  rpc CreateAccount(CreateAccountReq) returns (CreateAccountResp);
  rpc Recharge(RechargeReq) returns (RechargeResp);
  rpc PurchaseChapter(PurchaseChapterReq) returns (PurchaseChapterResp);
  rpc PurchaseMonthly(PurchaseMonthlyReq) returns (PurchaseMonthlyResp);
  rpc PurchaseVip(PurchaseVipReq) returns (PurchaseVipResp);
  rpc GetAccountLogs(GetAccountLogsReq) returns (GetAccountLogsResp);
  rpc GetRechargeOrder(GetRechargeOrderReq) returns (GetRechargeOrderResp);
  rpc GetPurchaseOrders(GetPurchaseOrdersReq) returns (GetPurchaseOrdersResp);
  rpc CheckChapterPurchased(CheckChapterPurchasedReq) returns (CheckChapterPurchasedResp);
  rpc CheckMonthlyStatus(CheckMonthlyStatusReq) returns (CheckMonthlyStatusResp);
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 402 | 余额不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突（如重复购买） |
| 500 | 服务器内部错误 |

## 部署说明

### 1. 数据库初始化
```bash
mysql -u root -p < gen/database/account_schema.sql
```

### 2. 启动Base服务
```bash
cd gen/app/base/account/cmd
go run main.go
```

### 3. 启动API服务
```bash
cd gen/app/api/account/cmd
go run main.go
```

### 4. 配置文件
将 `gen/config/account.toml` 复制到项目根目录的 `app/` 目录下。

## 注意事项

1. **事务安全**: 所有涉及金额变动的操作都使用数据库事务确保一致性
2. **缓存策略**: 账户信息和购买状态使用Redis缓存，提高查询性能
3. **分库分表**: 支持按用户ID进行分库分表
4. **幂等性**: 充值和购买操作具有幂等性，避免重复扣款
5. **监控**: 建议添加监控和告警，及时发现异常情况

## 扩展功能

1. **支付回调**: 需要实现支付宝、微信等第三方支付的回调处理
2. **退款功能**: 可以扩展退款相关接口
3. **优惠券**: 可以扩展优惠券和折扣功能
4. **积分系统**: 可以扩展积分获取和消费功能
