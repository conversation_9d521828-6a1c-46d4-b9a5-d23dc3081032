# 分表设计总结

## 概述

根据现有的beyondreading项目分表设计，更新了gen目录下的Account和Purchase微服务，实现了按userId进行分表的数据库架构。

## 分表策略

### 1. 分表数量
所有表都按照7个分表进行设计（00-06），分表数量常量定义在`gen/common/const.go`中：

```go
const (
    AccountTableShardNum       = 7  // account00-account06
    AccountLogTableShardNum    = 7  // account_log00-account_log06
    PurchaseOrderTableShardNum = 7  // purchase_order00-purchase_order06
    VipMonthlyOrderTableShardNum = 7 // vipmonthly_order00-vipmonthly_order06
)
```

### 2. 分表算法
使用userId取模的方式进行分表：

```go
shardIndex := userId % TableShardNum
tableName := fmt.Sprintf("table_name%02d", shardIndex)
```

### 3. 分表命名规则
- **账户表**: `account00`, `account01`, ..., `account06`
- **账户日志表**: `account_log00`, `account_log01`, ..., `account_log06`
- **购买订单表**: `purchase_order00`, `purchase_order01`, ..., `purchase_order06`
- **VIP/包月订单表**: `vipmonthly_order00`, `vipmonthly_order01`, ..., `vipmonthly_order06`

## 数据库表结构

### 1. 账户表分表 (account00-account06)
```sql
CREATE TABLE `account00` (
  `account_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID，关联用户表',
  `coin_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '书币余额',
  `total_recharged` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计充值金额',
  `total_consumed` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计消费金额',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '账户状态：1-正常，2-冻结，3-注销',
  `user_type` tinyint NOT NULL DEFAULT '1' COMMENT '用户类型：1-普通用户，2-VIP用户，3-包月用户',
  `user_level` int NOT NULL DEFAULT '1' COMMENT '用户等级（根据消费书币计算，1000书币一级）',
  `vip_expire_time` timestamp NULL DEFAULT NULL COMMENT 'VIP过期时间',
  `monthly_expire_time` timestamp NULL DEFAULT NULL COMMENT '包月过期时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`account_id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户账户表00';
```

### 2. 账户日志表分表 (account_log00-account_log06)
```sql
CREATE TABLE `account_log00` (
  `log_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `account_id` bigint unsigned NOT NULL COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `transaction_type` varchar(32) NOT NULL COMMENT '交易类型',
  `amount` decimal(15,2) NOT NULL COMMENT '变动金额',
  `balance_before` decimal(15,2) NOT NULL COMMENT '变动前余额',
  `balance_after` decimal(15,2) NOT NULL COMMENT '变动后余额',
  `order_id` varchar(64) DEFAULT NULL COMMENT '关联订单ID',
  `book_id` varchar(64) DEFAULT NULL COMMENT '书籍ID',
  `chapter_id` varchar(64) DEFAULT NULL COMMENT '章节ID',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `extra_data` text COMMENT '额外数据（JSON格式）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_transaction_type` (`transaction_type`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='账户日志表00';
```

### 3. 购买订单表分表 (purchase_order00-purchase_order06)
```sql
CREATE TABLE `purchase_order00` (
  `order_id` varchar(64) NOT NULL COMMENT '购买订单ID',
  `account_id` bigint unsigned NOT NULL COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `order_type` varchar(32) NOT NULL COMMENT '订单类型：chapter',
  `book_id` varchar(64) NOT NULL COMMENT '书籍ID',
  `book_name` varchar(128) DEFAULT NULL COMMENT '书籍名称',
  `chapter_id` varchar(64) DEFAULT NULL COMMENT '章节ID',
  `chapter_title` varchar(128) DEFAULT NULL COMMENT '章节标题',
  `chapter_order` int unsigned NOT NULL COMMENT '章节序号',
  `coin_amount` decimal(15,2) NOT NULL COMMENT '消费书币数量',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '订单状态：1-待支付，2-支付成功，3-支付失败',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_book_id` (`book_id`),
  KEY `idx_chapter_order` (`chapter_order`),
  KEY `idx_order_type` (`order_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_user_book_chapter` (`user_id`, `book_id`, `chapter_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='购买订单表（章节）00';
```

### 4. VIP/包月订单表分表 (vipmonthly_order00-vipmonthly_order06)
```sql
CREATE TABLE `vipmonthly_order00` (
  `order_id` varchar(64) NOT NULL COMMENT '订单ID',
  `account_id` bigint unsigned NOT NULL COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `order_type` varchar(32) NOT NULL COMMENT '订单类型：monthly-包月，vip-VIP',
  `coin_amount` decimal(15,2) NOT NULL COMMENT '消费书币数量',
  `duration_days` int NOT NULL COMMENT '有效期天数',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '订单状态：1-待支付，2-支付成功，3-支付失败',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_type` (`order_type`),
  KEY `idx_status` (`status`),
  KEY `idx_time_range` (`start_time`, `end_time`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='VIP/包月订单表00';
```

## DAO层分表实现

### 1. Account DAO分表方法

```go
// setAccountTable 根据userId获取分表名称
func (d *Dao) setAccountTable(userId uint64) string {
    shardIndex := userId % common.AccountTableShardNum
    return fmt.Sprintf("account%02d", shardIndex)
}

// setAccountLogTable 根据userId获取账户日志分表名称
func (d *Dao) setAccountLogTable(userId uint64) string {
    shardIndex := userId % common.AccountLogTableShardNum
    return fmt.Sprintf("account_log%02d", shardIndex)
}
```

### 2. Purchase DAO分表方法

```go
// setPurchaseOrderTable 根据userId获取购买订单分表名称
func (d *Dao) setPurchaseOrderTable(userId uint64) string {
    shardIndex := userId % common.PurchaseOrderTableShardNum
    return fmt.Sprintf("purchase_order%02d", shardIndex)
}

// setVipMonthlyOrderTable 根据userId获取VIP/包月订单分表名称
func (d *Dao) setVipMonthlyOrderTable(userId uint64) string {
    shardIndex := userId % common.VipMonthlyOrderTableShardNum
    return fmt.Sprintf("vipmonthly_order%02d", shardIndex)
}
```

### 3. 分表查询示例

```go
// 使用分表的查询示例
func (d *Dao) GetAccountByUserId(ctx context.Context, userId uint64) (*po.Account, error) {
    db, err := d.GetDB(userId)
    if err != nil {
        return nil, err
    }

    tableName := d.setAccountTable(userId)
    var account po.Account
    query := fmt.Sprintf(`SELECT account_id, user_id, coin_balance, total_recharged, total_consumed, 
              status, user_type, user_level, vip_expire_time, monthly_expire_time, 
              created_at, updated_at FROM %s WHERE user_id = ?`, tableName)

    err = db.GetContext(ctx, &account, query, userId)
    if err != nil {
        return nil, fmt.Errorf("failed to get account: %w", err)
    }

    return &account, nil
}
```

## 分表优势

### 1. 性能优化
- **数据分散**: 将大表拆分为多个小表，减少单表数据量
- **查询效率**: 减少索引大小，提高查询速度
- **并发处理**: 不同分表可以并行处理，提高并发能力

### 2. 扩展性
- **水平扩展**: 可以根据业务增长调整分表数量
- **负载均衡**: 数据均匀分布在各个分表中

### 3. 维护性
- **独立维护**: 每个分表可以独立进行维护操作
- **故障隔离**: 单个分表故障不影响其他分表

## 注意事项

### 1. 跨分表查询限制
- 无法直接进行跨分表的JOIN操作
- 需要在应用层进行数据聚合

### 2. 事务限制
- 跨分表事务需要使用分布式事务
- 建议在单个分表内完成事务操作

### 3. 数据一致性
- 需要确保相关数据在同一个分表中
- 使用userId作为分表键保证用户相关数据的一致性

## 分表路由规则

### 1. 用户数据路由
```
userId = 123
shardIndex = 123 % 7 = 4
tableName = "account04"
```

### 2. 示例用户分布
```
userId 1-6   -> account00, account_log00, purchase_order00, vipmonthly_order00
userId 7-13  -> account01, account_log01, purchase_order01, vipmonthly_order01
userId 14-20 -> account02, account_log02, purchase_order02, vipmonthly_order02
...
```

## 文件结构

```
gen/
├── common/
│   └── const.go                    # 分表常量定义
├── database/
│   └── account_schema.sql          # 分表数据库结构
├── app/base/account/dao/
│   └── dao.go                      # Account DAO分表实现
├── app/base/purchase/dao/
│   └── dao.go                      # Purchase DAO分表实现
└── docs/
    └── sharding_design_summary.md  # 分表设计总结
```

这个分表设计确保了数据的均匀分布和高效查询，同时保持了代码的可维护性和扩展性。
