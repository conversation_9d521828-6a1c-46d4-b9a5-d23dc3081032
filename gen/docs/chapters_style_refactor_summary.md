# 按照Chapters风格重构Account和Purchase代码总结

## 概述

严格按照beyondreading/app下chapters部分的代码风格，重构了account和purchase的代码，使用通用的错误处理方式`ecode.Back(c).Failure(errors.New("message"))`，并保持http.go文件逻辑与chapters一致。

## 关键改进

### 1. HTTP处理器风格统一

#### **修正前（错误的Handler模式）**
```go
// 错误的方式
type Handler struct {
    svc *svc.Service
}

func (h *Handler) RegisterRoutes(r *gin.Engine) {
    v1 := r.Group("/api/v1/account")
    {
        v1.GET("/info", h.GetAccount)
    }
}
```

#### **修正后（正确的chapters风格）**
```go
// ✅ 正确的chapters风格
var (
    service *svc.AccountSvc
)

func Start(c *conf.Config, s *svc.AccountSvc) {
    service = s
    router := bbrouter.Start(c.Base)

    v1 := router.Group("/api/v1/account")
    {
        v1.GET("/info", getAccount)
    }

    go func() {
        if err := router.Run(c.Port.HTTP); err != nil {
            panic(err)
        }
    }()
}
```

### 2. 错误处理方式统一

#### **修正前（不一致的错误处理）**
```go
// 错误的方式
c.JSON(http.StatusBadRequest, gin.H{
    "code":    400,
    "message": "Invalid parameter",
})

return &pb.Response{
    Code:    400,
    Message: "Invalid parameter",
}, nil
```

#### **修正后（统一的ecode处理）**
```go
// ✅ 统一使用ecode.Back
if param.UserId == 0 {
    ecode.Back(c).Failure(errors.New("invalid user id"))
    return
}

if err != nil {
    ecode.Back(c).Failure(err)
    return
}

ecode.Back(c).SetData(data).Success()
```

### 3. 服务层结构优化

#### **修正前（复杂的结构）**
```go
// 错误的方式
type Service struct {
    conf *conf.Config
    dao  *dao.Dao
}
```

#### **修正后（简化的chapters风格）**
```go
// ✅ 按照chapters风格
type AccountSvc struct {
    conf          *conf.Config
    dao           *dao.Dao
    accountClient *accountapi.Client
}
```

## 重构的文件结构

### 1. Account API模块

#### **gen/app/api/account/http/http.go**
```go
var (
    service *svc.AccountSvc
)

func Start(c *conf.Config, s *svc.AccountSvc) {
    service = s
    router := bbrouter.Start(c.Base)

    v1 := router.Group("/api/v1/account")
    {
        v1.GET("/info", getAccount)
        v1.POST("/create", createAccount)
        v1.POST("/recharge", recharge)
        v1.GET("/logs", getAccountLogs)
        v1.PUT("/status", updateUserStatus)
        v1.GET("/status", checkUserStatus)
        v1.POST("/deduct", deductCoins)
    }

    go func() {
        if err := router.Run(c.Port.HTTP); err != nil {
            panic(err)
        }
    }()
}
```

#### **gen/app/api/account/http/account.go**
```go
func getAccount(c *gin.Context) {
    param := new(vo.GetAccountReq)
    err := c.BindQuery(param)
    if err != nil {
        ecode.Back(c).Failure(err)
        return
    }

    if param.UserId == 0 {
        ecode.Back(c).Failure(errors.New("invalid user id"))
        return
    }

    data, err := service.GetAccount(c, param)
    if err != nil {
        ecode.Back(c).Failure(err)
        return
    }
    ecode.Back(c).SetData(data).Success()
}
```

#### **gen/app/api/account/svc/svc.go**
```go
type AccountSvc struct {
    conf          *conf.Config
    dao           *dao.Dao
    accountClient *accountapi.Client
}

func Load(c *conf.Config) *AccountSvc {
    svc := &AccountSvc{
        conf:          c,
        dao:           dao.Load(c),
        accountClient: accountapi.NewClient(),
    }
    return svc
}
```

#### **gen/app/api/account/svc/account.go**
```go
func (s *AccountSvc) GetAccount(ctx context.Context, req *vo.GetAccountReq) (*vo.GetAccountResp, error) {
    pbReq := &accountpb.GetAccountReq{
        UserId: req.UserId,
    }

    pbResp, err := s.accountClient.GetAccount(ctx, pbReq)
    if err != nil {
        return nil, err
    }

    resp := &vo.GetAccountResp{
        Account: s.convertAccountFromPB(pbResp.Account),
    }

    return resp, nil
}
```

### 2. Purchase API模块

#### **gen/app/api/purchase/http/http.go**
```go
var (
    service *svc.PurchaseSvc
)

func Start(c *conf.Config, s *svc.PurchaseSvc) {
    service = s
    router := bbrouter.Start(c.Base)

    v1 := router.Group("/api/v1/purchase")
    {
        v1.POST("/chapter", purchaseChapter)
        v1.POST("/monthly", purchaseMonthly)
        v1.POST("/vip", purchaseVip)
        v1.GET("/orders", getPurchaseOrders)
        v1.GET("/vip-monthly-orders", getVipMonthlyOrders)
        v1.GET("/check-chapter", checkChapterPurchased)
        v1.GET("/check-vip", checkVipStatus)
        v1.GET("/check-monthly", checkMonthlyStatus)
        v1.GET("/purchased-chapters", getPurchasedChapters)
    }

    go func() {
        if err := router.Run(c.Port.HTTP); err != nil {
            panic(err)
        }
    }()
}
```

#### **gen/app/api/purchase/http/purchase.go**
```go
func purchaseChapter(c *gin.Context) {
    param := new(vo.PurchaseChapterReq)
    err := c.BindJSON(param)
    if err != nil {
        ecode.Back(c).Failure(err)
        return
    }

    if param.UserId == 0 || len(param.ChapterOrders) == 0 {
        ecode.Back(c).Failure(errors.New("invalid parameter"))
        return
    }

    data, err := service.PurchaseChapter(c, param)
    if err != nil {
        ecode.Back(c).Failure(err)
        return
    }
    ecode.Back(c).SetData(data).Success()
}
```

## 错误处理规范

### 1. 参数验证错误
```go
// ✅ 统一的参数验证
if param.UserId == 0 {
    ecode.Back(c).Failure(errors.New("invalid user id"))
    return
}

if param.Amount <= 0 {
    ecode.Back(c).Failure(errors.New("invalid parameter"))
    return
}

if len(param.ChapterOrders) == 0 {
    ecode.Back(c).Failure(errors.New("invalid parameter"))
    return
}
```

### 2. 绑定错误处理
```go
// ✅ 统一的绑定错误处理
param := new(vo.GetAccountReq)
err := c.BindQuery(param)
if err != nil {
    ecode.Back(c).Failure(err)
    return
}

param := new(vo.CreateAccountReq)
err := c.BindJSON(param)
if err != nil {
    ecode.Back(c).Failure(err)
    return
}
```

### 3. 业务逻辑错误
```go
// ✅ 统一的业务错误处理
data, err := service.GetAccount(c, param)
if err != nil {
    ecode.Back(c).Failure(err)
    return
}
```

### 4. 成功响应
```go
// ✅ 统一的成功响应
ecode.Back(c).SetData(data).Success()
```

## 路由设计

### 1. Account API路由
```go
v1 := router.Group("/api/v1/account")
{
    v1.GET("/info", getAccount)           // 获取账户信息
    v1.POST("/create", createAccount)     // 创建账户
    v1.POST("/recharge", recharge)        // 充值
    v1.GET("/logs", getAccountLogs)       // 获取账户日志
    v1.PUT("/status", updateUserStatus)   // 更新用户状态
    v1.GET("/status", checkUserStatus)    // 检查用户状态
    v1.POST("/deduct", deductCoins)       // 扣除书币
}
```

### 2. Purchase API路由
```go
v1 := router.Group("/api/v1/purchase")
{
    v1.POST("/chapter", purchaseChapter)                 // 购买章节
    v1.POST("/monthly", purchaseMonthly)                 // 购买包月
    v1.POST("/vip", purchaseVip)                         // 购买VIP
    v1.GET("/orders", getPurchaseOrders)                 // 获取购买订单
    v1.GET("/vip-monthly-orders", getVipMonthlyOrders)   // 获取VIP/包月订单
    v1.GET("/check-chapter", checkChapterPurchased)      // 检查章节购买状态
    v1.GET("/check-vip", checkVipStatus)                 // 检查VIP状态
    v1.GET("/check-monthly", checkMonthlyStatus)         // 检查包月状态
    v1.GET("/purchased-chapters", getPurchasedChapters)  // 获取已购买章节
}
```

## 数据转换

### 1. PB到VO转换
```go
func (s *AccountSvc) convertAccountFromPB(pbAccount *accountpb.Account) *vo.Account {
    if pbAccount == nil {
        return nil
    }

    account := &vo.Account{
        AccountId:      pbAccount.AccountId,
        UserId:         pbAccount.UserId,
        CoinBalance:    pbAccount.CoinBalance,
        TotalRecharged: pbAccount.TotalRecharged,
        TotalConsumed:  pbAccount.TotalConsumed,
        Status:         pbAccount.Status,
        UserType:       pbAccount.UserType,
        UserLevel:      pbAccount.UserLevel,
        CreatedAt:      time.Unix(pbAccount.CreatedAt, 0),
        UpdatedAt:      time.Unix(pbAccount.UpdatedAt, 0),
    }

    // 处理可选字段
    if pbAccount.VipExpireTime > 0 {
        vipExpireTime := time.Unix(pbAccount.VipExpireTime, 0)
        account.VipExpireTime = &vipExpireTime
    }

    return account
}
```

### 2. VO到PB转换
```go
func (s *AccountSvc) GetAccount(ctx context.Context, req *vo.GetAccountReq) (*vo.GetAccountResp, error) {
    pbReq := &accountpb.GetAccountReq{
        UserId: req.UserId,
    }

    pbResp, err := s.accountClient.GetAccount(ctx, pbReq)
    if err != nil {
        return nil, err
    }

    resp := &vo.GetAccountResp{
        Account: s.convertAccountFromPB(pbResp.Account),
    }

    return resp, nil
}
```

## 优势总结

### 1. 代码一致性
- ✅ 与chapters模块保持100%一致的代码风格
- ✅ 统一的错误处理方式
- ✅ 相同的路由注册和启动方式

### 2. 错误处理规范
- ✅ 统一使用`ecode.Back(c).Failure(errors.New("message"))`
- ✅ 清晰的错误信息和状态码
- ✅ 一致的成功响应格式

### 3. 可维护性
- ✅ 清晰的文件结构和职责分离
- ✅ 统一的命名规范
- ✅ 完善的参数验证

### 4. 扩展性
- ✅ 易于添加新的API接口
- ✅ 支持灵活的路由配置
- ✅ 便于集成其他微服务

### 5. 性能优化
- ✅ 合理的默认分页参数设置
- ✅ 高效的数据转换
- ✅ 统一的客户端调用方式

现在account和purchase模块完全符合chapters的代码风格，提供了一致、高质量的API实现！
