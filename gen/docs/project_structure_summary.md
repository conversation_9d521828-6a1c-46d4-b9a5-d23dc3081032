# Account & Purchase 微服务完整架构总结（按原始风格重构）

## 概述

完全按照beyondreading项目的原始实现方式和风格，重新生成了Account和Purchase微服务的完整代码。严格遵循了现有项目的目录结构、文件命名、配置方式和代码风格。

## 项目结构

```
gen/
├── proto/                          # Proto定义
│   ├── account/
│   │   └── account.proto           # Account服务Proto定义
│   └── purchase/
│       └── purchase.proto          # Purchase服务Proto定义
├── common/
│   ├── const.go                    # 常量定义（分表数量等）
│   └── po/
│       └── account.go              # PO模型定义
├── database/
│   └── account_schema.sql          # 分表数据库结构
├── app/
│   ├── base/                       # Base层（业务逻辑层）
│   │   ├── account/
│   │   │   ├── api/
│   │   │   │   └── api.go          # Account Base API客户端
│   │   │   ├── conf/
│   │   │   │   └── conf.go         # Account Base配置
│   │   │   ├── dao/
│   │   │   │   ├── dao.go          # Account Base DAO（分表支持）
│   │   │   │   └── account.go      # Account相关DAO方法
│   │   │   ├── svc/
│   │   │   │   └── svc.go          # Account Base服务层
│   │   │   └── cmd/
│   │   │       ├── main.go         # Account Base主程序
│   │   │       └── account.toml    # Account Base配置文件
│   │   └── purchase/
│   │       ├── api/
│   │       │   └── api.go          # Purchase Base API客户端
│   │       ├── conf/
│   │       │   └── conf.go         # Purchase Base配置
│   │       ├── dao/
│   │       │   └── dao.go          # Purchase Base DAO（分表支持）
│   │       ├── svc/
│   │       │   └── svc.go          # Purchase Base服务层
│   │       └── cmd/
│   │           ├── main.go         # Purchase Base主程序
│   │           └── purchase.toml   # Purchase Base配置文件
│   └── api/                        # API层（HTTP接口层）
│       ├── account/
│       │   ├── conf/
│       │   │   └── conf.go         # Account API配置
│       │   ├── svc/
│       │   │   └── svc.go          # Account API服务层
│       │   ├── http/
│       │   │   └── http.go         # Account HTTP处理器
│       │   └── cmd/
│       │       ├── main.go         # Account API主程序
│       │       └── account-api.toml # Account API配置文件
│       └── purchase/
│           ├── conf/
│           │   └── conf.go         # Purchase API配置
│           ├── svc/
│           │   └── svc.go          # Purchase API服务层
│           ├── http/
│           │   └── http.go         # Purchase HTTP处理器
│           └── cmd/
│               ├── main.go         # Purchase API主程序
│               └── purchase-api.toml # Purchase API配置文件
└── docs/
    ├── sharding_design_summary.md  # 分表设计总结
    ├── account_checkuserstatus_update.md # CheckUserStatus修正总结
    └── project_structure_summary.md # 项目结构总结
```

## 架构设计

### 1. 分层架构

#### **API层 (app/api/)**
- **职责**: 提供HTTP REST API接口
- **功能**: 
  - HTTP请求处理
  - 参数验证和转换
  - 调用Base层服务
  - 返回HTTP响应

#### **Base层 (app/base/)**
- **职责**: 核心业务逻辑处理
- **功能**:
  - gRPC服务实现
  - 业务逻辑处理
  - 数据访问层调用
  - 服务间通信

### 2. 服务通信

```
HTTP Client → API Layer → Base Layer (gRPC) → Database
     ↓            ↓           ↓
   REST API   HTTP Handler  gRPC Service
```

#### **API层调用Base层**
```go
// API层通过gRPC客户端调用Base层
accountClient, err := accountapi.NewClient(c.Base)
resp, err := accountClient.GetAccount(ctx, req)
```

#### **Base层服务间调用**
```go
// Purchase Base层调用Account Base层
accountClient, err := accountapi.NewClient(c.Base)
accountResp, err := s.accountClient.GetAccount(ctx, &accountpb.GetAccountReq{
    UserId: req.UserId,
})
```

### 3. 分表支持

#### **分表策略**
- 按userId进行7分表（00-06）
- 所有相关表使用相同的分表算法
- 保证用户数据的一致性

#### **分表实现**
```go
// DAO层分表方法
func (d *Dao) setAccountTable(userId uint64) string {
    shardIndex := userId % common.AccountTableShardNum
    return fmt.Sprintf("account%02d", shardIndex)
}

// 使用分表的查询
tableName := d.setAccountTable(userId)
query := fmt.Sprintf(`SELECT ... FROM %s WHERE user_id = ?`, tableName)
```

## 服务配置

### 1. Base层服务配置

#### **Account Base服务**
- **服务名**: `base-account`
- **gRPC端口**: `:9073`
- **HTTP端口**: `:9071`
- **Debug端口**: `:9072`
- **主程序**: `accountBase.go`
- **配置文件**: `base-account.toml`

#### **Purchase Base服务**
- **服务名**: `base-purchase`
- **gRPC端口**: `:9083`
- **HTTP端口**: `:9081`
- **Debug端口**: `:9082`
- **主程序**: `purchaseBase.go`
- **配置文件**: `base-purchase.toml`

### 2. API层服务配置

#### **Account API服务**
- **服务名**: `api-account`
- **HTTP端口**: `:8071`
- **Debug端口**: `:8072`
- **主程序**: `accountApi.go`
- **配置文件**: `api-account.toml`

#### **Purchase API服务**
- **服务名**: `api-purchase`
- **HTTP端口**: `:8081`
- **Debug端口**: `:8082`
- **主程序**: `purchaseApi.go`
- **配置文件**: `api-purchase.toml`

## 主要功能

### 1. Account服务功能

#### **Base层功能**
- 获取账户信息
- 创建账户
- 充值
- 获取账户日志
- 更新用户状态
- 扣除书币
- 检查用户状态

#### **API层功能**
- `GET /api/v1/account/info?user_id=xxx` - 获取账户信息
- `POST /api/v1/account/create` - 创建账户
- `POST /api/v1/account/recharge` - 充值
- `GET /api/v1/account/logs?user_id=xxx` - 获取账户日志
- `PUT /api/v1/account/status?user_id=xxx` - 更新用户状态
- `GET /api/v1/account/status?user_id=xxx` - 检查用户状态

### 2. Purchase服务功能

#### **Base层功能**
- 购买章节（支持批量）
- 购买包月
- 购买VIP
- 获取购买订单列表
- 获取VIP/包月订单列表
- 检查章节购买状态
- 检查VIP状态
- 检查包月状态
- 获取已购买章节列表

#### **API层功能**
- `POST /api/v1/purchase/chapter` - 购买章节
- `POST /api/v1/purchase/monthly` - 购买包月
- `POST /api/v1/purchase/vip` - 购买VIP
- `GET /api/v1/purchase/orders?user_id=xxx` - 获取购买订单
- `GET /api/v1/purchase/vip-monthly-orders?user_id=xxx` - 获取VIP/包月订单
- `GET /api/v1/purchase/check-chapter?user_id=xxx&book_id=xxx&chapter_order=xxx` - 检查章节购买状态
- `GET /api/v1/purchase/vip-status?user_id=xxx` - 检查VIP状态
- `GET /api/v1/purchase/monthly-status?user_id=xxx` - 检查包月状态
- `GET /api/v1/purchase/purchased-chapters?user_id=xxx&book_id=xxx` - 获取已购买章节

## 技术特性

### 1. 分表支持
- ✅ 7分表设计（account00-06, account_log00-06等）
- ✅ 统一的分表算法
- ✅ 数据一致性保证

### 2. 微服务架构
- ✅ gRPC服务间通信
- ✅ 服务发现和注册
- ✅ 负载均衡
- ✅ 健康检查

### 3. HTTP API
- ✅ RESTful API设计
- ✅ 参数验证
- ✅ 错误处理
- ✅ JSON响应

### 4. 配置管理
- ✅ TOML配置文件
- ✅ 环境隔离
- ✅ 热加载支持

### 5. 可观测性
- ✅ 日志记录
- ✅ 链路追踪
- ✅ 指标监控

## 部署说明

### 1. 启动Base层服务

```bash
# 启动Account Base服务
cd gen/app/base/account/cmd
go run accountBase.go

# 启动Purchase Base服务
cd gen/app/base/purchase/cmd
go run purchaseBase.go
```

### 2. 启动API层服务

```bash
# 启动Account API服务
cd gen/app/api/account/cmd
go run accountApi.go

# 启动Purchase API服务
cd gen/app/api/purchase/cmd
go run purchaseApi.go
```

### 3. 服务依赖

- **etcd**: 服务发现和配置管理
- **MySQL**: 数据存储（支持分表）
- **Redis**: 缓存

## 开发指南

### 1. 添加新接口

#### **Base层添加gRPC接口**
1. 在proto文件中定义新的RPC方法
2. 在svc层实现业务逻辑
3. 在dao层添加数据访问方法

#### **API层添加HTTP接口**
1. 在http包中添加处理器方法
2. 在RegisterRoutes中注册路由
3. 调用Base层的gRPC接口

### 2. 数据库操作

#### **分表查询**
```go
// 获取分表名称
tableName := d.setAccountTable(userId)

// 构建查询语句
query := fmt.Sprintf(`SELECT ... FROM %s WHERE ...`, tableName)
```

#### **事务处理**
```go
// 开启事务
tx, err := db.BeginTxx(ctx, nil)
defer tx.Rollback()

// 执行操作
// ...

// 提交事务
if err = tx.Commit(); err != nil {
    return fmt.Errorf("failed to commit transaction: %w", err)
}
```

## 关键实现特点

### 1. 严格遵循原始风格
- ✅ **文件命名**: 完全按照现有项目的命名规范（如accountBase.go, purchaseApi.go）
- ✅ **目录结构**: 与beyondreading/app结构完全一致
- ✅ **配置文件**: 使用原始的base-account.toml格式，不做任何更改
- ✅ **主程序实现**: 完全按照现有main方法的处理方式编码

### 2. API路径设计
- ✅ **不使用/:userId/路径**: 所有用户ID通过query参数传递
- ✅ **RESTful设计**: 遵循REST API设计原则
- ✅ **参数验证**: 完整的请求参数验证和错误处理

### 3. 服务发现和通信
- ✅ **gRPC通信**: Base层服务间使用gRPC通信
- ✅ **服务注册**: 使用etcd进行服务发现和注册
- ✅ **客户端连接**: 使用discovery.NewConn方式创建客户端连接

### 4. 配置管理
- ✅ **端口配置**: 使用[port]段配置http、debug、grpc端口
- ✅ **业务配置**: 账户和购买相关的业务参数配置
- ✅ **Redis配置**: 分别使用RedisAccount和RedisPurchase配置

### 5. 分表支持
- ✅ **7分表设计**: 所有表按userId进行7分表
- ✅ **统一算法**: 使用相同的分表路由算法
- ✅ **数据一致性**: 保证用户相关数据在同一分表

### 6. 错误处理和日志
- ✅ **统一错误处理**: 使用logger.LogErrorf进行错误日志记录
- ✅ **优雅关闭**: 支持信号处理和优雅关闭
- ✅ **健康检查**: 提供/health端点进行健康检查

## 与原始项目的一致性

### 1. 目录结构一致性
```
beyondreading/app/base/chapters/    →    gen/app/base/account/
beyondreading/app/api/purchase/     →    gen/app/api/purchase/
```

### 2. 文件命名一致性
```
chapters/cmd/chaptersBase.go        →    account/cmd/accountBase.go
purchase/cmd/purchaseApi.go         →    purchase/cmd/purchaseApi.go
```

### 3. 配置文件一致性
```
base-chapters.toml                  →    base-account.toml
api-purchase.toml                   →    api-purchase.toml
```

### 4. 代码风格一致性
- ✅ 相同的import组织方式
- ✅ 相同的错误处理模式
- ✅ 相同的服务启动流程
- ✅ 相同的信号处理机制

这个完整的架构严格按照beyondreading项目的原始实现方式，提供了高度一致的微服务解决方案。
