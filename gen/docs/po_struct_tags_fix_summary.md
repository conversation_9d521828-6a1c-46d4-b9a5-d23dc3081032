# PO结构体标签命名修正总结

## 概述

将gen/common/po/account.go中所有结构体的db和json标签从下划线命名方式改为驼峰命名方式，确保与protobuf字段命名保持一致。

## 修正的结构体

### 1. **Account结构体**

#### **修正前（使用下划线）**
```go
type Account struct {
    AccountId         uint64     `db:"account_id" json:"account_id"`
    UserId            uint64     `db:"user_id" json:"user_id"`
    CoinBalance       float64    `db:"coin_balance" json:"coin_balance"`
    TotalRecharged    float64    `db:"total_recharged" json:"total_recharged"`
    TotalConsumed     float64    `db:"total_consumed" json:"total_consumed"`
    UserType          int32      `db:"user_type" json:"user_type"`
    UserLevel         int32      `db:"user_level" json:"user_level"`
    VipExpireTime     *time.Time `db:"vip_expire_time" json:"vip_expire_time"`
    MonthlyExpireTime *time.Time `db:"monthly_expire_time" json:"monthly_expire_time"`
    CreatedAt         time.Time  `db:"created_at" json:"created_at"`
    UpdatedAt         time.Time  `db:"updated_at" json:"updated_at"`
}
```

#### **修正后（使用驼峰命名）**
```go
type Account struct {
    AccountId         uint64     `db:"accountId" json:"accountId"`
    UserId            uint64     `db:"userId" json:"userId"`
    CoinBalance       float64    `db:"coinBalance" json:"coinBalance"`
    TotalRecharged    float64    `db:"totalRecharged" json:"totalRecharged"`
    TotalConsumed     float64    `db:"totalConsumed" json:"totalConsumed"`
    UserType          int32      `db:"userType" json:"userType"`
    UserLevel         int32      `db:"userLevel" json:"userLevel"`
    VipExpireTime     *time.Time `db:"vipExpireTime" json:"vipExpireTime"`
    MonthlyExpireTime *time.Time `db:"monthlyExpireTime" json:"monthlyExpireTime"`
    CreatedAt         time.Time  `db:"createdAt" json:"createdAt"`
    UpdatedAt         time.Time  `db:"updatedAt" json:"updatedAt"`
}
```

### 2. **AccountLog结构体**

#### **修正前（使用下划线）**
```go
type AccountLog struct {
    LogId           uint64    `db:"log_id" json:"log_id"`
    AccountId       uint64    `db:"account_id" json:"account_id"`
    UserId          uint64    `db:"user_id" json:"user_id"`
    TransactionType string    `db:"transaction_type" json:"transaction_type"`
    BalanceBefore   float64   `db:"balance_before" json:"balance_before"`
    BalanceAfter    float64   `db:"balance_after" json:"balance_after"`
    OrderId         string    `db:"order_id" json:"order_id"`
    BookId          string    `db:"book_id" json:"book_id"`
    ChapterId       string    `db:"chapter_id" json:"chapter_id"`
    ExtraData       string    `db:"extra_data" json:"extra_data"`
    CreatedAt       time.Time `db:"created_at" json:"created_at"`
}
```

#### **修正后（使用驼峰命名）**
```go
type AccountLog struct {
    LogId           uint64    `db:"logId" json:"logId"`
    AccountId       uint64    `db:"accountId" json:"accountId"`
    UserId          uint64    `db:"userId" json:"userId"`
    TransactionType string    `db:"transactionType" json:"transactionType"`
    BalanceBefore   float64   `db:"balanceBefore" json:"balanceBefore"`
    BalanceAfter    float64   `db:"balanceAfter" json:"balanceAfter"`
    OrderId         string    `db:"orderId" json:"orderId"`
    BookId          string    `db:"bookId" json:"bookId"`
    ChapterId       string    `db:"chapterId" json:"chapterId"`
    ExtraData       string    `db:"extraData" json:"extraData"`
    CreatedAt       time.Time `db:"createdAt" json:"createdAt"`
}
```

### 3. **RechargeOrder结构体**

#### **修正前（使用下划线）**
```go
type RechargeOrder struct {
    OrderId        string     `db:"order_id" json:"order_id"`
    AccountId      uint64     `db:"account_id" json:"account_id"`
    UserId         uint64     `db:"user_id" json:"user_id"`
    CoinAmount     float64    `db:"coin_amount" json:"coin_amount"`
    ExchangeRate   float32    `db:"exchange_rate" json:"exchange_rate"`
    PaymentMethod  string     `db:"payment_method" json:"payment_method"`
    PaymentOrderId string     `db:"payment_order_id" json:"payment_order_id"`
    PaidAt         *time.Time `db:"paid_at" json:"paid_at"`
    CreatedAt      time.Time  `db:"created_at" json:"created_at"`
    UpdatedAt      time.Time  `db:"updated_at" json:"updated_at"`
}
```

#### **修正后（使用驼峰命名）**
```go
type RechargeOrder struct {
    OrderId        string     `db:"orderId" json:"orderId"`
    AccountId      uint64     `db:"accountId" json:"accountId"`
    UserId         uint64     `db:"userId" json:"userId"`
    CoinAmount     float64    `db:"coinAmount" json:"coinAmount"`
    ExchangeRate   float32    `db:"exchangeRate" json:"exchangeRate"`
    PaymentMethod  string     `db:"paymentMethod" json:"paymentMethod"`
    PaymentOrderId string     `db:"paymentOrderId" json:"paymentOrderId"`
    PaidAt         *time.Time `db:"paidAt" json:"paidAt"`
    CreatedAt      time.Time  `db:"createdAt" json:"createdAt"`
    UpdatedAt      time.Time  `db:"updatedAt" json:"updatedAt"`
}
```

### 4. **PurchaseOrder结构体**

#### **修正前（使用下划线）**
```go
type PurchaseOrder struct {
    OrderId      string    `db:"order_id" json:"order_id"`
    AccountId    uint64    `db:"account_id" json:"account_id"`
    UserId       uint64    `db:"user_id" json:"user_id"`
    OrderType    string    `db:"order_type" json:"order_type"`
    BookId       string    `db:"book_id" json:"book_id"`
    BookName     string    `db:"book_name" json:"book_name"`
    ChapterId    string    `db:"chapter_id" json:"chapter_id"`
    ChapterTitle string    `db:"chapter_title" json:"chapter_title"`
    ChapterOrder uint32    `db:"chapter_order" json:"chapter_order"`
    CoinAmount   float64   `db:"coin_amount" json:"coin_amount"`
    CreatedAt    time.Time `db:"created_at" json:"created_at"`
    UpdatedAt    time.Time `db:"updated_at" json:"updated_at"`
}
```

#### **修正后（使用驼峰命名）**
```go
type PurchaseOrder struct {
    OrderId      string    `db:"orderId" json:"orderId"`
    AccountId    uint64    `db:"accountId" json:"accountId"`
    UserId       uint64    `db:"userId" json:"userId"`
    OrderType    string    `db:"orderType" json:"orderType"`
    BookId       string    `db:"bookId" json:"bookId"`
    BookName     string    `db:"bookName" json:"bookName"`
    ChapterId    string    `db:"chapterId" json:"chapterId"`
    ChapterTitle string    `db:"chapterTitle" json:"chapterTitle"`
    ChapterOrder uint32    `db:"chapterOrder" json:"chapterOrder"`
    CoinAmount   float64   `db:"coinAmount" json:"coinAmount"`
    CreatedAt    time.Time `db:"createdAt" json:"createdAt"`
    UpdatedAt    time.Time `db:"updatedAt" json:"updatedAt"`
}
```

### 5. **VipMonthlyOrder结构体**

#### **修正前（使用下划线）**
```go
type VipMonthlyOrder struct {
    OrderId      string     `db:"order_id" json:"order_id"`
    AccountId    uint64     `db:"account_id" json:"account_id"`
    UserId       uint64     `db:"user_id" json:"user_id"`
    OrderType    string     `db:"order_type" json:"order_type"`
    CoinAmount   float64    `db:"coin_amount" json:"coin_amount"`
    DurationDays int32      `db:"duration_days" json:"duration_days"`
    StartTime    *time.Time `db:"start_time" json:"start_time"`
    EndTime      *time.Time `db:"end_time" json:"end_time"`
    CreatedAt    time.Time  `db:"created_at" json:"created_at"`
    UpdatedAt    time.Time  `db:"updated_at" json:"updated_at"`
}
```

#### **修正后（使用驼峰命名）**
```go
type VipMonthlyOrder struct {
    OrderId      string     `db:"orderId" json:"orderId"`
    AccountId    uint64     `db:"accountId" json:"accountId"`
    UserId       uint64     `db:"userId" json:"userId"`
    OrderType    string     `db:"orderType" json:"orderType"`
    CoinAmount   float64    `db:"coinAmount" json:"coinAmount"`
    DurationDays int32      `db:"durationDays" json:"durationDays"`
    StartTime    *time.Time `db:"startTime" json:"startTime"`
    EndTime      *time.Time `db:"endTime" json:"endTime"`
    CreatedAt    time.Time  `db:"createdAt" json:"createdAt"`
    UpdatedAt    time.Time  `db:"updatedAt" json:"updatedAt"`
}
```

### 6. **ChapterPurchaseInfo结构体**

#### **修正前（使用下划线）**
```go
type ChapterPurchaseInfo struct {
    OrderId      string    `json:"order_id"`
    ChapterId    string    `json:"chapter_id"`
    ChapterTitle string    `json:"chapter_title"`
    ChapterOrder uint32    `json:"chapter_order"`
    CoinAmount   float64   `json:"coin_amount"`
    PurchasedAt  time.Time `json:"purchased_at"`
    IsMonthly    bool      `json:"is_monthly"`
    IsVip        bool      `json:"is_vip"`
}
```

#### **修正后（使用驼峰命名）**
```go
type ChapterPurchaseInfo struct {
    OrderId      string    `json:"orderId"`
    ChapterId    string    `json:"chapterId"`
    ChapterTitle string    `json:"chapterTitle"`
    ChapterOrder uint32    `json:"chapterOrder"`
    CoinAmount   float64   `json:"coinAmount"`
    PurchasedAt  time.Time `json:"purchasedAt"`
    IsMonthly    bool      `json:"isMonthly"`
    IsVip        bool      `json:"isVip"`
}
```

### 7. **ChapterPurchaseItem结构体**

#### **修正前（使用下划线）**
```go
type ChapterPurchaseItem struct {
    ChapterOrder uint32  `json:"chapter_order"`
    CoinAmount   float64 `json:"coin_amount"`
}
```

#### **修正后（使用驼峰命名）**
```go
type ChapterPurchaseItem struct {
    ChapterOrder uint32  `json:"chapterOrder"`
    CoinAmount   float64 `json:"coinAmount"`
}
```

## 修正的字段映射

### 1. **ID类字段**
- ✅ `account_id` → `accountId`
- ✅ `user_id` → `userId`
- ✅ `log_id` → `logId`
- ✅ `order_id` → `orderId`
- ✅ `book_id` → `bookId`
- ✅ `chapter_id` → `chapterId`
- ✅ `payment_order_id` → `paymentOrderId`

### 2. **余额相关字段**
- ✅ `coin_balance` → `coinBalance`
- ✅ `total_recharged` → `totalRecharged`
- ✅ `total_consumed` → `totalConsumed`
- ✅ `balance_before` → `balanceBefore`
- ✅ `balance_after` → `balanceAfter`
- ✅ `coin_amount` → `coinAmount`

### 3. **用户相关字段**
- ✅ `user_type` → `userType`
- ✅ `user_level` → `userLevel`
- ✅ `vip_expire_time` → `vipExpireTime`
- ✅ `monthly_expire_time` → `monthlyExpireTime`

### 4. **订单相关字段**
- ✅ `order_type` → `orderType`
- ✅ `transaction_type` → `transactionType`
- ✅ `payment_method` → `paymentMethod`
- ✅ `exchange_rate` → `exchangeRate`

### 5. **章节相关字段**
- ✅ `chapter_title` → `chapterTitle`
- ✅ `chapter_order` → `chapterOrder`
- ✅ `book_name` → `bookName`

### 6. **时间相关字段**
- ✅ `created_at` → `createdAt`
- ✅ `updated_at` → `updatedAt`
- ✅ `paid_at` → `paidAt`
- ✅ `purchased_at` → `purchasedAt`
- ✅ `start_time` → `startTime`
- ✅ `end_time` → `endTime`

### 7. **VIP/包月相关字段**
- ✅ `duration_days` → `durationDays`
- ✅ `is_monthly` → `isMonthly`
- ✅ `is_vip` → `isVip`

### 8. **其他字段**
- ✅ `extra_data` → `extraData`

## 影响和优势

### 1. **命名一致性**
- ✅ 与protobuf字段命名保持完全一致
- ✅ 与前端JavaScript/TypeScript命名习惯一致
- ✅ 统一的驼峰命名规范

### 2. **JSON序列化**
- ✅ JSON输出使用驼峰命名
- ✅ 符合RESTful API设计规范
- ✅ 便于前端开发者理解和使用

### 3. **数据库映射**
- ✅ db标签使用驼峰命名（需要确保数据库字段也使用驼峰命名）
- ✅ 或者需要配置ORM使用字段名映射
- ✅ 保持代码和数据库的一致性

### 4. **维护性提升**
- ✅ 减少命名混乱和错误
- ✅ 提高代码可读性
- ✅ 便于团队协作开发

### 5. **兼容性考虑**
- ✅ 需要确保数据库字段名与db标签一致
- ✅ 可能需要数据库迁移脚本
- ✅ 需要更新相关的SQL查询语句

## 注意事项

### 1. **数据库字段名**
如果数据库表中的字段仍然使用下划线命名，需要：
- 更新数据库表结构使用驼峰命名
- 或者配置ORM进行字段名映射
- 或者保持db标签使用下划线，只修改json标签

### 2. **SQL查询更新**
需要检查和更新所有相关的SQL查询语句，确保字段名一致。

### 3. **测试验证**
需要进行充分的测试，确保：
- JSON序列化/反序列化正常
- 数据库操作正常
- API接口返回正确的字段名

现在所有PO结构体的标签都使用了统一的驼峰命名方式，与protobuf字段命名保持完全一致！
