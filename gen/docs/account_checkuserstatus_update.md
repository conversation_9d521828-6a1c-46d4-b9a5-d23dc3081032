# Account CheckUserStatus 接口修正总结

## 概述

根据用户反馈，修正了Account服务中的CheckUserStatus接口，移除了bookId参数和书籍可读性判断逻辑。书籍可读性判断将放在book模块的接口中处理。

## 主要修改

### 1. Proto接口修改

#### **修改前 (错误)**
```protobuf
// 检查用户状态
message CheckUserStatusReq {
  uint64 user_id = 1;
  string book_id = 2;             // ❌ 不需要bookId参数
}

message CheckUserStatusResp {
  int32 code = 1;
  string message = 2;
  AccountInfo account = 3;
  bool has_vip = 4;               // 是否有VIP权限
  bool has_monthly = 5;           // 是否有包月权限
  bool can_read_book = 6;         // ❌ 不应该在account服务中判断书籍可读性
}
```

#### **修改后 (正确)**
```protobuf
// 检查用户状态
message CheckUserStatusReq {
  uint64 user_id = 1;             // ✅ 只需要用户ID
}

message CheckUserStatusResp {
  int32 code = 1;
  string message = 2;
  AccountInfo account = 3;
  bool has_vip = 4;               // ✅ 是否有VIP权限
  bool has_monthly = 5;           // ✅ 是否有包月权限
  // ✅ 移除can_read_book字段，书籍可读性判断放在book模块
}
```

### 2. 服务职责分离

#### **Account服务职责**
- ✅ 管理用户账户信息
- ✅ 处理充值、扣款等账户操作
- ✅ 检查用户VIP/包月状态
- ✅ 维护用户等级和权限过期时间

#### **Book服务职责**
- ✅ 判断书籍是否可读
- ✅ 根据用户VIP/包月状态和书籍属性判断权限
- ✅ 处理书籍相关的业务逻辑

### 3. 代码修改详情

#### **Account DAO层**
```go
// 修改前：包含书籍可读性判断
func (d *Dao) CheckUserStatus(ctx context.Context, userId uint64, bookId string) (*po.Account, bool, bool, bool, error) {
    // 复杂的书籍权限判断逻辑
}

// 修改后：只检查用户状态
func (d *Dao) CheckUserStatus(ctx context.Context, userId uint64) (*po.Account, bool, bool, error) {
    account, err := d.GetAccountByUserId(ctx, userId)
    if err != nil {
        return nil, false, false, err
    }

    now := time.Now()
    hasVip := account.VipExpireTime != nil && account.VipExpireTime.After(now)
    hasMonthly := account.MonthlyExpireTime != nil && account.MonthlyExpireTime.After(now)

    return account, hasVip, hasMonthly, nil
}
```

#### **Account服务层**
```go
// 修改后：简化的用户状态检查
func (s *AccountSvc) CheckUserStatus(ctx context.Context, req *pb.CheckUserStatusReq) (*pb.CheckUserStatusResp, error) {
    account, hasVip, hasMonthly, err := s.dao.CheckUserStatus(ctx, req.UserId)
    if err != nil {
        logger.LogErrorf("Failed to check user status: %v", err)
        return &pb.CheckUserStatusResp{
            Code:    500,
            Message: "Failed to check user status",
        }, nil
    }

    return &pb.CheckUserStatusResp{
        Code:       200,
        Message:    "Success",
        Account:    s.convertAccountToPB(account),
        HasVip:     hasVip,
        HasMonthly: hasMonthly,
        // ✅ 不再返回can_read_book字段
    }, nil
}
```

### 4. 新的架构设计

#### **服务调用流程**
```
用户请求阅读书籍
    ↓
Book服务接收请求
    ↓
Book服务调用Account.CheckUserStatus获取用户VIP/包月状态
    ↓
Book服务根据书籍属性和用户状态判断是否可读
    ↓
返回最终的可读性结果
```

#### **Book服务示例逻辑**
```go
// Book服务中的权限判断逻辑
func (s *BookSvc) CheckBookReadPermission(ctx context.Context, userId uint64, bookId string) (bool, error) {
    // 1. 调用Account服务获取用户状态
    accountResp, err := s.accountClient.CheckUserStatus(ctx, &accountpb.CheckUserStatusReq{
        UserId: userId,
    })
    if err != nil {
        return false, err
    }

    // 2. 获取书籍信息
    book, err := s.dao.GetBookById(ctx, bookId)
    if err != nil {
        return false, err
    }

    // 3. 根据书籍类型和用户状态判断权限
    if book.IsVipOnly && !accountResp.HasVip {
        return false, nil // VIP专享书籍，用户无VIP权限
    }

    if book.IsMonthlyOnly && !accountResp.HasMonthly {
        return false, nil // 包月专享书籍，用户无包月权限
    }

    // 4. 检查是否直接购买了章节
    // ... 其他业务逻辑

    return true, nil
}
```

### 5. 接口使用示例

#### **Account服务调用**
```go
// 只检查用户账户状态
resp, err := accountClient.CheckUserStatus(ctx, &accountpb.CheckUserStatusReq{
    UserId: 123,
})

if resp.HasVip {
    // 用户有VIP权限
}

if resp.HasMonthly {
    // 用户有包月权限
}
```

#### **Book服务调用**
```go
// 在Book服务中判断书籍可读性
canRead, err := bookClient.CheckBookReadPermission(ctx, &bookpb.CheckBookReadPermissionReq{
    UserId: 123,
    BookId: "book_456",
})
```

## 修改的优势

### 1. 职责分离
- Account服务专注于账户管理
- Book服务专注于书籍权限判断
- 各服务职责清晰，便于维护

### 2. 可扩展性
- 书籍权限规则变更只需修改Book服务
- Account服务保持稳定，不受书籍业务逻辑影响

### 3. 复用性
- Account.CheckUserStatus可以被多个服务调用
- 用户状态信息可以用于其他业务场景

### 4. 测试友好
- 各服务可以独立测试
- Mock依赖更加简单

## 文件修改清单

### **修改的文件**
- ✅ `gen/proto/account/account.proto` - 移除bookId参数和can_read_book字段
- ✅ `gen/app/base/account/dao/dao.go` - 简化CheckUserStatus方法
- ✅ `gen/app/base/account/svc/svc.go` - 更新服务层实现

### **保持不变的文件**
- ✅ `gen/proto/purchase/purchase.proto` - Purchase服务不受影响
- ✅ `gen/app/base/purchase/dao/dao.go` - Purchase DAO不受影响
- ✅ `gen/app/base/purchase/svc/svc.go` - Purchase服务不受影响

## 总结

通过这次修改：

1. ✅ **简化了Account服务**: 移除了不属于账户管理的书籍权限判断逻辑
2. ✅ **明确了服务边界**: Account负责账户状态，Book负责书籍权限
3. ✅ **提高了可维护性**: 各服务职责清晰，便于独立开发和测试
4. ✅ **保持了向后兼容**: 其他服务的接口和实现保持不变

现在Account服务专注于账户管理，书籍可读性判断将在Book服务中实现，架构更加清晰和合理！
