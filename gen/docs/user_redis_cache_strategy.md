# User模块Redis缓存策略实现

## 概述

按照beyondreading/app/base/books模块的缓存模式，为user模块实现了完整的Redis缓存策略，包括用户信息缓存、索引映射缓存、缓存更新同步等功能。

## 缓存架构设计

### 1. **缓存层级结构**
```
Redis缓存层
├── 用户信息缓存 (user:info:{userId})
├── 手机号映射缓存 (user:phone:{phone})
├── 邮箱映射缓存 (user:email:{email})
├── Google ID映射缓存 (user:google:{googleId})
└── Apple ID映射缓存 (user:apple:{appleId})
```

### 2. **缓存Key设计**
```go
// Redis缓存Key常量
const (
    RedisUserInfoId      = "user:info:%d"        // 用户信息缓存
    RedisPhoneToUserId   = "user:phone:%s"       // 手机号到用户ID映射
    RedisEmailToUserId   = "user:email:%s"       // 邮箱到用户ID映射
    RedisGoogleToUserId  = "user:google:%s"      // Google ID到用户ID映射
    RedisAppleToUserId   = "user:apple:%s"       // Apple ID到用户ID映射
    RedisUserToken       = "user:token:%s"       // 用户Token缓存
    RedisSmsCode         = "sms:code:%s:%d"      // 短信验证码缓存
    RedisSmsLimit        = "sms:limit:%s"        // 短信发送限制
    RedisLoginLimit      = "login:limit:%s"      // 登录限制
)
```

## 缓存策略实现

### 1. **查询缓存策略（Cache-Aside Pattern）**

#### **按用户ID查询**
```go
func (d *Dao) GetUserById(ctx context.Context, userId uint64) (*po.User, error) {
    // 1. 先从缓存获取
    user, err := d.getUserFromCache(ctx, userId)
    if err == nil && user != nil {
        logger.LogInfof("User %d found in cache", userId)
        return user, nil
    }

    // 2. 缓存未命中，从数据库查询
    user = &po.User{}
    err = db.GetContext(ctx, user, query, userId)
    if err != nil {
        if err == sql.ErrNoRows {
            // 3. 用户不存在，设置空缓存防止缓存穿透
            d.setEmptyUserCache(ctx, userId)
            return nil, nil
        }
        return nil, err
    }

    // 4. 设置缓存
    d.setUserToCache(ctx, user)
    logger.LogInfof("User %d cached successfully", userId)
    return user, nil
}
```

#### **按手机号查询（二级索引缓存）**
```go
func (d *Dao) GetUserByPhone(ctx context.Context, phone string) (*po.User, error) {
    // 1. 先从缓存通过手机号获取用户ID
    userId, err := d.getUserIdByPhoneFromCache(ctx, phone)
    if err == nil && userId > 0 {
        // 2. 通过用户ID获取完整用户信息（会走缓存）
        return d.GetUserById(ctx, userId)
    }

    // 3. 缓存未命中，查询所有分表
    for i := 0; i < 7; i++ {
        user := &po.User{}
        err := db.GetContext(ctx, user, query, phone)
        if err == nil {
            // 4. 设置用户信息缓存
            d.setUserToCache(ctx, user)
            // 5. 设置手机号到用户ID的映射缓存
            d.setPhoneToUserIdCache(ctx, phone, user.UserId)
            return user, nil
        }
    }
    return nil, nil
}
```

### 2. **缓存更新策略（Write-Through Pattern）**

#### **用户信息更新**
```go
func (d *Dao) UpdateUser(ctx context.Context, user *po.User) (*po.User, error) {
    // 1. 获取原始用户信息（用于缓存清理）
    oldUser, err := d.GetUserById(ctx, user.UserId)
    
    // 2. 更新数据库
    _, err = db.ExecContext(ctx, query, user.Nickname, user.Avatar, ...)
    
    // 3. 获取更新后的完整用户信息
    updatedUser, err := d.getUserFromDB(ctx, user.UserId)
    
    // 4. 更新用户信息缓存
    err = d.setUserToCache(ctx, updatedUser)
    
    // 5. 处理索引映射缓存变更
    if oldUser.Phone != updatedUser.Phone {
        // 删除旧手机号映射，添加新手机号映射
        if oldUser.Phone != "" {
            d.deletePhoneToUserIdCache(ctx, oldUser.Phone)
        }
        if updatedUser.Phone != "" {
            d.setPhoneToUserIdCache(ctx, updatedUser.Phone, updatedUser.UserId)
        }
    }
    
    return updatedUser, nil
}
```

#### **登录信息更新**
```go
func (d *Dao) UpdateUserLoginInfo(ctx context.Context, userId uint64, loginIp string) error {
    // 1. 更新数据库
    _, err := db.ExecContext(ctx, query, now, loginIp, now, userId)
    
    // 2. 获取更新后的用户信息并更新缓存
    updatedUser, err := d.getUserFromDB(ctx, userId)
    if err != nil {
        // 如果获取失败，删除缓存以确保数据一致性
        d.deleteUserCache(ctx, userId)
    } else {
        // 更新缓存
        d.setUserToCache(ctx, updatedUser)
    }
    
    return nil
}
```

### 3. **缓存创建策略**

#### **用户创建时的缓存设置**
```go
func (d *Dao) CreateUser(ctx context.Context, user *po.User) (*po.User, error) {
    // 1. 创建用户到数据库
    result, err := db.ExecContext(ctx, query, ...)
    user.UserId = uint64(userId)

    // 2. 设置用户信息缓存
    d.setUserToCache(ctx, user)

    // 3. 设置所有相关的索引映射缓存
    if user.Phone != "" {
        d.setPhoneToUserIdCache(ctx, user.Phone, user.UserId)
    }
    if user.Email != "" {
        d.setEmailToUserIdCache(ctx, user.Email, user.UserId)
    }
    if user.GoogleId != "" {
        d.setGoogleToUserIdCache(ctx, user.GoogleId, user.UserId)
    }
    if user.AppleId != "" {
        d.setAppleToUserIdCache(ctx, user.AppleId, user.UserId)
    }

    return user, nil
}
```

## 缓存方法实现

### 1. **基础缓存操作**

#### **用户信息缓存**
```go
// 获取用户信息缓存
func (d *Dao) getUserFromCache(ctx context.Context, userId uint64) (*po.User, error) {
    key := fmt.Sprintf(model.RedisUserInfoId, userId)
    userJstr, err := d.cache.RGetString(ctx, key)
    if err != nil {
        return nil, err
    }
    
    user := &po.User{}
    err = js.JSON.UnmarshalFromString(userJstr, user)
    return user, err
}

// 设置用户信息缓存
func (d *Dao) setUserToCache(ctx context.Context, user *po.User) error {
    key := fmt.Sprintf(model.RedisUserInfoId, user.UserId)
    expireTime := time.Hour * 24 // 24小时过期
    
    _, err := d.cache.RSet(ctx, key, user, expireTime)
    return err
}

// 删除用户信息缓存
func (d *Dao) deleteUserCache(ctx context.Context, userId uint64) error {
    key := fmt.Sprintf(model.RedisUserInfoId, userId)
    _, err := d.cache.RDel(ctx, key)
    return err
}
```

#### **空缓存设置（防止缓存穿透）**
```go
func (d *Dao) setEmptyUserCache(ctx context.Context, userId uint64) error {
    key := fmt.Sprintf(model.RedisUserInfoId, userId)
    expireTime := time.Minute * 5 // 5分钟过期
    
    _, err := d.cache.RSet(ctx, key, "null", expireTime)
    return err
}
```

### 2. **索引映射缓存**

#### **手机号映射缓存**
```go
// 获取手机号对应的用户ID
func (d *Dao) getUserIdByPhoneFromCache(ctx context.Context, phone string) (uint64, error) {
    key := fmt.Sprintf(model.RedisPhoneToUserId, phone)
    userIdStr, err := d.cache.RGetString(ctx, key)
    if err != nil {
        return 0, err
    }
    return strconv.ParseUint(userIdStr, 10, 64)
}

// 设置手机号到用户ID的映射缓存
func (d *Dao) setPhoneToUserIdCache(ctx context.Context, phone string, userId uint64) error {
    key := fmt.Sprintf(model.RedisPhoneToUserId, phone)
    expireTime := time.Hour * 24 // 24小时过期
    
    _, err := d.cache.RSet(ctx, key, userId, expireTime)
    return err
}

// 删除手机号到用户ID的映射缓存
func (d *Dao) deletePhoneToUserIdCache(ctx context.Context, phone string) error {
    key := fmt.Sprintf(model.RedisPhoneToUserId, phone)
    _, err := d.cache.RDel(ctx, key)
    return err
}
```

#### **邮箱映射缓存**
```go
// 类似手机号映射缓存的实现
func (d *Dao) getUserIdByEmailFromCache(ctx context.Context, email string) (uint64, error)
func (d *Dao) setEmailToUserIdCache(ctx context.Context, email string, userId uint64) error
func (d *Dao) deleteEmailToUserIdCache(ctx context.Context, email string) error
```

#### **第三方ID映射缓存**
```go
// Google ID映射缓存
func (d *Dao) getUserIdByGoogleIdFromCache(ctx context.Context, googleId string) (uint64, error)
func (d *Dao) setGoogleToUserIdCache(ctx context.Context, googleId string, userId uint64) error
func (d *Dao) deleteGoogleToUserIdCache(ctx context.Context, googleId string) error

// Apple ID映射缓存
func (d *Dao) getUserIdByAppleIdFromCache(ctx context.Context, appleId string) (uint64, error)
func (d *Dao) setAppleToUserIdCache(ctx context.Context, appleId string, userId uint64) error
func (d *Dao) deleteAppleToUserIdCache(ctx context.Context, appleId string) error
```

## 缓存过期策略

### 1. **过期时间设置**
```go
// 用户信息缓存：24小时过期
expireTime := time.Hour * 24

// 索引映射缓存：24小时过期
expireTime := time.Hour * 24

// 空缓存：5分钟过期（防止缓存穿透）
expireTime := time.Minute * 5

// 短信验证码：5分钟过期
expireTime := time.Minute * 5
```

### 2. **缓存一致性保证**
- ✅ 数据更新时同步更新缓存
- ✅ 更新失败时删除缓存确保一致性
- ✅ 索引变更时维护映射关系
- ✅ 空缓存防止缓存穿透

## 性能优化

### 1. **查询优化**
- ✅ 优先从缓存获取数据
- ✅ 二级索引缓存减少数据库查询
- ✅ 批量操作减少网络开销

### 2. **缓存命中率优化**
- ✅ 合理的过期时间设置
- ✅ 预热常用数据
- ✅ 异步更新缓存

### 3. **内存使用优化**
- ✅ JSON序列化减少内存占用
- ✅ 合理的过期时间避免内存泄漏
- ✅ 空缓存短期过期

## 监控和日志

### 1. **缓存操作日志**
```go
logger.LogInfof("User %d found in cache", userId)
logger.LogInfof("User %d cached successfully", userId)
logger.LogInfof("User %d cache updated successfully", userId)
logger.LogErrorf("Failed to set user to cache: %v", err)
```

### 2. **缓存性能监控**
- ✅ 缓存命中率统计
- ✅ 缓存操作耗时监控
- ✅ 缓存异常告警

现在User模块已经实现了完整的Redis缓存策略，确保了数据的高性能访问和一致性！
