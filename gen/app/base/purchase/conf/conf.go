package conf

import (
	"creativematrix.com/beyondreading/pkg/redis"
	"fmt"

	"creativematrix.com/beyondreading/pkg/config"
)

type Config struct {
	config.Base

	RedisPurchase *redis.Config

	Log struct {
		Level string
	}

	Purchase struct {
		EnableBatchPurchase bool    `toml:"enable_batch_purchase"` // 是否启用批量购买
		MaxBatchSize        int     `toml:"max_batch_size"`        // 最大批量购买数量
		CacheExpire         int     `toml:"cache_expire"`          // 缓存过期时间（秒）
		EnableCache         bool    `toml:"enable_cache"`          // 是否启用缓存
		VipPrice            float64 `toml:"vip_price"`             // VIP价格
		MonthlyPrice        float64 `toml:"monthly_price"`         // 包月价格
		ChapterPrice        float64 `toml:"chapter_price"`         // 章节价格
	} `toml:"purchase"`
}

func Load(app string) *Config {
	var conf = new(Config)
	if err := config.Load(app, conf); err != nil {
		panic(fmt.Sprintf("config load failed: %v", err))
	}
	return conf
}
