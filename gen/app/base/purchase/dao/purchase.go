package dao

import (
	"context"
	"creativematrix.com/beyondreading/gen/common"
	"database/sql"
	"fmt"
	"time"

	"creativematrix.com/beyondreading/gen/common/po"
	"creativematrix.com/beyondreading/pkg/mysql"
	"github.com/jmoiron/sqlx"
)

// GetDB 获取数据库连接
func (d *Dao) GetDB(userId uint64) (*sqlx.DB, error) {
	return d.msshard.GetDB(userId)
}

// CreatePurchaseOrder 创建购买订单
func (d *Dao) CreatePurchaseOrder(ctx context.Context, order *po.PurchaseOrder) error {
	db, err := d.GetDB(order.UserId)
	if err != nil {
		return err
	}

	query := `INSERT INTO %s (order_id, account_id, user_id, order_type, book_id, book_name,
			  chapter_id, chapter_title, chapter_order, coin_amount, status, created_at, updated_at)
			  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

	query = d.setPurchaseOrderTable(query, order.UserId)
	_, err = db.Exec(query, order.OrderId, order.AccountId, order.UserId, order.OrderType,
		order.BookId, order.BookName, order.ChapterId, order.ChapterTitle, order.ChapterOrder,
		order.CoinAmount, order.Status, order.CreatedAt, order.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to create purchase order: %w", err)
	}

	return nil
}

// GetPurchaseOrdersByUserId 根据用户ID获取购买订单列表
func (d *Dao) GetPurchaseOrdersByUserId(ctx context.Context, userId uint64, page, pageSize int32, bookId string) ([]*po.PurchaseOrder, int64, error) {
	db, err := d.GetDB(userId)
	if err != nil {
		return nil, 0, err
	}

	// 构建查询条件
	whereClause := "WHERE user_id = ?"
	args := []interface{}{userId}

	if bookId != "" {
		whereClause += " AND book_id = ?"
		args = append(args, bookId)
	}

	// 获取总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s %s", "%s", whereClause)
	countQuery = d.setPurchaseOrderTable(countQuery, userId)
	var total int64
	err = db.Get(&total, countQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get purchase orders count: %w", err)
	}

	// 获取分页数据
	offset := (page - 1) * pageSize
	dataQuery := fmt.Sprintf(`SELECT order_id, account_id, user_id, order_type, book_id, book_name,
							  chapter_id, chapter_title, chapter_order, coin_amount, status, created_at, updated_at
							  FROM %s %s ORDER BY created_at DESC LIMIT ? OFFSET ?`, "%s", whereClause)
	dataQuery = d.setPurchaseOrderTable(dataQuery, userId)
	args = append(args, pageSize, offset)

	var orders []*po.PurchaseOrder
	err = db.Select(&orders, dataQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get purchase orders: %w", err)
	}

	return orders, total, nil
}

// CheckChapterPurchased 检查章节是否已购买
func (d *Dao) CheckChapterPurchased(ctx context.Context, userId uint64, bookId string, chapterOrder uint32) (*po.PurchaseOrder, error) {
	db, err := d.GetDB(userId)
	if err != nil {
		return nil, err
	}

	var order po.PurchaseOrder
	query := `SELECT order_id, account_id, user_id, order_type, book_id, book_name,
			  chapter_id, chapter_title, chapter_order, coin_amount, status, created_at, updated_at
			  FROM %s WHERE user_id = ? AND book_id = ? AND chapter_order = ? AND status = ?`

	query = d.setPurchaseOrderTable(query, userId)
	err = db.Get(&order, query, userId, bookId, chapterOrder, common.OrderStatusSuccess)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil // 章节未购买
		}
		return nil, fmt.Errorf("failed to check chapter purchased: %w", err)
	}

	return &order, nil
}

// GetPurchasedChaptersByBookId 获取用户在指定书籍中已购买的章节列表
func (d *Dao) GetPurchasedChaptersByBookId(ctx context.Context, userId uint64, bookId string, page, pageSize int32) ([]*po.PurchaseOrder, int64, error) {
	db, err := d.GetDB(userId)
	if err != nil {
		return nil, 0, err
	}

	whereClause := "WHERE user_id = ? AND book_id = ? AND status = ?"
	args := []interface{}{userId, bookId, common.OrderStatusSuccess}

	// 获取总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s %s", "%s", whereClause)
	countQuery = d.setPurchaseOrderTable(countQuery, userId)
	var total int64
	err = db.Get(&total, countQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get purchased chapters count: %w", err)
	}

	// 获取分页数据
	offset := (page - 1) * pageSize
	dataQuery := fmt.Sprintf(`SELECT order_id, account_id, user_id, order_type, book_id, book_name,
							  chapter_id, chapter_title, chapter_order, coin_amount, status, created_at, updated_at
							  FROM %s %s ORDER BY chapter_order ASC LIMIT ? OFFSET ?`, "%s", whereClause)
	dataQuery = d.setPurchaseOrderTable(dataQuery, userId)
	args = append(args, pageSize, offset)

	var orders []*po.PurchaseOrder
	err = db.Select(&orders, dataQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get purchased chapters: %w", err)
	}

	return orders, total, nil
}

// CreateVipMonthlyOrder 创建VIP/包月订单
func (d *Dao) CreateVipMonthlyOrder(ctx context.Context, order *po.VipMonthlyOrder) error {
	db, err := d.GetDB(order.UserId)
	if err != nil {
		return err
	}

	query := `INSERT INTO %s (order_id, account_id, user_id, order_type, coin_amount, duration_days,
			  start_time, end_time, status, created_at, updated_at)
			  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

	query = d.setVipMonthlyOrderTable(query, order.UserId)
	_, err = db.Exec(query, order.OrderId, order.AccountId, order.UserId, order.OrderType,
		order.CoinAmount, order.DurationDays, order.StartTime, order.EndTime,
		order.Status, order.CreatedAt, order.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to create vip/monthly order: %w", err)
	}

	return nil
}

// GetVipMonthlyOrdersByUserId 根据用户ID获取VIP/包月订单列表
func (d *Dao) GetVipMonthlyOrdersByUserId(ctx context.Context, userId uint64, page, pageSize int32, orderType string) ([]*po.VipMonthlyOrder, int64, error) {
	db, err := d.GetDB(userId)
	if err != nil {
		return nil, 0, err
	}

	// 构建查询条件
	whereClause := "WHERE user_id = ?"
	args := []interface{}{userId}

	if orderType != "" {
		whereClause += " AND order_type = ?"
		args = append(args, orderType)
	}

	// 获取总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM %s %s", "%s", whereClause)
	countQuery = d.setVipMonthlyOrderTable(countQuery, userId)
	var total int64
	err = db.Get(&total, countQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get vip/monthly orders count: %w", err)
	}

	// 获取分页数据
	offset := (page - 1) * pageSize
	dataQuery := fmt.Sprintf(`SELECT order_id, account_id, user_id, order_type, coin_amount, duration_days,
							  start_time, end_time, status, created_at, updated_at
							  FROM %s %s ORDER BY created_at DESC LIMIT ? OFFSET ?`, "%s", whereClause)
	dataQuery = d.setVipMonthlyOrderTable(dataQuery, userId)
	args = append(args, pageSize, offset)

	var orders []*po.VipMonthlyOrder
	err = db.Select(&orders, dataQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get vip/monthly orders: %w", err)
	}

	return orders, total, nil
}

// CheckVipStatus 检查用户VIP状态
func (d *Dao) CheckVipStatus(ctx context.Context, userId uint64) (*po.VipMonthlyOrder, error) {
	db, err := d.GetDB(userId)
	if err != nil {
		return nil, err
	}

	var order po.VipMonthlyOrder
	query := `SELECT order_id, account_id, user_id, order_type, coin_amount, duration_days,
			  start_time, end_time, status, created_at, updated_at
			  FROM %s WHERE user_id = ? AND order_type = ? AND status = ? AND end_time > ? 
			  ORDER BY end_time DESC LIMIT 1`

	query = d.setVipMonthlyOrderTable(query, userId)
	err = db.Get(&order, query, userId, common.OrderTypeVip, common.OrderStatusSuccess, time.Now())
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil // VIP未激活
		}
		return nil, fmt.Errorf("failed to check vip status: %w", err)
	}

	return &order, nil
}

// CheckMonthlyStatus 检查用户包月状态
func (d *Dao) CheckMonthlyStatus(ctx context.Context, userId uint64) (*po.VipMonthlyOrder, error) {
	db, err := d.GetDB(userId)
	if err != nil {
		return nil, err
	}

	var order po.VipMonthlyOrder
	query := `SELECT order_id, account_id, user_id, order_type, coin_amount, duration_days,
			  start_time, end_time, status, created_at, updated_at
			  FROM %s WHERE user_id = ? AND order_type = ? AND status = ? AND end_time > ? 
			  ORDER BY end_time DESC LIMIT 1`

	query = d.setVipMonthlyOrderTable(query, userId)
	err = db.Get(&order, query, userId, common.OrderTypeMonthly, common.OrderStatusSuccess, time.Now())
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil // 包月未激活
		}
		return nil, fmt.Errorf("failed to check monthly status: %w", err)
	}

	return &order, nil
}

// setPurchaseOrderTable 设置购买订单分表名称
func (d *Dao) setPurchaseOrderTable(query string, userId uint64) string {
	shardIndex := userId % common.PurchaseOrderTableShardNum
	tableName := fmt.Sprintf("purchase_order%02d", shardIndex)
	return fmt.Sprintf(query, tableName)
}

// setVipMonthlyOrderTable 设置VIP/包月订单分表名称
func (d *Dao) setVipMonthlyOrderTable(query string, userId uint64) string {
	shardIndex := userId % common.VipMonthlyOrderTableShardNum
	tableName := fmt.Sprintf("vipmonthly_order%02d", shardIndex)
	return fmt.Sprintf(query, tableName)
}
