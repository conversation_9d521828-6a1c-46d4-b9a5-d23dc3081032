package dao

import (
	"context"
	"creativematrix.com/beyondreading/gen/app/base/purchase/conf"
	"creativematrix.com/beyondreading/pkg/mysql"
	"creativematrix.com/beyondreading/pkg/redis"
)

// Dao 数据访问层
type Dao struct {
	msshard *mysql.MSShard
	redis   *redis.Redis
	conf    *conf.Config
}

// New 创建新的DAO实例
func New(msshard *mysql.MSShard, redis *redis.Redis) *Dao {
	return &Dao{
		msshard: msshard,
		redis:   redis,
	}
}

// Load 根据配置加载DAO实例
func Load(c *conf.Config) *Dao {
	msshard := mysql.NewMSShard(c.Mysql)
	cache := redis.Load(c.RedisPurchase)
	return &Dao{
		msshard: msshard,
		redis:   cache,
		conf:    c,
	}
}

// Close 关闭DAO连接
func (d *Dao) Close() {
	// 关闭连接
}

// Ping 健康检查
func (d *Dao) Ping(ctx context.Context) error {
	return nil
}

// GetDB 获取数据库连接
func (d *Dao) GetDB(userId uint64) (*sqlx.DB, error) {
	userIdStr := fmt.Sprintf("%d", userId)
	db, err := d.msshard.DB(userIdStr)
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}
	return db, nil
}

