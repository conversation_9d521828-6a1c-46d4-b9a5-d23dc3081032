package main

import (
	"context"
	"flag"
	"fmt"
	"net"
	"os"
	"os/signal"
	"syscall"
	"time"

	"creativematrix.com/beyondreading/gen/app/base/purchase/api"
	"creativematrix.com/beyondreading/gen/app/base/purchase/conf"
	"creativematrix.com/beyondreading/gen/app/base/purchase/svc"
	pb "creativematrix.com/beyondreading/gen/proto/purchase"
	"creativematrix.com/beyondreading/pkg/discovery"
	"creativematrix.com/beyondreading/pkg/gm"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/tracer"
	"google.golang.org/grpc"
	"google.golang.org/grpc/health"
	"google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/reflection"
)

var (
	configFile = flag.String("conf", "purchase.toml", "config file")
)

func main() {
	flag.Parse()

	// 加载配置
	config := conf.Load(api.App)

	// 初始化日志
	logger.InitLog()

	// 初始化链路追踪
	tracer.InitTracing()

	// 创建服务
	service := svc.Load(config)

	// 创建gRPC服务器
	s := grpc.NewServer(
		grpc.UnaryInterceptor(gm.UnaryServerInterceptor()),
	)

	// 注册服务
	pb.RegisterPurchaseServer(s, service)

	// 注册健康检查
	healthServer := health.NewServer()
	grpc_health_v1.RegisterHealthServer(s, healthServer)
	healthServer.SetServingStatus("", grpc_health_v1.HealthCheckResponse_SERVING)

	// 注册反射服务
	reflection.Register(s)

	// 监听端口
	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", config.Port))
	if err != nil {
		logger.LogFatalf("Failed to listen: %v", err)
	}

	// 服务注册
	register := discovery.NewRegister(config.Etcd.Addrs, logger.Log)
	defer register.Close()

	if err := register.Register(context.Background(), api.App, fmt.Sprintf("%s:%d", config.Host, config.Port), 10); err != nil {
		logger.LogFatalf("Failed to register service: %v", err)
	}

	logger.LogInfof("Purchase service listening on :%d", config.Port)

	// 启动服务
	go func() {
		if err := s.Serve(lis); err != nil {
			logger.LogFatalf("Failed to serve: %v", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.LogInfo("Shutting down server...")

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	done := make(chan struct{})
	go func() {
		s.GracefulStop()
		close(done)
	}()

	select {
	case <-done:
		logger.LogInfo("Server gracefully stopped")
	case <-ctx.Done():
		logger.LogInfo("Server shutdown timeout, forcing stop")
		s.Stop()
	}
}
