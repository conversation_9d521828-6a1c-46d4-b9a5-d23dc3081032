package dao

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"creativematrix.com/beyondreading/gen/app/base/account/model"
	"creativematrix.com/beyondreading/gen/pkg/utils"
	"creativematrix.com/beyondreading/pkg/logger"
)

// CreateRechargeOrder 创建充值订单
func (d *Dao) CreateRechargeOrder(ctx context.Context, order *model.RechargeOrder) error {
	db, err := d.GetDB(order.UserId)
	if err != nil {
		return err
	}

	query := `INSERT INTO recharge_order (order_id, account_id, user_id, amount, coin_amount,
			  exchange_rate, payment_method, status, created_at, updated_at)
			  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

	_, err = db.Exec(query, order.OrderId, order.AccountId, order.UserId,
		order.Amount, order.CoinAmount, order.ExchangeRate, order.PaymentMethod,
		order.Status, order.CreatedAt, order.UpdatedAt)
	if err != nil {
		return fmt.Errorf("failed to create recharge order: %w", err)
	}

	// 缓存订单信息
	if err := d.setRechargeOrderToCache(ctx, order); err != nil {
		logger.LogErrorf("Failed to cache recharge order: %v", err)
	}

	return nil
}

// GetRechargeOrder 获取充值订单
func (d *Dao) GetRechargeOrder(ctx context.Context, orderId string) (*model.RechargeOrder, error) {
	// 先尝试从缓存获取
	if order, err := d.getRechargeOrderFromCache(ctx, orderId); err == nil && order != nil {
		return order, nil
	}

	// 从数据库获取（这里简化处理，实际应该根据订单ID确定分库）
	// 可以通过订单ID的前缀或者其他规则来确定用户ID
	db, err := d.msshard.DB("") // 使用默认连接
	if err != nil {
		return nil, err
	}

	query := `SELECT order_id, account_id, user_id, amount, coin_amount, exchange_rate,
			  payment_method, payment_order_id, status, paid_at, created_at, updated_at
			  FROM recharge_order WHERE order_id = ?`

	var order model.RechargeOrder
	err = db.Get(&order, query, orderId)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get recharge order: %w", err)
	}

	// 缓存订单信息
	if err := d.setRechargeOrderToCache(ctx, &order); err != nil {
		logger.LogErrorf("Failed to cache recharge order: %v", err)
	}

	return &order, nil
}

// UpdateRechargeOrderStatus 更新充值订单状态
func (d *Dao) UpdateRechargeOrderStatus(ctx context.Context, orderId string, status int, paymentOrderId *string) error {
	// 先获取订单信息确定用户ID
	order, err := d.GetRechargeOrder(ctx, orderId)
	if err != nil {
		return err
	}
	if order == nil {
		return fmt.Errorf("recharge order not found: %s", orderId)
	}

	db, err := d.GetDB(order.UserId)
	if err != nil {
		return err
	}

	now := time.Now()
	var paidAt *time.Time
	if status == model.OrderStatusPaid {
		paidAt = &now
	}

	query := `UPDATE recharge_order SET status = ?, payment_order_id = ?, paid_at = ?,
			  updated_at = ? WHERE order_id = ?`

	_, err = db.Exec(query, status, paymentOrderId, paidAt, now, orderId)
	if err != nil {
		return fmt.Errorf("failed to update recharge order status: %w", err)
	}

	// 清除缓存
	d.deleteRechargeOrderFromCache(ctx, orderId)

	return nil
}

// CreatePurchaseOrder 创建购买订单
func (d *Dao) CreatePurchaseOrder(ctx context.Context, order *model.PurchaseOrder) error {
	db, err := d.GetDB(order.UserId)
	if err != nil {
		return err
	}

	query := `INSERT INTO purchase_order (order_id, account_id, user_id, order_type, book_id,
			  book_name, chapter_id, chapter_title, chapter_order, coin_amount, duration_days,
			  start_time, end_time, status, created_at, updated_at)
			  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

	_, err = db.Exec(query, order.OrderId, order.AccountId, order.UserId,
		order.OrderType, order.BookId, order.BookName, order.ChapterId, order.ChapterTitle,
		order.ChapterOrder, order.CoinAmount, order.DurationDays, order.StartTime,
		order.EndTime, order.Status, order.CreatedAt, order.UpdatedAt)
	if err != nil {
		return fmt.Errorf("failed to create purchase order: %w", err)
	}

	// 缓存订单信息
	if err := d.setPurchaseOrderToCache(ctx, order); err != nil {
		logger.LogErrorf("Failed to cache purchase order: %v", err)
	}

	return nil
}

// GetPurchaseOrders 获取购买订单列表
func (d *Dao) GetPurchaseOrders(ctx context.Context, userId string, page, pageSize int, orderType string) ([]*model.PurchaseOrder, int64, error) {
	db, err := d.GetDB(userId)
	if err != nil {
		return nil, 0, err
	}

	// 构建查询条件
	whereClause := "WHERE user_id = ?"
	args := []interface{}{userId}

	if orderType != "" {
		whereClause += " AND order_type = ?"
		args = append(args, orderType)
	}

	// 查询总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM purchase_order %s", whereClause)
	var total int64
	err = db.Get(&total, countQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count purchase orders: %w", err)
	}

	// 查询数据
	offset := (page - 1) * pageSize
	dataQuery := fmt.Sprintf(`SELECT order_id, account_id, user_id, order_type, book_id, book_name,
							  chapter_id, chapter_title, chapter_order, coin_amount, duration_days,
							  start_time, end_time, status, created_at, updated_at
							  FROM purchase_order %s ORDER BY created_at DESC LIMIT ? OFFSET ?`, whereClause)

	args = append(args, pageSize, offset)

	var orders []*model.PurchaseOrder
	err = db.Select(&orders, dataQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get purchase orders: %w", err)
	}

	return orders, total, nil
}

// CheckChapterPurchased 检查章节是否已购买
func (d *Dao) CheckChapterPurchased(ctx context.Context, userId, bookId, chapterId string) (*model.ChapterPurchaseStatus, error) {
	// 先尝试从缓存获取
	cacheKey := fmt.Sprintf(model.CacheKeyChapterPurchased, userId, bookId, chapterId)
	if data, err := d.cache.GetString(ctx, cacheKey); err == nil {
		var status model.ChapterPurchaseStatus
		if err := utils.JsonUnmarshal([]byte(data), &status); err == nil {
			return &status, nil
		}
	}

	db, err := d.GetDB(userId)
	if err != nil {
		return nil, err
	}

	query := `SELECT user_id, book_id, chapter_id, created_at FROM purchase_order
			  WHERE user_id = ? AND book_id = ? AND chapter_id = ? AND order_type = ? AND status = ?
			  ORDER BY created_at DESC LIMIT 1`

	var result struct {
		UserId    string    `db:"user_id"`
		BookId    string    `db:"book_id"`
		ChapterId string    `db:"chapter_id"`
		CreatedAt time.Time `db:"created_at"`
	}

	err = db.Get(&result, query, userId, bookId, chapterId, model.OrderTypeChapter, model.OrderStatusPaid)

	status := &model.ChapterPurchaseStatus{
		UserId:      userId,
		BookId:      bookId,
		ChapterId:   chapterId,
		IsPurchased: false,
	}

	if err == nil {
		status.IsPurchased = true
		status.PurchasedAt = result.CreatedAt
	} else if err != sql.ErrNoRows {
		return nil, fmt.Errorf("failed to check chapter purchased: %w", err)
	}

	// 缓存结果
	if data, err := utils.JsonMarshal(status); err == nil {
		d.cache.Set(ctx, cacheKey, string(data), model.CacheExpireChapter)
	}

	return status, nil
}

// CheckMonthlyStatus 检查包月状态
func (d *Dao) CheckMonthlyStatus(ctx context.Context, userId, bookId string) (*model.MonthlyStatus, error) {
	// 先尝试从缓存获取
	cacheKey := fmt.Sprintf(model.CacheKeyMonthlyStatus, userId, bookId)
	if data, err := d.cache.GetString(ctx, cacheKey); err == nil {
		var status model.MonthlyStatus
		if err := utils.JsonUnmarshal([]byte(data), &status); err == nil {
			return &status, nil
		}
	}

	db, err := d.GetDB(userId)
	if err != nil {
		return nil, err
	}

	now := time.Now()
	query := `SELECT user_id, book_id, start_time, end_time FROM purchase_order
			  WHERE user_id = ? AND book_id = ? AND order_type = ? AND status = ?
			  AND start_time <= ? AND end_time > ? ORDER BY end_time DESC LIMIT 1`

	var result struct {
		UserId    string    `db:"user_id"`
		BookId    string    `db:"book_id"`
		StartTime time.Time `db:"start_time"`
		EndTime   time.Time `db:"end_time"`
	}

	err = db.Get(&result, query, userId, bookId, model.OrderTypeMonthly, model.OrderStatusPaid, now, now)

	status := &model.MonthlyStatus{
		UserId:   userId,
		BookId:   bookId,
		IsActive: false,
	}

	if err == nil {
		status.IsActive = true
		status.StartTime = result.StartTime
		status.EndTime = result.EndTime
	} else if err != sql.ErrNoRows {
		return nil, fmt.Errorf("failed to check monthly status: %w", err)
	}

	// 缓存结果
	if data, err := utils.JsonMarshal(status); err == nil {
		d.cache.Set(ctx, cacheKey, string(data), model.CacheExpireMonthly)
	}

	return status, nil
}

// 缓存相关方法
func (d *Dao) getRechargeOrderFromCache(ctx context.Context, orderId string) (*model.RechargeOrder, error) {
	key := fmt.Sprintf(model.CacheKeyRechargeOrder, orderId)
	data, err := d.cache.GetString(ctx, key)
	if err != nil {
		return nil, err
	}

	var order model.RechargeOrder
	if err := utils.JsonUnmarshal([]byte(data), &order); err != nil {
		return nil, err
	}

	return &order, nil
}

func (d *Dao) setRechargeOrderToCache(ctx context.Context, order *model.RechargeOrder) error {
	key := fmt.Sprintf(model.CacheKeyRechargeOrder, order.OrderId)
	data, err := utils.JsonMarshal(order)
	if err != nil {
		return err
	}

	_, err = d.cache.Set(ctx, key, string(data), model.CacheExpireOrder)
	return err
}

func (d *Dao) deleteRechargeOrderFromCache(ctx context.Context, orderId string) error {
	key := fmt.Sprintf(model.CacheKeyRechargeOrder, orderId)
	return d.cache.DelKey(ctx, key)
}

func (d *Dao) setPurchaseOrderToCache(ctx context.Context, order *model.PurchaseOrder) error {
	key := fmt.Sprintf(model.CacheKeyPurchaseOrder, order.OrderId)
	data, err := utils.JsonMarshal(order)
	if err != nil {
		return err
	}

	_, err = d.cache.Set(ctx, key, string(data), model.CacheExpireOrder)
	return err
}
