package dao

// SQL常量定义 - 参照purchase模块的模式

const (
	// 账户相关SQL
	SelectAccountByUserId_SQL = "SELECT account_id, user_id, coin_balance, total_recharged, total_consumed, status, created_at, updated_at FROM account WHERE user_id = ?"
	
	InsertAccount_SQL = "INSERT INTO account (user_id, coin_balance, total_recharged, total_consumed, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?)"
	
	UpdateAccountBalance_SQL = "UPDATE account SET coin_balance = ?, updated_at = ? WHERE account_id = ?"
	
	SelectAccountForUpdate_SQL = "SELECT account_id, user_id, coin_balance, total_recharged, total_consumed, status FROM account WHERE user_id = ? FOR UPDATE"

	// 账户日志相关SQL
	InsertAccountLog_SQL = "INSERT INTO account_log (account_id, user_id, transaction_type, amount, balance_before, balance_after, order_id, book_id, chapter_id, description, extra_data, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
	
	SelectAccountLogs_SQL = "SELECT log_id, account_id, user_id, transaction_type, amount, balance_before, balance_after, order_id, book_id, chapter_id, description, extra_data, created_at FROM account_log WHERE user_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?"
	
	CountAccountLogs_SQL = "SELECT COUNT(*) FROM account_log WHERE user_id = ?"

	// 充值订单相关SQL
	InsertRechargeOrder_SQL = "INSERT INTO recharge_order (order_id, account_id, user_id, amount, coin_amount, exchange_rate, payment_method, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
	
	SelectRechargeOrder_SQL = "SELECT order_id, account_id, user_id, amount, coin_amount, exchange_rate, payment_method, payment_order_id, status, paid_at, created_at, updated_at FROM recharge_order WHERE order_id = ?"
	
	UpdateRechargeOrderStatus_SQL = "UPDATE recharge_order SET status = ?, payment_order_id = ?, paid_at = ?, updated_at = ? WHERE order_id = ?"

	// 购买订单相关SQL
	InsertPurchaseOrder_SQL = "INSERT INTO purchase_order (order_id, account_id, user_id, order_type, book_id, book_name, chapter_id, chapter_title, chapter_order, coin_amount, duration_days, start_time, end_time, status, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
	
	SelectPurchaseOrders_SQL = "SELECT order_id, account_id, user_id, order_type, book_id, book_name, chapter_id, chapter_title, chapter_order, coin_amount, duration_days, start_time, end_time, status, created_at, updated_at FROM purchase_order WHERE user_id = ? ORDER BY created_at DESC LIMIT ? OFFSET ?"
	
	CountPurchaseOrders_SQL = "SELECT COUNT(*) FROM purchase_order WHERE user_id = ?"

	// 章节购买状态检查SQL
	CheckChapterPurchased_SQL = "SELECT user_id, book_id, chapter_id, created_at FROM purchase_order WHERE user_id = ? AND book_id = ? AND chapter_id = ? AND order_type = ? AND status = ? ORDER BY created_at DESC LIMIT 1"

	// 包月状态检查SQL
	CheckMonthlyStatus_SQL = "SELECT user_id, book_id, start_time, end_time FROM purchase_order WHERE user_id = ? AND book_id = ? AND order_type = ? AND status = ? AND start_time <= ? AND end_time > ? ORDER BY end_time DESC LIMIT 1"
)

// 分表相关常量
const (
	ACCOUNT_TABLE_HASH = 100 // 账户表分片数量
	ORDER_TABLE_HASH   = 100 // 订单表分片数量
)

// getAccountTableShard 获取账户表分片ID
func (d *Dao) getAccountTableShard(userId string) (int, error) {
	// 简化实现，实际可以根据userId进行hash分片
	// 参照purchase模块的分片逻辑
	return 0, nil
}

// getOrderTableShard 获取订单表分片ID
func (d *Dao) getOrderTableShard(accountId uint64) (int, error) {
	// 参照purchase模块的分片逻辑
	return 0, nil
	
	// 实际分片逻辑（注释掉，因为当前简化实现）
	// tableId := int(accountId % ORDER_TABLE_HASH)
	// return tableId, nil
}
