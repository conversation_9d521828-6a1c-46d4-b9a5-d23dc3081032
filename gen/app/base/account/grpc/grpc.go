package grpc

import (
	"net"

	"creativematrix.com/beyondreading/gen/app/base/account/conf"
	"creativematrix.com/beyondreading/gen/app/base/account/svc"
	pb "creativematrix.com/beyondreading/gen/proto/account"
	"creativematrix.com/beyondreading/pkg/gm"
	"google.golang.org/grpc"
	"google.golang.org/grpc/health"
	"google.golang.org/grpc/health/grpc_health_v1"
	"google.golang.org/grpc/reflection"
)

// Start 启动gRPC服务器
func Start(c *conf.Config, svc *svc.Service) (*grpc.Server, error) {
	lis, err := net.Listen("tcp", c.Port.GRPC)
	if err != nil {
		return nil, err
	}

	s := grpc.NewServer(
		grpc.UnaryInterceptor(gm.UnaryServerInterceptor()),
	)

	// 注册账户服务
	pb.RegisterAccountServer(s, svc)

	// 注册健康检查服务
	healthServer := health.NewServer()
	grpc_health_v1.RegisterHealthServer(s, healthServer)
	healthServer.SetServingStatus("", grpc_health_v1.HealthCheckResponse_SERVING)

	// 注册反射服务
	reflection.Register(s)

	go func() {
		if err := s.Serve(lis); err != nil {
			panic(err)
		}
	}()

	return s, nil
}
