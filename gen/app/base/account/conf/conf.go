package conf

import (
	"creativematrix.com/beyondreading/pkg/redis"
	"fmt"

	"creativematrix.com/beyondreading/pkg/config"
)

type Config struct {
	config.Base

	RedisAccount *redis.Config

	Log struct {
		Level string
	}

	Account struct {
		DefaultExchangeRate float32 `toml:"default_exchange_rate"` // 默认兑换比例
		MinRechargeAmount   float64 `toml:"min_recharge_amount"`   // 最小充值金额
		MaxRechargeAmount   float64 `toml:"max_recharge_amount"`   // 最大充值金额
		CacheExpire         int     `toml:"cache_expire"`          // 缓存过期时间（秒）
		EnableCache         bool    `toml:"enable_cache"`          // 是否启用缓存
	} `toml:"account"`
}

func Load(app string) *Config {
	var conf = new(Config)
	if err := config.Load(app, conf); err != nil {
		panic(fmt.Sprintf("config load failed: %v", err))
	}
	return conf
}
