package svc

import (
	"context"
	"fmt"
	"time"

	"creativematrix.com/beyondreading/gen/app/base/account/model"
	"creativematrix.com/beyondreading/gen/pkg/utils"
	pb "creativematrix.com/beyondreading/gen/proto/account"
	"creativematrix.com/beyondreading/pkg/logger"
)

// GetAccount 获取账户信息
func (s *AccountSvc) GetAccount(ctx context.Context, req *pb.GetAccountReq) (*pb.GetAccountResp, error) {
	if req.UserId == "" {
		return &pb.GetAccountResp{
			Code:    400,
			Message: "用户ID不能为空",
		}, nil
	}

	account, err := s.dao.GetAccountByUserId(ctx, req.UserId)
	if err != nil {
		logger.LogErrorf("Failed to get account for user %s: %v", req.UserId, err)
		return &pb.GetAccountResp{
			Code:    500,
			Message: "获取账户信息失败",
		}, nil
	}

	if account == nil {
		return &pb.GetAccountResp{
			Code:    404,
			Message: "账户不存在",
		}, nil
	}

	return &pb.GetAccountResp{
		Code:    200,
		Message: "success",
		Account: s.convertAccountToProto(account),
	}, nil
}

// CreateAccount 创建账户
func (s *AccountSvc) CreateAccount(ctx context.Context, req *pb.CreateAccountReq) (*pb.CreateAccountResp, error) {
	if req.UserId == "" {
		return &pb.CreateAccountResp{
			Code:    400,
			Message: "用户ID不能为空",
		}, nil
	}

	// 检查账户是否已存在
	existingAccount, err := s.dao.GetAccountByUserId(ctx, req.UserId)
	if err != nil {
		logger.LogErrorf("Failed to check existing account for user %s: %v", req.UserId, err)
		return &pb.CreateAccountResp{
			Code:    500,
			Message: "检查账户失败",
		}, nil
	}

	if existingAccount != nil {
		return &pb.CreateAccountResp{
			Code:    409,
			Message: "账户已存在",
			Account: s.convertAccountToProto(existingAccount),
		}, nil
	}

	// 创建新账户
	account, err := s.dao.CreateAccount(ctx, req.UserId)
	if err != nil {
		logger.LogErrorf("Failed to create account for user %s: %v", req.UserId, err)
		return &pb.CreateAccountResp{
			Code:    500,
			Message: "创建账户失败",
		}, nil
	}

	return &pb.CreateAccountResp{
		Code:    200,
		Message: "账户创建成功",
		Account: s.convertAccountToProto(account),
	}, nil
}

// Recharge 充值
func (s *AccountSvc) Recharge(ctx context.Context, req *pb.RechargeReq) (*pb.RechargeResp, error) {
	if req.UserId == "" {
		return &pb.RechargeResp{
			Code:    400,
			Message: "用户ID不能为空",
		}, nil
	}

	if req.Amount == "" {
		return &pb.RechargeResp{
			Code:    400,
			Message: "充值金额不能为空",
		}, nil
	}

	// 验证充值金额
	amount, err := utils.ParseDecimal(req.Amount)
	if err != nil || amount.IsNegative() || amount.IsZero() {
		return &pb.RechargeResp{
			Code:    400,
			Message: "充值金额无效",
		}, nil
	}

	// 检查充值金额范围
	minAmount, _ := utils.ParseDecimal(s.conf.Account.MinRechargeAmount)
	maxAmount, _ := utils.ParseDecimal(s.conf.Account.MaxRechargeAmount)
	if amount.LessThan(minAmount) || amount.GreaterThan(maxAmount) {
		return &pb.RechargeResp{
			Code:    400,
			Message: fmt.Sprintf("充值金额必须在 %s - %s 之间", s.conf.Account.MinRechargeAmount, s.conf.Account.MaxRechargeAmount),
		}, nil
	}

	// 获取或创建账户
	account, err := s.getOrCreateAccount(ctx, req.UserId)
	if err != nil {
		logger.LogErrorf("Failed to get or create account for user %s: %v", req.UserId, err)
		return &pb.RechargeResp{
			Code:    500,
			Message: "获取账户信息失败",
		}, nil
	}

	// 计算书币数量
	exchangeRate := req.ExchangeRate
	if exchangeRate == "" {
		exchangeRate = s.conf.Account.DefaultExchangeRate
	}

	rate, err := utils.ParseDecimal(exchangeRate)
	if err != nil {
		return &pb.RechargeResp{
			Code:    400,
			Message: "兑换比例无效",
		}, nil
	}

	coinAmount := amount.Mul(rate)

	// 创建充值订单
	orderId := utils.GenerateOrderId("RC") // RC = Recharge
	now := time.Now()

	rechargeOrder := &model.RechargeOrder{
		OrderId:       orderId,
		AccountId:     account.AccountId,
		UserId:        req.UserId,
		Amount:        req.Amount,
		CoinAmount:    coinAmount.String(),
		ExchangeRate:  exchangeRate,
		PaymentMethod: req.PaymentMethod,
		Status:        model.OrderStatusPending,
		CreatedAt:     now,
		UpdatedAt:     now,
	}

	err = s.dao.CreateRechargeOrder(ctx, rechargeOrder)
	if err != nil {
		logger.LogErrorf("Failed to create recharge order for user %s: %v", req.UserId, err)
		return &pb.RechargeResp{
			Code:    500,
			Message: "创建充值订单失败",
		}, nil
	}

	return &pb.RechargeResp{
		Code:    200,
		Message: "充值订单创建成功",
		Order:   s.convertRechargeOrderToProto(rechargeOrder),
	}, nil
}

// PurchaseChapter 购买章节
func (s *AccountSvc) PurchaseChapter(ctx context.Context, req *pb.PurchaseChapterReq) (*pb.PurchaseChapterResp, error) {
	if req.UserId == "" || req.BookId == "" || req.ChapterId == "" {
		return &pb.PurchaseChapterResp{
			Code:    400,
			Message: "用户ID、书籍ID和章节ID不能为空",
		}, nil
	}

	if req.CoinAmount == "" {
		return &pb.PurchaseChapterResp{
			Code:    400,
			Message: "消费书币数量不能为空",
		}, nil
	}

	// 验证书币数量
	coinAmount, err := utils.ParseDecimal(req.CoinAmount)
	if err != nil || coinAmount.IsNegative() || coinAmount.IsZero() {
		return &pb.PurchaseChapterResp{
			Code:    400,
			Message: "消费书币数量无效",
		}, nil
	}

	// 检查是否已购买
	purchaseStatus, err := s.dao.CheckChapterPurchased(ctx, req.UserId, req.BookId, req.ChapterId)
	if err != nil {
		logger.LogErrorf("Failed to check chapter purchased status: %v", err)
		return &pb.PurchaseChapterResp{
			Code:    500,
			Message: "检查购买状态失败",
		}, nil
	}

	if purchaseStatus.IsPurchased {
		return &pb.PurchaseChapterResp{
			Code:    409,
			Message: "章节已购买",
		}, nil
	}

	// 获取账户信息
	account, err := s.dao.GetAccountByUserId(ctx, req.UserId)
	if err != nil {
		logger.LogErrorf("Failed to get account for user %s: %v", req.UserId, err)
		return &pb.PurchaseChapterResp{
			Code:    500,
			Message: "获取账户信息失败",
		}, nil
	}

	if account == nil {
		return &pb.PurchaseChapterResp{
			Code:    404,
			Message: "账户不存在",
		}, nil
	}

	// 检查余额
	balance, err := utils.ParseDecimal(account.CoinBalance)
	if err != nil {
		return &pb.PurchaseChapterResp{
			Code:    500,
			Message: "账户余额异常",
		}, nil
	}

	if balance.LessThan(coinAmount) {
		return &pb.PurchaseChapterResp{
			Code:    402,
			Message: "书币余额不足",
		}, nil
	}

	// 创建购买订单
	orderId := utils.GenerateOrderId("PC") // PC = Purchase Chapter
	now := time.Now()

	purchaseOrder := &model.PurchaseOrder{
		OrderId:      orderId,
		AccountId:    account.AccountId,
		UserId:       req.UserId,
		OrderType:    model.OrderTypeChapter,
		BookId:       &req.BookId,
		BookName:     &req.BookName,
		ChapterId:    &req.ChapterId,
		ChapterTitle: &req.ChapterTitle,
		ChapterOrder: &req.ChapterOrder,
		CoinAmount:   req.CoinAmount,
		Status:       model.OrderStatusPending,
		CreatedAt:    now,
		UpdatedAt:    now,
	}

	err = s.dao.CreatePurchaseOrder(ctx, purchaseOrder)
	if err != nil {
		logger.LogErrorf("Failed to create purchase order: %v", err)
		return &pb.PurchaseChapterResp{
			Code:    500,
			Message: "创建购买订单失败",
		}, nil
	}

	// 扣除书币
	transactionReq := &model.TransactionRequest{
		UserId:          req.UserId,
		Amount:          "-" + req.CoinAmount, // 负数表示扣除
		TransactionType: model.TransactionTypePurchaseChapter,
		OrderId:         &orderId,
		BookId:          &req.BookId,
		ChapterId:       &req.ChapterId,
		Description:     utils.StringPtr(fmt.Sprintf("购买章节：%s", req.ChapterTitle)),
	}

	err = s.dao.UpdateAccountBalance(ctx, transactionReq)
	if err != nil {
		logger.LogErrorf("Failed to update account balance: %v", err)
		return &pb.PurchaseChapterResp{
			Code:    500,
			Message: "扣除书币失败",
		}, nil
	}

	// 更新订单状态为已支付
	purchaseOrder.Status = model.OrderStatusPaid
	purchaseOrder.UpdatedAt = time.Now()

	return &pb.PurchaseChapterResp{
		Code:    200,
		Message: "章节购买成功",
		Order:   s.convertPurchaseOrderToProto(purchaseOrder),
	}, nil
}

// PurchaseMonthly 购买包月
func (s *AccountSvc) PurchaseMonthly(ctx context.Context, req *pb.PurchaseMonthlyReq) (*pb.PurchaseMonthlyResp, error) {
	if req.UserId == "" || req.BookId == "" {
		return &pb.PurchaseMonthlyResp{
			Code:    400,
			Message: "用户ID和书籍ID不能为空",
		}, nil
	}

	if req.CoinAmount == "" {
		return &pb.PurchaseMonthlyResp{
			Code:    400,
			Message: "消费书币数量不能为空",
		}, nil
	}

	// 验证书币数量
	coinAmount, err := utils.ParseDecimal(req.CoinAmount)
	if err != nil || coinAmount.IsNegative() || coinAmount.IsZero() {
		return &pb.PurchaseMonthlyResp{
			Code:    400,
			Message: "消费书币数量无效",
		}, nil
	}

	// 设置默认包月天数
	durationDays := req.DurationDays
	if durationDays <= 0 {
		durationDays = 30 // 默认30天
	}

	// 检查当前包月状态
	monthlyStatus, err := s.dao.CheckMonthlyStatus(ctx, req.UserId, req.BookId)
	if err != nil {
		logger.LogErrorf("Failed to check monthly status: %v", err)
		return &pb.PurchaseMonthlyResp{
			Code:    500,
			Message: "检查包月状态失败",
		}, nil
	}

	if monthlyStatus.IsActive {
		return &pb.PurchaseMonthlyResp{
			Code:    409,
			Message: "当前已有有效包月",
		}, nil
	}

	// 获取账户信息
	account, err := s.dao.GetAccountByUserId(ctx, req.UserId)
	if err != nil {
		logger.LogErrorf("Failed to get account for user %s: %v", req.UserId, err)
		return &pb.PurchaseMonthlyResp{
			Code:    500,
			Message: "获取账户信息失败",
		}, nil
	}

	if account == nil {
		return &pb.PurchaseMonthlyResp{
			Code:    404,
			Message: "账户不存在",
		}, nil
	}

	// 检查余额
	balance, err := utils.ParseDecimal(account.CoinBalance)
	if err != nil {
		return &pb.PurchaseMonthlyResp{
			Code:    500,
			Message: "账户余额异常",
		}, nil
	}

	if balance.LessThan(coinAmount) {
		return &pb.PurchaseMonthlyResp{
			Code:    402,
			Message: "书币余额不足",
		}, nil
	}

	// 创建购买订单
	orderId := utils.GenerateOrderId("PM") // PM = Purchase Monthly
	now := time.Now()
	startTime := now
	endTime := now.AddDate(0, 0, int(durationDays))

	purchaseOrder := &model.PurchaseOrder{
		OrderId:      orderId,
		AccountId:    account.AccountId,
		UserId:       req.UserId,
		OrderType:    model.OrderTypeMonthly,
		BookId:       &req.BookId,
		BookName:     &req.BookName,
		CoinAmount:   req.CoinAmount,
		DurationDays: &[]int{int(durationDays)}[0],
		StartTime:    &startTime,
		EndTime:      &endTime,
		Status:       model.OrderStatusPending,
		CreatedAt:    now,
		UpdatedAt:    now,
	}

	err = s.dao.CreatePurchaseOrder(ctx, purchaseOrder)
	if err != nil {
		logger.LogErrorf("Failed to create purchase order: %v", err)
		return &pb.PurchaseMonthlyResp{
			Code:    500,
			Message: "创建购买订单失败",
		}, nil
	}

	// 扣除书币
	transactionReq := &model.TransactionRequest{
		UserId:          req.UserId,
		Amount:          "-" + req.CoinAmount,
		TransactionType: model.TransactionTypePurchaseMonthly,
		OrderId:         &orderId,
		BookId:          &req.BookId,
		Description:     utils.StringPtr(fmt.Sprintf("购买包月：%s", req.BookName)),
	}

	err = s.dao.UpdateAccountBalance(ctx, transactionReq)
	if err != nil {
		logger.LogErrorf("Failed to update account balance: %v", err)
		return &pb.PurchaseMonthlyResp{
			Code:    500,
			Message: "扣除书币失败",
		}, nil
	}

	// 更新订单状态为已支付
	purchaseOrder.Status = model.OrderStatusPaid
	purchaseOrder.UpdatedAt = time.Now()

	return &pb.PurchaseMonthlyResp{
		Code:    200,
		Message: "包月购买成功",
		Order:   s.convertPurchaseOrderToProto(purchaseOrder),
	}, nil
}

// PurchaseVip 购买VIP
func (s *AccountSvc) PurchaseVip(ctx context.Context, req *pb.PurchaseVipReq) (*pb.PurchaseVipResp, error) {
	if req.UserId == "" {
		return &pb.PurchaseVipResp{
			Code:    400,
			Message: "用户ID不能为空",
		}, nil
	}

	if req.CoinAmount == "" {
		return &pb.PurchaseVipResp{
			Code:    400,
			Message: "消费书币数量不能为空",
		}, nil
	}

	// 验证书币数量
	coinAmount, err := utils.ParseDecimal(req.CoinAmount)
	if err != nil || coinAmount.IsNegative() || coinAmount.IsZero() {
		return &pb.PurchaseVipResp{
			Code:    400,
			Message: "消费书币数量无效",
		}, nil
	}

	// 设置默认VIP天数
	durationDays := req.DurationDays
	if durationDays <= 0 {
		durationDays = 30 // 默认30天
	}

	// 获取账户信息
	account, err := s.dao.GetAccountByUserId(ctx, req.UserId)
	if err != nil {
		logger.LogErrorf("Failed to get account for user %s: %v", req.UserId, err)
		return &pb.PurchaseVipResp{
			Code:    500,
			Message: "获取账户信息失败",
		}, nil
	}

	if account == nil {
		return &pb.PurchaseVipResp{
			Code:    404,
			Message: "账户不存在",
		}, nil
	}

	// 检查余额
	balance, err := utils.ParseDecimal(account.CoinBalance)
	if err != nil {
		return &pb.PurchaseVipResp{
			Code:    500,
			Message: "账户余额异常",
		}, nil
	}

	if balance.LessThan(coinAmount) {
		return &pb.PurchaseVipResp{
			Code:    402,
			Message: "书币余额不足",
		}, nil
	}

	// 创建购买订单
	orderId := utils.GenerateOrderId("PV") // PV = Purchase VIP
	now := time.Now()
	startTime := now
	endTime := now.AddDate(0, 0, int(durationDays))

	purchaseOrder := &model.PurchaseOrder{
		OrderId:      orderId,
		AccountId:    account.AccountId,
		UserId:       req.UserId,
		OrderType:    model.OrderTypeVip,
		CoinAmount:   req.CoinAmount,
		DurationDays: &[]int{int(durationDays)}[0],
		StartTime:    &startTime,
		EndTime:      &endTime,
		Status:       model.OrderStatusPending,
		CreatedAt:    now,
		UpdatedAt:    now,
	}

	err = s.dao.CreatePurchaseOrder(ctx, purchaseOrder)
	if err != nil {
		logger.LogErrorf("Failed to create purchase order: %v", err)
		return &pb.PurchaseVipResp{
			Code:    500,
			Message: "创建购买订单失败",
		}, nil
	}

	// 扣除书币
	transactionReq := &model.TransactionRequest{
		UserId:          req.UserId,
		Amount:          "-" + req.CoinAmount,
		TransactionType: model.TransactionTypePurchaseVip,
		OrderId:         &orderId,
		Description:     utils.StringPtr(fmt.Sprintf("购买VIP：%d天", durationDays)),
	}

	err = s.dao.UpdateAccountBalance(ctx, transactionReq)
	if err != nil {
		logger.LogErrorf("Failed to update account balance: %v", err)
		return &pb.PurchaseVipResp{
			Code:    500,
			Message: "扣除书币失败",
		}, nil
	}

	// 更新订单状态为已支付
	purchaseOrder.Status = model.OrderStatusPaid
	purchaseOrder.UpdatedAt = time.Now()

	return &pb.PurchaseVipResp{
		Code:    200,
		Message: "VIP购买成功",
		Order:   s.convertPurchaseOrderToProto(purchaseOrder),
	}, nil
}

// 获取或创建账户
func (s *AccountSvc) getOrCreateAccount(ctx context.Context, userId string) (*model.Account, error) {
	account, err := s.dao.GetAccountByUserId(ctx, userId)
	if err != nil {
		return nil, err
	}

	if account == nil {
		account, err = s.dao.CreateAccount(ctx, userId)
		if err != nil {
			return nil, err
		}
	}

	return account, nil
}

// 转换模型到Proto
func (s *AccountSvc) convertAccountToProto(account *model.Account) *pb.AccountInfo {
	return &pb.AccountInfo{
		AccountId:      account.AccountId,
		UserId:         account.UserId,
		CoinBalance:    account.CoinBalance,
		TotalRecharged: account.TotalRecharged,
		TotalConsumed:  account.TotalConsumed,
		Status:         int32(account.Status),
		CreatedAt:      account.CreatedAt.Unix(),
		UpdatedAt:      account.UpdatedAt.Unix(),
	}
}

func (s *AccountSvc) convertRechargeOrderToProto(order *model.RechargeOrder) *pb.RechargeOrder {
	protoOrder := &pb.RechargeOrder{
		OrderId:       order.OrderId,
		AccountId:     order.AccountId,
		UserId:        order.UserId,
		Amount:        order.Amount,
		CoinAmount:    order.CoinAmount,
		ExchangeRate:  order.ExchangeRate,
		PaymentMethod: order.PaymentMethod,
		Status:        int32(order.Status),
		CreatedAt:     order.CreatedAt.Unix(),
		UpdatedAt:     order.UpdatedAt.Unix(),
	}

	if order.PaymentOrderId != nil {
		protoOrder.PaymentOrderId = *order.PaymentOrderId
	}

	if order.PaidAt != nil {
		protoOrder.PaidAt = order.PaidAt.Unix()
	}

	return protoOrder
}

func (s *AccountSvc) convertPurchaseOrderToProto(order *model.PurchaseOrder) *pb.PurchaseOrder {
	protoOrder := &pb.PurchaseOrder{
		OrderId:    order.OrderId,
		AccountId:  order.AccountId,
		UserId:     order.UserId,
		OrderType:  order.OrderType,
		CoinAmount: order.CoinAmount,
		Status:     int32(order.Status),
		CreatedAt:  order.CreatedAt.Unix(),
		UpdatedAt:  order.UpdatedAt.Unix(),
	}

	if order.BookId != nil {
		protoOrder.BookId = *order.BookId
	}
	if order.BookName != nil {
		protoOrder.BookName = *order.BookName
	}
	if order.ChapterId != nil {
		protoOrder.ChapterId = *order.ChapterId
	}
	if order.ChapterTitle != nil {
		protoOrder.ChapterTitle = *order.ChapterTitle
	}
	if order.ChapterOrder != nil {
		protoOrder.ChapterOrder = *order.ChapterOrder
	}
	if order.DurationDays != nil {
		protoOrder.DurationDays = int32(*order.DurationDays)
	}
	if order.StartTime != nil {
		protoOrder.StartTime = order.StartTime.Unix()
	}
	if order.EndTime != nil {
		protoOrder.EndTime = order.EndTime.Unix()
	}

	return protoOrder
}
