package svc

import (
	"context"
	"fmt"
	"time"

	"creativematrix.com/beyondreading/gen/app/base/account/conf"
	"creativematrix.com/beyondreading/gen/app/base/account/dao"
	"creativematrix.com/beyondreading/gen/common/po"
	pb "creativematrix.com/beyondreading/gen/proto/account"
	"creativematrix.com/beyondreading/pkg/logger"
	"database/sql"
)

// Service 账户服务
type Service struct {
	dao *dao.Dao
}

// New 创建账户服务实例
func New(dao *dao.Dao) *Service {
	return &Service{
		dao: dao,
	}
}

// Load 根据配置加载服务实例
func Load(c *conf.Config) *Service {
	return New(dao.Load(c))
}

// Close 关闭服务
func (s *Service) Close() {
	s.dao.Close()
}

// Ping 健康检查
func (s *Service) Ping(ctx context.Context) error {
	return s.dao.Ping(ctx)
}

// GetAccount 获取账户信息
func (s *Service) GetAccount(ctx context.Context, req *pb.GetAccountReq) (*pb.GetAccountResp, error) {
	account, err := s.dao.GetAccountByUserId(ctx, req.UserId)
	if err != nil {
		if err == sql.ErrNoRows {
			return &pb.GetAccountResp{
				Code:    404,
				Message: "Account not found",
			}, nil
		}
		logger.LogErrorf("Failed to get account: %v", err)
		return &pb.GetAccountResp{
			Code:    500,
			Message: "Internal server error",
		}, nil
	}

	return &pb.GetAccountResp{
		Code:    200,
		Message: "Success",
		Account: s.convertAccountToPB(account),
	}, nil
}

// CreateAccount 创建账户
func (s *Service) CreateAccount(ctx context.Context, req *pb.CreateAccountReq) (*pb.CreateAccountResp, error) {
	// 检查账户是否已存在
	existingAccount, err := s.dao.GetAccountByUserId(ctx, req.UserId)
	if err == nil && existingAccount != nil {
		return &pb.CreateAccountResp{
			Code:    200,
			Message: "Account already exists",
			Account: s.convertAccountToPB(existingAccount),
		}, nil
	}

	// 创建新账户
	account, err := s.dao.CreateAccount(ctx, req.UserId)
	if err != nil {
		logger.LogErrorf("Failed to create account: %v", err)
		return &pb.CreateAccountResp{
			Code:    500,
			Message: "Failed to create account",
		}, nil
	}

	return &pb.CreateAccountResp{
		Code:    200,
		Message: "Account created successfully",
		Account: s.convertAccountToPB(account),
	}, nil
}

// Recharge 充值
func (s *Service) Recharge(ctx context.Context, req *pb.RechargeReq) (*pb.RechargeResp, error) {
	// 计算书币数量
	coinAmount := req.Amount * float64(req.ExchangeRate)
	if req.ExchangeRate == 0 {
		coinAmount = req.Amount // 默认1:1兑换
	}

	// 更新账户余额
	account, err := s.dao.UpdateAccountBalance(ctx, req.UserId, coinAmount, "recharge")
	if err != nil {
		logger.LogErrorf("Failed to update account balance: %v", err)
		return &pb.RechargeResp{
			Code:    500,
			Message: "Failed to recharge",
		}, nil
	}

	// 创建充值订单（这里简化处理，实际应该先创建订单再更新余额）
	orderId := fmt.Sprintf("RO%d%02d%02d%02d%02d%02d",
		time.Now().Year(), time.Now().Month(), time.Now().Day(),
		time.Now().Hour(), time.Now().Minute(), time.Now().Second())

	// 创建账户日志
	log := &po.AccountLog{
		AccountId:       account.AccountId,
		UserId:          req.UserId,
		TransactionType: "recharge",
		Amount:          coinAmount,
		BalanceBefore:   account.CoinBalance - coinAmount,
		BalanceAfter:    account.CoinBalance,
		OrderId:         orderId,
		Description:     fmt.Sprintf("Recharge %.2f yuan, get %.2f coins", req.Amount, coinAmount),
		CreatedAt:       time.Now(),
	}

	err = s.dao.CreateAccountLog(ctx, log)
	if err != nil {
		logger.LogErrorf("Failed to create account log: %v", err)
	}

	// 构造充值订单响应
	order := &pb.RechargeOrder{
		OrderId:       orderId,
		AccountId:     account.AccountId,
		UserId:        req.UserId,
		Amount:        req.Amount,
		CoinAmount:    coinAmount,
		ExchangeRate:  req.ExchangeRate,
		PaymentMethod: req.PaymentMethod,
		Status:        2, // 支付成功
		PaidAt:        time.Now().Unix(),
		CreatedAt:     time.Now().Unix(),
		UpdatedAt:     time.Now().Unix(),
	}

	return &pb.RechargeResp{
		Code:    200,
		Message: "Recharge successful",
		Order:   order,
	}, nil
}

// GetAccountLogs 获取账户日志
func (s *Service) GetAccountLogs(ctx context.Context, req *pb.GetAccountLogsReq) (*pb.GetAccountLogsResp, error) {
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	logs, total, err := s.dao.GetAccountLogs(ctx, req.UserId, req.Page, req.PageSize, req.TransactionType)
	if err != nil {
		logger.LogErrorf("Failed to get account logs: %v", err)
		return &pb.GetAccountLogsResp{
			Code:    500,
			Message: "Failed to get account logs",
		}, nil
	}

	var pbLogs []*pb.AccountLog
	for _, log := range logs {
		pbLogs = append(pbLogs, s.convertAccountLogToPB(log))
	}

	return &pb.GetAccountLogsResp{
		Code:    200,
		Message: "Success",
		Logs:    pbLogs,
		Total:   total,
	}, nil
}

// UpdateUserStatus 更新用户状态
func (s *Service) UpdateUserStatus(ctx context.Context, req *pb.UpdateUserStatusReq) (*pb.UpdateUserStatusResp, error) {
	var vipExpireTime, monthlyExpireTime *time.Time

	if req.VipExpireTime > 0 {
		t := time.Unix(req.VipExpireTime, 0)
		vipExpireTime = &t
	}

	if req.MonthlyExpireTime > 0 {
		t := time.Unix(req.MonthlyExpireTime, 0)
		monthlyExpireTime = &t
	}

	account, err := s.dao.UpdateUserStatus(ctx, req.UserId, req.UserType, vipExpireTime, monthlyExpireTime)
	if err != nil {
		logger.LogErrorf("Failed to update user status: %v", err)
		return &pb.UpdateUserStatusResp{
			Code:    500,
			Message: "Failed to update user status",
		}, nil
	}

	return &pb.UpdateUserStatusResp{
		Code:    200,
		Message: "User status updated successfully",
		Account: s.convertAccountToPB(account),
	}, nil
}

// DeductCoins 扣除书币
func (s *Service) DeductCoins(ctx context.Context, req *pb.DeductCoinsReq) (*pb.DeductCoinsResp, error) {
	// 更新账户余额（扣除书币）
	account, err := s.dao.UpdateAccountBalance(ctx, req.UserId, -req.Amount, req.TransactionType)
	if err != nil {
		logger.LogErrorf("Failed to deduct coins: %v", err)
		return &pb.DeductCoinsResp{
			Code:    500,
			Message: fmt.Sprintf("Failed to deduct coins: %v", err),
		}, nil
	}

	// 创建账户日志
	log := &po.AccountLog{
		AccountId:       account.AccountId,
		UserId:          req.UserId,
		TransactionType: req.TransactionType,
		Amount:          -req.Amount,
		BalanceBefore:   account.CoinBalance + req.Amount,
		BalanceAfter:    account.CoinBalance,
		OrderId:         req.OrderId,
		BookId:          req.BookId,
		ChapterId:       req.ChapterId,
		Description:     req.Description,
		CreatedAt:       time.Now(),
	}

	err = s.dao.CreateAccountLog(ctx, log)
	if err != nil {
		logger.LogErrorf("Failed to create account log: %v", err)
	}

	return &pb.DeductCoinsResp{
		Code:    200,
		Message: "Coins deducted successfully",
		Account: s.convertAccountToPB(account),
	}, nil
}

// CheckUserStatus 检查用户状态（不包含书籍可读性判断）
func (s *Service) CheckUserStatus(ctx context.Context, req *pb.CheckUserStatusReq) (*pb.CheckUserStatusResp, error) {
	account, hasVip, hasMonthly, err := s.dao.CheckUserStatus(ctx, req.UserId)
	if err != nil {
		logger.LogErrorf("Failed to check user status: %v", err)
		return &pb.CheckUserStatusResp{
			Code:    500,
			Message: "Failed to check user status",
		}, nil
	}

	return &pb.CheckUserStatusResp{
		Code:       200,
		Message:    "Success",
		Account:    s.convertAccountToPB(account),
		HasVip:     hasVip,
		HasMonthly: hasMonthly,
	}, nil
}

// convertAccountToPB 转换Account为protobuf格式
func (s *Service) convertAccountToPB(account *po.Account) *pb.AccountInfo {
	pbAccount := &pb.AccountInfo{
		AccountId:      account.AccountId,
		UserId:         account.UserId,
		CoinBalance:    account.CoinBalance,
		TotalRecharged: account.TotalRecharged,
		TotalConsumed:  account.TotalConsumed,
		Status:         account.Status,
		UserType:       account.UserType,
		UserLevel:      account.UserLevel,
		CreatedAt:      account.CreatedAt.Unix(),
		UpdatedAt:      account.UpdatedAt.Unix(),
	}

	if account.VipExpireTime != nil {
		pbAccount.VipExpireTime = account.VipExpireTime.Unix()
	}

	if account.MonthlyExpireTime != nil {
		pbAccount.MonthlyExpireTime = account.MonthlyExpireTime.Unix()
	}

	return pbAccount
}

// convertAccountLogToPB 转换AccountLog为protobuf格式
func (s *Service) convertAccountLogToPB(log *po.AccountLog) *pb.AccountLog {
	return &pb.AccountLog{
		LogId:           log.LogId,
		AccountId:       log.AccountId,
		UserId:          log.UserId,
		TransactionType: log.TransactionType,
		Amount:          log.Amount,
		BalanceBefore:   log.BalanceBefore,
		BalanceAfter:    log.BalanceAfter,
		OrderId:         log.OrderId,
		BookId:          log.BookId,
		ChapterId:       log.ChapterId,
		Description:     log.Description,
		ExtraData:       log.ExtraData,
		CreatedAt:       log.CreatedAt.Unix(),
	}
}
