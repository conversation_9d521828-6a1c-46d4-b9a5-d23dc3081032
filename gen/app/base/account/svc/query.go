package svc

import (
	"context"

	pb "creativematrix.com/beyondreading/gen/proto/account"
	"creativematrix.com/beyondreading/pkg/logger"
)

// GetAccountLogs 获取账户日志
func (s *AccountSvc) GetAccountLogs(ctx context.Context, req *pb.GetAccountLogsReq) (*pb.GetAccountLogsResp, error) {
	if req.UserId == "" {
		return &pb.GetAccountLogsResp{
			Code:    400,
			Message: "用户ID不能为空",
		}, nil
	}

	// 设置默认分页参数
	page := req.Page
	if page <= 0 {
		page = 1
	}

	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 100 {
		pageSize = 100 // 限制最大页面大小
	}

	logs, total, err := s.dao.GetAccountLogs(ctx, req.UserId, int(page), int(pageSize), req.TransactionType)
	if err != nil {
		logger.LogErrorf("Failed to get account logs for user %s: %v", req.UserId, err)
		return &pb.GetAccountLogsResp{
			Code:    500,
			Message: "获取账户日志失败",
		}, nil
	}

	// 转换为Proto格式
	protoLogs := make([]*pb.AccountLog, 0, len(logs))
	for _, log := range logs {
		protoLog := &pb.AccountLog{
			LogId:           log.LogId,
			AccountId:       log.AccountId,
			UserId:          log.UserId,
			TransactionType: log.TransactionType,
			Amount:          log.Amount,
			BalanceBefore:   log.BalanceBefore,
			BalanceAfter:    log.BalanceAfter,
			CreatedAt:       log.CreatedAt.Unix(),
		}

		if log.OrderId != nil {
			protoLog.OrderId = *log.OrderId
		}
		if log.BookId != nil {
			protoLog.BookId = *log.BookId
		}
		if log.ChapterId != nil {
			protoLog.ChapterId = *log.ChapterId
		}
		if log.Description != nil {
			protoLog.Description = *log.Description
		}
		if log.ExtraData != nil {
			protoLog.ExtraData = *log.ExtraData
		}

		protoLogs = append(protoLogs, protoLog)
	}

	return &pb.GetAccountLogsResp{
		Code:    200,
		Message: "success",
		Logs:    protoLogs,
		Total:   total,
	}, nil
}

// GetRechargeOrder 获取充值订单
func (s *AccountSvc) GetRechargeOrder(ctx context.Context, req *pb.GetRechargeOrderReq) (*pb.GetRechargeOrderResp, error) {
	if req.OrderId == "" {
		return &pb.GetRechargeOrderResp{
			Code:    400,
			Message: "订单ID不能为空",
		}, nil
	}

	order, err := s.dao.GetRechargeOrder(ctx, req.OrderId)
	if err != nil {
		logger.LogErrorf("Failed to get recharge order %s: %v", req.OrderId, err)
		return &pb.GetRechargeOrderResp{
			Code:    500,
			Message: "获取充值订单失败",
		}, nil
	}

	if order == nil {
		return &pb.GetRechargeOrderResp{
			Code:    404,
			Message: "充值订单不存在",
		}, nil
	}

	return &pb.GetRechargeOrderResp{
		Code:    200,
		Message: "success",
		Order:   s.convertRechargeOrderToProto(order),
	}, nil
}

// GetPurchaseOrders 获取购买订单列表
func (s *AccountSvc) GetPurchaseOrders(ctx context.Context, req *pb.GetPurchaseOrdersReq) (*pb.GetPurchaseOrdersResp, error) {
	if req.UserId == "" {
		return &pb.GetPurchaseOrdersResp{
			Code:    400,
			Message: "用户ID不能为空",
		}, nil
	}

	// 设置默认分页参数
	page := req.Page
	if page <= 0 {
		page = 1
	}

	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 100 {
		pageSize = 100 // 限制最大页面大小
	}

	orders, total, err := s.dao.GetPurchaseOrders(ctx, req.UserId, int(page), int(pageSize), req.OrderType)
	if err != nil {
		logger.LogErrorf("Failed to get purchase orders for user %s: %v", req.UserId, err)
		return &pb.GetPurchaseOrdersResp{
			Code:    500,
			Message: "获取购买订单失败",
		}, nil
	}

	// 转换为Proto格式
	protoOrders := make([]*pb.PurchaseOrder, 0, len(orders))
	for _, order := range orders {
		protoOrders = append(protoOrders, s.convertPurchaseOrderToProto(order))
	}

	return &pb.GetPurchaseOrdersResp{
		Code:    200,
		Message: "success",
		Orders:  protoOrders,
		Total:   total,
	}, nil
}

// CheckChapterPurchased 检查章节是否已购买
func (s *AccountSvc) CheckChapterPurchased(ctx context.Context, req *pb.CheckChapterPurchasedReq) (*pb.CheckChapterPurchasedResp, error) {
	if req.UserId == "" || req.BookId == "" || req.ChapterId == "" {
		return &pb.CheckChapterPurchasedResp{
			Code:    400,
			Message: "用户ID、书籍ID和章节ID不能为空",
		}, nil
	}

	status, err := s.dao.CheckChapterPurchased(ctx, req.UserId, req.BookId, req.ChapterId)
	if err != nil {
		logger.LogErrorf("Failed to check chapter purchased status: %v", err)
		return &pb.CheckChapterPurchasedResp{
			Code:    500,
			Message: "检查章节购买状态失败",
		}, nil
	}

	resp := &pb.CheckChapterPurchasedResp{
		Code:        200,
		Message:     "success",
		IsPurchased: status.IsPurchased,
	}

	if status.IsPurchased {
		resp.PurchasedAt = status.PurchasedAt.Unix()
	}

	return resp, nil
}

// CheckMonthlyStatus 检查包月状态
func (s *AccountSvc) CheckMonthlyStatus(ctx context.Context, req *pb.CheckMonthlyStatusReq) (*pb.CheckMonthlyStatusResp, error) {
	if req.UserId == "" || req.BookId == "" {
		return &pb.CheckMonthlyStatusResp{
			Code:    400,
			Message: "用户ID和书籍ID不能为空",
		}, nil
	}

	status, err := s.dao.CheckMonthlyStatus(ctx, req.UserId, req.BookId)
	if err != nil {
		logger.LogErrorf("Failed to check monthly status: %v", err)
		return &pb.CheckMonthlyStatusResp{
			Code:    500,
			Message: "检查包月状态失败",
		}, nil
	}

	resp := &pb.CheckMonthlyStatusResp{
		Code:     200,
		Message:  "success",
		IsActive: status.IsActive,
	}

	if status.IsActive {
		resp.StartTime = status.StartTime.Unix()
		resp.EndTime = status.EndTime.Unix()
	}

	return resp, nil
}
