package api

import (
	"context"
	"creativematrix.com/beyondreading/pkg/discovery"
	"creativematrix.com/beyondreading/pkg/gm"
	"creativematrix.com/beyondreading/pkg/logger"
	"fmt"
	"google.golang.org/grpc/balancer/roundrobin"
	"google.golang.org/grpc/resolver"

	pb "creativematrix.com/beyondreading/gen/proto/user"
	"creativematrix.com/beyondreading/pkg/grpc"
	"google.golang.org/grpc"
)

type Client struct {
	client pb.UserServiceClient
}

func NewClient() *Client {
	options := []grpc.DialOption{
		grpc.WithInsecure(),
		grpc.WithDefaultServiceConfig(fmt.Sprintf(`{"LoadBalancingPolicy": "%s"}`, roundrobin.Name)),
		grpc.WithUnaryInterceptor(gm.UnaryClientInterceptor()),
	}

	r := discovery.NewResolver(c.Etcd.Addrs, logger.Log)
	resolver.Register(r)

	conn, err := grpc.NewClient("etcd:///"+App, options...)
	//conn, err := grpc.Dial("192.168.3.62:32333", options...)
	if err != nil {
		return nil, err
	}
	return pb.NewUserClient(conn), nil
}

// RegisterBySms 手机短信注册
func (c *Client) RegisterBySms(ctx context.Context, req *pb.RegisterBySmsReq) (*pb.RegisterBySmsResp, error) {
	return c.client.RegisterBySms(ctx, req)
}

// RegisterByGoogle Google账户注册
func (c *Client) RegisterByGoogle(ctx context.Context, req *pb.RegisterByGoogleReq) (*pb.RegisterByGoogleResp, error) {
	return c.client.RegisterByGoogle(ctx, req)
}

// RegisterByApple Apple账户注册
func (c *Client) RegisterByApple(ctx context.Context, req *pb.RegisterByAppleReq) (*pb.RegisterByAppleResp, error) {
	return c.client.RegisterByApple(ctx, req)
}

// LoginBySms 手机短信登录
func (c *Client) LoginBySms(ctx context.Context, req *pb.LoginBySmsReq) (*pb.LoginBySmsResp, error) {
	return c.client.LoginBySms(ctx, req)
}

// LoginByGoogle Google账户登录
func (c *Client) LoginByGoogle(ctx context.Context, req *pb.LoginByGoogleReq) (*pb.LoginByGoogleResp, error) {
	return c.client.LoginByGoogle(ctx, req)
}

// LoginByApple Apple账户登录
func (c *Client) LoginByApple(ctx context.Context, req *pb.LoginByAppleReq) (*pb.LoginByAppleResp, error) {
	return c.client.LoginByApple(ctx, req)
}

// GetUserInfo 获取用户信息
func (c *Client) GetUserInfo(ctx context.Context, req *pb.GetUserInfoReq) (*pb.GetUserInfoResp, error) {
	return c.client.GetUserInfo(ctx, req)
}

// UpdateUserInfo 更新用户信息
func (c *Client) UpdateUserInfo(ctx context.Context, req *pb.UpdateUserInfoReq) (*pb.UpdateUserInfoResp, error) {
	return c.client.UpdateUserInfo(ctx, req)
}

// GetLoginLogs 获取登录日志
func (c *Client) GetLoginLogs(ctx context.Context, req *pb.GetLoginLogsReq) (*pb.GetLoginLogsResp, error) {
	return c.client.GetLoginLogs(ctx, req)
}

// SendSmsCode 发送短信验证码
func (c *Client) SendSmsCode(ctx context.Context, req *pb.SendSmsCodeReq) (*pb.SendSmsCodeResp, error) {
	return c.client.SendSmsCode(ctx, req)
}

// VerifySmsCode 验证短信验证码
func (c *Client) VerifySmsCode(ctx context.Context, req *pb.VerifySmsCodeReq) (*pb.VerifySmsCodeResp, error) {
	return c.client.VerifySmsCode(ctx, req)
}
