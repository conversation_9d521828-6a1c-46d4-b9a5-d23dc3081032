package model

// Redis缓存Key
const (
	RedisUserInfoId      = "user:info:%d"        // 用户信息缓存
	RedisUserToken       = "user:token:%s"       // 用户Token缓存
	RedisPhoneToUserId   = "user:phone:%s"       // 手机号到用户ID映射
	RedisEmailToUserId   = "user:email:%s"       // 邮箱到用户ID映射
	RedisGoogleToUserId  = "user:google:%s"      // Google ID到用户ID映射
	RedisAppleToUserId   = "user:apple:%s"       // Apple ID到用户ID映射
	RedisSmsCode         = "sms:code:%s:%d"      // 短信验证码缓存
	RedisSmsLimit        = "sms:limit:%s"        // 短信发送限制
	RedisLoginLimit      = "login:limit:%s"      // 登录限制
)

// 用户状态
const (
	UserStatusNormal   = 1 // 正常
	UserStatusFrozen   = 2 // 冻结
	UserStatusDeleted  = 3 // 注销
)

// 登录类型
const (
	LoginTypeSms    = 1 // 手机短信
	LoginTypeGoogle = 2 // Google
	LoginTypeApple  = 3 // Apple
)

// 性别
const (
	GenderUnknown = 0 // 未知
	GenderMale    = 1 // 男
	GenderFemale  = 2 // 女
)

// 登录结果
const (
	LoginResultSuccess = 1 // 成功
	LoginResultFailed  = 2 // 失败
)

// 短信验证码类型
const (
	SmsCodeTypeRegister = 1 // 注册
	SmsCodeTypeLogin    = 2 // 登录
)

// 短信发送限制
const (
	SmsMaxSendPerDay   = 10               // 每天最多发送次数
	SmsMaxSendPerHour  = 5                // 每小时最多发送次数
	SmsCodeExpireTime  = 5 * 60           // 验证码过期时间（秒）
	SmsCodeLength      = 6                // 验证码长度
)

// 登录限制
const (
	MaxFailedLoginAttempts = 5            // 最大失败登录次数
	LoginLockDuration      = 30 * 60      // 登录锁定时间（秒）
	MaxLoginPerIpPerHour   = 100          // 每小时每IP最大登录次数
)

// JWT配置
const (
	JWTDefaultExpireTime = 7 * 24 * 60 * 60 // 默认过期时间（秒）
	JWTDefaultIssuer     = "beyondreading"   // 默认签发者
)

// 第三方登录配置
const (
	GoogleTokenVerifyURL = "https://www.googleapis.com/oauth2/v3/tokeninfo"
	AppleTokenVerifyURL  = "https://appleid.apple.com/auth/keys"
)

// 错误消息
const (
	ErrUserNotFound        = "User not found"
	ErrUserAlreadyExists   = "User already exists"
	ErrInvalidCredentials  = "Invalid credentials"
	ErrUserFrozen          = "User account is frozen"
	ErrUserDeleted         = "User account is deleted"
	ErrSmsCodeInvalid      = "SMS code is invalid"
	ErrSmsCodeExpired      = "SMS code is expired"
	ErrSmsCodeUsed         = "SMS code is already used"
	ErrTooManyRequests     = "Too many requests"
	ErrLoginLocked         = "Login is locked due to too many failed attempts"
	ErrTokenInvalid        = "Token is invalid"
	ErrTokenExpired        = "Token is expired"
	ErrGoogleTokenInvalid  = "Google token is invalid"
	ErrAppleTokenInvalid   = "Apple token is invalid"
)
