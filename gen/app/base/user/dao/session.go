package dao

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"creativematrix.com/beyondreading/gen/common/po"
)

// CreateUserSession 创建用户会话
func (d *Dao) CreateUserSession(ctx context.Context, session *po.UserSession) error {
	query := `INSERT INTO userSession (sessionId, userId, token, deviceId, userAgent, loginIp, expireAt, createdAt, updatedAt)
			  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
			  ON DUPLICATE KEY UPDATE 
			  token = VALUES(token), expireAt = VALUES(expireAt), updatedAt = VALUES(updatedAt)`

	now := time.Now()
	session.CreatedAt = now
	session.UpdatedAt = now

	db := d.msshard.Master()
	_, err := db.ExecContext(ctx, query,
		session.SessionId, session.UserId, session.Token, session.DeviceId,
		session.UserAgent, session.LoginIp, session.ExpireAt,
		session.CreatedAt, session.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to create user session: %w", err)
	}

	return nil
}

// GetUserSession 获取用户会话
func (d *Dao) GetUserSession(ctx context.Context, sessionId string) (*po.UserSession, error) {
	query := `SELECT sessionId, userId, token, deviceId, userAgent, loginIp, expireAt, createdAt, updatedAt
			  FROM userSession WHERE sessionId = ? AND expireAt > NOW()`

	db := d.msshard.Slave()
	session := &po.UserSession{}
	err := db.GetContext(ctx, session, query, sessionId)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get user session: %w", err)
	}

	return session, nil
}

// GetUserSessionsByUserId 获取用户的所有会话
func (d *Dao) GetUserSessionsByUserId(ctx context.Context, userId uint64) ([]*po.UserSession, error) {
	query := `SELECT sessionId, userId, token, deviceId, userAgent, loginIp, expireAt, createdAt, updatedAt
			  FROM userSession WHERE userId = ? AND expireAt > NOW()
			  ORDER BY updatedAt DESC`

	db := d.msshard.Slave()
	var sessions []*po.UserSession
	err := db.SelectContext(ctx, &sessions, query, userId)
	if err != nil {
		return nil, fmt.Errorf("failed to get user sessions: %w", err)
	}

	return sessions, nil
}

// UpdateUserSession 更新用户会话
func (d *Dao) UpdateUserSession(ctx context.Context, sessionId string, expireAt time.Time) error {
	query := `UPDATE userSession SET expireAt = ?, updatedAt = ? WHERE sessionId = ?`

	db := d.msshard.Master()
	_, err := db.ExecContext(ctx, query, expireAt, time.Now(), sessionId)
	if err != nil {
		return fmt.Errorf("failed to update user session: %w", err)
	}

	return nil
}

// DeleteUserSession 删除用户会话
func (d *Dao) DeleteUserSession(ctx context.Context, sessionId string) error {
	query := `DELETE FROM userSession WHERE sessionId = ?`

	db := d.msshard.Master()
	_, err := db.ExecContext(ctx, query, sessionId)
	if err != nil {
		return fmt.Errorf("failed to delete user session: %w", err)
	}

	return nil
}

// DeleteUserSessionsByUserId 删除用户的所有会话
func (d *Dao) DeleteUserSessionsByUserId(ctx context.Context, userId uint64) error {
	query := `DELETE FROM userSession WHERE userId = ?`

	db := d.msshard.Master()
	_, err := db.ExecContext(ctx, query, userId)
	if err != nil {
		return fmt.Errorf("failed to delete user sessions: %w", err)
	}

	return nil
}

// CleanExpiredSessions 清理过期的会话
func (d *Dao) CleanExpiredSessions(ctx context.Context) error {
	query := `DELETE FROM userSession WHERE expireAt < NOW()`

	db := d.msshard.Master()
	_, err := db.ExecContext(ctx, query)
	if err != nil {
		return fmt.Errorf("failed to clean expired sessions: %w", err)
	}

	return nil
}
