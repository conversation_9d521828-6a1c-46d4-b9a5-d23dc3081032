package svc

import (
	"context"
	"errors"
	"fmt"
	"time"

	"creativematrix.com/beyondreading/gen/app/base/user/model"
	"creativematrix.com/beyondreading/gen/common/po"
	pb "creativematrix.com/beyondreading/gen/proto/user"
	"creativematrix.com/beyondreading/pkg/logger"
)

// RegisterBySms 手机短信注册
func (s *UserSvc) RegisterBySms(ctx context.Context, req *pb.RegisterBySmsReq) (*pb.RegisterBySmsResp, error) {
	// 验证短信验证码
	isValid, err := s.dao.VerifySmsCode(ctx, req.Phone, req.SmsCode, model.SmsCodeTypeRegister)
	if err != nil {
		logger.LogErrorf("Failed to verify sms code: %v", err)
		return &pb.RegisterBySmsResp{
			Code:    500,
			Message: "Failed to verify sms code",
		}, nil
	}

	if !isValid {
		return &pb.RegisterBySmsResp{
			Code:    400,
			Message: model.ErrSmsCodeInvalid,
		}, nil
	}

	// 检查用户是否已存在
	existingUser, err := s.dao.GetUserByPhone(ctx, req.Phone)
	if err != nil {
		logger.LogErrorf("Failed to check existing user: %v", err)
		return &pb.RegisterBySmsResp{
			Code:    500,
			Message: "Failed to check existing user",
		}, nil
	}

	if existingUser != nil {
		return &pb.RegisterBySmsResp{
			Code:    400,
			Message: model.ErrUserAlreadyExists,
		}, nil
	}

	// 创建用户
	user := &po.User{
		Phone:       req.Phone,
		Nickname:    req.Nickname,
		Status:      model.UserStatusNormal,
		LoginType:   model.LoginTypeSms,
		LastLoginIp: req.ClientIp,
	}

	if user.Nickname == "" {
		user.Nickname = fmt.Sprintf("User_%s", req.Phone[len(req.Phone)-4:])
	}

	now := time.Now()
	user.LastLoginAt = &now

	createdUser, err := s.dao.CreateUser(ctx, user)
	if err != nil {
		logger.LogErrorf("Failed to create user: %v", err)
		return &pb.RegisterBySmsResp{
			Code:    500,
			Message: "Failed to create user",
		}, nil
	}

	// 生成JWT token
	token, err := s.jwtService.GenerateToken(createdUser)
	if err != nil {
		logger.LogErrorf("Failed to generate token: %v", err)
		return &pb.RegisterBySmsResp{
			Code:    500,
			Message: "Failed to generate token",
		}, nil
	}

	// 记录登录日志
	loginLog := &po.LoginLog{
		UserId:      createdUser.UserId,
		LoginType:   model.LoginTypeSms,
		LoginIp:     req.ClientIp,
		UserAgent:   req.UserAgent,
		DeviceId:    req.DeviceId,
		LoginResult: model.LoginResultSuccess,
	}

	err = s.dao.CreateLoginLog(ctx, loginLog)
	if err != nil {
		logger.LogErrorf("Failed to create login log: %v", err)
	}

	return &pb.RegisterBySmsResp{
		Code:    200,
		Message: "Registration successful",
		User:    s.convertUserToPB(createdUser),
		Token:   token,
	}, nil
}

// RegisterByGoogle Google账户注册
func (s *UserSvc) RegisterByGoogle(ctx context.Context, req *pb.RegisterByGoogleReq) (*pb.RegisterByGoogleResp, error) {
	// 验证Google token
	tokenInfo, err := s.googleService.VerifyToken(ctx, req.GoogleToken)
	if err != nil {
		logger.LogErrorf("Failed to verify google token: %v", err)
		return &pb.RegisterByGoogleResp{
			Code:    400,
			Message: model.ErrGoogleTokenInvalid,
		}, nil
	}

	// 检查用户是否已存在
	existingUser, err := s.dao.GetUserByGoogleId(ctx, req.GoogleId)
	if err != nil {
		logger.LogErrorf("Failed to check existing user: %v", err)
		return &pb.RegisterByGoogleResp{
			Code:    500,
			Message: "Failed to check existing user",
		}, nil
	}

	if existingUser != nil {
		return &pb.RegisterByGoogleResp{
			Code:    400,
			Message: model.ErrUserAlreadyExists,
		}, nil
	}

	// 创建用户
	user := &po.User{
		Email:       req.Email,
		Nickname:    req.Nickname,
		Avatar:      req.Avatar,
		Status:      model.UserStatusNormal,
		LoginType:   model.LoginTypeGoogle,
		GoogleId:    req.GoogleId,
		LastLoginIp: req.ClientIp,
	}

	if user.Email == "" {
		user.Email = tokenInfo.Email
	}
	if user.Nickname == "" {
		user.Nickname = tokenInfo.Name
	}
	if user.Avatar == "" {
		user.Avatar = tokenInfo.Picture
	}

	now := time.Now()
	user.LastLoginAt = &now

	createdUser, err := s.dao.CreateUser(ctx, user)
	if err != nil {
		logger.LogErrorf("Failed to create user: %v", err)
		return &pb.RegisterByGoogleResp{
			Code:    500,
			Message: "Failed to create user",
		}, nil
	}

	// 生成JWT token
	token, err := s.jwtService.GenerateToken(createdUser)
	if err != nil {
		logger.LogErrorf("Failed to generate token: %v", err)
		return &pb.RegisterByGoogleResp{
			Code:    500,
			Message: "Failed to generate token",
		}, nil
	}

	// 记录登录日志
	loginLog := &po.LoginLog{
		UserId:      createdUser.UserId,
		LoginType:   model.LoginTypeGoogle,
		LoginIp:     req.ClientIp,
		UserAgent:   req.UserAgent,
		DeviceId:    req.DeviceId,
		LoginResult: model.LoginResultSuccess,
	}

	err = s.dao.CreateLoginLog(ctx, loginLog)
	if err != nil {
		logger.LogErrorf("Failed to create login log: %v", err)
	}

	return &pb.RegisterByGoogleResp{
		Code:    200,
		Message: "Registration successful",
		User:    s.convertUserToPB(createdUser),
		Token:   token,
	}, nil
}

// RegisterByApple Apple账户注册
func (s *UserSvc) RegisterByApple(ctx context.Context, req *pb.RegisterByAppleReq) (*pb.RegisterByAppleResp, error) {
	// 验证Apple token
	tokenClaims, err := s.appleService.VerifyToken(ctx, req.AppleToken)
	if err != nil {
		logger.LogErrorf("Failed to verify apple token: %v", err)
		return &pb.RegisterByAppleResp{
			Code:    400,
			Message: model.ErrAppleTokenInvalid,
		}, nil
	}

	// 检查用户是否已存在
	existingUser, err := s.dao.GetUserByAppleId(ctx, req.AppleId)
	if err != nil {
		logger.LogErrorf("Failed to check existing user: %v", err)
		return &pb.RegisterByAppleResp{
			Code:    500,
			Message: "Failed to check existing user",
		}, nil
	}

	if existingUser != nil {
		return &pb.RegisterByAppleResp{
			Code:    400,
			Message: model.ErrUserAlreadyExists,
		}, nil
	}

	// 创建用户
	user := &po.User{
		Email:       req.Email,
		Nickname:    req.Nickname,
		Status:      model.UserStatusNormal,
		LoginType:   model.LoginTypeApple,
		AppleId:     req.AppleId,
		LastLoginIp: req.ClientIp,
	}

	if user.Email == "" {
		user.Email = tokenClaims.Email
	}
	if user.Nickname == "" {
		user.Nickname = fmt.Sprintf("Apple_User_%s", req.AppleId[len(req.AppleId)-6:])
	}

	now := time.Now()
	user.LastLoginAt = &now

	createdUser, err := s.dao.CreateUser(ctx, user)
	if err != nil {
		logger.LogErrorf("Failed to create user: %v", err)
		return &pb.RegisterByAppleResp{
			Code:    500,
			Message: "Failed to create user",
		}, nil
	}

	// 生成JWT token
	token, err := s.jwtService.GenerateToken(createdUser)
	if err != nil {
		logger.LogErrorf("Failed to generate token: %v", err)
		return &pb.RegisterByAppleResp{
			Code:    500,
			Message: "Failed to generate token",
		}, nil
	}

	// 记录登录日志
	loginLog := &po.LoginLog{
		UserId:      createdUser.UserId,
		LoginType:   model.LoginTypeApple,
		LoginIp:     req.ClientIp,
		UserAgent:   req.UserAgent,
		DeviceId:    req.DeviceId,
		LoginResult: model.LoginResultSuccess,
	}

	err = s.dao.CreateLoginLog(ctx, loginLog)
	if err != nil {
		logger.LogErrorf("Failed to create login log: %v", err)
	}

	return &pb.RegisterByAppleResp{
		Code:    200,
		Message: "Registration successful",
		User:    s.convertUserToPB(createdUser),
		Token:   token,
	}, nil
}

// LoginBySms 手机短信登录
func (s *UserSvc) LoginBySms(ctx context.Context, req *pb.LoginBySmsReq) (*pb.LoginBySmsResp, error) {
	// 验证短信验证码
	isValid, err := s.dao.VerifySmsCode(ctx, req.Phone, req.SmsCode, model.SmsCodeTypeLogin)
	if err != nil {
		logger.LogErrorf("Failed to verify sms code: %v", err)
		return &pb.LoginBySmsResp{
			Code:    500,
			Message: "Failed to verify sms code",
		}, nil
	}

	if !isValid {
		return &pb.LoginBySmsResp{
			Code:    400,
			Message: model.ErrSmsCodeInvalid,
		}, nil
	}

	// 获取用户信息
	user, err := s.dao.GetUserByPhone(ctx, req.Phone)
	if err != nil {
		logger.LogErrorf("Failed to get user: %v", err)
		return &pb.LoginBySmsResp{
			Code:    500,
			Message: "Failed to get user",
		}, nil
	}

	if user == nil {
		return &pb.LoginBySmsResp{
			Code:    404,
			Message: model.ErrUserNotFound,
		}, nil
	}

	// 检查用户状态
	if user.Status == model.UserStatusFrozen {
		return &pb.LoginBySmsResp{
			Code:    403,
			Message: model.ErrUserFrozen,
		}, nil
	}

	if user.Status == model.UserStatusDeleted {
		return &pb.LoginBySmsResp{
			Code:    403,
			Message: model.ErrUserDeleted,
		}, nil
	}

	// 更新登录信息
	err = s.dao.UpdateUserLoginInfo(ctx, user.UserId, req.ClientIp)
	if err != nil {
		logger.LogErrorf("Failed to update user login info: %v", err)
	}

	// 生成JWT token
	token, err := s.jwtService.GenerateToken(user)
	if err != nil {
		logger.LogErrorf("Failed to generate token: %v", err)
		return &pb.LoginBySmsResp{
			Code:    500,
			Message: "Failed to generate token",
		}, nil
	}

	// 记录登录日志
	loginLog := &po.LoginLog{
		UserId:      user.UserId,
		LoginType:   model.LoginTypeSms,
		LoginIp:     req.ClientIp,
		UserAgent:   req.UserAgent,
		DeviceId:    req.DeviceId,
		LoginResult: model.LoginResultSuccess,
	}

	err = s.dao.CreateLoginLog(ctx, loginLog)
	if err != nil {
		logger.LogErrorf("Failed to create login log: %v", err)
	}

	return &pb.LoginBySmsResp{
		Code:    200,
		Message: "Login successful",
		User:    s.convertUserToPB(user),
		Token:   token,
	}, nil
}
