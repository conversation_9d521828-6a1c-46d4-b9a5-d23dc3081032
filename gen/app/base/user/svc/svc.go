package svc

import (
	"context"

	"creativematrix.com/beyondreading/gen/app/base/user/conf"
	"creativematrix.com/beyondreading/gen/app/base/user/dao"
	"creativematrix.com/beyondreading/gen/app/base/user/service"
)

type UserSvc struct {
	conf          *conf.Config
	dao           *dao.Dao
	smsService    *service.SmsService
	jwtService    *service.JwtService
	googleService *service.GoogleService
	appleService  *service.AppleService
}

func Load(c *conf.Config) *UserSvc {
	svc := &UserSvc{
		conf:          c,
		dao:           dao.Load(c),
		smsService:    service.NewSmsService(c.SMS),
		jwtService:    service.NewJwtService(c.JWT),
		googleService: service.NewGoogleService(c.Google),
		appleService:  service.NewAppleService(c.Apple),
	}

	return svc
}

func (s *UserSvc) Ping(ctx context.Context) error {
	return s.dao.Ping(ctx)
}

func (s *UserSvc) Close() {
	s.dao.Close()
}
