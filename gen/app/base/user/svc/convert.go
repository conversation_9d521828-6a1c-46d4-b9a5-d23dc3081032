package svc

import (
	"creativematrix.com/beyondreading/gen/common/po"
	pb "creativematrix.com/beyondreading/gen/proto/user"
)

// convertUserToPB 转换用户信息为protobuf格式
func (s *UserSvc) convertUserToPB(user *po.User) *pb.UserInfo {
	if user == nil {
		return nil
	}

	pbUser := &pb.UserInfo{
		UserId:      user.UserId,
		Phone:       user.Phone,
		Email:       user.Email,
		Nickname:    user.Nickname,
		Avatar:      user.Avatar,
		Gender:      user.Gender,
		Birthday:    user.Birthday,
		Location:    user.Location,
		Status:      user.Status,
		LoginType:   user.LoginType,
		GoogleId:    user.GoogleId,
		AppleId:     user.AppleId,
		LastLoginIp: user.LastLoginIp,
		CreatedAt:   user.CreatedAt.Unix(),
		UpdatedAt:   user.UpdatedAt.Unix(),
	}

	if user.LastLoginAt != nil {
		pbUser.LastLoginAt = user.LastLoginAt.Unix()
	}

	return pbUser
}

// convertLoginLogToPB 转换登录日志为protobuf格式
func (s *UserSvc) convertLoginLogToPB(log *po.LoginLog) *pb.LoginLog {
	if log == nil {
		return nil
	}

	pbLog := &pb.LoginLog{
		LogId:       log.LogId,
		UserId:      log.UserId,
		LoginType:   log.LoginType,
		LoginIp:     log.LoginIp,
		UserAgent:   log.UserAgent,
		DeviceId:    log.DeviceId,
		LoginResult: log.LoginResult,
		FailReason:  log.FailReason,
		CreatedAt:   log.CreatedAt.Unix(),
	}

	return pbLog
}
