package service

import (
	"context"
	"crypto/rsa"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"creativematrix.com/beyondreading/gen/app/base/user/conf"
	"creativematrix.com/beyondreading/pkg/logger"
	"github.com/golang-jwt/jwt/v5"
)

type AppleService struct {
	config *conf.AppleConfig
}

type AppleTokenClaims struct {
	Iss           string `json:"iss"`
	Aud           string `json:"aud"`
	Exp           int64  `json:"exp"`
	Iat           int64  `json:"iat"`
	Sub           string `json:"sub"`
	Email         string `json:"email"`
	EmailVerified string `json:"email_verified"`
	IsPrivateEmail string `json:"is_private_email"`
	RealUserStatus int    `json:"real_user_status"`
	jwt.RegisteredClaims
}

type ApplePublicKey struct {
	Kty string `json:"kty"`
	Kid string `json:"kid"`
	Use string `json:"use"`
	Alg string `json:"alg"`
	N   string `json:"n"`
	E   string `json:"e"`
}

type AppleKeysResponse struct {
	Keys []ApplePublicKey `json:"keys"`
}

func NewAppleService(config *conf.AppleConfig) *AppleService {
	return &AppleService{
		config: config,
	}
}

// VerifyToken 验证Apple ID token
func (a *AppleService) VerifyToken(ctx context.Context, idToken string) (*AppleTokenClaims, error) {
	// 解析token header获取kid
	token, _, err := new(jwt.Parser).ParseUnverified(idToken, &AppleTokenClaims{})
	if err != nil {
		return nil, fmt.Errorf("failed to parse token header: %w", err)
	}

	kid, ok := token.Header["kid"].(string)
	if !ok {
		return nil, fmt.Errorf("missing kid in token header")
	}

	// 获取Apple公钥
	publicKey, err := a.getApplePublicKey(ctx, kid)
	if err != nil {
		return nil, fmt.Errorf("failed to get apple public key: %w", err)
	}

	// 验证token
	parsedToken, err := jwt.ParseWithClaims(idToken, &AppleTokenClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return publicKey, nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to verify apple token: %w", err)
	}

	claims, ok := parsedToken.Claims.(*AppleTokenClaims)
	if !ok || !parsedToken.Valid {
		return nil, fmt.Errorf("invalid apple token")
	}

	// 验证audience
	if claims.Aud != a.config.ClientID {
		return nil, fmt.Errorf("invalid audience: %s", claims.Aud)
	}

	// 验证issuer
	if claims.Iss != "https://appleid.apple.com" {
		return nil, fmt.Errorf("invalid issuer: %s", claims.Iss)
	}

	// 验证过期时间
	if claims.Exp < time.Now().Unix() {
		return nil, fmt.Errorf("token is expired")
	}

	logger.LogInfof("Apple token verified successfully for user: %s", claims.Sub)
	return claims, nil
}

// getApplePublicKey 获取Apple公钥
func (a *AppleService) getApplePublicKey(ctx context.Context, kid string) (*rsa.PublicKey, error) {
	// 获取Apple公钥列表
	req, err := http.NewRequestWithContext(ctx, "GET", "https://appleid.apple.com/auth/keys", nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get apple keys: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("apple keys request failed with status: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var keysResp AppleKeysResponse
	err = json.Unmarshal(body, &keysResp)
	if err != nil {
		return nil, fmt.Errorf("failed to parse keys response: %w", err)
	}

	// 查找对应的公钥
	for _, key := range keysResp.Keys {
		if key.Kid == kid {
			return a.parseRSAPublicKey(key)
		}
	}

	return nil, fmt.Errorf("public key not found for kid: %s", kid)
}

// parseRSAPublicKey 解析RSA公钥
func (a *AppleService) parseRSAPublicKey(key ApplePublicKey) (*rsa.PublicKey, error) {
	// 这里需要实现RSA公钥解析
	// 由于涉及复杂的密码学操作，这里简化实现
	// 实际项目中需要使用专门的库来处理JWK格式的公钥
	
	// 示例实现（实际需要更完整的实现）
	return nil, fmt.Errorf("RSA public key parsing not implemented")
}

// ValidateAppleId 验证Apple ID格式
func (a *AppleService) ValidateAppleId(appleId string) bool {
	// Apple ID通常是一个长字符串
	return len(appleId) > 0 && len(appleId) <= 100
}

// GenerateClientSecret 生成客户端密钥（用于服务端到服务端的调用）
func (a *AppleService) GenerateClientSecret() (string, error) {
	// 这里需要使用Apple提供的私钥生成JWT
	// 实际实现需要读取私钥文件并生成JWT
	
	now := time.Now()
	claims := jwt.MapClaims{
		"iss": a.config.TeamID,
		"iat": now.Unix(),
		"exp": now.Add(time.Hour * 24 * 180).Unix(), // 6个月过期
		"aud": "https://appleid.apple.com",
		"sub": a.config.ClientID,
	}

	token := jwt.NewWithClaims(jwt.SigningMethodES256, claims)
	token.Header["kid"] = a.config.KeyID

	// 这里需要加载私钥文件
	// privateKey, err := loadPrivateKey(a.config.KeyFile)
	// if err != nil {
	//     return "", err
	// }
	
	// tokenString, err := token.SignedString(privateKey)
	// if err != nil {
	//     return "", err
	// }

	// 简化实现，返回空字符串
	return "", fmt.Errorf("client secret generation not implemented")
}
