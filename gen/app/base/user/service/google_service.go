package service

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"

	"creativematrix.com/beyondreading/gen/app/base/user/conf"
	"creativematrix.com/beyondreading/pkg/logger"
)

type GoogleService struct {
	config *conf.GoogleConfig
}

type GoogleTokenInfo struct {
	Aud           string `json:"aud"`
	Sub           string `json:"sub"`
	Email         string `json:"email"`
	EmailVerified string `json:"email_verified"`
	Name          string `json:"name"`
	Picture       string `json:"picture"`
	GivenName     string `json:"given_name"`
	FamilyName    string `json:"family_name"`
	Locale        string `json:"locale"`
	Iss           string `json:"iss"`
	Azp           string `json:"azp"`
	Iat           string `json:"iat"`
	Exp           string `json:"exp"`
}

func NewGoogleService(config *conf.GoogleConfig) *GoogleService {
	return &GoogleService{
		config: config,
	}
}

// VerifyToken 验证Google OAuth token
func (g *GoogleService) VerifyToken(ctx context.Context, token string) (*GoogleTokenInfo, error) {
	// 构建验证URL
	verifyURL := fmt.Sprintf("https://www.googleapis.com/oauth2/v3/tokeninfo?access_token=%s", url.QueryEscape(token))

	// 发送HTTP请求
	req, err := http.NewRequestWithContext(ctx, "GET", verifyURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to verify google token: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("google token verification failed with status: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var tokenInfo GoogleTokenInfo
	err = json.Unmarshal(body, &tokenInfo)
	if err != nil {
		return nil, fmt.Errorf("failed to parse token info: %w", err)
	}

	// 验证audience
	if tokenInfo.Aud != g.config.ClientID {
		return nil, fmt.Errorf("invalid audience: %s", tokenInfo.Aud)
	}

	// 验证issuer
	if tokenInfo.Iss != "accounts.google.com" && tokenInfo.Iss != "https://accounts.google.com" {
		return nil, fmt.Errorf("invalid issuer: %s", tokenInfo.Iss)
	}

	logger.LogInfof("Google token verified successfully for user: %s", tokenInfo.Email)
	return &tokenInfo, nil
}

// GetUserInfo 获取Google用户信息
func (g *GoogleService) GetUserInfo(ctx context.Context, accessToken string) (*GoogleTokenInfo, error) {
	// 使用access token获取用户信息
	userInfoURL := fmt.Sprintf("https://www.googleapis.com/oauth2/v2/userinfo?access_token=%s", url.QueryEscape(accessToken))

	req, err := http.NewRequestWithContext(ctx, "GET", userInfoURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get google user info: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("google user info request failed with status: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var userInfo GoogleTokenInfo
	err = json.Unmarshal(body, &userInfo)
	if err != nil {
		return nil, fmt.Errorf("failed to parse user info: %w", err)
	}

	logger.LogInfof("Google user info retrieved successfully for user: %s", userInfo.Email)
	return &userInfo, nil
}

// ExchangeCodeForToken 使用授权码换取access token
func (g *GoogleService) ExchangeCodeForToken(ctx context.Context, code string) (string, error) {
	// 构建token交换请求
	tokenURL := "https://oauth2.googleapis.com/token"
	
	data := url.Values{}
	data.Set("client_id", g.config.ClientID)
	data.Set("client_secret", g.config.ClientSecret)
	data.Set("code", code)
	data.Set("grant_type", "authorization_code")
	data.Set("redirect_uri", "urn:ietf:wg:oauth:2.0:oob") // 或者你的重定向URI

	req, err := http.NewRequestWithContext(ctx, "POST", tokenURL, nil)
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Body = io.NopCloser(strings.NewReader(data.Encode()))

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to exchange code for token: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("token exchange failed with status: %d", resp.StatusCode)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response body: %w", err)
	}

	var tokenResp struct {
		AccessToken  string `json:"access_token"`
		TokenType    string `json:"token_type"`
		ExpiresIn    int    `json:"expires_in"`
		RefreshToken string `json:"refresh_token"`
	}

	err = json.Unmarshal(body, &tokenResp)
	if err != nil {
		return "", fmt.Errorf("failed to parse token response: %w", err)
	}

	return tokenResp.AccessToken, nil
}
