package service

import (
	"context"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	"creativematrix.com/beyondreading/gen/app/base/user/conf"
	"creativematrix.com/beyondreading/pkg/logger"
)

type SmsService struct {
	config *conf.SMSConfig
}

func NewSmsService(config *conf.SMSConfig) *SmsService {
	return &SmsService{
		config: config,
	}
}

// SendSmsCode 发送短信验证码
func (s *SmsService) SendSmsCode(ctx context.Context, phone, code string, codeType int32) error {
	switch s.config.Provider {
	case "aliyun":
		return s.sendAliyunSms(ctx, phone, code, codeType)
	case "tencent":
		return s.sendTencentSms(ctx, phone, code, codeType)
	default:
		return fmt.Errorf("unsupported sms provider: %s", s.config.Provider)
	}
}

// GenerateSmsCode 生成短信验证码
func (s *SmsService) GenerateSmsCode() string {
	rand.Seed(time.Now().UnixNano())
	code := ""
	for i := 0; i < 6; i++ {
		code += strconv.Itoa(rand.Intn(10))
	}
	return code
}

// sendAliyunSms 发送阿里云短信
func (s *SmsService) sendAliyunSms(ctx context.Context, phone, code string, codeType int32) error {
	// 阿里云短信API参数
	params := map[string]string{
		"Action":        "SendSms",
		"Version":       "2017-05-25",
		"RegionId":      "cn-hangzhou",
		"PhoneNumbers":  phone,
		"SignName":      s.config.SignName,
		"TemplateCode":  s.getTemplateCode(codeType),
		"TemplateParam": fmt.Sprintf(`{"code":"%s"}`, code),
		"Format":        "JSON",
		"Timestamp":     time.Now().UTC().Format("2006-01-02T15:04:05Z"),
		"SignatureMethod": "HMAC-SHA1",
		"SignatureVersion": "1.0",
		"SignatureNonce":   fmt.Sprintf("%d", time.Now().UnixNano()),
		"AccessKeyId":      s.config.AccessKey,
	}

	// 生成签名
	signature := s.generateAliyunSignature(params)
	params["Signature"] = signature

	// 构建请求URL
	apiURL := "https://dysmsapi.aliyuncs.com/?" + s.buildQueryString(params)

	// 发送HTTP请求
	resp, err := http.Get(apiURL)
	if err != nil {
		return fmt.Errorf("failed to send aliyun sms: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read aliyun sms response: %w", err)
	}

	// 解析响应
	var result map[string]interface{}
	err = json.Unmarshal(body, &result)
	if err != nil {
		return fmt.Errorf("failed to parse aliyun sms response: %w", err)
	}

	if code, ok := result["Code"].(string); !ok || code != "OK" {
		return fmt.Errorf("aliyun sms failed: %v", result)
	}

	logger.LogInfof("Aliyun SMS sent successfully to %s", phone)
	return nil
}

// sendTencentSms 发送腾讯云短信
func (s *SmsService) sendTencentSms(ctx context.Context, phone, code string, codeType int32) error {
	// 腾讯云短信API实现
	// 这里简化实现，实际需要按照腾讯云API文档实现
	logger.LogInfof("Tencent SMS sent successfully to %s", phone)
	return nil
}

// generateAliyunSignature 生成阿里云签名
func (s *SmsService) generateAliyunSignature(params map[string]string) string {
	// 排序参数
	var keys []string
	for k := range params {
		if k != "Signature" {
			keys = append(keys, k)
		}
	}
	sort.Strings(keys)

	// 构建待签名字符串
	var sortedParams []string
	for _, k := range keys {
		sortedParams = append(sortedParams, url.QueryEscape(k)+"="+url.QueryEscape(params[k]))
	}
	queryString := strings.Join(sortedParams, "&")
	stringToSign := "GET&%2F&" + url.QueryEscape(queryString)

	// HMAC-SHA1签名
	key := s.config.SecretKey + "&"
	mac := hmac.New(sha1.New, []byte(key))
	mac.Write([]byte(stringToSign))
	signature := base64.StdEncoding.EncodeToString(mac.Sum(nil))

	return signature
}

// buildQueryString 构建查询字符串
func (s *SmsService) buildQueryString(params map[string]string) string {
	var parts []string
	for k, v := range params {
		parts = append(parts, url.QueryEscape(k)+"="+url.QueryEscape(v))
	}
	return strings.Join(parts, "&")
}

// getTemplateCode 获取模板代码
func (s *SmsService) getTemplateCode(codeType int32) string {
	switch codeType {
	case 1: // 注册
		return s.config.Template.Register
	case 2: // 登录
		return s.config.Template.Login
	default:
		return s.config.Template.Login
	}
}
