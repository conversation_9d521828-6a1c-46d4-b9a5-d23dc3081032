package main

import (
	"context"
	"flag"
	"net"
	"os"
	"os/signal"
	"syscall"

	"creativematrix.com/beyondreading/gen/app/base/user/conf"
	"creativematrix.com/beyondreading/gen/app/base/user/svc"
	pb "creativematrix.com/beyondreading/gen/proto/user"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/tracer"
	"google.golang.org/grpc"
)

const App = "base-user"

func main() {
	flag.Parse()

	// 加载配置
	config := conf.Load(App)

	// 初始化日志
	logger.InitLog()

	// 初始化链路追踪
	tracer.InitTracing()

	// 初始化服务
	service := svc.Load(config)

	// 启动gRPC服务
	lis, err := net.Listen("tcp", config.GRPC.Addr)
	if err != nil {
		panic(err)
	}

	s := grpc.NewServer()
	pb.RegisterUserServiceServer(s, service)

	go func() {
		logger.LogInfof("User gRPC server listening on %s", config.GRPC.Addr)
		if err := s.Serve(lis); err != nil {
			panic(err)
		}
	}()

	// 等待退出信号
	c := make(chan os.Signal, 1)
	signal.Notify(c, syscall.SIGHUP, syscall.SIGQUIT, syscall.SIGTERM, syscall.SIGINT)
	for {
		sig := <-c
		logger.LogInfof("get a signal %s", sig.String())
		switch sig {
		case syscall.SIGQUIT, syscall.SIGTERM, syscall.SIGINT:
			s.GracefulStop()
			service.Close()
			logger.LogInfof("user base exit")
			return
		case syscall.SIGHUP:
		default:
			return
		}
	}
}
