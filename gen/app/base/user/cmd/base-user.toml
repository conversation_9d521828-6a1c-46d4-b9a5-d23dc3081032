# User Base服务配置文件

[mysql]
dsn = "root:password@tcp(localhost:3306)/beyondreading?charset=utf8mb4&parseTime=True&loc=Local"
max_open_conns = 100
max_idle_conns = 10
conn_max_lifetime = 3600

[redis_user]
addr = "localhost:6379"
password = ""
db = 1
pool_size = 10
min_idle_conns = 5

[grpc]
addr = ":9003"
timeout = 30

[sms]
provider = "aliyun"
access_key = "your_aliyun_access_key"
secret_key = "your_aliyun_secret_key"
sign_name = "BeyondReading"

[sms.template]
register = "SMS_123456789"
login = "SMS_987654321"

[jwt]
secret = "your_jwt_secret_key_here"
expire_time = 604800  # 7天
issuer = "beyondreading"

[google]
client_id = "your_google_client_id"
client_secret = "your_google_client_secret"

[apple]
team_id = "your_apple_team_id"
client_id = "your_apple_client_id"
key_id = "your_apple_key_id"
key_file = "path/to/apple/private/key.p8"

[log]
level = "info"
file = "logs/base-user.log"
max_size = 100
max_age = 7
max_backups = 10
compress = true

[tracer]
endpoint = "http://localhost:14268/api/traces"
service_name = "base-user"
sample_rate = 1.0
