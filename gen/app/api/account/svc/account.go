package svc

import (
	"context"
	"creativematrix.com/beyondreading/pkg/ecode"
	"time"

	"creativematrix.com/beyondreading/gen/app/api/account/model/vo"
	pb "creativematrix.com/beyondreading/gen/proto/account"
	"creativematrix.com/beyondreading/pkg/logger"
)

// GetAccount 获取账户信息
func (s *AccountSvc) GetAccount(ctx context.Context, req *vo.GetAccountReq) (*vo.AccountInfoResp, error) {
	pbReq := &pb.GetAccountReq{
		UserId: req.UserId,
	}

	resp, err := s.dao.AccountClient.GetAccount(ctx, pbReq)
	if err != nil {
		logger.LogErrorf("Failed to get account: %v", err)
		return nil, err
	}

	if resp.Code != 200 {
		return nil, ecode.New(int(resp.Code), resp.Message)
	}

	account := resp.Account
	return &vo.AccountInfoResp{
		AccountId:      account.AccountId,
		UserId:         account.UserId,
		CoinBalance:    account.CoinBalance,
		TotalRecharged: account.TotalRecharged,
		TotalConsumed:  account.TotalConsumed,
		Status:         int(account.Status),
		CreatedAt:      time.Unix(account.CreatedAt, 0),
		UpdatedAt:      time.Unix(account.UpdatedAt, 0),
	}, nil
}

// Recharge 充值
func (s *AccountSvc) Recharge(ctx context.Context, req *vo.RechargeReq) (*vo.RechargeResp, error) {
	pbReq := &pb.RechargeReq{
		UserId:        req.UserId,
		Amount:        req.Amount,
		PaymentMethod: req.PaymentMethod,
		ExchangeRate:  req.ExchangeRate,
	}

	resp, err := s.dao.AccountClient.Recharge(ctx, pbReq)
	if err != nil {
		logger.LogErrorf("Failed to recharge: %v", err)
		return nil, err
	}

	if resp.Code != 200 {
		return nil, ecode.New(int(resp.Code), resp.Message)
	}

	order := resp.Order
	return &vo.RechargeResp{
		OrderId:       order.OrderId,
		AccountId:     order.AccountId,
		UserId:        order.UserId,
		Amount:        order.Amount,
		CoinAmount:    order.CoinAmount,
		ExchangeRate:  order.ExchangeRate,
		PaymentMethod: order.PaymentMethod,
		Status:        int(order.Status),
		CreatedAt:     time.Unix(order.CreatedAt, 0),
	}, nil
}

// PurchaseChapter 购买章节
func (s *AccountSvc) PurchaseChapter(ctx context.Context, req *vo.PurchaseChapterReq) (*vo.PurchaseOrderResp, error) {
	pbReq := &pb.PurchaseChapterReq{
		UserId:       req.UserId,
		BookId:       req.BookId,
		BookName:     req.BookName,
		ChapterId:    req.ChapterId,
		ChapterTitle: req.ChapterTitle,
		ChapterOrder: req.ChapterOrder,
		CoinAmount:   req.CoinAmount,
	}

	resp, err := s.dao.AccountClient.PurchaseChapter(ctx, pbReq)
	if err != nil {
		logger.LogErrorf("Failed to purchase chapter: %v", err)
		return nil, err
	}

	if resp.Code != 200 {
		return nil, ecode.New(int(resp.Code), resp.Message)
	}

	return s.convertPurchaseOrderToVO(resp.Order), nil
}

// PurchaseMonthly 购买包月
func (s *AccountSvc) PurchaseMonthly(ctx context.Context, req *vo.PurchaseMonthlyReq) (*vo.PurchaseOrderResp, error) {
	pbReq := &pb.PurchaseMonthlyReq{
		UserId:       req.UserId,
		BookId:       req.BookId,
		BookName:     req.BookName,
		CoinAmount:   req.CoinAmount,
		DurationDays: req.DurationDays,
	}

	resp, err := s.dao.AccountClient.PurchaseMonthly(ctx, pbReq)
	if err != nil {
		logger.LogErrorf("Failed to purchase monthly: %v", err)
		return nil, err
	}

	if resp.Code != 200 {
		return nil, ecode.New(int(resp.Code), resp.Message)
	}

	return s.convertPurchaseOrderToVO(resp.Order), nil
}

// PurchaseVip 购买VIP
func (s *AccountSvc) PurchaseVip(ctx context.Context, req *vo.PurchaseVipReq) (*vo.PurchaseOrderResp, error) {
	pbReq := &pb.PurchaseVipReq{
		UserId:       req.UserId,
		CoinAmount:   req.CoinAmount,
		DurationDays: req.DurationDays,
	}

	resp, err := s.dao.AccountClient.PurchaseVip(ctx, pbReq)
	if err != nil {
		logger.LogErrorf("Failed to purchase vip: %v", err)
		return nil, err
	}

	if resp.Code != 200 {
		return nil, ecode.New(int(resp.Code), resp.Message)
	}

	return s.convertPurchaseOrderToVO(resp.Order), nil
}

// GetAccountLogs 获取账户日志
func (s *AccountSvc) GetAccountLogs(ctx context.Context, req *vo.GetAccountLogsReq) (*vo.GetAccountLogsResp, error) {
	pbReq := &pb.GetAccountLogsReq{
		UserId:          req.UserId,
		Page:            req.Page,
		PageSize:        req.PageSize,
		TransactionType: req.TransactionType,
	}

	resp, err := s.dao.AccountClient.GetAccountLogs(ctx, pbReq)
	if err != nil {
		logger.LogErrorf("Failed to get account logs: %v", err)
		return nil, err
	}

	if resp.Code != 200 {
		return nil, ecode.New(int(resp.Code), resp.Message)
	}

	logs := make([]*vo.AccountLogResp, 0, len(resp.Logs))
	for _, log := range resp.Logs {
		logs = append(logs, &vo.AccountLogResp{
			LogId:           log.LogId,
			AccountId:       log.AccountId,
			UserId:          log.UserId,
			TransactionType: log.TransactionType,
			Amount:          log.Amount,
			BalanceBefore:   log.BalanceBefore,
			BalanceAfter:    log.BalanceAfter,
			OrderId:         log.OrderId,
			BookId:          log.BookId,
			ChapterId:       log.ChapterId,
			Description:     log.Description,
			ExtraData:       log.ExtraData,
			CreatedAt:       time.Unix(log.CreatedAt, 0),
		})
	}

	return &vo.GetAccountLogsResp{
		Logs:  logs,
		Total: resp.Total,
	}, nil
}

// GetPurchaseOrders 获取购买订单
func (s *AccountSvc) GetPurchaseOrders(ctx context.Context, req *vo.GetPurchaseOrdersReq) (*vo.GetPurchaseOrdersResp, error) {
	pbReq := &pb.GetPurchaseOrdersReq{
		UserId:    req.UserId,
		Page:      req.Page,
		PageSize:  req.PageSize,
		OrderType: req.OrderType,
	}

	resp, err := s.dao.AccountClient.GetPurchaseOrders(ctx, pbReq)
	if err != nil {
		logger.LogErrorf("Failed to get purchase orders: %v", err)
		return nil, err
	}

	if resp.Code != 200 {
		return nil, ecode.New(int(resp.Code), resp.Message)
	}

	orders := make([]*vo.PurchaseOrderResp, 0, len(resp.Orders))
	for _, order := range resp.Orders {
		orders = append(orders, s.convertPurchaseOrderToVO(order))
	}

	return &vo.GetPurchaseOrdersResp{
		Orders: orders,
		Total:  resp.Total,
	}, nil
}

// CheckChapterPurchased 检查章节购买状态
func (s *AccountSvc) CheckChapterPurchased(ctx context.Context, req *vo.CheckChapterPurchasedReq) (*vo.CheckChapterPurchasedResp, error) {
	pbReq := &pb.CheckChapterPurchasedReq{
		UserId:    req.UserId,
		BookId:    req.BookId,
		ChapterId: req.ChapterId,
	}

	resp, err := s.dao.AccountClient.CheckChapterPurchased(ctx, pbReq)
	if err != nil {
		logger.LogErrorf("Failed to check chapter purchased: %v", err)
		return nil, err
	}

	if resp.Code != 200 {
		return nil, ecode.New(int(resp.Code), resp.Message)
	}

	result := &vo.CheckChapterPurchasedResp{
		IsPurchased: resp.IsPurchased,
	}

	if resp.PurchasedAt > 0 {
		purchasedAt := time.Unix(resp.PurchasedAt, 0)
		result.PurchasedAt = &purchasedAt
	}

	return result, nil
}

// CheckMonthlyStatus 检查包月状态
func (s *AccountSvc) CheckMonthlyStatus(ctx context.Context, req *vo.CheckMonthlyStatusReq) (*vo.CheckMonthlyStatusResp, error) {
	pbReq := &pb.CheckMonthlyStatusReq{
		UserId: req.UserId,
		BookId: req.BookId,
	}

	resp, err := s.dao.AccountClient.CheckMonthlyStatus(ctx, pbReq)
	if err != nil {
		logger.LogErrorf("Failed to check monthly status: %v", err)
		return nil, err
	}

	if resp.Code != 200 {
		return nil, ecode.New(int(resp.Code), resp.Message)
	}

	result := &vo.CheckMonthlyStatusResp{
		IsActive: resp.IsActive,
	}

	if resp.StartTime > 0 {
		startTime := time.Unix(resp.StartTime, 0)
		result.StartTime = &startTime
	}

	if resp.EndTime > 0 {
		endTime := time.Unix(resp.EndTime, 0)
		result.EndTime = &endTime
	}

	return result, nil
}

// 转换购买订单到VO
func (s *AccountSvc) convertPurchaseOrderToVO(order *pb.PurchaseOrder) *vo.PurchaseOrderResp {
	result := &vo.PurchaseOrderResp{
		OrderId:    order.OrderId,
		AccountId:  order.AccountId,
		UserId:     order.UserId,
		OrderType:  order.OrderType,
		CoinAmount: order.CoinAmount,
		Status:     int(order.Status),
		CreatedAt:  time.Unix(order.CreatedAt, 0),
	}

	if order.BookId != "" {
		result.BookId = order.BookId
	}
	if order.BookName != "" {
		result.BookName = order.BookName
	}
	if order.ChapterId != "" {
		result.ChapterId = order.ChapterId
	}
	if order.ChapterTitle != "" {
		result.ChapterTitle = order.ChapterTitle
	}
	if order.ChapterOrder > 0 {
		result.ChapterOrder = order.ChapterOrder
	}
	if order.DurationDays > 0 {
		result.DurationDays = int(order.DurationDays)
	}
	if order.StartTime > 0 {
		startTime := time.Unix(order.StartTime, 0)
		result.StartTime = &startTime
	}
	if order.EndTime > 0 {
		endTime := time.Unix(order.EndTime, 0)
		result.EndTime = &endTime
	}

	return result
}
