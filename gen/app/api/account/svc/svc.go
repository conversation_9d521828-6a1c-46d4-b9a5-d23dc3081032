package svc

import (
	"context"
	"time"

	"creativematrix.com/beyondreading/gen/app/api/account/conf"
	"creativematrix.com/beyondreading/gen/app/api/account/dao"
	"creativematrix.com/beyondreading/gen/app/api/account/model/vo"
	accountpb "creativematrix.com/beyondreading/gen/proto/account"
)

// Service Account API服务
type Service struct {
	dao *dao.Dao
}

// New 创建Account API服务实例
func New(dao *dao.Dao) *Service {
	return &Service{
		dao: dao,
	}
}

// Load 根据配置加载服务实例
func Load(c *conf.Config) *Service {
	return New(dao.Load(c))
}

func (s *Service) Ping(ctx context.Context) (err error) {
	return s.dao.Ping(ctx)
}

func (s *Service) Close() {
	s.dao.Close()
}

// GetAccount 获取账户信息
func (s *Service) GetAccount(ctx context.Context, userId uint64) (*vo.GetAccountResp, error) {
	req := &accountpb.GetAccountReq{
		UserId: userId,
	}

	resp, err := s.dao.AccountClient.GetAccount(ctx, req)
	if err != nil {
		return nil, err
	}

	return &vo.GetAccountResp{
		Account: s.convertAccountToPB(resp.Account),
	}, nil
}

// CreateAccount 创建账户
func (s *Service) CreateAccount(ctx context.Context, userId uint64) (*vo.CreateAccountResp, error) {
	req := &accountpb.CreateAccountReq{
		UserId: userId,
	}

	resp, err := s.dao.AccountClient.CreateAccount(ctx, req)
	if err != nil {
		return nil, err
	}

	return &vo.CreateAccountResp{
		Account: s.convertAccountToPB(resp.Account),
	}, nil
}

// Recharge 充值
func (s *Service) Recharge(ctx context.Context, req *vo.RechargeReq) (*vo.RechargeResp, error) {
	pbReq := &accountpb.RechargeReq{
		UserId:      req.UserId,
		Amount:      req.Amount,
		PaymentType: req.PaymentType,
		Description: req.Description,
	}

	resp, err := s.dao.AccountClient.Recharge(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	return &vo.RechargeResp{
		Account: s.convertAccountToPB(resp.Account),
		LogId:   resp.LogId,
	}, nil
}

// GetAccountLogs 获取账户日志
func (s *Service) GetAccountLogs(ctx context.Context, req *vo.GetAccountLogsReq) (*vo.GetAccountLogsResp, error) {
	pbReq := &accountpb.GetAccountLogsReq{
		UserId:          req.UserId,
		Page:            req.Page,
		PageSize:        req.PageSize,
		TransactionType: req.TransactionType,
	}

	resp, err := s.dao.AccountClient.GetAccountLogs(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	logs := make([]*vo.AccountLogInfo, 0, len(resp.Logs))
	for _, log := range resp.Logs {
		logs = append(logs, s.convertAccountLogToPB(log))
	}

	return &vo.GetAccountLogsResp{
		Logs:  logs,
		Total: resp.Total,
	}, nil
}

// UpdateUserStatus 更新用户状态
func (s *Service) UpdateUserStatus(ctx context.Context, req *vo.UpdateUserStatusReq) (*vo.UpdateUserStatusResp, error) {
	pbReq := &accountpb.UpdateUserStatusReq{
		UserId:            req.UserId,
		UserType:          req.UserType,
		VipExpireTime:     req.VipExpireTime,
		MonthlyExpireTime: req.MonthlyExpireTime,
	}

	resp, err := s.dao.AccountClient.UpdateUserStatus(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	return &vo.UpdateUserStatusResp{
		Account: s.convertAccountToPB(resp.Account),
	}, nil
}

// DeductCoins 扣除书币
func (s *Service) DeductCoins(ctx context.Context, req *vo.DeductCoinsReq) (*vo.DeductCoinsResp, error) {
	pbReq := &accountpb.DeductCoinsReq{
		UserId:          req.UserId,
		Amount:          req.Amount,
		OrderId:         req.OrderId,
		BookId:          req.BookId,
		ChapterId:       req.ChapterId,
		TransactionType: req.TransactionType,
		Description:     req.Description,
	}

	resp, err := s.dao.AccountClient.DeductCoins(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	return &vo.DeductCoinsResp{
		Account: s.convertAccountToPB(resp.Account),
		LogId:   resp.LogId,
	}, nil
}

// CheckUserStatus 检查用户状态
func (s *Service) CheckUserStatus(ctx context.Context, userId uint64) (*vo.CheckUserStatusResp, error) {
	req := &accountpb.CheckUserStatusReq{
		UserId: userId,
	}

	resp, err := s.dao.AccountClient.CheckUserStatus(ctx, req)
	if err != nil {
		return nil, err
	}

	return &vo.CheckUserStatusResp{
		Account:    s.convertAccountToPB(resp.Account),
		HasVip:     resp.HasVip,
		HasMonthly: resp.HasMonthly,
	}, nil
}

// convertAccountToPB 转换账户信息到VO
func (s *Service) convertAccountToPB(account *accountpb.AccountInfo) *vo.AccountInfo {
	if account == nil {
		return nil
	}

	var vipExpireTime, monthlyExpireTime *time.Time
	if account.VipExpireTime > 0 {
		t := time.Unix(account.VipExpireTime, 0)
		vipExpireTime = &t
	}
	if account.MonthlyExpireTime > 0 {
		t := time.Unix(account.MonthlyExpireTime, 0)
		monthlyExpireTime = &t
	}

	return &vo.AccountInfo{
		AccountId:         account.AccountId,
		UserId:            account.UserId,
		CoinBalance:       account.CoinBalance,
		TotalRecharged:    account.TotalRecharged,
		TotalConsumed:     account.TotalConsumed,
		Status:            account.Status,
		UserType:          account.UserType,
		UserLevel:         account.UserLevel,
		VipExpireTime:     vipExpireTime,
		MonthlyExpireTime: monthlyExpireTime,
		CreatedAt:         time.Unix(account.CreatedAt, 0),
		UpdatedAt:         time.Unix(account.UpdatedAt, 0),
	}
}

// convertAccountLogToPB 转换账户日志到VO
func (s *Service) convertAccountLogToPB(log *accountpb.AccountLog) *vo.AccountLogInfo {
	if log == nil {
		return nil
	}

	return &vo.AccountLogInfo{
		LogId:           log.LogId,
		AccountId:       log.AccountId,
		UserId:          log.UserId,
		TransactionType: log.TransactionType,
		Amount:          log.Amount,
		BalanceBefore:   log.BalanceBefore,
		BalanceAfter:    log.BalanceAfter,
		OrderId:         log.OrderId,
		BookId:          log.BookId,
		ChapterId:       log.ChapterId,
		Description:     log.Description,
		ExtraData:       log.ExtraData,
		CreatedAt:       time.Unix(log.CreatedAt, 0),
	}
}
