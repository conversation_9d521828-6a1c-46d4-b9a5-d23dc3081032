package http

import (
	"errors"
	"strconv"

	"creativematrix.com/beyondreading/gen/app/api/account/model/vo"
	"creativematrix.com/beyondreading/pkg/ecode"
	"github.com/gin-gonic/gin"
)

func getAccount(c *gin.Context) {
	param := new(vo.GetAccountReq)
	err := c.BindQuery(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	if param.UserId == 0 {
		ecode.Back(c).Failure(errors.New("invalid user id"))
		return
	}

	data, err := service.GetAccount(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

func createAccount(c *gin.Context) {
	param := new(vo.CreateAccountReq)
	err := c.BindJSON(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	if param.UserId == 0 {
		ecode.Back(c).Failure(errors.New("invalid user id"))
		return
	}

	data, err := service.CreateAccount(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

func recharge(c *gin.Context) {
	param := new(vo.RechargeReq)
	err := c.BindJSON(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	if param.UserId == 0 || param.Amount <= 0 || param.PaymentMethod == "" {
		ecode.Back(c).Failure(errors.New("invalid parameter"))
		return
	}

	// 设置默认兑换比例
	if param.ExchangeRate == 0 {
		param.ExchangeRate = 1.0 // 默认1:1兑换
	}

	data, err := service.Recharge(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

func getAccountLogs(c *gin.Context) {
	param := new(vo.GetAccountLogsReq)
	err := c.BindQuery(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	if param.UserId == 0 {
		ecode.Back(c).Failure(errors.New("invalid user id"))
		return
	}

	// 设置默认分页参数
	if param.Page <= 0 {
		param.Page = 1
	}
	if param.PageSize <= 0 {
		param.PageSize = 20
	}

	data, err := service.GetAccountLogs(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

func updateUserStatus(c *gin.Context) {
	param := new(vo.UpdateUserStatusReq)
	err := c.BindJSON(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	if param.UserId == 0 {
		ecode.Back(c).Failure(errors.New("invalid user id"))
		return
	}

	data, err := service.UpdateUserStatus(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

func checkUserStatus(c *gin.Context) {
	userIdStr := c.Query("user_id")
	if userIdStr == "" {
		ecode.Back(c).Failure(errors.New("invalid user id"))
		return
	}

	userId, err := strconv.ParseUint(userIdStr, 10, 64)
	if err != nil {
		ecode.Back(c).Failure(errors.New("invalid user id"))
		return
	}

	param := &vo.CheckUserStatusReq{
		UserId: userId,
	}

	data, err := service.CheckUserStatus(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

func deductCoins(c *gin.Context) {
	param := new(vo.DeductCoinsReq)
	err := c.BindJSON(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	if param.UserId == 0 || param.Amount <= 0 {
		ecode.Back(c).Failure(errors.New("invalid parameter"))
		return
	}

	data, err := service.DeductCoins(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}
