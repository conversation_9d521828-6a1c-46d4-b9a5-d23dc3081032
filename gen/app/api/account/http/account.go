package http

import (
	"creativematrix.com/beyondreading/gen/app/api/account/model/vo"
	"creativematrix.com/beyondreading/pkg/ecode"
	"github.com/gin-gonic/gin"
)

// getAccount 获取账户信息
func getAccount(c *gin.Context) {
	param := new(vo.GetAccountReq)
	err := c.BindQuery(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	data, err := service.GetAccount(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

// recharge 充值
func recharge(c *gin.Context) {
	param := new(vo.RechargeReq)
	err := c.BindJSON(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	data, err := service.Recharge(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

// purchaseChapter 购买章节
func purchaseChapter(c *gin.Context) {
	param := new(vo.PurchaseChapterReq)
	err := c.BindJSON(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	data, err := service.PurchaseChapter(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

// purchaseMonthly 购买包月
func purchaseMonthly(c *gin.Context) {
	param := new(vo.PurchaseMonthlyReq)
	err := c.BindJSON(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	data, err := service.PurchaseMonthly(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

// purchaseVip 购买VIP
func purchaseVip(c *gin.Context) {
	param := new(vo.PurchaseVipReq)
	err := c.BindJSON(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	data, err := service.PurchaseVip(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

// getAccountLogs 获取账户日志
func getAccountLogs(c *gin.Context) {
	param := new(vo.GetAccountLogsReq)
	err := c.BindQuery(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	data, err := service.GetAccountLogs(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

// getPurchaseOrders 获取购买订单
func getPurchaseOrders(c *gin.Context) {
	param := new(vo.GetPurchaseOrdersReq)
	err := c.BindQuery(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	data, err := service.GetPurchaseOrders(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

// checkChapterPurchased 检查章节购买状态
func checkChapterPurchased(c *gin.Context) {
	param := new(vo.CheckChapterPurchasedReq)
	err := c.BindQuery(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	data, err := service.CheckChapterPurchased(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

// checkMonthlyStatus 检查包月状态
func checkMonthlyStatus(c *gin.Context) {
	param := new(vo.CheckMonthlyStatusReq)
	err := c.BindQuery(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	data, err := service.CheckMonthlyStatus(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}
