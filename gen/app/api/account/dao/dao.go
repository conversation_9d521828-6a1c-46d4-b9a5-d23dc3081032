package dao

import (
	"context"
	"creativematrix.com/beyondreading/gen/app/api/account/conf"
	accountapi "creativematrix.com/beyondreading/gen/app/base/account/api"
	accountpb "creativematrix.com/beyondreading/gen/proto/account"
)

// Dao 数据访问层
type Dao struct {
	conf          *conf.Config
	AccountClient accountpb.AccountClient
}

// Load 创建DAO实例
func Load(c *conf.Config) *Dao {
	accountClient := accountapi.NewClient(c.Base)

	return &Dao{
		conf:          c,
		AccountClient: accountClient,
	}
}

func (d *Dao) Ping(ctx context.Context) (err error) {
	return
}

func (d *Dao) Close() {
	//d.es.Close()
}
