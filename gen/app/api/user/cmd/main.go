package main

import (
	"context"
	"flag"
	"os"
	"os/signal"
	"syscall"

	"creativematrix.com/beyondreading/gen/app/api/user/conf"
	"creativematrix.com/beyondreading/gen/app/api/user/http"
	"creativematrix.com/beyondreading/gen/app/api/user/svc"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/tracer"
)

const App = "api-user"

func main() {
	flag.Parse()

	// 加载配置
	config := conf.Load(App)

	// 初始化日志
	logger.InitLog()

	// 初始化链路追踪
	tracer.InitTracing()

	// 初始化服务
	service := svc.Load(config)

	// 启动HTTP服务
	http.Start(config, service)

	// 等待退出信号
	c := make(chan os.Signal, 1)
	signal.Notify(c, syscall.SIGHUP, syscall.SIGQUIT, syscall.SIGTERM, syscall.SIGINT)
	for {
		s := <-c
		logger.LogInfof("get a signal %s", s.String())
		switch s {
		case syscall.SIGQUIT, syscall.SIGTERM, syscall.SIGINT:
			service.Close()
			logger.LogInfof("user api exit")
			return
		case syscall.SIGHUP:
		default:
			return
		}
	}
}
