package svc

import (
	"time"

	"creativematrix.com/beyondreading/gen/app/api/user/model/vo"
	userpb "creativematrix.com/beyondreading/gen/proto/user"
)

// convertUserFromPB 转换用户信息从PB到VO
func (s *UserSvc) convertUserFromPB(pbUser *userpb.UserInfo) *vo.UserInfo {
	if pbUser == nil {
		return nil
	}

	user := &vo.UserInfo{
		UserId:      pbUser.UserId,
		Phone:       pbUser.Phone,
		Email:       pbUser.Email,
		Nickname:    pbUser.Nickname,
		Avatar:      pbUser.Avatar,
		Gender:      pbUser.Gender,
		Birthday:    pbUser.Birthday,
		Location:    pbUser.Location,
		Status:      pbUser.Status,
		LoginType:   pbUser.LoginType,
		GoogleId:    pbUser.GoogleId,
		AppleId:     pbUser.AppleId,
		LastLoginIp: pbUser.LastLoginIp,
		CreatedAt:   time.Unix(pbUser.CreatedAt, 0),
		UpdatedAt:   time.Unix(pbUser.UpdatedAt, 0),
	}

	if pbUser.LastLoginAt > 0 {
		lastLoginAt := time.Unix(pbUser.LastLoginAt, 0)
		user.LastLoginAt = &lastLoginAt
	}

	return user
}

// convertLoginLogFromPB 转换登录日志从PB到VO
func (s *UserSvc) convertLoginLogFromPB(pbLog *userpb.LoginLog) *vo.LoginLogInfo {
	if pbLog == nil {
		return nil
	}

	log := &vo.LoginLogInfo{
		LogId:       pbLog.LogId,
		UserId:      pbLog.UserId,
		LoginType:   pbLog.LoginType,
		LoginIp:     pbLog.LoginIp,
		UserAgent:   pbLog.UserAgent,
		DeviceId:    pbLog.DeviceId,
		LoginResult: pbLog.LoginResult,
		FailReason:  pbLog.FailReason,
		CreatedAt:   time.Unix(pbLog.CreatedAt, 0),
	}

	return log
}
