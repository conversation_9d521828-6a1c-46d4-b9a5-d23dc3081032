package svc

import (
	"context"

	"creativematrix.com/beyondreading/gen/app/api/user/model/vo"
	userpb "creativematrix.com/beyondreading/gen/proto/user"
)

// RegisterBySms 手机短信注册
func (s *UserSvc) RegisterBySms(ctx context.Context, req *vo.RegisterBySmsReq) (*vo.RegisterBySmsResp, error) {
	pbReq := &userpb.RegisterBySmsReq{
		Phone:     req.Phone,
		SmsCode:   req.SmsCode,
		Nickname:  req.Nickname,
		ClientIp:  req.ClientIp,
		UserAgent: req.UserAgent,
		DeviceId:  req.DeviceId,
	}

	pbResp, err := s.dao.userClient.RegisterBySms(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	resp := &vo.RegisterBySmsResp{
		User:  s.convertUserFromPB(pbResp.User),
		Token: pbResp.Token,
	}

	return resp, nil
}

// RegisterByGoogle Google账户注册
func (s *UserSvc) RegisterByGoogle(ctx context.Context, req *vo.RegisterByGoogleReq) (*vo.RegisterByGoogleResp, error) {
	pbReq := &userpb.RegisterByGoogleReq{
		GoogleToken: req.GoogleToken,
		GoogleId:    req.GoogleId,
		Email:       req.Email,
		Nickname:    req.Nickname,
		Avatar:      req.Avatar,
		ClientIp:    req.ClientIp,
		UserAgent:   req.UserAgent,
		DeviceId:    req.DeviceId,
	}

	pbResp, err := s.dao.userClient.RegisterByGoogle(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	resp := &vo.RegisterByGoogleResp{
		User:  s.convertUserFromPB(pbResp.User),
		Token: pbResp.Token,
	}

	return resp, nil
}

// RegisterByApple Apple账户注册
func (s *UserSvc) RegisterByApple(ctx context.Context, req *vo.RegisterByAppleReq) (*vo.RegisterByAppleResp, error) {
	pbReq := &userpb.RegisterByAppleReq{
		AppleToken: req.AppleToken,
		AppleId:    req.AppleId,
		Email:      req.Email,
		Nickname:   req.Nickname,
		ClientIp:   req.ClientIp,
		UserAgent:  req.UserAgent,
		DeviceId:   req.DeviceId,
	}

	pbResp, err := s.dao.userClient.RegisterByApple(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	resp := &vo.RegisterByAppleResp{
		User:  s.convertUserFromPB(pbResp.User),
		Token: pbResp.Token,
	}

	return resp, nil
}

// LoginBySms 手机短信登录
func (s *UserSvc) LoginBySms(ctx context.Context, req *vo.LoginBySmsReq) (*vo.LoginBySmsResp, error) {
	pbReq := &userpb.LoginBySmsReq{
		Phone:     req.Phone,
		SmsCode:   req.SmsCode,
		ClientIp:  req.ClientIp,
		UserAgent: req.UserAgent,
		DeviceId:  req.DeviceId,
	}

	pbResp, err := s.dao.userClient.LoginBySms(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	resp := &vo.LoginBySmsResp{
		User:  s.convertUserFromPB(pbResp.User),
		Token: pbResp.Token,
	}

	return resp, nil
}

// LoginByGoogle Google账户登录
func (s *UserSvc) LoginByGoogle(ctx context.Context, req *vo.LoginByGoogleReq) (*vo.LoginByGoogleResp, error) {
	pbReq := &userpb.LoginByGoogleReq{
		GoogleToken: req.GoogleToken,
		GoogleId:    req.GoogleId,
		ClientIp:    req.ClientIp,
		UserAgent:   req.UserAgent,
		DeviceId:    req.DeviceId,
	}

	pbResp, err := s.dao.userClient.LoginByGoogle(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	resp := &vo.LoginByGoogleResp{
		User:  s.convertUserFromPB(pbResp.User),
		Token: pbResp.Token,
	}

	return resp, nil
}

// LoginByApple Apple账户登录
func (s *UserSvc) LoginByApple(ctx context.Context, req *vo.LoginByAppleReq) (*vo.LoginByAppleResp, error) {
	pbReq := &userpb.LoginByAppleReq{
		AppleToken: req.AppleToken,
		AppleId:    req.AppleId,
		ClientIp:   req.ClientIp,
		UserAgent:  req.UserAgent,
		DeviceId:   req.DeviceId,
	}

	pbResp, err := s.dao.userClient.LoginByApple(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	resp := &vo.LoginByAppleResp{
		User:  s.convertUserFromPB(pbResp.User),
		Token: pbResp.Token,
	}

	return resp, nil
}

// GetUserInfo 获取用户信息
func (s *UserSvc) GetUserInfo(ctx context.Context, req *vo.GetUserInfoReq) (*vo.GetUserInfoResp, error) {
	pbReq := &userpb.GetUserInfoReq{
		UserId: req.UserId,
	}

	pbResp, err := s.dao.userClient.GetUserInfo(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	resp := &vo.GetUserInfoResp{
		User: s.convertUserFromPB(pbResp.User),
	}

	return resp, nil
}

// UpdateUserInfo 更新用户信息
func (s *UserSvc) UpdateUserInfo(ctx context.Context, req *vo.UpdateUserInfoReq) (*vo.UpdateUserInfoResp, error) {
	pbReq := &userpb.UpdateUserInfoReq{
		UserId:   req.UserId,
		Nickname: req.Nickname,
		Avatar:   req.Avatar,
		Gender:   req.Gender,
		Birthday: req.Birthday,
		Location: req.Location,
	}

	pbResp, err := s.dao.userClient.UpdateUserInfo(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	resp := &vo.UpdateUserInfoResp{
		User: s.convertUserFromPB(pbResp.User),
	}

	return resp, nil
}

// GetLoginLogs 获取登录日志
func (s *UserSvc) GetLoginLogs(ctx context.Context, req *vo.GetLoginLogsReq) (*vo.GetLoginLogsResp, error) {
	pbReq := &userpb.GetLoginLogsReq{
		UserId:   req.UserId,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	pbResp, err := s.dao.userClient.GetLoginLogs(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	var logs []*vo.LoginLogInfo
	for _, log := range pbResp.Logs {
		logs = append(logs, s.convertLoginLogFromPB(log))
	}

	resp := &vo.GetLoginLogsResp{
		Logs:  logs,
		Total: pbResp.Total,
	}

	return resp, nil
}

// SendSmsCode 发送短信验证码
func (s *UserSvc) SendSmsCode(ctx context.Context, req *vo.SendSmsCodeReq) (*vo.SendSmsCodeResp, error) {
	pbReq := &userpb.SendSmsCodeReq{
		Phone:    req.Phone,
		CodeType: req.CodeType,
	}

	pbResp, err := s.dao.userClient.SendSmsCode(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	resp := &vo.SendSmsCodeResp{
		Message: pbResp.Message,
	}

	return resp, nil
}

// VerifySmsCode 验证短信验证码
func (s *UserSvc) VerifySmsCode(ctx context.Context, req *vo.VerifySmsCodeReq) (*vo.VerifySmsCodeResp, error) {
	pbReq := &userpb.VerifySmsCodeReq{
		Phone:    req.Phone,
		SmsCode:  req.SmsCode,
		CodeType: req.CodeType,
	}

	pbResp, err := s.dao.userClient.VerifySmsCode(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	resp := &vo.VerifySmsCodeResp{
		IsValid: pbResp.IsValid,
		Message: pbResp.Message,
	}

	return resp, nil
}
