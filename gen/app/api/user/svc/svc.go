package svc

import (
	"context"

	"creativematrix.com/beyondreading/gen/app/api/user/conf"
	"creativematrix.com/beyondreading/gen/app/api/user/dao"
)

type UserSvc struct {
	conf *conf.Config
	dao  *dao.Dao
}

func Load(c *conf.Config) *UserSvc {
	svc := &UserSvc{
		conf: c,
		dao:  dao.Load(c),
	}

	return svc
}

func (s *UserSvc) Ping(ctx context.Context) error {
	return s.dao.Ping(ctx)
}

func (s *UserSvc) Close() {
	s.dao.Close()
}
