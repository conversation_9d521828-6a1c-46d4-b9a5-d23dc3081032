package dao

import (
	"context"

	"creativematrix.com/beyondreading/gen/app/api/user/conf"
	userapi "creativematrix.com/beyondreading/gen/app/base/user/api"
)

type Dao struct {
	conf       *conf.Config
	userClient *userapi.Client
}

func Load(c *conf.Config) *Dao {
	return &Dao{
		conf:       c,
		userClient: userapi.NewClient(),
	}
}

func (d *Dao) Ping(ctx context.Context) error {
	return nil
}

func (d *Dao) Close() {
	// 关闭连接
}
