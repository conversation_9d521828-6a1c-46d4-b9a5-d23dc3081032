package http

import (
	"errors"
	"strconv"

	"creativematrix.com/beyondreading/gen/app/api/user/model/vo"
	"creativematrix.com/beyondreading/pkg/ecode"
	"github.com/gin-gonic/gin"
)

// registerBySms 手机短信注册
func (h *Handler) registerBySms(c *gin.Context) {
	param := new(vo.RegisterBySmsReq)
	err := c.BindJSON(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	if param.Phone == "" || param.SmsCode == "" {
		ecode.Back(c).Failure(errors.New("invalid parameter"))
		return
	}

	// 获取客户端IP和User-Agent
	param.ClientIp = c.ClientIP()
	param.UserAgent = c.GetHeader("User-Agent")

	data, err := h.svc.RegisterBySms(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

// registerByGoogle Google账户注册
func (h *Handler) registerByGoogle(c *gin.Context) {
	param := new(vo.RegisterByGoogleReq)
	err := c.BindJSON(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	if param.GoogleToken == "" || param.GoogleId == "" {
		ecode.Back(c).Failure(errors.New("invalid parameter"))
		return
	}

	// 获取客户端IP和User-Agent
	param.ClientIp = c.ClientIP()
	param.UserAgent = c.GetHeader("User-Agent")

	data, err := h.svc.RegisterByGoogle(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

// registerByApple Apple账户注册
func (h *Handler) registerByApple(c *gin.Context) {
	param := new(vo.RegisterByAppleReq)
	err := c.BindJSON(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	if param.AppleToken == "" || param.AppleId == "" {
		ecode.Back(c).Failure(errors.New("invalid parameter"))
		return
	}

	// 获取客户端IP和User-Agent
	param.ClientIp = c.ClientIP()
	param.UserAgent = c.GetHeader("User-Agent")

	data, err := h.svc.RegisterByApple(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

// loginBySms 手机短信登录
func (h *Handler) loginBySms(c *gin.Context) {
	param := new(vo.LoginBySmsReq)
	err := c.BindJSON(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	if param.Phone == "" || param.SmsCode == "" {
		ecode.Back(c).Failure(errors.New("invalid parameter"))
		return
	}

	// 获取客户端IP和User-Agent
	param.ClientIp = c.ClientIP()
	param.UserAgent = c.GetHeader("User-Agent")

	data, err := h.svc.LoginBySms(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

// loginByGoogle Google账户登录
func (h *Handler) loginByGoogle(c *gin.Context) {
	param := new(vo.LoginByGoogleReq)
	err := c.BindJSON(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	if param.GoogleToken == "" || param.GoogleId == "" {
		ecode.Back(c).Failure(errors.New("invalid parameter"))
		return
	}

	// 获取客户端IP和User-Agent
	param.ClientIp = c.ClientIP()
	param.UserAgent = c.GetHeader("User-Agent")

	data, err := h.svc.LoginByGoogle(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

// loginByApple Apple账户登录
func (h *Handler) loginByApple(c *gin.Context) {
	param := new(vo.LoginByAppleReq)
	err := c.BindJSON(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	if param.AppleToken == "" || param.AppleId == "" {
		ecode.Back(c).Failure(errors.New("invalid parameter"))
		return
	}

	// 获取客户端IP和User-Agent
	param.ClientIp = c.ClientIP()
	param.UserAgent = c.GetHeader("User-Agent")

	data, err := h.svc.LoginByApple(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

// getUserInfo 获取用户信息
func (h *Handler) getUserInfo(c *gin.Context) {
	userIdStr := c.Query("userId")
	if userIdStr == "" {
		ecode.Back(c).Failure(errors.New("invalid user id"))
		return
	}

	userId, err := strconv.ParseUint(userIdStr, 10, 64)
	if err != nil {
		ecode.Back(c).Failure(errors.New("invalid user id"))
		return
	}

	param := &vo.GetUserInfoReq{
		UserId: userId,
	}

	data, err := h.svc.GetUserInfo(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

// updateUserInfo 更新用户信息
func (h *Handler) updateUserInfo(c *gin.Context) {
	param := new(vo.UpdateUserInfoReq)
	err := c.BindJSON(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	if param.UserId == 0 {
		ecode.Back(c).Failure(errors.New("invalid user id"))
		return
	}

	data, err := h.svc.UpdateUserInfo(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

// getLoginLogs 获取登录日志
func (h *Handler) getLoginLogs(c *gin.Context) {
	param := new(vo.GetLoginLogsReq)
	err := c.BindQuery(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	if param.UserId == 0 {
		ecode.Back(c).Failure(errors.New("invalid user id"))
		return
	}

	// 设置默认分页参数
	if param.Page <= 0 {
		param.Page = 1
	}
	if param.PageSize <= 0 {
		param.PageSize = 20
	}

	data, err := h.svc.GetLoginLogs(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

// sendSmsCode 发送短信验证码
func (h *Handler) sendSmsCode(c *gin.Context) {
	param := new(vo.SendSmsCodeReq)
	err := c.BindJSON(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	if param.Phone == "" || param.CodeType <= 0 {
		ecode.Back(c).Failure(errors.New("invalid parameter"))
		return
	}

	data, err := h.svc.SendSmsCode(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

// verifySmsCode 验证短信验证码
func (h *Handler) verifySmsCode(c *gin.Context) {
	param := new(vo.VerifySmsCodeReq)
	err := c.BindJSON(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	if param.Phone == "" || param.SmsCode == "" || param.CodeType <= 0 {
		ecode.Back(c).Failure(errors.New("invalid parameter"))
		return
	}

	data, err := h.svc.VerifySmsCode(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}
