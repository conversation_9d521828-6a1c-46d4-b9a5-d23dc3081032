package http

import (
	"creativematrix.com/beyondreading/gen/app/api/user/svc"
	"github.com/gin-gonic/gin"
)

type Handler struct {
	svc *svc.UserSvc
}

// New 创建HTTP处理器
func New(svc *svc.UserSvc) *Handler {
	return &Handler{
		svc: svc,
	}
}

func (h *Handler) RegisterRoutes(r *gin.Engine) {
	v1 := r.Group("/api/v1/user")
	{
		// 注册相关
		v1.POST("/register/sms", h.RegisterBySms)
		v1.POST("/register/google", h.RegisterByGoogle)
		v1.POST("/register/apple", h.RegisterByApple)

		// 登录相关
		v1.POST("/login/sms", h.LoginBySms)
		v1.POST("/login/google", h.LoginByGoogle)
		v1.POST("/login/apple", h.LoginByApple)

		// 用户信息管理
		v1.GET("/info", h.GetUserInfo)
		v1.PUT("/info", h.UpdateUserInfo)

		// 登录日志
		v1.GET("/login-logs", h.GetLoginLogs)

		// 短信验证码
		v1.POST("/sms/send", h.SendSmsCode)
		v1.POST("/sms/verify", h.VerifySmsCode)
	}
}

//func Start(c *conf.Config, s *svc.UserSvc) {
//	service = s
//	router := bbrouter.Start(c.Base)
//
//	v1 := router.Group("/api/v1/user")
//	{
//		// 注册相关
//		v1.POST("/register/sms", registerBySms)
//		v1.POST("/register/google", registerByGoogle)
//		v1.POST("/register/apple", registerByApple)
//
//		// 登录相关
//		v1.POST("/login/sms", loginBySms)
//		v1.POST("/login/google", loginByGoogle)
//		v1.POST("/login/apple", loginByApple)
//
//		// 用户信息管理
//		v1.GET("/info", getUserInfo)
//		v1.PUT("/info", updateUserInfo)
//
//		// 登录日志
//		v1.GET("/login-logs", getLoginLogs)
//
//		// 短信验证码
//		v1.POST("/sms/send", sendSmsCode)
//		v1.POST("/sms/verify", verifySmsCode)
//	}
//
//	go func() {
//		if err := router.Run(c.Port.HTTP); err != nil {
//			panic(err)
//		}
//	}()
//}
