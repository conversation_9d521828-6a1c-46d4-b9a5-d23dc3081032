package http

import (
	"creativematrix.com/beyondreading/gen/app/api/user/conf"
	"creativematrix.com/beyondreading/gen/app/api/user/svc"
	bbrouter "creativematrix.com/beyondreading/pkg/router"
)

var (
	service *svc.UserSvc
)

func Start(c *conf.Config, s *svc.UserSvc) {
	service = s
	router := bbrouter.Start(c.Base)

	v1 := router.Group("/api/v1/user")
	{
		// 注册相关
		v1.POST("/register/sms", registerBySms)
		v1.POST("/register/google", registerByGoogle)
		v1.POST("/register/apple", registerByApple)

		// 登录相关
		v1.POST("/login/sms", loginBySms)
		v1.POST("/login/google", loginByGoogle)
		v1.POST("/login/apple", loginByApple)

		// 用户信息管理
		v1.GET("/info", getUserInfo)
		v1.PUT("/info", updateUserInfo)

		// 登录日志
		v1.GET("/login-logs", getLoginLogs)

		// 短信验证码
		v1.POST("/sms/send", sendSmsCode)
		v1.POST("/sms/verify", verifySmsCode)
	}

	go func() {
		if err := router.Run(c.Port.HTTP); err != nil {
			panic(err)
		}
	}()
}
