package dao

import (
	"context"
	"creativematrix.com/beyondreading/gen/app/api/purchase/conf"
	accountapi "creativematrix.com/beyondreading/gen/app/base/account/api"
	purchaseapi "creativematrix.com/beyondreading/gen/app/base/purchase/api"
	accountpb "creativematrix.com/beyondreading/gen/proto/account"
	pb "creativematrix.com/beyondreading/gen/proto/purchase"
)

// Dao 数据访问层
type Dao struct {
	conf           *conf.Config
	PurchaseClient pb.PurchaseClient
	AccountClient  accountpb.AccountClient
}

// Load 创建DAO实例
func Load(c *conf.Config) *Dao {

	purchaseClient := purchaseapi.NewClient(c.Base)
	accountClient := accountapi.NewClient(c.Base)

	return &Dao{
		conf:           c,
		PurchaseClient: purchaseClient,
		AccountClient:  accountClient,
	}
}

func (d *Dao) Ping(ctx context.Context) (err error) {
	return
}

func (d *Dao) Close() {
	//d.es.Close()
}
