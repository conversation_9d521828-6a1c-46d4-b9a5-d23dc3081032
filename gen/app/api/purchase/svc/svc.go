package svc

import (
	"context"
	"time"

	"creativematrix.com/beyondreading/gen/app/api/purchase/conf"
	"creativematrix.com/beyondreading/gen/app/api/purchase/dao"
	"creativematrix.com/beyondreading/gen/app/api/purchase/model/vo"
	purchasepb "creativematrix.com/beyondreading/gen/proto/purchase"
)

// Service Purchase API服务
type Service struct {
	dao *dao.Dao
}

// New 创建Purchase API服务实例
func New(dao *dao.Dao) *Service {
	return &Service{
		dao: dao,
	}
}

// Load 根据配置加载服务实例
func Load(c *conf.Config) *Service {
	return New(dao.Load(c))
}

func (s *Service) Ping(ctx context.Context) (err error) {
	return s.dao.Ping(ctx)
}

func (s *Service) Close() {
	s.dao.Close()
}


// PurchaseChapter 购买章节
func (s *Service) PurchaseChapter(ctx context.Context, req *vo.PurchaseChapterReq) (*vo.PurchaseChapterResp, error) {
	// 转换章节购买项
	chapterOrders := make([]uint32, 0, len(req.Chapters))
	coinAmounts := make([]float64, 0, len(req.Chapters))

	for _, chapter := range req.Chapters {
		chapterOrders = append(chapterOrders, chapter.ChapterOrder)
		coinAmounts = append(coinAmounts, chapter.CoinAmount)
	}

	pbReq := &purchasepb.PurchaseChapterReq{
		UserId:        req.UserId,
		BookId:        req.BookId,
		ChapterOrders: chapterOrders,
		CoinAmounts:   coinAmounts,
	}

	resp, err := s.dao.PurchaseClient.PurchaseChapter(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	orders := make([]*vo.PurchaseOrderInfo, 0, len(resp.Orders))
	for _, order := range resp.Orders {
		orders = append(orders, s.convertPurchaseOrderToPB(order))
	}

	return &vo.PurchaseChapterResp{
		Orders: orders,
	}, nil
}

// PurchaseMonthly 购买包月
func (s *Service) PurchaseMonthly(ctx context.Context, req *vo.PurchaseMonthlyReq) (*vo.PurchaseMonthlyResp, error) {
	durationDays := req.DurationDays
	if durationDays <= 0 {
		durationDays = 30 // 默认30天
	}

	pbReq := &purchasepb.PurchaseMonthlyReq{
		UserId:       req.UserId,
		CoinAmount:   req.CoinAmount,
		DurationDays: durationDays,
	}

	resp, err := s.dao.PurchaseClient.PurchaseMonthly(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	return &vo.PurchaseMonthlyResp{
		Order: s.convertVipMonthlyOrderToPB(resp.Order),
	}, nil
}

// PurchaseVip 购买VIP
func (s *Service) PurchaseVip(ctx context.Context, req *vo.PurchaseVipReq) (*vo.PurchaseVipResp, error) {
	durationDays := req.DurationDays
	if durationDays <= 0 {
		durationDays = 30 // 默认30天
	}

	pbReq := &purchasepb.PurchaseVipReq{
		UserId:       req.UserId,
		CoinAmount:   req.CoinAmount,
		DurationDays: durationDays,
	}

	resp, err := s.dao.PurchaseClient.PurchaseVip(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	return &vo.PurchaseVipResp{
		Order: s.convertVipMonthlyOrderToPB(resp.Order),
	}, nil
}

// GetPurchaseOrders 获取购买订单列表
func (s *Service) GetPurchaseOrders(ctx context.Context, req *vo.GetPurchaseOrdersReq) (*vo.GetPurchaseOrdersResp, error) {
	pbReq := &purchasepb.GetPurchaseOrdersReq{
		UserId:   req.UserId,
		Page:     req.Page,
		PageSize: req.PageSize,
		BookId:   req.BookId,
	}

	resp, err := s.dao.PurchaseClient.GetPurchaseOrders(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	orders := make([]*vo.PurchaseOrderInfo, 0, len(resp.Orders))
	for _, order := range resp.Orders {
		orders = append(orders, s.convertPurchaseOrderToPB(order))
	}

	return &vo.GetPurchaseOrdersResp{
		Orders: orders,
		Total:  resp.Total,
	}, nil
}

// GetVipMonthlyOrders 获取VIP/包月订单列表
func (s *Service) GetVipMonthlyOrders(ctx context.Context, req *vo.GetVipMonthlyOrdersReq) (*vo.GetVipMonthlyOrdersResp, error) {
	pbReq := &purchasepb.GetVipMonthlyOrdersReq{
		UserId:    req.UserId,
		Page:      req.Page,
		PageSize:  req.PageSize,
		OrderType: req.OrderType,
	}

	resp, err := s.dao.PurchaseClient.GetVipMonthlyOrders(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	orders := make([]*vo.VipMonthlyOrderInfo, 0, len(resp.Orders))
	for _, order := range resp.Orders {
		orders = append(orders, s.convertVipMonthlyOrderToPB(order))
	}

	return &vo.GetVipMonthlyOrdersResp{
		Orders: orders,
		Total:  resp.Total,
	}, nil
}

// CheckChapterPurchased 检查章节购买状态
func (s *Service) CheckChapterPurchased(ctx context.Context, req *vo.CheckChapterPurchasedReq) (*vo.CheckChapterPurchasedResp, error) {
	pbReq := &purchasepb.CheckChapterPurchasedReq{
		UserId:       req.UserId,
		BookId:       req.BookId,
		ChapterOrder: req.ChapterOrder,
	}

	resp, err := s.dao.PurchaseClient.CheckChapterPurchased(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	var purchasedAt *time.Time
	if resp.PurchasedAt > 0 {
		t := time.Unix(resp.PurchasedAt, 0)
		purchasedAt = &t
	}

	return &vo.CheckChapterPurchasedResp{
		IsPurchased: resp.IsPurchased,
		PurchasedAt: purchasedAt,
		IsMonthly:   resp.IsMonthly,
		IsVip:       resp.IsVip,
		OrderId:     resp.OrderId,
	}, nil
}

// CheckVipStatus 检查VIP状态
func (s *Service) CheckVipStatus(ctx context.Context, req *vo.CheckVipStatusReq) (*vo.CheckVipStatusResp, error) {
	pbReq := &purchasepb.CheckVipStatusReq{
		UserId: req.UserId,
	}

	resp, err := s.dao.PurchaseClient.CheckVipStatus(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	var startTime, endTime *time.Time
	if resp.StartTime > 0 {
		t := time.Unix(resp.StartTime, 0)
		startTime = &t
	}
	if resp.EndTime > 0 {
		t := time.Unix(resp.EndTime, 0)
		endTime = &t
	}

	return &vo.CheckVipStatusResp{
		IsActive:  resp.IsActive,
		StartTime: startTime,
		EndTime:   endTime,
		OrderId:   resp.OrderId,
	}, nil
}

// CheckMonthlyStatus 检查包月状态
func (s *Service) CheckMonthlyStatus(ctx context.Context, req *vo.CheckMonthlyStatusReq) (*vo.CheckMonthlyStatusResp, error) {
	pbReq := &purchasepb.CheckMonthlyStatusReq{
		UserId: req.UserId,
	}

	resp, err := s.dao.PurchaseClient.CheckMonthlyStatus(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	var startTime, endTime *time.Time
	if resp.StartTime > 0 {
		t := time.Unix(resp.StartTime, 0)
		startTime = &t
	}
	if resp.EndTime > 0 {
		t := time.Unix(resp.EndTime, 0)
		endTime = &t
	}

	return &vo.CheckMonthlyStatusResp{
		IsActive:  resp.IsActive,
		StartTime: startTime,
		EndTime:   endTime,
		OrderId:   resp.OrderId,
	}, nil
}

// GetPurchasedChapters 获取已购买的章节列表
func (s *Service) GetPurchasedChapters(ctx context.Context, req *vo.GetPurchasedChaptersReq) (*vo.GetPurchasedChaptersResp, error) {
	pbReq := &purchasepb.GetPurchasedChaptersReq{
		UserId:   req.UserId,
		BookId:   req.BookId,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	resp, err := s.dao.PurchaseClient.GetPurchasedChapters(ctx, pbReq)
	if err != nil {
		return nil, err
	}

	chapters := make([]*vo.ChapterPurchaseInfo, 0, len(resp.Chapters))
	for _, chapter := range resp.Chapters {
		chapters = append(chapters, s.convertChapterPurchaseInfoToPB(chapter))
	}

	return &vo.GetPurchasedChaptersResp{
		Chapters: chapters,
		Total:    resp.Total,
	}, nil
}

// convertPurchaseOrderToPB 转换购买订单到VO
func (s *Service) convertPurchaseOrderToPB(order *purchasepb.PurchaseOrder) *vo.PurchaseOrderInfo {
	if order == nil {
		return nil
	}

	return &vo.PurchaseOrderInfo{
		OrderId:      order.OrderId,
		AccountId:    order.AccountId,
		UserId:       order.UserId,
		OrderType:    order.OrderType,
		BookId:       order.BookId,
		BookName:     order.BookName,
		ChapterId:    order.ChapterId,
		ChapterTitle: order.ChapterTitle,
		ChapterOrder: order.ChapterOrder,
		CoinAmount:   order.CoinAmount,
		Status:       order.Status,
		CreatedAt:    time.Unix(order.CreatedAt, 0),
		UpdatedAt:    time.Unix(order.UpdatedAt, 0),
	}
}

// convertVipMonthlyOrderToPB 转换VIP/包月订单到VO
func (s *Service) convertVipMonthlyOrderToPB(order *purchasepb.VipMonthlyOrder) *vo.VipMonthlyOrderInfo {
	if order == nil {
		return nil
	}

	var startTime, endTime *time.Time
	if order.StartTime > 0 {
		t := time.Unix(order.StartTime, 0)
		startTime = &t
	}
	if order.EndTime > 0 {
		t := time.Unix(order.EndTime, 0)
		endTime = &t
	}

	return &vo.VipMonthlyOrderInfo{
		OrderId:      order.OrderId,
		AccountId:    order.AccountId,
		UserId:       order.UserId,
		OrderType:    order.OrderType,
		CoinAmount:   order.CoinAmount,
		DurationDays: order.DurationDays,
		StartTime:    startTime,
		EndTime:      endTime,
		Status:       order.Status,
		CreatedAt:    time.Unix(order.CreatedAt, 0),
		UpdatedAt:    time.Unix(order.UpdatedAt, 0),
	}
}

// convertChapterPurchaseInfoToPB 转换章节购买信息到VO
func (s *Service) convertChapterPurchaseInfoToPB(chapter *purchasepb.ChapterPurchaseInfo) *vo.ChapterPurchaseInfo {
	if chapter == nil {
		return nil
	}

	return &vo.ChapterPurchaseInfo{
		OrderId:      chapter.OrderId,
		ChapterId:    chapter.ChapterId,
		ChapterTitle: chapter.ChapterTitle,
		ChapterOrder: chapter.ChapterOrder,
		CoinAmount:   chapter.CoinAmount,
		PurchasedAt:  time.Unix(chapter.PurchasedAt, 0),
		IsMonthly:    chapter.IsMonthly,
		IsVip:        chapter.IsVip,
	}
}
