package main

import (
	"context"
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"creativematrix.com/beyondreading/gen/app/api/purchase/conf"
	httphandler "creativematrix.com/beyondreading/gen/app/api/purchase/http"
	"creativematrix.com/beyondreading/gen/app/api/purchase/svc"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/tracer"
	"github.com/gin-gonic/gin"
)

var (
	configFile = flag.String("conf", "purchase-api.toml", "config file")
)

const App = "api-purchase"

func main() {
	flag.Parse()

	// 加载配置
	config := conf.Load(App)

	// 初始化日志
	logger.InitLog()

	// 初始化链路追踪
	tracer.InitTracing()

	// 创建服务
	service := svc.Load(config)

	// 创建HTTP处理器
	handler := httphandler.New(service)

	// 创建Gin引擎
	r := gin.New()
	r.Use(gin.Logger())
	r.Use(gin.Recovery())

	// 注册路由
	handler.RegisterRoutes(r)

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"status": "ok"})
	})

	// 创建HTTP服务器
	srv := &http.Server{
		Addr:    fmt.Sprintf(":%d", config.Port),
		Handler: r,
	}

	logger.LogInfof("Purchase API service listening on :%d", config.Port)

	// 启动服务
	go func() {
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.LogFatalf("Failed to start server: %v", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.LogInfo("Shutting down server...")

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		logger.LogFatalf("Server forced to shutdown: %v", err)
	}

	logger.LogInfo("Server exiting")
}
