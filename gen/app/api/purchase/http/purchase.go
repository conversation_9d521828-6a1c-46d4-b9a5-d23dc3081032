package http

import (
	"errors"
	"strconv"

	"creativematrix.com/beyondreading/gen/app/api/purchase/model/vo"
	"creativematrix.com/beyondreading/pkg/ecode"
	"github.com/gin-gonic/gin"
)

func purchaseChapter(c *gin.Context) {
	param := new(vo.PurchaseChapterReq)
	err := c.BindJSON(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	if param.UserId == 0 || len(param.ChapterOrders) == 0 {
		ecode.Back(c).Failure(errors.New("invalid parameter"))
		return
	}

	data, err := service.PurchaseChapter(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

func purchaseMonthly(c *gin.Context) {
	param := new(vo.PurchaseMonthlyReq)
	err := c.Bind<PERSON>(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	if param.UserId == 0 || param.DurationDays <= 0 {
		ecode.Back(c).Failure(errors.New("invalid parameter"))
		return
	}

	data, err := service.PurchaseMonthly(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

func purchaseVip(c *gin.Context) {
	param := new(vo.PurchaseVipReq)
	err := c.BindJSON(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	if param.UserId == 0 || param.DurationDays <= 0 {
		ecode.Back(c).Failure(errors.New("invalid parameter"))
		return
	}

	data, err := service.PurchaseVip(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

func getPurchaseOrders(c *gin.Context) {
	param := new(vo.GetPurchaseOrdersReq)
	err := c.BindQuery(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	if param.UserId == 0 {
		ecode.Back(c).Failure(errors.New("invalid user id"))
		return
	}

	// 设置默认分页参数
	if param.Page <= 0 {
		param.Page = 1
	}
	if param.PageSize <= 0 {
		param.PageSize = 20
	}

	data, err := service.GetPurchaseOrders(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

func getVipMonthlyOrders(c *gin.Context) {
	param := new(vo.GetVipMonthlyOrdersReq)
	err := c.BindQuery(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	if param.UserId == 0 {
		ecode.Back(c).Failure(errors.New("invalid user id"))
		return
	}

	// 设置默认分页参数
	if param.Page <= 0 {
		param.Page = 1
	}
	if param.PageSize <= 0 {
		param.PageSize = 20
	}

	data, err := service.GetVipMonthlyOrders(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

func checkChapterPurchased(c *gin.Context) {
	userIdStr := c.Query("user_id")
	bookId := c.Query("book_id")
	chapterIdStr := c.Query("chapter_id")

	if userIdStr == "" || bookId == "" || chapterIdStr == "" {
		ecode.Back(c).Failure(errors.New("invalid parameter"))
		return
	}

	userId, err := strconv.ParseUint(userIdStr, 10, 64)
	if err != nil {
		ecode.Back(c).Failure(errors.New("invalid user id"))
		return
	}

	chapterId, err := strconv.ParseUint(chapterIdStr, 10, 64)
	if err != nil {
		ecode.Back(c).Failure(errors.New("invalid chapter id"))
		return
	}

	param := &vo.CheckChapterPurchasedReq{
		UserId:    userId,
		BookId:    bookId,
		ChapterId: chapterId,
	}

	data, err := service.CheckChapterPurchased(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

func checkVipStatus(c *gin.Context) {
	userIdStr := c.Query("user_id")
	if userIdStr == "" {
		ecode.Back(c).Failure(errors.New("invalid user id"))
		return
	}

	userId, err := strconv.ParseUint(userIdStr, 10, 64)
	if err != nil {
		ecode.Back(c).Failure(errors.New("invalid user id"))
		return
	}

	param := &vo.CheckVipStatusReq{
		UserId: userId,
	}

	data, err := service.CheckVipStatus(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

func checkMonthlyStatus(c *gin.Context) {
	userIdStr := c.Query("user_id")
	if userIdStr == "" {
		ecode.Back(c).Failure(errors.New("invalid user id"))
		return
	}

	userId, err := strconv.ParseUint(userIdStr, 10, 64)
	if err != nil {
		ecode.Back(c).Failure(errors.New("invalid user id"))
		return
	}

	param := &vo.CheckMonthlyStatusReq{
		UserId: userId,
	}

	data, err := service.CheckMonthlyStatus(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

func getPurchasedChapters(c *gin.Context) {
	param := new(vo.GetPurchasedChaptersReq)
	err := c.BindQuery(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	if param.UserId == 0 || param.BookId == "" {
		ecode.Back(c).Failure(errors.New("invalid parameter"))
		return
	}

	data, err := service.GetPurchasedChapters(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}
