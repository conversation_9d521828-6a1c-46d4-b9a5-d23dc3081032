package http

import (
	"creativematrix.com/beyondreading/gen/app/api/purchase/model/vo"
	"creativematrix.com/beyondreading/gen/app/api/purchase/svc"
	"creativematrix.com/beyondreading/pkg/logger"
	"github.com/gin-gonic/gin"
	"net/http"
)

// Handler Purchase HTTP处理器
type Handler struct {
	svc *svc.Service
}

// New 创建HTTP处理器
func New(svc *svc.Service) *Handler {
	return &Handler{
		svc: svc,
	}
}

// RegisterRoutes 注册路由
func (h *Handler) RegisterRoutes(r *gin.Engine) {
	v1 := r.Group("/api/v1/purchase")
	{
		v1.POST("/chapter", h.PurchaseChapter)
		v1.POST("/monthly", h.PurchaseMonthly)
		v1.POST("/vip", h.PurchaseVip)
		v1.GET("/orders", h.GetPurchaseOrders)
		v1.GET("/vip-monthly-orders", h.GetVipMonthlyOrders)
		v1.GET("/check-chapter", h.CheckChapterPurchased)
		v1.GET("/vip-status", h.CheckVipStatus)
		v1.GET("/monthly-status", h.CheckMonthlyStatus)
		v1.GET("/purchased-chapters", h.GetPurchasedChapters)
	}
}

// PurchaseChapter 购买章节
func (h *Handler) PurchaseChapter(c *gin.Context) {
	var req vo.PurchaseChapterReq
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.LogErrorf("PurchaseChapter bind json error: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.svc.PurchaseChapter(c.Request.Context(), &req)
	if err != nil {
		logger.LogErrorf("PurchaseChapter service error: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// PurchaseMonthly 购买包月
func (h *Handler) PurchaseMonthly(c *gin.Context) {
	var req vo.PurchaseMonthlyReq
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.LogErrorf("PurchaseMonthly bind json error: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.svc.PurchaseMonthly(c.Request.Context(), &req)
	if err != nil {
		logger.LogErrorf("PurchaseMonthly service error: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// PurchaseVip 购买VIP
func (h *Handler) PurchaseVip(c *gin.Context) {
	var req vo.PurchaseVipReq
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.LogErrorf("PurchaseVip bind json error: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.svc.PurchaseVip(c.Request.Context(), &req)
	if err != nil {
		logger.LogErrorf("PurchaseVip service error: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// GetPurchaseOrders 获取购买订单列表
func (h *Handler) GetPurchaseOrders(c *gin.Context) {
	var req vo.GetPurchaseOrdersReq
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.LogErrorf("GetPurchaseOrders bind query error: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	resp, err := h.svc.GetPurchaseOrders(c.Request.Context(), &req)
	if err != nil {
		logger.LogErrorf("GetPurchaseOrders service error: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// GetVipMonthlyOrders 获取VIP/包月订单列表
func (h *Handler) GetVipMonthlyOrders(c *gin.Context) {
	var req vo.GetVipMonthlyOrdersReq
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.LogErrorf("GetVipMonthlyOrders bind query error: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	resp, err := h.svc.GetVipMonthlyOrders(c.Request.Context(), &req)
	if err != nil {
		logger.LogErrorf("GetVipMonthlyOrders service error: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// CheckChapterPurchased 检查章节购买状态
func (h *Handler) CheckChapterPurchased(c *gin.Context) {
	var req vo.CheckChapterPurchasedReq
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.LogErrorf("CheckChapterPurchased bind query error: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.svc.CheckChapterPurchased(c.Request.Context(), &req)
	if err != nil {
		logger.LogErrorf("CheckChapterPurchased service error: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// CheckVipStatus 检查VIP状态
func (h *Handler) CheckVipStatus(c *gin.Context) {
	var req vo.CheckVipStatusReq
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.LogErrorf("CheckVipStatus bind query error: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.svc.CheckVipStatus(c.Request.Context(), &req)
	if err != nil {
		logger.LogErrorf("CheckVipStatus service error: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// CheckMonthlyStatus 检查包月状态
func (h *Handler) CheckMonthlyStatus(c *gin.Context) {
	var req vo.CheckMonthlyStatusReq
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.LogErrorf("CheckMonthlyStatus bind query error: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	resp, err := h.svc.CheckMonthlyStatus(c.Request.Context(), &req)
	if err != nil {
		logger.LogErrorf("CheckMonthlyStatus service error: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	c.JSON(http.StatusOK, resp)
}

// GetPurchasedChapters 获取已购买的章节列表
func (h *Handler) GetPurchasedChapters(c *gin.Context) {
	var req vo.GetPurchasedChaptersReq
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.LogErrorf("GetPurchasedChapters bind query error: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	resp, err := h.svc.GetPurchasedChapters(c.Request.Context(), &req)
	if err != nil {
		logger.LogErrorf("GetPurchasedChapters service error: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	c.JSON(http.StatusOK, resp)
}


