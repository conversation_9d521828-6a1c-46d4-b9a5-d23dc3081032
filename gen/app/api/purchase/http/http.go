package http

import (
	"creativematrix.com/beyondreading/gen/app/api/purchase/conf"
	"creativematrix.com/beyondreading/gen/app/api/purchase/svc"
	bbrouter "creativematrix.com/beyondreading/pkg/router"
)

var (
	service *svc.PurchaseSvc
)

func Start(c *conf.Config, s *svc.PurchaseSvc) {
	service = s
	router := bbrouter.Start(c.Base)

	v1 := router.Group("/api/v1/purchase")
	{
		v1.POST("/chapter", purchaseChapter)
		v1.POST("/monthly", purchaseMonthly)
		v1.POST("/vip", purchaseVip)
		v1.GET("/orders", getPurchaseOrders)
		v1.GET("/vip-monthly-orders", getVipMonthlyOrders)
		v1.GET("/check-chapter", checkChapterPurchased)
		v1.GET("/check-vip", checkVipStatus)
		v1.GET("/check-monthly", checkMonthlyStatus)
		v1.GET("/purchased-chapters", getPurchasedChapters)
	}

	go func() {
		if err := router.Run(c.Port.HTTP); err != nil {
			panic(err)
		}
	}()
}
