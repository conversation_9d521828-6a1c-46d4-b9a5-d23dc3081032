package po

import "time"

// RechargeOrder 充值订单模型
type RechargeOrder struct {
	OrderId        string    `db:"order_id" json:"order_id"`                 // 充值订单ID
	AccountId      uint64    `db:"account_id" json:"account_id"`             // 账户ID
	UserId         uint64    `db:"user_id" json:"user_id"`                   // 用户ID
	Amount         float64   `db:"amount" json:"amount"`                     // 充值金额（人民币）
	CoinAmount     float64   `db:"coin_amount" json:"coin_amount"`           // 获得书币数量
	ExchangeRate   float64   `db:"exchange_rate" json:"exchange_rate"`       // 兑换比例
	PaymentMethod  string    `db:"payment_method" json:"payment_method"`     // 支付方式：alipay, wechat, apple, bank
	PaymentOrderId *string   `db:"payment_order_id" json:"payment_order_id"` // 第三方支付订单ID
	Status         int32     `db:"status" json:"status"`                     // 订单状态：1-待支付，2-支付成功，3-支付失败，4-已退款
	PaidAt         *time.Time `db:"paid_at" json:"paid_at"`                  // 支付时间
	CreatedAt      time.Time `db:"created_at" json:"created_at"`             // 创建时间
	UpdatedAt      time.Time `db:"updated_at" json:"updated_at"`             // 更新时间
}
