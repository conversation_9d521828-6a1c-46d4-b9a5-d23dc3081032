-- Account微服务数据库表结构
-- 分表基数：8 (00-07，即user_id % 8)

-- 用户账户表（分表 account00-account07）
DROP TABLE IF EXISTS `account00`;
CREATE TABLE `account00` (
  `account_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID，关联用户表',
  `coin_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '书币余额',
  `total_recharged` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计充值金额',
  `total_consumed` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计消费金额',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '账户状态：1-正常，2-冻结，3-注销',
  `user_type` tinyint NOT NULL DEFAULT '1' COMMENT '用户类型：1-普通用户，2-VIP用户，3-包月用户',
  `user_level` int NOT NULL DEFAULT '1' COMMENT '用户等级（根据消费书币计算，1000书币一级）',
  `vip_expire_time` timestamp NULL DEFAULT NULL COMMENT 'VIP过期时间',
  `monthly_expire_time` timestamp NULL DEFAULT NULL COMMENT '包月过期时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`account_id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户账户表00';

DROP TABLE IF EXISTS `account01`;
CREATE TABLE `account01` (
  `account_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID，关联用户表',
  `coin_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '书币余额',
  `total_recharged` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计充值金额',
  `total_consumed` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计消费金额',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '账户状态：1-正常，2-冻结，3-注销',
  `user_type` tinyint NOT NULL DEFAULT '1' COMMENT '用户类型：1-普通用户，2-VIP用户，3-包月用户',
  `user_level` int NOT NULL DEFAULT '1' COMMENT '用户等级（根据消费书币计算，1000书币一级）',
  `vip_expire_time` timestamp NULL DEFAULT NULL COMMENT 'VIP过期时间',
  `monthly_expire_time` timestamp NULL DEFAULT NULL COMMENT '包月过期时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`account_id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户账户表01';

DROP TABLE IF EXISTS `account02`;
CREATE TABLE `account02` (
  `account_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID，关联用户表',
  `coin_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '书币余额',
  `total_recharged` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计充值金额',
  `total_consumed` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计消费金额',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '账户状态：1-正常，2-冻结，3-注销',
  `user_type` tinyint NOT NULL DEFAULT '1' COMMENT '用户类型：1-普通用户，2-VIP用户，3-包月用户',
  `user_level` int NOT NULL DEFAULT '1' COMMENT '用户等级（根据消费书币计算，1000书币一级）',
  `vip_expire_time` timestamp NULL DEFAULT NULL COMMENT 'VIP过期时间',
  `monthly_expire_time` timestamp NULL DEFAULT NULL COMMENT '包月过期时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`account_id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户账户表02';

DROP TABLE IF EXISTS `account03`;
CREATE TABLE `account03` (
  `account_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID，关联用户表',
  `coin_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '书币余额',
  `total_recharged` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计充值金额',
  `total_consumed` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计消费金额',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '账户状态：1-正常，2-冻结，3-注销',
  `user_type` tinyint NOT NULL DEFAULT '1' COMMENT '用户类型：1-普通用户，2-VIP用户，3-包月用户',
  `user_level` int NOT NULL DEFAULT '1' COMMENT '用户等级（根据消费书币计算，1000书币一级）',
  `vip_expire_time` timestamp NULL DEFAULT NULL COMMENT 'VIP过期时间',
  `monthly_expire_time` timestamp NULL DEFAULT NULL COMMENT '包月过期时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`account_id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户账户表03';

DROP TABLE IF EXISTS `account04`;
CREATE TABLE `account04` (
  `account_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID，关联用户表',
  `coin_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '书币余额',
  `total_recharged` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计充值金额',
  `total_consumed` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计消费金额',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '账户状态：1-正常，2-冻结，3-注销',
  `user_type` tinyint NOT NULL DEFAULT '1' COMMENT '用户类型：1-普通用户，2-VIP用户，3-包月用户',
  `user_level` int NOT NULL DEFAULT '1' COMMENT '用户等级（根据消费书币计算，1000书币一级）',
  `vip_expire_time` timestamp NULL DEFAULT NULL COMMENT 'VIP过期时间',
  `monthly_expire_time` timestamp NULL DEFAULT NULL COMMENT '包月过期时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`account_id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户账户表04';

DROP TABLE IF EXISTS `account05`;
CREATE TABLE `account05` (
  `account_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID，关联用户表',
  `coin_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '书币余额',
  `total_recharged` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计充值金额',
  `total_consumed` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计消费金额',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '账户状态：1-正常，2-冻结，3-注销',
  `user_type` tinyint NOT NULL DEFAULT '1' COMMENT '用户类型：1-普通用户，2-VIP用户，3-包月用户',
  `user_level` int NOT NULL DEFAULT '1' COMMENT '用户等级（根据消费书币计算，1000书币一级）',
  `vip_expire_time` timestamp NULL DEFAULT NULL COMMENT 'VIP过期时间',
  `monthly_expire_time` timestamp NULL DEFAULT NULL COMMENT '包月过期时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`account_id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户账户表05';

DROP TABLE IF EXISTS `account06`;
CREATE TABLE `account06` (
  `account_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID，关联用户表',
  `coin_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '书币余额',
  `total_recharged` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计充值金额',
  `total_consumed` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计消费金额',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '账户状态：1-正常，2-冻结，3-注销',
  `user_type` tinyint NOT NULL DEFAULT '1' COMMENT '用户类型：1-普通用户，2-VIP用户，3-包月用户',
  `user_level` int NOT NULL DEFAULT '1' COMMENT '用户等级（根据消费书币计算，1000书币一级）',
  `vip_expire_time` timestamp NULL DEFAULT NULL COMMENT 'VIP过期时间',
  `monthly_expire_time` timestamp NULL DEFAULT NULL COMMENT '包月过期时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`account_id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户账户表06';

DROP TABLE IF EXISTS `account07`;
CREATE TABLE `account07` (
  `account_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID，关联用户表',
  `coin_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '书币余额',
  `total_recharged` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计充值金额',
  `total_consumed` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计消费金额',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '账户状态：1-正常，2-冻结，3-注销',
  `user_type` tinyint NOT NULL DEFAULT '1' COMMENT '用户类型：1-普通用户，2-VIP用户，3-包月用户',
  `user_level` int NOT NULL DEFAULT '1' COMMENT '用户等级（根据消费书币计算，1000书币一级）',
  `vip_expire_time` timestamp NULL DEFAULT NULL COMMENT 'VIP过期时间',
  `monthly_expire_time` timestamp NULL DEFAULT NULL COMMENT '包月过期时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`account_id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户账户表07';

-- 账户日志表（分表 account_log00-account_log07）
DROP TABLE IF EXISTS `account_log00`;
CREATE TABLE `account_log00` (
  `log_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `account_id` bigint unsigned NOT NULL COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `transaction_type` varchar(32) NOT NULL COMMENT '交易类型：recharge, purchase_chapter, purchase_monthly, purchase_vip',
  `amount` decimal(15,2) NOT NULL COMMENT '变动金额（正数为增加，负数为减少）',
  `balance_before` decimal(15,2) NOT NULL COMMENT '变动前余额',
  `balance_after` decimal(15,2) NOT NULL COMMENT '变动后余额',
  `order_id` varchar(64) DEFAULT NULL COMMENT '关联订单ID',
  `book_id` varchar(64) DEFAULT NULL COMMENT '书籍ID',
  `chapter_id` varchar(64) DEFAULT NULL COMMENT '章节ID',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `extra_data` text COMMENT '额外数据（JSON格式）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_transaction_type` (`transaction_type`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_book_id` (`book_id`),
  KEY `idx_chapter_id` (`chapter_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='账户日志表00';
