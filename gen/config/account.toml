# Account微服务配置文件

# 基础配置继承自 app/base.toml

# MySQL数据库配置
[mysql]
driver = "mysql"
dsn = ["root:password@tcp(127.0.0.1:3306)/beyondreading?charset=utf8mb4&parseTime=True&loc=Local"]
maxOpen = 100
maxIdle = 10
maxIdleTime = "240s"
maxLifetime = "1800s"
queryTimeout = "5s"
execTimeout = "5s"
tranTimeout = "10s"

# Redis缓存配置
[redisAccount]
address = "127.0.0.1:6379"
password = ""
maxIdle = 10
maxActive = 100

# 日志配置
[log]
level = "info"

# 账户相关配置
[account]
defaultExchangeRate = "1.0000"  # 默认兑换比例 1元=1书币
minRechargeAmount = "1.00"      # 最小充值金额
maxRechargeAmount = "10000.00"  # 最大充值金额
cacheExpire = 3600              # 缓存过期时间（秒）
enableCache = true              # 是否启用缓存

# 支付配置
[payment]
# 支付宝配置
[payment.alipay]
appId = "your_alipay_app_id"
privateKey = "your_alipay_private_key"
publicKey = "your_alipay_public_key"

# 微信支付配置
[payment.wechat]
appId = "your_wechat_app_id"
mchId = "your_wechat_mch_id"
key = "your_wechat_key"
notifyUrl = "https://your-domain.com/payment/wechat/notify"

# 苹果支付配置
[payment.apple]
bundleId = "com.yourcompany.yourapp"
isProduction = false
