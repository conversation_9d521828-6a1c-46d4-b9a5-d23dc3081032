syntax = "proto3";
package account;
option go_package = "creativematrix.com/beyondreading/gen/proto/account";

// Account服务定义
service Account {
  // 获取账户信息
  rpc GetAccount(GetAccountReq) returns (GetAccountResp);
  
  // 创建账户
  rpc CreateAccount(CreateAccountReq) returns (CreateAccountResp);
  
  // 充值
  rpc Recharge(RechargeReq) returns (RechargeResp);
  
  // 购买章节
  rpc PurchaseChapter(PurchaseChapterReq) returns (PurchaseChapterResp);
  
  // 购买包月
  rpc PurchaseMonthly(PurchaseMonthlyReq) returns (PurchaseMonthlyResp);
  
  // 购买VIP
  rpc PurchaseVip(PurchaseVipReq) returns (PurchaseVipResp);
  
  // 获取账户日志
  rpc GetAccountLogs(GetAccountLogsReq) returns (GetAccountLogsResp);
  
  // 获取充值订单
  rpc GetRechargeOrder(GetRechargeOrderReq) returns (GetRechargeOrderResp);
  
  // 获取购买订单列表
  rpc GetPurchaseOrders(GetPurchaseOrdersReq) returns (GetPurchaseOrdersResp);
  
  // 检查用户是否已购买章节
  rpc CheckChapterPurchased(CheckChapterPurchasedReq) returns (CheckChapterPurchasedResp);
  
  // 检查用户包月状态
  rpc CheckMonthlyStatus(CheckMonthlyStatusReq) returns (CheckMonthlyStatusResp);
}

// 账户信息
message AccountInfo {
  uint64 account_id = 1;
  string user_id = 2;
  string coin_balance = 3;  // 使用string避免精度问题
  string total_recharged = 4;
  string total_consumed = 5;
  int32 status = 6;  // 1-正常，2-冻结，3-注销
  int64 created_at = 7;  // Unix时间戳
  int64 updated_at = 8;  // Unix时间戳
}

// 账户日志
message AccountLog {
  uint64 log_id = 1;
  uint64 account_id = 2;
  string user_id = 3;
  string transaction_type = 4;
  string amount = 5;
  string balance_before = 6;
  string balance_after = 7;
  string order_id = 8;
  string book_id = 9;
  string chapter_id = 10;
  string description = 11;
  string extra_data = 12;  // JSON字符串
  int64 created_at = 13;   // Unix时间戳
}

// 充值订单
message RechargeOrder {
  string order_id = 1;
  uint64 account_id = 2;
  string user_id = 3;
  string amount = 4;
  string coin_amount = 5;
  string exchange_rate = 6;
  string payment_method = 7;
  string payment_order_id = 8;
  int32 status = 9;  // 1-待支付，2-支付成功，3-支付失败，4-已退款
  int64 paid_at = 10;    // Unix时间戳
  int64 created_at = 11; // Unix时间戳
  int64 updated_at = 12; // Unix时间戳
}

// 购买订单
message PurchaseOrder {
  string order_id = 1;
  uint64 account_id = 2;
  string user_id = 3;
  string order_type = 4;  // chapter, monthly, vip
  string book_id = 5;
  string book_name = 6;
  string chapter_id = 7;
  string chapter_title = 8;
  uint32 chapter_order = 9;
  string coin_amount = 10;
  int32 duration_days = 11;
  int64 start_time = 12;  // Unix时间戳
  int64 end_time = 13;    // Unix时间戳
  int32 status = 14;      // 1-待支付，2-支付成功，3-支付失败
  int64 created_at = 15;  // Unix时间戳
  int64 updated_at = 16;  // Unix时间戳
}

// 请求和响应消息

// 获取账户信息
message GetAccountReq {
  string user_id = 1;
}

message GetAccountResp {
  int32 code = 1;
  string message = 2;
  AccountInfo account = 3;
}

// 创建账户
message CreateAccountReq {
  string user_id = 1;
}

message CreateAccountResp {
  int32 code = 1;
  string message = 2;
  AccountInfo account = 3;
}

// 充值
message RechargeReq {
  string user_id = 1;
  string amount = 2;  // 充值金额（人民币）
  string payment_method = 3;  // 支付方式
  string exchange_rate = 4;  // 兑换比例，可选，默认1:1
}

message RechargeResp {
  int32 code = 1;
  string message = 2;
  RechargeOrder order = 3;
}

// 购买章节
message PurchaseChapterReq {
  string user_id = 1;
  string book_id = 2;
  string book_name = 3;
  string chapter_id = 4;
  string chapter_title = 5;
  uint32 chapter_order = 6;
  string coin_amount = 7;  // 消费书币数量
}

message PurchaseChapterResp {
  int32 code = 1;
  string message = 2;
  PurchaseOrder order = 3;
}

// 购买包月
message PurchaseMonthlyReq {
  string user_id = 1;
  string book_id = 2;
  string book_name = 3;
  string coin_amount = 4;
  int32 duration_days = 5;  // 包月天数，默认30天
}

message PurchaseMonthlyResp {
  int32 code = 1;
  string message = 2;
  PurchaseOrder order = 3;
}

// 购买VIP
message PurchaseVipReq {
  string user_id = 1;
  string coin_amount = 2;
  int32 duration_days = 3;  // VIP天数
}

message PurchaseVipResp {
  int32 code = 1;
  string message = 2;
  PurchaseOrder order = 3;
}

// 获取账户日志
message GetAccountLogsReq {
  string user_id = 1;
  int32 page = 2;
  int32 page_size = 3;
  string transaction_type = 4;  // 可选，筛选交易类型
}

message GetAccountLogsResp {
  int32 code = 1;
  string message = 2;
  repeated AccountLog logs = 3;
  int64 total = 4;
}

// 获取充值订单
message GetRechargeOrderReq {
  string order_id = 1;
}

message GetRechargeOrderResp {
  int32 code = 1;
  string message = 2;
  RechargeOrder order = 3;
}

// 获取购买订单列表
message GetPurchaseOrdersReq {
  string user_id = 1;
  int32 page = 2;
  int32 page_size = 3;
  string order_type = 4;  // 可选，筛选订单类型
}

message GetPurchaseOrdersResp {
  int32 code = 1;
  string message = 2;
  repeated PurchaseOrder orders = 3;
  int64 total = 4;
}

// 检查章节购买状态
message CheckChapterPurchasedReq {
  string user_id = 1;
  string book_id = 2;
  string chapter_id = 3;
}

message CheckChapterPurchasedResp {
  int32 code = 1;
  string message = 2;
  bool is_purchased = 3;
  int64 purchased_at = 4;  // Unix时间戳
}

// 检查包月状态
message CheckMonthlyStatusReq {
  string user_id = 1;
  string book_id = 2;
}

message CheckMonthlyStatusResp {
  int32 code = 1;
  string message = 2;
  bool is_active = 3;
  int64 start_time = 4;  // Unix时间戳
  int64 end_time = 5;    // Unix时间戳
}

// 通用响应
message CommonResp {
  int32 code = 1;
  string message = 2;
}
