// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.30.2
// source: gen/proto/user/user.proto

package user

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	UserService_RegisterBySms_FullMethodName    = "/user.UserService/RegisterBySms"
	UserService_RegisterByGoogle_FullMethodName = "/user.UserService/RegisterByGoogle"
	UserService_RegisterByApple_FullMethodName  = "/user.UserService/RegisterByApple"
	UserService_LoginBySms_FullMethodName       = "/user.UserService/LoginBySms"
	UserService_LoginByGoogle_FullMethodName    = "/user.UserService/LoginByGoogle"
	UserService_LoginByApple_FullMethodName     = "/user.UserService/LoginByApple"
	UserService_GetUserInfo_FullMethodName      = "/user.UserService/GetUserInfo"
	UserService_UpdateUserInfo_FullMethodName   = "/user.UserService/UpdateUserInfo"
	UserService_GetLoginLogs_FullMethodName     = "/user.UserService/GetLoginLogs"
	UserService_SendSmsCode_FullMethodName      = "/user.UserService/SendSmsCode"
	UserService_VerifySmsCode_FullMethodName    = "/user.UserService/VerifySmsCode"
)

// UserServiceClient is the client API for UserService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// User服务定义
type UserServiceClient interface {
	// 注册相关
	RegisterBySms(ctx context.Context, in *RegisterBySmsReq, opts ...grpc.CallOption) (*RegisterBySmsResp, error)
	RegisterByGoogle(ctx context.Context, in *RegisterByGoogleReq, opts ...grpc.CallOption) (*RegisterByGoogleResp, error)
	RegisterByApple(ctx context.Context, in *RegisterByAppleReq, opts ...grpc.CallOption) (*RegisterByAppleResp, error)
	// 登录相关
	LoginBySms(ctx context.Context, in *LoginBySmsReq, opts ...grpc.CallOption) (*LoginBySmsResp, error)
	LoginByGoogle(ctx context.Context, in *LoginByGoogleReq, opts ...grpc.CallOption) (*LoginByGoogleResp, error)
	LoginByApple(ctx context.Context, in *LoginByAppleReq, opts ...grpc.CallOption) (*LoginByAppleResp, error)
	// 用户信息管理
	GetUserInfo(ctx context.Context, in *GetUserInfoReq, opts ...grpc.CallOption) (*GetUserInfoResp, error)
	UpdateUserInfo(ctx context.Context, in *UpdateUserInfoReq, opts ...grpc.CallOption) (*UpdateUserInfoResp, error)
	// 登录日志
	GetLoginLogs(ctx context.Context, in *GetLoginLogsReq, opts ...grpc.CallOption) (*GetLoginLogsResp, error)
	// 短信验证码
	SendSmsCode(ctx context.Context, in *SendSmsCodeReq, opts ...grpc.CallOption) (*SendSmsCodeResp, error)
	VerifySmsCode(ctx context.Context, in *VerifySmsCodeReq, opts ...grpc.CallOption) (*VerifySmsCodeResp, error)
}

type userServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewUserServiceClient(cc grpc.ClientConnInterface) UserServiceClient {
	return &userServiceClient{cc}
}

func (c *userServiceClient) RegisterBySms(ctx context.Context, in *RegisterBySmsReq, opts ...grpc.CallOption) (*RegisterBySmsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RegisterBySmsResp)
	err := c.cc.Invoke(ctx, UserService_RegisterBySms_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) RegisterByGoogle(ctx context.Context, in *RegisterByGoogleReq, opts ...grpc.CallOption) (*RegisterByGoogleResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RegisterByGoogleResp)
	err := c.cc.Invoke(ctx, UserService_RegisterByGoogle_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) RegisterByApple(ctx context.Context, in *RegisterByAppleReq, opts ...grpc.CallOption) (*RegisterByAppleResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RegisterByAppleResp)
	err := c.cc.Invoke(ctx, UserService_RegisterByApple_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) LoginBySms(ctx context.Context, in *LoginBySmsReq, opts ...grpc.CallOption) (*LoginBySmsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LoginBySmsResp)
	err := c.cc.Invoke(ctx, UserService_LoginBySms_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) LoginByGoogle(ctx context.Context, in *LoginByGoogleReq, opts ...grpc.CallOption) (*LoginByGoogleResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LoginByGoogleResp)
	err := c.cc.Invoke(ctx, UserService_LoginByGoogle_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) LoginByApple(ctx context.Context, in *LoginByAppleReq, opts ...grpc.CallOption) (*LoginByAppleResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LoginByAppleResp)
	err := c.cc.Invoke(ctx, UserService_LoginByApple_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetUserInfo(ctx context.Context, in *GetUserInfoReq, opts ...grpc.CallOption) (*GetUserInfoResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserInfoResp)
	err := c.cc.Invoke(ctx, UserService_GetUserInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) UpdateUserInfo(ctx context.Context, in *UpdateUserInfoReq, opts ...grpc.CallOption) (*UpdateUserInfoResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateUserInfoResp)
	err := c.cc.Invoke(ctx, UserService_UpdateUserInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) GetLoginLogs(ctx context.Context, in *GetLoginLogsReq, opts ...grpc.CallOption) (*GetLoginLogsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetLoginLogsResp)
	err := c.cc.Invoke(ctx, UserService_GetLoginLogs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) SendSmsCode(ctx context.Context, in *SendSmsCodeReq, opts ...grpc.CallOption) (*SendSmsCodeResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendSmsCodeResp)
	err := c.cc.Invoke(ctx, UserService_SendSmsCode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userServiceClient) VerifySmsCode(ctx context.Context, in *VerifySmsCodeReq, opts ...grpc.CallOption) (*VerifySmsCodeResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(VerifySmsCodeResp)
	err := c.cc.Invoke(ctx, UserService_VerifySmsCode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserServiceServer is the server API for UserService service.
// All implementations should embed UnimplementedUserServiceServer
// for forward compatibility.
//
// User服务定义
type UserServiceServer interface {
	// 注册相关
	RegisterBySms(context.Context, *RegisterBySmsReq) (*RegisterBySmsResp, error)
	RegisterByGoogle(context.Context, *RegisterByGoogleReq) (*RegisterByGoogleResp, error)
	RegisterByApple(context.Context, *RegisterByAppleReq) (*RegisterByAppleResp, error)
	// 登录相关
	LoginBySms(context.Context, *LoginBySmsReq) (*LoginBySmsResp, error)
	LoginByGoogle(context.Context, *LoginByGoogleReq) (*LoginByGoogleResp, error)
	LoginByApple(context.Context, *LoginByAppleReq) (*LoginByAppleResp, error)
	// 用户信息管理
	GetUserInfo(context.Context, *GetUserInfoReq) (*GetUserInfoResp, error)
	UpdateUserInfo(context.Context, *UpdateUserInfoReq) (*UpdateUserInfoResp, error)
	// 登录日志
	GetLoginLogs(context.Context, *GetLoginLogsReq) (*GetLoginLogsResp, error)
	// 短信验证码
	SendSmsCode(context.Context, *SendSmsCodeReq) (*SendSmsCodeResp, error)
	VerifySmsCode(context.Context, *VerifySmsCodeReq) (*VerifySmsCodeResp, error)
}

// UnimplementedUserServiceServer should be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedUserServiceServer struct{}

func (UnimplementedUserServiceServer) RegisterBySms(context.Context, *RegisterBySmsReq) (*RegisterBySmsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterBySms not implemented")
}
func (UnimplementedUserServiceServer) RegisterByGoogle(context.Context, *RegisterByGoogleReq) (*RegisterByGoogleResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterByGoogle not implemented")
}
func (UnimplementedUserServiceServer) RegisterByApple(context.Context, *RegisterByAppleReq) (*RegisterByAppleResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterByApple not implemented")
}
func (UnimplementedUserServiceServer) LoginBySms(context.Context, *LoginBySmsReq) (*LoginBySmsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LoginBySms not implemented")
}
func (UnimplementedUserServiceServer) LoginByGoogle(context.Context, *LoginByGoogleReq) (*LoginByGoogleResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LoginByGoogle not implemented")
}
func (UnimplementedUserServiceServer) LoginByApple(context.Context, *LoginByAppleReq) (*LoginByAppleResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LoginByApple not implemented")
}
func (UnimplementedUserServiceServer) GetUserInfo(context.Context, *GetUserInfoReq) (*GetUserInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserInfo not implemented")
}
func (UnimplementedUserServiceServer) UpdateUserInfo(context.Context, *UpdateUserInfoReq) (*UpdateUserInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserInfo not implemented")
}
func (UnimplementedUserServiceServer) GetLoginLogs(context.Context, *GetLoginLogsReq) (*GetLoginLogsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoginLogs not implemented")
}
func (UnimplementedUserServiceServer) SendSmsCode(context.Context, *SendSmsCodeReq) (*SendSmsCodeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendSmsCode not implemented")
}
func (UnimplementedUserServiceServer) VerifySmsCode(context.Context, *VerifySmsCodeReq) (*VerifySmsCodeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifySmsCode not implemented")
}
func (UnimplementedUserServiceServer) testEmbeddedByValue() {}

// UnsafeUserServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserServiceServer will
// result in compilation errors.
type UnsafeUserServiceServer interface {
	mustEmbedUnimplementedUserServiceServer()
}

func RegisterUserServiceServer(s grpc.ServiceRegistrar, srv UserServiceServer) {
	// If the following call pancis, it indicates UnimplementedUserServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&UserService_ServiceDesc, srv)
}

func _UserService_RegisterBySms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterBySmsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).RegisterBySms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_RegisterBySms_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).RegisterBySms(ctx, req.(*RegisterBySmsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_RegisterByGoogle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterByGoogleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).RegisterByGoogle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_RegisterByGoogle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).RegisterByGoogle(ctx, req.(*RegisterByGoogleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_RegisterByApple_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterByAppleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).RegisterByApple(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_RegisterByApple_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).RegisterByApple(ctx, req.(*RegisterByAppleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_LoginBySms_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LoginBySmsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).LoginBySms(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_LoginBySms_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).LoginBySms(ctx, req.(*LoginBySmsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_LoginByGoogle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LoginByGoogleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).LoginByGoogle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_LoginByGoogle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).LoginByGoogle(ctx, req.(*LoginByGoogleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_LoginByApple_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LoginByAppleReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).LoginByApple(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_LoginByApple_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).LoginByApple(ctx, req.(*LoginByAppleReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetUserInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetUserInfo(ctx, req.(*GetUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_UpdateUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).UpdateUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_UpdateUserInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).UpdateUserInfo(ctx, req.(*UpdateUserInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_GetLoginLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoginLogsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).GetLoginLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_GetLoginLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).GetLoginLogs(ctx, req.(*GetLoginLogsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_SendSmsCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendSmsCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).SendSmsCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_SendSmsCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).SendSmsCode(ctx, req.(*SendSmsCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserService_VerifySmsCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifySmsCodeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServiceServer).VerifySmsCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserService_VerifySmsCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServiceServer).VerifySmsCode(ctx, req.(*VerifySmsCodeReq))
	}
	return interceptor(ctx, in, info, handler)
}

// UserService_ServiceDesc is the grpc.ServiceDesc for UserService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "user.UserService",
	HandlerType: (*UserServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RegisterBySms",
			Handler:    _UserService_RegisterBySms_Handler,
		},
		{
			MethodName: "RegisterByGoogle",
			Handler:    _UserService_RegisterByGoogle_Handler,
		},
		{
			MethodName: "RegisterByApple",
			Handler:    _UserService_RegisterByApple_Handler,
		},
		{
			MethodName: "LoginBySms",
			Handler:    _UserService_LoginBySms_Handler,
		},
		{
			MethodName: "LoginByGoogle",
			Handler:    _UserService_LoginByGoogle_Handler,
		},
		{
			MethodName: "LoginByApple",
			Handler:    _UserService_LoginByApple_Handler,
		},
		{
			MethodName: "GetUserInfo",
			Handler:    _UserService_GetUserInfo_Handler,
		},
		{
			MethodName: "UpdateUserInfo",
			Handler:    _UserService_UpdateUserInfo_Handler,
		},
		{
			MethodName: "GetLoginLogs",
			Handler:    _UserService_GetLoginLogs_Handler,
		},
		{
			MethodName: "SendSmsCode",
			Handler:    _UserService_SendSmsCode_Handler,
		},
		{
			MethodName: "VerifySmsCode",
			Handler:    _UserService_VerifySmsCode_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "gen/proto/user/user.proto",
}
