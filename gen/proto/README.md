# Protocol Buffers 代码生成指南

## 问题说明

如果遇到 `"google.protobuf.Timestamp" is not defined` 错误，这是因为 Protocol Buffers 编译器找不到 Google 的标准类型定义。

## ✅ 已解决方案

**我们已经更新了所有代码，使用简化版proto文件（`account_simple.proto`），避免了timestamp依赖问题。**

### 使用简化版proto文件（推荐且已实现）

我们提供了 `account_simple.proto` 文件，使用 `int64` 类型的Unix时间戳替代 `google.protobuf.Timestamp`：

```bash
# 使用简化版proto文件生成代码
protoc --go_out=. --go-grpc_out=. --go-grpc_opt=require_unimplemented_servers=false ./account/account_simple.proto
```

**所有服务代码已经更新为使用Unix时间戳，无需额外修改。**

### 方案2：安装完整的Protocol Buffers

#### 1. 安装 Protocol Buffers 编译器

**macOS:**
```bash
brew install protobuf
```

**Ubuntu/Debian:**
```bash
sudo apt-get update
sudo apt-get install protobuf-compiler
```

**Windows:**
1. 从 [GitHub Releases](https://github.com/protocolbuffers/protobuf/releases) 下载最新版本
2. 解压到某个目录（如 `C:\protoc`）
3. 将 `C:\protoc\bin` 添加到系统 PATH 环境变量

**或使用 Chocolatey (Windows):**
```bash
choco install protoc
```

#### 2. 安装 Go 插件

```bash
go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest
```

#### 3. 验证安装

```bash
protoc --version
protoc-gen-go --version
protoc-gen-go-grpc --version
```

### 方案3：使用脚本自动生成

#### Linux/macOS:
```bash
chmod +x run.sh
./run.sh
```

#### Windows:
```bash
run.bat
```

## 生成的文件

成功执行后，会在 `account/` 目录下生成以下文件：
- `account.pb.go` - Protocol Buffers 消息定义
- `account_grpc.pb.go` - gRPC 服务定义

## 时间戳处理

### 使用 google.protobuf.Timestamp (原版)
```go
import "google.golang.org/protobuf/types/known/timestamppb"

// 转换 time.Time 到 protobuf Timestamp
pbTime := timestamppb.New(time.Now())

// 转换 protobuf Timestamp 到 time.Time
goTime := pbTime.AsTime()
```

### 使用 int64 Unix时间戳 (简化版)
```go
// 转换 time.Time 到 Unix时间戳
unixTime := time.Now().Unix()

// 转换 Unix时间戳到 time.Time
goTime := time.Unix(unixTime, 0)
```

## 推荐使用简化版

为了避免依赖问题，建议使用 `account_simple.proto`：

1. **更简单**: 不需要额外的依赖
2. **更兼容**: 避免版本兼容性问题
3. **更高效**: Unix时间戳占用空间更小

## 手动生成命令

如果自动脚本失败，可以手动执行：

```bash
# 生成简化版
protoc --go_out=. --go-grpc_out=. --go-grpc_opt=require_unimplemented_servers=false ./account/account_simple.proto

# 生成完整版（需要正确安装protobuf）
protoc --go_out=. --go-grpc_out=. --go-grpc_opt=require_unimplemented_servers=false ./account/account.proto
```

## 常见问题

### Q: 提示找不到 protoc 命令
A: 请按照上述方法安装 Protocol Buffers 编译器

### Q: 提示找不到 protoc-gen-go
A: 执行 `go install google.golang.org/protobuf/cmd/protoc-gen-go@latest`

### Q: 仍然提示 google.protobuf.Timestamp 未定义
A: 使用 `account_simple.proto` 文件，或确保 protoc 版本 >= 3.0

### Q: 生成的代码在哪里？
A: 在 `account/` 目录下，文件名为 `*.pb.go` 和 `*_grpc.pb.go`

## 集成到项目

生成代码后，需要在项目中导入：

```go
import pb "creativematrix.com/beyondreading/gen/proto/account"
```

确保在项目根目录的 `go.mod` 中模块名正确：
```
module creativematrix.com/beyondreading
```
