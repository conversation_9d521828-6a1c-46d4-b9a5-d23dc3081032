syntax = "proto3";
package purchase;
option go_package = "creativematrix.com/beyondreading/gen/proto/purchase";

service Purchase {
  // 购买章节（支持批量购买）
  rpc PurchaseChapter(PurchaseChapterReq) returns (PurchaseChapterResp);

  // 购买包月
  rpc PurchaseMonthly(PurchaseMonthlyReq) returns (PurchaseMonthlyResp);

  // 购买VIP
  rpc PurchaseVip(PurchaseVipReq) returns (PurchaseVipResp);

  // 获取购买订单列表
  rpc GetPurchaseOrders(GetPurchaseOrdersReq) returns (GetPurchaseOrdersResp);

  // 获取VIP/包月订单列表
  rpc GetVipMonthlyOrders(GetVipMonthlyOrdersReq) returns (GetVipMonthlyOrdersResp);

  // 检查章节购买状态
  rpc CheckChapterPurchased(CheckChapterPurchasedReq) returns (CheckChapterPurchasedResp);

  // 检查VIP状态
  rpc CheckVipStatus(CheckVipStatusReq) returns (CheckVipStatusResp);

  // 检查包月状态
  rpc CheckMonthlyStatus(CheckMonthlyStatusReq) returns (CheckMonthlyStatusResp);

  // 获取用户已购买的章节列表
  rpc GetPurchasedChapters(GetPurchasedChaptersReq) returns (GetPurchasedChaptersResp);
}

// 购买订单（章节）
message PurchaseOrder {
  string order_id = 1;
  uint64 account_id = 2;
  uint64 user_id = 3;
  string order_type = 4;          // 订单类型：chapter
  string book_id = 5;
  string book_name = 6;
  string chapter_id = 7;
  string chapter_title = 8;
  uint32 chapter_order = 9;
  double coin_amount = 10;        // 消费书币数量
  int32 status = 11;              // 订单状态：1-待支付，2-支付成功，3-支付失败
  int64 created_at = 12;
  int64 updated_at = 13;
}

// VIP/包月订单
message VipMonthlyOrder {
  string order_id = 1;
  uint64 account_id = 2;
  uint64 user_id = 3;
  string order_type = 4;          // 订单类型：monthly, vip
  double coin_amount = 5;         // 消费书币数量
  int32 duration_days = 6;        // 有效期天数
  int64 start_time = 7;           // 开始时间
  int64 end_time = 8;             // 结束时间
  int32 status = 9;               // 订单状态：1-待支付，2-支付成功，3-支付失败
  int64 created_at = 10;
  int64 updated_at = 11;
}

// 章节购买信息
message ChapterPurchaseInfo {
  string order_id = 1;
  string chapter_id = 2;
  string chapter_title = 3;
  uint32 chapter_order = 4;
  double coin_amount = 5;
  int64 purchased_at = 6;
  bool is_monthly = 7;            // 是否通过包月获得
  bool is_vip = 8;                // 是否通过VIP获得
}

// 批量章节购买项
message ChapterPurchaseItem {
  uint32 chapter_order = 1;
  double coin_amount = 2;
}

// 请求和响应消息

// 购买章节（支持批量）
message PurchaseChapterReq {
  uint64 user_id = 1;
  string book_id = 2;
  repeated ChapterPurchaseItem chapters = 3;  // 支持批量购买
}

message PurchaseChapterResp {
  int32 code = 1;
  string message = 2;
  repeated PurchaseOrder orders = 3;          // 返回多个订单
}

// 购买包月
message PurchaseMonthlyReq {
  uint64 user_id = 1;
  double coin_amount = 2;         // 消费书币数量
  int32 duration_days = 3;        // 包月天数，默认30天
}

message PurchaseMonthlyResp {
  int32 code = 1;
  string message = 2;
  VipMonthlyOrder order = 3;
}

// 购买VIP
message PurchaseVipReq {
  uint64 user_id = 1;
  double coin_amount = 2;         // 消费书币数量
  int32 duration_days = 3;        // VIP天数
}

message PurchaseVipResp {
  int32 code = 1;
  string message = 2;
  VipMonthlyOrder order = 3;
}

// 获取购买订单列表（章节）
message GetPurchaseOrdersReq {
  uint64 user_id = 1;
  int32 page = 2;
  int32 page_size = 3;
  string book_id = 4;             // 可选，筛选特定书籍
}

message GetPurchaseOrdersResp {
  int32 code = 1;
  string message = 2;
  repeated PurchaseOrder orders = 3;
  int64 total = 4;
}

// 获取VIP/包月订单列表
message GetVipMonthlyOrdersReq {
  uint64 user_id = 1;
  int32 page = 2;
  int32 page_size = 3;
  string order_type = 4;          // 可选，筛选订单类型：monthly, vip
}

message GetVipMonthlyOrdersResp {
  int32 code = 1;
  string message = 2;
  repeated VipMonthlyOrder orders = 3;
  int64 total = 4;
}

// 检查章节购买状态
message CheckChapterPurchasedReq {
  uint64 user_id = 1;
  string book_id = 2;
  uint32 chapter_order = 3;
}

message CheckChapterPurchasedResp {
  int32 code = 1;
  string message = 2;
  bool is_purchased = 3;
  int64 purchased_at = 4;
  bool is_monthly = 5;            // 是否通过包月获得
  bool is_vip = 6;                // 是否通过VIP获得
  string order_id = 7;
}

// 检查VIP状态
message CheckVipStatusReq {
  uint64 user_id = 1;
}

message CheckVipStatusResp {
  int32 code = 1;
  string message = 2;
  bool is_active = 3;
  int64 start_time = 4;
  int64 end_time = 5;
  string order_id = 6;
}

// 检查包月状态
message CheckMonthlyStatusReq {
  uint64 user_id = 1;
}

message CheckMonthlyStatusResp {
  int32 code = 1;
  string message = 2;
  bool is_active = 3;
  int64 start_time = 4;
  int64 end_time = 5;
  string order_id = 6;
}

// 获取用户已购买的章节列表
message GetPurchasedChaptersReq {
  uint64 user_id = 1;
  string book_id = 2;
  int32 page = 3;
  int32 page_size = 4;
}

message GetPurchasedChaptersResp {
  int32 code = 1;
  string message = 2;
  repeated ChapterPurchaseInfo chapters = 3;
  int64 total = 4;
}
