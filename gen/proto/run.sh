
#!/bin/bash

# Protocol Buffers代码生成脚本

# 检查protoc是否安装
if ! command -v protoc &> /dev/null; then
    echo "protoc 未安装，请先安装 Protocol Buffers 编译器"
    echo "安装方法："
    echo "  macOS: brew install protobuf"
    echo "  Ubuntu: sudo apt-get install protobuf-compiler"
    echo "  Windows: 下载并安装 https://github.com/protocolbuffers/protobuf/releases"
    exit 1
fi

# 检查Go插件是否安装
if ! command -v protoc-gen-go &> /dev/null; then
    echo "protoc-gen-go 未安装，正在安装..."
    go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
fi

if ! command -v protoc-gen-go-grpc &> /dev/null; then
    echo "protoc-gen-go-grpc 未安装，正在安装..."
    go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest
fi

echo "正在生成 Protocol Buffers Go 代码..."

# 生成Go代码 - 使用简化版proto文件
protoc \
    --proto_path=. \
    --go_out=. \
    --go_opt=paths=source_relative \
    --go-grpc_out=. \
    --go-grpc_opt=paths=source_relative \
    --go-grpc_opt=require_unimplemented_servers=false \
    ./account/account_simple.proto

if [ $? -eq 0 ]; then
    echo "✅ Protocol Buffers 代码生成成功！"
    echo "生成的文件："
    ls -la ./account/*.go 2>/dev/null || echo "请检查 ./account/ 目录"
else
    echo "❌ Protocol Buffers 代码生成失败！"
    echo ""
    echo "请尝试手动运行："
    echo "protoc --go_out=. --go-grpc_out=. --go-grpc_opt=require_unimplemented_servers=false ./account/account_simple.proto"
fi
