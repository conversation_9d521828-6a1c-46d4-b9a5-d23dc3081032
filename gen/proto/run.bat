@echo off
REM Protocol Buffers代码生成脚本 (Windows)

echo 正在检查 protoc 是否安装...
where protoc >nul 2>nul
if %errorlevel% neq 0 (
    echo protoc 未安装，请先安装 Protocol Buffers 编译器
    echo 下载地址: https://github.com/protocolbuffers/protobuf/releases
    echo 或使用 chocolatey: choco install protoc
    pause
    exit /b 1
)

echo 正在检查 Go 插件是否安装...
where protoc-gen-go >nul 2>nul
if %errorlevel% neq 0 (
    echo protoc-gen-go 未安装，正在安装...
    go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
)

where protoc-gen-go-grpc >nul 2>nul
if %errorlevel% neq 0 (
    echo protoc-gen-go-grpc 未安装，正在安装...
    go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest
)

echo 正在生成 Protocol Buffers Go 代码...

REM 生成Go代码 - 使用简化版proto文件
protoc --proto_path=. --go_out=. --go_opt=paths=source_relative --go-grpc_out=. --go-grpc_opt=paths=source_relative --go-grpc_opt=require_unimplemented_servers=false ./account/account_simple.proto

if %errorlevel% equ 0 (
    echo ✅ Protocol Buffers 代码生成成功！
    echo 生成的文件：
    dir /b .\account\*.go 2>nul
) else (
    echo ❌ Protocol Buffers 代码生成失败！
    echo.
    echo 请尝试手动运行：
    echo protoc --go_out=. --go-grpc_out=. --go-grpc_opt=require_unimplemented_servers=false ./account/account_simple.proto
)

pause
