# BeyondReading Account微服务

基于现有BeyondReading项目架构生成的Account账户管理微服务，提供用户账户管理、充值、购买等功能。

## 功能特性

### 核心功能
- ✅ 用户账户管理（创建、查询、状态管理）
- ✅ 充值功能（支持多种支付方式）
- ✅ 购买功能（章节、包月、VIP）
- ✅ 账户日志记录（所有交易记录）
- ✅ 购买状态检查（章节购买状态、包月状态）
- ✅ 订单管理（充值订单、购买订单）

### 技术特性
- 🏗️ 微服务架构（gRPC + HTTP API）
- 💾 MySQL数据存储 + Redis缓存
- 🔒 事务安全保证（使用sqlx事务）
- 📊 分库分表支持
- 🚀 高性能缓存策略
- 📝 完整的日志记录
- 🔄 幂等性保证
- 🛠️ 使用sqlx进行数据库操作（与现有项目保持一致）
- 🧮 高精度小数运算（避免浮点数精度问题）
- ✅ 完整的数据验证和工具函数

## 项目结构

```
gen/
├── app/
│   ├── api/account/          # HTTP API服务
│   │   ├── cmd/             # 启动入口
│   │   ├── conf/            # 配置管理
│   │   ├── dao/             # 数据访问层
│   │   ├── http/            # HTTP处理器
│   │   ├── model/vo/        # 视图对象
│   │   └── svc/             # 业务逻辑层
│   └── base/account/         # gRPC微服务
│       ├── api/             # gRPC客户端
│       ├── cmd/             # 启动入口
│       ├── conf/            # 配置管理
│       ├── dao/             # 数据访问层
│       ├── grpc/            # gRPC服务器
│       ├── model/           # 数据模型
│       └── svc/             # 业务逻辑层
├── config/                   # 配置文件
├── database/                 # 数据库脚本
├── docs/                     # 文档
├── pkg/utils/                # 工具包（高精度小数、验证、订单生成等）
├── proto/account/            # Protocol Buffers定义
└── test/                     # 测试文件
```

## 数据库设计

### 核心表结构

#### 1. account - 用户账户表
```sql
CREATE TABLE `account` (
  `account_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` varchar(64) NOT NULL,
  `coin_balance` decimal(15,2) NOT NULL DEFAULT '0.00',
  `total_recharged` decimal(15,2) NOT NULL DEFAULT '0.00',
  `total_consumed` decimal(15,2) NOT NULL DEFAULT '0.00',
  `status` tinyint NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`account_id`),
  UNIQUE KEY `uk_user_id` (`user_id`)
);
```

#### 2. account_log - 账户日志表
```sql
CREATE TABLE `account_log` (
  `log_id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint unsigned NOT NULL,
  `user_id` varchar(64) NOT NULL,
  `transaction_type` varchar(32) NOT NULL,
  `amount` decimal(15,2) NOT NULL,
  `balance_before` decimal(15,2) NOT NULL,
  `balance_after` decimal(15,2) NOT NULL,
  `order_id` varchar(64) DEFAULT NULL,
  `book_id` varchar(64) DEFAULT NULL,
  `chapter_id` varchar(64) DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `extra_data` json DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`log_id`)
);
```

#### 3. recharge_order - 充值订单表
```sql
CREATE TABLE `recharge_order` (
  `order_id` varchar(64) NOT NULL,
  `account_id` bigint unsigned NOT NULL,
  `user_id` varchar(64) NOT NULL,
  `amount` decimal(15,2) NOT NULL,
  `coin_amount` decimal(15,2) NOT NULL,
  `exchange_rate` decimal(10,4) NOT NULL DEFAULT '1.0000',
  `payment_method` varchar(32) NOT NULL,
  `payment_order_id` varchar(128) DEFAULT NULL,
  `status` tinyint NOT NULL DEFAULT '1',
  `paid_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`order_id`)
);
```

#### 4. purchase_order - 购买订单表
```sql
CREATE TABLE `purchase_order` (
  `order_id` varchar(64) NOT NULL,
  `account_id` bigint unsigned NOT NULL,
  `user_id` varchar(64) NOT NULL,
  `order_type` varchar(32) NOT NULL,
  `book_id` varchar(64) DEFAULT NULL,
  `book_name` varchar(128) DEFAULT NULL,
  `chapter_id` varchar(64) DEFAULT NULL,
  `chapter_title` varchar(128) DEFAULT NULL,
  `chapter_order` int unsigned DEFAULT NULL,
  `coin_amount` decimal(15,2) NOT NULL,
  `duration_days` int DEFAULT NULL,
  `start_time` timestamp NULL DEFAULT NULL,
  `end_time` timestamp NULL DEFAULT NULL,
  `status` tinyint NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`order_id`)
);
```

## API接口

### HTTP API
- `GET /account/info` - 获取账户信息
- `POST /account/recharge` - 充值
- `POST /account/purchase/chapter` - 购买章节
- `POST /account/purchase/monthly` - 购买包月
- `POST /account/purchase/vip` - 购买VIP
- `GET /account/logs` - 获取账户日志
- `GET /account/purchase/orders` - 获取购买订单
- `GET /account/check/chapter` - 检查章节购买状态
- `GET /account/check/monthly` - 检查包月状态

### gRPC API
详见 `proto/account/account.proto` 文件定义

## 快速开始

### 1. 环境准备
```bash
# 安装依赖
go mod tidy

# 启动MySQL
docker run -d --name mysql -p 3306:3306 -e MYSQL_ROOT_PASSWORD=password mysql:8.0

# 启动Redis
docker run -d --name redis -p 6379:6379 redis:7.0

# 启动etcd
docker run -d --name etcd -p 2379:2379 -p 2380:2380 quay.io/coreos/etcd:v3.5.0
```

### 2. 数据库初始化
```bash
mysql -u root -p < gen/database/account_schema.sql
```

### 3. 配置文件
将 `gen/config/account.toml` 复制到项目根目录的 `app/` 目录下，并根据实际环境调整配置。

### 4. 启动服务

#### 启动Base服务（gRPC）
```bash
cd gen/app/base/account/cmd
go run main.go
```

#### 启动API服务（HTTP）
```bash
cd gen/app/api/account/cmd
go run main.go
```

### 5. 测试接口
```bash
# 获取账户信息
curl "http://localhost:8081/account/info?userId=test_user_001"

# 充值
curl -X POST "http://localhost:8081/account/recharge" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "test_user_001",
    "amount": "100.00",
    "paymentMethod": "alipay"
  }'

# 购买章节
curl -X POST "http://localhost:8081/account/purchase/chapter" \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "test_user_001",
    "bookId": "book_001",
    "bookName": "测试书籍",
    "chapterId": "chapter_001",
    "chapterTitle": "第一章",
    "chapterOrder": 1,
    "coinAmount": "5.00"
  }'
```

## 运行测试

```bash
# 运行单元测试
cd gen/test
go test -v

# 运行基准测试
go test -bench=.

# 运行覆盖率测试
go test -cover
```

## 部署说明

### Docker部署
```dockerfile
# 构建Base服务
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY . .
RUN go build -o account-base ./gen/app/base/account/cmd

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/account-base .
CMD ["./account-base"]
```

### Kubernetes部署
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: account-base
spec:
  replicas: 3
  selector:
    matchLabels:
      app: account-base
  template:
    metadata:
      labels:
        app: account-base
    spec:
      containers:
      - name: account-base
        image: beyondreading/account-base:latest
        ports:
        - containerPort: 8083
```

## 监控和运维

### 健康检查
```bash
# 检查Base服务
grpc_health_probe -addr=localhost:8083

# 检查API服务
curl http://localhost:8081/health
```

### 日志监控
- 使用ELK Stack收集和分析日志
- 关键指标：QPS、响应时间、错误率
- 业务指标：充值金额、购买次数、用户活跃度

### 告警配置
- 数据库连接异常
- Redis连接异常
- 账户余额异常变动
- 大额交易告警

## 扩展功能

### 已规划功能
- [ ] 支付回调处理
- [ ] 退款功能
- [ ] 优惠券系统
- [ ] 积分系统
- [ ] 风控系统
- [ ] 数据统计和报表

### 性能优化
- [ ] 读写分离
- [ ] 分库分表优化
- [ ] 缓存预热
- [ ] 异步处理

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请提交 Issue 或联系开发团队。
