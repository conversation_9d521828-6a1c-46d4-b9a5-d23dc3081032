package utils

import (
	"fmt"
	"math/rand"
	"time"
)

// GenerateOrderId 生成订单ID
// prefix: 订单前缀，如 "RC"(充值), "PC"(购买章节), "PM"(包月), "PV"(VIP)
func GenerateOrderId(prefix string) string {
	now := time.Now()
	
	// 格式: 前缀 + 年月日时分秒 + 6位随机数
	// 例如: RC20240101120000123456
	timestamp := now.Format("20060102150405")
	
	// 生成6位随机数
	rand.Seed(now.UnixNano())
	randomNum := rand.Intn(999999-100000) + 100000
	
	return fmt.Sprintf("%s%s%06d", prefix, timestamp, randomNum)
}

// GenerateTransactionId 生成交易ID（用于日志记录）
func GenerateTransactionId() string {
	now := time.Now()
	
	// 格式: TXN + 年月日时分秒毫秒 + 4位随机数
	// 例如: TXN20240101120000123456
	timestamp := now.Format("20060102150405")
	millisecond := now.Nanosecond() / 1000000
	
	// 生成4位随机数
	rand.Seed(now.UnixNano())
	randomNum := rand.Intn(9999-1000) + 1000
	
	return fmt.Sprintf("TXN%s%03d%04d", timestamp, millisecond, randomNum)
}

// ValidateOrderId 验证订单ID格式
func ValidateOrderId(orderId string) bool {
	if len(orderId) < 16 {
		return false
	}
	
	// 检查前缀
	validPrefixes := []string{"RC", "PC", "PM", "PV", "TXN"}
	hasValidPrefix := false
	for _, prefix := range validPrefixes {
		if len(orderId) >= len(prefix) && orderId[:len(prefix)] == prefix {
			hasValidPrefix = true
			break
		}
	}
	
	return hasValidPrefix
}

// GetOrderType 从订单ID获取订单类型
func GetOrderType(orderId string) string {
	if len(orderId) < 2 {
		return "unknown"
	}
	
	prefix := orderId[:2]
	switch prefix {
	case "RC":
		return "recharge"
	case "PC":
		return "purchase_chapter"
	case "PM":
		return "purchase_monthly"
	case "PV":
		return "purchase_vip"
	default:
		if len(orderId) >= 3 && orderId[:3] == "TXN" {
			return "transaction"
		}
		return "unknown"
	}
}
