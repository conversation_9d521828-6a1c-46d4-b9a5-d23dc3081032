# Utils Package - Account微服务工具包

## 概述

这个utils包专门为Account微服务设计，提供了高精度小数运算、数据验证、字符串处理、订单生成等功能。

## 主要功能模块

### 1. 高精度小数运算 (decimal.go)

用于处理金额计算，避免浮点数精度问题。

#### 基本用法

```go
import "creativematrix.com/beyondreading/gen/pkg/utils"

// 解析字符串为Decimal
amount, err := utils.ParseDecimal("123.45")
if err != nil {
    // 处理错误
}

// 基本运算
a, _ := utils.ParseDecimal("10.50")
b, _ := utils.ParseDecimal("5.25")

sum := a.Add(b)        // 15.75
diff := a.Sub(b)       // 5.25
product := a.Mul(b)    // 55.13
quotient, _ := a.Div(b) // 2.00

// 比较操作
if a.<PERSON>(b) {
    fmt.Println("a > b")
}

// 转换为字符串
fmt.Println(sum.String())                    // "15.75"
fmt.Println(sum.StringWithPrecision(4))      // "15.7500"
```

#### 主要方法

- `ParseDecimal(string) (*Decimal, error)` - 解析字符串
- `Add(*Decimal) *Decimal` - 加法
- `Sub(*Decimal) *Decimal` - 减法
- `Mul(*Decimal) *Decimal` - 乘法
- `Div(*Decimal) (*Decimal, error)` - 除法
- `GreaterThan(*Decimal) bool` - 大于比较
- `LessThan(*Decimal) bool` - 小于比较
- `Equal(*Decimal) bool` - 相等比较
- `IsZero() bool` - 是否为零
- `IsPositive() bool` - 是否为正数
- `IsNegative() bool` - 是否为负数

### 2. 订单生成 (order.go)

生成唯一的订单ID和交易ID。

```go
// 生成充值订单ID
rechargeOrderId := utils.GenerateOrderId("RC")  // RC20240101120000123456

// 生成购买章节订单ID
chapterOrderId := utils.GenerateOrderId("PC")   // PC20240101120000123456

// 生成包月订单ID
monthlyOrderId := utils.GenerateOrderId("PM")   // PM20240101120000123456

// 生成VIP订单ID
vipOrderId := utils.GenerateOrderId("PV")       // PV20240101120000123456

// 生成交易ID
transactionId := utils.GenerateTransactionId()  // TXN20240101120000123456

// 验证订单ID格式
isValid := utils.ValidateOrderId(rechargeOrderId)

// 获取订单类型
orderType := utils.GetOrderType(rechargeOrderId) // "recharge"
```

### 3. 数据验证 (validator.go)

提供各种数据格式验证功能。

```go
// 用户ID验证（24位ObjectId）
isValid := utils.ValidateUserId("61668aad169d3aecb0ff0af4")

// 金额验证
isValid = utils.ValidateAmount("123.45")
isValid = utils.ValidatePositiveAmount("123.45")  // 必须为正数
isValid = utils.ValidateNonNegativeAmount("0")    // 非负数

// 支付方式验证
isValid = utils.ValidatePaymentMethod("alipay")   // alipay, wechat, apple, bank

// 订单类型验证
isValid = utils.ValidateOrderType("chapter")      // chapter, monthly, vip

// 兑换比例验证
isValid = utils.ValidateExchangeRate("1.0")       // 0 < rate <= 100

// 分页参数验证
isValid = utils.ValidatePage(1)                   // page > 0
isValid = utils.ValidatePageSize(20)              // 0 < pageSize <= 100

// 邮箱验证
isValid = utils.ValidateEmail("<EMAIL>")

// 手机号验证（中国大陆）
isValid = utils.ValidatePhone("***********")

// 密码强度验证
isValid = utils.ValidatePassword("abc123")        // 6-20位，包含字母和数字

// 金额范围验证
isValid = utils.ValidateAmountRange("50", "10", "100")

// 字符串清理（防XSS和SQL注入）
cleaned := utils.SanitizeString("<script>alert('xss')</script>")
```

### 4. 字符串工具 (string.go)

提供指针操作、JSON处理、格式化等功能。

```go
// 指针操作
strPtr := utils.StringPtr("hello")
intPtr := utils.IntPtr(123)
boolPtr := utils.BoolPtr(true)

// 安全获取指针值
str := utils.StringValue(strPtr)    // "hello"
num := utils.IntValue(intPtr)       // 123
flag := utils.BoolValue(boolPtr)    // true

// 字符串检查
isEmpty := utils.IsEmpty("")        // true
isNotEmpty := utils.IsNotEmpty("hello") // true

// 默认值
result := utils.DefaultString("", "default") // "default"

// JSON操作
data, err := utils.JsonMarshal(struct{Name string}{Name: "test"})
jsonStr, err := utils.JsonMarshalString(data)
err = utils.JsonUnmarshal(data, &target)
err = utils.JsonUnmarshalString(jsonStr, &target)

// 金额格式化
formatted := utils.FormatMoney("123.45")           // "123.45"
withSymbol := utils.FormatMoneyWithSymbol("123.45", "¥") // "¥123.45"
```

### 5. 错误处理 (error.go)

自定义错误类型和预定义错误码。

```go
// 创建自定义错误
err := utils.NewError(400, "请求参数错误")
err = utils.NewErrorWithCode(utils.CodeBadRequest)

// 预定义错误
err = utils.ErrBadRequest("用户ID不能为空")
err = utils.ErrPaymentRequired("书币余额不足")
err = utils.ErrNotFound("账户不存在")
err = utils.ErrConflict("重复购买")
err = utils.ErrInternalServer("服务器内部错误")

// 业务错误
err = utils.ErrAccountNotFound()
err = utils.ErrInsufficientBalance()
err = utils.ErrInvalidAmount()
err = utils.ErrDuplicatePurchase()

// 错误检查
if customErr, ok := utils.IsError(err); ok {
    fmt.Printf("Code: %d, Message: %s", customErr.Code, customErr.Message)
}

// 包装标准错误
wrappedErr := utils.WrapError(err, utils.CodeInternalServerError)
```

### 6. 通用工具 (utils.go)

提供各种实用工具函数。

```go
// 网络相关
ip := utils.GetLocalIP()
ip = utils.InternalIP()  // 兼容现有项目

// 哈希和随机
hash := utils.MD5("hello world")
randomStr := utils.RandomString(10)
randomNum := utils.RandomNumber(6)

// 环境变量
value := utils.GetEnv("PORT", "8080")
port := utils.GetEnvInt("PORT", 8080)
debug := utils.GetEnvBool("DEBUG", false)

// 切片操作
contains := utils.Contains([]string{"a", "b", "c"}, "b")
containsInt := utils.ContainsInt([]int{1, 2, 3}, 2)
unique := utils.RemoveDuplicates([]string{"a", "b", "a", "c"})

// 字符串操作
truncated := utils.TruncateString("hello world", 5)  // "hello..."
padded := utils.PadLeft("123", 5, "0")               // "00123"

// 数学操作
min := utils.Min(10, 20)        // 10
max := utils.Max(10, 20)        // 20
abs := utils.Abs(-10)           // 10

// 条件操作
result := utils.IfString(true, "yes", "no")  // "yes"
result = utils.IfInt(false, 1, 2)            // 2

// 重试机制
err := utils.Retry(3, time.Second, func() error {
    // 可能失败的操作
    return nil
})

// 安全的goroutine
utils.SafeGoroutine(func() {
    // 可能panic的操作
})
```

## 在Account微服务中的使用

### DAO层使用示例

```go
// dao/account.go
func (d *Dao) UpdateAccountBalance(ctx context.Context, req *model.TransactionRequest) error {
    // 解析金额
    amount, err := utils.ParseDecimal(req.Amount)
    if err != nil {
        return utils.ErrInvalidAmount()
    }
    
    // 计算新余额
    currentBalance, _ := utils.ParseDecimal(account.CoinBalance)
    newBalance := currentBalance.Add(amount)
    
    // 检查余额
    if amount.IsNegative() && newBalance.IsNegative() {
        return utils.ErrInsufficientBalance()
    }
    
    // 更新数据库...
}
```

### Service层使用示例

```go
// svc/account.go
func (s *AccountSvc) Recharge(ctx context.Context, req *pb.RechargeReq) (*pb.RechargeResp, error) {
    // 验证参数
    if !utils.ValidateUserId(req.UserId) {
        return nil, utils.ErrBadRequest("用户ID格式错误")
    }
    
    if !utils.ValidatePositiveAmount(req.Amount) {
        return nil, utils.ErrBadRequest("充值金额必须大于0")
    }
    
    if !utils.ValidatePaymentMethod(req.PaymentMethod) {
        return nil, utils.ErrBadRequest("不支持的支付方式")
    }
    
    // 生成订单ID
    orderId := utils.GenerateOrderId("RC")
    
    // 处理业务逻辑...
}
```

## 测试

运行测试：

```bash
cd gen/pkg/utils
go test -v
go test -bench=.
go test -cover
```

## 注意事项

1. **精度问题**: 所有金额计算都使用Decimal类型，避免float64精度问题
2. **验证顺序**: 先进行格式验证，再进行业务逻辑验证
3. **错误处理**: 使用统一的错误码和错误消息
4. **性能考虑**: Decimal运算比float64慢，但保证精度
5. **线程安全**: 所有函数都是线程安全的

## 扩展

如需添加新的工具函数，请：

1. 在对应的文件中添加函数
2. 编写相应的测试用例
3. 更新README文档
4. 确保与现有项目风格一致
