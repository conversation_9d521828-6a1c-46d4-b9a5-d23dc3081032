package utils

import (
	"encoding/json"
	"fmt"
)

// StringPtr 返回字符串指针
func StringPtr(s string) *string {
	return &s
}

// IntPtr 返回int指针
func IntPtr(i int) *int {
	return &i
}

// Int32Ptr 返回int32指针
func Int32Ptr(i int32) *int32 {
	return &i
}

// Int64Ptr 返回int64指针
func Int64Ptr(i int64) *int64 {
	return &i
}

// Uint32Ptr 返回uint32指针
func Uint32Ptr(i uint32) *uint32 {
	return &i
}

// Uint64Ptr 返回uint64指针
func Uint64Ptr(i uint64) *uint64 {
	return &i
}

// BoolPtr 返回bool指针
func BoolPtr(b bool) *bool {
	return &b
}

// StringValue 安全获取字符串指针的值
func StringValue(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

// IntValue 安全获取int指针的值
func IntValue(i *int) int {
	if i == nil {
		return 0
	}
	return *i
}

// Int32Value 安全获取int32指针的值
func Int32Value(i *int32) int32 {
	if i == nil {
		return 0
	}
	return *i
}

// Int64Value 安全获取int64指针的值
func Int64Value(i *int64) int64 {
	if i == nil {
		return 0
	}
	return *i
}

// Uint32Value 安全获取uint32指针的值
func Uint32Value(i *uint32) uint32 {
	if i == nil {
		return 0
	}
	return *i
}

// Uint64Value 安全获取uint64指针的值
func Uint64Value(i *uint64) uint64 {
	if i == nil {
		return 0
	}
	return *i
}

// BoolValue 安全获取bool指针的值
func BoolValue(b *bool) bool {
	if b == nil {
		return false
	}
	return *b
}

// IsEmpty 检查字符串是否为空
func IsEmpty(s string) bool {
	return len(s) == 0
}

// IsNotEmpty 检查字符串是否不为空
func IsNotEmpty(s string) bool {
	return len(s) > 0
}

// DefaultString 如果字符串为空则返回默认值
func DefaultString(s, defaultValue string) string {
	if IsEmpty(s) {
		return defaultValue
	}
	return s
}

// JsonMarshal JSON序列化
func JsonMarshal(v interface{}) ([]byte, error) {
	return json.Marshal(v)
}

// JsonUnmarshal JSON反序列化
func JsonUnmarshal(data []byte, v interface{}) error {
	return json.Unmarshal(data, v)
}

// JsonMarshalString JSON序列化为字符串
func JsonMarshalString(v interface{}) (string, error) {
	data, err := json.Marshal(v)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// JsonUnmarshalString 从字符串JSON反序列化
func JsonUnmarshalString(data string, v interface{}) error {
	return json.Unmarshal([]byte(data), v)
}

// FormatMoney 格式化金额显示
func FormatMoney(amount string) string {
	if amount == "" {
		return "0.00"
	}
	
	decimal, err := ParseDecimal(amount)
	if err != nil {
		return "0.00"
	}
	
	return decimal.String()
}

// FormatMoneyWithSymbol 格式化金额显示（带符号）
func FormatMoneyWithSymbol(amount string, symbol string) string {
	if symbol == "" {
		symbol = "¥"
	}
	return fmt.Sprintf("%s%s", symbol, FormatMoney(amount))
}
