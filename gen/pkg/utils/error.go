package utils

import "fmt"

// Error 自定义错误类型
type Error struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// Error 实现error接口
func (e *Error) Error() string {
	return fmt.Sprintf("code: %d, message: %s", e.Code, e.Message)
}

// NewError 创建新的错误
func NewError(code int, message string) *Error {
	return &Error{
		Code:    code,
		Message: message,
	}
}

// 预定义的错误码
const (
	// 成功
	CodeSuccess = 200

	// 客户端错误 4xx
	CodeBadRequest          = 400
	CodeUnauthorized        = 401
	CodePaymentRequired     = 402
	CodeForbidden           = 403
	CodeNotFound            = 404
	CodeMethodNotAllowed    = 405
	CodeConflict            = 409
	CodeUnprocessableEntity = 422

	// 服务器错误 5xx
	CodeInternalServerError = 500
	CodeNotImplemented      = 501
	CodeBadGateway          = 502
	CodeServiceUnavailable  = 503
	CodeGatewayTimeout      = 504

	// 业务错误码 6xxx
	CodeAccountNotFound     = 6001
	CodeInsufficientBalance = 6002
	CodeAccountFrozen       = 6003
	CodeAccountClosed       = 6004
	CodeInvalidAmount       = 6005
	CodeOrderNotFound       = 6006
	CodeOrderExpired        = 6007
	CodeOrderPaid           = 6008
	CodeDuplicatePurchase   = 6009
	CodePaymentFailed       = 6010
)

// 预定义的错误消息
var ErrorMessages = map[int]string{
	CodeSuccess:             "成功",
	CodeBadRequest:          "请求参数错误",
	CodeUnauthorized:        "未授权",
	CodePaymentRequired:     "余额不足",
	CodeForbidden:           "禁止访问",
	CodeNotFound:            "资源不存在",
	CodeMethodNotAllowed:    "方法不允许",
	CodeConflict:            "资源冲突",
	CodeUnprocessableEntity: "无法处理的实体",
	CodeInternalServerError: "服务器内部错误",
	CodeNotImplemented:      "功能未实现",
	CodeBadGateway:          "网关错误",
	CodeServiceUnavailable:  "服务不可用",
	CodeGatewayTimeout:      "网关超时",
	CodeAccountNotFound:     "账户不存在",
	CodeInsufficientBalance: "书币余额不足",
	CodeAccountFrozen:       "账户已冻结",
	CodeAccountClosed:       "账户已注销",
	CodeInvalidAmount:       "金额无效",
	CodeOrderNotFound:       "订单不存在",
	CodeOrderExpired:        "订单已过期",
	CodeOrderPaid:           "订单已支付",
	CodeDuplicatePurchase:   "重复购买",
	CodePaymentFailed:       "支付失败",
}

// NewErrorWithCode 根据错误码创建错误
func NewErrorWithCode(code int) *Error {
	message, exists := ErrorMessages[code]
	if !exists {
		message = "未知错误"
	}
	return &Error{
		Code:    code,
		Message: message,
	}
}

// NewErrorWithCodeAndMessage 根据错误码和自定义消息创建错误
func NewErrorWithCodeAndMessage(code int, message string) *Error {
	return &Error{
		Code:    code,
		Message: message,
	}
}

// IsError 检查是否为自定义错误
func IsError(err error) (*Error, bool) {
	if e, ok := err.(*Error); ok {
		return e, true
	}
	return nil, false
}

// WrapError 包装标准错误为自定义错误
func WrapError(err error, code int) *Error {
	if err == nil {
		return nil
	}
	
	if e, ok := IsError(err); ok {
		return e
	}
	
	return &Error{
		Code:    code,
		Message: err.Error(),
	}
}

// 常用错误创建函数
func ErrBadRequest(message string) *Error {
	if message == "" {
		message = ErrorMessages[CodeBadRequest]
	}
	return NewErrorWithCodeAndMessage(CodeBadRequest, message)
}

func ErrUnauthorized(message string) *Error {
	if message == "" {
		message = ErrorMessages[CodeUnauthorized]
	}
	return NewErrorWithCodeAndMessage(CodeUnauthorized, message)
}

func ErrPaymentRequired(message string) *Error {
	if message == "" {
		message = ErrorMessages[CodePaymentRequired]
	}
	return NewErrorWithCodeAndMessage(CodePaymentRequired, message)
}

func ErrNotFound(message string) *Error {
	if message == "" {
		message = ErrorMessages[CodeNotFound]
	}
	return NewErrorWithCodeAndMessage(CodeNotFound, message)
}

func ErrConflict(message string) *Error {
	if message == "" {
		message = ErrorMessages[CodeConflict]
	}
	return NewErrorWithCodeAndMessage(CodeConflict, message)
}

func ErrInternalServer(message string) *Error {
	if message == "" {
		message = ErrorMessages[CodeInternalServerError]
	}
	return NewErrorWithCodeAndMessage(CodeInternalServerError, message)
}

func ErrAccountNotFound() *Error {
	return NewErrorWithCode(CodeAccountNotFound)
}

func ErrInsufficientBalance() *Error {
	return NewErrorWithCode(CodeInsufficientBalance)
}

func ErrAccountFrozen() *Error {
	return NewErrorWithCode(CodeAccountFrozen)
}

func ErrInvalidAmount() *Error {
	return NewErrorWithCode(CodeInvalidAmount)
}

func ErrOrderNotFound() *Error {
	return NewErrorWithCode(CodeOrderNotFound)
}

func ErrDuplicatePurchase() *Error {
	return NewErrorWithCode(CodeDuplicatePurchase)
}
