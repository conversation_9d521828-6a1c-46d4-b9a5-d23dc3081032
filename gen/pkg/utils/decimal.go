package utils

import (
	"errors"
	"fmt"
	"math/big"
	"strconv"
	"strings"
)

// Decimal 高精度小数类型，用于金额计算
type Decimal struct {
	value *big.Rat
}

// NewDecimal 创建新的Decimal
func NewDecimal(value string) (*Decimal, error) {
	rat := new(big.Rat)
	_, ok := rat.SetString(value)
	if !ok {
		return nil, fmt.Errorf("invalid decimal value: %s", value)
	}
	return &Decimal{value: rat}, nil
}

// NewDecimalFromFloat 从float64创建Decimal
func NewDecimalFromFloat(value float64) *Decimal {
	rat := new(big.Rat)
	rat.SetFloat64(value)
	return &Decimal{value: rat}
}

// NewDecimalFromInt 从int64创建Decimal
func NewDecimalFromInt(value int64) *Decimal {
	rat := new(big.Rat)
	rat.SetInt64(value)
	return &Decimal{value: rat}
}

// ParseDecimal 解析字符串为Decimal
func ParseDecimal(value string) (*Decimal, error) {
	if value == "" {
		return NewDecimal("0"), nil
	}
	
	// 去除空格
	value = strings.TrimSpace(value)
	
	// 检查是否为负数
	isNegative := strings.HasPrefix(value, "-")
	if isNegative {
		value = value[1:]
	}
	
	// 验证格式
	if !isValidDecimalString(value) {
		return nil, fmt.Errorf("invalid decimal format: %s", value)
	}
	
	rat := new(big.Rat)
	_, ok := rat.SetString(value)
	if !ok {
		return nil, fmt.Errorf("failed to parse decimal: %s", value)
	}
	
	if isNegative {
		rat.Neg(rat)
	}
	
	return &Decimal{value: rat}, nil
}

// String 转换为字符串，保留2位小数
func (d *Decimal) String() string {
	if d.value == nil {
		return "0.00"
	}
	
	// 转换为float64然后格式化为2位小数
	f, _ := d.value.Float64()
	return fmt.Sprintf("%.2f", f)
}

// StringWithPrecision 转换为字符串，指定小数位数
func (d *Decimal) StringWithPrecision(precision int) string {
	if d.value == nil {
		return "0.00"
	}
	
	f, _ := d.value.Float64()
	format := fmt.Sprintf("%%.%df", precision)
	return fmt.Sprintf(format, f)
}

// Add 加法
func (d *Decimal) Add(other *Decimal) *Decimal {
	if d.value == nil || other.value == nil {
		return NewDecimalFromInt(0)
	}
	
	result := new(big.Rat)
	result.Add(d.value, other.value)
	return &Decimal{value: result}
}

// Sub 减法
func (d *Decimal) Sub(other *Decimal) *Decimal {
	if d.value == nil || other.value == nil {
		return NewDecimalFromInt(0)
	}
	
	result := new(big.Rat)
	result.Sub(d.value, other.value)
	return &Decimal{value: result}
}

// Mul 乘法
func (d *Decimal) Mul(other *Decimal) *Decimal {
	if d.value == nil || other.value == nil {
		return NewDecimalFromInt(0)
	}
	
	result := new(big.Rat)
	result.Mul(d.value, other.value)
	return &Decimal{value: result}
}

// Div 除法
func (d *Decimal) Div(other *Decimal) (*Decimal, error) {
	if d.value == nil || other.value == nil {
		return nil, errors.New("nil decimal value")
	}
	
	if other.value.Sign() == 0 {
		return nil, errors.New("division by zero")
	}
	
	result := new(big.Rat)
	result.Quo(d.value, other.value)
	return &Decimal{value: result}, nil
}

// IsZero 是否为零
func (d *Decimal) IsZero() bool {
	if d.value == nil {
		return true
	}
	return d.value.Sign() == 0
}

// IsNegative 是否为负数
func (d *Decimal) IsNegative() bool {
	if d.value == nil {
		return false
	}
	return d.value.Sign() < 0
}

// IsPositive 是否为正数
func (d *Decimal) IsPositive() bool {
	if d.value == nil {
		return false
	}
	return d.value.Sign() > 0
}

// LessThan 小于比较
func (d *Decimal) LessThan(other *Decimal) bool {
	if d.value == nil || other.value == nil {
		return false
	}
	return d.value.Cmp(other.value) < 0
}

// LessOrEqual 小于等于比较
func (d *Decimal) LessOrEqual(other *Decimal) bool {
	if d.value == nil || other.value == nil {
		return false
	}
	return d.value.Cmp(other.value) <= 0
}

// GreaterThan 大于比较
func (d *Decimal) GreaterThan(other *Decimal) bool {
	if d.value == nil || other.value == nil {
		return false
	}
	return d.value.Cmp(other.value) > 0
}

// GreaterOrEqual 大于等于比较
func (d *Decimal) GreaterOrEqual(other *Decimal) bool {
	if d.value == nil || other.value == nil {
		return false
	}
	return d.value.Cmp(other.value) >= 0
}

// Equal 相等比较
func (d *Decimal) Equal(other *Decimal) bool {
	if d.value == nil || other.value == nil {
		return d.value == nil && other.value == nil
	}
	return d.value.Cmp(other.value) == 0
}

// Float64 转换为float64
func (d *Decimal) Float64() (float64, bool) {
	if d.value == nil {
		return 0, false
	}
	return d.value.Float64()
}

// Copy 创建副本
func (d *Decimal) Copy() *Decimal {
	if d.value == nil {
		return NewDecimalFromInt(0)
	}
	
	result := new(big.Rat)
	result.Set(d.value)
	return &Decimal{value: result}
}

// Abs 绝对值
func (d *Decimal) Abs() *Decimal {
	if d.value == nil {
		return NewDecimalFromInt(0)
	}
	
	result := new(big.Rat)
	result.Abs(d.value)
	return &Decimal{value: result}
}

// Neg 取负值
func (d *Decimal) Neg() *Decimal {
	if d.value == nil {
		return NewDecimalFromInt(0)
	}
	
	result := new(big.Rat)
	result.Neg(d.value)
	return &Decimal{value: result}
}

// isValidDecimalString 验证小数字符串格式
func isValidDecimalString(s string) bool {
	if s == "" {
		return false
	}
	
	// 检查是否只包含数字和小数点
	dotCount := 0
	for _, r := range s {
		if r == '.' {
			dotCount++
			if dotCount > 1 {
				return false
			}
		} else if r < '0' || r > '9' {
			return false
		}
	}
	
	// 不能以小数点开头或结尾
	if strings.HasPrefix(s, ".") || strings.HasSuffix(s, ".") {
		return false
	}
	
	return true
}
