package utils

import (
	"regexp"
	"strconv"
	"strings"
)

// ValidateUserId 验证用户ID格式
func ValidateUserId(userId string) bool {
	if userId == "" {
		return false
	}
	
	// 用户ID应该是24位的ObjectId格式
	if len(userId) != 24 {
		return false
	}
	
	// 检查是否为有效的十六进制字符串
	matched, _ := regexp.MatchString("^[a-fA-F0-9]{24}$", userId)
	return matched
}

// ValidateAmount 验证金额格式
func ValidateAmount(amount string) bool {
	if amount == "" {
		return false
	}
	
	// 尝试解析为Decimal
	_, err := ParseDecimal(amount)
	return err == nil
}

// ValidatePositiveAmount 验证正数金额
func ValidatePositiveAmount(amount string) bool {
	if !ValidateAmount(amount) {
		return false
	}
	
	decimal, err := ParseDecimal(amount)
	if err != nil {
		return false
	}
	
	return decimal.IsPositive()
}

// ValidateNonNegativeAmount 验证非负数金额
func ValidateNonNegativeAmount(amount string) bool {
	if !ValidateAmount(amount) {
		return false
	}
	
	decimal, err := ParseDecimal(amount)
	if err != nil {
		return false
	}
	
	return !decimal.IsNegative()
}

// ValidatePaymentMethod 验证支付方式
func ValidatePaymentMethod(method string) bool {
	validMethods := []string{"alipay", "wechat", "apple", "bank"}
	for _, valid := range validMethods {
		if method == valid {
			return true
		}
	}
	return false
}

// ValidateOrderType 验证订单类型
func ValidateOrderType(orderType string) bool {
	validTypes := []string{"chapter", "monthly", "vip"}
	for _, valid := range validTypes {
		if orderType == valid {
			return true
		}
	}
	return false
}

// ValidateTransactionType 验证交易类型
func ValidateTransactionType(transactionType string) bool {
	validTypes := []string{"recharge", "purchase_chapter", "purchase_monthly", "purchase_vip", "refund"}
	for _, valid := range validTypes {
		if transactionType == valid {
			return true
		}
	}
	return false
}

// ValidateBookId 验证书籍ID格式
func ValidateBookId(bookId string) bool {
	if bookId == "" {
		return false
	}
	
	// 书籍ID应该是24位的ObjectId格式
	return ValidateUserId(bookId) // 使用相同的验证逻辑
}

// ValidateChapterId 验证章节ID格式
func ValidateChapterId(chapterId string) bool {
	if chapterId == "" {
		return false
	}
	
	// 章节ID应该是24位的ObjectId格式
	return ValidateUserId(chapterId) // 使用相同的验证逻辑
}

// ValidateChapterOrder 验证章节序号
func ValidateChapterOrder(order uint32) bool {
	return order > 0 && order <= 99999 // 章节序号应该在合理范围内
}

// ValidateDurationDays 验证持续天数
func ValidateDurationDays(days int32) bool {
	return days > 0 && days <= 3650 // 最多10年
}

// ValidateExchangeRate 验证兑换比例
func ValidateExchangeRate(rate string) bool {
	if rate == "" {
		return false
	}
	
	decimal, err := ParseDecimal(rate)
	if err != nil {
		return false
	}
	
	// 兑换比例应该大于0且小于等于100
	min, _ := ParseDecimal("0")
	max, _ := ParseDecimal("100")
	
	return decimal.GreaterThan(min) && decimal.LessOrEqual(max)
}

// ValidatePage 验证分页参数
func ValidatePage(page int32) bool {
	return page > 0
}

// ValidatePageSize 验证页面大小
func ValidatePageSize(pageSize int32) bool {
	return pageSize > 0 && pageSize <= 100
}

// ValidateEmail 验证邮箱格式
func ValidateEmail(email string) bool {
	if email == "" {
		return false
	}
	
	emailRegex := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return emailRegex.MatchString(email)
}

// ValidatePhone 验证手机号格式（中国大陆）
func ValidatePhone(phone string) bool {
	if phone == "" {
		return false
	}
	
	phoneRegex := regexp.MustCompile(`^1[3-9]\d{9}$`)
	return phoneRegex.MatchString(phone)
}

// ValidatePassword 验证密码强度
func ValidatePassword(password string) bool {
	if len(password) < 6 || len(password) > 20 {
		return false
	}
	
	// 至少包含一个字母和一个数字
	hasLetter := regexp.MustCompile(`[a-zA-Z]`).MatchString(password)
	hasNumber := regexp.MustCompile(`\d`).MatchString(password)
	
	return hasLetter && hasNumber
}

// SanitizeString 清理字符串（移除危险字符）
func SanitizeString(input string) string {
	// 移除HTML标签
	htmlRegex := regexp.MustCompile(`<[^>]*>`)
	cleaned := htmlRegex.ReplaceAllString(input, "")
	
	// 移除SQL注入相关字符
	sqlRegex := regexp.MustCompile(`[';\"\\]`)
	cleaned = sqlRegex.ReplaceAllString(cleaned, "")
	
	// 移除多余的空格
	cleaned = strings.TrimSpace(cleaned)
	spaceRegex := regexp.MustCompile(`\s+`)
	cleaned = spaceRegex.ReplaceAllString(cleaned, " ")
	
	return cleaned
}

// ValidateAmountRange 验证金额范围
func ValidateAmountRange(amount, min, max string) bool {
	if !ValidateAmount(amount) || !ValidateAmount(min) || !ValidateAmount(max) {
		return false
	}
	
	amountDecimal, _ := ParseDecimal(amount)
	minDecimal, _ := ParseDecimal(min)
	maxDecimal, _ := ParseDecimal(max)
	
	return amountDecimal.GreaterOrEqual(minDecimal) && amountDecimal.LessOrEqual(maxDecimal)
}

// ValidateStringLength 验证字符串长度
func ValidateStringLength(str string, minLen, maxLen int) bool {
	length := len(str)
	return length >= minLen && length <= maxLen
}

// IsNumeric 检查字符串是否为数字
func IsNumeric(str string) bool {
	_, err := strconv.ParseFloat(str, 64)
	return err == nil
}
