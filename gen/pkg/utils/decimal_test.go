package utils

import (
	"testing"
)

func TestParseDecimal(t *testing.T) {
	tests := []struct {
		name    string
		input   string
		want    string
		wantErr bool
	}{
		{"正数", "123.45", "123.45", false},
		{"负数", "-123.45", "-123.45", false},
		{"整数", "100", "100.00", false},
		{"零", "0", "0.00", false},
		{"空字符串", "", "0.00", false},
		{"小数点开头", ".5", "", true},
		{"小数点结尾", "5.", "", true},
		{"多个小数点", "5.5.5", "", true},
		{"非数字", "abc", "", true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ParseDecimal(tt.input)
			if (err != nil) != tt.wantErr {
				t.<PERSON>("ParseDecimal() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && got.String() != tt.want {
				t.Errorf("ParseDecimal() = %v, want %v", got.String(), tt.want)
			}
		})
	}
}

func TestDecimalOperations(t *testing.T) {
	// 测试加法
	a, _ := ParseDecimal("10.50")
	b, _ := ParseDecimal("5.25")
	result := a.Add(b)
	if result.String() != "15.75" {
		t.Errorf("Add() = %v, want 15.75", result.String())
	}

	// 测试减法
	result = a.Sub(b)
	if result.String() != "5.25" {
		t.Errorf("Sub() = %v, want 5.25", result.String())
	}

	// 测试乘法
	result = a.Mul(b)
	if result.String() != "55.13" {
		t.Errorf("Mul() = %v, want 55.13", result.String())
	}

	// 测试除法
	result, err := a.Div(b)
	if err != nil {
		t.Errorf("Div() error = %v", err)
	}
	if result.String() != "2.00" {
		t.Errorf("Div() = %v, want 2.00", result.String())
	}

	// 测试除零
	zero, _ := ParseDecimal("0")
	_, err = a.Div(zero)
	if err == nil {
		t.Error("Div() should return error for division by zero")
	}
}

func TestDecimalComparisons(t *testing.T) {
	a, _ := ParseDecimal("10.50")
	b, _ := ParseDecimal("5.25")
	c, _ := ParseDecimal("10.50")

	// 测试大于
	if !a.GreaterThan(b) {
		t.Error("GreaterThan() should return true")
	}

	// 测试小于
	if !b.LessThan(a) {
		t.Error("LessThan() should return true")
	}

	// 测试相等
	if !a.Equal(c) {
		t.Error("Equal() should return true")
	}

	// 测试大于等于
	if !a.GreaterOrEqual(c) {
		t.Error("GreaterOrEqual() should return true")
	}

	// 测试小于等于
	if !b.LessOrEqual(a) {
		t.Error("LessOrEqual() should return true")
	}
}

func TestDecimalProperties(t *testing.T) {
	positive, _ := ParseDecimal("10.50")
	negative, _ := ParseDecimal("-10.50")
	zero, _ := ParseDecimal("0")

	// 测试正数
	if !positive.IsPositive() {
		t.Error("IsPositive() should return true for positive number")
	}

	// 测试负数
	if !negative.IsNegative() {
		t.Error("IsNegative() should return true for negative number")
	}

	// 测试零
	if !zero.IsZero() {
		t.Error("IsZero() should return true for zero")
	}

	// 测试绝对值
	abs := negative.Abs()
	if !abs.Equal(positive) {
		t.Error("Abs() should return positive value")
	}

	// 测试取负
	neg := positive.Neg()
	if !neg.Equal(negative) {
		t.Error("Neg() should return negative value")
	}
}

func TestDecimalStringWithPrecision(t *testing.T) {
	d, _ := ParseDecimal("123.456789")

	tests := []struct {
		precision int
		want      string
	}{
		{0, "123"},
		{1, "123.5"},
		{2, "123.46"},
		{4, "123.4568"},
	}

	for _, tt := range tests {
		t.Run("", func(t *testing.T) {
			got := d.StringWithPrecision(tt.precision)
			if got != tt.want {
				t.Errorf("StringWithPrecision(%d) = %v, want %v", tt.precision, got, tt.want)
			}
		})
	}
}

func BenchmarkParseDecimal(b *testing.B) {
	for i := 0; i < b.N; i++ {
		ParseDecimal("123.456")
	}
}

func BenchmarkDecimalAdd(b *testing.B) {
	a, _ := ParseDecimal("123.456")
	c, _ := ParseDecimal("789.012")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		a.Add(c)
	}
}

func BenchmarkDecimalString(b *testing.B) {
	d, _ := ParseDecimal("123.456")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		d.String()
	}
}
