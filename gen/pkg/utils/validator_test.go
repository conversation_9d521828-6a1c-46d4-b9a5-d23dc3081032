package utils

import "testing"

func TestValidateUserId(t *testing.T) {
	tests := []struct {
		name   string
		userId string
		want   bool
	}{
		{"有效用户ID", "61668aad169d3aecb0ff0af4", true},
		{"有效用户ID大写", "61668AAD169D3AECB0FF0AF4", true},
		{"无效长度", "61668aad169d3aecb0ff0af", false},
		{"包含非十六进制字符", "61668aad169d3aecb0ff0afg", false},
		{"空字符串", "", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ValidateUserId(tt.userId); got != tt.want {
				t.Errorf("ValidateUserId() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestValidateAmount(t *testing.T) {
	tests := []struct {
		name   string
		amount string
		want   bool
	}{
		{"有效金额", "123.45", true},
		{"整数", "100", true},
		{"零", "0", true},
		{"负数", "-10.5", true},
		{"空字符串", "", false},
		{"非数字", "abc", false},
		{"多个小数点", "12.34.56", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ValidateAmount(tt.amount); got != tt.want {
				t.Errorf("ValidateAmount() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestValidatePositiveAmount(t *testing.T) {
	tests := []struct {
		name   string
		amount string
		want   bool
	}{
		{"正数", "123.45", true},
		{"零", "0", false},
		{"负数", "-10.5", false},
		{"无效格式", "abc", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ValidatePositiveAmount(tt.amount); got != tt.want {
				t.Errorf("ValidatePositiveAmount() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestValidatePaymentMethod(t *testing.T) {
	tests := []struct {
		name   string
		method string
		want   bool
	}{
		{"支付宝", "alipay", true},
		{"微信", "wechat", true},
		{"苹果支付", "apple", true},
		{"银行卡", "bank", true},
		{"无效方式", "invalid", false},
		{"空字符串", "", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ValidatePaymentMethod(tt.method); got != tt.want {
				t.Errorf("ValidatePaymentMethod() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestValidateOrderType(t *testing.T) {
	tests := []struct {
		name      string
		orderType string
		want      bool
	}{
		{"章节", "chapter", true},
		{"包月", "monthly", true},
		{"VIP", "vip", true},
		{"无效类型", "invalid", false},
		{"空字符串", "", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ValidateOrderType(tt.orderType); got != tt.want {
				t.Errorf("ValidateOrderType() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestValidateExchangeRate(t *testing.T) {
	tests := []struct {
		name string
		rate string
		want bool
	}{
		{"有效比例", "1.0", true},
		{"有效比例", "0.5", true},
		{"最大值", "100", true},
		{"零", "0", false},
		{"负数", "-1", false},
		{"超过最大值", "101", false},
		{"无效格式", "abc", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ValidateExchangeRate(tt.rate); got != tt.want {
				t.Errorf("ValidateExchangeRate() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestValidateEmail(t *testing.T) {
	tests := []struct {
		name  string
		email string
		want  bool
	}{
		{"有效邮箱", "<EMAIL>", true},
		{"有效邮箱", "<EMAIL>", true},
		{"无效邮箱", "invalid-email", false},
		{"无效邮箱", "@example.com", false},
		{"无效邮箱", "test@", false},
		{"空字符串", "", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ValidateEmail(tt.email); got != tt.want {
				t.Errorf("ValidateEmail() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestValidatePhone(t *testing.T) {
	tests := []struct {
		name  string
		phone string
		want  bool
	}{
		{"有效手机号", "13812345678", true},
		{"有效手机号", "15987654321", true},
		{"无效手机号", "12812345678", false},
		{"无效手机号", "1381234567", false},
		{"无效手机号", "138123456789", false},
		{"空字符串", "", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ValidatePhone(tt.phone); got != tt.want {
				t.Errorf("ValidatePhone() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestValidatePassword(t *testing.T) {
	tests := []struct {
		name     string
		password string
		want     bool
	}{
		{"有效密码", "abc123", true},
		{"有效密码", "Password1", true},
		{"太短", "ab1", false},
		{"太长", "abcdefghijklmnopqrstuvwxyz123", false},
		{"只有字母", "abcdef", false},
		{"只有数字", "123456", false},
		{"空字符串", "", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ValidatePassword(tt.password); got != tt.want {
				t.Errorf("ValidatePassword() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestSanitizeString(t *testing.T) {
	tests := []struct {
		name  string
		input string
		want  string
	}{
		{"正常字符串", "hello world", "hello world"},
		{"HTML标签", "<script>alert('xss')</script>", "alert('xss')"},
		{"SQL注入", "'; DROP TABLE users; --", " DROP TABLE users --"},
		{"多余空格", "  hello   world  ", "hello world"},
		{"混合情况", "<p>hello'; world</p>", "hello world"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := SanitizeString(tt.input); got != tt.want {
				t.Errorf("SanitizeString() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestValidateAmountRange(t *testing.T) {
	tests := []struct {
		name   string
		amount string
		min    string
		max    string
		want   bool
	}{
		{"在范围内", "50", "10", "100", true},
		{"等于最小值", "10", "10", "100", true},
		{"等于最大值", "100", "10", "100", true},
		{"小于最小值", "5", "10", "100", false},
		{"大于最大值", "150", "10", "100", false},
		{"无效金额", "abc", "10", "100", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := ValidateAmountRange(tt.amount, tt.min, tt.max); got != tt.want {
				t.Errorf("ValidateAmountRange() = %v, want %v", got, tt.want)
			}
		})
	}
}
