package test

import (
	"context"
	"testing"

	"creativematrix.com/beyondreading/gen/app/base/account/conf"
	"creativematrix.com/beyondreading/gen/app/base/account/svc"
	pb "creativematrix.com/beyondreading/gen/proto/account"
	"github.com/stretchr/testify/assert"
)

func TestAccountService(t *testing.T) {
	// 初始化配置（需要根据实际情况调整）
	config := &conf.Config{}
	// 这里应该加载测试配置

	// 初始化服务
	service := svc.Load(config)
	defer service.Close()

	ctx := context.Background()
	testUserId := "test_user_001"

	t.Run("CreateAccount", func(t *testing.T) {
		req := &pb.CreateAccountReq{
			UserId: testUserId,
		}

		resp, err := service.CreateAccount(ctx, req)
		assert.NoError(t, err)
		assert.Equal(t, int32(200), resp.Code)
		assert.NotNil(t, resp.Account)
		assert.Equal(t, testUserId, resp.Account.UserId)
		assert.Equal(t, "0.00", resp.Account.CoinBalance)
	})

	t.Run("GetAccount", func(t *testing.T) {
		req := &pb.GetAccountReq{
			UserId: testUserId,
		}

		resp, err := service.GetAccount(ctx, req)
		assert.NoError(t, err)
		assert.Equal(t, int32(200), resp.Code)
		assert.NotNil(t, resp.Account)
		assert.Equal(t, testUserId, resp.Account.UserId)
	})

	t.Run("Recharge", func(t *testing.T) {
		req := &pb.RechargeReq{
			UserId:        testUserId,
			Amount:        "100.00",
			PaymentMethod: "alipay",
			ExchangeRate:  "1.0000",
		}

		resp, err := service.Recharge(ctx, req)
		assert.NoError(t, err)
		assert.Equal(t, int32(200), resp.Code)
		assert.NotNil(t, resp.Order)
		assert.Equal(t, testUserId, resp.Order.UserId)
		assert.Equal(t, "100.00", resp.Order.Amount)
		assert.Equal(t, "100.00", resp.Order.CoinAmount)
	})

	t.Run("PurchaseChapter", func(t *testing.T) {
		// 先充值
		rechargeReq := &pb.RechargeReq{
			UserId:        testUserId,
			Amount:        "50.00",
			PaymentMethod: "alipay",
		}
		service.Recharge(ctx, rechargeReq)

		// 购买章节
		req := &pb.PurchaseChapterReq{
			UserId:       testUserId,
			BookId:       "test_book_001",
			BookName:     "测试书籍",
			ChapterId:    "test_chapter_001",
			ChapterTitle: "第一章",
			ChapterOrder: 1,
			CoinAmount:   "5.00",
		}

		resp, err := service.PurchaseChapter(ctx, req)
		assert.NoError(t, err)
		assert.Equal(t, int32(200), resp.Code)
		assert.NotNil(t, resp.Order)
		assert.Equal(t, testUserId, resp.Order.UserId)
		assert.Equal(t, "test_book_001", resp.Order.BookId)
		assert.Equal(t, "5.00", resp.Order.CoinAmount)
	})

	t.Run("CheckChapterPurchased", func(t *testing.T) {
		req := &pb.CheckChapterPurchasedReq{
			UserId:    testUserId,
			BookId:    "test_book_001",
			ChapterId: "test_chapter_001",
		}

		resp, err := service.CheckChapterPurchased(ctx, req)
		assert.NoError(t, err)
		assert.Equal(t, int32(200), resp.Code)
		assert.True(t, resp.IsPurchased)
		assert.NotNil(t, resp.PurchasedAt)
	})

	t.Run("PurchaseMonthly", func(t *testing.T) {
		req := &pb.PurchaseMonthlyReq{
			UserId:       testUserId,
			BookId:       "test_book_001",
			BookName:     "测试书籍",
			CoinAmount:   "30.00",
			DurationDays: 30,
		}

		resp, err := service.PurchaseMonthly(ctx, req)
		assert.NoError(t, err)
		assert.Equal(t, int32(200), resp.Code)
		assert.NotNil(t, resp.Order)
		assert.Equal(t, "monthly", resp.Order.OrderType)
		assert.Equal(t, int32(30), resp.Order.DurationDays)
	})

	t.Run("CheckMonthlyStatus", func(t *testing.T) {
		req := &pb.CheckMonthlyStatusReq{
			UserId: testUserId,
			BookId: "test_book_001",
		}

		resp, err := service.CheckMonthlyStatus(ctx, req)
		assert.NoError(t, err)
		assert.Equal(t, int32(200), resp.Code)
		assert.True(t, resp.IsActive)
		assert.NotNil(t, resp.StartTime)
		assert.NotNil(t, resp.EndTime)
	})

	t.Run("GetAccountLogs", func(t *testing.T) {
		req := &pb.GetAccountLogsReq{
			UserId:   testUserId,
			Page:     1,
			PageSize: 10,
		}

		resp, err := service.GetAccountLogs(ctx, req)
		assert.NoError(t, err)
		assert.Equal(t, int32(200), resp.Code)
		assert.NotNil(t, resp.Logs)
		assert.True(t, len(resp.Logs) > 0)
		assert.True(t, resp.Total > 0)
	})

	t.Run("GetPurchaseOrders", func(t *testing.T) {
		req := &pb.GetPurchaseOrdersReq{
			UserId:   testUserId,
			Page:     1,
			PageSize: 10,
		}

		resp, err := service.GetPurchaseOrders(ctx, req)
		assert.NoError(t, err)
		assert.Equal(t, int32(200), resp.Code)
		assert.NotNil(t, resp.Orders)
		assert.True(t, len(resp.Orders) > 0)
		assert.True(t, resp.Total > 0)
	})
}

func TestAccountValidation(t *testing.T) {
	config := &conf.Config{}
	service := svc.Load(config)
	defer service.Close()

	ctx := context.Background()

	t.Run("CreateAccount_EmptyUserId", func(t *testing.T) {
		req := &pb.CreateAccountReq{
			UserId: "",
		}

		resp, err := service.CreateAccount(ctx, req)
		assert.NoError(t, err)
		assert.Equal(t, int32(400), resp.Code)
		assert.Contains(t, resp.Message, "用户ID不能为空")
	})

	t.Run("Recharge_InvalidAmount", func(t *testing.T) {
		req := &pb.RechargeReq{
			UserId:        "test_user",
			Amount:        "invalid_amount",
			PaymentMethod: "alipay",
		}

		resp, err := service.Recharge(ctx, req)
		assert.NoError(t, err)
		assert.Equal(t, int32(400), resp.Code)
		assert.Contains(t, resp.Message, "充值金额无效")
	})

	t.Run("PurchaseChapter_InsufficientBalance", func(t *testing.T) {
		// 创建新用户（余额为0）
		testUserId := "test_user_insufficient"
		createReq := &pb.CreateAccountReq{
			UserId: testUserId,
		}
		service.CreateAccount(ctx, createReq)

		// 尝试购买章节
		req := &pb.PurchaseChapterReq{
			UserId:       testUserId,
			BookId:       "test_book",
			BookName:     "测试书籍",
			ChapterId:    "test_chapter",
			ChapterTitle: "第一章",
			ChapterOrder: 1,
			CoinAmount:   "10.00",
		}

		resp, err := service.PurchaseChapter(ctx, req)
		assert.NoError(t, err)
		assert.Equal(t, int32(402), resp.Code)
		assert.Contains(t, resp.Message, "书币余额不足")
	})
}

// 基准测试
func BenchmarkGetAccount(b *testing.B) {
	config := &conf.Config{}
	service := svc.Load(config)
	defer service.Close()

	ctx := context.Background()
	req := &pb.GetAccountReq{
		UserId: "benchmark_user",
	}

	// 先创建账户
	createReq := &pb.CreateAccountReq{
		UserId: "benchmark_user",
	}
	service.CreateAccount(ctx, createReq)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		service.GetAccount(ctx, req)
	}
}

func BenchmarkPurchaseChapter(b *testing.B) {
	config := &conf.Config{}
	service := svc.Load(config)
	defer service.Close()

	ctx := context.Background()
	testUserId := "benchmark_user_purchase"

	// 先创建账户并充值
	createReq := &pb.CreateAccountReq{
		UserId: testUserId,
	}
	service.CreateAccount(ctx, createReq)

	rechargeReq := &pb.RechargeReq{
		UserId:        testUserId,
		Amount:        "10000.00",
		PaymentMethod: "alipay",
	}
	service.Recharge(ctx, rechargeReq)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		req := &pb.PurchaseChapterReq{
			UserId:       testUserId,
			BookId:       "benchmark_book",
			BookName:     "基准测试书籍",
			ChapterId:    "benchmark_chapter_" + string(rune(i)),
			ChapterTitle: "基准测试章节",
			ChapterOrder: uint32(i + 1),
			CoinAmount:   "1.00",
		}
		service.PurchaseChapter(ctx, req)
	}
}
