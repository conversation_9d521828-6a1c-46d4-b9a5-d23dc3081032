-- Payment模块MySQL数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS beyondreading_payment DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE beyondreading_payment;

-- 支付方式配置表
CREATE TABLE IF NOT EXISTS payment_methods (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    method INT NOT NULL COMMENT '支付方式: 1-Google Pay, 2-Apple Pay, 3-PayPal, 4-Alipay, 5-WeChat Pay',
    name VARCHAR(50) NOT NULL COMMENT '支付方式名称',
    enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    config JSON COMMENT '支付方式配置信息',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序顺序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_method (method),
    KEY idx_enabled (enabled),
    KEY idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付方式配置表';

-- 支付产品配置表
CREATE TABLE IF NOT EXISTS payment_products (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    product_id INT NOT NULL COMMENT '产品ID',
    product_name VARCHAR(100) NOT NULL COMMENT '产品名称',
    product_type INT NOT NULL COMMENT '产品类型: 1-VIP, 2-包月订阅, 3-书币充值',
    platform VARCHAR(10) NOT NULL COMMENT '平台: android, ios',
    price DECIMAL(10,2) NOT NULL COMMENT '价格(美分)',
    currency VARCHAR(3) NOT NULL DEFAULT 'USD' COMMENT '货币类型',
    coin_amount DECIMAL(10,2) DEFAULT 0 COMMENT '书币数量(仅书币充值产品)',
    vip_days INT DEFAULT 0 COMMENT 'VIP天数(仅VIP产品)',
    enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序顺序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_product_id (product_id),
    KEY idx_product_type (product_type),
    KEY idx_enabled (enabled),
    KEY idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付产品配置表';

-- 插入默认支付方式配置
INSERT INTO payment_methods (method, name, enabled, config, sort_order) VALUES
(1, 'Google Pay', TRUE, '{"package_name": "com.beyondreading.app"}', 1),
(2, 'Apple Pay', TRUE, '{"bundle_id": "com.beyondreading.app"}', 2),
(3, 'PayPal', TRUE, '{"environment": "sandbox"}', 3),
(4, 'Alipay', TRUE, '{"gateway": "https://openapi.alipay.com/gateway.do"}', 4),
(5, 'WeChat Pay', TRUE, '{"gateway": "https://api.mch.weixin.qq.com"}', 5)
ON DUPLICATE KEY UPDATE
name = VALUES(name),
enabled = VALUES(enabled),
config = VALUES(config),
sort_order = VALUES(sort_order),
updated_at = CURRENT_TIMESTAMP;

-- 插入默认支付产品配置
INSERT INTO payment_products (product_id, product_name, product_type, platform, price, currency, coin_amount, vip_days, enabled, sort_order) VALUES
-- VIP产品
(0, 'VIP Monthly', 1, 'android' 999, 'USD', 0, 30, TRUE, 1),
(1, 'VIP Yearly', 1, 'android', 9999, 'USD', 0, 365, TRUE, 2),

-- 包月订阅产品
(2, 'Monthly Subscription', 2, 'android', 599, 'USD', 0, 30, TRUE, 3),

-- 书币充值产品
(3, '100 Coins', 3, 'android', 99, 'USD', 100, 0, TRUE, 4),
(4, '500 Coins', 3, 'android', 499, 'USD', 500, 0, TRUE, 5),
(5, '1000 Coins', 3, 'android', 999, 'USD', 1000, 0, TRUE, 6),
(6, '2000 Coins', 3, 'android', 1899, 'USD', 2000, 0, TRUE, 7),
(7, '5000 Coins', 3, 'android', 4599, 'USD', 5000, 0, TRUE, 8),

-- VIP产品
(100, 'VIP Monthly', 1, 'ios' 999, 'USD', 0, 30, TRUE, 1),
(101, 'VIP Yearly', 1, 'ios', 9999, 'USD', 0, 365, TRUE, 2),

-- 包月订阅产品
(102, 'Monthly Subscription', 2, 'ios', 599, 'USD', 0, 30, TRUE, 3),

-- 书币充值产品
(103, '100 Coins', 3, 'ios', 99, 'USD', 100, 0, TRUE, 4),
(104, '500 Coins', 3, 'ios', 499, 'USD', 500, 0, TRUE, 5),
(105, '1000 Coins', 3, 'ios', 999, 'USD', 1000, 0, TRUE, 6),
(106, '2000 Coins', 3, 'ios', 1899, 'USD', 2000, 0, TRUE, 7),
(107, '5000 Coins', 3, 'ios', 4599, 'USD', 5000, 0, TRUE, 8)

ON DUPLICATE KEY UPDATE
product_name = VALUES(product_name),
product_type = VALUES(product_type),
price = VALUES(price),
currency = VALUES(currency),
coin_amount = VALUES(coin_amount),
vip_days = VALUES(vip_days),
enabled = VALUES(enabled),
sort_order = VALUES(sort_order),
updated_at = CURRENT_TIMESTAMP;

-- 创建索引优化查询性能
-- 支付方式表索引
CREATE INDEX IF NOT EXISTS idx_payment_methods_enabled_sort ON payment_methods (enabled, sort_order);

-- 支付产品表索引
CREATE INDEX IF NOT EXISTS idx_payment_products_type_enabled ON payment_products (product_type, enabled);
CREATE INDEX IF NOT EXISTS idx_payment_products_enabled_sort ON payment_products (enabled, sort_order);

-- 显示创建的表结构
SHOW CREATE TABLE payment_methods;
SHOW CREATE TABLE payment_products;

-- 显示插入的数据
SELECT * FROM payment_methods ORDER BY sort_order;
SELECT * FROM payment_products ORDER BY sort_order;
