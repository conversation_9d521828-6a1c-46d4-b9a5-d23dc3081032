-- Account微服务数据库表设计（分表版本）

-- 用户账户表（分表 account00-account06）
DROP TABLE IF EXISTS `account00`;
CREATE TABLE `account00` (
  `account_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID，关联用户表',
  `coin_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '书币余额',
  `total_recharged` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计充值金额',
  `total_consumed` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计消费金额',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '账户状态：1-正常，2-冻结，3-注销',
  `user_type` tinyint NOT NULL DEFAULT '1' COMMENT '用户类型：1-普通用户，2-VIP用户，3-包月用户',
  `user_level` int NOT NULL DEFAULT '1' COMMENT '用户等级（根据消费书币计算，1000书币一级）',
  `vip_expire_time` timestamp NULL DEFAULT NULL COMMENT 'VIP过期时间',
  `monthly_expire_time` timestamp NULL DEFAULT NULL COMMENT '包月过期时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`account_id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户账户表00';

DROP TABLE IF EXISTS `account01`;
CREATE TABLE `account01` (
  `account_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID，关联用户表',
  `coin_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '书币余额',
  `total_recharged` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计充值金额',
  `total_consumed` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计消费金额',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '账户状态：1-正常，2-冻结，3-注销',
  `user_type` tinyint NOT NULL DEFAULT '1' COMMENT '用户类型：1-普通用户，2-VIP用户，3-包月用户',
  `user_level` int NOT NULL DEFAULT '1' COMMENT '用户等级（根据消费书币计算，1000书币一级）',
  `vip_expire_time` timestamp NULL DEFAULT NULL COMMENT 'VIP过期时间',
  `monthly_expire_time` timestamp NULL DEFAULT NULL COMMENT '包月过期时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`account_id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户账户表01';

DROP TABLE IF EXISTS `account02`;
CREATE TABLE `account02` (
  `account_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID，关联用户表',
  `coin_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '书币余额',
  `total_recharged` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计充值金额',
  `total_consumed` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计消费金额',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '账户状态：1-正常，2-冻结，3-注销',
  `user_type` tinyint NOT NULL DEFAULT '1' COMMENT '用户类型：1-普通用户，2-VIP用户，3-包月用户',
  `user_level` int NOT NULL DEFAULT '1' COMMENT '用户等级（根据消费书币计算，1000书币一级）',
  `vip_expire_time` timestamp NULL DEFAULT NULL COMMENT 'VIP过期时间',
  `monthly_expire_time` timestamp NULL DEFAULT NULL COMMENT '包月过期时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`account_id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户账户表02';

DROP TABLE IF EXISTS `account03`;
CREATE TABLE `account03` (
  `account_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID，关联用户表',
  `coin_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '书币余额',
  `total_recharged` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计充值金额',
  `total_consumed` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计消费金额',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '账户状态：1-正常，2-冻结，3-注销',
  `user_type` tinyint NOT NULL DEFAULT '1' COMMENT '用户类型：1-普通用户，2-VIP用户，3-包月用户',
  `user_level` int NOT NULL DEFAULT '1' COMMENT '用户等级（根据消费书币计算，1000书币一级）',
  `vip_expire_time` timestamp NULL DEFAULT NULL COMMENT 'VIP过期时间',
  `monthly_expire_time` timestamp NULL DEFAULT NULL COMMENT '包月过期时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`account_id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户账户表03';

DROP TABLE IF EXISTS `account04`;
CREATE TABLE `account04` (
  `account_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID，关联用户表',
  `coin_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '书币余额',
  `total_recharged` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计充值金额',
  `total_consumed` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计消费金额',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '账户状态：1-正常，2-冻结，3-注销',
  `user_type` tinyint NOT NULL DEFAULT '1' COMMENT '用户类型：1-普通用户，2-VIP用户，3-包月用户',
  `user_level` int NOT NULL DEFAULT '1' COMMENT '用户等级（根据消费书币计算，1000书币一级）',
  `vip_expire_time` timestamp NULL DEFAULT NULL COMMENT 'VIP过期时间',
  `monthly_expire_time` timestamp NULL DEFAULT NULL COMMENT '包月过期时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`account_id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户账户表04';

DROP TABLE IF EXISTS `account05`;
CREATE TABLE `account05` (
  `account_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID，关联用户表',
  `coin_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '书币余额',
  `total_recharged` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计充值金额',
  `total_consumed` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计消费金额',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '账户状态：1-正常，2-冻结，3-注销',
  `user_type` tinyint NOT NULL DEFAULT '1' COMMENT '用户类型：1-普通用户，2-VIP用户，3-包月用户',
  `user_level` int NOT NULL DEFAULT '1' COMMENT '用户等级（根据消费书币计算，1000书币一级）',
  `vip_expire_time` timestamp NULL DEFAULT NULL COMMENT 'VIP过期时间',
  `monthly_expire_time` timestamp NULL DEFAULT NULL COMMENT '包月过期时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`account_id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户账户表05';

DROP TABLE IF EXISTS `account06`;
CREATE TABLE `account06` (
  `account_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID，关联用户表',
  `coin_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '书币余额',
  `total_recharged` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计充值金额',
  `total_consumed` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '累计消费金额',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '账户状态：1-正常，2-冻结，3-注销',
  `user_type` tinyint NOT NULL DEFAULT '1' COMMENT '用户类型：1-普通用户，2-VIP用户，3-包月用户',
  `user_level` int NOT NULL DEFAULT '1' COMMENT '用户等级（根据消费书币计算，1000书币一级）',
  `vip_expire_time` timestamp NULL DEFAULT NULL COMMENT 'VIP过期时间',
  `monthly_expire_time` timestamp NULL DEFAULT NULL COMMENT '包月过期时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`account_id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户账户表06';

-- 账户日志表（分表 account_log00-account_log06）
DROP TABLE IF EXISTS `account_log00`;
CREATE TABLE `account_log00` (
  `log_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `account_id` bigint unsigned NOT NULL COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `transaction_type` varchar(32) NOT NULL COMMENT '交易类型：recharge, purchase_chapter, purchase_monthly, purchase_vip',
  `amount` decimal(15,2) NOT NULL COMMENT '变动金额（正数为增加，负数为减少）',
  `balance_before` decimal(15,2) NOT NULL COMMENT '变动前余额',
  `balance_after` decimal(15,2) NOT NULL COMMENT '变动后余额',
  `order_id` varchar(64) DEFAULT NULL COMMENT '关联订单ID',
  `book_id` varchar(64) DEFAULT NULL COMMENT '书籍ID',
  `chapter_id` varchar(64) DEFAULT NULL COMMENT '章节ID',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `extra_data` text COMMENT '额外数据（JSON格式）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_transaction_type` (`transaction_type`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_book_id` (`book_id`),
  KEY `idx_chapter_id` (`chapter_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='账户日志表00';

DROP TABLE IF EXISTS `account_log01`;
CREATE TABLE `account_log01` (
  `log_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `account_id` bigint unsigned NOT NULL COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `transaction_type` varchar(32) NOT NULL COMMENT '交易类型：recharge, purchase_chapter, purchase_monthly, purchase_vip',
  `amount` decimal(15,2) NOT NULL COMMENT '变动金额（正数为增加，负数为减少）',
  `balance_before` decimal(15,2) NOT NULL COMMENT '变动前余额',
  `balance_after` decimal(15,2) NOT NULL COMMENT '变动后余额',
  `order_id` varchar(64) DEFAULT NULL COMMENT '关联订单ID',
  `book_id` varchar(64) DEFAULT NULL COMMENT '书籍ID',
  `chapter_id` varchar(64) DEFAULT NULL COMMENT '章节ID',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `extra_data` text COMMENT '额外数据（JSON格式）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_transaction_type` (`transaction_type`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_book_id` (`book_id`),
  KEY `idx_chapter_id` (`chapter_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='账户日志表01';

DROP TABLE IF EXISTS `account_log02`;
CREATE TABLE `account_log02` (
  `log_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `account_id` bigint unsigned NOT NULL COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `transaction_type` varchar(32) NOT NULL COMMENT '交易类型：recharge, purchase_chapter, purchase_monthly, purchase_vip',
  `amount` decimal(15,2) NOT NULL COMMENT '变动金额（正数为增加，负数为减少）',
  `balance_before` decimal(15,2) NOT NULL COMMENT '变动前余额',
  `balance_after` decimal(15,2) NOT NULL COMMENT '变动后余额',
  `order_id` varchar(64) DEFAULT NULL COMMENT '关联订单ID',
  `book_id` varchar(64) DEFAULT NULL COMMENT '书籍ID',
  `chapter_id` varchar(64) DEFAULT NULL COMMENT '章节ID',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `extra_data` text COMMENT '额外数据（JSON格式）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_transaction_type` (`transaction_type`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_book_id` (`book_id`),
  KEY `idx_chapter_id` (`chapter_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='账户日志表02';

DROP TABLE IF EXISTS `account_log03`;
CREATE TABLE `account_log03` (
  `log_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `account_id` bigint unsigned NOT NULL COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `transaction_type` varchar(32) NOT NULL COMMENT '交易类型：recharge, purchase_chapter, purchase_monthly, purchase_vip',
  `amount` decimal(15,2) NOT NULL COMMENT '变动金额（正数为增加，负数为减少）',
  `balance_before` decimal(15,2) NOT NULL COMMENT '变动前余额',
  `balance_after` decimal(15,2) NOT NULL COMMENT '变动后余额',
  `order_id` varchar(64) DEFAULT NULL COMMENT '关联订单ID',
  `book_id` varchar(64) DEFAULT NULL COMMENT '书籍ID',
  `chapter_id` varchar(64) DEFAULT NULL COMMENT '章节ID',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `extra_data` text COMMENT '额外数据（JSON格式）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_transaction_type` (`transaction_type`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_book_id` (`book_id`),
  KEY `idx_chapter_id` (`chapter_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='账户日志表03';

DROP TABLE IF EXISTS `account_log04`;
CREATE TABLE `account_log04` (
  `log_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `account_id` bigint unsigned NOT NULL COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `transaction_type` varchar(32) NOT NULL COMMENT '交易类型：recharge, purchase_chapter, purchase_monthly, purchase_vip',
  `amount` decimal(15,2) NOT NULL COMMENT '变动金额（正数为增加，负数为减少）',
  `balance_before` decimal(15,2) NOT NULL COMMENT '变动前余额',
  `balance_after` decimal(15,2) NOT NULL COMMENT '变动后余额',
  `order_id` varchar(64) DEFAULT NULL COMMENT '关联订单ID',
  `book_id` varchar(64) DEFAULT NULL COMMENT '书籍ID',
  `chapter_id` varchar(64) DEFAULT NULL COMMENT '章节ID',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `extra_data` text COMMENT '额外数据（JSON格式）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_transaction_type` (`transaction_type`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_book_id` (`book_id`),
  KEY `idx_chapter_id` (`chapter_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='账户日志表04';

DROP TABLE IF EXISTS `account_log05`;
CREATE TABLE `account_log05` (
  `log_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `account_id` bigint unsigned NOT NULL COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `transaction_type` varchar(32) NOT NULL COMMENT '交易类型：recharge, purchase_chapter, purchase_monthly, purchase_vip',
  `amount` decimal(15,2) NOT NULL COMMENT '变动金额（正数为增加，负数为减少）',
  `balance_before` decimal(15,2) NOT NULL COMMENT '变动前余额',
  `balance_after` decimal(15,2) NOT NULL COMMENT '变动后余额',
  `order_id` varchar(64) DEFAULT NULL COMMENT '关联订单ID',
  `book_id` varchar(64) DEFAULT NULL COMMENT '书籍ID',
  `chapter_id` varchar(64) DEFAULT NULL COMMENT '章节ID',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `extra_data` text COMMENT '额外数据（JSON格式）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_transaction_type` (`transaction_type`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_book_id` (`book_id`),
  KEY `idx_chapter_id` (`chapter_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='账户日志表05';

DROP TABLE IF EXISTS `account_log06`;
CREATE TABLE `account_log06` (
  `log_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `account_id` bigint unsigned NOT NULL COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `transaction_type` varchar(32) NOT NULL COMMENT '交易类型：recharge, purchase_chapter, purchase_monthly, purchase_vip',
  `amount` decimal(15,2) NOT NULL COMMENT '变动金额（正数为增加，负数为减少）',
  `balance_before` decimal(15,2) NOT NULL COMMENT '变动前余额',
  `balance_after` decimal(15,2) NOT NULL COMMENT '变动后余额',
  `order_id` varchar(64) DEFAULT NULL COMMENT '关联订单ID',
  `book_id` varchar(64) DEFAULT NULL COMMENT '书籍ID',
  `chapter_id` varchar(64) DEFAULT NULL COMMENT '章节ID',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `extra_data` text COMMENT '额外数据（JSON格式）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_transaction_type` (`transaction_type`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_book_id` (`book_id`),
  KEY `idx_chapter_id` (`chapter_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='账户日志表06';

-- 充值订单表
DROP TABLE IF EXISTS `recharge_order`;
CREATE TABLE `recharge_order` (
  `order_id` varchar(64) NOT NULL COMMENT '充值订单ID',
  `account_id` bigint unsigned NOT NULL COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `amount` decimal(15,2) NOT NULL COMMENT '充值金额（人民币）',
  `coin_amount` decimal(15,2) NOT NULL COMMENT '获得书币数量',
  `exchange_rate` decimal(8,4) NOT NULL DEFAULT '1.0000' COMMENT '兑换比例',
  `payment_method` varchar(32) NOT NULL COMMENT '支付方式：alipay, wechat, apple, bank',
  `payment_order_id` varchar(128) DEFAULT NULL COMMENT '第三方支付订单ID',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '订单状态：1-待支付，2-支付成功，3-支付失败，4-已退款',
  `paid_at` timestamp NULL DEFAULT NULL COMMENT '支付时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_payment_method` (`payment_method`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='充值订单表';

-- 购买订单表（章节）（分表 purchase_order00-purchase_order06）
DROP TABLE IF EXISTS `purchase_order00`;
CREATE TABLE `purchase_order00` (
  `order_id` varchar(64) NOT NULL COMMENT '购买订单ID',
  `account_id` bigint unsigned NOT NULL COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `order_type` varchar(32) NOT NULL COMMENT '订单类型：chapter',
  `book_id` varchar(64) NOT NULL COMMENT '书籍ID',
  `book_name` varchar(128) DEFAULT NULL COMMENT '书籍名称',
  `chapter_id` varchar(64) DEFAULT NULL COMMENT '章节ID',
  `chapter_title` varchar(128) DEFAULT NULL COMMENT '章节标题',
  `chapter_order` int unsigned NOT NULL COMMENT '章节序号',
  `coin_amount` decimal(15,2) NOT NULL COMMENT '消费书币数量',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '订单状态：1-待支付，2-支付成功，3-支付失败',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_book_id` (`book_id`),
  KEY `idx_chapter_order` (`chapter_order`),
  KEY `idx_order_type` (`order_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_user_book_chapter` (`user_id`, `book_id`, `chapter_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='购买订单表（章节）00';

DROP TABLE IF EXISTS `purchase_order01`;
CREATE TABLE `purchase_order01` (
  `order_id` varchar(64) NOT NULL COMMENT '购买订单ID',
  `account_id` bigint unsigned NOT NULL COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `order_type` varchar(32) NOT NULL COMMENT '订单类型：chapter',
  `book_id` varchar(64) NOT NULL COMMENT '书籍ID',
  `book_name` varchar(128) DEFAULT NULL COMMENT '书籍名称',
  `chapter_id` varchar(64) DEFAULT NULL COMMENT '章节ID',
  `chapter_title` varchar(128) DEFAULT NULL COMMENT '章节标题',
  `chapter_order` int unsigned NOT NULL COMMENT '章节序号',
  `coin_amount` decimal(15,2) NOT NULL COMMENT '消费书币数量',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '订单状态：1-待支付，2-支付成功，3-支付失败',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_book_id` (`book_id`),
  KEY `idx_chapter_order` (`chapter_order`),
  KEY `idx_order_type` (`order_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_user_book_chapter` (`user_id`, `book_id`, `chapter_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='购买订单表（章节）01';

DROP TABLE IF EXISTS `purchase_order02`;
CREATE TABLE `purchase_order02` (
  `order_id` varchar(64) NOT NULL COMMENT '购买订单ID',
  `account_id` bigint unsigned NOT NULL COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `order_type` varchar(32) NOT NULL COMMENT '订单类型：chapter',
  `book_id` varchar(64) NOT NULL COMMENT '书籍ID',
  `book_name` varchar(128) DEFAULT NULL COMMENT '书籍名称',
  `chapter_id` varchar(64) DEFAULT NULL COMMENT '章节ID',
  `chapter_title` varchar(128) DEFAULT NULL COMMENT '章节标题',
  `chapter_order` int unsigned NOT NULL COMMENT '章节序号',
  `coin_amount` decimal(15,2) NOT NULL COMMENT '消费书币数量',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '订单状态：1-待支付，2-支付成功，3-支付失败',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_book_id` (`book_id`),
  KEY `idx_chapter_order` (`chapter_order`),
  KEY `idx_order_type` (`order_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_user_book_chapter` (`user_id`, `book_id`, `chapter_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='购买订单表（章节）02';

DROP TABLE IF EXISTS `purchase_order03`;
CREATE TABLE `purchase_order03` (
  `order_id` varchar(64) NOT NULL COMMENT '购买订单ID',
  `account_id` bigint unsigned NOT NULL COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `order_type` varchar(32) NOT NULL COMMENT '订单类型：chapter',
  `book_id` varchar(64) NOT NULL COMMENT '书籍ID',
  `book_name` varchar(128) DEFAULT NULL COMMENT '书籍名称',
  `chapter_id` varchar(64) DEFAULT NULL COMMENT '章节ID',
  `chapter_title` varchar(128) DEFAULT NULL COMMENT '章节标题',
  `chapter_order` int unsigned NOT NULL COMMENT '章节序号',
  `coin_amount` decimal(15,2) NOT NULL COMMENT '消费书币数量',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '订单状态：1-待支付，2-支付成功，3-支付失败',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_book_id` (`book_id`),
  KEY `idx_chapter_order` (`chapter_order`),
  KEY `idx_order_type` (`order_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_user_book_chapter` (`user_id`, `book_id`, `chapter_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='购买订单表（章节）03';

DROP TABLE IF EXISTS `purchase_order04`;
CREATE TABLE `purchase_order04` (
  `order_id` varchar(64) NOT NULL COMMENT '购买订单ID',
  `account_id` bigint unsigned NOT NULL COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `order_type` varchar(32) NOT NULL COMMENT '订单类型：chapter',
  `book_id` varchar(64) NOT NULL COMMENT '书籍ID',
  `book_name` varchar(128) DEFAULT NULL COMMENT '书籍名称',
  `chapter_id` varchar(64) DEFAULT NULL COMMENT '章节ID',
  `chapter_title` varchar(128) DEFAULT NULL COMMENT '章节标题',
  `chapter_order` int unsigned NOT NULL COMMENT '章节序号',
  `coin_amount` decimal(15,2) NOT NULL COMMENT '消费书币数量',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '订单状态：1-待支付，2-支付成功，3-支付失败',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_book_id` (`book_id`),
  KEY `idx_chapter_order` (`chapter_order`),
  KEY `idx_order_type` (`order_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_user_book_chapter` (`user_id`, `book_id`, `chapter_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='购买订单表（章节）04';

DROP TABLE IF EXISTS `purchase_order05`;
CREATE TABLE `purchase_order05` (
  `order_id` varchar(64) NOT NULL COMMENT '购买订单ID',
  `account_id` bigint unsigned NOT NULL COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `order_type` varchar(32) NOT NULL COMMENT '订单类型：chapter',
  `book_id` varchar(64) NOT NULL COMMENT '书籍ID',
  `book_name` varchar(128) DEFAULT NULL COMMENT '书籍名称',
  `chapter_id` varchar(64) DEFAULT NULL COMMENT '章节ID',
  `chapter_title` varchar(128) DEFAULT NULL COMMENT '章节标题',
  `chapter_order` int unsigned NOT NULL COMMENT '章节序号',
  `coin_amount` decimal(15,2) NOT NULL COMMENT '消费书币数量',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '订单状态：1-待支付，2-支付成功，3-支付失败',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_book_id` (`book_id`),
  KEY `idx_chapter_order` (`chapter_order`),
  KEY `idx_order_type` (`order_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_user_book_chapter` (`user_id`, `book_id`, `chapter_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='购买订单表（章节）05';

DROP TABLE IF EXISTS `purchase_order06`;
CREATE TABLE `purchase_order06` (
  `order_id` varchar(64) NOT NULL COMMENT '购买订单ID',
  `account_id` bigint unsigned NOT NULL COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `order_type` varchar(32) NOT NULL COMMENT '订单类型：chapter',
  `book_id` varchar(64) NOT NULL COMMENT '书籍ID',
  `book_name` varchar(128) DEFAULT NULL COMMENT '书籍名称',
  `chapter_id` varchar(64) DEFAULT NULL COMMENT '章节ID',
  `chapter_title` varchar(128) DEFAULT NULL COMMENT '章节标题',
  `chapter_order` int unsigned NOT NULL COMMENT '章节序号',
  `coin_amount` decimal(15,2) NOT NULL COMMENT '消费书币数量',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '订单状态：1-待支付，2-支付成功，3-支付失败',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_book_id` (`book_id`),
  KEY `idx_chapter_order` (`chapter_order`),
  KEY `idx_order_type` (`order_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_user_book_chapter` (`user_id`, `book_id`, `chapter_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='购买订单表（章节）06';

-- VIP/包月订单表（分表 vipmonthly_order00-vipmonthly_order06）
DROP TABLE IF EXISTS `vipmonthly_order00`;
CREATE TABLE `vipmonthly_order00` (
  `order_id` varchar(64) NOT NULL COMMENT '订单ID',
  `account_id` bigint unsigned NOT NULL COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `order_type` varchar(32) NOT NULL COMMENT '订单类型：monthly-包月，vip-VIP',
  `coin_amount` decimal(15,2) NOT NULL COMMENT '消费书币数量',
  `duration_days` int NOT NULL COMMENT '有效期天数',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '订单状态：1-待支付，2-支付成功，3-支付失败',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_type` (`order_type`),
  KEY `idx_status` (`status`),
  KEY `idx_time_range` (`start_time`, `end_time`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='VIP/包月订单表00';

DROP TABLE IF EXISTS `vipmonthly_order01`;
CREATE TABLE `vipmonthly_order01` (
  `order_id` varchar(64) NOT NULL COMMENT '订单ID',
  `account_id` bigint unsigned NOT NULL COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `order_type` varchar(32) NOT NULL COMMENT '订单类型：monthly-包月，vip-VIP',
  `coin_amount` decimal(15,2) NOT NULL COMMENT '消费书币数量',
  `duration_days` int NOT NULL COMMENT '有效期天数',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '订单状态：1-待支付，2-支付成功，3-支付失败',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_type` (`order_type`),
  KEY `idx_status` (`status`),
  KEY `idx_time_range` (`start_time`, `end_time`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='VIP/包月订单表01';

DROP TABLE IF EXISTS `vipmonthly_order02`;
CREATE TABLE `vipmonthly_order02` (
  `order_id` varchar(64) NOT NULL COMMENT '订单ID',
  `account_id` bigint unsigned NOT NULL COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `order_type` varchar(32) NOT NULL COMMENT '订单类型：monthly-包月，vip-VIP',
  `coin_amount` decimal(15,2) NOT NULL COMMENT '消费书币数量',
  `duration_days` int NOT NULL COMMENT '有效期天数',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '订单状态：1-待支付，2-支付成功，3-支付失败',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_type` (`order_type`),
  KEY `idx_status` (`status`),
  KEY `idx_time_range` (`start_time`, `end_time`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='VIP/包月订单表02';

DROP TABLE IF EXISTS `vipmonthly_order03`;
CREATE TABLE `vipmonthly_order03` (
  `order_id` varchar(64) NOT NULL COMMENT '订单ID',
  `account_id` bigint unsigned NOT NULL COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `order_type` varchar(32) NOT NULL COMMENT '订单类型：monthly-包月，vip-VIP',
  `coin_amount` decimal(15,2) NOT NULL COMMENT '消费书币数量',
  `duration_days` int NOT NULL COMMENT '有效期天数',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '订单状态：1-待支付，2-支付成功，3-支付失败',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_type` (`order_type`),
  KEY `idx_status` (`status`),
  KEY `idx_time_range` (`start_time`, `end_time`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='VIP/包月订单表03';

DROP TABLE IF EXISTS `vipmonthly_order04`;
CREATE TABLE `vipmonthly_order04` (
  `order_id` varchar(64) NOT NULL COMMENT '订单ID',
  `account_id` bigint unsigned NOT NULL COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `order_type` varchar(32) NOT NULL COMMENT '订单类型：monthly-包月，vip-VIP',
  `coin_amount` decimal(15,2) NOT NULL COMMENT '消费书币数量',
  `duration_days` int NOT NULL COMMENT '有效期天数',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '订单状态：1-待支付，2-支付成功，3-支付失败',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_type` (`order_type`),
  KEY `idx_status` (`status`),
  KEY `idx_time_range` (`start_time`, `end_time`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='VIP/包月订单表04';

DROP TABLE IF EXISTS `vipmonthly_order05`;
CREATE TABLE `vipmonthly_order05` (
  `order_id` varchar(64) NOT NULL COMMENT '订单ID',
  `account_id` bigint unsigned NOT NULL COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `order_type` varchar(32) NOT NULL COMMENT '订单类型：monthly-包月，vip-VIP',
  `coin_amount` decimal(15,2) NOT NULL COMMENT '消费书币数量',
  `duration_days` int NOT NULL COMMENT '有效期天数',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '订单状态：1-待支付，2-支付成功，3-支付失败',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_type` (`order_type`),
  KEY `idx_status` (`status`),
  KEY `idx_time_range` (`start_time`, `end_time`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='VIP/包月订单表05';

DROP TABLE IF EXISTS `vipmonthly_order06`;
CREATE TABLE `vipmonthly_order06` (
  `order_id` varchar(64) NOT NULL COMMENT '订单ID',
  `account_id` bigint unsigned NOT NULL COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `order_type` varchar(32) NOT NULL COMMENT '订单类型：monthly-包月，vip-VIP',
  `coin_amount` decimal(15,2) NOT NULL COMMENT '消费书币数量',
  `duration_days` int NOT NULL COMMENT '有效期天数',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '订单状态：1-待支付，2-支付成功，3-支付失败',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_type` (`order_type`),
  KEY `idx_status` (`status`),
  KEY `idx_time_range` (`start_time`, `end_time`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='VIP/包月订单表06';

-- 初始化数据
INSERT INTO `account` (`user_id`, `coin_balance`, `total_recharged`, `total_consumed`, `status`, `user_type`, `user_level`, `created_at`, `updated_at`) VALUES
(123, '1000.00', '1000.00', '0.00', 1, 1, 1, NOW(), NOW()),
(456, '500.00', '500.00', '100.00', 1, 1, 1, NOW(), NOW()),
(789, '2000.00', '2000.00', '1500.00', 1, 2, 2, NOW(), NOW());

-- 插入测试充值订单
INSERT INTO `recharge_order` (`order_id`, `account_id`, `user_id`, `amount`, `coin_amount`, `payment_method`, `status`, `paid_at`) VALUES
('****************', 1, 123, '100.00', '1000.00', 'alipay', 2, NOW()),
('****************', 2, 456, '50.00', '500.00', 'wechat', 2, NOW()),
('****************', 3, 789, '200.00', '2000.00', 'alipay', 2, NOW());

-- 插入测试购买订单
INSERT INTO `purchase_order` (`order_id`, `account_id`, `user_id`, `order_type`, `book_id`, `book_name`, `chapter_id`, `chapter_title`, `chapter_order`, `coin_amount`, `status`) VALUES
('****************', 2, 456, 'chapter', 'book_001', '测试书籍1', 'chapter_001', '第一章', 1, '5.00', 2),
('****************', 2, 456, 'chapter', 'book_001', '测试书籍1', 'chapter_002', '第二章', 2, '5.00', 2);

-- 插入测试VIP/包月订单
INSERT INTO `vipmonthly_order` (`order_id`, `account_id`, `user_id`, `order_type`, `coin_amount`, `duration_days`, `start_time`, `end_time`, `status`) VALUES
('****************', 3, 789, 'vip', '100.00', 30, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 2),
('****************', 3, 789, 'monthly', '50.00', 30, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 2);

-- 创建索引优化查询性能
CREATE INDEX `idx_account_user_type_status` ON `account` (`user_type`, `status`);
CREATE INDEX `idx_account_log_user_type` ON `account_log` (`user_id`, `transaction_type`, `created_at`);
CREATE INDEX `idx_purchase_order_user_book` ON `purchase_order` (`user_id`, `book_id`, `order_type`);
CREATE INDEX `idx_vipmonthly_order_user_type` ON `vipmonthly_order` (`user_id`, `order_type`, `status`);
CREATE INDEX `idx_vipmonthly_order_time_range` ON `vipmonthly_order` (`start_time`, `end_time`);
