-- User微服务数据库表结构
-- 分表基数：7 (00-06，即user_id % 7)

-- 用户表（分表 user00-user06）
DROP TABLE IF EXISTS `user00`;
CREATE TABLE `user00` (
  `userId` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `nickname` varchar(50) NOT NULL DEFAULT '' COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `gender` tinyint NOT NULL DEFAULT '0' COMMENT '性别：0-未知，1-男，2-女',
  `birthday` varchar(10) DEFAULT NULL COMMENT '生日（YYYY-MM-DD）',
  `location` varchar(100) DEFAULT NULL COMMENT '地区',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-正常，2-冻结，3-注销',
  `loginType` tinyint NOT NULL DEFAULT '1' COMMENT '注册类型：1-手机，2-Google，3-Apple',
  `googleId` varchar(100) DEFAULT NULL COMMENT 'Google账户ID',
  `appleId` varchar(100) DEFAULT NULL COMMENT 'Apple账户ID',
  `lastLoginAt` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `lastLoginIp` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`userId`),
  UNIQUE KEY `uk_phone` (`phone`),
  UNIQUE KEY `uk_email` (`email`),
  UNIQUE KEY `uk_google_id` (`googleId`),
  UNIQUE KEY `uk_apple_id` (`appleId`),
  KEY `idx_status` (`status`),
  KEY `idx_login_type` (`loginType`),
  KEY `idx_created_at` (`createdAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表00';

DROP TABLE IF EXISTS `user01`;
CREATE TABLE `user01` (
  `userId` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `nickname` varchar(50) NOT NULL DEFAULT '' COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `gender` tinyint NOT NULL DEFAULT '0' COMMENT '性别：0-未知，1-男，2-女',
  `birthday` varchar(10) DEFAULT NULL COMMENT '生日（YYYY-MM-DD）',
  `location` varchar(100) DEFAULT NULL COMMENT '地区',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-正常，2-冻结，3-注销',
  `loginType` tinyint NOT NULL DEFAULT '1' COMMENT '注册类型：1-手机，2-Google，3-Apple',
  `googleId` varchar(100) DEFAULT NULL COMMENT 'Google账户ID',
  `appleId` varchar(100) DEFAULT NULL COMMENT 'Apple账户ID',
  `lastLoginAt` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `lastLoginIp` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`userId`),
  UNIQUE KEY `uk_phone` (`phone`),
  UNIQUE KEY `uk_email` (`email`),
  UNIQUE KEY `uk_google_id` (`googleId`),
  UNIQUE KEY `uk_apple_id` (`appleId`),
  KEY `idx_status` (`status`),
  KEY `idx_login_type` (`loginType`),
  KEY `idx_created_at` (`createdAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表01';

DROP TABLE IF EXISTS `user02`;
CREATE TABLE `user02` (
  `userId` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `nickname` varchar(50) NOT NULL DEFAULT '' COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `gender` tinyint NOT NULL DEFAULT '0' COMMENT '性别：0-未知，1-男，2-女',
  `birthday` varchar(10) DEFAULT NULL COMMENT '生日（YYYY-MM-DD）',
  `location` varchar(100) DEFAULT NULL COMMENT '地区',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-正常，2-冻结，3-注销',
  `loginType` tinyint NOT NULL DEFAULT '1' COMMENT '注册类型：1-手机，2-Google，3-Apple',
  `googleId` varchar(100) DEFAULT NULL COMMENT 'Google账户ID',
  `appleId` varchar(100) DEFAULT NULL COMMENT 'Apple账户ID',
  `lastLoginAt` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `lastLoginIp` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`userId`),
  UNIQUE KEY `uk_phone` (`phone`),
  UNIQUE KEY `uk_email` (`email`),
  UNIQUE KEY `uk_google_id` (`googleId`),
  UNIQUE KEY `uk_apple_id` (`appleId`),
  KEY `idx_status` (`status`),
  KEY `idx_login_type` (`loginType`),
  KEY `idx_created_at` (`createdAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表02';

DROP TABLE IF EXISTS `user03`;
CREATE TABLE `user03` (
  `userId` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `nickname` varchar(50) NOT NULL DEFAULT '' COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `gender` tinyint NOT NULL DEFAULT '0' COMMENT '性别：0-未知，1-男，2-女',
  `birthday` varchar(10) DEFAULT NULL COMMENT '生日（YYYY-MM-DD）',
  `location` varchar(100) DEFAULT NULL COMMENT '地区',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-正常，2-冻结，3-注销',
  `loginType` tinyint NOT NULL DEFAULT '1' COMMENT '注册类型：1-手机，2-Google，3-Apple',
  `googleId` varchar(100) DEFAULT NULL COMMENT 'Google账户ID',
  `appleId` varchar(100) DEFAULT NULL COMMENT 'Apple账户ID',
  `lastLoginAt` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `lastLoginIp` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`userId`),
  UNIQUE KEY `uk_phone` (`phone`),
  UNIQUE KEY `uk_email` (`email`),
  UNIQUE KEY `uk_google_id` (`googleId`),
  UNIQUE KEY `uk_apple_id` (`appleId`),
  KEY `idx_status` (`status`),
  KEY `idx_login_type` (`loginType`),
  KEY `idx_created_at` (`createdAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表03';

DROP TABLE IF EXISTS `user04`;
CREATE TABLE `user04` (
  `userId` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `nickname` varchar(50) NOT NULL DEFAULT '' COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `gender` tinyint NOT NULL DEFAULT '0' COMMENT '性别：0-未知，1-男，2-女',
  `birthday` varchar(10) DEFAULT NULL COMMENT '生日（YYYY-MM-DD）',
  `location` varchar(100) DEFAULT NULL COMMENT '地区',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-正常，2-冻结，3-注销',
  `loginType` tinyint NOT NULL DEFAULT '1' COMMENT '注册类型：1-手机，2-Google，3-Apple',
  `googleId` varchar(100) DEFAULT NULL COMMENT 'Google账户ID',
  `appleId` varchar(100) DEFAULT NULL COMMENT 'Apple账户ID',
  `lastLoginAt` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `lastLoginIp` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`userId`),
  UNIQUE KEY `uk_phone` (`phone`),
  UNIQUE KEY `uk_email` (`email`),
  UNIQUE KEY `uk_google_id` (`googleId`),
  UNIQUE KEY `uk_apple_id` (`appleId`),
  KEY `idx_status` (`status`),
  KEY `idx_login_type` (`loginType`),
  KEY `idx_created_at` (`createdAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表04';

DROP TABLE IF EXISTS `user05`;
CREATE TABLE `user05` (
  `userId` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `nickname` varchar(50) NOT NULL DEFAULT '' COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `gender` tinyint NOT NULL DEFAULT '0' COMMENT '性别：0-未知，1-男，2-女',
  `birthday` varchar(10) DEFAULT NULL COMMENT '生日（YYYY-MM-DD）',
  `location` varchar(100) DEFAULT NULL COMMENT '地区',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-正常，2-冻结，3-注销',
  `loginType` tinyint NOT NULL DEFAULT '1' COMMENT '注册类型：1-手机，2-Google，3-Apple',
  `googleId` varchar(100) DEFAULT NULL COMMENT 'Google账户ID',
  `appleId` varchar(100) DEFAULT NULL COMMENT 'Apple账户ID',
  `lastLoginAt` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `lastLoginIp` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`userId`),
  UNIQUE KEY `uk_phone` (`phone`),
  UNIQUE KEY `uk_email` (`email`),
  UNIQUE KEY `uk_google_id` (`googleId`),
  UNIQUE KEY `uk_apple_id` (`appleId`),
  KEY `idx_status` (`status`),
  KEY `idx_login_type` (`loginType`),
  KEY `idx_created_at` (`createdAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表05';

DROP TABLE IF EXISTS `user06`;
CREATE TABLE `user06` (
  `userId` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `nickname` varchar(50) NOT NULL DEFAULT '' COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `gender` tinyint NOT NULL DEFAULT '0' COMMENT '性别：0-未知，1-男，2-女',
  `birthday` varchar(10) DEFAULT NULL COMMENT '生日（YYYY-MM-DD）',
  `location` varchar(100) DEFAULT NULL COMMENT '地区',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-正常，2-冻结，3-注销',
  `loginType` tinyint NOT NULL DEFAULT '1' COMMENT '注册类型：1-手机，2-Google，3-Apple',
  `googleId` varchar(100) DEFAULT NULL COMMENT 'Google账户ID',
  `appleId` varchar(100) DEFAULT NULL COMMENT 'Apple账户ID',
  `lastLoginAt` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `lastLoginIp` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`userId`),
  UNIQUE KEY `uk_phone` (`phone`),
  UNIQUE KEY `uk_email` (`email`),
  UNIQUE KEY `uk_google_id` (`googleId`),
  UNIQUE KEY `uk_apple_id` (`appleId`),
  KEY `idx_status` (`status`),
  KEY `idx_login_type` (`loginType`),
  KEY `idx_created_at` (`createdAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表06';

-- 登录日志表（分表 loginLog00-loginLog06）
DROP TABLE IF EXISTS `loginLog00`;
CREATE TABLE `loginLog00` (
  `logId` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `userId` bigint unsigned NOT NULL COMMENT '用户ID',
  `loginType` tinyint NOT NULL COMMENT '登录类型：1-手机短信，2-Google，3-Apple',
  `loginIp` varchar(45) NOT NULL COMMENT '登录IP',
  `userAgent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `deviceId` varchar(100) DEFAULT NULL COMMENT '设备ID',
  `loginResult` tinyint NOT NULL COMMENT '登录结果：1-成功，2-失败',
  `failReason` varchar(255) DEFAULT NULL COMMENT '失败原因',
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`logId`),
  KEY `idx_user_id` (`userId`),
  KEY `idx_login_type` (`loginType`),
  KEY `idx_login_result` (`loginResult`),
  KEY `idx_login_ip` (`loginIp`),
  KEY `idx_created_at` (`createdAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='登录日志表00';

DROP TABLE IF EXISTS `loginLog01`;
CREATE TABLE `loginLog01` (
  `logId` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `userId` bigint unsigned NOT NULL COMMENT '用户ID',
  `loginType` tinyint NOT NULL COMMENT '登录类型：1-手机短信，2-Google，3-Apple',
  `loginIp` varchar(45) NOT NULL COMMENT '登录IP',
  `userAgent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `deviceId` varchar(100) DEFAULT NULL COMMENT '设备ID',
  `loginResult` tinyint NOT NULL COMMENT '登录结果：1-成功，2-失败',
  `failReason` varchar(255) DEFAULT NULL COMMENT '失败原因',
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`logId`),
  KEY `idx_user_id` (`userId`),
  KEY `idx_login_type` (`loginType`),
  KEY `idx_login_result` (`loginResult`),
  KEY `idx_login_ip` (`loginIp`),
  KEY `idx_created_at` (`createdAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='登录日志表01';

DROP TABLE IF EXISTS `loginLog02`;
CREATE TABLE `loginLog02` (
  `logId` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `userId` bigint unsigned NOT NULL COMMENT '用户ID',
  `loginType` tinyint NOT NULL COMMENT '登录类型：1-手机短信，2-Google，3-Apple',
  `loginIp` varchar(45) NOT NULL COMMENT '登录IP',
  `userAgent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `deviceId` varchar(100) DEFAULT NULL COMMENT '设备ID',
  `loginResult` tinyint NOT NULL COMMENT '登录结果：1-成功，2-失败',
  `failReason` varchar(255) DEFAULT NULL COMMENT '失败原因',
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`logId`),
  KEY `idx_user_id` (`userId`),
  KEY `idx_login_type` (`loginType`),
  KEY `idx_login_result` (`loginResult`),
  KEY `idx_login_ip` (`loginIp`),
  KEY `idx_created_at` (`createdAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='登录日志表02';

DROP TABLE IF EXISTS `loginLog03`;
CREATE TABLE `loginLog03` (
  `logId` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `userId` bigint unsigned NOT NULL COMMENT '用户ID',
  `loginType` tinyint NOT NULL COMMENT '登录类型：1-手机短信，2-Google，3-Apple',
  `loginIp` varchar(45) NOT NULL COMMENT '登录IP',
  `userAgent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `deviceId` varchar(100) DEFAULT NULL COMMENT '设备ID',
  `loginResult` tinyint NOT NULL COMMENT '登录结果：1-成功，2-失败',
  `failReason` varchar(255) DEFAULT NULL COMMENT '失败原因',
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`logId`),
  KEY `idx_user_id` (`userId`),
  KEY `idx_login_type` (`loginType`),
  KEY `idx_login_result` (`loginResult`),
  KEY `idx_login_ip` (`loginIp`),
  KEY `idx_created_at` (`createdAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='登录日志表03';

DROP TABLE IF EXISTS `loginLog04`;
CREATE TABLE `loginLog04` (
  `logId` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `userId` bigint unsigned NOT NULL COMMENT '用户ID',
  `loginType` tinyint NOT NULL COMMENT '登录类型：1-手机短信，2-Google，3-Apple',
  `loginIp` varchar(45) NOT NULL COMMENT '登录IP',
  `userAgent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `deviceId` varchar(100) DEFAULT NULL COMMENT '设备ID',
  `loginResult` tinyint NOT NULL COMMENT '登录结果：1-成功，2-失败',
  `failReason` varchar(255) DEFAULT NULL COMMENT '失败原因',
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`logId`),
  KEY `idx_user_id` (`userId`),
  KEY `idx_login_type` (`loginType`),
  KEY `idx_login_result` (`loginResult`),
  KEY `idx_login_ip` (`loginIp`),
  KEY `idx_created_at` (`createdAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='登录日志表04';

DROP TABLE IF EXISTS `loginLog05`;
CREATE TABLE `loginLog05` (
  `logId` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `userId` bigint unsigned NOT NULL COMMENT '用户ID',
  `loginType` tinyint NOT NULL COMMENT '登录类型：1-手机短信，2-Google，3-Apple',
  `loginIp` varchar(45) NOT NULL COMMENT '登录IP',
  `userAgent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `deviceId` varchar(100) DEFAULT NULL COMMENT '设备ID',
  `loginResult` tinyint NOT NULL COMMENT '登录结果：1-成功，2-失败',
  `failReason` varchar(255) DEFAULT NULL COMMENT '失败原因',
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`logId`),
  KEY `idx_user_id` (`userId`),
  KEY `idx_login_type` (`loginType`),
  KEY `idx_login_result` (`loginResult`),
  KEY `idx_login_ip` (`loginIp`),
  KEY `idx_created_at` (`createdAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='登录日志表05';

DROP TABLE IF EXISTS `loginLog06`;
CREATE TABLE `loginLog06` (
  `logId` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `userId` bigint unsigned NOT NULL COMMENT '用户ID',
  `loginType` tinyint NOT NULL COMMENT '登录类型：1-手机短信，2-Google，3-Apple',
  `loginIp` varchar(45) NOT NULL COMMENT '登录IP',
  `userAgent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `deviceId` varchar(100) DEFAULT NULL COMMENT '设备ID',
  `loginResult` tinyint NOT NULL COMMENT '登录结果：1-成功，2-失败',
  `failReason` varchar(255) DEFAULT NULL COMMENT '失败原因',
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`logId`),
  KEY `idx_user_id` (`userId`),
  KEY `idx_login_type` (`loginType`),
  KEY `idx_login_result` (`loginResult`),
  KEY `idx_login_ip` (`loginIp`),
  KEY `idx_created_at` (`createdAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='登录日志表06';

-- 短信验证码表（不分表）
DROP TABLE IF EXISTS `smsCode`;
CREATE TABLE `smsCode` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `code` varchar(10) NOT NULL COMMENT '验证码',
  `codeType` tinyint NOT NULL COMMENT '验证码类型：1-注册，2-登录',
  `isUsed` tinyint NOT NULL DEFAULT '0' COMMENT '是否已使用：0-未使用，1-已使用',
  `expireAt` timestamp NOT NULL COMMENT '过期时间',
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_phone_type` (`phone`, `codeType`),
  KEY `idx_expire_at` (`expireAt`),
  KEY `idx_created_at` (`createdAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='短信验证码表';

-- 用户会话表（不分表）
DROP TABLE IF EXISTS `userSession`;
CREATE TABLE `userSession` (
  `sessionId` varchar(64) NOT NULL COMMENT '会话ID',
  `userId` bigint unsigned NOT NULL COMMENT '用户ID',
  `token` varchar(500) NOT NULL COMMENT 'JWT token',
  `deviceId` varchar(100) DEFAULT NULL COMMENT '设备ID',
  `userAgent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `loginIp` varchar(45) DEFAULT NULL COMMENT '登录IP',
  `expireAt` timestamp NOT NULL COMMENT '过期时间',
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`sessionId`),
  KEY `idx_user_id` (`userId`),
  KEY `idx_expire_at` (`expireAt`),
  KEY `idx_created_at` (`createdAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会话表';
