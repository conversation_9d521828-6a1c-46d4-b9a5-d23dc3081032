#!/bin/bash

# 批量更新user_id类型的脚本
# 将所有测试文件中的user_id从string改为uint64

echo "🔄 批量更新user_id类型..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 更新计数
UPDATE_COUNT=0

# 更新函数
update_file() {
    local file="$1"
    local description="$2"
    
    if [ -f "$file" ]; then
        echo -e "${BLUE}📝 更新文件: $file${NC}"
        echo "   描述: $description"
        
        # 备份原文件
        cp "$file" "$file.bak"
        
        # 执行替换
        # 1. 更新测试常量中的user_id
        sed -i 's/testUserId = "test_user_123"/testUserId = uint64(123)/g' "$file"
        sed -i 's/testUserId = "user_123"/testUserId = uint64(123)/g' "$file"
        
        # 2. 更新测试数据中的字符串user_id为数字
        sed -i 's/UserId:.*""/UserId: 0/g' "$file"
        sed -i 's/UserId:.*"user123"/UserId: 123/g' "$file"
        sed -i 's/UserId:.*"test_user_123"/UserId: 123/g' "$file"
        sed -i 's/UserId:.*"test_user_456"/UserId: 456/g' "$file"
        sed -i 's/UserId:.*"test_user_789"/UserId: 789/g' "$file"
        sed -i 's/UserId:.*testUserId + "_new"/UserId: testUserId + 1/g' "$file"
        sed -i 's/UserId:.*testUserId + "_create_test"/UserId: testUserId + 2/g' "$file"
        
        # 3. 更新日志输出中的格式
        sed -i 's/UserId=%s/UserId=%d/g' "$file"
        
        # 检查是否有变化
        if ! diff -q "$file" "$file.bak" > /dev/null; then
            echo -e "${GREEN}   ✅ 文件已更新${NC}"
            ((UPDATE_COUNT++))
        else
            echo -e "${YELLOW}   ⚠️  文件无需更新${NC}"
            rm "$file.bak"
        fi
    else
        echo -e "${RED}   ❌ 文件不存在: $file${NC}"
    fi
}

# 项目根目录
PROJECT_ROOT=$(cd "$(dirname "$0")/.." && pwd)
echo -e "${BLUE}📁 项目根目录: $PROJECT_ROOT${NC}"

echo ""
echo "🔍 查找并更新测试文件..."

# 更新Purchase服务测试文件
echo ""
echo "📋 更新Purchase服务测试文件..."
update_file "$PROJECT_ROOT/gen2/app/base/purchase/svc/purchase_test.go" "Purchase服务参数验证测试"
update_file "$PROJECT_ROOT/gen2/app/base/purchase/svc/purchase_standard_test.go" "Purchase服务标准测试"
update_file "$PROJECT_ROOT/gen2/app/base/purchase/svc/purchase_production_test.go" "Purchase服务生产级测试"
update_file "$PROJECT_ROOT/gen2/app/base/purchase/svc/purchase_real_test.go" "Purchase服务真实DAO测试"
update_file "$PROJECT_ROOT/gen2/app/base/purchase/svc/purchase_integration_test.go" "Purchase服务集成测试"

# 更新Account服务测试文件
echo ""
echo "📋 更新Account服务测试文件..."
update_file "$PROJECT_ROOT/gen2/app/base/account/svc/account_test.go" "Account服务参数验证测试"
update_file "$PROJECT_ROOT/gen2/app/base/account/svc/account_standard_test.go" "Account服务标准测试"
update_file "$PROJECT_ROOT/gen2/app/base/account/svc/account_production_test.go" "Account服务生产级测试"

# 更新其他目录中的测试文件
echo ""
echo "📋 更新其他目录中的测试文件..."
update_file "$PROJECT_ROOT/app/base/purchase/svc/purchase_test.go" "Purchase服务测试(app目录)"
update_file "$PROJECT_ROOT/app/base/purchase/svc/purchase_standard_test.go" "Purchase服务标准测试(app目录)"
update_file "$PROJECT_ROOT/app/base/purchase/svc/purchase_production_test.go" "Purchase服务生产级测试(app目录)"
update_file "$PROJECT_ROOT/app/base/account/svc/account_test.go" "Account服务测试(app目录)"
update_file "$PROJECT_ROOT/app/base/account/svc/account_standard_test.go" "Account服务标准测试(app目录)"
update_file "$PROJECT_ROOT/app/base/account/svc/account_production_test.go" "Account服务生产级测试(app目录)"

echo ""
echo "📊 更新统计:"
echo "总共更新了 $UPDATE_COUNT 个文件"

if [ $UPDATE_COUNT -gt 0 ]; then
    echo ""
    echo -e "${GREEN}✅ 更新完成！${NC}"
    echo ""
    echo "📝 更新内容:"
    echo "1. 测试常量中的user_id类型从string改为uint64"
    echo "2. 测试数据中的user_id值从字符串改为数字"
    echo "3. 日志输出格式从%s改为%d"
    echo ""
    echo "🔍 备份文件:"
    echo "原文件已备份为 .bak 文件，如需回滚可以使用备份文件"
    echo ""
    echo "🧪 下一步:"
    echo "1. 检查更新后的文件是否正确"
    echo "2. 运行测试验证更新是否成功"
    echo "3. 如果测试通过，可以删除备份文件"
else
    echo ""
    echo -e "${YELLOW}⚠️  没有文件需要更新${NC}"
fi

echo ""
echo "🛠️  手动检查项目:"
echo "1. 检查proto文件是否已更新user_id类型"
echo "2. 检查PO结构体是否已更新user_id类型"
echo "3. 检查数据库表结构是否已更新user_id类型"
echo "4. 检查DAO层的SQL语句是否需要调整"
echo "5. 检查服务层的类型转换是否正确"

echo ""
echo "📚 相关文件:"
echo "- Proto文件: gen2/proto/account/account.proto"
echo "- Proto文件: gen2/proto/purchase/purchase.proto"
echo "- PO结构体: gen2/app/common/po/account.go"
echo "- 数据库脚本: gen2/scripts/init_test_db.sql"
