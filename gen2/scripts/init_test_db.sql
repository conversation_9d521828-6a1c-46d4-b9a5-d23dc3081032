-- 测试数据库初始化脚本

-- 创建测试数据库
CREATE DATABASE IF NOT EXISTS `beyondreading_test` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `beyondreading_test`;

-- 创建账户表
CREATE TABLE IF NOT EXISTS `account` (
  `account_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '账户ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `coin_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '书币余额',
  `total_recharged` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总充值金额',
  `total_consumed` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总消费金额',
  `user_type` varchar(20) NOT NULL DEFAULT 'normal' COMMENT '用户类型：normal-普通用户，vip-VIP用户，monthly-包月用户',
  `user_level` int(11) NOT NULL DEFAULT '1' COMMENT '用户等级',
  `vip_expire_time` datetime DEFAULT NULL COMMENT 'VIP过期时间',
  `monthly_expire_time` datetime DEFAULT NULL COMMENT '包月过期时间',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '账户状态：1-正常，2-冻结，3-注销',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`account_id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='账户表';

-- 创建账户日志表
CREATE TABLE IF NOT EXISTS `account_log` (
  `log_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `account_id` bigint(20) NOT NULL COMMENT '账户ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `log_type` varchar(20) NOT NULL COMMENT '日志类型：recharge-充值，consume-消费，refund-退款',
  `amount` decimal(10,2) NOT NULL COMMENT '金额',
  `balance_before` decimal(10,2) NOT NULL COMMENT '操作前余额',
  `balance_after` decimal(10,2) NOT NULL COMMENT '操作后余额',
  `order_type` varchar(20) DEFAULT NULL COMMENT '订单类型：chapter-章节，monthly-包月，vip-VIP',
  `order_id` varchar(64) DEFAULT NULL COMMENT '订单ID',
  `book_id` varchar(64) DEFAULT NULL COMMENT '书籍ID',
  `chapter_id` varchar(64) DEFAULT NULL COMMENT '章节ID',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_log_type` (`log_type`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='账户日志表';

-- 创建充值订单表
CREATE TABLE IF NOT EXISTS `recharge_order` (
  `order_id` varchar(64) NOT NULL COMMENT '订单ID',
  `account_id` bigint(20) NOT NULL COMMENT '账户ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `amount` decimal(10,2) NOT NULL COMMENT '充值金额',
  `coin_amount` decimal(10,2) NOT NULL COMMENT '书币数量',
  `payment_method` varchar(20) NOT NULL COMMENT '支付方式：alipay-支付宝，wechat-微信，bank-银行卡',
  `payment_id` varchar(128) DEFAULT NULL COMMENT '支付平台订单ID',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '订单状态：1-待支付，2-已支付，3-已取消，4-已退款',
  `paid_at` datetime DEFAULT NULL COMMENT '支付时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='充值订单表';

-- 创建购买订单表
CREATE TABLE IF NOT EXISTS `purchase_order` (
  `order_id` varchar(64) NOT NULL COMMENT '订单ID',
  `account_id` bigint(20) NOT NULL COMMENT '账户ID',
  `user_id` bigint(20) unsigned NOT NULL COMMENT '用户ID',
  `order_type` varchar(20) NOT NULL COMMENT '订单类型：chapter-章节，monthly-包月，vip-VIP',
  `book_id` varchar(64) DEFAULT NULL COMMENT '书籍ID',
  `book_name` varchar(255) DEFAULT NULL COMMENT '书籍名称',
  `chapter_id` varchar(64) DEFAULT NULL COMMENT '章节ID',
  `chapter_title` varchar(255) DEFAULT NULL COMMENT '章节标题',
  `chapter_order` int(11) DEFAULT NULL COMMENT '章节序号',
  `coin_amount` decimal(10,2) NOT NULL COMMENT '消费书币数量',
  `duration_days` int(11) DEFAULT NULL COMMENT '有效期天数（VIP/包月）',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间（VIP/包月）',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间（VIP/包月）',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '订单状态：1-待支付，2-已支付，3-已取消，4-已退款',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_type` (`order_type`),
  KEY `idx_book_id` (`book_id`),
  KEY `idx_chapter_id` (`chapter_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  UNIQUE KEY `uk_user_book_chapter` (`user_id`, `book_id`, `chapter_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='购买订单表';

-- 插入测试数据
INSERT INTO `account` (`user_id`, `coin_balance`, `total_recharged`, `total_consumed`, `user_type`, `user_level`, `status`) VALUES
(123, '1000.00', '1000.00', '0.00', 'normal', 1, 1),
(456, '500.00', '500.00', '200.00', 'normal', 1, 1),
(789, '2000.00', '2000.00', '1500.00', 'vip', 2, 1);

-- 插入测试账户日志
INSERT INTO `account_log` (`account_id`, `user_id`, `log_type`, `amount`, `balance_before`, `balance_after`, `order_type`, `description`) VALUES
(1, 123, 'recharge', '1000.00', '0.00', '1000.00', NULL, '测试充值'),
(2, 456, 'recharge', '500.00', '0.00', '500.00', NULL, '测试充值'),
(2, 456, 'consume', '200.00', '500.00', '300.00', 'chapter', '购买章节'),
(3, 789, 'recharge', '2000.00', '0.00', '2000.00', NULL, '测试充值'),
(3, 789, 'consume', '1500.00', '2000.00', '500.00', 'vip', '购买VIP');

-- 插入测试充值订单
INSERT INTO `recharge_order` (`order_id`, `account_id`, `user_id`, `amount`, `coin_amount`, `payment_method`, `status`, `paid_at`) VALUES
('****************', 1, 123, '100.00', '1000.00', 'alipay', 2, NOW()),
('****************', 2, 456, '50.00', '500.00', 'wechat', 2, NOW()),
('****************', 3, 789, '200.00', '2000.00', 'alipay', 2, NOW());

-- 插入测试购买订单
INSERT INTO `purchase_order` (`order_id`, `account_id`, `user_id`, `order_type`, `book_id`, `book_name`, `chapter_id`, `chapter_title`, `chapter_order`, `coin_amount`, `status`) VALUES
('****************', 2, 456, 'chapter', 'book_001', '测试书籍1', 'chapter_001', '第一章', 1, '5.00', 2),
('****************', 2, 456, 'chapter', 'book_001', '测试书籍1', 'chapter_002', '第二章', 2, '5.00', 2);

INSERT INTO `purchase_order` (`order_id`, `account_id`, `user_id`, `order_type`, `coin_amount`, `duration_days`, `start_time`, `end_time`, `status`) VALUES
('****************', 3, 789, 'vip', '100.00', 30, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), 2);

-- 创建索引优化查询性能
CREATE INDEX `idx_account_user_type_status` ON `account` (`user_type`, `status`);
CREATE INDEX `idx_account_log_user_type` ON `account_log` (`user_id`, `log_type`, `created_at`);
CREATE INDEX `idx_purchase_order_user_book` ON `purchase_order` (`user_id`, `book_id`, `order_type`);
CREATE INDEX `idx_purchase_order_time_range` ON `purchase_order` (`start_time`, `end_time`);

-- 显示创建结果
SELECT 'Database and tables created successfully!' as result;
SELECT COUNT(*) as account_count FROM account;
SELECT COUNT(*) as account_log_count FROM account_log;
SELECT COUNT(*) as recharge_order_count FROM recharge_order;
SELECT COUNT(*) as purchase_order_count FROM purchase_order;
