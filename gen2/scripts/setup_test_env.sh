#!/bin/bash

# 测试环境快速设置脚本

echo "🚀 设置 BeyondReading 测试环境..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 错误处理
set -e

# 检查必要的命令
check_command() {
    if ! command -v $1 &> /dev/null; then
        echo -e "${RED}❌ $1 未安装，请先安装 $1${NC}"
        exit 1
    fi
}

echo "🔍 检查依赖..."
check_command "go"
check_command "mysql"

# 检查Go版本
GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
echo -e "${GREEN}✅ Go版本: $GO_VERSION${NC}"

# 检查MySQL服务
if ! systemctl is-active --quiet mysql 2>/dev/null && ! pgrep mysqld > /dev/null; then
    echo -e "${YELLOW}⚠️  MySQL服务未运行，尝试启动...${NC}"
    if command -v systemctl &> /dev/null; then
        sudo systemctl start mysql || echo -e "${RED}❌ 无法启动MySQL服务${NC}"
    else
        echo -e "${YELLOW}⚠️  请手动启动MySQL服务${NC}"
    fi
fi

# 设置项目根目录
PROJECT_ROOT=$(cd "$(dirname "$0")/.." && pwd)
echo -e "${BLUE}📁 项目根目录: $PROJECT_ROOT${NC}"

# 初始化数据库
echo ""
echo "🗄️  初始化测试数据库..."
DB_SCRIPT="$PROJECT_ROOT/scripts/init_test_db.sql"

if [ -f "$DB_SCRIPT" ]; then
    echo "执行数据库初始化脚本..."
    read -p "请输入MySQL root密码: " -s MYSQL_PASSWORD
    echo
    
    if mysql -u root -p$MYSQL_PASSWORD < "$DB_SCRIPT"; then
        echo -e "${GREEN}✅ 数据库初始化成功${NC}"
    else
        echo -e "${RED}❌ 数据库初始化失败${NC}"
        exit 1
    fi
else
    echo -e "${RED}❌ 数据库脚本不存在: $DB_SCRIPT${NC}"
    exit 1
fi

# 检查Redis（可选）
echo ""
echo "🔍 检查Redis服务..."
if command -v redis-cli &> /dev/null; then
    if redis-cli ping > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Redis服务正常${NC}"
    else
        echo -e "${YELLOW}⚠️  Redis服务未运行，某些缓存功能可能不可用${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  Redis未安装，某些缓存功能可能不可用${NC}"
fi

# 检查配置文件
echo ""
echo "📋 检查配置文件..."

CONFIG_FILES=(
    "$PROJECT_ROOT/app/base/account/cmd/config.toml"
    "$PROJECT_ROOT/app/base/purchase/cmd/config.toml"
)

for config_file in "${CONFIG_FILES[@]}"; do
    if [ -f "$config_file" ]; then
        echo -e "${GREEN}✅ 配置文件存在: $config_file${NC}"
    else
        echo -e "${RED}❌ 配置文件缺失: $config_file${NC}"
        exit 1
    fi
done

# 编译项目
echo ""
echo "🔨 编译项目..."

cd "$PROJECT_ROOT"

# 编译Account服务
echo "编译Account服务..."
cd "$PROJECT_ROOT/app/base/account/svc"
if go build .; then
    echo -e "${GREEN}✅ Account服务编译成功${NC}"
else
    echo -e "${RED}❌ Account服务编译失败${NC}"
    exit 1
fi

# 编译Purchase服务
echo "编译Purchase服务..."
cd "$PROJECT_ROOT/app/base/purchase/svc"
if go build .; then
    echo -e "${GREEN}✅ Purchase服务编译成功${NC}"
else
    echo -e "${RED}❌ Purchase服务编译失败${NC}"
    exit 1
fi

# 运行测试
echo ""
echo "🧪 运行测试..."

# 运行Account服务测试
echo "运行Account服务测试..."
cd "$PROJECT_ROOT/app/base/account/svc"
if [ -f "account_production_test.go" ]; then
    echo "运行生产级测试..."
    go test -v -short -run "Test.*Production" -timeout 30s
    echo -e "${GREEN}✅ Account服务测试完成${NC}"
else
    echo -e "${YELLOW}⚠️  Account生产级测试文件不存在${NC}"
fi

# 运行Purchase服务测试
echo ""
echo "运行Purchase服务测试..."
cd "$PROJECT_ROOT/app/base/purchase/svc"
if [ -f "purchase_production_test.go" ]; then
    echo "运行生产级测试..."
    go test -v -short -run "TestPurchase.*Production" -timeout 30s
    echo -e "${GREEN}✅ Purchase服务测试完成${NC}"
else
    echo -e "${YELLOW}⚠️  Purchase生产级测试文件不存在${NC}"
fi

# 生成测试报告
echo ""
echo "📊 生成测试报告..."

cd "$PROJECT_ROOT/app/base/account/svc"
if go test -coverprofile=account_coverage.out -run "Test.*Production" -timeout 30s > /dev/null 2>&1; then
    go tool cover -html=account_coverage.out -o account_coverage.html
    echo -e "${GREEN}✅ Account服务覆盖率报告: account_coverage.html${NC}"
fi

cd "$PROJECT_ROOT/app/base/purchase/svc"
if go test -coverprofile=purchase_coverage.out -run "TestPurchase.*Production" -timeout 30s > /dev/null 2>&1; then
    go tool cover -html=purchase_coverage.out -o purchase_coverage.html
    echo -e "${GREEN}✅ Purchase服务覆盖率报告: purchase_coverage.html${NC}"
fi

# 显示总结
echo ""
echo "🎉 测试环境设置完成！"
echo ""
echo "📋 环境信息:"
echo "- 项目根目录: $PROJECT_ROOT"
echo "- 测试数据库: beyondreading_test"
echo "- Go版本: $GO_VERSION"
echo ""
echo "🚀 下一步操作:"
echo "1. 启动Account服务:"
echo "   cd $PROJECT_ROOT/app/base/account/cmd && go run main.go"
echo ""
echo "2. 启动Purchase服务:"
echo "   cd $PROJECT_ROOT/app/base/purchase/cmd && go run main.go"
echo ""
echo "3. 运行完整测试:"
echo "   cd $PROJECT_ROOT/app/base/purchase/svc && ./run_production_tests.sh"
echo "   cd $PROJECT_ROOT/app/base/account/svc && ./run_production_tests.sh"
echo ""
echo "4. 查看测试覆盖率报告:"
echo "   - Account: $PROJECT_ROOT/app/base/account/svc/account_coverage.html"
echo "   - Purchase: $PROJECT_ROOT/app/base/purchase/svc/purchase_coverage.html"
echo ""
echo "📚 文档:"
echo "- 生产级测试文档: $PROJECT_ROOT/docs/production_tests.md"
echo "- 测试说明: $PROJECT_ROOT/app/base/purchase/svc/README_TESTS.md"
echo ""
echo -e "${GREEN}✨ 环境设置成功，开始测试吧！${NC}"
