#!/bin/bash

# 标准测试环境验证脚本

echo "🔍 验证标准测试环境..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 错误计数
ERROR_COUNT=0

# 检查函数
check_file() {
    if [ -f "$1" ]; then
        echo -e "${GREEN}✅ $1${NC}"
    else
        echo -e "${RED}❌ $1 (缺失)${NC}"
        ((ERROR_COUNT++))
    fi
}

check_directory() {
    if [ -d "$1" ]; then
        echo -e "${GREEN}✅ $1${NC}"
    else
        echo -e "${RED}❌ $1 (缺失)${NC}"
        ((ERROR_COUNT++))
    fi
}

# 设置项目根目录
PROJECT_ROOT=$(cd "$(dirname "$0")/.." && pwd)
echo -e "${BLUE}📁 项目根目录: $PROJECT_ROOT${NC}"

echo ""
echo "🔍 检查配置文件..."

# 检查配置文件
CONFIG_FILES=(
    "$PROJECT_ROOT/config/base-purchase.toml"
    "$PROJECT_ROOT/config/base-account.toml"
)

for config_file in "${CONFIG_FILES[@]}"; do
    check_file "$config_file"
done

echo ""
echo "🔍 检查API应用名称..."

# 检查API应用名称
PURCHASE_API_FILE="$PROJECT_ROOT/app/base/purchase/api/api.go"
ACCOUNT_API_FILE="$PROJECT_ROOT/app/base/account/api/api.go"

if [ -f "$PURCHASE_API_FILE" ]; then
    PURCHASE_APP=$(grep "const App" "$PURCHASE_API_FILE" | sed 's/.*= *"\([^"]*\)".*/\1/')
    echo -e "${GREEN}✅ Purchase API App: $PURCHASE_APP${NC}"
    
    # 验证配置文件名是否匹配
    EXPECTED_CONFIG="$PROJECT_ROOT/config/$PURCHASE_APP.toml"
    if [ -f "$EXPECTED_CONFIG" ]; then
        echo -e "${GREEN}✅ 配置文件匹配: $PURCHASE_APP.toml${NC}"
    else
        echo -e "${RED}❌ 配置文件不匹配: 期望 $PURCHASE_APP.toml${NC}"
        ((ERROR_COUNT++))
    fi
else
    echo -e "${RED}❌ Purchase API文件不存在: $PURCHASE_API_FILE${NC}"
    ((ERROR_COUNT++))
fi

if [ -f "$ACCOUNT_API_FILE" ]; then
    ACCOUNT_APP=$(grep "const App" "$ACCOUNT_API_FILE" | sed 's/.*= *"\([^"]*\)".*/\1/')
    echo -e "${GREEN}✅ Account API App: $ACCOUNT_APP${NC}"
    
    # 验证配置文件名是否匹配
    EXPECTED_CONFIG="$PROJECT_ROOT/config/$ACCOUNT_APP.toml"
    if [ -f "$EXPECTED_CONFIG" ]; then
        echo -e "${GREEN}✅ 配置文件匹配: $ACCOUNT_APP.toml${NC}"
    else
        echo -e "${RED}❌ 配置文件不匹配: 期望 $ACCOUNT_APP.toml${NC}"
        ((ERROR_COUNT++))
    fi
else
    echo -e "${RED}❌ Account API文件不存在: $ACCOUNT_API_FILE${NC}"
    ((ERROR_COUNT++))
fi

echo ""
echo "🔍 检查测试文件..."

# 检查测试文件
TEST_FILES=(
    "$PROJECT_ROOT/app/base/purchase/svc/purchase_standard_test.go"
    "$PROJECT_ROOT/app/base/account/svc/account_standard_test.go"
    "$PROJECT_ROOT/app/base/purchase/svc/run_standard_tests.sh"
    "$PROJECT_ROOT/app/base/account/svc/run_standard_tests.sh"
)

for test_file in "${TEST_FILES[@]}"; do
    check_file "$test_file"
done

echo ""
echo "🔍 检查目录结构..."

# 检查目录结构
DIRECTORIES=(
    "$PROJECT_ROOT/config"
    "$PROJECT_ROOT/app/base/purchase/svc"
    "$PROJECT_ROOT/app/base/account/svc"
    "$PROJECT_ROOT/app/base/purchase/api"
    "$PROJECT_ROOT/app/base/account/api"
    "$PROJECT_ROOT/docs"
)

for directory in "${DIRECTORIES[@]}"; do
    check_directory "$directory"
done

echo ""
echo "🔍 检查Go环境..."

# 检查Go版本
if command -v go &> /dev/null; then
    GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
    echo -e "${GREEN}✅ Go版本: $GO_VERSION${NC}"
else
    echo -e "${RED}❌ Go未安装${NC}"
    ((ERROR_COUNT++))
fi

# 检查Go模块
cd "$PROJECT_ROOT"
if [ -f "go.mod" ]; then
    echo -e "${GREEN}✅ Go模块文件存在${NC}"
    MODULE_NAME=$(grep "module" go.mod | awk '{print $2}')
    echo -e "${BLUE}📦 模块名称: $MODULE_NAME${NC}"
else
    echo -e "${RED}❌ Go模块文件不存在${NC}"
    ((ERROR_COUNT++))
fi

echo ""
echo "🔍 验证配置文件内容..."

# 验证Purchase配置文件
PURCHASE_CONFIG="$PROJECT_ROOT/config/base-purchase.toml"
if [ -f "$PURCHASE_CONFIG" ]; then
    echo "检查Purchase配置文件内容..."
    
    # 检查必要的配置节
    REQUIRED_SECTIONS=("[log]" "[port]" "[redisPurchase]")
    for section in "${REQUIRED_SECTIONS[@]}"; do
        if grep -q "$section" "$PURCHASE_CONFIG"; then
            echo -e "${GREEN}✅ 配置节存在: $section${NC}"
        else
            echo -e "${YELLOW}⚠️  配置节缺失: $section${NC}"
        fi
    done
fi

# 验证Account配置文件
ACCOUNT_CONFIG="$PROJECT_ROOT/config/base-account.toml"
if [ -f "$ACCOUNT_CONFIG" ]; then
    echo "检查Account配置文件内容..."
    
    # 检查必要的配置节
    REQUIRED_SECTIONS=("[log]" "[port]" "[redisAccount]")
    for section in "${REQUIRED_SECTIONS[@]}"; do
        if grep -q "$section" "$ACCOUNT_CONFIG"; then
            echo -e "${GREEN}✅ 配置节存在: $section${NC}"
        else
            echo -e "${YELLOW}⚠️  配置节缺失: $section${NC}"
        fi
    done
fi

echo ""
echo "🔍 检查测试脚本权限..."

# 检查测试脚本权限
TEST_SCRIPTS=(
    "$PROJECT_ROOT/app/base/purchase/svc/run_standard_tests.sh"
    "$PROJECT_ROOT/app/base/account/svc/run_standard_tests.sh"
)

for script in "${TEST_SCRIPTS[@]}"; do
    if [ -f "$script" ]; then
        if [ -x "$script" ]; then
            echo -e "${GREEN}✅ $script (可执行)${NC}"
        else
            echo -e "${YELLOW}⚠️  $script (需要执行权限)${NC}"
            echo "   运行: chmod +x $script"
        fi
    fi
done

echo ""
echo "📊 验证结果总结:"

if [ $ERROR_COUNT -eq 0 ]; then
    echo -e "${GREEN}🎉 所有检查通过！标准测试环境已就绪。${NC}"
    
    echo ""
    echo "🚀 下一步操作:"
    echo "1. 运行Purchase服务标准测试:"
    echo "   cd $PROJECT_ROOT/app/base/purchase/svc"
    echo "   ./run_standard_tests.sh"
    echo ""
    echo "2. 运行Account服务标准测试:"
    echo "   cd $PROJECT_ROOT/app/base/account/svc"
    echo "   ./run_standard_tests.sh"
    echo ""
    echo "3. 查看标准测试文档:"
    echo "   cat $PROJECT_ROOT/docs/standard_tests.md"
    
else
    echo -e "${RED}❌ 发现 $ERROR_COUNT 个问题，请修复后再运行测试。${NC}"
    
    echo ""
    echo "🛠️  修复建议:"
    echo "1. 确保所有配置文件存在于 gen2/config/ 目录"
    echo "2. 检查API应用名称与配置文件名是否匹配"
    echo "3. 确保所有测试文件已生成"
    echo "4. 给测试脚本添加执行权限"
fi

echo ""
echo "📚 相关文档:"
echo "- 标准测试文档: $PROJECT_ROOT/docs/standard_tests.md"
echo "- 配置文件示例: $PROJECT_ROOT/config/"
echo "- 测试文件: $PROJECT_ROOT/app/base/*/svc/*_standard_test.go"

exit $ERROR_COUNT
