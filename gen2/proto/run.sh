#!/bin/bash

# Protocol Buffers代码生成脚本

echo "正在生成 Protocol Buffers Go 代码..."

# 生成account服务的proto代码
protoc --go_out=. --go-grpc_out=. --go-grpc_opt=require_unimplemented_servers=false ./account/account.proto

# 生成purchase服务的proto代码
protoc --go_out=. --go-grpc_out=. --go-grpc_opt=require_unimplemented_servers=false ./purchase/purchase.proto

if [ $? -eq 0 ]; then
    echo "✅ Protocol Buffers 代码生成成功！"
    echo "生成的文件："
    find . -name "*.pb.go" -type f
else
    echo "❌ Protocol Buffers 代码生成失败！"
    exit 1
fi
