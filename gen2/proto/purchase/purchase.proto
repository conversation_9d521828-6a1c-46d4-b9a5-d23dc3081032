syntax = "proto3";
package purchase;
option go_package = "creativematrix.com/beyondreading/gen2/proto/purchase";

service Purchase {
  // 购买章节
  rpc PurchaseChapter(PurchaseChapterReq) returns (PurchaseChapterResp);

  // 购买包月
  rpc PurchaseMonthly(PurchaseMonthlyReq) returns (PurchaseMonthlyResp);

  // 购买VIP
  rpc PurchaseVip(PurchaseVipReq) returns (PurchaseVipResp);

  // 获取购买订单列表
  rpc GetPurchaseOrders(GetPurchaseOrdersReq) returns (GetPurchaseOrdersResp);

  // 检查章节购买状态
  rpc CheckChapterPurchased(CheckChapterPurchasedReq) returns (CheckChapterPurchasedResp);

  // 检查包月状态
  rpc CheckMonthlyStatus(CheckMonthlyStatusReq) returns (CheckMonthlyStatusResp);

  // 获取用户已购买的章节列表
  rpc GetPurchasedChapters(GetPurchasedChaptersReq) returns (GetPurchasedChaptersResp);
}

// 购买订单
message PurchaseOrder {
  string order_id = 1;
  uint64 account_id = 2;
  uint64 user_id = 3;             // 用户ID改为uint64
  string order_type = 4;          // 订单类型：chapter, monthly, vip
  string book_id = 5;             // 书籍ID
  string book_name = 6;           // 书籍名称
  string chapter_id = 7;          // 章节ID（章节购买时使用）
  string chapter_title = 8;       // 章节标题
  uint32 chapter_order = 9;       // 章节序号
  double coin_amount = 10;        // 消费书币数量
  int32 duration_days = 11;       // 有效期天数（包月、VIP使用）
  int64 start_time = 12;          // 开始时间
  int64 end_time = 13;            // 结束时间
  int32 status = 14;              // 订单状态：1-待支付，2-支付成功，3-支付失败
  int64 created_at = 15;          // 创建时间
  int64 updated_at = 16;          // 更新时间
}

// 章节购买信息
message ChapterPurchaseInfo {
  string chapter_id = 1;
  uint32 chapter_order = 2;
  string order_id = 3;
  int64 purchased_at = 4;
  bool is_monthly = 5;            // 是否通过包月获得
}

// 请求和响应消息

// 购买章节
message PurchaseChapterReq {
  uint64 user_id = 1;             // 用户ID改为uint64
  string book_id = 2;
  uint32 chapter_order = 3;       // 章节序号
  double coin_amount = 4;         // 消费书币数量
}

message PurchaseChapterResp {
  int32 code = 1;
  string message = 2;
  PurchaseOrder order = 3;
}

// 购买包月
message PurchaseMonthlyReq {
  uint64 user_id = 1;             // 用户ID改为uint64
  string book_id = 2;
  string book_name = 3;
  double coin_amount = 4;
  int32 duration_days = 5;        // 包月天数，默认30天
}

message PurchaseMonthlyResp {
  int32 code = 1;
  string message = 2;
  PurchaseOrder order = 3;
}

// 购买VIP
message PurchaseVipReq {
  uint64 user_id = 1;             // 用户ID改为uint64
  double coin_amount = 2;
  int32 duration_days = 3;        // VIP天数
}

message PurchaseVipResp {
  int32 code = 1;
  string message = 2;
  PurchaseOrder order = 3;
}

// 获取购买订单列表
message GetPurchaseOrdersReq {
  uint64 user_id = 1;             // 用户ID改为uint64
  int32 page = 2;
  int32 page_size = 3;
  string order_type = 4;          // 可选，筛选订单类型
}

message GetPurchaseOrdersResp {
  int32 code = 1;
  string message = 2;
  repeated PurchaseOrder orders = 3;
  int64 total = 4;
}

// 检查章节购买状态
message CheckChapterPurchasedReq {
  uint64 user_id = 1;             // 用户ID改为uint64
  string book_id = 2;
  uint32 chapter_order = 3;
}

message CheckChapterPurchasedResp {
  int32 code = 1;
  string message = 2;
  bool is_purchased = 3;
  int64 purchased_at = 4;         // 购买时间
  bool is_monthly = 5;            // 是否通过包月获得
}

// 检查包月状态
message CheckMonthlyStatusReq {
  uint64 user_id = 1;             // 用户ID改为uint64
  string book_id = 2;
}

message CheckMonthlyStatusResp {
  int32 code = 1;
  string message = 2;
  bool is_active = 3;
  int64 start_time = 4;           // 开始时间
  int64 end_time = 5;             // 结束时间
}

// 获取用户已购买的章节列表
message GetPurchasedChaptersReq {
  uint64 user_id = 1;             // 用户ID改为uint64
  string book_id = 2;
  int32 page = 3;
  int32 page_size = 4;
}

message GetPurchasedChaptersResp {
  int32 code = 1;
  string message = 2;
  repeated ChapterPurchaseInfo chapters = 3;
  int64 total = 4;
}
