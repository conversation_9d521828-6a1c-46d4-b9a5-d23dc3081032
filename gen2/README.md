# Account & Purchase 微服务

基于现有BeyondReading项目架构生成的Account和Purchase微服务，完全遵循现有代码风格和技术栈。

## 🏗️ 项目结构

```
gen2/
├── app/
│   ├── api/account/          # Account HTTP API服务
│   ├── base/account/         # Account gRPC微服务
│   ├── base/purchase/        # Purchase gRPC微服务
│   └── common/po/            # 数据模型定义
├── config/                   # 配置文件
├── database/                 # 数据库脚本
├── proto/                    # Protocol Buffers定义
└── README.md                 # 项目说明
```

## 🎯 功能特性

### Account微服务
- ✅ **账户管理**: account_id与user_id一对一绑定
- ✅ **充值功能**: 支持多种支付方式，充值转换为书币
- ✅ **余额管理**: 记录书币余额、总充值、总消费
- ✅ **用户状态**: 普通用户、VIP用户、包月用户
- ✅ **用户等级**: 根据消费书币自动计算等级（1000书币一级）
- ✅ **等级更新**: 购买时自动更新TotalConsumed和UserLevel字段
- ✅ **交易日志**: 完整的account_log记录
- ✅ **有限HTTP接口**: 仅提供getAccount、getAccountLogs、recharge

### Purchase微服务
- ✅ **章节购买**: 根据UserId、BookId、ChapterOrder、CoinAmount购买
- ✅ **包月购买**: 购买书籍包月服务
- ✅ **VIP购买**: 购买VIP会员
- ✅ **购买记录**: 所有购买记录保存在MySQL
- ✅ **状态检查**: 检查章节购买状态、包月状态
- ✅ **书币扣除**: 自动调用Account服务扣除书币
- ✅ **等级更新**: 购买时自动触发用户等级重新计算

## 📊 数据库设计

### Account相关表
- `account` - 账户表
- `account_log` - 账户日志表
- `recharge_order` - 充值订单表

### Purchase相关表
- `purchase_order` - 购买订单表
- `monthly_status` - 包月状态表（快速查询）

## 🚀 技术栈

- **语言**: Go 1.21
- **框架**: Gin (HTTP) + gRPC
- **数据库**: MySQL + Redis
- **服务发现**: etcd
- **数据库操作**: sqlx（与现有项目保持一致）
- **配置管理**: TOML
- **日志**: 统一日志框架
- **监控**: 集成现有监控体系

## 📋 API接口

### Account HTTP API (仅限3个接口)

```bash
# 获取账户信息
GET /account/info?userId=xxx

# 获取账户日志
GET /account/logs?userId=xxx&page=1&pageSize=20

# 充值
POST /account/recharge
{
  "userId": "xxx",
  "amount": "100.00",
  "paymentMethod": "alipay"
}
```

### Account gRPC接口

- `GetAccount` - 获取账户信息
- `CreateAccount` - 创建账户
- `Recharge` - 充值
- `GetAccountLogs` - 获取账户日志
- `UpdateUserStatus` - 更新用户状态
- `DeductCoins` - 扣除书币（内部接口）
- `CheckUserStatus` - 检查用户状态

### Purchase gRPC接口

- `PurchaseChapter` - 购买章节
- `PurchaseMonthly` - 购买包月
- `PurchaseVip` - 购买VIP
- `GetPurchaseOrders` - 获取购买订单
- `CheckChapterPurchased` - 检查章节购买状态
- `CheckMonthlyStatus` - 检查包月状态
- `GetPurchasedChapters` - 获取已购买章节

## 🔧 部署和运行

### 1. 数据库初始化

```bash
mysql -u root -p < database/account_schema.sql
```

### 2. 生成Protocol Buffers代码

```bash
cd proto
chmod +x run.sh
./run.sh
```

### 3. 启动服务

```bash
# 启动Account gRPC服务
cd app/base/account/cmd
go run main.go

# 启动Account HTTP API服务
cd app/api/account/cmd
go run main.go

# 启动Purchase gRPC服务
cd app/base/purchase/cmd
go run main.go
```

### 4. 配置文件

- `config/base-account.toml` - Account微服务配置
- `config/api-account.toml` - Account API服务配置
- `config/base-purchase.toml` - Purchase微服务配置

## 📝 使用示例

### 购买章节流程

```go
// 1. 检查章节是否已购买
checkResp, _ := purchaseClient.CheckChapterPurchased(ctx, &pb.CheckChapterPurchasedReq{
    UserId:       "user123",
    BookId:       "book456",
    ChapterOrder: 1,
})

// 2. 如果未购买，执行购买
if !checkResp.IsPurchased {
    purchaseResp, _ := purchaseClient.PurchaseChapter(ctx, &pb.PurchaseChapterReq{
        UserId:       "user123",
        BookId:       "book456",
        ChapterOrder: 1,
        CoinAmount:   "5.00",
    })
}
```

### 充值流程

```go
// 1. 调用充值接口
rechargeResp, _ := accountClient.Recharge(ctx, &pb.RechargeReq{
    UserId:        "user123",
    Amount:        "100.00",
    PaymentMethod: "alipay",
})

// 2. 处理支付回调（需要实现支付回调处理）
// 3. 支付成功后自动增加书币余额
```

## 🔒 安全特性

- **事务安全**: 使用数据库事务确保数据一致性
- **幂等性**: 防止重复购买和重复扣款
- **权限控制**: 内部gRPC接口不对外暴露
- **参数验证**: 完整的参数验证和错误处理

## 📈 性能优化

- **Redis缓存**: 缓存账户信息和购买状态
- **分库分表**: 支持按用户ID分片
- **连接池**: 数据库和Redis连接池优化
- **索引优化**: 针对查询场景优化数据库索引

## 🔄 与现有项目集成

### 1. 服务发现
使用现有的etcd服务发现机制，自动注册和发现服务。

### 2. 配置管理
使用现有的配置管理框架，支持TOML配置文件。

### 3. 日志系统
集成现有的日志框架，统一日志格式和输出。

### 4. 监控告警
集成现有的监控体系，支持健康检查和性能监控。

## 📋 注意事项

1. **PurchaseChapterReq参数**: 严格按照要求只包含UserId、BookId、ChapterOrder、CoinAmount
2. **HTTP接口限制**: Account API只提供3个HTTP接口，其他功能通过gRPC调用
3. **数据库操作**: 使用sqlx实现，与现有purchase/dao保持一致
4. **错误处理**: 使用现有的ecode错误处理机制
5. **事务处理**: 使用mysql.Transact进行事务管理

## 🎯 符合要求检查

✅ **微服务架构**: 使用gRPC进行服务间通信
✅ **账户绑定**: account_id与user_id一对一绑定
✅ **充值功能**: 支持充值转换为书币
✅ **用户状态**: 支持普通用户、VIP用户、包月用户、用户等级
✅ **有限HTTP接口**: 仅提供getAccount、getAccountLogs、recharge
✅ **购买功能**: 支持购买章节、包月、VIP
✅ **书币扣除**: 购买时自动扣除书币
✅ **交易记录**: 所有记录保存在account_log表
✅ **参数限制**: PurchaseChapterReq只包含指定参数
✅ **数据库操作**: 使用sqlx实现，参照现有purchase/dao

这个实现完全基于现有项目架构，可以无缝集成到BeyondReading项目中！
