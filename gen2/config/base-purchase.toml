# Purchase微服务配置文件

[port]
http = ":8082"
grpc = ":8084"

[etcd]
addrs = ["127.0.0.1:2379"]

[mysql]
dsn = "root:password@tcp(127.0.0.1:3306)/beyondreading?charset=utf8mb4&parseTime=True&loc=Local"
max_open_conns = 100
max_idle_conns = 10
conn_max_lifetime = "1h"

[cache]
addr = "127.0.0.1:6379"
password = ""
db = 0
pool_size = 10
min_idle_conns = 5

[log]
level = "info"

[purchase]
chapter_price = "5.00"    # 默认章节价格
monthly_price = "30.00"   # 包月价格
vip_price = "100.00"      # VIP价格
monthly_days = 30         # 包月天数
vip_days = 30             # VIP天数
cache_expire = 3600       # 缓存过期时间（秒）
enable_cache = true       # 是否启用缓存
