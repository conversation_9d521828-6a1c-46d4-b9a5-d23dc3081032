# Account微服务配置文件

[port]
http = ":8081"
grpc = ":8083"

[etcd]
addrs = ["127.0.0.1:2379"]

[mysql]
dsn = "root:password@tcp(127.0.0.1:3306)/beyondreading?charset=utf8mb4&parseTime=True&loc=Local"
max_open_conns = 100
max_idle_conns = 10
conn_max_lifetime = "1h"

[cache]
addr = "127.0.0.1:6379"
password = ""
db = 0
pool_size = 10
min_idle_conns = 5

[log]
level = "info"

[account]
default_exchange_rate = "1.0"    # 默认兑换比例 1元=1书币
min_recharge_amount = "1.00"     # 最小充值金额
max_recharge_amount = "10000.00" # 最大充值金额
cache_expire = 3600              # 缓存过期时间（秒）
enable_cache = true              # 是否启用缓存

[payment.alipay]
app_id = "your_alipay_app_id"
private_key = "your_alipay_private_key"
public_key = "your_alipay_public_key"

[payment.wechat]
app_id = "your_wechat_app_id"
mch_id = "your_wechat_mch_id"
key = "your_wechat_key"
notify_url = "https://your-domain.com/payment/wechat/notify"

[payment.apple]
bundle_id = "com.yourcompany.app"
is_production = false
