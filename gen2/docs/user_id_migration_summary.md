# User ID 类型迁移总结

## 概述

本文档总结了将account表中user_id字段从varchar(64)改为bigint unsigned的完整迁移过程。

## 迁移范围

### 1. 数据库表结构更新

#### 已更新的表
- `account` - 账户表
- `account_log` - 账户日志表  
- `recharge_order` - 充值订单表
- `purchase_order` - 购买订单表

#### 已更新的Schema文件
- `gen/database/account_schema.sql`
- `gen2/database/account_schema.sql`
- `gen2/scripts/init_test_db.sql`

#### 数据库迁移脚本
- `gen2/database/migrate_user_id_to_bigint.sql` - 完整的数据库迁移脚本

### 2. Proto文件更新

#### Account服务Proto (`gen2/proto/account/account.proto`)
```protobuf
// 更新前
message GetAccountReq {
  string user_id = 1;
}

// 更新后  
message GetAccountReq {
  uint64 user_id = 1;
}
```

#### Purchase服务Proto (`gen2/proto/purchase/purchase.proto`)
```protobuf
// 更新前
message PurchaseChapterReq {
  string user_id = 1;
}

// 更新后
message PurchaseChapterReq {
  uint64 user_id = 1;
}
```

#### 已更新的消息类型
- `AccountInfo`
- `AccountLog`
- `RechargeOrder`
- `GetAccountReq/CreateAccountReq/RechargeReq/DeductCoinsReq`
- `GetAccountLogsReq/UpdateUserStatusReq/CheckUserStatusReq`
- `PurchaseOrder`
- `PurchaseChapterReq/PurchaseMonthlyReq/PurchaseVipReq`
- `GetPurchaseOrdersReq/CheckChapterPurchasedReq/CheckMonthlyStatusReq`
- `GetPurchasedChaptersReq`

### 3. PO结构体更新

#### Account相关结构体 (`gen2/app/common/po/account.go`)
```go
// 更新前
type Account struct {
    UserId string `db:"user_id" json:"user_id"`
}

// 更新后
type Account struct {
    UserId uint64 `db:"user_id" json:"user_id"`
}
```

#### 已更新的结构体
- `Account`
- `AccountLog`
- `RechargeOrder`
- `PurchaseOrder`

### 4. DAO层更新

#### Account DAO (`gen2/app/base/account/dao/`)
```go
// 更新前
func (d *Dao) GetAccountByUserId(ctx context.Context, userId string) (*po.Account, error)

// 更新后
func (d *Dao) GetAccountByUserId(ctx context.Context, userId uint64) (*po.Account, error)
```

#### Purchase DAO (`gen2/app/base/purchase/dao/`)
```go
// 更新前
func (d *Dao) PurchaseChapter(ctx context.Context, userId string, ...) (*po.PurchaseOrder, error)

// 更新后
func (d *Dao) PurchaseChapter(ctx context.Context, userId uint64, ...) (*po.PurchaseOrder, error)
```

#### 已更新的方法
**Account DAO:**
- `GetDB(userId uint64)`
- `GetTableName(userId uint64, tableName string)`
- `GetAccountByUserId(userId uint64)`
- `CreateAccount(userId uint64)`
- `UpdateAccountBalance(userId uint64, ...)`
- `GetAccountLogs(userId uint64, ...)`
- `UpdateUserStatus(userId uint64, ...)`
- `CheckUserStatus(userId uint64, ...)`

**Purchase DAO:**
- `GetDB(userId uint64)`
- `GetTableName(userId uint64, tableName string)`
- `PurchaseChapter(userId uint64, ...)`
- `PurchaseMonthly(userId uint64, ...)`
- `PurchaseVip(userId uint64, ...)`
- `CheckChapterPurchased(userId uint64, ...)`
- `CheckMonthlyStatus(userId uint64, ...)`
- `GetPurchaseOrders(userId uint64, ...)`
- `GetPurchasedChapters(userId uint64, ...)`

### 5. 测试数据更新

#### 测试常量更新
```go
// 更新前
const testUserId = "test_user_123"

// 更新后
const testUserId = uint64(123)
```

#### 测试数据更新
```go
// 更新前
UserId: "test_user_123"

// 更新后
UserId: 123
```

## 迁移步骤

### 1. 数据库迁移
```sql
-- 执行迁移脚本
mysql -u root -p < gen2/database/migrate_user_id_to_bigint.sql
```

### 2. 代码更新
1. 更新Proto文件并重新生成代码
2. 更新PO结构体
3. 更新DAO层方法
4. 更新服务层代码
5. 更新测试代码

### 3. 验证迁移
1. 运行数据库迁移脚本
2. 验证表结构正确
3. 运行测试确保代码正常工作
4. 验证数据完整性

## 注意事项

### 1. 数据兼容性
- 原有的字符串类型user_id必须是纯数字才能迁移
- 迁移脚本会自动过滤非数字的user_id
- 建议在迁移前清理数据

### 2. 分片键处理
```go
// DAO层需要将uint64转换为string用于分片
func (d *Dao) GetDB(userId uint64) (*sqlx.DB, error) {
    userIdStr := fmt.Sprintf("%d", userId)
    return d.msshard.DB(userIdStr)
}
```

### 3. 类型转换
- Proto中使用uint64
- Go代码中使用uint64
- 数据库中使用bigint unsigned
- 分片键仍使用string

### 4. 测试更新
- 所有测试文件中的user_id都需要更新
- 测试数据需要使用数字而不是字符串
- 日志输出格式需要从%s改为%d

## 回滚方案

如果需要回滚，可以：

1. 恢复备份的数据库表
```sql
RENAME TABLE account TO account_new;
RENAME TABLE account_backup TO account;
-- 对其他表执行相同操作
```

2. 恢复代码到迁移前的版本

3. 重新生成Proto代码

## 验证清单

- [ ] 数据库表结构已更新
- [ ] 数据迁移成功，无数据丢失
- [ ] Proto文件已更新
- [ ] PO结构体已更新
- [ ] DAO层方法已更新
- [ ] 服务层代码已更新
- [ ] 测试代码已更新
- [ ] 所有测试通过
- [ ] 功能验证正常

## 性能影响

### 优势
- bigint unsigned比varchar(64)更节省存储空间
- 数字比较比字符串比较更快
- 索引效率更高

### 注意
- 需要确保应用层正确处理uint64类型
- 分片逻辑需要适配新的类型转换

## 后续工作

1. 更新API文档
2. 更新客户端SDK
3. 通知相关团队类型变更
4. 监控系统运行状况
5. 清理备份表（确认无问题后）

## 联系信息

如有问题，请联系：
- 数据库团队：负责数据迁移
- 后端团队：负责代码更新
- 测试团队：负责功能验证
