# Purchase API 类型修正总结

## 概述

根据对 `common/po/account.go` 和 `proto/purchase/purchase.proto` 的重新学习，发现了字段类型的重要差异，特别是金额相关字段应该使用 `float64` 而不是 `string`。本文档总结了所有的类型修正。

## 关键发现

### 1. Account PO 中的字段类型
```go
// gen2/app/common/po/account.go
type Account struct {
    AccountId      uint64     `db:"account_id" json:"account_id"`
    UserId         uint64     `db:"user_id" json:"user_id"`
    CoinBalance    float64    `db:"coin_balance" json:"coin_balance"`    // ❗ 是 float64，不是 string
    TotalRecharged float64    `db:"total_recharged" json:"total_recharged"`
    TotalConsumed  float64    `db:"total_consumed" json:"total_consumed"`
    // ...
}
```

### 2. Purchase Proto 中的字段类型
```protobuf
// gen2/proto/purchase/purchase.proto
message PurchaseChapterReq {
  uint64 user_id = 1;
  string book_id = 2;
  uint32 chapter_order = 3;
  double coin_amount = 4;        // ❗ 是 double，对应 Go 的 float64
}

message PurchaseOrder {
  string order_id = 1;
  uint64 account_id = 2;
  uint64 user_id = 3;
  string order_type = 4;
  // ...
  double coin_amount = 10;       // ❗ 是 double，对应 Go 的 float64
  int32 duration_days = 11;
  int64 start_time = 12;
  int64 end_time = 13;
  int32 status = 14;             // ❗ 是 int32，不是 int
  // ...
}
```

## 类型修正详情

### 1. VO 模型修正

#### **修正前 (错误)**
```go
type PurchaseChapterReq struct {
    UserId       uint64 `json:"userId" binding:"required"`
    BookId       string `json:"bookId" binding:"required"`
    ChapterOrder uint32 `json:"chapterOrder" binding:"required"`
    CoinAmount   string `json:"coinAmount" binding:"required"` // ❌ 错误：应该是 float64
}

type PurchaseChapterResp struct {
    // ...
    CoinAmount   string `json:"coinAmount"` // ❌ 错误：应该是 float64
    Status       int    `json:"status"`     // ❌ 错误：应该是 int32
    // ...
}
```

#### **修正后 (正确)**
```go
type PurchaseChapterReq struct {
    UserId       uint64  `json:"userId" binding:"required"`
    BookId       string  `json:"bookId" binding:"required"`
    ChapterOrder uint32  `json:"chapterOrder" binding:"required"`
    CoinAmount   float64 `json:"coinAmount" binding:"required"` // ✅ 正确：float64
}

type PurchaseChapterResp struct {
    // ...
    CoinAmount   float64 `json:"coinAmount"` // ✅ 正确：float64
    Status       int32   `json:"status"`     // ✅ 正确：int32
    // ...
}
```

### 2. 服务层修正

#### **修正前 (错误)**
```go
grpcReq := &pb.PurchaseChapterReq{
    UserId:       req.UserId,
    BookId:       req.BookId,
    ChapterOrder: req.ChapterOrder,
    CoinAmount:   req.CoinAmount, // 如果 req.CoinAmount 是 string，这里会类型错误
}

resp := &vo.PurchaseChapterResp{
    // ...
    CoinAmount:   grpcResp.Order.CoinAmount, // 如果目标是 string，这里会类型错误
    Status:       int(grpcResp.Order.Status), // 不必要的类型转换
    // ...
}
```

#### **修正后 (正确)**
```go
grpcReq := &pb.PurchaseChapterReq{
    UserId:       req.UserId,
    BookId:       req.BookId,
    ChapterOrder: req.ChapterOrder,
    CoinAmount:   req.CoinAmount, // ✅ 正确：float64 到 double 的直接传递
}

resp := &vo.PurchaseChapterResp{
    // ...
    CoinAmount:   grpcResp.Order.CoinAmount, // ✅ 正确：double 到 float64 的直接赋值
    Status:       grpcResp.Order.Status,     // ✅ 正确：int32 到 int32 的直接赋值
    // ...
}
```

### 3. 测试数据修正

#### **修正前 (错误)**
```go
req := vo.PurchaseChapterReq{
    UserId:       123,
    BookId:       "book_456",
    ChapterOrder: 1,
    CoinAmount:   "5.00", // ❌ 错误：字符串类型
}
```

#### **修正后 (正确)**
```go
req := vo.PurchaseChapterReq{
    UserId:       123,
    BookId:       "book_456",
    ChapterOrder: 1,
    CoinAmount:   5.00, // ✅ 正确：float64 数字类型
}
```

### 4. API 文档修正

#### **修正前 (错误)**
```json
{
  "userId": 123,
  "bookId": "book_456",
  "chapterOrder": 1,
  "coinAmount": "5.00"
}
```

#### **修正后 (正确)**
```json
{
  "userId": 123,
  "bookId": "book_456",
  "chapterOrder": 1,
  "coinAmount": 5.00
}
```

## 影响的文件列表

### 1. VO 模型文件
- ✅ `gen2/app/api/purchase/model/vo/purchase_vo.go`
  - `PurchaseChapterReq.CoinAmount`: `string` → `float64`
  - `PurchaseChapterResp.CoinAmount`: `string` → `float64`
  - `PurchaseChapterResp.Status`: `int` → `int32`
  - `PurchaseMonthlyReq.CoinAmount`: `string` → `float64`
  - `PurchaseMonthlyResp.CoinAmount`: `string` → `float64`
  - `PurchaseMonthlyResp.Status`: `int` → `int32`
  - `PurchaseVipReq.CoinAmount`: `string` → `float64`
  - `PurchaseVipResp.CoinAmount`: `string` → `float64`
  - `PurchaseVipResp.Status`: `int` → `int32`
  - `PurchaseOrderResp.CoinAmount`: `string` → `float64`
  - `PurchaseOrderResp.Status`: `int` → `int32`
  - `ChapterPurchaseInfo.CoinAmount`: `string` → `float64`

### 2. 服务层文件
- ✅ `gen2/app/api/purchase/svc/purchase.go`
  - 移除了不必要的类型转换
  - 直接使用 `float64` 和 `int32` 类型

### 3. 测试文件
- ✅ `gen2/app/api/purchase/http/http_test.go`
  - 所有测试数据中的 `coinAmount` 从字符串改为数字

### 4. 文档文件
- ✅ `gen2/app/api/purchase/docs/api.md`
  - 所有示例中的 `coinAmount` 从字符串改为数字
  - 更新了注意事项说明

## Proto 到 Go 类型映射

| Proto 类型 | Go 类型 | 说明 |
|-----------|---------|------|
| `double` | `float64` | 双精度浮点数 |
| `float` | `float32` | 单精度浮点数 |
| `int32` | `int32` | 32位有符号整数 |
| `int64` | `int64` | 64位有符号整数 |
| `uint32` | `uint32` | 32位无符号整数 |
| `uint64` | `uint64` | 64位无符号整数 |
| `string` | `string` | 字符串 |
| `bool` | `bool` | 布尔值 |

## JSON 序列化行为

### float64 在 JSON 中的表示
```go
type Example struct {
    Amount float64 `json:"amount"`
}

// JSON 输出
{
  "amount": 5.00    // 数字类型，不是字符串
}
```

### 客户端请求格式
```javascript
// 正确的 JavaScript 请求
fetch('/api/v1/purchase/chapter', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    userId: 123,
    bookId: "book_456",
    chapterOrder: 1,
    coinAmount: 5.00  // 数字类型，不是字符串
  })
});
```

## 验证和测试

### 1. 类型验证
```bash
# 测试正确的数字类型
curl -X POST http://localhost:8080/api/v1/purchase/chapter \
  -H "Content-Type: application/json" \
  -d '{
    "userId": 123,
    "bookId": "book_456",
    "chapterOrder": 1,
    "coinAmount": 5.00
  }'

# 测试错误的字符串类型（应该失败）
curl -X POST http://localhost:8080/api/v1/purchase/chapter \
  -H "Content-Type: application/json" \
  -d '{
    "userId": 123,
    "bookId": "book_456",
    "chapterOrder": 1,
    "coinAmount": "5.00"
  }'
```

### 2. 单元测试验证
```go
func TestCoinAmountType(t *testing.T) {
    req := vo.PurchaseChapterReq{
        UserId:       123,
        BookId:       "book_456",
        ChapterOrder: 1,
        CoinAmount:   5.00, // 确保是 float64 类型
    }
    
    // 验证类型
    assert.IsType(t, float64(0), req.CoinAmount)
}
```

## 最佳实践

### 1. 金额处理
- ✅ 使用 `float64` 存储金额
- ✅ 在 JSON 中使用数字格式
- ✅ 避免字符串和数字之间的转换

### 2. 状态码处理
- ✅ 使用 `int32` 匹配 Proto 定义
- ✅ 避免不必要的类型转换

### 3. API 设计
- ✅ 保持类型一致性
- ✅ 遵循 Proto 定义
- ✅ 提供清晰的文档说明

## 总结

通过这次类型修正，Purchase API 现在完全符合以下标准：

1. **类型一致性**: 所有金额字段使用 `float64`
2. **Proto 兼容性**: 完全匹配 gRPC Proto 定义
3. **JSON 规范性**: 金额在 JSON 中表示为数字
4. **测试完整性**: 所有测试使用正确的类型
5. **文档准确性**: API 文档反映真实的数据类型

这确保了 API 的类型安全性和与后端 gRPC 服务的完美兼容性。
