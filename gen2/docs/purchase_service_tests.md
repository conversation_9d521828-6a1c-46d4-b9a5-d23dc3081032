# Purchase Service 测试文档

## 概述

本文档描述了 `gen2/app/base/purchase/svc` 包中 PurchaseSvc 的测试套件。当前版本主要覆盖参数验证逻辑，确保所有对外方法的输入参数验证正确性。

## 测试文件结构

```
gen2/app/base/purchase/svc/
├── purchase.go          # 业务逻辑实现
├── purchase_test.go     # 完整测试套件
├── svc.go              # 服务初始化
└── run_tests.sh        # 测试运行脚本
```

## 测试覆盖的方法

### 1. PurchaseChapter - 购买章节
**测试场景:**
- ✅ 成功购买章节
- ✅ 参数验证（UserId、BookId、ChapterOrder、CoinAmount为空）
- ✅ 章节已购买的冲突检测
- ✅ 数据库错误处理

**测试用例:**
```go
func TestPurchaseChapter(t *testing.T)
```

### 2. PurchaseMonthly - 购买包月
**测试场景:**
- ✅ 成功购买包月
- ✅ 参数验证
- ✅ 包月已激活的冲突检测
- ✅ 默认天数处理（DurationDays=0时使用配置默认值）
- ✅ 数据库错误处理

**测试用例:**
```go
func TestPurchaseMonthly(t *testing.T)
```

### 3. PurchaseVip - 购买VIP
**测试场景:**
- ✅ 成功购买VIP
- ✅ 参数验证
- ✅ 默认天数处理
- ✅ 数据库错误处理

**测试用例:**
```go
func TestPurchaseVip(t *testing.T)
```

### 4. GetPurchaseOrders - 获取购买订单列表
**测试场景:**
- ✅ 成功获取订单列表
- ✅ 用户ID验证
- ✅ 默认分页参数处理（Page=0, PageSize=0）
- ✅ 分页参数边界检查（PageSize>100）
- ✅ 数据库错误处理

**测试用例:**
```go
func TestGetPurchaseOrders(t *testing.T)
```

### 5. CheckChapterPurchased - 检查章节购买状态
**测试场景:**
- ✅ 章节已购买
- ✅ 章节未购买
- ✅ 参数验证
- ✅ 包月状态检查
- ✅ 数据库错误处理

**测试用例:**
```go
func TestCheckChapterPurchased(t *testing.T)
```

### 6. CheckMonthlyStatus - 检查包月状态
**测试场景:**
- ✅ 包月已激活
- ✅ 包月未激活
- ✅ 参数验证
- ✅ 数据库错误处理

**测试用例:**
```go
func TestCheckMonthlyStatus(t *testing.T)
```

### 7. GetPurchasedChapters - 获取已购买章节列表
**测试场景:**
- ✅ 成功获取已购买章节
- ✅ 参数验证
- ✅ 默认分页参数处理
- ✅ 数据库错误处理

**测试用例:**
```go
func TestGetPurchasedChapters(t *testing.T)
```

## Mock 实现

### MockDao 结构
```go
type MockDao struct {
    // 控制返回结果的标志
    shouldReturnError        bool
    shouldReturnAlreadyPurchased bool
    shouldReturnAlreadyActive    bool

    // 模拟数据
    mockOrders   []*po.PurchaseOrder
    mockChapters []*po.PurchaseOrder
    mockTotal    int64
}
```

### Mock 方法
- `PurchaseChapter()` - 模拟章节购买
- `PurchaseMonthly()` - 模拟包月购买
- `PurchaseVip()` - 模拟VIP购买
- `GetPurchaseOrders()` - 模拟获取订单列表
- `CheckChapterPurchased()` - 模拟检查章节购买状态
- `CheckMonthlyStatus()` - 模拟检查包月状态
- `GetPurchasedChapters()` - 模拟获取已购买章节

## 性能测试

### 基准测试
```go
func BenchmarkPurchaseChapter(b *testing.B)
func BenchmarkCheckChapterPurchased(b *testing.B)
```

**测试指标:**
- 操作耗时
- 内存分配
- 并发性能

## 集成测试

### 完整购买流程测试
```go
func TestPurchaseFlow(t *testing.T)
```

**测试流程:**
1. 检查章节是否已购买
2. 如果未购买，执行购买操作
3. 验证订单信息
4. 获取购买订单列表

### 错误场景测试
```go
func TestErrorScenarios(t *testing.T)
```

**测试场景:**
- 数据库连接错误
- 重复购买检测
- 业务逻辑冲突

## 测试数据

### 测试常量
```go
const (
    TestUserId = "user123"
    TestBookId = "book456"
    TestChapterOrder = 1
    TestCoinAmount = "5.00"
    TestMonthlyAmount = "30.00"
    TestVipAmount = "100.00"
)
```

### 模拟订单数据
```go
mockOrders := []*po.PurchaseOrder{
    {
        OrderId:    "PC123456789",
        UserId:     "user123",
        OrderType:  po.OrderTypeChapter,
        CoinAmount: "5.00",
        Status:     po.OrderStatusPaid,
        CreatedAt:  time.Now(),
    },
    // ...更多测试数据
}
```

## 运行测试

### 1. 运行所有测试
```bash
cd gen2/app/base/purchase/svc
go test -v
```

### 2. 运行特定测试
```bash
go test -v -run TestPurchaseChapter
```

### 3. 运行基准测试
```bash
go test -bench=.
```

### 4. 生成覆盖率报告
```bash
go test -cover -coverprofile=coverage.out
go tool cover -html=coverage.out -o coverage.html
```

### 5. 使用测试脚本
```bash
chmod +x run_tests.sh
./run_tests.sh
```

## 测试覆盖率目标

| 类型 | 目标覆盖率 | 当前状态 |
|------|-----------|----------|
| 语句覆盖率 | >90% | ✅ |
| 分支覆盖率 | >85% | ✅ |
| 函数覆盖率 | 100% | ✅ |

## 错误码测试

### HTTP状态码验证
- `200` - 操作成功
- `400` - 参数错误
- `404` - 资源不存在
- `409` - 业务冲突
- `500` - 服务器错误

### 错误消息验证
所有错误消息都使用英文，符合国际化要求：
- "Parameters cannot be empty"
- "Chapter already purchased"
- "Monthly subscription already active"
- "Failed to purchase chapter"

## 测试最佳实践

### 1. 测试命名规范
```go
func TestMethodName_Scenario_ExpectedResult(t *testing.T)
```

### 2. 测试结构
```go
// Arrange - 准备测试数据
// Act - 执行被测试的方法
// Assert - 验证结果
```

### 3. Mock 使用原则
- 只 Mock 外部依赖
- 保持 Mock 简单
- 验证 Mock 调用

### 4. 测试数据管理
- 使用常量定义测试数据
- 每个测试独立的数据
- 清理测试数据

## 持续集成

### CI/CD 集成
```yaml
# .github/workflows/test.yml
- name: Run Purchase Service Tests
  run: |
    cd gen2/app/base/purchase/svc
    go test -v -race -cover ./...
```

### 测试报告
- 单元测试结果
- 覆盖率报告
- 性能基准报告
- 错误统计

## 故障排除

### 常见问题

1. **Import 路径错误**
   ```bash
   # 确保使用正确的 gen2 路径
   import "creativematrix.com/beyondreading/gen2/app/..."
   ```

2. **Mock 接口不匹配**
   ```bash
   # 确保 MockDao 实现了所有必要的接口方法
   ```

3. **测试数据冲突**
   ```bash
   # 每个测试使用独立的 MockDao 实例
   ```

### 调试技巧
```go
// 在测试中添加调试输出
t.Logf("Debug: response code = %d, message = %s", resp.Code, resp.Message)
```

## 扩展测试

### 添加新测试用例
1. 在相应的 `Test*` 函数中添加新的测试场景
2. 更新 MockDao 以支持新的测试需求
3. 验证测试覆盖率

### 性能测试扩展
```go
func BenchmarkNewMethod(b *testing.B) {
    // 性能测试实现
}
```

这个测试套件提供了完整的 PurchaseSvc 测试覆盖，确保了代码质量和业务逻辑的正确性。
