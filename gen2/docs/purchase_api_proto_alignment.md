# Purchase API Proto 对齐总结

## 概述

根据 `proto/account/account.proto` 的字段类型定义，重新生成了 Purchase API 的相关参数处理，确保与 gRPC Proto 定义完全一致。

## Proto 文件分析

### 1. Account Proto 字段类型
```protobuf
// proto/account/account.proto
message DeductCoinsReq {
  uint64 user_id = 1;
  string amount = 2;              // ❗ 注意：account.proto 中是 string
  string order_id = 3;
  string book_id = 4;
  string chapter_id = 5;
  string transaction_type = 6;
  string description = 7;
}

message AccountInfo {
  uint64 account_id = 1;
  uint64 user_id = 2;
  string coin_balance = 3;        // ❗ 注意：account.proto 中是 string
  string total_recharged = 4;     // ❗ 注意：account.proto 中是 string
  string total_consumed = 5;      // ❗ 注意：account.proto 中是 string
  // ...
}
```

### 2. Purchase Proto 字段类型
```protobuf
// proto/purchase/purchase.proto
message PurchaseChapterReq {
  uint64 user_id = 1;
  string book_id = 2;
  uint32 chapter_order = 3;
  double coin_amount = 4;         // ❗ 注意：purchase.proto 中是 double
}

message PurchaseOrder {
  string order_id = 1;
  uint64 account_id = 2;
  uint64 user_id = 3;
  string order_type = 4;
  // ...
  double coin_amount = 10;        // ❗ 注意：purchase.proto 中是 double
  int32 duration_days = 11;
  int64 start_time = 12;
  int64 end_time = 13;
  int32 status = 14;
  // ...
}
```

## 关键发现

### 类型差异
1. **Account服务**: 金额字段使用 `string` 类型
2. **Purchase服务**: 金额字段使用 `double` 类型
3. **需要类型转换**: 在调用不同服务时需要进行类型转换

## 重新生成的组件

### 1. 类型转换工具 (`svc/converter.go`)

#### **新增转换函数**
```go
// convertFloat64ToString 将float64转换为字符串，用于调用account服务
func convertFloat64ToString(amount float64) string {
    return fmt.Sprintf("%.2f", amount)
}

// convertStringToFloat64 将字符串转换为float64，用于处理account服务的响应
func convertStringToFloat64(amountStr string) (float64, error) {
    return strconv.ParseFloat(amountStr, 64)
}
```

#### **新增验证函数**
```go
// validateCoinAmount 验证书币金额是否有效
func validateCoinAmount(amount float64) error {
    if amount <= 0 {
        return fmt.Errorf("coin amount must be greater than 0, got: %.2f", amount)
    }
    if amount > 999999.99 {
        return fmt.Errorf("coin amount too large, max: 999999.99, got: %.2f", amount)
    }
    return nil
}

// validateUserId 验证用户ID是否有效
func validateUserId(userId uint64) error {
    if userId == 0 {
        return fmt.Errorf("user ID cannot be 0")
    }
    return nil
}

// 其他验证函数...
```

### 2. DAO层增强 (`dao/dao.go`)

#### **新增Account客户端**
```go
type Dao struct {
    conf           *conf.Config
    PurchaseClient pb.PurchaseServiceClient
    AccountClient  accountpb.AccountServiceClient  // ✅ 新增
    purchaseConn   *grpc.ClientConn
    accountConn    *grpc.ClientConn                // ✅ 新增
}
```

#### **新增Account客户端初始化**
```go
func (d *Dao) initAccountClient() {
    // 连接Account gRPC服务
    conn, err := grpc.DialContext(ctx, d.conf.GRPC.Account.Addr, ...)
    d.accountConn = conn
    d.AccountClient = accountpb.NewAccountServiceClient(conn)
}
```

### 3. 配置增强 (`conf/conf.go`)

#### **新增Account gRPC配置**
```go
GRPC struct {
    Purchase struct {
        Addr    string `toml:"addr"`
        Timeout string `toml:"timeout"`
    } `toml:"purchase"`
    Account struct {                    // ✅ 新增
        Addr    string `toml:"addr"`
        Timeout string `toml:"timeout"`
    } `toml:"account"`
} `toml:"grpc"`
```

#### **配置文件更新** (`config/api-purchase.toml`)
```toml
[grpc.purchase]
addr = "127.0.0.1:9583"
timeout = "30s"

[grpc.account]                          # ✅ 新增
addr = "127.0.0.1:8183"
timeout = "30s"
```

### 4. 服务层增强 (`svc/purchase.go`)

#### **新增参数验证**
```go
func (s *PurchaseSvc) PurchaseChapter(ctx context.Context, req *vo.PurchaseChapterReq) (*vo.PurchaseChapterResp, error) {
    // ✅ 新增参数验证
    if err := validateUserId(req.UserId); err != nil {
        return nil, fmt.Errorf("invalid user ID: %w", err)
    }
    if err := validateBookId(req.BookId); err != nil {
        return nil, fmt.Errorf("invalid book ID: %w", err)
    }
    if err := validateChapterOrder(req.ChapterOrder); err != nil {
        return nil, fmt.Errorf("invalid chapter order: %w", err)
    }
    if err := validateCoinAmount(req.CoinAmount); err != nil {
        return nil, fmt.Errorf("invalid coin amount: %w", err)
    }

    // 调用Purchase gRPC服务
    grpcReq := &pb.PurchaseChapterReq{
        UserId:       req.UserId,
        BookId:       req.BookId,
        ChapterOrder: req.ChapterOrder,
        CoinAmount:   req.CoinAmount, // ✅ purchase.proto中是double，直接传递
    }
    // ...
}
```

#### **新增分页参数处理**
```go
func (s *PurchaseSvc) GetPurchaseOrders(ctx context.Context, req *vo.GetPurchaseOrdersReq) (*vo.GetPurchaseOrdersResp, error) {
    // ✅ 新增分页参数验证和默认值设置
    page := req.Page
    if page <= 0 {
        page = 1
    }
    pageSize := req.PageSize
    if pageSize <= 0 {
        pageSize = 20
    }
    if pageSize > 100 {
        pageSize = 100 // 限制最大页面大小
    }
    // ...
}
```

### 5. HTTP处理器优化 (`http/http.go`)

#### **移除重复的分页处理**
```go
// 修改前：HTTP层处理分页参数
func (h *PurchaseHandler) GetPurchaseOrders(c *gin.Context) {
    // 设置默认分页参数
    if req.Page <= 0 {
        req.Page = 1
    }
    if req.PageSize <= 0 {
        req.PageSize = 20
    }
    // ...
}

// 修改后：服务层统一处理分页参数
func (h *PurchaseHandler) GetPurchaseOrders(c *gin.Context) {
    // ✅ 直接调用服务层，分页参数由服务层处理
    resp, err := h.svc.GetPurchaseOrders(c.Request.Context(), &req)
    // ...
}
```

## 类型处理策略

### 1. Purchase API 内部使用 float64
```go
// VO模型中使用float64
type PurchaseChapterReq struct {
    CoinAmount float64 `json:"coinAmount"` // ✅ 与purchase.proto的double对应
}
```

### 2. 调用Account服务时转换为string
```go
// 如果需要调用Account服务扣除书币
accountReq := &accountpb.DeductCoinsReq{
    UserId: req.UserId,
    Amount: convertFloat64ToString(req.CoinAmount), // ✅ 转换为string
    // ...
}
```

### 3. 调用Purchase服务时直接传递float64
```go
// 调用Purchase gRPC服务
purchaseReq := &pb.PurchaseChapterReq{
    CoinAmount: req.CoinAmount, // ✅ 直接传递float64
    // ...
}
```

## 验证规则

### 1. 用户ID验证
- 不能为0
- 必须是有效的uint64

### 2. 金额验证
- 必须大于0
- 不能超过999999.99
- 保留两位小数

### 3. 分页验证
- page默认为1，最小为1
- pageSize默认为20，最大为100
- 防止过大的分页请求

### 4. 字符串字段验证
- bookId不能为空，最大长度64
- chapterOrder必须大于0，最大99999

## 错误处理

### 1. 参数验证错误
```go
if err := validateCoinAmount(req.CoinAmount); err != nil {
    return nil, fmt.Errorf("invalid coin amount: %w", err)
}
```

### 2. gRPC调用错误
```go
grpcResp, err := s.dao.PurchaseClient.PurchaseChapter(ctx, grpcReq)
if err != nil {
    logger.LogErrorf("Failed to purchase chapter: %v", err)
    return nil, fmt.Errorf("purchase chapter failed: %w", err)
}
```

### 3. 业务逻辑错误
```go
if grpcResp.Code != 200 {
    return nil, fmt.Errorf("purchase chapter failed: %s", grpcResp.Message)
}
```

## 性能优化

### 1. 连接复用
- Purchase和Account gRPC连接分别管理
- 连接池复用，避免频繁建立连接

### 2. 参数验证前置
- 在服务层进行参数验证
- 避免无效请求到达gRPC服务

### 3. 分页限制
- 限制最大页面大小为100
- 防止大量数据查询影响性能

## 总结

通过重新对齐proto定义，Purchase API现在具备：

1. **类型安全**: 完全匹配proto定义的字段类型
2. **参数验证**: 全面的输入参数验证
3. **错误处理**: 统一的错误处理机制
4. **性能优化**: 合理的分页和连接管理
5. **扩展性**: 支持调用多个gRPC服务
6. **维护性**: 清晰的代码结构和文档

这确保了API的稳定性、安全性和与后端gRPC服务的完美兼容性。
