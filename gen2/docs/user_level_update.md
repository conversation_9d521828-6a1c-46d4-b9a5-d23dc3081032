# 用户等级更新机制

## 概述

在用户发生购买行为时，系统会根据消费的书币数量自动计算并更新用户等级。用户等级基于累计消费的书币数量计算，每消费1000书币升一级。

## 等级计算规则

### 计算公式
```
用户等级 = (总消费书币数量 / 1000) + 1
```

### 常量定义
```go
const CoinsPerLevel = 1000 // 每1000书币升一级
```

### 等级示例
| 累计消费书币 | 用户等级 | 说明 |
|-------------|---------|------|
| 0 - 999     | 1级     | 新用户默认等级 |
| 1000 - 1999 | 2级     | 消费满1000书币 |
| 2000 - 2999 | 3级     | 消费满2000书币 |
| 5000 - 5999 | 6级     | 消费满5000书币 |
| 10000+      | 11级+   | 高级用户 |

## 实现流程

### 1. 购买触发流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant Purchase as Purchase服务
    participant Account as Account服务
    participant DB as 数据库

    User->>Purchase: 购买章节/包月/VIP
    Purchase->>Account: 调用DeductCoins扣除书币
    Account->>DB: 开始事务
    Account->>DB: 锁定账户记录
    Account->>DB: 检查余额是否足够
    Account->>Account: 计算新余额和总消费
    Account->>Account: 根据总消费计算新等级
    Account->>DB: 更新account表
    Account->>DB: 插入account_log记录
    Account->>DB: 提交事务
    Account->>Purchase: 返回扣款成功
    Purchase->>DB: 插入purchase_order记录
    Purchase->>User: 返回购买成功
```

### 2. Account服务更新逻辑

#### 核心方法：UpdateAccountBalance

```go
func (d *Dao) UpdateAccountBalance(ctx context.Context, userId, amount, orderType, orderId, bookId, chapterId, description string) error {
    return mysql.Transact(db, func(tx *sqlx.Tx) error {
        // 1. 锁定账户记录
        var account po.Account
        query := `SELECT account_id, user_id, coin_balance, total_recharged, total_consumed, 
                  status, user_type, user_level FROM account WHERE user_id = ? FOR UPDATE`
        err = tx.Get(&account, query, userId)
        
        // 2. 检查账户状态和余额
        if account.Status != po.AccountStatusNormal {
            return fmt.Errorf("account is not active")
        }
        
        currentBalance, _ := po.ParseDecimalString(account.CoinBalance)
        deductAmount, _ := po.ParseDecimalString(amount)
        
        if currentBalance < deductAmount {
            return fmt.Errorf("insufficient balance")
        }
        
        // 3. 计算新值
        newBalance := currentBalance - deductAmount
        currentTotalConsumed, _ := po.ParseDecimalString(account.TotalConsumed)
        newTotalConsumed := currentTotalConsumed + deductAmount
        newUserLevel := po.GetUserLevel(fmt.Sprintf("%.2f", newTotalConsumed))
        
        // 4. 更新账户表
        updateQuery := `UPDATE account SET coin_balance = ?, total_consumed = ?, 
                        user_level = ?, updated_at = ? WHERE account_id = ?`
        _, err = tx.Exec(updateQuery, fmt.Sprintf("%.2f", newBalance), 
                        fmt.Sprintf("%.2f", newTotalConsumed), newUserLevel, time.Now(), account.AccountId)
        
        // 5. 记录交易日志
        logQuery := `INSERT INTO account_log (...) VALUES (...)`
        _, err = tx.Exec(logQuery, ...)
        
        return nil
    })
}
```

### 3. 等级计算实现

#### GetUserLevel方法

```go
func GetUserLevel(totalConsumed string) int {
    if totalConsumed == "" || totalConsumed == "0" || totalConsumed == "0.00" {
        return 1 // 默认1级
    }
    
    // 解析消费金额
    consumed, err := ParseDecimalString(totalConsumed)
    if err != nil {
        return 1 // 解析失败返回1级
    }
    
    // 计算等级：消费书币数量 / 1000 + 1
    level := int(consumed/CoinsPerLevel) + 1
    
    // 确保等级至少为1
    if level < 1 {
        level = 1
    }
    
    return level
}
```

## 数据库更新

### Account表字段更新

购买时会更新以下字段：

```sql
UPDATE account 
SET coin_balance = ?,        -- 扣除书币后的余额
    total_consumed = ?,      -- 累计消费金额（增加）
    user_level = ?,          -- 重新计算的用户等级
    updated_at = ?           -- 更新时间
WHERE account_id = ?
```

### Account Log记录

每次购买都会在account_log表中记录：

```sql
INSERT INTO account_log 
(account_id, user_id, transaction_type, amount, balance_before, balance_after, 
 order_id, book_id, chapter_id, description, created_at) 
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
```

字段说明：
- `transaction_type`: 交易类型（purchase_chapter, purchase_monthly, purchase_vip）
- `amount`: 扣除的书币数量（负数）
- `balance_before`: 扣除前余额
- `balance_after`: 扣除后余额
- `order_id`: 关联的购买订单ID

## 购买场景示例

### 场景1：购买章节

```go
// 用户当前状态
currentBalance: "1000.00"
totalConsumed: "500.00"
userLevel: 1

// 购买章节，消费50书币
purchaseAmount: "50.00"

// 购买后状态
newBalance: "950.00"      // 1000.00 - 50.00
newTotalConsumed: "550.00" // 500.00 + 50.00
newUserLevel: 1           // 550 / 1000 + 1 = 1

// 数据库更新
UPDATE account SET 
    coin_balance = '950.00',
    total_consumed = '550.00',
    user_level = 1,
    updated_at = NOW()
WHERE user_id = 'user123'
```

### 场景2：达到升级条件

```go
// 用户当前状态
currentBalance: "2000.00"
totalConsumed: "950.00"
userLevel: 1

// 购买包月，消费100书币
purchaseAmount: "100.00"

// 购买后状态
newBalance: "1900.00"      // 2000.00 - 100.00
newTotalConsumed: "1050.00" // 950.00 + 100.00
newUserLevel: 2            // 1050 / 1000 + 1 = 2 (升级！)

// 系统可以发送升级通知
if newUserLevel > oldUserLevel {
    sendLevelUpNotification(userId, newUserLevel)
}
```

## 事务安全保证

### 1. 数据库事务
- 使用`mysql.Transact`确保原子性
- 账户记录加锁（FOR UPDATE）防止并发问题
- 余额检查和扣除在同一事务中

### 2. 错误处理
- 余额不足时回滚事务
- 账户状态异常时拒绝操作
- 数据解析错误时返回错误

### 3. 并发控制
- 使用行级锁防止同一用户并发购买
- 事务隔离级别保证数据一致性

## 监控和日志

### 1. 关键指标监控
- 用户等级分布统计
- 升级频率监控
- 消费金额统计

### 2. 日志记录
- 每次等级变更记录详细日志
- 异常情况告警
- 性能监控

### 3. 数据校验
- 定期校验total_consumed和user_level的一致性
- 检查account_log记录完整性

## 扩展功能

### 1. 等级权益
```go
// 根据用户等级提供不同权益
func GetUserPrivileges(userLevel int) []string {
    switch {
    case userLevel >= 10:
        return []string{"VIP专属内容", "优先客服", "专属折扣"}
    case userLevel >= 5:
        return []string{"会员专区", "优先客服"}
    case userLevel >= 2:
        return []string{"新手福利"}
    default:
        return []string{}
    }
}
```

### 2. 等级奖励
```go
// 升级奖励
func ProcessLevelUpReward(userId string, newLevel int) {
    reward := calculateLevelReward(newLevel)
    grantReward(userId, reward)
}
```

### 3. 等级显示
```go
// 等级进度显示
func GetLevelProgress(totalConsumed string) (currentLevel int, progress float64, nextLevelCoins float64) {
    consumed, _ := ParseDecimalString(totalConsumed)
    currentLevel = int(consumed/CoinsPerLevel) + 1
    
    coinsInCurrentLevel := consumed - float64((currentLevel-1)*CoinsPerLevel)
    progress = coinsInCurrentLevel / CoinsPerLevel
    nextLevelCoins = CoinsPerLevel - coinsInCurrentLevel
    
    return
}
```

这个机制确保了用户等级能够准确反映用户的消费行为，并且在每次购买时都能及时更新，为后续的用户权益和个性化服务提供基础数据支持。
