# Response Messages - English Translation

## Overview

All response messages in Account and Purchase services have been updated from Chinese to English to maintain consistency and international compatibility.

## Account Service Messages

### GetAccount Method
| Code | Chinese (Before) | English (After) |
|------|------------------|-----------------|
| 400  | 用户ID不能为空 | User ID cannot be empty |
| 500  | 获取账户信息失败 | Failed to get account information |
| 500  | 创建账户失败 | Failed to create account |
| 200  | success | success |

### CreateAccount Method
| Code | Chinese (Before) | English (After) |
|------|------------------|-----------------|
| 400  | 用户ID不能为空 | User ID cannot be empty |
| 500  | 检查账户失败 | Failed to check existing account |
| 409  | 账户已存在 | Account already exists |
| 500  | 创建账户失败 | Failed to create account |
| 200  | 账户创建成功 | Account created successfully |

### Recharge Method
| Code | Chinese (Before) | English (After) |
|------|------------------|-----------------|
| 400  | 参数不能为空 | Parameters cannot be empty |
| 500  | 获取账户信息失败 | Failed to get account information |
| 404  | 账户不存在 | Account not found |
| 500  | 创建充值订单失败 | Failed to create recharge order |
| 200  | 充值订单创建成功 | Recharge order created successfully |

### GetAccountLogs Method
| Code | Chinese (Before) | English (After) |
|------|------------------|-----------------|
| 400  | 用户ID不能为空 | User ID cannot be empty |
| 500  | 获取账户日志失败 | Failed to get account logs |
| 200  | success | success |

### UpdateUserStatus Method
| Code | Chinese (Before) | English (After) |
|------|------------------|-----------------|
| 400  | 用户ID不能为空 | User ID cannot be empty |
| 500  | 更新用户状态失败 | Failed to update user status |
| 200  | 用户状态更新成功 | User status updated successfully |

### DeductCoins Method
| Code | Chinese (Before) | English (After) |
|------|------------------|-----------------|
| 400  | 参数不能为空 | Parameters cannot be empty |
| 500  | 扣除书币失败 | Failed to deduct coins |
| 500  | 获取账户信息失败 | Failed to get account information |
| 200  | 书币扣除成功 | Coins deducted successfully |

### CheckUserStatus Method
| Code | Chinese (Before) | English (After) |
|------|------------------|-----------------|
| 400  | 用户ID不能为空 | User ID cannot be empty |
| 500  | 检查用户状态失败 | Failed to check user status |
| 404  | 账户不存在 | Account not found |
| 200  | success | success |

## Purchase Service Messages

### PurchaseChapter Method
| Code | Chinese (Before) | English (After) |
|------|------------------|-----------------|
| 400  | 参数不能为空 | Parameters cannot be empty |
| 409  | 章节已购买 | Chapter already purchased |
| 500  | 购买章节失败 | Failed to purchase chapter |
| 200  | 章节购买成功 | Chapter purchased successfully |

### PurchaseMonthly Method
| Code | Chinese (Before) | English (After) |
|------|------------------|-----------------|
| 400  | 参数不能为空 | Parameters cannot be empty |
| 409  | 包月已激活 | Monthly subscription already active |
| 500  | 购买包月失败 | Failed to purchase monthly subscription |
| 200  | 包月购买成功 | Monthly subscription purchased successfully |

### PurchaseVip Method
| Code | Chinese (Before) | English (After) |
|------|------------------|-----------------|
| 400  | 参数不能为空 | Parameters cannot be empty |
| 500  | 购买VIP失败 | Failed to purchase VIP |
| 200  | VIP购买成功 | VIP purchased successfully |

### GetPurchaseOrders Method
| Code | Chinese (Before) | English (After) |
|------|------------------|-----------------|
| 400  | 用户ID不能为空 | User ID cannot be empty |
| 500  | 获取购买订单失败 | Failed to get purchase orders |
| 200  | success | success |

### CheckChapterPurchased Method
| Code | Chinese (Before) | English (After) |
|------|------------------|-----------------|
| 400  | 参数不能为空 | Parameters cannot be empty |
| 500  | 检查章节购买状态失败 | Failed to check chapter purchase status |
| 200  | success | success |

### CheckMonthlyStatus Method
| Code | Chinese (Before) | English (After) |
|------|------------------|-----------------|
| 400  | 参数不能为空 | Parameters cannot be empty |
| 500  | 检查包月状态失败 | Failed to check monthly status |
| 200  | success | success |

### GetPurchasedChapters Method
| Code | Chinese (Before) | English (After) |
|------|------------------|-----------------|
| 400  | 参数不能为空 | Parameters cannot be empty |
| 500  | 获取已购买章节失败 | Failed to get purchased chapters |
| 200  | success | success |

## Message Categories

### Error Messages
- **Parameter Validation**: "Parameters cannot be empty", "User ID cannot be empty"
- **Resource Not Found**: "Account not found"
- **Business Logic**: "Chapter already purchased", "Monthly subscription already active"
- **System Errors**: "Failed to get account information", "Failed to create account"

### Success Messages
- **Creation**: "Account created successfully", "Recharge order created successfully"
- **Purchase**: "Chapter purchased successfully", "VIP purchased successfully"
- **Update**: "User status updated successfully", "Coins deducted successfully"
- **Generic**: "success" (for simple query operations)

## Implementation Details

### Files Modified
1. `gen2/app/base/account/svc/account.go` - All Account service methods
2. `gen2/app/base/purchase/svc/purchase.go` - All Purchase service methods

### Pattern Used
```go
// Before (Chinese)
return &pb.SomeResp{
    Code:    400,
    Message: "参数不能为空",
}, nil

// After (English)
return &pb.SomeResp{
    Code:    400,
    Message: "Parameters cannot be empty",
}, nil
```

### Consistency Rules
1. **Parameter validation**: "Parameters cannot be empty" or specific field validation
2. **Resource not found**: "[Resource] not found"
3. **Operation failed**: "Failed to [operation]"
4. **Operation success**: "[Operation] successfully" or "success"
5. **Business conflicts**: "[Resource] already [state]"

## Benefits

1. **International Compatibility**: English messages are universally understood
2. **API Documentation**: Easier to document and understand for international developers
3. **Debugging**: Consistent error messages across the system
4. **Client Integration**: Frontend applications can display meaningful error messages
5. **Logging**: Better log analysis and monitoring

## Usage Examples

### Client Error Handling
```javascript
// Frontend JavaScript example
if (response.code !== 200) {
    switch (response.code) {
        case 400:
            showError("Invalid parameters: " + response.message);
            break;
        case 404:
            showError("Resource not found: " + response.message);
            break;
        case 409:
            showError("Conflict: " + response.message);
            break;
        case 500:
            showError("Server error: " + response.message);
            break;
        default:
            showError("Unknown error: " + response.message);
    }
}
```

### Go Client Example
```go
resp, err := accountClient.GetAccount(ctx, &pb.GetAccountReq{
    UserId: "user123",
})
if err != nil {
    log.Printf("gRPC error: %v", err)
    return
}

if resp.Code != 200 {
    log.Printf("Business error: %s (code: %d)", resp.Message, resp.Code)
    return
}

// Success handling
log.Printf("Account retrieved successfully")
```

## Verification

To verify all messages have been updated to English:

```bash
# Run the message check script
chmod +x gen2/check_messages.sh
./gen2/check_messages.sh

# Manual verification
grep -r "Message.*[\u4e00-\u9fa5]" gen2/app/base/account/svc/
grep -r "Message.*[\u4e00-\u9fa5]" gen2/app/base/purchase/svc/
```

All response messages in both Account and Purchase services now use English, providing better international compatibility and consistency across the API.
