# 生产级测试文档

## 概述

本文档描述了如何运行与生产环境一致的测试，确保测试与线上使用完全一样。测试使用与main方法相同的初始化流程，连接真实的数据库，数据会更新到数据库中。

## 测试架构

### 初始化流程
```go
// 与main方法完全一致的初始化流程
config := conf.Load(api.App, configPath)
logger.InitLog(api.App, config.Log.Level)
tracer.InitTracing(config.Base, api.App)

service := svc.Load(config)
```

### 配置文件位置
- Purchase服务: `gen2/app/base/purchase/cmd/config.toml`
- Account服务: `gen2/app/base/account/cmd/config.toml`

## 环境准备

### 1. 数据库准备
```bash
# 启动MySQL服务
sudo systemctl start mysql

# 创建测试数据库和表
mysql -u root -p < gen2/scripts/init_test_db.sql
```

### 2. Redis准备（可选）
```bash
# 启动Redis服务
sudo systemctl start redis

# 或使用Docker
docker run -d -p 6379:6379 redis:latest
```

### 3. 依赖服务
- Account服务需要启动（用于Purchase服务的gRPC调用）
- 确保所有配置的外部服务可访问

## 测试文件

### Purchase服务测试
**文件**: `gen2/app/base/purchase/svc/purchase_production_test.go`

**测试方法**:
- `TestPurchaseChapterProduction` - 购买章节完整流程
- `TestPurchaseMonthlyProduction` - 购买包月完整流程
- `TestPurchaseVipProduction` - 购买VIP完整流程
- `TestGetPurchaseOrdersProduction` - 获取订单列表完整流程
- `TestCheckChapterPurchasedProduction` - 检查购买状态完整流程

### Account服务测试
**文件**: `gen2/app/base/account/svc/account_production_test.go`

**测试方法**:
- `TestGetAccountProduction` - 获取账户完整流程
- `TestCreateAccountProduction` - 创建账户完整流程
- `TestRechargeProduction` - 充值完整流程
- `TestDeductCoinsProduction` - 扣除书币完整流程

## 运行测试

### Purchase服务测试
```bash
cd gen2/app/base/purchase/svc

# 运行生产级测试
chmod +x run_production_tests.sh
./run_production_tests.sh

# 或直接运行
go test -v -run "TestPurchase.*Production" -timeout 60s
```

### Account服务测试
```bash
cd gen2/app/base/account/svc

# 运行生产级测试
chmod +x run_production_tests.sh
./run_production_tests.sh

# 或直接运行
go test -v -run "Test.*Production" -timeout 60s
```

### 短模式测试（跳过外部依赖）
```bash
# 只运行参数验证，跳过需要外部依赖的测试
go test -v -short -run "Test.*Production" -timeout 30s
```

## 测试特点

### 1. 真实环境模拟
- ✅ 使用真实的配置文件
- ✅ 连接真实的数据库
- ✅ 调用真实的外部服务
- ✅ 数据会写入数据库

### 2. 完整业务流程
- ✅ 参数验证
- ✅ 业务逻辑处理
- ✅ 数据库操作
- ✅ 外部服务调用
- ✅ 错误处理

### 3. 测试数据管理
- ✅ 使用测试专用数据库
- ✅ 预置测试数据
- ✅ 测试后清理（可选）

## 配置文件示例

### Purchase服务配置
```toml
[base]
name = "purchase"
version = "1.0.0"
env = "test"

[mysql.default]
dsn = "root:password@tcp(localhost:3306)/beyondreading_test?charset=utf8mb4&parseTime=True&loc=Local"

[purchase]
chapter_price = "5.00"
monthly_price = "30.00"
vip_price = "100.00"
monthly_days = 30
vip_days = 30

[grpc.account]
addr = "localhost:9001"
timeout = "30s"
```

### Account服务配置
```toml
[base]
name = "account"
version = "1.0.0"
env = "test"

[mysql.default]
dsn = "root:password@tcp(localhost:3306)/beyondreading_test?charset=utf8mb4&parseTime=True&loc=Local"

[account]
default_balance = "0.00"
coins_per_level = 1000
```

## 测试结果分析

### 成功场景
- **参数验证测试**: 应该全部通过
- **业务逻辑测试**: 在环境正常时应该通过
- **数据库操作**: 数据会实际写入数据库

### 失败场景
- **数据库连接失败**: 检查数据库服务和配置
- **外部服务调用失败**: 检查依赖服务状态
- **权限问题**: 检查数据库用户权限

### 日志分析
```bash
# 查看测试日志
go test -v -run "Test.*Production" 2>&1 | tee test.log

# 分析失败原因
grep -i "error\|fail" test.log
```

## 故障排除

### 常见问题

#### 1. 配置文件未找到
```
❌ 错误: 配置文件不存在: ../cmd/config.toml
```
**解决方案**: 确保配置文件存在于正确位置

#### 2. 数据库连接失败
```
Failed to connect to database
```
**解决方案**: 
- 检查MySQL服务状态
- 验证数据库连接配置
- 确保数据库用户有足够权限

#### 3. gRPC调用失败
```
connection refused
```
**解决方案**:
- 启动依赖的gRPC服务
- 检查服务地址和端口配置

#### 4. Redis连接失败
```
redis connection failed
```
**解决方案**:
- 启动Redis服务
- 检查Redis配置
- 或在配置中禁用缓存

### 调试技巧

#### 1. 启用详细日志
```toml
[log]
level = "debug"
```

#### 2. 禁用外部依赖
```toml
[tracing.jaeger]
enable = false

[metrics.prometheus]
enable = false
```

#### 3. 使用测试数据库
```toml
[mysql.default]
dsn = "root:password@tcp(localhost:3306)/beyondreading_test?charset=utf8mb4&parseTime=True&loc=Local"
```

## 持续集成

### CI/CD 集成
```yaml
# .github/workflows/production-tests.yml
name: Production Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
        ports:
          - 3306:3306
      
      redis:
        image: redis:latest
        ports:
          - 6379:6379
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Go
      uses: actions/setup-go@v2
      with:
        go-version: 1.19
    
    - name: Initialize Database
      run: |
        mysql -h 127.0.0.1 -u root -ppassword < gen2/scripts/init_test_db.sql
    
    - name: Run Production Tests
      run: |
        cd gen2/app/base/purchase/svc
        go test -v -run "TestPurchase.*Production" -timeout 60s
        
        cd ../../../account/svc
        go test -v -run "Test.*Production" -timeout 60s
```

## 最佳实践

### 1. 测试数据管理
- 使用专用的测试数据库
- 每次测试前重置数据状态
- 使用事务回滚清理测试数据

### 2. 配置管理
- 为测试环境创建专用配置
- 使用环境变量覆盖敏感配置
- 禁用不必要的外部服务

### 3. 错误处理
- 区分参数验证错误和业务逻辑错误
- 记录详细的错误日志
- 提供清晰的错误消息

### 4. 性能考虑
- 使用连接池优化数据库连接
- 合理设置超时时间
- 监控测试执行时间

这个生产级测试框架确保了测试与线上环境的一致性，提供了完整的业务流程验证，是质量保证的重要组成部分。
