# 标准测试文档

## 概述

本文档描述了按照books模块配置加载方式生成的标准测试。这些测试完全按照项目规范，使用与main.go相同的初始化流程，确保测试与生产环境完全一致。

## 配置加载方式

### Books模块的标准方式
根据项目分析，配置加载遵循以下规范：

```go
// 标准初始化流程（与main.go完全一致）
config := conf.Load(api.App)
logger.InitLog(api.App, config.Log.Level)
tracer.InitTracing(config.Base, api.App)

service := svc.Load(config)
```

### 配置文件规范
- **文件位置**: `gen2/config/`
- **命名规则**: `{api.App}.toml`
- **Purchase服务**: `gen2/config/base-purchase.toml`
- **Account服务**: `gen2/config/base-account.toml`

### API应用名称
- **Purchase服务**: `api.App = "base-purchase"`
- **Account服务**: `api.App = "base-account"`

## 测试文件

### Purchase服务标准测试
**文件**: `gen2/app/base/purchase/svc/purchase_standard_test.go`

**初始化方式**:
```go
func setupStandardTestService(t *testing.T) *PurchaseSvc {
    // 按照main.go的方式加载配置
    config := conf.Load(api.App)
    
    // 初始化日志
    logger.InitLog(api.App, config.Log.Level)
    
    // 初始化链路追踪
    tracer.InitTracing(config.Base, api.App)
    
    // 创建服务实例
    service := Load(config)
    return service
}
```

**测试方法**:
- `TestPurchaseChapterStandard` - 购买章节标准流程
- `TestPurchaseMonthlyStandard` - 购买包月标准流程
- `TestPurchaseVipStandard` - 购买VIP标准流程
- `TestGetPurchaseOrdersStandard` - 获取订单列表标准流程
- `TestCheckChapterPurchasedStandard` - 检查购买状态标准流程

### Account服务标准测试
**文件**: `gen2/app/base/account/svc/account_standard_test.go`

**测试方法**:
- `TestGetAccountStandard` - 获取账户标准流程
- `TestCreateAccountStandard` - 创建账户标准流程
- `TestRechargeStandard` - 充值标准流程
- `TestDeductCoinsStandard` - 扣除书币标准流程

## 运行测试

### Purchase服务测试
```bash
cd gen2/app/base/purchase/svc

# 运行标准测试
chmod +x run_standard_tests.sh
./run_standard_tests.sh

# 或直接运行
go test -v -run "Test.*Standard" -timeout 60s
```

### Account服务测试
```bash
cd gen2/app/base/account/svc

# 运行标准测试
chmod +x run_standard_tests.sh
./run_standard_tests.sh

# 或直接运行
go test -v -run "Test.*Standard" -timeout 60s
```

### 短模式测试
```bash
# 只运行参数验证，跳过需要外部依赖的测试
go test -v -short -run "Test.*Standard" -timeout 30s
```

## 配置文件示例

### Purchase服务配置 (gen2/config/base-purchase.toml)
```toml
[log]
level = "info"

[redisPurchase]
address = "127.0.0.1:6379"
password = "123456"
maxIdle = 3
maxActive = 1000

[port]
http = ":9581"
debug = ":9582"
grpc = ":9583"

# Purchase业务配置
[purchase]
chapter_price = "5.00"
monthly_price = "30.00"
vip_price = "100.00"
monthly_days = 30
vip_days = 30
cache_expire = 3600
enable_cache = true

# Account服务gRPC客户端配置
[account_client]
addr = "127.0.0.1:8183"
timeout = "30s"
```

### Account服务配置 (gen2/config/base-account.toml)
```toml
[log]
level = "info"

[redisAccount]
address = "127.0.0.1:6379"
password = "123456"
maxIdle = 3
maxActive = 1000

[port]
http = ":8181"
debug = ":8182"
grpc = ":8183"

# Account业务配置
[account]
default_balance = "0.00"
coins_per_level = 1000
vip_expire_days = 30
monthly_expire_days = 30
```

## 测试特点

### 1. 完全标准化
- ✅ 使用与main.go完全相同的初始化流程
- ✅ 按照项目规范加载配置文件
- ✅ 遵循books模块的配置加载方式
- ✅ 使用真实的服务实例

### 2. 配置规范化
- ✅ 配置文件位置: `gen2/config/`
- ✅ 配置文件命名: `{api.App}.toml`
- ✅ 应用名称来自: `api.App`
- ✅ 加载方式: `conf.Load(api.App)`

### 3. 测试完整性
- ✅ 参数验证测试
- ✅ 业务逻辑测试
- ✅ 默认值处理测试
- ✅ 错误处理测试

## 与其他测试的对比

| 测试类型 | 配置加载方式 | DAO实例 | 外部依赖 | 适用场景 |
|---------|-------------|---------|---------|---------|
| 标准测试 | conf.Load(api.App) | 真实DAO | 真实连接 | 生产环境验证 |
| 参数验证测试 | 手动创建配置 | dao=nil | 无依赖 | 快速验证 |
| 集成测试 | 手动创建配置 | Mock DAO | Mock依赖 | 业务逻辑验证 |

## 测试结果分析

### 成功场景
- **参数验证测试**: 应该全部通过
- **配置加载**: 应该成功加载配置文件
- **服务初始化**: 应该成功创建服务实例

### 失败场景
- **配置文件不存在**: 检查 `gen2/config/base-{service}.toml`
- **数据库连接失败**: 检查数据库服务和配置
- **外部服务调用失败**: 检查依赖服务状态

### 日志分析
```bash
# 查看测试日志
go test -v -run "Test.*Standard" 2>&1 | tee standard_test.log

# 分析失败原因
grep -i "error\|fail" standard_test.log
```

## 故障排除

### 常见问题

#### 1. 配置文件未找到
```
❌ 错误: 配置文件不存在: ../../../../config/base-purchase.toml
```
**解决方案**: 确保配置文件存在于 `gen2/config/` 目录

#### 2. API应用名称不匹配
```
Failed to load config
```
**解决方案**: 检查 `api.App` 常量与配置文件名是否匹配

#### 3. 服务初始化失败
```
Failed to create service
```
**解决方案**: 检查配置文件格式和必要的配置项

### 调试技巧

#### 1. 验证配置加载
```go
t.Logf("Config loaded: %+v", config)
```

#### 2. 验证API应用名称
```go
t.Logf("API App: %s", api.App)
```

#### 3. 验证配置文件路径
```bash
ls -la gen2/config/base-*.toml
```

## 最佳实践

### 1. 配置管理
- 确保配置文件与api.App名称匹配
- 使用项目标准的配置结构
- 为测试环境配置合适的参数

### 2. 测试设计
- 区分参数验证和业务逻辑测试
- 使用短模式跳过外部依赖
- 记录详细的测试日志

### 3. 错误处理
- 提供清晰的错误消息
- 区分配置错误和业务错误
- 记录足够的调试信息

### 4. 持续集成
- 在CI中运行标准测试
- 监控测试覆盖率
- 定期验证配置文件

## 扩展说明

### 与Books模块对比
标准测试完全按照books模块的方式实现：
- 相同的配置加载流程
- 相同的初始化顺序
- 相同的文件组织结构
- 相同的命名规范

### 未来改进
1. **配置验证**: 添加配置文件格式验证
2. **环境隔离**: 支持不同环境的配置
3. **自动化**: 自动生成测试配置
4. **监控**: 添加测试性能监控

这个标准测试框架确保了与项目规范的完全一致性，提供了可靠的测试基础设施。
