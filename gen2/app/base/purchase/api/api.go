package api

import (
	pb "creativematrix.com/beyondreading/gen2/proto/purchase"
	"creativematrix.com/beyondreading/pkg/config"
	"creativematrix.com/beyondreading/pkg/discovery"
	"creativematrix.com/beyondreading/pkg/logger"
	"google.golang.org/grpc"
)

const App = "base-purchase"

func NewClient(c config.Base) (pb.PurchaseClient, error) {
	resolver := discovery.NewResolver(c.Etcd.Addrs, logger.Log)
	conn, err := grpc.Dial(
		resolver.Scheme()+"://"+App,
		grpc.WithInsecure(),
		grpc.WithResolvers(resolver),
		grpc.WithDefaultServiceConfig(`{"loadBalancingPolicy":"round_robin"}`),
	)
	if err != nil {
		return nil, err
	}

	return pb.NewPurchaseClient(conn), nil
}
