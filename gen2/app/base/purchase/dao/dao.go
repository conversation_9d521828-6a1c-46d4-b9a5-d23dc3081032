package dao

import (
	"context"
	"fmt"

	accountapi "creativematrix.com/beyondreading/gen2/app/base/account/api"
	"creativematrix.com/beyondreading/gen2/app/base/purchase/conf"
	accountpb "creativematrix.com/beyondreading/gen2/proto/account"
	"creativematrix.com/beyondreading/pkg/mysql"
	"creativematrix.com/beyondreading/pkg/redis"
	"github.com/jmoiron/sqlx"
)

type Dao struct {
	msshard       mysql.Mysqler
	cache         redis.Redis
	conf          *conf.Config
	AccountClient accountpb.AccountClient
}

func Load(c *conf.Config) *Dao {
	// 初始化MySQL连接
	mysqlConn := mysql.New(c.Mysql)

	// 初始化Redis连接
	redisConn := redis.Load(c.Cache)

	// 初始化Account gRPC客户端
	rpcAccount, err := accountapi.NewClient(c.Base)
	if err != nil {
		panic(fmt.Sprintf("failed to create account client: %v", err))
	}

	return &Dao{
		msshard:       mysqlConn,
		cache:         redisConn,
		conf:          c,
		AccountClient: rpcAccount,
	}
}

func (d *Dao) Ping(ctx context.Context) error {
	// 检查MySQL连接
	for _, db := range d.msshard.All() {
		if err := db.PingContext(ctx); err != nil {
			return fmt.Errorf("mysql ping failed: %w", err)
		}
	}

	// 检查Redis连接
	if _, err := d.cache.RDo(ctx, "PING"); err != nil {
		return fmt.Errorf("redis ping failed: %w", err)
	}

	return nil
}

func (d *Dao) Close() {
	// 关闭Redis连接
	if err := d.cache.RClose(); err != nil {
		fmt.Printf("Failed to close redis connection: %v\n", err)
	}
}

// GetDB 获取数据库连接
func (d *Dao) GetDB(userId uint64) (*sqlx.DB, error) {
	// 将uint64转换为string用于分片键
	userIdStr := fmt.Sprintf("%d", userId)
	db, err := d.msshard.DB(userIdStr)
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}
	return db, nil
}

// GetTableName 获取分表名称
func (d *Dao) GetTableName(userId uint64, tableName string) string {
	// 将uint64转换为string用于分片键
	userIdStr := fmt.Sprintf("%d", userId)
	return d.msshard.Table(userIdStr, tableName)
}

// GetCache 获取缓存连接
func (d *Dao) GetCache() redis.Redis {
	return d.cache
}

// GetConfig 获取配置
func (d *Dao) GetConfig() *conf.Config {
	return d.conf
}
