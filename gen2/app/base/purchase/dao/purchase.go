package dao

import (
	"context"
	"fmt"
	"time"

	"creativematrix.com/beyondreading/gen2/app/common/po"
	accountpb "creativematrix.com/beyondreading/gen2/proto/account"
	"creativematrix.com/beyondreading/pkg/mysql"
	"github.com/jmoiron/sqlx"
)

// CreatePurchaseOrder 创建购买订单
func (d *Dao) CreatePurchaseOrder(ctx context.Context, order *po.PurchaseOrder) error {
	db, err := d.GetDB(order.UserId)
	if err != nil {
		return err
	}

	query := `INSERT INTO purchase_order (order_id, account_id, user_id, order_type, book_id,
			  book_name, chapter_id, chapter_title, chapter_order, coin_amount, duration_days,
			  start_time, end_time, status, created_at, updated_at)
			  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

	_, err = db.Exec(query, order.OrderId, order.AccountId, order.UserId,
		order.OrderType, order.BookId, order.BookName, order.ChapterId, order.ChapterTitle,
		order.ChapterOrder, order.CoinAmount, order.DurationDays, order.StartTime,
		order.EndTime, order.Status, order.CreatedAt, order.UpdatedAt)
	if err != nil {
		return fmt.Errorf("failed to create purchase order: %w", err)
	}

	return nil
}

// PurchaseChapter 购买章节
func (d *Dao) PurchaseChapter(ctx context.Context, userId uint64, bookId string, chapterOrder uint32, coinAmount string) (*po.PurchaseOrder, error) {
	// 检查是否已经购买过
	purchased, err := d.CheckChapterPurchased(ctx, userId, bookId, chapterOrder)
	if err != nil {
		return nil, err
	}
	if purchased {
		return nil, fmt.Errorf("chapter already purchased")
	}

	// 获取账户信息
	accountResp, err := d.AccountClient.GetAccount(ctx, &accountpb.GetAccountReq{
		UserId: userId,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get account: %w", err)
	}
	if accountResp.Code != 200 {
		return nil, fmt.Errorf("get account failed: %s", accountResp.Message)
	}

	db, err := d.GetDB(userId)
	if err != nil {
		return nil, err
	}

	var order *po.PurchaseOrder
	err = mysql.Transact(db, func(tx *sqlx.Tx) error {
		// 生成订单ID
		orderId := d.generateOrderId("PC") // PC = Purchase Chapter

		// 扣除书币
		deductResp, err := d.AccountClient.DeductCoins(ctx, &accountpb.DeductCoinsReq{
			UserId:          userId,
			Amount:          coinAmount,
			OrderId:         orderId,
			BookId:          bookId,
			TransactionType: po.TransactionTypePurchaseChapter,
			Description:     fmt.Sprintf("购买章节 %d", chapterOrder),
		})
		if err != nil {
			return fmt.Errorf("failed to deduct coins: %w", err)
		}
		if deductResp.Code != 200 {
			return fmt.Errorf("deduct coins failed: %s", deductResp.Message)
		}

		// 创建购买订单
		now := time.Now()
		order = &po.PurchaseOrder{
			OrderId:      orderId,
			AccountId:    accountResp.Account.AccountId,
			UserId:       userId,
			OrderType:    po.OrderTypeChapter,
			BookId:       &bookId,
			ChapterOrder: &chapterOrder,
			CoinAmount:   coinAmount,
			Status:       po.OrderStatusPaid,
			CreatedAt:    now,
			UpdatedAt:    now,
		}

		// 插入订单记录
		query := `INSERT INTO purchase_order (order_id, account_id, user_id, order_type, book_id,
				  chapter_order, coin_amount, status, created_at, updated_at)
				  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

		_, err = tx.Exec(query, order.OrderId, order.AccountId, order.UserId,
			order.OrderType, order.BookId, order.ChapterOrder, order.CoinAmount,
			order.Status, order.CreatedAt, order.UpdatedAt)
		if err != nil {
			return fmt.Errorf("failed to insert purchase order: %w", err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return order, nil
}

// PurchaseMonthly 购买包月
func (d *Dao) PurchaseMonthly(ctx context.Context, userId uint64, bookId, bookName, coinAmount string, durationDays int) (*po.PurchaseOrder, error) {
	// 检查是否已有有效包月
	active, err := d.CheckMonthlyStatus(ctx, userId, bookId)
	if err != nil {
		return nil, err
	}
	if active {
		return nil, fmt.Errorf("monthly subscription already active")
	}

	// 获取账户信息
	accountResp, err := d.AccountClient.GetAccount(ctx, &accountpb.GetAccountReq{
		UserId: userId,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get account: %w", err)
	}
	if accountResp.Code != 200 {
		return nil, fmt.Errorf("get account failed: %s", accountResp.Message)
	}

	db, err := d.GetDB(userId)
	if err != nil {
		return nil, err
	}

	var order *po.PurchaseOrder
	err = mysql.Transact(db, func(tx *sqlx.Tx) error {
		// 生成订单ID
		orderId := d.generateOrderId("PM") // PM = Purchase Monthly

		// 扣除书币
		deductResp, err := d.AccountClient.DeductCoins(ctx, &accountpb.DeductCoinsReq{
			UserId:          userId,
			Amount:          coinAmount,
			OrderId:         orderId,
			BookId:          bookId,
			TransactionType: po.TransactionTypePurchaseMonthly,
			Description:     fmt.Sprintf("购买包月 %s", bookName),
		})
		if err != nil {
			return fmt.Errorf("failed to deduct coins: %w", err)
		}
		if deductResp.Code != 200 {
			return fmt.Errorf("deduct coins failed: %s", deductResp.Message)
		}

		// 创建购买订单
		now := time.Now()
		startTime := now
		endTime := now.AddDate(0, 0, durationDays)

		order = &po.PurchaseOrder{
			OrderId:      orderId,
			AccountId:    accountResp.Account.AccountId,
			UserId:       userId,
			OrderType:    po.OrderTypeMonthly,
			BookId:       &bookId,
			BookName:     &bookName,
			CoinAmount:   coinAmount,
			DurationDays: &durationDays,
			StartTime:    &startTime,
			EndTime:      &endTime,
			Status:       po.OrderStatusPaid,
			CreatedAt:    now,
			UpdatedAt:    now,
		}

		// 插入订单记录
		query := `INSERT INTO purchase_order (order_id, account_id, user_id, order_type, book_id,
				  book_name, coin_amount, duration_days, start_time, end_time, status, created_at, updated_at)
				  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

		_, err = tx.Exec(query, order.OrderId, order.AccountId, order.UserId,
			order.OrderType, order.BookId, order.BookName, order.CoinAmount,
			order.DurationDays, order.StartTime, order.EndTime, order.Status,
			order.CreatedAt, order.UpdatedAt)
		if err != nil {
			return fmt.Errorf("failed to insert purchase order: %w", err)
		}

		// 更新包月状态表
		monthlyQuery := `INSERT INTO monthly_status (user_id, book_id, order_id, start_time, end_time, status, created_at, updated_at)
						 VALUES (?, ?, ?, ?, ?, ?, ?, ?)
						 ON DUPLICATE KEY UPDATE order_id = VALUES(order_id), start_time = VALUES(start_time),
						 end_time = VALUES(end_time), status = VALUES(status), updated_at = VALUES(updated_at)`

		_, err = tx.Exec(monthlyQuery, userId, bookId, orderId, startTime, endTime, 1, now, now)
		if err != nil {
			return fmt.Errorf("failed to update monthly status: %w", err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return order, nil
}

// PurchaseVip 购买VIP
func (d *Dao) PurchaseVip(ctx context.Context, userId uint64, coinAmount string, durationDays int) (*po.PurchaseOrder, error) {
	// 获取账户信息
	accountResp, err := d.AccountClient.GetAccount(ctx, &accountpb.GetAccountReq{
		UserId: userId,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get account: %w", err)
	}
	if accountResp.Code != 200 {
		return nil, fmt.Errorf("get account failed: %s", accountResp.Message)
	}

	db, err := d.GetDB(userId)
	if err != nil {
		return nil, err
	}

	var order *po.PurchaseOrder
	err = mysql.Transact(db, func(tx *sqlx.Tx) error {
		// 生成订单ID
		orderId := d.generateOrderId("PV") // PV = Purchase VIP

		// 扣除书币
		deductResp, err := d.AccountClient.DeductCoins(ctx, &accountpb.DeductCoinsReq{
			UserId:          userId,
			Amount:          coinAmount,
			OrderId:         orderId,
			TransactionType: po.TransactionTypePurchaseVip,
			Description:     fmt.Sprintf("购买VIP %d天", durationDays),
		})
		if err != nil {
			return fmt.Errorf("failed to deduct coins: %w", err)
		}
		if deductResp.Code != 200 {
			return fmt.Errorf("deduct coins failed: %s", deductResp.Message)
		}

		// 计算VIP过期时间
		now := time.Now()
		var vipExpireTime time.Time
		if accountResp.Account.VipExpireTime > 0 && time.Unix(accountResp.Account.VipExpireTime, 0).After(now) {
			// 如果已有VIP，在现有基础上延长
			vipExpireTime = time.Unix(accountResp.Account.VipExpireTime, 0).AddDate(0, 0, durationDays)
		} else {
			// 如果没有VIP或已过期，从现在开始计算
			vipExpireTime = now.AddDate(0, 0, durationDays)
		}

		// 更新用户VIP状态
		_, err = d.AccountClient.UpdateUserStatus(ctx, &accountpb.UpdateUserStatusReq{
			UserId:        userId,
			UserType:      po.UserTypeVip,
			VipExpireTime: vipExpireTime.Unix(),
		})
		if err != nil {
			return fmt.Errorf("failed to update user status: %w", err)
		}

		// 创建购买订单
		startTime := now
		endTime := vipExpireTime

		order = &po.PurchaseOrder{
			OrderId:      orderId,
			AccountId:    accountResp.Account.AccountId,
			UserId:       userId,
			OrderType:    po.OrderTypeVip,
			CoinAmount:   coinAmount,
			DurationDays: &durationDays,
			StartTime:    &startTime,
			EndTime:      &endTime,
			Status:       po.OrderStatusPaid,
			CreatedAt:    now,
			UpdatedAt:    now,
		}

		// 插入订单记录
		query := `INSERT INTO purchase_order (order_id, account_id, user_id, order_type,
				  coin_amount, duration_days, start_time, end_time, status, created_at, updated_at)
				  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

		_, err = tx.Exec(query, order.OrderId, order.AccountId, order.UserId,
			order.OrderType, order.CoinAmount, order.DurationDays, order.StartTime,
			order.EndTime, order.Status, order.CreatedAt, order.UpdatedAt)
		if err != nil {
			return fmt.Errorf("failed to insert purchase order: %w", err)
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return order, nil
}

// CheckChapterPurchased 检查章节是否已购买
func (d *Dao) CheckChapterPurchased(ctx context.Context, userId uint64, bookId string, chapterOrder uint32) (bool, error) {
	db, err := d.GetDB(userId)
	if err != nil {
		return false, err
	}

	// 检查直接购买
	query := `SELECT COUNT(*) FROM purchase_order
			  WHERE user_id = ? AND book_id = ? AND chapter_order = ? AND order_type = ? AND status = ?`
	var count int
	err = db.Get(&count, query, userId, bookId, chapterOrder, po.OrderTypeChapter, po.OrderStatusPaid)
	if err != nil {
		return false, fmt.Errorf("failed to check chapter purchased: %w", err)
	}

	if count > 0 {
		return true, nil
	}

	// 检查包月状态
	return d.CheckMonthlyStatus(ctx, userId, bookId)
}

// CheckMonthlyStatus 检查包月状态
func (d *Dao) CheckMonthlyStatus(ctx context.Context, userId uint64, bookId string) (bool, error) {
	db, err := d.GetDB(userId)
	if err != nil {
		return false, err
	}

	query := `SELECT COUNT(*) FROM monthly_status
			  WHERE user_id = ? AND book_id = ? AND status = 1 AND end_time > NOW()`
	var count int
	err = db.Get(&count, query, userId, bookId)
	if err != nil {
		return false, fmt.Errorf("failed to check monthly status: %w", err)
	}

	return count > 0, nil
}

// GetPurchaseOrders 获取购买订单列表
func (d *Dao) GetPurchaseOrders(ctx context.Context, userId uint64, page, pageSize int, orderType string) ([]*po.PurchaseOrder, int64, error) {
	db, err := d.GetDB(userId)
	if err != nil {
		return nil, 0, err
	}

	// 构建查询条件
	whereClause := "WHERE user_id = ?"
	args := []interface{}{userId}

	if orderType != "" {
		whereClause += " AND order_type = ?"
		args = append(args, orderType)
	}

	// 查询总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM purchase_order %s", whereClause)
	var total int64
	err = db.Get(&total, countQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count purchase orders: %w", err)
	}

	// 查询数据
	offset := (page - 1) * pageSize
	dataQuery := fmt.Sprintf(`SELECT order_id, account_id, user_id, order_type, book_id, book_name,
							  chapter_id, chapter_title, chapter_order, coin_amount, duration_days,
							  start_time, end_time, status, created_at, updated_at
							  FROM purchase_order %s ORDER BY created_at DESC LIMIT ? OFFSET ?`, whereClause)

	args = append(args, pageSize, offset)

	var orders []*po.PurchaseOrder
	err = db.Select(&orders, dataQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get purchase orders: %w", err)
	}

	return orders, total, nil
}

// GetPurchasedChapters 获取已购买的章节列表
func (d *Dao) GetPurchasedChapters(ctx context.Context, userId uint64, bookId string, page, pageSize int) ([]*po.PurchaseOrder, int64, error) {
	db, err := d.GetDB(userId)
	if err != nil {
		return nil, 0, err
	}

	// 查询总数
	countQuery := `SELECT COUNT(*) FROM purchase_order
				   WHERE user_id = ? AND book_id = ? AND order_type = ? AND status = ?`
	var total int64
	err = db.Get(&total, countQuery, userId, bookId, po.OrderTypeChapter, po.OrderStatusPaid)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count purchased chapters: %w", err)
	}

	// 查询数据
	offset := (page - 1) * pageSize
	dataQuery := `SELECT order_id, account_id, user_id, order_type, book_id, book_name,
				  chapter_id, chapter_title, chapter_order, coin_amount, status, created_at, updated_at
				  FROM purchase_order
				  WHERE user_id = ? AND book_id = ? AND order_type = ? AND status = ?
				  ORDER BY chapter_order ASC LIMIT ? OFFSET ?`

	var chapters []*po.PurchaseOrder
	err = db.Select(&chapters, dataQuery, userId, bookId, po.OrderTypeChapter, po.OrderStatusPaid, pageSize, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get purchased chapters: %w", err)
	}

	return chapters, total, nil
}

// 辅助方法
func (d *Dao) generateOrderId(prefix string) string {
	// 简化实现，实际应该生成唯一ID
	return fmt.Sprintf("%s%d", prefix, time.Now().UnixNano())
}
