package grpc

import (
	"context"
	"net"

	"creativematrix.com/beyondreading/gen2/app/base/purchase/conf"
	"creativematrix.com/beyondreading/gen2/app/base/purchase/svc"
	pb "creativematrix.com/beyondreading/gen2/proto/purchase"
	"creativematrix.com/beyondreading/pkg/gm"
	"google.golang.org/grpc"
)

func Start(c *conf.Config, svc *svc.PurchaseSvc) (*grpc.Server, error) {
	s := grpc.NewServer(grpc.UnaryInterceptor(gm.UnaryServerInterceptor()))

	pb.RegisterPurchaseServer(s, &server{ps: svc})
	lis, err := net.Listen("tcp", c.Port.GRPC)
	if err != nil {
		return nil, err
	}
	go func() {
		if err := s.Serve(lis); err != nil {
			panic(err)
		}
	}()
	return s, nil
}

type server struct {
	ps *svc.PurchaseSvc
}

// PurchaseChapter 购买章节
func (s *server) PurchaseChapter(ctx context.Context, req *pb.PurchaseChapterReq) (*pb.PurchaseChapterResp, error) {
	return s.ps.PurchaseChapter(ctx, req)
}

// PurchaseMonthly 购买包月
func (s *server) PurchaseMonthly(ctx context.Context, req *pb.PurchaseMonthlyReq) (*pb.PurchaseMonthlyResp, error) {
	return s.ps.PurchaseMonthly(ctx, req)
}

// PurchaseVip 购买VIP
func (s *server) PurchaseVip(ctx context.Context, req *pb.PurchaseVipReq) (*pb.PurchaseVipResp, error) {
	return s.ps.PurchaseVip(ctx, req)
}

// GetPurchaseOrders 获取购买订单列表
func (s *server) GetPurchaseOrders(ctx context.Context, req *pb.GetPurchaseOrdersReq) (*pb.GetPurchaseOrdersResp, error) {
	return s.ps.GetPurchaseOrders(ctx, req)
}

// CheckChapterPurchased 检查章节购买状态
func (s *server) CheckChapterPurchased(ctx context.Context, req *pb.CheckChapterPurchasedReq) (*pb.CheckChapterPurchasedResp, error) {
	return s.ps.CheckChapterPurchased(ctx, req)
}

// CheckMonthlyStatus 检查包月状态
func (s *server) CheckMonthlyStatus(ctx context.Context, req *pb.CheckMonthlyStatusReq) (*pb.CheckMonthlyStatusResp, error) {
	return s.ps.CheckMonthlyStatus(ctx, req)
}

// GetPurchasedChapters 获取已购买的章节列表
func (s *server) GetPurchasedChapters(ctx context.Context, req *pb.GetPurchasedChaptersReq) (*pb.GetPurchasedChaptersResp, error) {
	return s.ps.GetPurchasedChapters(ctx, req)
}
