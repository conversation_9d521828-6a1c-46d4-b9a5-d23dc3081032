package conf

import (
	"fmt"

	"creativematrix.com/beyondreading/pkg/config"
)

type Config struct {
	config.Base

	Log struct {
		Level string `toml:"level"`
	} `toml:"log"`

	Purchase struct {
		ChapterPrice string `toml:"chapter_price"` // 默认章节价格
		MonthlyPrice string `toml:"monthly_price"` // 包月价格
		VipPrice     string `toml:"vip_price"`     // VIP价格
		MonthlyDays  int    `toml:"monthly_days"`  // 包月天数
		VipDays      int    `toml:"vip_days"`      // VIP天数
		CacheExpire  int    `toml:"cache_expire"`  // 缓存过期时间（秒）
		EnableCache  bool   `toml:"enable_cache"`  // 是否启用缓存
	} `toml:"purchase"`
}

func Load(app string) *Config {
	var conf = new(Config)
	if err := config.Load(app, conf); err != nil {
		panic(fmt.Sprintf("config load failed: %v", err))
	}
	return conf
}
