package svc

import (
	"context"
	"fmt"
	"github.com/BurntSushi/toml"
	"os"
	"path/filepath"
	"testing"

	"creativematrix.com/beyondreading/gen2/app/base/purchase/conf"
	pb "creativematrix.com/beyondreading/gen2/proto/purchase"
	"creativematrix.com/beyondreading/pkg/logger"
)

const (
	// 测试应用名称
	testApp = "base-purchase"

	// 测试用户和数据
	testUserId            = "test_user_123"
	testBookId            = "test_book_456"
	testBookName          = "测试书籍"
	testChapterOrder      = uint32(1)
	testCoinAmount        = "5.00"
	testMonthlyCoinAmount = "30.00"
	testVipCoinAmount     = "100.00"
)

func doInit() *PurchaseSvc {
	app := "base-purchase"
	cf, err := loadConf(app)
	if err != nil {
		panic(err)
	}

	logger.InitLog(app, cf.Log.Level)
	purchaseSvc := Load(cf)

	return purchaseSvc
}

func loadConf(app string) (*conf.Config, error) {
	cf := &conf.Config{}
	if _, err := toml.DecodeFile("../../../../base.toml", cf); err != nil {
		return nil, err
	}
	if _, err := toml.DecodeFile(fmt.Sprintf("../cmd/%s.toml", app), cf); err != nil {
		return nil, err
	}
	return cf, nil
}

// setupTestService 按照main方法的方式创建测试服务
func setupTestService(t *testing.T) *PurchaseSvc {

	return doInit()
}

// getConfigPath 获取配置文件路径
func getConfigPath(t *testing.T) string {
	// 获取当前工作目录
	wd, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get working directory: %v", err)
	}

	// 构建配置文件路径：从svc目录到cmd目录
	configPath := filepath.Join(wd, "..", "cmd", "config.toml")

	// 检查配置文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		t.Fatalf("Config file not found: %s", configPath)
	}

	return configPath
}

// cleanupTestData 清理测试数据
func cleanupTestData(t *testing.T, service *PurchaseSvc) {
	// 这里可以添加清理测试数据的逻辑
	// 例如删除测试过程中创建的订单记录
	t.Log("Cleaning up test data...")
}

// TestPurchaseChapterProduction 生产级购买章节测试
func TestPurchaseChapterProduction(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping production test in short mode")
	}

	service := setupTestService(t)
	defer cleanupTestData(t, service)

	ctx := context.Background()

	tests := []struct {
		name         string
		req          *pb.PurchaseChapterReq
		expectedCode int32
		checkOrder   bool
	}{
		//{
		//	name: "参数验证 - 用户ID为空",
		//	req: &pb.PurchaseChapterReq{
		//		UserId:       "",
		//		BookId:       testBookId,
		//		ChapterOrder: testChapterOrder,
		//		CoinAmount:   testCoinAmount,
		//	},
		//	expectedCode: 400,
		//	checkOrder:   false,
		//},
		//{
		//	name: "参数验证 - 书籍ID为空",
		//	req: &pb.PurchaseChapterReq{
		//		UserId:       testUserId,
		//		BookId:       "",
		//		ChapterOrder: testChapterOrder,
		//		CoinAmount:   testCoinAmount,
		//	},
		//	expectedCode: 400,
		//	checkOrder:   false,
		//},
		//{
		//	name: "参数验证 - 章节序号为0",
		//	req: &pb.PurchaseChapterReq{
		//		UserId:       testUserId,
		//		BookId:       testBookId,
		//		ChapterOrder: 0,
		//		CoinAmount:   testCoinAmount,
		//	},
		//	expectedCode: 400,
		//	checkOrder:   false,
		//},
		//{
		//	name: "参数验证 - 书币数量为空",
		//	req: &pb.PurchaseChapterReq{
		//		UserId:       testUserId,
		//		BookId:       testBookId,
		//		ChapterOrder: testChapterOrder,
		//		CoinAmount:   "",
		//	},
		//	expectedCode: 400,
		//	checkOrder:   false,
		//},
		{
			name: "正常购买章节 - 可能因为数据库连接或余额不足而失败",
			req: &pb.PurchaseChapterReq{
				UserId:       testUserId,
				BookId:       testBookId,
				ChapterOrder: testChapterOrder,
				CoinAmount:   testCoinAmount,
			},
			expectedCode: 0, // 不预期特定的错误码，因为可能是数据库连接问题
			checkOrder:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := service.PurchaseChapter(ctx, tt.req)

			if err != nil {
				t.Errorf("PurchaseChapter() error = %v", err)
				return
			}

			if tt.expectedCode != 0 && resp.Code != tt.expectedCode {
				t.Errorf("PurchaseChapter() code = %v, want %v", resp.Code, tt.expectedCode)
			}

			// 记录响应信息用于调试
			t.Logf("Response: Code=%d, Message=%s", resp.Code, resp.Message)

			if tt.checkOrder && resp.Order != nil {
				t.Logf("Order created: ID=%s, Type=%s", resp.Order.OrderId, resp.Order.OrderType)
			}
		})
	}
}

// TestPurchaseMonthlyProduction 生产级购买包月测试
func TestPurchaseMonthlyProduction(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping production test in short mode")
	}

	service := setupTestService(t)
	defer cleanupTestData(t, service)

	ctx := context.Background()

	tests := []struct {
		name         string
		req          *pb.PurchaseMonthlyReq
		expectedCode int32
	}{
		{
			name: "参数验证 - 用户ID为空",
			req: &pb.PurchaseMonthlyReq{
				UserId:     "",
				BookId:     testBookId,
				BookName:   testBookName,
				CoinAmount: testMonthlyCoinAmount,
			},
			expectedCode: 400,
		},
		{
			name: "参数验证 - 书籍ID为空",
			req: &pb.PurchaseMonthlyReq{
				UserId:     testUserId,
				BookId:     "",
				BookName:   testBookName,
				CoinAmount: testMonthlyCoinAmount,
			},
			expectedCode: 400,
		},
		{
			name: "参数验证 - 书籍名称为空",
			req: &pb.PurchaseMonthlyReq{
				UserId:     testUserId,
				BookId:     testBookId,
				BookName:   "",
				CoinAmount: testMonthlyCoinAmount,
			},
			expectedCode: 400,
		},
		{
			name: "参数验证 - 书币数量为空",
			req: &pb.PurchaseMonthlyReq{
				UserId:     testUserId,
				BookId:     testBookId,
				BookName:   testBookName,
				CoinAmount: "",
			},
			expectedCode: 400,
		},
		{
			name: "默认天数测试",
			req: &pb.PurchaseMonthlyReq{
				UserId:       testUserId,
				BookId:       testBookId,
				BookName:     testBookName,
				CoinAmount:   testMonthlyCoinAmount,
				DurationDays: 0, // 应该使用配置中的默认值
			},
			expectedCode: 0, // 不预期特定错误码
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := service.PurchaseMonthly(ctx, tt.req)

			if err != nil {
				t.Errorf("PurchaseMonthly() error = %v", err)
				return
			}

			if tt.expectedCode != 0 && resp.Code != tt.expectedCode {
				t.Errorf("PurchaseMonthly() code = %v, want %v", resp.Code, tt.expectedCode)
			}

			t.Logf("Response: Code=%d, Message=%s", resp.Code, resp.Message)
		})
	}
}

// TestPurchaseVipProduction 生产级购买VIP测试
func TestPurchaseVipProduction(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping production test in short mode")
	}

	service := setupTestService(t)
	defer cleanupTestData(t, service)

	ctx := context.Background()

	tests := []struct {
		name         string
		req          *pb.PurchaseVipReq
		expectedCode int32
	}{
		{
			name: "参数验证 - 用户ID为空",
			req: &pb.PurchaseVipReq{
				UserId:     "",
				CoinAmount: testVipCoinAmount,
			},
			expectedCode: 400,
		},
		{
			name: "参数验证 - 书币数量为空",
			req: &pb.PurchaseVipReq{
				UserId:     testUserId,
				CoinAmount: "",
			},
			expectedCode: 400,
		},
		{
			name: "默认天数测试",
			req: &pb.PurchaseVipReq{
				UserId:       testUserId,
				CoinAmount:   testVipCoinAmount,
				DurationDays: 0, // 应该使用配置中的默认值
			},
			expectedCode: 0, // 不预期特定错误码
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := service.PurchaseVip(ctx, tt.req)

			if err != nil {
				t.Errorf("PurchaseVip() error = %v", err)
				return
			}

			if tt.expectedCode != 0 && resp.Code != tt.expectedCode {
				t.Errorf("PurchaseVip() code = %v, want %v", resp.Code, tt.expectedCode)
			}

			t.Logf("Response: Code=%d, Message=%s", resp.Code, resp.Message)
		})
	}
}

// TestGetPurchaseOrdersProduction 生产级获取购买订单测试
func TestGetPurchaseOrdersProduction(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping production test in short mode")
	}

	service := setupTestService(t)
	defer cleanupTestData(t, service)

	ctx := context.Background()

	tests := []struct {
		name         string
		req          *pb.GetPurchaseOrdersReq
		expectedCode int32
	}{
		{
			name: "参数验证 - 用户ID为空",
			req: &pb.GetPurchaseOrdersReq{
				UserId: "",
			},
			expectedCode: 400,
		},
		{
			name: "正常获取订单列表",
			req: &pb.GetPurchaseOrdersReq{
				UserId:   testUserId,
				Page:     1,
				PageSize: 10,
			},
			expectedCode: 0, // 可能因为数据库连接问题而失败
		},
		{
			name: "默认分页参数",
			req: &pb.GetPurchaseOrdersReq{
				UserId:   testUserId,
				Page:     0, // 应该使用默认值1
				PageSize: 0, // 应该使用默认值20
			},
			expectedCode: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := service.GetPurchaseOrders(ctx, tt.req)

			if err != nil {
				t.Errorf("GetPurchaseOrders() error = %v", err)
				return
			}

			if tt.expectedCode != 0 && resp.Code != tt.expectedCode {
				t.Errorf("GetPurchaseOrders() code = %v, want %v", resp.Code, tt.expectedCode)
			}

			t.Logf("Response: Code=%d, Message=%s, Total=%d", resp.Code, resp.Message, resp.Total)
		})
	}
}

// TestCheckChapterPurchasedProduction 生产级检查章节购买状态测试
func TestCheckChapterPurchasedProduction(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping production test in short mode")
	}

	service := setupTestService(t)
	defer cleanupTestData(t, service)

	ctx := context.Background()

	tests := []struct {
		name         string
		req          *pb.CheckChapterPurchasedReq
		expectedCode int32
	}{
		{
			name: "参数验证 - 用户ID为空",
			req: &pb.CheckChapterPurchasedReq{
				UserId:       "",
				BookId:       testBookId,
				ChapterOrder: testChapterOrder,
			},
			expectedCode: 400,
		},
		{
			name: "参数验证 - 书籍ID为空",
			req: &pb.CheckChapterPurchasedReq{
				UserId:       testUserId,
				BookId:       "",
				ChapterOrder: testChapterOrder,
			},
			expectedCode: 400,
		},
		{
			name: "正常检查章节购买状态",
			req: &pb.CheckChapterPurchasedReq{
				UserId:       testUserId,
				BookId:       testBookId,
				ChapterOrder: testChapterOrder,
			},
			expectedCode: 0, // 可能因为数据库连接问题而失败
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := service.CheckChapterPurchased(ctx, tt.req)

			if err != nil {
				t.Errorf("CheckChapterPurchased() error = %v", err)
				return
			}

			if tt.expectedCode != 0 && resp.Code != tt.expectedCode {
				t.Errorf("CheckChapterPurchased() code = %v, want %v", resp.Code, tt.expectedCode)
			}

			t.Logf("Response: Code=%d, Message=%s, IsPurchased=%v",
				resp.Code, resp.Message, resp.IsPurchased)
		})
	}
}
