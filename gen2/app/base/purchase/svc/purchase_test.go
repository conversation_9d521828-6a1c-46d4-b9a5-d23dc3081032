package svc

import (
	"context"
	"testing"

	"creativematrix.com/beyondreading/gen2/app/base/purchase/conf"
	pb "creativematrix.com/beyondreading/gen2/proto/purchase"
)

// createTestConfig 创建测试配置
func createTestConfig() *conf.Config {
	return &conf.Config{
		Purchase: struct {
			ChapterPrice    string `toml:"chapter_price"`
			MonthlyPrice    string `toml:"monthly_price"`
			VipPrice        string `toml:"vip_price"`
			MonthlyDays     int    `toml:"monthly_days"`
			VipDays         int    `toml:"vip_days"`
			CacheExpire     int    `toml:"cache_expire"`
			EnableCache     bool   `toml:"enable_cache"`
		}{
			ChapterPrice: "5.00",
			MonthlyPrice: "30.00",
			VipPrice:     "100.00",
			MonthlyDays:  30,
			VipDays:      30,
			CacheExpire:  3600,
			EnableCache:  true,
		},
	}
}

// TestPurchaseChapter 测试购买章节方法 - 仅参数验证
func TestPurchaseChapter(t *testing.T) {
	// 注意：这个测试只能验证参数验证逻辑，因为dao为nil
	// 要测试完整业务逻辑，请参考 purchase_real_test.go
	svc := &PurchaseSvc{
		conf: createTestConfig(),
		dao:  nil, // 参数验证测试不需要真实的 DAO
	}

	tests := []struct {
		name         string
		req          *pb.PurchaseChapterReq
		expectedCode int32
		expectedMsg  string
	}{
		{
			name: "用户ID为空",
			req: &pb.PurchaseChapterReq{
				UserId:       "",
				BookId:       "book456",
				ChapterOrder: 1,
				CoinAmount:   "5.00",
			},
			expectedCode: 400,
			expectedMsg:  "Parameters cannot be empty",
		},
		{
			name: "书籍ID为空",
			req: &pb.PurchaseChapterReq{
				UserId:       "user123",
				BookId:       "",
				ChapterOrder: 1,
				CoinAmount:   "5.00",
			},
			expectedCode: 400,
			expectedMsg:  "Parameters cannot be empty",
		},
		{
			name: "章节序号为0",
			req: &pb.PurchaseChapterReq{
				UserId:       "user123",
				BookId:       "book456",
				ChapterOrder: 0,
				CoinAmount:   "5.00",
			},
			expectedCode: 400,
			expectedMsg:  "Parameters cannot be empty",
		},
		{
			name: "书币数量为空",
			req: &pb.PurchaseChapterReq{
				UserId:       "user123",
				BookId:       "book456",
				ChapterOrder: 1,
				CoinAmount:   "",
			},
			expectedCode: 400,
			expectedMsg:  "Parameters cannot be empty",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := svc.PurchaseChapter(context.Background(), tt.req)

			if err != nil {
				t.Errorf("PurchaseChapter() error = %v", err)
				return
			}

			if resp.Code != tt.expectedCode {
				t.Errorf("PurchaseChapter() code = %v, want %v", resp.Code, tt.expectedCode)
			}

			if resp.Message != tt.expectedMsg {
				t.Errorf("PurchaseChapter() message = %v, want %v", resp.Message, tt.expectedMsg)
			}
		})
	}
}

// TestPurchaseMonthly 测试购买包月方法
func TestPurchaseMonthly(t *testing.T) {
	svc := &PurchaseSvc{
		conf: createTestConfig(),
		dao:  nil,
	}

	tests := []struct {
		name         string
		req          *pb.PurchaseMonthlyReq
		expectedCode int32
		expectedMsg  string
	}{
		{
			name: "用户ID为空",
			req: &pb.PurchaseMonthlyReq{
				UserId:     "",
				BookId:     "book456",
				BookName:   "测试书籍",
				CoinAmount: "30.00",
			},
			expectedCode: 400,
			expectedMsg:  "Parameters cannot be empty",
		},
		{
			name: "书籍ID为空",
			req: &pb.PurchaseMonthlyReq{
				UserId:     "user123",
				BookId:     "",
				BookName:   "测试书籍",
				CoinAmount: "30.00",
			},
			expectedCode: 400,
			expectedMsg:  "Parameters cannot be empty",
		},
		{
			name: "书籍名称为空",
			req: &pb.PurchaseMonthlyReq{
				UserId:     "user123",
				BookId:     "book456",
				BookName:   "",
				CoinAmount: "30.00",
			},
			expectedCode: 400,
			expectedMsg:  "Parameters cannot be empty",
		},
		{
			name: "书币数量为空",
			req: &pb.PurchaseMonthlyReq{
				UserId:     "user123",
				BookId:     "book456",
				BookName:   "测试书籍",
				CoinAmount: "",
			},
			expectedCode: 400,
			expectedMsg:  "Parameters cannot be empty",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := svc.PurchaseMonthly(context.Background(), tt.req)

			if err != nil {
				t.Errorf("PurchaseMonthly() error = %v", err)
				return
			}

			if resp.Code != tt.expectedCode {
				t.Errorf("PurchaseMonthly() code = %v, want %v", resp.Code, tt.expectedCode)
			}

			if resp.Message != tt.expectedMsg {
				t.Errorf("PurchaseMonthly() message = %v, want %v", resp.Message, tt.expectedMsg)
			}
		})
	}
}

// TestPurchaseVip 测试购买VIP方法
func TestPurchaseVip(t *testing.T) {
	svc := &PurchaseSvc{
		conf: createTestConfig(),
		dao:  nil,
	}

	tests := []struct {
		name         string
		req          *pb.PurchaseVipReq
		expectedCode int32
		expectedMsg  string
	}{
		{
			name: "用户ID为空",
			req: &pb.PurchaseVipReq{
				UserId:     "",
				CoinAmount: "100.00",
			},
			expectedCode: 400,
			expectedMsg:  "Parameters cannot be empty",
		},
		{
			name: "书币数量为空",
			req: &pb.PurchaseVipReq{
				UserId:     "user123",
				CoinAmount: "",
			},
			expectedCode: 400,
			expectedMsg:  "Parameters cannot be empty",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := svc.PurchaseVip(context.Background(), tt.req)

			if err != nil {
				t.Errorf("PurchaseVip() error = %v", err)
				return
			}

			if resp.Code != tt.expectedCode {
				t.Errorf("PurchaseVip() code = %v, want %v", resp.Code, tt.expectedCode)
			}

			if resp.Message != tt.expectedMsg {
				t.Errorf("PurchaseVip() message = %v, want %v", resp.Message, tt.expectedMsg)
			}
		})
	}
}

// TestGetPurchaseOrders 测试获取购买订单方法
func TestGetPurchaseOrders(t *testing.T) {
	svc := &PurchaseSvc{
		conf: createTestConfig(),
		dao:  nil,
	}

	tests := []struct {
		name         string
		req          *pb.GetPurchaseOrdersReq
		expectedCode int32
		expectedMsg  string
	}{
		{
			name: "用户ID为空",
			req: &pb.GetPurchaseOrdersReq{
				UserId: "",
			},
			expectedCode: 400,
			expectedMsg:  "User ID cannot be empty",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := svc.GetPurchaseOrders(context.Background(), tt.req)

			if err != nil {
				t.Errorf("GetPurchaseOrders() error = %v", err)
				return
			}

			if resp.Code != tt.expectedCode {
				t.Errorf("GetPurchaseOrders() code = %v, want %v", resp.Code, tt.expectedCode)
			}

			if resp.Message != tt.expectedMsg {
				t.Errorf("GetPurchaseOrders() message = %v, want %v", resp.Message, tt.expectedMsg)
			}
		})
	}
}

// TestCheckChapterPurchased 测试检查章节购买状态方法
func TestCheckChapterPurchased(t *testing.T) {
	svc := &PurchaseSvc{
		conf: createTestConfig(),
		dao:  nil,
	}

	tests := []struct {
		name         string
		req          *pb.CheckChapterPurchasedReq
		expectedCode int32
		expectedMsg  string
	}{
		{
			name: "用户ID为空",
			req: &pb.CheckChapterPurchasedReq{
				UserId:       "",
				BookId:       "book456",
				ChapterOrder: 1,
			},
			expectedCode: 400,
			expectedMsg:  "Parameters cannot be empty",
		},
		{
			name: "书籍ID为空",
			req: &pb.CheckChapterPurchasedReq{
				UserId:       "user123",
				BookId:       "",
				ChapterOrder: 1,
			},
			expectedCode: 400,
			expectedMsg:  "Parameters cannot be empty",
		},
		{
			name: "章节序号为0",
			req: &pb.CheckChapterPurchasedReq{
				UserId:       "user123",
				BookId:       "book456",
				ChapterOrder: 0,
			},
			expectedCode: 400,
			expectedMsg:  "Parameters cannot be empty",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := svc.CheckChapterPurchased(context.Background(), tt.req)

			if err != nil {
				t.Errorf("CheckChapterPurchased() error = %v", err)
				return
			}

			if resp.Code != tt.expectedCode {
				t.Errorf("CheckChapterPurchased() code = %v, want %v", resp.Code, tt.expectedCode)
			}

			if resp.Message != tt.expectedMsg {
				t.Errorf("CheckChapterPurchased() message = %v, want %v", resp.Message, tt.expectedMsg)
			}
		})
	}
}

// TestCheckMonthlyStatus 测试检查包月状态方法
func TestCheckMonthlyStatus(t *testing.T) {
	svc := &PurchaseSvc{
		conf: createTestConfig(),
		dao:  nil,
	}

	tests := []struct {
		name         string
		req          *pb.CheckMonthlyStatusReq
		expectedCode int32
		expectedMsg  string
	}{
		{
			name: "用户ID为空",
			req: &pb.CheckMonthlyStatusReq{
				UserId: "",
				BookId: "book456",
			},
			expectedCode: 400,
			expectedMsg:  "Parameters cannot be empty",
		},
		{
			name: "书籍ID为空",
			req: &pb.CheckMonthlyStatusReq{
				UserId: "user123",
				BookId: "",
			},
			expectedCode: 400,
			expectedMsg:  "Parameters cannot be empty",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := svc.CheckMonthlyStatus(context.Background(), tt.req)

			if err != nil {
				t.Errorf("CheckMonthlyStatus() error = %v", err)
				return
			}

			if resp.Code != tt.expectedCode {
				t.Errorf("CheckMonthlyStatus() code = %v, want %v", resp.Code, tt.expectedCode)
			}

			if resp.Message != tt.expectedMsg {
				t.Errorf("CheckMonthlyStatus() message = %v, want %v", resp.Message, tt.expectedMsg)
			}
		})
	}
}

// TestGetPurchasedChapters 测试获取已购买章节方法
func TestGetPurchasedChapters(t *testing.T) {
	svc := &PurchaseSvc{
		conf: createTestConfig(),
		dao:  nil,
	}

	tests := []struct {
		name         string
		req          *pb.GetPurchasedChaptersReq
		expectedCode int32
		expectedMsg  string
	}{
		{
			name: "用户ID为空",
			req: &pb.GetPurchasedChaptersReq{
				UserId: "",
				BookId: "book456",
			},
			expectedCode: 400,
			expectedMsg:  "Parameters cannot be empty",
		},
		{
			name: "书籍ID为空",
			req: &pb.GetPurchasedChaptersReq{
				UserId: "user123",
				BookId: "",
			},
			expectedCode: 400,
			expectedMsg:  "Parameters cannot be empty",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := svc.GetPurchasedChapters(context.Background(), tt.req)

			if err != nil {
				t.Errorf("GetPurchasedChapters() error = %v", err)
				return
			}

			if resp.Code != tt.expectedCode {
				t.Errorf("GetPurchasedChapters() code = %v, want %v", resp.Code, tt.expectedCode)
			}

			if resp.Message != tt.expectedMsg {
				t.Errorf("GetPurchasedChapters() message = %v, want %v", resp.Message, tt.expectedMsg)
			}
		})
	}
}

// TestDefaultValues 测试默认值处理
func TestDefaultValues(t *testing.T) {
	svc := &PurchaseSvc{
		conf: createTestConfig(),
		dao:  nil,
	}

	t.Run("PurchaseMonthly使用默认天数", func(t *testing.T) {
		req := &pb.PurchaseMonthlyReq{
			UserId:       123,               // 用户ID改为uint64类型
			BookId:       "book456",
			BookName:     "测试书籍",
			CoinAmount:   "30.00",
			DurationDays: 0, // 应该使用默认值
		}

		// 这个测试只能验证参数验证通过，因为没有真实的DAO
		resp, err := svc.PurchaseMonthly(context.Background(), req)
		if err != nil {
			t.Errorf("PurchaseMonthly() error = %v", err)
		}

		// 验证不是参数错误
		if resp.Code == 400 && resp.Message == "Parameters cannot be empty" {
			t.Error("PurchaseMonthly() should not fail parameter validation with DurationDays=0")
		}
	})

	t.Run("PurchaseVip使用默认天数", func(t *testing.T) {
		req := &pb.PurchaseVipReq{
			UserId:       "user123",
			CoinAmount:   "100.00",
			DurationDays: 0, // 应该使用默认值
		}

		resp, err := svc.PurchaseVip(context.Background(), req)
		if err != nil {
			t.Errorf("PurchaseVip() error = %v", err)
		}

		// 验证不是参数错误
		if resp.Code == 400 && resp.Message == "Parameters cannot be empty" {
			t.Error("PurchaseVip() should not fail parameter validation with DurationDays=0")
		}
	})
}

// BenchmarkPurchaseChapter 基准测试
func BenchmarkPurchaseChapter(b *testing.B) {
	svc := &PurchaseSvc{
		conf: createTestConfig(),
		dao:  nil,
	}

	req := &pb.PurchaseChapterReq{
		UserId:       "user123",
		BookId:       "book456",
		ChapterOrder: 1,
		CoinAmount:   "5.00",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		svc.PurchaseChapter(context.Background(), req)
	}
}

// BenchmarkParameterValidation 参数验证基准测试
func BenchmarkParameterValidation(b *testing.B) {
	svc := &PurchaseSvc{
		conf: createTestConfig(),
		dao:  nil,
	}

	req := &pb.PurchaseChapterReq{
		UserId:       "",
		BookId:       "book456",
		ChapterOrder: 1,
		CoinAmount:   "5.00",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		svc.PurchaseChapter(context.Background(), req)
	}
}