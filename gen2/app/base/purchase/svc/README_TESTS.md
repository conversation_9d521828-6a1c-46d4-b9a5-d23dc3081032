# Purchase Service 测试说明

## 测试文件

- `purchase_test.go` - 参数验证测试（dao=nil）
- `purchase_real_test.go` - 使用真实DAO结构的测试
- `purchase_integration_test.go` - 完整的集成测试（需要Mock）
- `run_tests.sh` - 测试运行脚本

## 测试覆盖

### 当前实现的测试

#### 1. 参数验证测试 (`purchase_test.go`)
✅ **所有方法的输入参数验证** - dao=nil，只测试参数验证逻辑
- `TestPurchaseChapter` - 购买章节参数验证
- `TestPurchaseMonthly` - 购买包月参数验证
- `TestPurchaseVip` - 购买VIP参数验证
- `TestGetPurchaseOrders` - 获取订单列表参数验证
- `TestCheckChapterPurchased` - 检查章节购买状态参数验证
- `TestCheckMonthlyStatus` - 检查包月状态参数验证
- `TestGetPurchasedChapters` - 获取已购买章节参数验证
- `TestDefaultValues` - 测试默认天数和分页参数处理
- `BenchmarkPurchaseChapter` - 购买章节性能基准
- `BenchmarkParameterValidation` - 参数验证性能基准

#### 2. 真实DAO结构测试 (`purchase_real_test.go`)
✅ **使用真实PurchaseSvc和DAO结构** - 测试实际运行情况
- `TestPurchaseChapterWithRealSvc` - 使用真实DAO的购买章节测试
- `TestPurchaseMonthlyWithRealSvc` - 使用真实DAO的购买包月测试
- `TestPurchaseVipWithRealSvc` - 使用真实DAO的购买VIP测试
- `TestGetPurchaseOrdersWithRealSvc` - 使用真实DAO的获取订单测试
- `TestCheckChapterPurchasedWithRealSvc` - 使用真实DAO的检查购买状态测试

#### 3. 集成测试 (`purchase_integration_test.go`)
✅ **完整的业务逻辑测试** - 包含Mock外部依赖
- `TestPurchaseChapterIntegration` - 完整的购买章节流程测试
- `TestPurchaseMonthlyIntegration` - 完整的购买包月流程测试
- `TestPurchaseVipIntegration` - 完整的购买VIP流程测试
- `TestCheckChapterPurchasedIntegration` - 完整的检查购买状态测试

### 测试特点

1. **专注参数验证**: 当前测试主要验证参数验证逻辑的正确性
2. **无需外部依赖**: 测试不依赖真实的数据库或外部服务
3. **快速执行**: 所有测试都能快速完成
4. **英文消息验证**: 验证错误消息使用英文

## 运行测试

### 基本运行
```bash
cd gen2/app/base/purchase/svc
go test -v
```

### 使用脚本运行
```bash
chmod +x run_tests.sh
./run_tests.sh
```

### 运行特定测试
```bash
go test -v -run TestPurchaseChapter
```

### 运行基准测试
```bash
go test -bench=.
```

## 测试示例

### 参数验证测试示例
```go
{
    name: "用户ID为空",
    req: &pb.PurchaseChapterReq{
        UserId:       "",
        BookId:       "book456",
        ChapterOrder: 1,
        CoinAmount:   "5.00",
    },
    expectedCode: 400,
    expectedMsg:  "Parameters cannot be empty",
}
```

### 默认值测试示例
```go
req := &pb.PurchaseMonthlyReq{
    UserId:       "user123",
    BookId:       "book456",
    BookName:     "测试书籍",
    CoinAmount:   "30.00",
    DurationDays: 0, // 应该使用默认值
}
```

## 测试限制

### 当前限制
- ❌ 不包含完整的业务逻辑测试（需要 Mock DAO）
- ❌ 不包含数据库操作测试
- ❌ 不包含外部服务调用测试
- ❌ 不包含错误场景的完整测试

### 扩展建议
1. **实现 Mock DAO**: 创建完整的 DAO 模拟实现
2. **业务逻辑测试**: 测试购买流程、状态检查等
3. **集成测试**: 测试与 Account 服务的交互
4. **错误处理测试**: 测试各种错误场景

## 解决类型问题

由于 `PurchaseSvc` 的 `dao` 字段是私有的且类型为具体的 `*dao.Dao`，直接注入 Mock 比较困难。当前采用的解决方案：

1. **参数验证测试**: 只测试不依赖 DAO 的参数验证逻辑
2. **设置 dao 为 nil**: 对于参数验证测试，DAO 为 nil 不影响测试

### 未来改进方案
1. **接口化 DAO**: 将 DAO 定义为接口
2. **依赖注入**: 通过构造函数注入 DAO 接口
3. **测试专用构造函数**: 创建专门用于测试的构造函数

## 测试结果示例

```
=== RUN   TestPurchaseChapter
=== RUN   TestPurchaseChapter/用户ID为空
=== RUN   TestPurchaseChapter/书籍ID为空
=== RUN   TestPurchaseChapter/章节序号为0
=== RUN   TestPurchaseChapter/书币数量为空
--- PASS: TestPurchaseChapter (0.00s)
    --- PASS: TestPurchaseChapter/用户ID为空 (0.00s)
    --- PASS: TestPurchaseChapter/书籍ID为空 (0.00s)
    --- PASS: TestPurchaseChapter/章节序号为0 (0.00s)
    --- PASS: TestPurchaseChapter/书币数量为空 (0.00s)
```

## 贡献指南

### 添加新测试
1. 在 `purchase_test.go` 中添加新的测试函数
2. 遵循现有的测试命名规范
3. 使用表驱动测试模式
4. 更新此 README 文档

### 测试命名规范
- 测试函数: `TestMethodName`
- 测试用例: 描述性的中文名称
- 基准测试: `BenchmarkMethodName`

这个测试套件为 Purchase Service 提供了基础的测试覆盖，确保了参数验证逻辑的正确性。随着项目的发展，可以逐步扩展为更完整的测试套件。
