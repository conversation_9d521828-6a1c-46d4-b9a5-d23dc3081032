package svc

import (
	"context"
	"testing"

	"creativematrix.com/beyondreading/gen2/app/base/purchase/conf"
	"creativematrix.com/beyondreading/gen2/app/base/purchase/dao"
	pb "creativematrix.com/beyondreading/gen2/proto/purchase"
)

// createRealPurchaseSvc 创建真实的PurchaseSvc用于测试
func createRealPurchaseSvc() *PurchaseSvc {
	// 创建测试配置
	conf := &conf.Config{
		Purchase: struct {
			ChapterPrice    string `toml:"chapter_price"`
			MonthlyPrice    string `toml:"monthly_price"`
			VipPrice        string `toml:"vip_price"`
			MonthlyDays     int    `toml:"monthly_days"`
			VipDays         int    `toml:"vip_days"`
			CacheExpire     int    `toml:"cache_expire"`
			EnableCache     bool   `toml:"enable_cache"`
		}{
			ChapterPrice: "5.00",
			MonthlyPrice: "30.00",
			VipPrice:     "100.00",
			MonthlyDays:  30,
			VipDays:      30,
			CacheExpire:  3600,
			EnableCache:  false, // 测试时禁用缓存
		},
	}

	// 创建DAO - 注意：这里会使用真实的DAO但没有真实的数据库连接
	// 在实际测试中，这些方法会因为没有数据库连接而失败
	// 但我们可以测试参数验证和业务逻辑
	dao := &dao.Dao{}

	return &PurchaseSvc{
		conf: conf,
		dao:  dao,
	}
}

// TestPurchaseChapterWithRealSvc 使用真实PurchaseSvc测试购买章节
func TestPurchaseChapterWithRealSvc(t *testing.T) {
	svc := createRealPurchaseSvc()

	tests := []struct {
		name         string
		req          *pb.PurchaseChapterReq
		expectedCode int32
		expectedMsg  string
	}{
		{
			name: "参数验证 - 用户ID为空",
			req: &pb.PurchaseChapterReq{
				UserId:       "",
				BookId:       "book456",
				ChapterOrder: 1,
				CoinAmount:   "5.00",
			},
			expectedCode: 400,
			expectedMsg:  "Parameters cannot be empty",
		},
		{
			name: "参数验证 - 书籍ID为空",
			req: &pb.PurchaseChapterReq{
				UserId:       "user123",
				BookId:       "",
				ChapterOrder: 1,
				CoinAmount:   "5.00",
			},
			expectedCode: 400,
			expectedMsg:  "Parameters cannot be empty",
		},
		{
			name: "参数验证 - 章节序号为0",
			req: &pb.PurchaseChapterReq{
				UserId:       "user123",
				BookId:       "book456",
				ChapterOrder: 0,
				CoinAmount:   "5.00",
			},
			expectedCode: 400,
			expectedMsg:  "Parameters cannot be empty",
		},
		{
			name: "参数验证 - 书币数量为空",
			req: &pb.PurchaseChapterReq{
				UserId:       "user123",
				BookId:       "book456",
				ChapterOrder: 1,
				CoinAmount:   "",
			},
			expectedCode: 400,
			expectedMsg:  "Parameters cannot be empty",
		},
		{
			name: "参数验证通过 - 会因为没有数据库连接而失败",
			req: &pb.PurchaseChapterReq{
				UserId:       "user123",
				BookId:       "book456",
				ChapterOrder: 1,
				CoinAmount:   "5.00",
			},
			expectedCode: 500, // 预期会因为数据库连接失败而返回500
			expectedMsg:  "Failed to purchase chapter",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := svc.PurchaseChapter(context.Background(), tt.req)

			if err != nil {
				t.Errorf("PurchaseChapter() error = %v", err)
				return
			}

			if resp.Code != tt.expectedCode {
				t.Errorf("PurchaseChapter() code = %v, want %v", resp.Code, tt.expectedCode)
			}

			if resp.Message != tt.expectedMsg {
				t.Errorf("PurchaseChapter() message = %v, want %v", resp.Message, tt.expectedMsg)
			}
		})
	}
}

// TestPurchaseMonthlyWithRealSvc 使用真实PurchaseSvc测试购买包月
func TestPurchaseMonthlyWithRealSvc(t *testing.T) {
	svc := createRealPurchaseSvc()

	tests := []struct {
		name         string
		req          *pb.PurchaseMonthlyReq
		expectedCode int32
		expectedMsg  string
	}{
		{
			name: "参数验证 - 用户ID为空",
			req: &pb.PurchaseMonthlyReq{
				UserId:     "",
				BookId:     "book456",
				BookName:   "测试书籍",
				CoinAmount: "30.00",
			},
			expectedCode: 400,
			expectedMsg:  "Parameters cannot be empty",
		},
		{
			name: "参数验证 - 书籍ID为空",
			req: &pb.PurchaseMonthlyReq{
				UserId:     "user123",
				BookId:     "",
				BookName:   "测试书籍",
				CoinAmount: "30.00",
			},
			expectedCode: 400,
			expectedMsg:  "Parameters cannot be empty",
		},
		{
			name: "参数验证 - 书籍名称为空",
			req: &pb.PurchaseMonthlyReq{
				UserId:     "user123",
				BookId:     "book456",
				BookName:   "",
				CoinAmount: "30.00",
			},
			expectedCode: 400,
			expectedMsg:  "Parameters cannot be empty",
		},
		{
			name: "参数验证 - 书币数量为空",
			req: &pb.PurchaseMonthlyReq{
				UserId:     "user123",
				BookId:     "book456",
				BookName:   "测试书籍",
				CoinAmount: "",
			},
			expectedCode: 400,
			expectedMsg:  "Parameters cannot be empty",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := svc.PurchaseMonthly(context.Background(), tt.req)

			if err != nil {
				t.Errorf("PurchaseMonthly() error = %v", err)
				return
			}

			if resp.Code != tt.expectedCode {
				t.Errorf("PurchaseMonthly() code = %v, want %v", resp.Code, tt.expectedCode)
			}

			if resp.Message != tt.expectedMsg {
				t.Errorf("PurchaseMonthly() message = %v, want %v", resp.Message, tt.expectedMsg)
			}
		})
	}
}

// TestPurchaseVipWithRealSvc 使用真实PurchaseSvc测试购买VIP
func TestPurchaseVipWithRealSvc(t *testing.T) {
	svc := createRealPurchaseSvc()

	tests := []struct {
		name         string
		req          *pb.PurchaseVipReq
		expectedCode int32
		expectedMsg  string
	}{
		{
			name: "参数验证 - 用户ID为空",
			req: &pb.PurchaseVipReq{
				UserId:     "",
				CoinAmount: "100.00",
			},
			expectedCode: 400,
			expectedMsg:  "Parameters cannot be empty",
		},
		{
			name: "参数验证 - 书币数量为空",
			req: &pb.PurchaseVipReq{
				UserId:     "user123",
				CoinAmount: "",
			},
			expectedCode: 400,
			expectedMsg:  "Parameters cannot be empty",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := svc.PurchaseVip(context.Background(), tt.req)

			if err != nil {
				t.Errorf("PurchaseVip() error = %v", err)
				return
			}

			if resp.Code != tt.expectedCode {
				t.Errorf("PurchaseVip() code = %v, want %v", resp.Code, tt.expectedCode)
			}

			if resp.Message != tt.expectedMsg {
				t.Errorf("PurchaseVip() message = %v, want %v", resp.Message, tt.expectedMsg)
			}
		})
	}
}

// TestGetPurchaseOrdersWithRealSvc 使用真实PurchaseSvc测试获取购买订单
func TestGetPurchaseOrdersWithRealSvc(t *testing.T) {
	svc := createRealPurchaseSvc()

	tests := []struct {
		name         string
		req          *pb.GetPurchaseOrdersReq
		expectedCode int32
		expectedMsg  string
	}{
		{
			name: "参数验证 - 用户ID为空",
			req: &pb.GetPurchaseOrdersReq{
				UserId: "",
			},
			expectedCode: 400,
			expectedMsg:  "User ID cannot be empty",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := svc.GetPurchaseOrders(context.Background(), tt.req)

			if err != nil {
				t.Errorf("GetPurchaseOrders() error = %v", err)
				return
			}

			if resp.Code != tt.expectedCode {
				t.Errorf("GetPurchaseOrders() code = %v, want %v", resp.Code, tt.expectedCode)
			}

			if resp.Message != tt.expectedMsg {
				t.Errorf("GetPurchaseOrders() message = %v, want %v", resp.Message, tt.expectedMsg)
			}
		})
	}
}

// TestCheckChapterPurchasedWithRealSvc 使用真实PurchaseSvc测试检查章节购买状态
func TestCheckChapterPurchasedWithRealSvc(t *testing.T) {
	svc := createRealPurchaseSvc()

	tests := []struct {
		name         string
		req          *pb.CheckChapterPurchasedReq
		expectedCode int32
		expectedMsg  string
	}{
		{
			name: "参数验证 - 用户ID为空",
			req: &pb.CheckChapterPurchasedReq{
				UserId:       "",
				BookId:       "book456",
				ChapterOrder: 1,
			},
			expectedCode: 400,
			expectedMsg:  "Parameters cannot be empty",
		},
		{
			name: "参数验证 - 书籍ID为空",
			req: &pb.CheckChapterPurchasedReq{
				UserId:       "user123",
				BookId:       "",
				ChapterOrder: 1,
			},
			expectedCode: 400,
			expectedMsg:  "Parameters cannot be empty",
		},
		{
			name: "参数验证 - 章节序号为0",
			req: &pb.CheckChapterPurchasedReq{
				UserId:       "user123",
				BookId:       "book456",
				ChapterOrder: 0,
			},
			expectedCode: 400,
			expectedMsg:  "Parameters cannot be empty",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := svc.CheckChapterPurchased(context.Background(), tt.req)

			if err != nil {
				t.Errorf("CheckChapterPurchased() error = %v", err)
				return
			}

			if resp.Code != tt.expectedCode {
				t.Errorf("CheckChapterPurchased() code = %v, want %v", resp.Code, tt.expectedCode)
			}

			if resp.Message != tt.expectedMsg {
				t.Errorf("CheckChapterPurchased() message = %v, want %v", resp.Message, tt.expectedMsg)
			}
		})
	}
}
