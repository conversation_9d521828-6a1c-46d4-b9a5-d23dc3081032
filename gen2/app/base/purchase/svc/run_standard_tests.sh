#!/bin/bash

# Purchase Service 标准测试运行脚本
# 按照books模块的配置加载方式运行测试

echo "🧪 运行 Purchase Service 标准测试..."

# 设置测试环境
export GO111MODULE=on

# 检查配置文件 - 按照项目规范，配置文件在 gen2/config/ 目录下
CONFIG_FILE="../../../../config/base-purchase.toml"
if [ ! -f "$CONFIG_FILE" ]; then
    echo "❌ 错误: 配置文件不存在: $CONFIG_FILE"
    echo "请确保配置文件存在于 gen2/config/base-purchase.toml"
    exit 1
fi

echo "✅ 找到配置文件: $CONFIG_FILE"

# 检查测试文件
if [ ! -f "purchase_standard_test.go" ]; then
    echo "❌ 错误: 标准测试文件不存在"
    exit 1
fi

echo "✅ 找到标准测试文件: purchase_standard_test.go"

# 显示测试环境信息
echo ""
echo "📋 测试环境信息:"
echo "当前目录: $(pwd)"
echo "Go版本: $(go version)"
echo "配置文件: $CONFIG_FILE"
echo "应用名称: base-purchase (来自 api.App)"

# 检查依赖服务状态
echo ""
echo "⚠️  注意事项:"
echo "1. 测试使用与main.go完全相同的初始化流程"
echo "2. 配置文件按照项目规范从 gen2/config/base-purchase.toml 加载"
echo "3. 测试会尝试连接真实的数据库和外部服务"
echo "4. 参数验证测试应该都能通过"
echo "5. 业务逻辑测试可能因为外部依赖而失败"

# 询问是否继续
read -p "是否继续运行标准测试？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "测试已取消"
    exit 0
fi

echo ""
echo "🚀 开始运行标准测试..."

# 运行标准测试
echo "📋 运行Purchase服务标准测试..."
echo "使用配置: $CONFIG_FILE"
echo "初始化方式: config := conf.Load(api.App)"
echo ""

go test -v -run "Test.*Standard" -timeout 60s

# 运行短模式测试（跳过需要外部依赖的测试）
echo ""
echo "📋 运行短模式测试（跳过外部依赖）..."
go test -v -short -run "Test.*Standard" -timeout 30s

# 生成测试覆盖率报告
echo ""
echo "📈 生成测试覆盖率报告..."
go test -coverprofile=standard_coverage.out -run "Test.*Standard" -timeout 60s
if [ -f "standard_coverage.out" ]; then
    go tool cover -html=standard_coverage.out -o standard_coverage.html
    echo "📄 标准测试覆盖率报告已生成: standard_coverage.html"
    
    # 显示覆盖率统计
    echo "📊 覆盖率统计:"
    go tool cover -func=standard_coverage.out | tail -1
else
    echo "❌ 覆盖率报告生成失败"
fi

echo ""
echo "✅ 标准测试完成！"

# 显示测试统计
echo ""
echo "📊 测试统计:"
if [ -f "purchase_standard_test.go" ]; then
    echo "标准测试数量: $(grep -c "func Test.*Standard" purchase_standard_test.go)"
    echo "测试文件: purchase_standard_test.go"
else
    echo "未找到标准测试文件"
fi

echo ""
echo "🔍 测试覆盖的方法:"
echo "- PurchaseChapter (标准流程)"
echo "- PurchaseMonthly (标准流程)"
echo "- PurchaseVip (标准流程)"
echo "- GetPurchaseOrders (标准流程)"
echo "- CheckChapterPurchased (标准流程)"

echo ""
echo "💡 测试说明:"
echo "- 使用与main.go完全相同的初始化流程"
echo "- 配置加载: config := conf.Load(api.App)"
echo "- 日志初始化: logger.InitLog(api.App, config.Log.Level)"
echo "- 链路追踪: tracer.InitTracing(config.Base, api.App)"
echo "- 服务创建: service := svc.Load(config)"
echo "- 配置文件: gen2/config/base-purchase.toml"

echo ""
echo "🛠️ 故障排除:"
echo "- 如果配置加载失败，检查 gen2/config/base-purchase.toml 是否存在"
echo "- 如果测试失败，检查数据库连接配置"
echo "- 如果gRPC调用失败，检查Account服务是否启动"
echo "- 查看测试日志了解具体错误信息"

echo ""
echo "📝 下一步:"
echo "- 查看测试日志分析失败原因"
echo "- 检查标准测试覆盖率报告: standard_coverage.html"
echo "- 根据需要调整测试数据和配置"
echo "- 对比与books模块的配置加载方式"
