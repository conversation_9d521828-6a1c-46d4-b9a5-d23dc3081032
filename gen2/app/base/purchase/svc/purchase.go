package svc

import (
	"context"
	"creativematrix.com/beyondreading/gen2/app/common/po"
	pb "creativematrix.com/beyondreading/gen2/proto/purchase"
	"creativematrix.com/beyondreading/pkg/logger"
)

// PurchaseChapter 购买章节
func (s *PurchaseSvc) PurchaseChapter(ctx context.Context, req *pb.PurchaseChapterReq) (*pb.PurchaseChapterResp, error) {
	if req.UserId == "" || req.BookId == "" || req.ChapterOrder == 0 || req.CoinAmount == "" {
		return &pb.PurchaseChapterResp{
			Code:    400,
			Message: "Parameters cannot be empty",
		}, nil
	}

	order, err := s.dao.PurchaseChapter(ctx, req.UserId, req.BookId, req.ChapterOrder, req.CoinAmount)
	if err != nil {
		logger.LogErrorf("Failed to purchase chapter: %v", err)
		if err.Error() == "chapter already purchased" {
			return &pb.PurchaseChapterResp{
				Code:    409,
				Message: "Chapter already purchased",
			}, nil
		}
		return &pb.PurchaseChapterResp{
			Code:    500,
			Message: "Failed to purchase chapter",
		}, nil
	}

	return &pb.PurchaseChapterResp{
		Code:    200,
		Message: "Chapter purchased successfully",
		Order:   s.convertPurchaseOrderToProto(order),
	}, nil
}

// PurchaseMonthly 购买包月
func (s *PurchaseSvc) PurchaseMonthly(ctx context.Context, req *pb.PurchaseMonthlyReq) (*pb.PurchaseMonthlyResp, error) {
	if req.UserId == "" || req.BookId == "" || req.BookName == "" || req.CoinAmount == "" {
		return &pb.PurchaseMonthlyResp{
			Code:    400,
			Message: "Parameters cannot be empty",
		}, nil
	}

	durationDays := req.DurationDays
	if durationDays <= 0 {
		durationDays = int32(s.conf.Purchase.MonthlyDays) // 默认30天
	}

	order, err := s.dao.PurchaseMonthly(ctx, req.UserId, req.BookId, req.BookName, req.CoinAmount, int(durationDays))
	if err != nil {
		logger.LogErrorf("Failed to purchase monthly: %v", err)
		if err.Error() == "monthly subscription already active" {
			return &pb.PurchaseMonthlyResp{
				Code:    409,
				Message: "Monthly subscription already active",
			}, nil
		}
		return &pb.PurchaseMonthlyResp{
			Code:    500,
			Message: "Failed to purchase monthly subscription",
		}, nil
	}

	return &pb.PurchaseMonthlyResp{
		Code:    200,
		Message: "Monthly subscription purchased successfully",
		Order:   s.convertPurchaseOrderToProto(order),
	}, nil
}

// PurchaseVip 购买VIP
func (s *PurchaseSvc) PurchaseVip(ctx context.Context, req *pb.PurchaseVipReq) (*pb.PurchaseVipResp, error) {
	if req.UserId == "" || req.CoinAmount == "" {
		return &pb.PurchaseVipResp{
			Code:    400,
			Message: "Parameters cannot be empty",
		}, nil
	}

	durationDays := req.DurationDays
	if durationDays <= 0 {
		durationDays = int32(s.conf.Purchase.VipDays) // 默认30天
	}

	order, err := s.dao.PurchaseVip(ctx, req.UserId, req.CoinAmount, int(durationDays))
	if err != nil {
		logger.LogErrorf("Failed to purchase VIP: %v", err)
		return &pb.PurchaseVipResp{
			Code:    500,
			Message: "Failed to purchase VIP",
		}, nil
	}

	return &pb.PurchaseVipResp{
		Code:    200,
		Message: "VIP purchased successfully",
		Order:   s.convertPurchaseOrderToProto(order),
	}, nil
}

// GetPurchaseOrders 获取购买订单列表
func (s *PurchaseSvc) GetPurchaseOrders(ctx context.Context, req *pb.GetPurchaseOrdersReq) (*pb.GetPurchaseOrdersResp, error) {
	if req.UserId == "" {
		return &pb.GetPurchaseOrdersResp{
			Code:    400,
			Message: "User ID cannot be empty",
		}, nil
	}

	// 设置默认分页参数
	page := req.Page
	if page <= 0 {
		page = 1
	}

	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 100 {
		pageSize = 100
	}

	orders, total, err := s.dao.GetPurchaseOrders(ctx, req.UserId, int(page), int(pageSize), req.OrderType)
	if err != nil {
		logger.LogErrorf("Failed to get purchase orders for user %s: %v", req.UserId, err)
		return &pb.GetPurchaseOrdersResp{
			Code:    500,
			Message: "Failed to get purchase orders",
		}, nil
	}

	// 转换为Proto格式
	protoOrders := make([]*pb.PurchaseOrder, 0, len(orders))
	for _, order := range orders {
		protoOrders = append(protoOrders, s.convertPurchaseOrderToProto(order))
	}

	return &pb.GetPurchaseOrdersResp{
		Code:    200,
		Message: "success",
		Orders:  protoOrders,
		Total:   total,
	}, nil
}

// CheckChapterPurchased 检查章节购买状态
func (s *PurchaseSvc) CheckChapterPurchased(ctx context.Context, req *pb.CheckChapterPurchasedReq) (*pb.CheckChapterPurchasedResp, error) {
	if req.UserId == "" || req.BookId == "" || req.ChapterOrder == 0 {
		return &pb.CheckChapterPurchasedResp{
			Code:    400,
			Message: "Parameters cannot be empty",
		}, nil
	}

	purchased, err := s.dao.CheckChapterPurchased(ctx, req.UserId, req.BookId, req.ChapterOrder)
	if err != nil {
		logger.LogErrorf("Failed to check chapter purchased: %v", err)
		return &pb.CheckChapterPurchasedResp{
			Code:    500,
			Message: "Failed to check chapter purchase status",
		}, nil
	}

	// 检查是否通过包月获得
	isMonthly, err := s.dao.CheckMonthlyStatus(ctx, req.UserId, req.BookId)
	if err != nil {
		logger.LogErrorf("Failed to check monthly status: %v", err)
		isMonthly = false
	}

	return &pb.CheckChapterPurchasedResp{
		Code:        200,
		Message:     "success",
		IsPurchased: purchased,
		IsMonthly:   isMonthly,
	}, nil
}

// CheckMonthlyStatus 检查包月状态
func (s *PurchaseSvc) CheckMonthlyStatus(ctx context.Context, req *pb.CheckMonthlyStatusReq) (*pb.CheckMonthlyStatusResp, error) {
	if req.UserId == "" || req.BookId == "" {
		return &pb.CheckMonthlyStatusResp{
			Code:    400,
			Message: "Parameters cannot be empty",
		}, nil
	}

	isActive, err := s.dao.CheckMonthlyStatus(ctx, req.UserId, req.BookId)
	if err != nil {
		logger.LogErrorf("Failed to check monthly status: %v", err)
		return &pb.CheckMonthlyStatusResp{
			Code:    500,
			Message: "Failed to check monthly status",
		}, nil
	}

	return &pb.CheckMonthlyStatusResp{
		Code:     200,
		Message:  "success",
		IsActive: isActive,
	}, nil
}

// GetPurchasedChapters 获取已购买的章节列表
func (s *PurchaseSvc) GetPurchasedChapters(ctx context.Context, req *pb.GetPurchasedChaptersReq) (*pb.GetPurchasedChaptersResp, error) {
	if req.UserId == "" || req.BookId == "" {
		return &pb.GetPurchasedChaptersResp{
			Code:    400,
			Message: "Parameters cannot be empty",
		}, nil
	}

	// 设置默认分页参数
	page := req.Page
	if page <= 0 {
		page = 1
	}

	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 100 {
		pageSize = 100
	}

	chapters, total, err := s.dao.GetPurchasedChapters(ctx, req.UserId, req.BookId, int(page), int(pageSize))
	if err != nil {
		logger.LogErrorf("Failed to get purchased chapters: %v", err)
		return &pb.GetPurchasedChaptersResp{
			Code:    500,
			Message: "Failed to get purchased chapters",
		}, nil
	}

	// 转换为Proto格式
	protoChapters := make([]*pb.ChapterPurchaseInfo, 0, len(chapters))
	for _, chapter := range chapters {
		if chapter.ChapterOrder != nil {
			protoChapters = append(protoChapters, &pb.ChapterPurchaseInfo{
				ChapterId:    getStringValue(chapter.ChapterId),
				ChapterOrder: *chapter.ChapterOrder,
				OrderId:      chapter.OrderId,
				PurchasedAt:  chapter.CreatedAt.Unix(),
				IsMonthly:    false, // 这里是直接购买的章节
			})
		}
	}

	return &pb.GetPurchasedChaptersResp{
		Code:     200,
		Message:  "success",
		Chapters: protoChapters,
		Total:    total,
	}, nil
}

// 辅助方法
func (s *PurchaseSvc) convertPurchaseOrderToProto(order *po.PurchaseOrder) *pb.PurchaseOrder {
	proto := &pb.PurchaseOrder{
		OrderId:    order.OrderId,
		AccountId:  order.AccountId,
		UserId:     order.UserId,
		OrderType:  order.OrderType,
		CoinAmount: order.CoinAmount,
		Status:     int32(order.Status),
		CreatedAt:  order.CreatedAt.Unix(),
		UpdatedAt:  order.UpdatedAt.Unix(),
	}

	if order.BookId != nil {
		proto.BookId = *order.BookId
	}
	if order.BookName != nil {
		proto.BookName = *order.BookName
	}
	if order.ChapterId != nil {
		proto.ChapterId = *order.ChapterId
	}
	if order.ChapterTitle != nil {
		proto.ChapterTitle = *order.ChapterTitle
	}
	if order.ChapterOrder != nil {
		proto.ChapterOrder = *order.ChapterOrder
	}
	if order.DurationDays != nil {
		proto.DurationDays = int32(*order.DurationDays)
	}
	if order.StartTime != nil {
		proto.StartTime = order.StartTime.Unix()
	}
	if order.EndTime != nil {
		proto.EndTime = order.EndTime.Unix()
	}

	return proto
}

func getStringValue(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}
