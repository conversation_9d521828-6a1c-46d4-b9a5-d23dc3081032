#!/bin/bash

# Purchase Service 生产级测试运行脚本

echo "🧪 运行 Purchase Service 生产级测试..."

# 设置测试环境
export GO111MODULE=on

# 检查配置文件
CONFIG_FILE="../cmd/config.toml"
if [ ! -f "$CONFIG_FILE" ]; then
    echo "❌ 错误: 配置文件不存在: $CONFIG_FILE"
    echo "请确保配置文件存在于 gen2/app/base/purchase/cmd/config.toml"
    exit 1
fi

echo "✅ 找到配置文件: $CONFIG_FILE"

# 检查测试文件
if [ ! -f "purchase_production_test.go" ]; then
    echo "❌ 错误: 生产级测试文件不存在"
    exit 1
fi

echo "✅ 找到生产级测试文件: purchase_production_test.go"

# 显示测试环境信息
echo ""
echo "📋 测试环境信息:"
echo "当前目录: $(pwd)"
echo "Go版本: $(go version)"
echo "配置文件: $CONFIG_FILE"

# 检查数据库连接（可选）
echo ""
echo "⚠️  注意事项:"
echo "1. 确保测试数据库已启动并可连接"
echo "2. 确保Redis服务已启动（如果启用缓存）"
echo "3. 确保Account服务已启动（用于gRPC调用）"
echo "4. 测试会尝试连接真实的数据库和外部服务"

# 询问是否继续
read -p "是否继续运行生产级测试？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "测试已取消"
    exit 0
fi

echo ""
echo "🚀 开始运行生产级测试..."

# 运行生产级测试
echo "📋 运行参数验证测试..."
go test -v -run "TestPurchase.*Production" -timeout 60s

# 运行短模式测试（跳过需要外部依赖的测试）
echo ""
echo "📋 运行短模式测试（跳过外部依赖）..."
go test -v -short -run "TestPurchase.*Production" -timeout 30s

# 生成测试覆盖率报告
echo ""
echo "📈 生成测试覆盖率报告..."
go test -coverprofile=production_coverage.out -run "TestPurchase.*Production" -timeout 60s
if [ -f "production_coverage.out" ]; then
    go tool cover -html=production_coverage.out -o production_coverage.html
    echo "📄 生产级测试覆盖率报告已生成: production_coverage.html"
    
    # 显示覆盖率统计
    echo "📊 覆盖率统计:"
    go tool cover -func=production_coverage.out | tail -1
else
    echo "❌ 覆盖率报告生成失败"
fi

echo ""
echo "✅ 生产级测试完成！"

# 显示测试统计
echo ""
echo "📊 测试统计:"
if [ -f "purchase_production_test.go" ]; then
    echo "生产级测试数量: $(grep -c "func Test.*Production" purchase_production_test.go)"
    echo "测试文件: purchase_production_test.go"
else
    echo "未找到生产级测试文件"
fi

echo ""
echo "🔍 测试覆盖的方法:"
echo "- PurchaseChapter (完整流程)"
echo "- PurchaseMonthly (完整流程)"
echo "- PurchaseVip (完整流程)"
echo "- GetPurchaseOrders (完整流程)"
echo "- CheckChapterPurchased (完整流程)"

echo ""
echo "💡 测试说明:"
echo "- 使用与main方法相同的初始化流程"
echo "- 加载真实的配置文件"
echo "- 连接真实的数据库和外部服务"
echo "- 测试数据会写入数据库"
echo "- 参数验证测试应该都能通过"
echo "- 业务逻辑测试可能因为外部依赖而失败"

echo ""
echo "🛠️ 故障排除:"
echo "- 如果测试失败，检查数据库连接配置"
echo "- 如果gRPC调用失败，检查Account服务是否启动"
echo "- 如果Redis连接失败，检查Redis服务状态"
echo "- 查看测试日志了解具体错误信息"

echo ""
echo "📝 下一步:"
echo "- 查看测试日志分析失败原因"
echo "- 检查生产级测试覆盖率报告"
echo "- 根据需要调整测试数据和配置"
