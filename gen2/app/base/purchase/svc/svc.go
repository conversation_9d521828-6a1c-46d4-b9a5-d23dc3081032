package svc

import (
	"context"

	"creativematrix.com/beyondreading/gen2/app/base/purchase/conf"
	"creativematrix.com/beyondreading/gen2/app/base/purchase/dao"
)

type PurchaseSvc struct {
	conf *conf.Config
	dao  *dao.Dao
}

func Load(c *conf.Config) *PurchaseSvc {
	svc := &PurchaseSvc{
		conf: c,
		dao:  dao.Load(c),
	}

	return svc
}

func (s *PurchaseSvc) Ping(ctx context.Context) error {
	return s.dao.Ping(ctx)
}

func (s *PurchaseSvc) Close() {
	s.dao.Close()
}
