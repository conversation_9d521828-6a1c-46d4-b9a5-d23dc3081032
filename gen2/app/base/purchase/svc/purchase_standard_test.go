package svc

import (
	"context"
	"testing"
	"time"

	"creativematrix.com/beyondreading/gen2/app/base/purchase/api"
	"creativematrix.com/beyondreading/gen2/app/base/purchase/conf"
	pb "creativematrix.com/beyondreading/gen2/proto/purchase"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/tracer"
)

const (
	// 测试用户和数据
	testUserId = "test_user_123"
	testBookId = "test_book_456"
	testBookName = "测试书籍"
	testChapterOrder = uint32(1)
	testCoinAmount = "5.00"
	testMonthlyCoinAmount = "30.00"
	testVipCoinAmount = "100.00"
)

// setupStandardTestService 按照标准方式创建测试服务
func setupStandardTestService(t *testing.T) *PurchaseSvc {
	// 按照main.go的方式加载配置
	config := conf.Load(api.App)

	// 初始化日志
	logger.InitLog(api.App, config.Log.Level)

	// 初始化链路追踪
	tracer.InitTracing(config.Base, api.App)

	// 创建服务实例
	service := Load(config)
	if service == nil {
		t.Fatal("Failed to create service")
	}

	return service
}

// cleanupStandardTestData 清理测试数据
func cleanupStandardTestData(t *testing.T, service *PurchaseSvc) {
	// 这里可以添加清理测试数据的逻辑
	t.Log("Cleaning up test data...")
}

// TestPurchaseChapterStandard 标准购买章节测试
func TestPurchaseChapterStandard(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping standard test in short mode")
	}

	service := setupStandardTestService(t)
	defer cleanupStandardTestData(t, service)

	ctx := context.Background()

	tests := []struct {
		name         string
		req          *pb.PurchaseChapterReq
		expectedCode int32
		checkOrder   bool
	}{
		{
			name: "参数验证 - 用户ID为空",
			req: &pb.PurchaseChapterReq{
				UserId:       "",
				BookId:       testBookId,
				ChapterOrder: testChapterOrder,
				CoinAmount:   testCoinAmount,
			},
			expectedCode: 400,
			checkOrder:   false,
		},
		{
			name: "参数验证 - 书籍ID为空",
			req: &pb.PurchaseChapterReq{
				UserId:       testUserId,
				BookId:       "",
				ChapterOrder: testChapterOrder,
				CoinAmount:   testCoinAmount,
			},
			expectedCode: 400,
			checkOrder:   false,
		},
		{
			name: "参数验证 - 章节序号为0",
			req: &pb.PurchaseChapterReq{
				UserId:       testUserId,
				BookId:       testBookId,
				ChapterOrder: 0,
				CoinAmount:   testCoinAmount,
			},
			expectedCode: 400,
			checkOrder:   false,
		},
		{
			name: "参数验证 - 书币数量为空",
			req: &pb.PurchaseChapterReq{
				UserId:       testUserId,
				BookId:       testBookId,
				ChapterOrder: testChapterOrder,
				CoinAmount:   "",
			},
			expectedCode: 400,
			checkOrder:   false,
		},
		{
			name: "正常购买章节 - 实际调用数据库",
			req: &pb.PurchaseChapterReq{
				UserId:       testUserId,
				BookId:       testBookId,
				ChapterOrder: testChapterOrder,
				CoinAmount:   testCoinAmount,
			},
			expectedCode: 0, // 不预期特定的错误码，因为可能是数据库连接问题或业务逻辑问题
			checkOrder:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := service.PurchaseChapter(ctx, tt.req)

			if err != nil {
				t.Errorf("PurchaseChapter() error = %v", err)
				return
			}

			if tt.expectedCode != 0 && resp.Code != tt.expectedCode {
				t.Errorf("PurchaseChapter() code = %v, want %v", resp.Code, tt.expectedCode)
			}

			// 记录响应信息用于调试
			t.Logf("Response: Code=%d, Message=%s", resp.Code, resp.Message)

			if tt.checkOrder && resp.Order != nil {
				t.Logf("Order created: ID=%s, Type=%s", resp.Order.OrderId, resp.Order.OrderType)
			}
		})
	}
}

// TestPurchaseMonthlyStandard 标准购买包月测试
func TestPurchaseMonthlyStandard(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping standard test in short mode")
	}

	service := setupStandardTestService(t)
	defer cleanupStandardTestData(t, service)

	ctx := context.Background()

	tests := []struct {
		name         string
		req          *pb.PurchaseMonthlyReq
		expectedCode int32
	}{
		{
			name: "参数验证 - 用户ID为空",
			req: &pb.PurchaseMonthlyReq{
				UserId:     "",
				BookId:     testBookId,
				BookName:   testBookName,
				CoinAmount: testMonthlyCoinAmount,
			},
			expectedCode: 400,
		},
		{
			name: "参数验证 - 书籍ID为空",
			req: &pb.PurchaseMonthlyReq{
				UserId:     testUserId,
				BookId:     "",
				BookName:   testBookName,
				CoinAmount: testMonthlyCoinAmount,
			},
			expectedCode: 400,
		},
		{
			name: "参数验证 - 书籍名称为空",
			req: &pb.PurchaseMonthlyReq{
				UserId:     testUserId,
				BookId:     testBookId,
				BookName:   "",
				CoinAmount: testMonthlyCoinAmount,
			},
			expectedCode: 400,
		},
		{
			name: "参数验证 - 书币数量为空",
			req: &pb.PurchaseMonthlyReq{
				UserId:     testUserId,
				BookId:     testBookId,
				BookName:   testBookName,
				CoinAmount: "",
			},
			expectedCode: 400,
		},
		{
			name: "默认天数测试 - 使用配置中的默认值",
			req: &pb.PurchaseMonthlyReq{
				UserId:       testUserId,
				BookId:       testBookId,
				BookName:     testBookName,
				CoinAmount:   testMonthlyCoinAmount,
				DurationDays: 0, // 应该使用配置中的默认值30天
			},
			expectedCode: 0, // 不预期特定错误码
		},
		{
			name: "正常购买包月 - 实际调用数据库",
			req: &pb.PurchaseMonthlyReq{
				UserId:       testUserId,
				BookId:       testBookId,
				BookName:     testBookName,
				CoinAmount:   testMonthlyCoinAmount,
				DurationDays: 30,
			},
			expectedCode: 0, // 不预期特定错误码
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := service.PurchaseMonthly(ctx, tt.req)

			if err != nil {
				t.Errorf("PurchaseMonthly() error = %v", err)
				return
			}

			if tt.expectedCode != 0 && resp.Code != tt.expectedCode {
				t.Errorf("PurchaseMonthly() code = %v, want %v", resp.Code, tt.expectedCode)
			}

			t.Logf("Response: Code=%d, Message=%s", resp.Code, resp.Message)

			if resp.Order != nil {
				t.Logf("Order created: ID=%s, DurationDays=%d", resp.Order.OrderId, resp.Order.DurationDays)
			}
		})
	}
}

// TestPurchaseVipStandard 标准购买VIP测试
func TestPurchaseVipStandard(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping standard test in short mode")
	}

	service := setupStandardTestService(t)
	defer cleanupStandardTestData(t, service)

	ctx := context.Background()

	tests := []struct {
		name         string
		req          *pb.PurchaseVipReq
		expectedCode int32
	}{
		{
			name: "参数验证 - 用户ID为空",
			req: &pb.PurchaseVipReq{
				UserId:     "",
				CoinAmount: testVipCoinAmount,
			},
			expectedCode: 400,
		},
		{
			name: "参数验证 - 书币数量为空",
			req: &pb.PurchaseVipReq{
				UserId:     testUserId,
				CoinAmount: "",
			},
			expectedCode: 400,
		},
		{
			name: "默认天数测试 - 使用配置中的默认值",
			req: &pb.PurchaseVipReq{
				UserId:       testUserId,
				CoinAmount:   testVipCoinAmount,
				DurationDays: 0, // 应该使用配置中的默认值30天
			},
			expectedCode: 0, // 不预期特定错误码
		},
		{
			name: "正常购买VIP - 实际调用数据库",
			req: &pb.PurchaseVipReq{
				UserId:       testUserId,
				CoinAmount:   testVipCoinAmount,
				DurationDays: 30,
			},
			expectedCode: 0, // 不预期特定错误码
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := service.PurchaseVip(ctx, tt.req)

			if err != nil {
				t.Errorf("PurchaseVip() error = %v", err)
				return
			}

			if tt.expectedCode != 0 && resp.Code != tt.expectedCode {
				t.Errorf("PurchaseVip() code = %v, want %v", resp.Code, tt.expectedCode)
			}

			t.Logf("Response: Code=%d, Message=%s", resp.Code, resp.Message)

			if resp.Order != nil {
				t.Logf("VIP Order created: ID=%s, DurationDays=%d", resp.Order.OrderId, resp.Order.DurationDays)
			}
		})
	}
}

// TestGetPurchaseOrdersStandard 标准获取购买订单测试
func TestGetPurchaseOrdersStandard(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping standard test in short mode")
	}

	service := setupStandardTestService(t)
	defer cleanupStandardTestData(t, service)

	ctx := context.Background()

	tests := []struct {
		name         string
		req          *pb.GetPurchaseOrdersReq
		expectedCode int32
	}{
		{
			name: "参数验证 - 用户ID为空",
			req: &pb.GetPurchaseOrdersReq{
				UserId: "",
			},
			expectedCode: 400,
		},
		{
			name: "正常获取订单列表",
			req: &pb.GetPurchaseOrdersReq{
				UserId:   testUserId,
				Page:     1,
				PageSize: 10,
			},
			expectedCode: 0, // 可能因为数据库连接问题而失败
		},
		{
			name: "默认分页参数",
			req: &pb.GetPurchaseOrdersReq{
				UserId:   testUserId,
				Page:     0, // 应该使用默认值1
				PageSize: 0, // 应该使用默认值20
			},
			expectedCode: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := service.GetPurchaseOrders(ctx, tt.req)

			if err != nil {
				t.Errorf("GetPurchaseOrders() error = %v", err)
				return
			}

			if tt.expectedCode != 0 && resp.Code != tt.expectedCode {
				t.Errorf("GetPurchaseOrders() code = %v, want %v", resp.Code, tt.expectedCode)
			}

			t.Logf("Response: Code=%d, Message=%s, Total=%d", resp.Code, resp.Message, resp.Total)

			if resp.Orders != nil {
				t.Logf("Found %d orders", len(resp.Orders))
			}
		})
	}
}

// TestCheckChapterPurchasedStandard 标准检查章节购买状态测试
func TestCheckChapterPurchasedStandard(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping standard test in short mode")
	}

	service := setupStandardTestService(t)
	defer cleanupStandardTestData(t, service)

	ctx := context.Background()

	tests := []struct {
		name         string
		req          *pb.CheckChapterPurchasedReq
		expectedCode int32
	}{
		{
			name: "参数验证 - 用户ID为空",
			req: &pb.CheckChapterPurchasedReq{
				UserId:       "",
				BookId:       testBookId,
				ChapterOrder: testChapterOrder,
			},
			expectedCode: 400,
		},
		{
			name: "参数验证 - 书籍ID为空",
			req: &pb.CheckChapterPurchasedReq{
				UserId:       testUserId,
				BookId:       "",
				ChapterOrder: testChapterOrder,
			},
			expectedCode: 400,
		},
		{
			name: "正常检查章节购买状态",
			req: &pb.CheckChapterPurchasedReq{
				UserId:       testUserId,
				BookId:       testBookId,
				ChapterOrder: testChapterOrder,
			},
			expectedCode: 0, // 可能因为数据库连接问题而失败
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := service.CheckChapterPurchased(ctx, tt.req)

			if err != nil {
				t.Errorf("CheckChapterPurchased() error = %v", err)
				return
			}

			if tt.expectedCode != 0 && resp.Code != tt.expectedCode {
				t.Errorf("CheckChapterPurchased() code = %v, want %v", resp.Code, tt.expectedCode)
			}

			t.Logf("Response: Code=%d, Message=%s, IsPurchased=%v, IsMonthly=%v",
				resp.Code, resp.Message, resp.IsPurchased, resp.IsMonthly)
		})
	}
}