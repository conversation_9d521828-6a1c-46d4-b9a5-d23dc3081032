package grpc

import (
	"context"
	"net"

	"creativematrix.com/beyondreading/gen2/app/base/account/conf"
	"creativematrix.com/beyondreading/gen2/app/base/account/svc"
	"creativematrix.com/beyondreading/pkg/gm"
	pb "creativematrix.com/beyondreading/gen2/proto/account"
	"google.golang.org/grpc"
)

func Start(c *conf.Config, svc *svc.AccountSvc) (*grpc.Server, error) {
	s := grpc.NewServer(grpc.UnaryInterceptor(gm.UnaryServerInterceptor()))

	pb.RegisterAccountServer(s, &server{as: svc})
	lis, err := net.Listen("tcp", c.Port.GRPC)
	if err != nil {
		return nil, err
	}
	go func() {
		if err := s.Serve(lis); err != nil {
			panic(err)
		}
	}()
	return s, nil
}

type server struct {
	as *svc.AccountSvc
}

// GetAccount 获取账户信息
func (s *server) GetAccount(ctx context.Context, req *pb.GetAccountReq) (*pb.GetAccountResp, error) {
	return s.as.GetAccount(ctx, req)
}

// CreateAccount 创建账户
func (s *server) CreateAccount(ctx context.Context, req *pb.CreateAccountReq) (*pb.CreateAccountResp, error) {
	return s.as.CreateAccount(ctx, req)
}

// Recharge 充值
func (s *server) Recharge(ctx context.Context, req *pb.RechargeReq) (*pb.RechargeResp, error) {
	return s.as.Recharge(ctx, req)
}

// GetAccountLogs 获取账户日志
func (s *server) GetAccountLogs(ctx context.Context, req *pb.GetAccountLogsReq) (*pb.GetAccountLogsResp, error) {
	return s.as.GetAccountLogs(ctx, req)
}

// UpdateUserStatus 更新用户状态
func (s *server) UpdateUserStatus(ctx context.Context, req *pb.UpdateUserStatusReq) (*pb.UpdateUserStatusResp, error) {
	return s.as.UpdateUserStatus(ctx, req)
}

// DeductCoins 扣除书币
func (s *server) DeductCoins(ctx context.Context, req *pb.DeductCoinsReq) (*pb.DeductCoinsResp, error) {
	return s.as.DeductCoins(ctx, req)
}

// CheckUserStatus 检查用户状态
func (s *server) CheckUserStatus(ctx context.Context, req *pb.CheckUserStatusReq) (*pb.CheckUserStatusResp, error) {
	return s.as.CheckUserStatus(ctx, req)
}
