# Account Service 配置文件

[base]
name = "account"
version = "1.0.0"
env = "test"

[log]
level = "debug"
format = "json"
output = "stdout"

[mysql]
# 测试数据库配置
[mysql.default]
dsn = "root:password@tcp(localhost:3306)/beyondreading_test?charset=utf8mb4&parseTime=True&loc=Local"
max_open_conns = 10
max_idle_conns = 5
conn_max_lifetime = "1h"

[redis]
# 测试Redis配置
[redis.default]
addr = "localhost:6379"
password = ""
db = 1
pool_size = 10
min_idle_conns = 5

[account]
# 账户相关配置
default_balance = "0.00"
default_level = 1
coins_per_level = 1000
vip_expire_days = 30
monthly_expire_days = 30
cache_expire = 3600
enable_cache = true

[grpc]
# Account gRPC服务配置
[grpc.account]
addr = ":9001"
timeout = "30s"

[http]
# Account HTTP服务配置
[http.account]
addr = ":8001"
timeout = "30s"
read_timeout = "30s"
write_timeout = "30s"

[discovery]
# 服务发现配置
[discovery.etcd]
endpoints = ["localhost:2379"]
timeout = "5s"
username = ""
password = ""

[tracing]
# 链路追踪配置
[tracing.jaeger]
endpoint = "http://localhost:14268/api/traces"
service_name = "account-service"
sample_rate = 1.0
enable = false  # 测试时禁用

[metrics]
# 监控指标配置
[metrics.prometheus]
addr = ":9101"
path = "/metrics"
enable = false  # 测试时禁用
