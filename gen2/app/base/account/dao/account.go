package dao

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"creativematrix.com/beyondreading/gen2/app/common/po"
	"creativematrix.com/beyondreading/pkg/mysql"
	"github.com/jmoiron/sqlx"
)

// GetAccountByUserId 根据用户ID获取账户信息
func (d *Dao) GetAccountByUserId(ctx context.Context, userId uint64) (*po.Account, error) {
	db, err := d.GetDB(userId)
	if err != nil {
		return nil, err
	}

	var account po.Account
	query := `SELECT account_id, user_id, coin_balance, total_recharged, total_consumed,
			  status, user_type, user_level, vip_expire_time, created_at, updated_at
			  FROM account WHERE user_id = ?`

	err = db.Get(&account, query, userId)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil // 账户不存在
		}
		return nil, fmt.Errorf("failed to get account: %w", err)
	}

	return &account, nil
}

// CreateAccount 创建账户
func (d *Dao) CreateAccount(ctx context.Context, userId uint64) (*po.Account, error) {
	db, err := d.GetDB(userId)
	if err != nil {
		return nil, err
	}

	now := time.Now()
	account := &po.Account{
		UserId:         userId,
		CoinBalance:    "0.00",
		TotalRecharged: "0.00",
		TotalConsumed:  "0.00",
		Status:         po.AccountStatusNormal,
		UserType:       po.UserTypeNormal,
		UserLevel:      1,
		CreatedAt:      now,
		UpdatedAt:      now,
	}

	query := `INSERT INTO account (user_id, coin_balance, total_recharged, total_consumed,
			  status, user_type, user_level, created_at, updated_at)
			  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`

	result, err := db.Exec(query, account.UserId, account.CoinBalance,
		account.TotalRecharged, account.TotalConsumed, account.Status,
		account.UserType, account.UserLevel, account.CreatedAt, account.UpdatedAt)
	if err != nil {
		return nil, fmt.Errorf("failed to create account: %w", err)
	}

	accountId, err := result.LastInsertId()
	if err != nil {
		return nil, fmt.Errorf("failed to get account id: %w", err)
	}

	account.AccountId = uint64(accountId)
	return account, nil
}

// UpdateAccountBalance 更新账户余额（带事务）- 用于购买扣除书币
func (d *Dao) UpdateAccountBalance(ctx context.Context, userId uint64, amount, orderType, orderId, bookId, chapterId, description string) error {
	db, err := d.GetDB(userId)
	if err != nil {
		return err
	}

	return mysql.Transact(db, func(tx *sqlx.Tx) error {
		// 获取当前账户信息（加锁）
		var account po.Account
		query := `SELECT account_id, user_id, coin_balance, total_recharged, total_consumed,
				  status, user_type, user_level FROM account WHERE user_id = ? FOR UPDATE`
		err = tx.Get(&account, query, userId)
		if err != nil {
			return fmt.Errorf("failed to get account for update: %w", err)
		}

		// 检查账户状态
		if account.Status != po.AccountStatusNormal {
			return fmt.Errorf("account is not active, status: %d", account.Status)
		}

		// 解析当前余额和扣除金额
		currentBalance, err := po.ParseDecimalString(account.CoinBalance)
		if err != nil {
			return fmt.Errorf("failed to parse current balance: %w", err)
		}

		deductAmount, err := po.ParseDecimalString(amount)
		if err != nil {
			return fmt.Errorf("failed to parse deduct amount: %w", err)
		}

		// 检查余额是否足够
		if currentBalance < deductAmount {
			return fmt.Errorf("insufficient balance: current=%.2f, required=%.2f", currentBalance, deductAmount)
		}

		// 计算新余额
		newBalance := currentBalance - deductAmount
		balanceBefore := account.CoinBalance
		balanceAfter := fmt.Sprintf("%.2f", newBalance)

		// 解析当前总消费
		currentTotalConsumed, err := po.ParseDecimalString(account.TotalConsumed)
		if err != nil {
			return fmt.Errorf("failed to parse total consumed: %w", err)
		}

		// 计算新的总消费金额
		newTotalConsumed := currentTotalConsumed + deductAmount
		newTotalConsumedStr := fmt.Sprintf("%.2f", newTotalConsumed)

		// 根据新的总消费金额计算用户等级
		newUserLevel := po.GetUserLevel(newTotalConsumedStr)

		// 更新账户余额、总消费和用户等级
		updateQuery := `UPDATE account SET coin_balance = ?, total_consumed = ?,
						user_level = ?, updated_at = ? WHERE account_id = ?`
		_, err = tx.Exec(updateQuery, balanceAfter, newTotalConsumedStr, newUserLevel, time.Now(), account.AccountId)
		if err != nil {
			return fmt.Errorf("failed to update account balance: %w", err)
		}

		// 记录账户日志（扣除金额记录为负数）
		logQuery := `INSERT INTO account_log (account_id, user_id, transaction_type, amount,
					 balance_before, balance_after, order_id, book_id, chapter_id, description, created_at)
					 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

		var bookIdPtr, chapterIdPtr, descPtr *string
		if bookId != "" {
			bookIdPtr = &bookId
		}
		if chapterId != "" {
			chapterIdPtr = &chapterId
		}
		if description != "" {
			descPtr = &description
		}

		// 扣除金额记录为负数
		logAmount := fmt.Sprintf("-%.2f", deductAmount)
		_, err = tx.Exec(logQuery, account.AccountId, userId, orderType, logAmount,
			balanceBefore, balanceAfter, orderId, bookIdPtr, chapterIdPtr, descPtr, time.Now())
		if err != nil {
			return fmt.Errorf("failed to insert account log: %w", err)
		}

		return nil
	})
}

// GetAccountLogs 获取账户日志
func (d *Dao) GetAccountLogs(ctx context.Context, userId uint64, page, pageSize int, transactionType string) ([]*po.AccountLog, int64, error) {
	db, err := d.GetDB(userId)
	if err != nil {
		return nil, 0, err
	}

	// 构建查询条件
	whereClause := "WHERE user_id = ?"
	args := []interface{}{userId}

	if transactionType != "" {
		whereClause += " AND transaction_type = ?"
		args = append(args, transactionType)
	}

	// 查询总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM account_log %s", whereClause)
	var total int64
	err = db.Get(&total, countQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count account logs: %w", err)
	}

	// 查询数据
	offset := (page - 1) * pageSize
	dataQuery := fmt.Sprintf(`SELECT log_id, account_id, user_id, transaction_type, amount,
							  balance_before, balance_after, order_id, book_id, chapter_id,
							  description, extra_data, created_at FROM account_log %s
							  ORDER BY created_at DESC LIMIT ? OFFSET ?`, whereClause)

	args = append(args, pageSize, offset)

	var logs []*po.AccountLog
	err = db.Select(&logs, dataQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get account logs: %w", err)
	}

	return logs, total, nil
}

// UpdateUserStatus 更新用户状态
func (d *Dao) UpdateUserStatus(ctx context.Context, userId uint64, userType int, vipExpireTime *time.Time) error {
	db, err := d.GetDB(userId)
	if err != nil {
		return err
	}

	query := `UPDATE account SET user_type = ?, vip_expire_time = ?, updated_at = ? WHERE user_id = ?`
	_, err = db.Exec(query, userType, vipExpireTime, time.Now(), userId)
	if err != nil {
		return fmt.Errorf("failed to update user status: %w", err)
	}

	return nil
}

// CheckUserStatus 检查用户状态
func (d *Dao) CheckUserStatus(ctx context.Context, userId uint64, bookId string) (*po.Account, bool, error) {
	// 获取账户信息
	account, err := d.GetAccountByUserId(ctx, userId)
	if err != nil {
		return nil, false, err
	}
	if account == nil {
		return nil, false, nil
	}

	// 检查包月状态
	isMonthly := false
	if bookId != "" {
		isMonthly, err = d.checkMonthlyStatus(ctx, userId, bookId)
		if err != nil {
			return account, false, err
		}
	}

	return account, isMonthly, nil
}

// checkMonthlyStatus 检查包月状态
func (d *Dao) checkMonthlyStatus(ctx context.Context, userId, bookId string) (bool, error) {
	db, err := d.GetDB(userId)
	if err != nil {
		return false, err
	}

	query := `SELECT COUNT(*) FROM monthly_status
			  WHERE user_id = ? AND book_id = ? AND status = 1 AND end_time > NOW()`
	var count int
	err = db.Get(&count, query, userId, bookId)
	if err != nil {
		return false, fmt.Errorf("failed to check monthly status: %w", err)
	}

	return count > 0, nil
}
