package dao

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"creativematrix.com/beyondreading/gen2/app/common/po"
	"creativematrix.com/beyondreading/pkg/mysql"
	"github.com/jmoiron/sqlx"
)

// CreateRechargeOrder 创建充值订单
func (d *Dao) CreateRechargeOrder(ctx context.Context, order *po.RechargeOrder) error {
	db, err := d.GetDB(order.UserId)
	if err != nil {
		return err
	}

	query := `INSERT INTO recharge_order (order_id, account_id, user_id, amount, coin_amount,
			  exchange_rate, payment_method, status, created_at, updated_at)
			  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

	_, err = db.Exec(query, order.OrderId, order.AccountId, order.UserId,
		order.Amount, order.CoinAmount, order.ExchangeRate, order.PaymentMethod,
		order.Status, order.CreatedAt, order.UpdatedAt)
	if err != nil {
		return fmt.Errorf("failed to create recharge order: %w", err)
	}

	return nil
}

// GetRechargeOrder 获取充值订单
func (d *Dao) GetRechargeOrder(ctx context.Context, orderId string) (*po.RechargeOrder, error) {
	// 这里需要根据orderId确定分片，简化实现
	// 实际应该从orderId中解析出userId或者维护orderId到userId的映射
	db, err := d.msshard.DB("default") // 简化实现
	if err != nil {
		return nil, err
	}

	query := `SELECT order_id, account_id, user_id, amount, coin_amount, exchange_rate,
			  payment_method, payment_order_id, status, paid_at, created_at, updated_at
			  FROM recharge_order WHERE order_id = ?`

	var order po.RechargeOrder
	err = db.Get(&order, query, orderId)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get recharge order: %w", err)
	}

	return &order, nil
}

// UpdateRechargeOrderStatus 更新充值订单状态
func (d *Dao) UpdateRechargeOrderStatus(ctx context.Context, orderId string, status int, paymentOrderId string) error {
	// 这里需要根据orderId确定分片
	db, err := d.msshard.DB("default") // 简化实现
	if err != nil {
		return err
	}

	now := time.Now()
	var paidAt *time.Time
	if status == po.OrderStatusPaid {
		paidAt = &now
	}

	query := `UPDATE recharge_order SET status = ?, payment_order_id = ?, paid_at = ?,
			  updated_at = ? WHERE order_id = ?`

	_, err = db.Exec(query, status, paymentOrderId, paidAt, now, orderId)
	if err != nil {
		return fmt.Errorf("failed to update recharge order status: %w", err)
	}

	return nil
}

// ProcessRechargeSuccess 处理充值成功
func (d *Dao) ProcessRechargeSuccess(ctx context.Context, orderId string) error {
	// 获取充值订单
	order, err := d.GetRechargeOrder(ctx, orderId)
	if err != nil {
		return err
	}
	if order == nil {
		return fmt.Errorf("recharge order not found: %s", orderId)
	}

	if order.Status == po.OrderStatusPaid {
		return nil // 已经处理过了
	}

	db, err := d.GetDB(order.UserId)
	if err != nil {
		return err
	}

	return mysql.Transact(db, func(tx *sqlx.Tx) error {
		// 更新订单状态
		now := time.Now()
		updateOrderQuery := `UPDATE recharge_order SET status = ?, paid_at = ?, updated_at = ? WHERE order_id = ?`
		_, err = tx.Exec(updateOrderQuery, po.OrderStatusPaid, now, now, orderId)
		if err != nil {
			return fmt.Errorf("failed to update recharge order: %w", err)
		}

		// 获取账户信息（加锁）
		var account po.Account
		accountQuery := `SELECT account_id, user_id, coin_balance, total_recharged, total_consumed,
						 status, user_level FROM account WHERE user_id = ? FOR UPDATE`
		err = tx.Get(&account, accountQuery, order.UserId)
		if err != nil {
			return fmt.Errorf("failed to get account for update: %w", err)
		}

		// 计算新余额（这里需要实现decimal计算）
		balanceBefore := account.CoinBalance
		balanceAfter := account.CoinBalance // 这里需要实际计算：balanceBefore + order.CoinAmount
		totalRecharged := account.TotalRecharged // 这里需要实际计算：totalRecharged + order.Amount

		// 用户等级基于消费金额，充值不影响等级，保持原等级
		userLevel := account.UserLevel

		// 更新账户余额
		updateAccountQuery := `UPDATE account SET coin_balance = ?, total_recharged = ?, user_level = ?, updated_at = ? WHERE account_id = ?`
		_, err = tx.Exec(updateAccountQuery, balanceAfter, totalRecharged, userLevel, now, account.AccountId)
		if err != nil {
			return fmt.Errorf("failed to update account balance: %w", err)
		}

		// 记录账户日志
		logQuery := `INSERT INTO account_log (account_id, user_id, transaction_type, amount,
					 balance_before, balance_after, order_id, description, created_at)
					 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`

		description := fmt.Sprintf("充值成功，获得%s书币", order.CoinAmount)
		_, err = tx.Exec(logQuery, account.AccountId, order.UserId, po.TransactionTypeRecharge,
			order.CoinAmount, balanceBefore, balanceAfter, orderId, description, now)
		if err != nil {
			return fmt.Errorf("failed to insert account log: %w", err)
		}

		return nil
	})
}
