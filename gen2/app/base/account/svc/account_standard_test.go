package svc

import (
	"context"
	"testing"

	"creativematrix.com/beyondreading/gen2/app/base/account/api"
	"creativematrix.com/beyondreading/gen2/app/base/account/conf"
	pb "creativematrix.com/beyondreading/gen2/proto/account"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/tracer"
)

const (
	// 测试用户和数据
	testUserId = "test_user_123"
	testAccountId = int64(1001)
	testCoinAmount = "50.00"
	testRechargeAmount = "100.00"
)

// setupStandardTestAccountService 按照标准方式创建测试服务
func setupStandardTestAccountService(t *testing.T) *AccountSvc {
	// 按照main.go的方式加载配置
	config := conf.Load(api.App)
	
	// 初始化日志
	logger.InitLog(api.App, config.Log.Level)
	
	// 初始化链路追踪
	tracer.InitTracing(config.Base, api.App)
	
	// 创建服务实例
	service := Load(config)
	if service == nil {
		t.Fatal("Failed to create service")
	}
	
	return service
}

// cleanupStandardTestAccountData 清理测试数据
func cleanupStandardTestAccountData(t *testing.T, service *AccountSvc) {
	// 这里可以添加清理测试数据的逻辑
	t.Log("Cleaning up test account data...")
}

// TestGetAccountStandard 标准获取账户测试
func TestGetAccountStandard(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping standard test in short mode")
	}
	
	service := setupStandardTestAccountService(t)
	defer cleanupStandardTestAccountData(t, service)
	
	ctx := context.Background()
	
	tests := []struct {
		name         string
		req          *pb.GetAccountReq
		expectedCode int32
	}{
		{
			name: "参数验证 - 用户ID为空",
			req: &pb.GetAccountReq{
				UserId: "",
			},
			expectedCode: 400,
		},
		{
			name: "正常获取账户信息",
			req: &pb.GetAccountReq{
				UserId: testUserId,
			},
			expectedCode: 0, // 可能因为数据库连接问题而失败，或者账户不存在会自动创建
		},
		{
			name: "获取不存在的账户 - 应该自动创建",
			req: &pb.GetAccountReq{
				UserId: testUserId + "_new",
			},
			expectedCode: 0, // 可能因为数据库连接问题而失败
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := service.GetAccount(ctx, tt.req)
			
			if err != nil {
				t.Errorf("GetAccount() error = %v", err)
				return
			}
			
			if tt.expectedCode != 0 && resp.Code != tt.expectedCode {
				t.Errorf("GetAccount() code = %v, want %v", resp.Code, tt.expectedCode)
			}
			
			t.Logf("Response: Code=%d, Message=%s", resp.Code, resp.Message)
			
			if resp.Account != nil {
				t.Logf("Account: ID=%d, UserId=%s, Balance=%s, Level=%d", 
					resp.Account.AccountId, resp.Account.UserId, 
					resp.Account.CoinBalance, resp.Account.UserLevel)
			}
		})
	}
}

// TestCreateAccountStandard 标准创建账户测试
func TestCreateAccountStandard(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping standard test in short mode")
	}
	
	service := setupStandardTestAccountService(t)
	defer cleanupStandardTestAccountData(t, service)
	
	ctx := context.Background()
	
	tests := []struct {
		name         string
		req          *pb.CreateAccountReq
		expectedCode int32
	}{
		{
			name: "参数验证 - 用户ID为空",
			req: &pb.CreateAccountReq{
				UserId: "",
			},
			expectedCode: 400,
		},
		{
			name: "正常创建账户",
			req: &pb.CreateAccountReq{
				UserId: testUserId + "_create_test",
			},
			expectedCode: 0, // 可能因为数据库连接问题而失败
		},
		{
			name: "重复创建账户 - 应该返回已存在的账户",
			req: &pb.CreateAccountReq{
				UserId: testUserId,
			},
			expectedCode: 0, // 可能返回已存在的账户或创建新账户
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := service.CreateAccount(ctx, tt.req)
			
			if err != nil {
				t.Errorf("CreateAccount() error = %v", err)
				return
			}
			
			if tt.expectedCode != 0 && resp.Code != tt.expectedCode {
				t.Errorf("CreateAccount() code = %v, want %v", resp.Code, tt.expectedCode)
			}
			
			t.Logf("Response: Code=%d, Message=%s", resp.Code, resp.Message)
			
			if resp.Account != nil {
				t.Logf("Created Account: ID=%d, UserId=%s, Balance=%s", 
					resp.Account.AccountId, resp.Account.UserId, resp.Account.CoinBalance)
			}
		})
	}
}

// TestRechargeStandard 标准充值测试
func TestRechargeStandard(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping standard test in short mode")
	}
	
	service := setupStandardTestAccountService(t)
	defer cleanupStandardTestAccountData(t, service)
	
	ctx := context.Background()
	
	tests := []struct {
		name         string
		req          *pb.RechargeReq
		expectedCode int32
	}{
		{
			name: "参数验证 - 用户ID为空",
			req: &pb.RechargeReq{
				UserId:        "",
				Amount:        testRechargeAmount,
				PaymentMethod: "alipay",
			},
			expectedCode: 400,
		},
		{
			name: "参数验证 - 金额为空",
			req: &pb.RechargeReq{
				UserId:        testUserId,
				Amount:        "",
				PaymentMethod: "alipay",
			},
			expectedCode: 400,
		},
		{
			name: "参数验证 - 支付方式为空",
			req: &pb.RechargeReq{
				UserId:        testUserId,
				Amount:        testRechargeAmount,
				PaymentMethod: "",
			},
			expectedCode: 400,
		},
		{
			name: "正常充值 - 支付宝",
			req: &pb.RechargeReq{
				UserId:        testUserId,
				Amount:        testRechargeAmount,
				PaymentMethod: "alipay",
			},
			expectedCode: 0, // 可能因为数据库连接问题而失败
		},
		{
			name: "正常充值 - 微信支付",
			req: &pb.RechargeReq{
				UserId:        testUserId,
				Amount:        "50.00",
				PaymentMethod: "wechat",
			},
			expectedCode: 0, // 可能因为数据库连接问题而失败
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := service.Recharge(ctx, tt.req)
			
			if err != nil {
				t.Errorf("Recharge() error = %v", err)
				return
			}
			
			if tt.expectedCode != 0 && resp.Code != tt.expectedCode {
				t.Errorf("Recharge() code = %v, want %v", resp.Code, tt.expectedCode)
			}
			
			t.Logf("Response: Code=%d, Message=%s", resp.Code, resp.Message)
			
			if resp.Order != nil {
				t.Logf("Recharge Order: ID=%s, Amount=%s, Status=%d", 
					resp.Order.OrderId, resp.Order.Amount, resp.Order.Status)
			}
		})
	}
}

// TestDeductCoinsStandard 标准扣除书币测试
func TestDeductCoinsStandard(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping standard test in short mode")
	}
	
	service := setupStandardTestAccountService(t)
	defer cleanupStandardTestAccountData(t, service)
	
	ctx := context.Background()
	
	tests := []struct {
		name         string
		req          *pb.DeductCoinsReq
		expectedCode int32
	}{
		{
			name: "参数验证 - 用户ID为空",
			req: &pb.DeductCoinsReq{
				UserId:      "",
				Amount:      testCoinAmount,
				OrderType:   "chapter",
				OrderId:     "test_order_123",
				Description: "测试扣除书币",
			},
			expectedCode: 400,
		},
		{
			name: "参数验证 - 金额为空",
			req: &pb.DeductCoinsReq{
				UserId:      testUserId,
				Amount:      "",
				OrderType:   "chapter",
				OrderId:     "test_order_123",
				Description: "测试扣除书币",
			},
			expectedCode: 400,
		},
		{
			name: "参数验证 - 订单类型为空",
			req: &pb.DeductCoinsReq{
				UserId:      testUserId,
				Amount:      testCoinAmount,
				OrderType:   "",
				OrderId:     "test_order_123",
				Description: "测试扣除书币",
			},
			expectedCode: 400,
		},
		{
			name: "正常扣除书币 - 购买章节",
			req: &pb.DeductCoinsReq{
				UserId:      testUserId,
				Amount:      testCoinAmount,
				OrderType:   "chapter",
				OrderId:     "test_order_123",
				BookId:      "test_book_456",
				ChapterId:   "test_chapter_1",
				Description: "测试扣除书币",
			},
			expectedCode: 0, // 可能因为数据库连接问题或余额不足而失败
		},
		{
			name: "正常扣除书币 - 购买包月",
			req: &pb.DeductCoinsReq{
				UserId:      testUserId,
				Amount:      "30.00",
				OrderType:   "monthly",
				OrderId:     "test_monthly_123",
				BookId:      "test_book_456",
				Description: "测试购买包月",
			},
			expectedCode: 0, // 可能因为数据库连接问题或余额不足而失败
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := service.DeductCoins(ctx, tt.req)
			
			if err != nil {
				t.Errorf("DeductCoins() error = %v", err)
				return
			}
			
			if tt.expectedCode != 0 && resp.Code != tt.expectedCode {
				t.Errorf("DeductCoins() code = %v, want %v", resp.Code, tt.expectedCode)
			}
			
			t.Logf("Response: Code=%d, Message=%s", resp.Code, resp.Message)
			
			if resp.Code == 200 {
				t.Logf("Balance after deduction: %s", resp.BalanceAfter)
			}
		})
	}
}
