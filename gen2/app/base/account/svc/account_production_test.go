package svc

import (
	"context"
	"os"
	"path/filepath"
	"testing"

	"creativematrix.com/beyondreading/gen2/app/base/account/conf"
	"creativematrix.com/beyondreading/gen2/app/common/po"
	pb "creativematrix.com/beyondreading/gen2/proto/account"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/tracer"
)

const (
	// 测试应用名称
	testApp = "account-test"
	
	// 测试用户和数据
	testUserId = "test_user_123"
	testAccountId = int64(1001)
	testCoinAmount = "50.00"
	testRechargeAmount = "100.00"
)

// setupTestAccountService 按照main方法的方式创建测试服务
func setupTestAccountService(t *testing.T) *AccountSvc {
	// 获取配置文件路径
	configPath := getAccountConfigPath(t)
	
	// 加载配置
	config := conf.Load(testApp, configPath)
	if config == nil {
		t.Fatal("Failed to load config")
	}
	
	// 初始化日志
	logger.InitLog(testApp, config.Log.Level)
	
	// 初始化链路追踪
	tracer.InitTracing(config.Base, testApp)
	
	// 创建服务实例
	service := Load(config)
	if service == nil {
		t.Fatal("Failed to create service")
	}
	
	return service
}

// getAccountConfigPath 获取配置文件路径
func getAccountConfigPath(t *testing.T) string {
	// 获取当前工作目录
	wd, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get working directory: %v", err)
	}
	
	// 构建配置文件路径：从svc目录到cmd目录
	configPath := filepath.Join(wd, "..", "cmd", "config.toml")
	
	// 检查配置文件是否存在
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		t.Fatalf("Config file not found: %s", configPath)
	}
	
	return configPath
}

// cleanupTestAccountData 清理测试数据
func cleanupTestAccountData(t *testing.T, service *AccountSvc) {
	// 这里可以添加清理测试数据的逻辑
	t.Log("Cleaning up test account data...")
}

// TestGetAccountProduction 生产级获取账户测试
func TestGetAccountProduction(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping production test in short mode")
	}
	
	service := setupTestAccountService(t)
	defer cleanupTestAccountData(t, service)
	
	ctx := context.Background()
	
	tests := []struct {
		name         string
		req          *pb.GetAccountReq
		expectedCode int32
	}{
		{
			name: "参数验证 - 用户ID为空",
			req: &pb.GetAccountReq{
				UserId: "",
			},
			expectedCode: 400,
		},
		{
			name: "正常获取账户信息",
			req: &pb.GetAccountReq{
				UserId: testUserId,
			},
			expectedCode: 0, // 可能因为数据库连接问题而失败，或者账户不存在会自动创建
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := service.GetAccount(ctx, tt.req)
			
			if err != nil {
				t.Errorf("GetAccount() error = %v", err)
				return
			}
			
			if tt.expectedCode != 0 && resp.Code != tt.expectedCode {
				t.Errorf("GetAccount() code = %v, want %v", resp.Code, tt.expectedCode)
			}
			
			t.Logf("Response: Code=%d, Message=%s", resp.Code, resp.Message)
			
			if resp.Account != nil {
				t.Logf("Account: ID=%d, UserId=%s, Balance=%s, Level=%d", 
					resp.Account.AccountId, resp.Account.UserId, 
					resp.Account.CoinBalance, resp.Account.UserLevel)
			}
		})
	}
}

// TestCreateAccountProduction 生产级创建账户测试
func TestCreateAccountProduction(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping production test in short mode")
	}
	
	service := setupTestAccountService(t)
	defer cleanupTestAccountData(t, service)
	
	ctx := context.Background()
	
	tests := []struct {
		name         string
		req          *pb.CreateAccountReq
		expectedCode int32
	}{
		{
			name: "参数验证 - 用户ID为空",
			req: &pb.CreateAccountReq{
				UserId: "",
			},
			expectedCode: 400,
		},
		{
			name: "正常创建账户",
			req: &pb.CreateAccountReq{
				UserId: testUserId + "_new",
			},
			expectedCode: 0, // 可能因为数据库连接问题而失败
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := service.CreateAccount(ctx, tt.req)
			
			if err != nil {
				t.Errorf("CreateAccount() error = %v", err)
				return
			}
			
			if tt.expectedCode != 0 && resp.Code != tt.expectedCode {
				t.Errorf("CreateAccount() code = %v, want %v", resp.Code, tt.expectedCode)
			}
			
			t.Logf("Response: Code=%d, Message=%s", resp.Code, resp.Message)
			
			if resp.Account != nil {
				t.Logf("Created Account: ID=%d, UserId=%s, Balance=%s", 
					resp.Account.AccountId, resp.Account.UserId, resp.Account.CoinBalance)
			}
		})
	}
}

// TestRechargeProduction 生产级充值测试
func TestRechargeProduction(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping production test in short mode")
	}
	
	service := setupTestAccountService(t)
	defer cleanupTestAccountData(t, service)
	
	ctx := context.Background()
	
	tests := []struct {
		name         string
		req          *pb.RechargeReq
		expectedCode int32
	}{
		{
			name: "参数验证 - 用户ID为空",
			req: &pb.RechargeReq{
				UserId:        "",
				Amount:        testRechargeAmount,
				PaymentMethod: "alipay",
			},
			expectedCode: 400,
		},
		{
			name: "参数验证 - 金额为空",
			req: &pb.RechargeReq{
				UserId:        testUserId,
				Amount:        "",
				PaymentMethod: "alipay",
			},
			expectedCode: 400,
		},
		{
			name: "参数验证 - 支付方式为空",
			req: &pb.RechargeReq{
				UserId:        testUserId,
				Amount:        testRechargeAmount,
				PaymentMethod: "",
			},
			expectedCode: 400,
		},
		{
			name: "正常充值",
			req: &pb.RechargeReq{
				UserId:        testUserId,
				Amount:        testRechargeAmount,
				PaymentMethod: "alipay",
			},
			expectedCode: 0, // 可能因为数据库连接问题而失败
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := service.Recharge(ctx, tt.req)
			
			if err != nil {
				t.Errorf("Recharge() error = %v", err)
				return
			}
			
			if tt.expectedCode != 0 && resp.Code != tt.expectedCode {
				t.Errorf("Recharge() code = %v, want %v", resp.Code, tt.expectedCode)
			}
			
			t.Logf("Response: Code=%d, Message=%s", resp.Code, resp.Message)
			
			if resp.Order != nil {
				t.Logf("Recharge Order: ID=%s, Amount=%s, Status=%d", 
					resp.Order.OrderId, resp.Order.Amount, resp.Order.Status)
			}
		})
	}
}

// TestDeductCoinsProduction 生产级扣除书币测试
func TestDeductCoinsProduction(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping production test in short mode")
	}
	
	service := setupTestAccountService(t)
	defer cleanupTestAccountData(t, service)
	
	ctx := context.Background()
	
	tests := []struct {
		name         string
		req          *pb.DeductCoinsReq
		expectedCode int32
	}{
		{
			name: "参数验证 - 用户ID为空",
			req: &pb.DeductCoinsReq{
				UserId:      "",
				Amount:      testCoinAmount,
				OrderType:   "chapter",
				OrderId:     "test_order_123",
				Description: "测试扣除书币",
			},
			expectedCode: 400,
		},
		{
			name: "参数验证 - 金额为空",
			req: &pb.DeductCoinsReq{
				UserId:      testUserId,
				Amount:      "",
				OrderType:   "chapter",
				OrderId:     "test_order_123",
				Description: "测试扣除书币",
			},
			expectedCode: 400,
		},
		{
			name: "正常扣除书币",
			req: &pb.DeductCoinsReq{
				UserId:      testUserId,
				Amount:      testCoinAmount,
				OrderType:   "chapter",
				OrderId:     "test_order_123",
				BookId:      "test_book_456",
				ChapterId:   "test_chapter_1",
				Description: "测试扣除书币",
			},
			expectedCode: 0, // 可能因为数据库连接问题或余额不足而失败
		},
	}
	
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resp, err := service.DeductCoins(ctx, tt.req)
			
			if err != nil {
				t.Errorf("DeductCoins() error = %v", err)
				return
			}
			
			if tt.expectedCode != 0 && resp.Code != tt.expectedCode {
				t.Errorf("DeductCoins() code = %v, want %v", resp.Code, tt.expectedCode)
			}
			
			t.Logf("Response: Code=%d, Message=%s", resp.Code, resp.Message)
			
			if resp.Code == 200 {
				t.Logf("Balance after deduction: %s", resp.BalanceAfter)
			}
		})
	}
}
