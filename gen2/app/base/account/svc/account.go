package svc

import (
	"context"
	"fmt"
	"time"

	"creativematrix.com/beyondreading/gen2/app/common/po"
	pb "creativematrix.com/beyondreading/gen2/proto/account"
	"creativematrix.com/beyondreading/pkg/logger"
)

// GetAccount 获取账户信息
func (s *AccountSvc) GetAccount(ctx context.Context, req *pb.GetAccountReq) (*pb.GetAccountResp, error) {
	if req.UserId == "" {
		return &pb.GetAccountResp{
			Code:    400,
			Message: "User ID cannot be empty",
		}, nil
	}

	account, err := s.dao.GetAccountByUserId(ctx, req.UserId)
	if err != nil {
		logger.LogErrorf("Failed to get account for user %s: %v", req.UserId, err)
		return &pb.GetAccountResp{
			Code:    500,
			Message: "Failed to get account information",
		}, nil
	}

	if account == nil {
		// 自动创建账户
		account, err = s.dao.CreateAccount(ctx, req.UserId)
		if err != nil {
			logger.LogErrorf("Failed to create account for user %s: %v", req.UserId, err)
			return &pb.GetAccountResp{
				Code:    500,
				Message: "Failed to create account",
			}, nil
		}
	}

	return &pb.GetAccountResp{
		Code:    200,
		Message: "success",
		Account: s.convertAccountToProto(account),
	}, nil
}

// CreateAccount 创建账户
func (s *AccountSvc) CreateAccount(ctx context.Context, req *pb.CreateAccountReq) (*pb.CreateAccountResp, error) {
	if req.UserId == "" {
		return &pb.CreateAccountResp{
			Code:    400,
			Message: "User ID cannot be empty",
		}, nil
	}

	// 检查账户是否已存在
	existingAccount, err := s.dao.GetAccountByUserId(ctx, req.UserId)
	if err != nil {
		logger.LogErrorf("Failed to check existing account for user %s: %v", req.UserId, err)
		return &pb.CreateAccountResp{
			Code:    500,
			Message: "Failed to check existing account",
		}, nil
	}

	if existingAccount != nil {
		return &pb.CreateAccountResp{
			Code:    409,
			Message: "Account already exists",
			Account: s.convertAccountToProto(existingAccount),
		}, nil
	}

	// 创建新账户
	account, err := s.dao.CreateAccount(ctx, req.UserId)
	if err != nil {
		logger.LogErrorf("Failed to create account for user %s: %v", req.UserId, err)
		return &pb.CreateAccountResp{
			Code:    500,
			Message: "Failed to create account",
		}, nil
	}

	return &pb.CreateAccountResp{
		Code:    200,
		Message: "Account created successfully",
		Account: s.convertAccountToProto(account),
	}, nil
}

// Recharge 充值
func (s *AccountSvc) Recharge(ctx context.Context, req *pb.RechargeReq) (*pb.RechargeResp, error) {
	if req.UserId == "" || req.Amount == "" || req.PaymentMethod == "" {
		return &pb.RechargeResp{
			Code:    400,
			Message: "Parameters cannot be empty",
		}, nil
	}

	// 获取账户信息
	account, err := s.dao.GetAccountByUserId(ctx, req.UserId)
	if err != nil {
		logger.LogErrorf("Failed to get account for user %s: %v", req.UserId, err)
		return &pb.RechargeResp{
			Code:    500,
			Message: "Failed to get account information",
		}, nil
	}

	if account == nil {
		return &pb.RechargeResp{
			Code:    404,
			Message: "Account not found",
		}, nil
	}

	// 生成订单ID
	orderId := s.generateOrderId("RC") // RC = Recharge

	// 设置默认兑换比例
	exchangeRate := req.ExchangeRate
	if exchangeRate == "" {
		exchangeRate = s.conf.Account.DefaultExchangeRate
	}

	// 计算书币数量（这里需要实现decimal计算）
	coinAmount := req.Amount // 简化实现，实际应该根据兑换比例计算

	// 创建充值订单
	now := time.Now()
	order := &po.RechargeOrder{
		OrderId:       orderId,
		AccountId:     account.AccountId,
		UserId:        req.UserId,
		Amount:        req.Amount,
		CoinAmount:    coinAmount,
		ExchangeRate:  exchangeRate,
		PaymentMethod: req.PaymentMethod,
		Status:        po.OrderStatusPending,
		CreatedAt:     now,
		UpdatedAt:     now,
	}

	err = s.dao.CreateRechargeOrder(ctx, order)
	if err != nil {
		logger.LogErrorf("Failed to create recharge order: %v", err)
		return &pb.RechargeResp{
			Code:    500,
			Message: "Failed to create recharge order",
		}, nil
	}

	return &pb.RechargeResp{
		Code:    200,
		Message: "Recharge order created successfully",
		Order:   s.convertRechargeOrderToProto(order),
	}, nil
}

// GetAccountLogs 获取账户日志
func (s *AccountSvc) GetAccountLogs(ctx context.Context, req *pb.GetAccountLogsReq) (*pb.GetAccountLogsResp, error) {
	if req.UserId == "" {
		return &pb.GetAccountLogsResp{
			Code:    400,
			Message: "User ID cannot be empty",
		}, nil
	}

	// 设置默认分页参数
	page := req.Page
	if page <= 0 {
		page = 1
	}

	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 100 {
		pageSize = 100
	}

	logs, total, err := s.dao.GetAccountLogs(ctx, req.UserId, int(page), int(pageSize), req.TransactionType)
	if err != nil {
		logger.LogErrorf("Failed to get account logs for user %s: %v", req.UserId, err)
		return &pb.GetAccountLogsResp{
			Code:    500,
			Message: "Failed to get account logs",
		}, nil
	}

	// 转换为Proto格式
	protoLogs := make([]*pb.AccountLog, 0, len(logs))
	for _, log := range logs {
		protoLogs = append(protoLogs, s.convertAccountLogToProto(log))
	}

	return &pb.GetAccountLogsResp{
		Code:    200,
		Message: "success",
		Logs:    protoLogs,
		Total:   total,
	}, nil
}

// UpdateUserStatus 更新用户状态
func (s *AccountSvc) UpdateUserStatus(ctx context.Context, req *pb.UpdateUserStatusReq) (*pb.UpdateUserStatusResp, error) {
	if req.UserId == "" {
		return &pb.UpdateUserStatusResp{
			Code:    400,
			Message: "User ID cannot be empty",
		}, nil
	}

	var vipExpireTime *time.Time
	if req.VipExpireTime > 0 {
		t := time.Unix(req.VipExpireTime, 0)
		vipExpireTime = &t
	}

	err := s.dao.UpdateUserStatus(ctx, req.UserId, int(req.UserType), vipExpireTime)
	if err != nil {
		logger.LogErrorf("Failed to update user status for user %s: %v", req.UserId, err)
		return &pb.UpdateUserStatusResp{
			Code:    500,
			Message: "Failed to update user status",
		}, nil
	}

	return &pb.UpdateUserStatusResp{
		Code:    200,
		Message: "User status updated successfully",
	}, nil
}

// DeductCoins 扣除书币
func (s *AccountSvc) DeductCoins(ctx context.Context, req *pb.DeductCoinsReq) (*pb.DeductCoinsResp, error) {
	if req.UserId == "" || req.Amount == "" {
		return &pb.DeductCoinsResp{
			Code:    400,
			Message: "Parameters cannot be empty",
		}, nil
	}

	err := s.dao.UpdateAccountBalance(ctx, req.UserId, req.Amount, req.TransactionType,
		req.OrderId, req.BookId, req.ChapterId, req.Description)
	if err != nil {
		logger.LogErrorf("Failed to deduct coins for user %s: %v", req.UserId, err)
		return &pb.DeductCoinsResp{
			Code:    500,
			Message: "Failed to deduct coins",
		}, nil
	}

	// 获取更新后的账户信息
	account, err := s.dao.GetAccountByUserId(ctx, req.UserId)
	if err != nil {
		logger.LogErrorf("Failed to get updated account for user %s: %v", req.UserId, err)
		return &pb.DeductCoinsResp{
			Code:    500,
			Message: "Failed to get account information",
		}, nil
	}

	return &pb.DeductCoinsResp{
		Code:         200,
		Message:      "Coins deducted successfully",
		BalanceAfter: account.CoinBalance,
	}, nil
}

// CheckUserStatus 检查用户状态
func (s *AccountSvc) CheckUserStatus(ctx context.Context, req *pb.CheckUserStatusReq) (*pb.CheckUserStatusResp, error) {
	if req.UserId == "" {
		return &pb.CheckUserStatusResp{
			Code:    400,
			Message: "User ID cannot be empty",
		}, nil
	}

	account, isMonthly, err := s.dao.CheckUserStatus(ctx, req.UserId, req.BookId)
	if err != nil {
		logger.LogErrorf("Failed to check user status for user %s: %v", req.UserId, err)
		return &pb.CheckUserStatusResp{
			Code:    500,
			Message: "Failed to check user status",
		}, nil
	}

	if account == nil {
		return &pb.CheckUserStatusResp{
			Code:    404,
			Message: "Account not found",
		}, nil
	}

	resp := &pb.CheckUserStatusResp{
		Code:      200,
		Message:   "success",
		UserType:  int32(account.UserType),
		UserLevel: int32(account.UserLevel),
		IsVip:     account.IsVipActive(),
		IsMonthly: isMonthly,
	}

	if account.VipExpireTime != nil {
		resp.VipExpireTime = account.VipExpireTime.Unix()
	}

	return resp, nil
}

// 辅助方法
func (s *AccountSvc) generateOrderId(prefix string) string {
	// 简化实现，实际应该生成唯一ID
	return fmt.Sprintf("%s%d", prefix, time.Now().UnixNano())
}

func (s *AccountSvc) convertAccountToProto(account *po.Account) *pb.AccountInfo {
	proto := &pb.AccountInfo{
		AccountId:      account.AccountId,
		UserId:         account.UserId,
		CoinBalance:    account.CoinBalance,
		TotalRecharged: account.TotalRecharged,
		TotalConsumed:  account.TotalConsumed,
		Status:         int32(account.Status),
		UserType:       int32(account.UserType),
		UserLevel:      int32(account.UserLevel),
		CreatedAt:      account.CreatedAt.Unix(),
		UpdatedAt:      account.UpdatedAt.Unix(),
	}

	if account.VipExpireTime != nil {
		proto.VipExpireTime = account.VipExpireTime.Unix()
	}

	return proto
}

func (s *AccountSvc) convertRechargeOrderToProto(order *po.RechargeOrder) *pb.RechargeOrder {
	proto := &pb.RechargeOrder{
		OrderId:       order.OrderId,
		AccountId:     order.AccountId,
		UserId:        order.UserId,
		Amount:        order.Amount,
		CoinAmount:    order.CoinAmount,
		ExchangeRate:  order.ExchangeRate,
		PaymentMethod: order.PaymentMethod,
		Status:        int32(order.Status),
		CreatedAt:     order.CreatedAt.Unix(),
		UpdatedAt:     order.UpdatedAt.Unix(),
	}

	if order.PaymentOrderId != nil {
		proto.PaymentOrderId = *order.PaymentOrderId
	}

	if order.PaidAt != nil {
		proto.PaidAt = order.PaidAt.Unix()
	}

	return proto
}

func (s *AccountSvc) convertAccountLogToProto(log *po.AccountLog) *pb.AccountLog {
	proto := &pb.AccountLog{
		LogId:           log.LogId,
		AccountId:       log.AccountId,
		UserId:          log.UserId,
		TransactionType: log.TransactionType,
		Amount:          log.Amount,
		BalanceBefore:   log.BalanceBefore,
		BalanceAfter:    log.BalanceAfter,
		CreatedAt:       log.CreatedAt.Unix(),
	}

	if log.OrderId != nil {
		proto.OrderId = *log.OrderId
	}
	if log.BookId != nil {
		proto.BookId = *log.BookId
	}
	if log.ChapterId != nil {
		proto.ChapterId = *log.ChapterId
	}
	if log.Description != nil {
		proto.Description = *log.Description
	}
	if log.ExtraData != nil {
		proto.ExtraData = *log.ExtraData
	}

	return proto
}
