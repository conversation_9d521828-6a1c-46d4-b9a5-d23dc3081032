package conf

import (
	"fmt"

	"creativematrix.com/beyondreading/pkg/config"
)

type Config struct {
	config.Base

	Log struct {
		Level string `toml:"level"`
	} `toml:"log"`

	Account struct {
		DefaultExchangeRate float32 `toml:"default_exchange_rate"` // 默认兑换比例
		MinRechargeAmount   float32 `toml:"min_recharge_amount"`   // 最小充值金额
		MaxRechargeAmount   float32 `toml:"max_recharge_amount"`   // 最大充值金额
		CacheExpire         int     `toml:"cache_expire"`          // 缓存过期时间（秒）
		EnableCache         bool    `toml:"enable_cache"`          // 是否启用缓存
	} `toml:"account"`

	Payment struct {
		Alipay struct {
			AppId      string `toml:"app_id"`
			PrivateKey string `toml:"private_key"`
			PublicKey  string `toml:"public_key"`
		} `toml:"alipay"`

		Wechat struct {
			AppId     string `toml:"app_id"`
			MchId     string `toml:"mch_id"`
			Key       string `toml:"key"`
			NotifyUrl string `toml:"notify_url"`
		} `toml:"wechat"`

		Apple struct {
			BundleId     string `toml:"bundle_id"`
			IsProduction bool   `toml:"is_production"`
		} `toml:"apple"`
	} `toml:"payment"`
}

func Load(app string) *Config {
	var conf = new(Config)
	if err := config.Load(app, conf); err != nil {
		panic(fmt.Sprintf("config load failed: %v", err))
	}
	return conf
}
