package po

import (
	"testing"
	"time"
)

func TestGetUserLevel(t *testing.T) {
	tests := []struct {
		name          string
		totalConsumed string
		expectedLevel int
	}{
		{"零消费", "0", 1},
		{"零消费字符串", "0.00", 1},
		{"空字符串", "", 1},
		{"999书币", "999.00", 1},
		{"1000书币", "1000.00", 2},
		{"1500书币", "1500.00", 2},
		{"2000书币", "2000.00", 3},
		{"2999书币", "2999.99", 3},
		{"3000书币", "3000.00", 4},
		{"5000书币", "5000.00", 6},
		{"10000书币", "10000.00", 11},
		{"无效格式", "abc", 1},
		{"负数", "-100", 1},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			level := GetUserLevel(tt.totalConsumed)
			if level != tt.expectedLevel {
				t.<PERSON>rf("GetUserLevel(%s) = %d, want %d", tt.totalConsumed, level, tt.expectedLevel)
			}
		})
	}
}

func TestParseDecimalString(t *testing.T) {
	tests := []struct {
		name        string
		input       string
		expected    float64
		expectError bool
	}{
		{"正常数字", "123.45", 123.45, false},
		{"整数", "100", 100.0, false},
		{"零", "0", 0.0, false},
		{"小数", "0.99", 0.99, false},
		{"空字符串", "", 0.0, false},
		{"带空格", " 123.45 ", 123.45, false},
		{"无效格式", "abc", 0.0, true},
		{"多个小数点", "12.34.56", 0.0, true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := ParseDecimalString(tt.input)

			if tt.expectError {
				if err == nil {
					t.Errorf("parseDecimalString(%s) expected error, but got none", tt.input)
				}
			} else {
				if err != nil {
					t.Errorf("parseDecimalString(%s) unexpected error: %v", tt.input, err)
				}
				if result != tt.expected {
					t.Errorf("parseDecimalString(%s) = %f, want %f", tt.input, result, tt.expected)
				}
			}
		})
	}
}

func TestAccountIsVipActive(t *testing.T) {
	now := time.Now()
	future := now.Add(24 * time.Hour)
	past := now.Add(-24 * time.Hour)

	tests := []struct {
		name        string
		account     Account
		expectedVip bool
	}{
		{
			name: "VIP有效",
			account: Account{
				VipExpireTime: &future,
			},
			expectedVip: true,
		},
		{
			name: "VIP已过期",
			account: Account{
				VipExpireTime: &past,
			},
			expectedVip: false,
		},
		{
			name: "无VIP",
			account: Account{
				VipExpireTime: nil,
			},
			expectedVip: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			isVip := tt.account.IsVipActive()
			if isVip != tt.expectedVip {
				t.Errorf("IsVipActive() = %v, want %v", isVip, tt.expectedVip)
			}
		})
	}
}

func TestUserLevelCalculation(t *testing.T) {
	// 测试用户等级计算的边界情况
	testCases := []struct {
		consumed string
		level    int
	}{
		{"0", 1},       // 0书币 = 1级
		{"999", 1},     // 999书币 = 1级
		{"1000", 2},    // 1000书币 = 2级
		{"1999", 2},    // 1999书币 = 2级
		{"2000", 3},    // 2000书币 = 3级
		{"9999", 10},   // 9999书币 = 10级
		{"10000", 11},  // 10000书币 = 11级
	}

	for _, tc := range testCases {
		t.Run("消费"+tc.consumed+"书币", func(t *testing.T) {
			level := GetUserLevel(tc.consumed)
			if level != tc.level {
				t.Errorf("消费%s书币应该是%d级，实际得到%d级", tc.consumed, tc.level, level)
			}
		})
	}
}

// BenchmarkGetUserLevel 性能测试
func BenchmarkGetUserLevel(b *testing.B) {
	for i := 0; i < b.N; i++ {
		GetUserLevel("5000.00")
	}
}

// BenchmarkParseDecimalString 性能测试
func BenchmarkParseDecimalString(b *testing.B) {
	for i := 0; i < b.N; i++ {
		ParseDecimalString("1234.56")
	}
}
