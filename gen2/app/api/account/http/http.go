package http

import (
	"creativematrix.com/beyondreading/gen2/app/api/account/conf"
	"creativematrix.com/beyondreading/gen2/app/api/account/svc"
	"creativematrix.com/beyondreading/pkg/router"
)

var (
	service *svc.AccountSvc
)

func Start(c *conf.Config, s *svc.AccountSvc) {
	service = s
	r := router.Start(c.Base)

	v1 := r.Group("/account")
	{
		// 只提供有限的HTTP接口
		v1.GET("/info", getAccount)     // 获取账户信息
		v1.GET("/logs", getAccountLogs) // 获取账户日志
		v1.POST("/recharge", recharge)  // 充值
	}

	go func() {
		if err := r.Run(c.Port.HTTP); err != nil {
			panic(err)
		}
	}()
}
