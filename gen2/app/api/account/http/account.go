package http

import (
	"creativematrix.com/beyondreading/gen2/app/api/account/model/vo"
	"creativematrix.com/beyondreading/pkg/ecode"
	"github.com/gin-gonic/gin"
)

// getAccount 获取账户信息
func getAccount(c *gin.Context) {
	param := new(vo.GetAccountReq)
	err := c.BindQuery(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	data, err := service.GetAccount(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

// recharge 充值
func recharge(c *gin.Context) {
	param := new(vo.RechargeReq)
	err := c.BindJSON(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	data, err := service.Recharge(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}

// getAccountLogs 获取账户日志
func getAccountLogs(c *gin.Context) {
	param := new(vo.GetAccountLogsReq)
	err := c.BindQuery(param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}

	data, err := service.GetAccountLogs(c, param)
	if err != nil {
		ecode.Back(c).Failure(err)
		return
	}
	ecode.Back(c).SetData(data).Success()
}
