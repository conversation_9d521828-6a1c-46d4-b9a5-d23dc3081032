package svc

import (
	"context"
	"time"

	"creativematrix.com/beyondreading/gen2/app/api/account/model/vo"
	pb "creativematrix.com/beyondreading/gen2/proto/account"
	"creativematrix.com/beyondreading/pkg/ecode"
	"creativematrix.com/beyondreading/pkg/logger"
)

// GetAccount 获取账户信息
func (s *AccountSvc) GetAccount(ctx context.Context, req *vo.GetAccountReq) (*vo.AccountInfoResp, error) {
	pbReq := &pb.GetAccountReq{
		UserId: req.UserId,
	}

	resp, err := s.dao.AccountClient.GetAccount(ctx, pbReq)
	if err != nil {
		logger.LogErrorf("Failed to get account: %v", err)
		return nil, err
	}

	if resp.Code != 200 {
		return nil, ecode.New(int(resp.Code), resp.Message)
	}

	account := resp.Account
	result := &vo.AccountInfoResp{
		AccountId:      account.AccountId,
		UserId:         account.UserId,
		CoinBalance:    account.CoinBalance,
		TotalRecharged: account.TotalRecharged,
		TotalConsumed:  account.TotalConsumed,
		Status:         int(account.Status),
		UserType:       int(account.UserType),
		UserLevel:      int(account.UserLevel),
		IsVip:          account.VipExpireTime > 0 && time.Unix(account.VipExpireTime, 0).After(time.Now()),
		CreatedAt:      time.Unix(account.CreatedAt, 0),
		UpdatedAt:      time.Unix(account.UpdatedAt, 0),
	}

	if account.VipExpireTime > 0 {
		vipExpireTime := time.Unix(account.VipExpireTime, 0)
		result.VipExpireTime = &vipExpireTime
	}

	return result, nil
}

// Recharge 充值
func (s *AccountSvc) Recharge(ctx context.Context, req *vo.RechargeReq) (*vo.RechargeResp, error) {
	pbReq := &pb.RechargeReq{
		UserId:        req.UserId,
		Amount:        req.Amount,
		PaymentMethod: req.PaymentMethod,
		ExchangeRate:  req.ExchangeRate,
	}

	resp, err := s.dao.AccountClient.Recharge(ctx, pbReq)
	if err != nil {
		logger.LogErrorf("Failed to recharge: %v", err)
		return nil, err
	}

	if resp.Code != 200 {
		return nil, ecode.New(int(resp.Code), resp.Message)
	}

	order := resp.Order
	return &vo.RechargeResp{
		OrderId:       order.OrderId,
		AccountId:     order.AccountId,
		UserId:        order.UserId,
		Amount:        order.Amount,
		CoinAmount:    order.CoinAmount,
		ExchangeRate:  order.ExchangeRate,
		PaymentMethod: order.PaymentMethod,
		Status:        int(order.Status),
		CreatedAt:     time.Unix(order.CreatedAt, 0),
	}, nil
}

// GetAccountLogs 获取账户日志
func (s *AccountSvc) GetAccountLogs(ctx context.Context, req *vo.GetAccountLogsReq) (*vo.GetAccountLogsResp, error) {
	pbReq := &pb.GetAccountLogsReq{
		UserId:          req.UserId,
		Page:            req.Page,
		PageSize:        req.PageSize,
		TransactionType: req.TransactionType,
	}

	resp, err := s.dao.AccountClient.GetAccountLogs(ctx, pbReq)
	if err != nil {
		logger.LogErrorf("Failed to get account logs: %v", err)
		return nil, err
	}

	if resp.Code != 200 {
		return nil, ecode.New(int(resp.Code), resp.Message)
	}

	logs := make([]*vo.AccountLogResp, 0, len(resp.Logs))
	for _, log := range resp.Logs {
		logs = append(logs, &vo.AccountLogResp{
			LogId:           log.LogId,
			AccountId:       log.AccountId,
			UserId:          log.UserId,
			TransactionType: log.TransactionType,
			Amount:          log.Amount,
			BalanceBefore:   log.BalanceBefore,
			BalanceAfter:    log.BalanceAfter,
			OrderId:         log.OrderId,
			BookId:          log.BookId,
			ChapterId:       log.ChapterId,
			Description:     log.Description,
			ExtraData:       log.ExtraData,
			CreatedAt:       time.Unix(log.CreatedAt, 0),
		})
	}

	return &vo.GetAccountLogsResp{
		Logs:  logs,
		Total: resp.Total,
	}, nil
}
