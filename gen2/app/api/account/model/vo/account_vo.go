package vo

import "time"

// GetAccountReq 获取账户信息请求
type GetAccountReq struct {
	UserId string `form:"userId" binding:"required" json:"userId"`
}

// AccountInfoResp 账户信息响应
type AccountInfoResp struct {
	AccountId      uint64     `json:"accountId"`
	UserId         string     `json:"userId"`
	CoinBalance    string     `json:"coinBalance"`
	TotalRecharged string     `json:"totalRecharged"`
	TotalConsumed  string     `json:"totalConsumed"`
	Status         int        `json:"status"`
	UserType       int        `json:"userType"`       // 用户类型：1-普通用户，2-VIP用户，3-包月用户
	UserLevel      int        `json:"userLevel"`      // 用户等级
	IsVip          bool       `json:"isVip"`          // 是否VIP
	VipExpireTime  *time.Time `json:"vipExpireTime"`  // VIP过期时间
	CreatedAt      time.Time  `json:"createdAt"`
	UpdatedAt      time.Time  `json:"updatedAt"`
}

// RechargeReq 充值请求
type RechargeReq struct {
	UserId        string `json:"userId" binding:"required"`
	Amount        string `json:"amount" binding:"required"`
	PaymentMethod string `json:"paymentMethod" binding:"required"`
	ExchangeRate  string `json:"exchangeRate,omitempty"`
}

// RechargeResp 充值响应
type RechargeResp struct {
	OrderId       string    `json:"orderId"`
	AccountId     uint64    `json:"accountId"`
	UserId        string    `json:"userId"`
	Amount        string    `json:"amount"`
	CoinAmount    string    `json:"coinAmount"`
	ExchangeRate  string    `json:"exchangeRate"`
	PaymentMethod string    `json:"paymentMethod"`
	Status        int       `json:"status"`
	CreatedAt     time.Time `json:"createdAt"`
}

// GetAccountLogsReq 获取账户日志请求
type GetAccountLogsReq struct {
	UserId          string `form:"userId" binding:"required"`
	Page            int32  `form:"page,omitempty"`
	PageSize        int32  `form:"pageSize,omitempty"`
	TransactionType string `form:"transactionType,omitempty"`
}

// AccountLogResp 账户日志响应
type AccountLogResp struct {
	LogId           uint64    `json:"logId"`
	AccountId       uint64    `json:"accountId"`
	UserId          string    `json:"userId"`
	TransactionType string    `json:"transactionType"`
	Amount          string    `json:"amount"`
	BalanceBefore   string    `json:"balanceBefore"`
	BalanceAfter    string    `json:"balanceAfter"`
	OrderId         string    `json:"orderId,omitempty"`
	BookId          string    `json:"bookId,omitempty"`
	ChapterId       string    `json:"chapterId,omitempty"`
	Description     string    `json:"description,omitempty"`
	ExtraData       string    `json:"extraData,omitempty"`
	CreatedAt       time.Time `json:"createdAt"`
}

// GetAccountLogsResp 获取账户日志响应
type GetAccountLogsResp struct {
	Logs  []*AccountLogResp `json:"logs"`
	Total int64             `json:"total"`
}
