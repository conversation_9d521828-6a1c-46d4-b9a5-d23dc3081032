package dao

import (
	"context"
	"fmt"

	"creativematrix.com/beyondreading/gen2/app/api/account/conf"
	accountapi "creativematrix.com/beyondreading/gen2/app/base/account/api"
	accountpb "creativematrix.com/beyondreading/gen2/proto/account"
	"creativematrix.com/beyondreading/pkg/elastic"
	"creativematrix.com/beyondreading/pkg/mongo"
	"creativematrix.com/beyondreading/pkg/redis"
)

type Dao struct {
	cache         redis.Redis
	schema        map[string]*mongo.Model
	es            *elastic.Elastic
	AccountClient accountpb.AccountClient
	summerConn    *mongo.Connection
}

var summerTable = []string{}

func (d *Dao) Ping(ctx context.Context) (err error) {
	return
}

func (d *Dao) Close() {
	if d.es != nil {
		d.es.Close()
	}
}

func Load(c *conf.Config) *Dao {
	//summerConn := mongo.Connect(c.MongodbBooks)
	//schema := map[string]*mongo.Model{}
	//
	//for _, name := range summerTable {
	//	schema[name] = summerConn.Model(name)
	//}

	// 初始化Account gRPC客户端
	rpcAccount, err := accountapi.NewClient(c.Base)
	if err != nil {
		panic(fmt.Sprintf("failed to create account client: %v", err))
	}

	return &Dao{
		//schema:        schema,
		AccountClient: rpcAccount,
		cache:         redis.Load(c.Cache),
		//summerConn:    summerConn,
	}
}
