package vo

import "time"

// PurchaseChapterReq 购买章节请求
type PurchaseChapterReq struct {
	UserId       uint64  `json:"userId" binding:"required"`
	BookId       string  `json:"bookId" binding:"required"`
	ChapterOrder uint32  `json:"chapterOrder" binding:"required"`
	CoinAmount   float64 `json:"coinAmount" binding:"required"` // purchase.proto中是double，对应float64
}

// PurchaseChapterResp 购买章节响应
type PurchaseChapterResp struct {
	OrderId      string    `json:"orderId"`
	AccountId    uint64    `json:"accountId"`
	UserId       uint64    `json:"userId"`
	OrderType    string    `json:"orderType"`
	BookId       string    `json:"bookId"`
	BookName     string    `json:"bookName,omitempty"`
	ChapterId    string    `json:"chapterId,omitempty"`
	ChapterTitle string    `json:"chapterTitle,omitempty"`
	ChapterOrder uint32    `json:"chapterOrder"`
	CoinAmount   float64   `json:"coinAmount"` // purchase.proto中是double，对应float64
	Status       int32     `json:"status"`     // purchase.proto中是int32
	CreatedAt    time.Time `json:"createdAt"`
}

// PurchaseMonthlyReq 购买包月请求
type PurchaseMonthlyReq struct {
	UserId       uint64  `json:"userId" binding:"required"`
	BookId       string  `json:"bookId" binding:"required"`
	BookName     string  `json:"bookName" binding:"required"`
	CoinAmount   float64 `json:"coinAmount" binding:"required"` // 改为float64类型
	DurationDays int32   `json:"durationDays,omitempty"`        // 包月天数，默认30天
}

// PurchaseMonthlyResp 购买包月响应
type PurchaseMonthlyResp struct {
	OrderId      string     `json:"orderId"`
	AccountId    uint64     `json:"accountId"`
	UserId       uint64     `json:"userId"`
	OrderType    string     `json:"orderType"`
	BookId       string     `json:"bookId"`
	BookName     string     `json:"bookName"`
	CoinAmount   float64    `json:"coinAmount"` // 改为float64类型
	DurationDays int32      `json:"durationDays"`
	StartTime    *time.Time `json:"startTime"`
	EndTime      *time.Time `json:"endTime"`
	Status       int32      `json:"status"` // 改为int32类型
	CreatedAt    time.Time  `json:"createdAt"`
}

// PurchaseVipReq 购买VIP请求
type PurchaseVipReq struct {
	UserId       uint64  `json:"userId" binding:"required"`
	CoinAmount   float64 `json:"coinAmount" binding:"required"` // 改为float64类型
	DurationDays int32   `json:"durationDays,omitempty"`        // VIP天数，默认30天
}

// PurchaseVipResp 购买VIP响应
type PurchaseVipResp struct {
	OrderId      string     `json:"orderId"`
	AccountId    uint64     `json:"accountId"`
	UserId       uint64     `json:"userId"`
	OrderType    string     `json:"orderType"`
	CoinAmount   float64    `json:"coinAmount"` // 改为float64类型
	DurationDays int32      `json:"durationDays"`
	StartTime    *time.Time `json:"startTime"`
	EndTime      *time.Time `json:"endTime"`
	Status       int32      `json:"status"` // 改为int32类型
	CreatedAt    time.Time  `json:"createdAt"`
}

// GetPurchaseOrdersReq 获取购买订单列表请求
type GetPurchaseOrdersReq struct {
	UserId    uint64 `form:"userId" binding:"required"`
	Page      int32  `form:"page,omitempty"`
	PageSize  int32  `form:"pageSize,omitempty"`
	OrderType string `form:"orderType,omitempty"` // 可选，筛选订单类型：chapter, monthly, vip
}

// PurchaseOrderResp 购买订单响应
type PurchaseOrderResp struct {
	OrderId      string     `json:"orderId"`
	AccountId    uint64     `json:"accountId"`
	UserId       uint64     `json:"userId"`
	OrderType    string     `json:"orderType"`
	BookId       string     `json:"bookId,omitempty"`
	BookName     string     `json:"bookName,omitempty"`
	ChapterId    string     `json:"chapterId,omitempty"`
	ChapterTitle string     `json:"chapterTitle,omitempty"`
	ChapterOrder uint32     `json:"chapterOrder,omitempty"`
	CoinAmount   float64    `json:"coinAmount"` // 改为float64类型
	DurationDays int32      `json:"durationDays,omitempty"`
	StartTime    *time.Time `json:"startTime,omitempty"`
	EndTime      *time.Time `json:"endTime,omitempty"`
	Status       int32      `json:"status"` // 改为int32类型
	CreatedAt    time.Time  `json:"createdAt"`
	UpdatedAt    time.Time  `json:"updatedAt"`
}

// GetPurchaseOrdersResp 获取购买订单列表响应
type GetPurchaseOrdersResp struct {
	Orders []*PurchaseOrderResp `json:"orders"`
	Total  int64                `json:"total"`
}

// CheckChapterPurchasedReq 检查章节购买状态请求
type CheckChapterPurchasedReq struct {
	UserId       uint64 `form:"userId" binding:"required"`
	BookId       string `form:"bookId" binding:"required"`
	ChapterOrder uint32 `form:"chapterOrder" binding:"required"`
}

// CheckChapterPurchasedResp 检查章节购买状态响应
type CheckChapterPurchasedResp struct {
	IsPurchased  bool       `json:"isPurchased"`
	PurchasedAt  *time.Time `json:"purchasedAt,omitempty"`
	IsMonthly    bool       `json:"isMonthly"`    // 是否通过包月获得
	OrderId      string     `json:"orderId,omitempty"`
	OrderType    string     `json:"orderType,omitempty"`
}

// CheckMonthlyStatusReq 检查包月状态请求
type CheckMonthlyStatusReq struct {
	UserId uint64 `form:"userId" binding:"required"`
	BookId string `form:"bookId" binding:"required"`
}

// CheckMonthlyStatusResp 检查包月状态响应
type CheckMonthlyStatusResp struct {
	IsActive  bool       `json:"isActive"`
	StartTime *time.Time `json:"startTime,omitempty"`
	EndTime   *time.Time `json:"endTime,omitempty"`
	OrderId   string     `json:"orderId,omitempty"`
}

// GetPurchasedChaptersReq 获取已购买章节列表请求
type GetPurchasedChaptersReq struct {
	UserId   uint64 `form:"userId" binding:"required"`
	BookId   string `form:"bookId" binding:"required"`
	Page     int32  `form:"page,omitempty"`
	PageSize int32  `form:"pageSize,omitempty"`
}

// ChapterPurchaseInfo 章节购买信息
type ChapterPurchaseInfo struct {
	OrderId      string    `json:"orderId"`
	ChapterId    string    `json:"chapterId,omitempty"`
	ChapterTitle string    `json:"chapterTitle,omitempty"`
	ChapterOrder uint32    `json:"chapterOrder"`
	CoinAmount   float64   `json:"coinAmount"` // 改为float64类型
	PurchasedAt  time.Time `json:"purchasedAt"`
	IsMonthly    bool      `json:"isMonthly"` // 是否通过包月获得
}

// GetPurchasedChaptersResp 获取已购买章节列表响应
type GetPurchasedChaptersResp struct {
	Chapters []*ChapterPurchaseInfo `json:"chapters"`
	Total    int64                  `json:"total"`
}
