# Purchase API Service

Purchase API 服务提供购买相关的HTTP接口，作为Purchase gRPC服务的HTTP网关。

## 项目结构

```
gen2/app/api/purchase/
├── api/                    # API常量定义
│   └── api.go
├── cmd/                    # 主程序入口
│   └── main.go
├── conf/                   # 配置管理
│   └── conf.go
├── dao/                    # 数据访问层（gRPC客户端）
│   └── dao.go
├── docs/                   # API文档
│   └── api.md
├── http/                   # HTTP处理器
│   ├── http.go
│   └── http_test.go
├── model/                  # 数据模型
│   └── vo/
│       └── purchase_vo.go
├── svc/                    # 服务层
│   ├── svc.go
│   └── purchase.go
└── README.md
```

## 功能特性

### 购买功能
- **章节购买**: 购买指定书籍的单个章节
- **包月购买**: 购买指定书籍的包月服务
- **VIP购买**: 购买全站VIP服务

### 查询功能
- **订单查询**: 获取用户的购买订单列表
- **购买状态检查**: 检查章节是否已购买
- **包月状态检查**: 检查书籍包月状态
- **已购章节查询**: 获取已购买的章节列表

### 系统功能
- **健康检查**: 服务健康状态监控
- **错误处理**: 统一的错误响应格式
- **参数验证**: 请求参数自动验证

## 技术栈

- **Web框架**: Gin
- **配置管理**: TOML
- **日志**: 自定义日志组件
- **链路追踪**: Jaeger（可选）
- **通信协议**: gRPC客户端 + HTTP服务端
- **数据格式**: JSON

## 快速开始

### 1. 环境准备

确保以下服务已启动：
- Purchase gRPC服务（端口9583）
- Account gRPC服务（端口8183）

### 2. 配置文件

编辑 `gen2/config/api-purchase.toml`：

```toml
[base]
name = "api-purchase"
version = "1.0.0"
env = "development"

[log]
level = "info"

[port]
http = ":8080"

[grpc.purchase]
addr = "127.0.0.1:9583"
timeout = "30s"
```

### 3. 启动服务

```bash
cd gen2/app/api/purchase/cmd
go run main.go
```

服务将在 `http://localhost:8080` 启动。

### 4. 验证服务

```bash
# 健康检查
curl http://localhost:8080/health

# API健康检查
curl http://localhost:8080/api/v1/purchase/ping
```

## API 接口

### 购买接口

#### 购买章节
```bash
POST /api/v1/purchase/chapter
Content-Type: application/json

{
  "userId": 123,
  "bookId": "book_456",
  "chapterOrder": 1,
  "coinAmount": "5.00"
}
```

#### 购买包月
```bash
POST /api/v1/purchase/monthly
Content-Type: application/json

{
  "userId": 123,
  "bookId": "book_456",
  "bookName": "测试书籍",
  "coinAmount": "30.00",
  "durationDays": 30
}
```

#### 购买VIP
```bash
POST /api/v1/purchase/vip
Content-Type: application/json

{
  "userId": 123,
  "coinAmount": "100.00",
  "durationDays": 30
}
```

### 查询接口

#### 获取订单列表
```bash
GET /api/v1/purchase/orders?userId=123&page=1&pageSize=10&orderType=chapter
```

#### 检查章节购买状态
```bash
GET /api/v1/purchase/chapter/check?userId=123&bookId=book_456&chapterOrder=1
```

#### 检查包月状态
```bash
GET /api/v1/purchase/monthly/check?userId=123&bookId=book_456
```

#### 获取已购章节
```bash
GET /api/v1/purchase/chapters?userId=123&bookId=book_456&page=1&pageSize=10
```

详细的API文档请参考 [docs/api.md](docs/api.md)。

## 开发指南

### 添加新接口

1. **定义VO模型** (`model/vo/purchase_vo.go`)
2. **实现服务层** (`svc/purchase.go`)
3. **添加HTTP处理器** (`http/http.go`)
4. **注册路由** (`http/http.go` 中的 `RegisterRoutes`)
5. **编写测试** (`http/http_test.go`)
6. **更新文档** (`docs/api.md`)

### 错误处理

所有接口都使用统一的错误响应格式：

```json
{
  "code": 400,
  "message": "错误描述",
  "error": "详细错误信息"
}
```

### 日志记录

使用统一的日志组件：

```go
logger.LogInfof("Info message: %s", info)
logger.LogErrorf("Error occurred: %v", err)
```

## 测试

### 运行测试

```bash
# 运行所有测试
go test ./...

# 运行HTTP测试
go test ./http/

# 运行测试并显示覆盖率
go test -cover ./...
```

### 测试说明

- HTTP测试需要gRPC服务支持，可能需要Mock
- 参数验证测试可以独立运行
- 集成测试需要完整的服务环境

## 部署

### Docker部署

```dockerfile
FROM golang:1.19-alpine AS builder
WORKDIR /app
COPY . .
RUN go build -o purchase-api ./gen2/app/api/purchase/cmd

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/purchase-api .
COPY --from=builder /app/gen2/config/api-purchase.toml ./config/
CMD ["./purchase-api"]
```

### 环境变量

可以通过环境变量覆盖配置：

- `HTTP_PORT`: HTTP服务端口
- `GRPC_PURCHASE_ADDR`: Purchase gRPC服务地址
- `LOG_LEVEL`: 日志级别

## 监控

### 健康检查

- `GET /health`: 基础健康检查
- `GET /api/v1/purchase/ping`: 服务健康检查（包含gRPC连接）

### 指标监控

支持Prometheus指标（可选）：
- HTTP请求数量和延迟
- gRPC调用数量和延迟
- 错误率统计

### 链路追踪

支持Jaeger链路追踪（可选）：
- HTTP请求链路
- gRPC调用链路
- 错误链路追踪

## 故障排除

### 常见问题

1. **gRPC连接失败**
   - 检查Purchase gRPC服务是否启动
   - 验证配置中的地址和端口
   - 检查网络连接

2. **参数验证失败**
   - 检查请求格式是否正确
   - 验证必填字段是否提供
   - 确认数据类型匹配

3. **服务启动失败**
   - 检查配置文件是否存在
   - 验证端口是否被占用
   - 查看日志错误信息

### 日志分析

```bash
# 查看服务日志
tail -f /var/log/purchase-api.log

# 过滤错误日志
grep "ERROR" /var/log/purchase-api.log
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交代码
4. 编写测试
5. 更新文档
6. 提交Pull Request

## 许可证

本项目采用MIT许可证。
