#!/bin/bash

# Purchase API 服务启动脚本

echo "🚀 启动 Purchase API 服务..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
SCRIPT_DIR=$(cd "$(dirname "$0")" && pwd)
PROJECT_ROOT=$(cd "$SCRIPT_DIR/.." && pwd)
CMD_DIR="$PROJECT_ROOT/cmd"

echo -e "${BLUE}📁 项目目录: $PROJECT_ROOT${NC}"

# 检查配置文件
CONFIG_FILE="$PROJECT_ROOT/../../../config/api-purchase.toml"
if [ ! -f "$CONFIG_FILE" ]; then
    echo -e "${RED}❌ 配置文件不存在: $CONFIG_FILE${NC}"
    echo "请确保配置文件存在于 gen2/config/api-purchase.toml"
    exit 1
fi

echo -e "${GREEN}✅ 找到配置文件: $CONFIG_FILE${NC}"

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo -e "${RED}❌ Go未安装，请先安装Go${NC}"
    exit 1
fi

GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
echo -e "${GREEN}✅ Go版本: $GO_VERSION${NC}"

# 检查依赖服务
echo ""
echo "🔍 检查依赖服务..."

# 检查Purchase gRPC服务
PURCHASE_GRPC_ADDR="127.0.0.1:9583"
if nc -z 127.0.0.1 9583 2>/dev/null; then
    echo -e "${GREEN}✅ Purchase gRPC服务运行中 ($PURCHASE_GRPC_ADDR)${NC}"
else
    echo -e "${YELLOW}⚠️  Purchase gRPC服务未运行 ($PURCHASE_GRPC_ADDR)${NC}"
    echo "   请先启动Purchase gRPC服务"
fi

# 检查端口占用
HTTP_PORT="8080"
if lsof -i :$HTTP_PORT > /dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  端口 $HTTP_PORT 已被占用${NC}"
    echo "   请修改配置文件中的端口或停止占用该端口的进程"
    read -p "是否继续启动？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
else
    echo -e "${GREEN}✅ 端口 $HTTP_PORT 可用${NC}"
fi

# 进入命令目录
cd "$CMD_DIR"

echo ""
echo "🔧 编译项目..."

# 编译项目
if go build -o purchase-api main.go; then
    echo -e "${GREEN}✅ 编译成功${NC}"
else
    echo -e "${RED}❌ 编译失败${NC}"
    exit 1
fi

echo ""
echo "🚀 启动服务..."

# 设置环境变量
export GO111MODULE=on

# 启动服务
echo -e "${BLUE}📋 服务信息:${NC}"
echo "   - 服务名称: Purchase API"
echo "   - HTTP端口: $HTTP_PORT"
echo "   - 配置文件: $CONFIG_FILE"
echo "   - 工作目录: $CMD_DIR"
echo ""

echo -e "${GREEN}🎉 Purchase API 服务启动中...${NC}"
echo "   HTTP服务: http://localhost:$HTTP_PORT"
echo "   健康检查: http://localhost:$HTTP_PORT/health"
echo "   API文档: http://localhost:$HTTP_PORT/api/v1/purchase/ping"
echo ""
echo "按 Ctrl+C 停止服务"
echo ""

# 启动服务
./purchase-api

echo ""
echo -e "${YELLOW}👋 服务已停止${NC}"
