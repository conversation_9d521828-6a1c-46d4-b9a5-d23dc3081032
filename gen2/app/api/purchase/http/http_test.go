package http

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"creativematrix.com/beyondreading/gen2/app/api/purchase/conf"
	"creativematrix.com/beyondreading/gen2/app/api/purchase/model/vo"
	"creativematrix.com/beyondreading/gen2/app/api/purchase/svc"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func setupTestRouter() (*gin.Engine, *PurchaseHandler) {
	gin.SetMode(gin.TestMode)

	// 创建测试配置，按照chapters模式
	config := &conf.Config{}
	// 注意：这里需要真实的gRPC服务或者Mock
	// 为了测试，我们先创建一个基本的服务实例
	service := svc.Load(config)

	handler := NewPurchaseHandler(service)

	r := gin.New()
	handler.RegisterRoutes(r)

	return r, handler
}

func TestPurchaseChapter(t *testing.T) {
	router, _ := setupTestRouter()

	// 测试请求数据
	req := vo.PurchaseChapterReq{
		UserId:       123,
		BookId:       "book_456",
		ChapterOrder: 1,
		CoinAmount:   5.00, // 改为float64类型
	}

	jsonData, _ := json.Marshal(req)

	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("POST", "/api/v1/purchase/chapter", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Content-Type", "application/json")

	router.ServeHTTP(w, httpReq)

	// 注意：由于没有真实的gRPC服务，这个测试可能会失败
	// 在实际环境中，需要启动gRPC服务或使用Mock
	t.Logf("Response Status: %d", w.Code)
	t.Logf("Response Body: %s", w.Body.String())
}

func TestPurchaseChapterInvalidRequest(t *testing.T) {
	router, _ := setupTestRouter()

	// 测试无效请求数据（缺少必填字段）
	req := vo.PurchaseChapterReq{
		BookId:       "book_456",
		ChapterOrder: 1,
		CoinAmount:   5.00, // 改为float64类型
		// 缺少 UserId
	}

	jsonData, _ := json.Marshal(req)

	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("POST", "/api/v1/purchase/chapter", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Content-Type", "application/json")

	router.ServeHTTP(w, httpReq)

	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, float64(400), response["code"])
}

func TestGetPurchaseOrders(t *testing.T) {
	router, _ := setupTestRouter()

	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("GET", "/api/v1/purchase/orders?userId=123&page=1&pageSize=10", nil)

	router.ServeHTTP(w, httpReq)

	t.Logf("Response Status: %d", w.Code)
	t.Logf("Response Body: %s", w.Body.String())
}

func TestGetPurchaseOrdersInvalidUserId(t *testing.T) {
	router, _ := setupTestRouter()

	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("GET", "/api/v1/purchase/orders?page=1&pageSize=10", nil)

	router.ServeHTTP(w, httpReq)

	assert.Equal(t, http.StatusBadRequest, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, float64(400), response["code"])
}

func TestCheckChapterPurchased(t *testing.T) {
	router, _ := setupTestRouter()

	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("GET", "/api/v1/purchase/chapter/check?userId=123&bookId=book_456&chapterOrder=1", nil)

	router.ServeHTTP(w, httpReq)

	t.Logf("Response Status: %d", w.Code)
	t.Logf("Response Body: %s", w.Body.String())
}

func TestPing(t *testing.T) {
	router, _ := setupTestRouter()

	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("GET", "/api/v1/purchase/ping", nil)

	router.ServeHTTP(w, httpReq)

	t.Logf("Response Status: %d", w.Code)
	t.Logf("Response Body: %s", w.Body.String())
}

func TestPurchaseMonthly(t *testing.T) {
	router, _ := setupTestRouter()

	req := vo.PurchaseMonthlyReq{
		UserId:       123,
		BookId:       "book_456",
		BookName:     "测试书籍",
		CoinAmount:   30.00, // 改为float64类型
		DurationDays: 30,
	}

	jsonData, _ := json.Marshal(req)

	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("POST", "/api/v1/purchase/monthly", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Content-Type", "application/json")

	router.ServeHTTP(w, httpReq)

	t.Logf("Response Status: %d", w.Code)
	t.Logf("Response Body: %s", w.Body.String())
}

func TestPurchaseVip(t *testing.T) {
	router, _ := setupTestRouter()

	req := vo.PurchaseVipReq{
		UserId:       123,
		CoinAmount:   100.00, // 改为float64类型
		DurationDays: 30,
	}

	jsonData, _ := json.Marshal(req)

	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("POST", "/api/v1/purchase/vip", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Content-Type", "application/json")

	router.ServeHTTP(w, httpReq)

	t.Logf("Response Status: %d", w.Code)
	t.Logf("Response Body: %s", w.Body.String())
}

func TestCheckMonthlyStatus(t *testing.T) {
	router, _ := setupTestRouter()

	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("GET", "/api/v1/purchase/monthly/check?userId=123&bookId=book_456", nil)

	router.ServeHTTP(w, httpReq)

	t.Logf("Response Status: %d", w.Code)
	t.Logf("Response Body: %s", w.Body.String())
}

func TestGetPurchasedChapters(t *testing.T) {
	router, _ := setupTestRouter()

	w := httptest.NewRecorder()
	httpReq, _ := http.NewRequest("GET", "/api/v1/purchase/chapters?userId=123&bookId=book_456&page=1&pageSize=10", nil)

	router.ServeHTTP(w, httpReq)

	t.Logf("Response Status: %d", w.Code)
	t.Logf("Response Body: %s", w.Body.String())
}
