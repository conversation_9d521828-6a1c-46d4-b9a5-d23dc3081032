package http

import (
	"net/http"
	"strconv"

	"creativematrix.com/beyondreading/gen2/app/api/purchase/model/vo"
	"creativematrix.com/beyondreading/gen2/app/api/purchase/svc"
	"creativematrix.com/beyondreading/pkg/logger"
	"github.com/gin-gonic/gin"
)

// PurchaseHandler 购买服务HTTP处理器
type PurchaseHandler struct {
	svc *svc.PurchaseSvc
}

// NewPurchaseHandler 创建购买服务HTTP处理器
func NewPurchaseHandler(svc *svc.PurchaseSvc) *PurchaseHandler {
	return &PurchaseHandler{
		svc: svc,
	}
}

// RegisterRoutes 注册路由
func (h *PurchaseHandler) RegisterRoutes(r *gin.Engine) {
	api := r.Group("/api/v1/purchase")
	{
		// 购买相关接口
		api.POST("/chapter", h.PurchaseChapter)
		api.POST("/monthly", h.PurchaseMonthly)
		api.POST("/vip", h.PurchaseVip)

		// 查询相关接口
		api.GET("/orders", h.GetPurchaseOrders)
		api.GET("/chapter/check", h.CheckChapterPurchased)
		api.GET("/monthly/check", h.CheckMonthlyStatus)
		api.GET("/chapters", h.GetPurchasedChapters)

		// 健康检查
		api.GET("/ping", h.Ping)
	}
}

// PurchaseChapter 购买章节
func (h *PurchaseHandler) PurchaseChapter(c *gin.Context) {
	var req vo.PurchaseChapterReq
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.LogErrorf("Invalid request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	resp, err := h.svc.PurchaseChapter(c.Request.Context(), &req)
	if err != nil {
		logger.LogErrorf("Purchase chapter failed: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Purchase chapter failed",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Purchase chapter successful",
		"data":    resp,
	})
}

// PurchaseMonthly 购买包月
func (h *PurchaseHandler) PurchaseMonthly(c *gin.Context) {
	var req vo.PurchaseMonthlyReq
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.LogErrorf("Invalid request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	resp, err := h.svc.PurchaseMonthly(c.Request.Context(), &req)
	if err != nil {
		logger.LogErrorf("Purchase monthly failed: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Purchase monthly failed",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Purchase monthly successful",
		"data":    resp,
	})
}

// PurchaseVip 购买VIP
func (h *PurchaseHandler) PurchaseVip(c *gin.Context) {
	var req vo.PurchaseVipReq
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.LogErrorf("Invalid request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	resp, err := h.svc.PurchaseVip(c.Request.Context(), &req)
	if err != nil {
		logger.LogErrorf("Purchase VIP failed: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Purchase VIP failed",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Purchase VIP successful",
		"data":    resp,
	})
}

// GetPurchaseOrders 获取购买订单列表
func (h *PurchaseHandler) GetPurchaseOrders(c *gin.Context) {
	var req vo.GetPurchaseOrdersReq
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.LogErrorf("Invalid request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	resp, err := h.svc.GetPurchaseOrders(c.Request.Context(), &req)
	if err != nil {
		logger.LogErrorf("Get purchase orders failed: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Get purchase orders failed",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Get purchase orders successful",
		"data":    resp,
	})
}

// CheckChapterPurchased 检查章节购买状态
func (h *PurchaseHandler) CheckChapterPurchased(c *gin.Context) {
	var req vo.CheckChapterPurchasedReq
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.LogErrorf("Invalid request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	resp, err := h.svc.CheckChapterPurchased(c.Request.Context(), &req)
	if err != nil {
		logger.LogErrorf("Check chapter purchased failed: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Check chapter purchased failed",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Check chapter purchased successful",
		"data":    resp,
	})
}

// CheckMonthlyStatus 检查包月状态
func (h *PurchaseHandler) CheckMonthlyStatus(c *gin.Context) {
	var req vo.CheckMonthlyStatusReq
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.LogErrorf("Invalid request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	resp, err := h.svc.CheckMonthlyStatus(c.Request.Context(), &req)
	if err != nil {
		logger.LogErrorf("Check monthly status failed: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Check monthly status failed",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Check monthly status successful",
		"data":    resp,
	})
}

// GetPurchasedChapters 获取已购买章节列表
func (h *PurchaseHandler) GetPurchasedChapters(c *gin.Context) {
	var req vo.GetPurchasedChaptersReq
	if err := c.ShouldBindQuery(&req); err != nil {
		logger.LogErrorf("Invalid request: %v", err)
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	resp, err := h.svc.GetPurchasedChapters(c.Request.Context(), &req)
	if err != nil {
		logger.LogErrorf("Get purchased chapters failed: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Get purchased chapters failed",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Get purchased chapters successful",
		"data":    resp,
	})
}

// Ping 健康检查
func (h *PurchaseHandler) Ping(c *gin.Context) {
	err := h.svc.Ping(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Service unavailable",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "pong",
	})
}
