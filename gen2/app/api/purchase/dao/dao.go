package dao

import (
	"context"

	"creativematrix.com/beyondreading/gen2/app/api/purchase/conf"
	purchaseapi "creativematrix.com/beyondreading/gen2/app/base/purchase/api"
	accountapi "creativematrix.com/beyondreading/gen2/app/base/account/api"
	accountpb "creativematrix.com/beyondreading/gen2/proto/account"
	pb "creativematrix.com/beyondreading/gen2/proto/purchase"
)

// Dao 数据访问层，按照chapters模式
type Dao struct {
	PurchaseClient pb.PurchaseClient
	AccountClient  accountpb.AccountClient
}

// Load 创建DAO实例，直接调用base中的NewClient方法
func Load(c *conf.Config) *Dao {
	// 直接调用base/purchase/api中的NewClient方法
	purchaseClient, err := purchaseapi.NewClient(c.Base)
	if err != nil {
		panic(err)
	}

	// 直接调用base/account/api中的NewClient方法
	accountClient, err := accountapi.NewClient(c.Base)
	if err != nil {
		panic(err)
	}

	return &Dao{
		PurchaseClient: purchaseClient,
		AccountClient:  accountClient,
	}
}

// Ping 健康检查
func (d *Dao) Ping(ctx context.Context) error {
	// 这里可以调用Purchase服务的健康检查接口
	return nil
}
