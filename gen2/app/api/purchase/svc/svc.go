package svc

import (
	"context"

	"creativematrix.com/beyondreading/gen2/app/api/purchase/conf"
	"creativematrix.com/beyondreading/gen2/app/api/purchase/dao"
)

type PurchaseSvc struct {
	conf *conf.Config
	dao  *dao.Dao
}

func Load(c *conf.Config) *PurchaseSvc {
	svc := &PurchaseSvc{
		conf: c,
		dao:  dao.Load(c),
	}

	return svc
}

func (s *PurchaseSvc) Ping(ctx context.Context) error {
	return s.dao.Ping(ctx)
}

// Close 方法不再需要，因为使用API客户端而不是直接连接
