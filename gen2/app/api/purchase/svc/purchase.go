package svc

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"creativematrix.com/beyondreading/gen2/app/api/purchase/model/vo"
	pb "creativematrix.com/beyondreading/gen2/proto/purchase"
	accountpb "creativematrix.com/beyondreading/gen2/proto/account"
	"creativematrix.com/beyondreading/pkg/logger"
)

// PurchaseChapter 购买章节
func (s *PurchaseSvc) PurchaseChapter(ctx context.Context, req *vo.PurchaseChapterReq) (*vo.PurchaseChapterResp, error) {
	// 参数验证
	if err := validateUserId(req.UserId); err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}
	if err := validateBookId(req.BookId); err != nil {
		return nil, fmt.Errorf("invalid book ID: %w", err)
	}
	if err := validateChapterOrder(req.ChapterOrder); err != nil {
		return nil, fmt.Errorf("invalid chapter order: %w", err)
	}
	if err := validateCoinAmount(req.CoinAmount); err != nil {
		return nil, fmt.Errorf("invalid coin amount: %w", err)
	}

	// 调用gRPC服务
	grpcReq := &pb.PurchaseChapterReq{
		UserId:       req.UserId,
		BookId:       req.BookId,
		ChapterOrder: req.ChapterOrder,
		CoinAmount:   req.CoinAmount, // purchase.proto中是double，直接传递float64
	}

	grpcResp, err := s.dao.PurchaseClient.PurchaseChapter(ctx, grpcReq)
	if err != nil {
		logger.LogErrorf("Failed to purchase chapter: %v", err)
		return nil, fmt.Errorf("purchase chapter failed: %w", err)
	}

	if grpcResp.Code != 200 {
		return nil, fmt.Errorf("purchase chapter failed: %s", grpcResp.Message)
	}

	// 转换响应
	resp := &vo.PurchaseChapterResp{
		OrderId:      grpcResp.Order.OrderId,
		AccountId:    grpcResp.Order.AccountId,
		UserId:       grpcResp.Order.UserId,
		OrderType:    grpcResp.Order.OrderType,
		BookId:       grpcResp.Order.BookId,
		BookName:     grpcResp.Order.BookName,
		ChapterId:    grpcResp.Order.ChapterId,
		ChapterTitle: grpcResp.Order.ChapterTitle,
		ChapterOrder: grpcResp.Order.ChapterOrder,
		CoinAmount:   grpcResp.Order.CoinAmount, // purchase.proto中是double，直接赋值
		Status:       grpcResp.Order.Status,     // purchase.proto中是int32，直接赋值
		CreatedAt:    time.Unix(grpcResp.Order.CreatedAt, 0),
	}

	return resp, nil
}

// PurchaseMonthly 购买包月
func (s *PurchaseSvc) PurchaseMonthly(ctx context.Context, req *vo.PurchaseMonthlyReq) (*vo.PurchaseMonthlyResp, error) {
	// 参数验证
	if err := validateUserId(req.UserId); err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}
	if err := validateBookId(req.BookId); err != nil {
		return nil, fmt.Errorf("invalid book ID: %w", err)
	}
	if err := validateCoinAmount(req.CoinAmount); err != nil {
		return nil, fmt.Errorf("invalid coin amount: %w", err)
	}
	if req.DurationDays > 0 {
		if err := validateDurationDays(req.DurationDays); err != nil {
			return nil, fmt.Errorf("invalid duration days: %w", err)
		}
	}

	// 调用gRPC服务
	grpcReq := &pb.PurchaseMonthlyReq{
		UserId:       req.UserId,
		BookId:       req.BookId,
		BookName:     req.BookName,
		CoinAmount:   req.CoinAmount, // purchase.proto中是double，直接传递float64
		DurationDays: req.DurationDays,
	}

	grpcResp, err := s.dao.PurchaseClient.PurchaseMonthly(ctx, grpcReq)
	if err != nil {
		logger.LogErrorf("Failed to purchase monthly: %v", err)
		return nil, fmt.Errorf("purchase monthly failed: %w", err)
	}

	if grpcResp.Code != 200 {
		return nil, fmt.Errorf("purchase monthly failed: %s", grpcResp.Message)
	}

	// 转换响应
	resp := &vo.PurchaseMonthlyResp{
		OrderId:      grpcResp.Order.OrderId,
		AccountId:    grpcResp.Order.AccountId,
		UserId:       grpcResp.Order.UserId,
		OrderType:    grpcResp.Order.OrderType,
		BookId:       grpcResp.Order.BookId,
		BookName:     grpcResp.Order.BookName,
		CoinAmount:   grpcResp.Order.CoinAmount, // purchase.proto中是double，直接赋值
		DurationDays: grpcResp.Order.DurationDays,
		Status:       grpcResp.Order.Status, // purchase.proto中是int32，直接赋值
		CreatedAt:    time.Unix(grpcResp.Order.CreatedAt, 0),
	}

	if grpcResp.Order.StartTime > 0 {
		startTime := time.Unix(grpcResp.Order.StartTime, 0)
		resp.StartTime = &startTime
	}

	if grpcResp.Order.EndTime > 0 {
		endTime := time.Unix(grpcResp.Order.EndTime, 0)
		resp.EndTime = &endTime
	}

	return resp, nil
}

// PurchaseVip 购买VIP
func (s *PurchaseSvc) PurchaseVip(ctx context.Context, req *vo.PurchaseVipReq) (*vo.PurchaseVipResp, error) {
	// 参数验证
	if err := validateUserId(req.UserId); err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}
	if err := validateCoinAmount(req.CoinAmount); err != nil {
		return nil, fmt.Errorf("invalid coin amount: %w", err)
	}
	if req.DurationDays > 0 {
		if err := validateDurationDays(req.DurationDays); err != nil {
			return nil, fmt.Errorf("invalid duration days: %w", err)
		}
	}

	// 调用gRPC服务
	grpcReq := &pb.PurchaseVipReq{
		UserId:       req.UserId,
		CoinAmount:   req.CoinAmount, // purchase.proto中是double，直接传递float64
		DurationDays: req.DurationDays,
	}

	grpcResp, err := s.dao.PurchaseClient.PurchaseVip(ctx, grpcReq)
	if err != nil {
		logger.LogErrorf("Failed to purchase VIP: %v", err)
		return nil, fmt.Errorf("purchase VIP failed: %w", err)
	}

	if grpcResp.Code != 200 {
		return nil, fmt.Errorf("purchase VIP failed: %s", grpcResp.Message)
	}

	// 转换响应
	resp := &vo.PurchaseVipResp{
		OrderId:      grpcResp.Order.OrderId,
		AccountId:    grpcResp.Order.AccountId,
		UserId:       grpcResp.Order.UserId,
		OrderType:    grpcResp.Order.OrderType,
		CoinAmount:   grpcResp.Order.CoinAmount, // purchase.proto中是double，直接赋值
		DurationDays: grpcResp.Order.DurationDays,
		Status:       grpcResp.Order.Status, // purchase.proto中是int32，直接赋值
		CreatedAt:    time.Unix(grpcResp.Order.CreatedAt, 0),
	}

	if grpcResp.Order.StartTime > 0 {
		startTime := time.Unix(grpcResp.Order.StartTime, 0)
		resp.StartTime = &startTime
	}

	if grpcResp.Order.EndTime > 0 {
		endTime := time.Unix(grpcResp.Order.EndTime, 0)
		resp.EndTime = &endTime
	}

	return resp, nil
}

// GetPurchaseOrders 获取购买订单列表
func (s *PurchaseSvc) GetPurchaseOrders(ctx context.Context, req *vo.GetPurchaseOrdersReq) (*vo.GetPurchaseOrdersResp, error) {
	// 参数验证
	if err := validateUserId(req.UserId); err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}

	// 设置默认分页参数
	page := req.Page
	if page <= 0 {
		page = 1
	}
	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 100 {
		pageSize = 100 // 限制最大页面大小
	}

	// 调用gRPC服务
	grpcReq := &pb.GetPurchaseOrdersReq{
		UserId:    req.UserId,
		Page:      page,
		PageSize:  pageSize,
		OrderType: req.OrderType,
	}

	grpcResp, err := s.dao.PurchaseClient.GetPurchaseOrders(ctx, grpcReq)
	if err != nil {
		logger.LogErrorf("Failed to get purchase orders: %v", err)
		return nil, fmt.Errorf("get purchase orders failed: %w", err)
	}

	if grpcResp.Code != 200 {
		return nil, fmt.Errorf("get purchase orders failed: %s", grpcResp.Message)
	}

	// 转换响应
	orders := make([]*vo.PurchaseOrderResp, 0, len(grpcResp.Orders))
	for _, order := range grpcResp.Orders {
		orderResp := &vo.PurchaseOrderResp{
			OrderId:      order.OrderId,
			AccountId:    order.AccountId,
			UserId:       order.UserId,
			OrderType:    order.OrderType,
			BookId:       order.BookId,
			BookName:     order.BookName,
			ChapterId:    order.ChapterId,
			ChapterTitle: order.ChapterTitle,
			ChapterOrder: order.ChapterOrder,
			CoinAmount:   order.CoinAmount, // 现在是float64类型，直接赋值
			DurationDays: order.DurationDays,
			Status:       order.Status, // 现在是int32类型，直接赋值
			CreatedAt:    time.Unix(order.CreatedAt, 0),
			UpdatedAt:    time.Unix(order.UpdatedAt, 0),
		}

		if order.StartTime > 0 {
			startTime := time.Unix(order.StartTime, 0)
			orderResp.StartTime = &startTime
		}

		if order.EndTime > 0 {
			endTime := time.Unix(order.EndTime, 0)
			orderResp.EndTime = &endTime
		}

		orders = append(orders, orderResp)
	}

	resp := &vo.GetPurchaseOrdersResp{
		Orders: orders,
		Total:  grpcResp.Total,
	}

	return resp, nil
}

// CheckChapterPurchased 检查章节购买状态
func (s *PurchaseSvc) CheckChapterPurchased(ctx context.Context, req *vo.CheckChapterPurchasedReq) (*vo.CheckChapterPurchasedResp, error) {
	// 参数验证
	if err := validateUserId(req.UserId); err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}
	if err := validateBookId(req.BookId); err != nil {
		return nil, fmt.Errorf("invalid book ID: %w", err)
	}
	if err := validateChapterOrder(req.ChapterOrder); err != nil {
		return nil, fmt.Errorf("invalid chapter order: %w", err)
	}

	// 调用gRPC服务
	grpcReq := &pb.CheckChapterPurchasedReq{
		UserId:       req.UserId,
		BookId:       req.BookId,
		ChapterOrder: req.ChapterOrder,
	}

	grpcResp, err := s.dao.PurchaseClient.CheckChapterPurchased(ctx, grpcReq)
	if err != nil {
		logger.LogErrorf("Failed to check chapter purchased: %v", err)
		return nil, fmt.Errorf("check chapter purchased failed: %w", err)
	}

	if grpcResp.Code != 200 {
		return nil, fmt.Errorf("check chapter purchased failed: %s", grpcResp.Message)
	}

	// 转换响应
	resp := &vo.CheckChapterPurchasedResp{
		IsPurchased: grpcResp.IsPurchased,
		IsMonthly:   grpcResp.IsMonthly,
	}

	if grpcResp.PurchasedAt > 0 {
		purchasedAt := time.Unix(grpcResp.PurchasedAt, 0)
		resp.PurchasedAt = &purchasedAt
	}

	return resp, nil
}

// CheckMonthlyStatus 检查包月状态
func (s *PurchaseSvc) CheckMonthlyStatus(ctx context.Context, req *vo.CheckMonthlyStatusReq) (*vo.CheckMonthlyStatusResp, error) {
	// 参数验证
	if err := validateUserId(req.UserId); err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}
	if err := validateBookId(req.BookId); err != nil {
		return nil, fmt.Errorf("invalid book ID: %w", err)
	}

	// 调用gRPC服务
	grpcReq := &pb.CheckMonthlyStatusReq{
		UserId: req.UserId,
		BookId: req.BookId,
	}

	grpcResp, err := s.dao.PurchaseClient.CheckMonthlyStatus(ctx, grpcReq)
	if err != nil {
		logger.LogErrorf("Failed to check monthly status: %v", err)
		return nil, fmt.Errorf("check monthly status failed: %w", err)
	}

	if grpcResp.Code != 200 {
		return nil, fmt.Errorf("check monthly status failed: %s", grpcResp.Message)
	}

	// 转换响应
	resp := &vo.CheckMonthlyStatusResp{
		IsActive: grpcResp.IsActive,
	}

	if grpcResp.StartTime > 0 {
		startTime := time.Unix(grpcResp.StartTime, 0)
		resp.StartTime = &startTime
	}

	if grpcResp.EndTime > 0 {
		endTime := time.Unix(grpcResp.EndTime, 0)
		resp.EndTime = &endTime
	}

	return resp, nil
}

// GetPurchasedChapters 获取已购买章节列表
func (s *PurchaseSvc) GetPurchasedChapters(ctx context.Context, req *vo.GetPurchasedChaptersReq) (*vo.GetPurchasedChaptersResp, error) {
	// 参数验证
	if err := validateUserId(req.UserId); err != nil {
		return nil, fmt.Errorf("invalid user ID: %w", err)
	}
	if err := validateBookId(req.BookId); err != nil {
		return nil, fmt.Errorf("invalid book ID: %w", err)
	}

	// 设置默认分页参数
	page := req.Page
	if page <= 0 {
		page = 1
	}
	pageSize := req.PageSize
	if pageSize <= 0 {
		pageSize = 20
	}
	if pageSize > 100 {
		pageSize = 100 // 限制最大页面大小
	}

	// 调用gRPC服务
	grpcReq := &pb.GetPurchasedChaptersReq{
		UserId:   req.UserId,
		BookId:   req.BookId,
		Page:     page,
		PageSize: pageSize,
	}

	grpcResp, err := s.dao.PurchaseClient.GetPurchasedChapters(ctx, grpcReq)
	if err != nil {
		logger.LogErrorf("Failed to get purchased chapters: %v", err)
		return nil, fmt.Errorf("get purchased chapters failed: %w", err)
	}

	if grpcResp.Code != 200 {
		return nil, fmt.Errorf("get purchased chapters failed: %s", grpcResp.Message)
	}

	// 转换响应
	chapters := make([]*vo.ChapterPurchaseInfo, 0, len(grpcResp.Chapters))
	for _, chapter := range grpcResp.Chapters {
		chapterInfo := &vo.ChapterPurchaseInfo{
			OrderId:      chapter.OrderId,
			ChapterId:    chapter.ChapterId,
			ChapterTitle: chapter.ChapterTitle,
			ChapterOrder: chapter.ChapterOrder,
			CoinAmount:   chapter.CoinAmount, // 现在是float64类型，直接赋值
			PurchasedAt:  time.Unix(chapter.PurchasedAt, 0),
			IsMonthly:    chapter.IsMonthly,
		}

		chapters = append(chapters, chapterInfo)
	}

	resp := &vo.GetPurchasedChaptersResp{
		Chapters: chapters,
		Total:    grpcResp.Total,
	}

	return resp, nil
}
