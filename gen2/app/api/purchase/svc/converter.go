package svc

import (
	"fmt"
	"strconv"
)

// convertFloat64ToString 将float64转换为字符串，用于调用account服务
// account.proto中的amount字段是string类型
func convertFloat64ToString(amount float64) string {
	return fmt.Sprintf("%.2f", amount)
}

// convertStringToFloat64 将字符串转换为float64，用于处理account服务的响应
// account.proto中的balance字段是string类型
func convertStringToFloat64(amountStr string) (float64, error) {
	return strconv.ParseFloat(amountStr, 64)
}

// validateCoinAmount 验证书币金额是否有效
func validateCoinAmount(amount float64) error {
	if amount <= 0 {
		return fmt.Errorf("coin amount must be greater than 0, got: %.2f", amount)
	}
	if amount > 999999.99 {
		return fmt.Errorf("coin amount too large, max: 999999.99, got: %.2f", amount)
	}
	return nil
}

// validateUserId 验证用户ID是否有效
func validateUserId(userId uint64) error {
	if userId == 0 {
		return fmt.Erro<PERSON>("user ID cannot be 0")
	}
	return nil
}

// validateBookId 验证书籍ID是否有效
func validateBookId(bookId string) error {
	if bookId == "" {
		return fmt.Errorf("book ID cannot be empty")
	}
	if len(bookId) > 64 {
		return fmt.Errorf("book ID too long, max: 64, got: %d", len(bookId))
	}
	return nil
}

// validateChapterOrder 验证章节序号是否有效
func validateChapterOrder(chapterOrder uint32) error {
	if chapterOrder == 0 {
		return fmt.Errorf("chapter order must be greater than 0")
	}
	if chapterOrder > 99999 {
		return fmt.Errorf("chapter order too large, max: 99999, got: %d", chapterOrder)
	}
	return nil
}

// validateDurationDays 验证持续天数是否有效
func validateDurationDays(days int32) error {
	if days <= 0 {
		return fmt.Errorf("duration days must be greater than 0, got: %d", days)
	}
	if days > 3650 { // 最多10年
		return fmt.Errorf("duration days too large, max: 3650, got: %d", days)
	}
	return nil
}
