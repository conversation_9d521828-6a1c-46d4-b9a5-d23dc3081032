# Purchase API 文档

## 概述

Purchase API 提供购买相关的HTTP接口，包括章节购买、包月购买、VIP购买以及相关的查询功能。

## 基础信息

- **Base URL**: `http://localhost:8080`
- **API Version**: `v1`
- **Content-Type**: `application/json`

## 接口列表

### 1. 购买章节

**POST** `/api/v1/purchase/chapter`

购买指定书籍的章节。

#### 请求参数

```json
{
  "userId": 123,
  "bookId": "book_456",
  "chapterOrder": 1,
  "coinAmount": 5.00
}
```

#### 响应示例

```json
{
  "code": 200,
  "message": "Purchase chapter successful",
  "data": {
    "orderId": "****************",
    "accountId": 1001,
    "userId": 123,
    "orderType": "chapter",
    "bookId": "book_456",
    "bookName": "测试书籍",
    "chapterId": "chapter_001",
    "chapterTitle": "第一章",
    "chapterOrder": 1,
    "coinAmount": 5.00,
    "status": 2,
    "createdAt": "2023-12-01T12:00:01Z"
  }
}
```

### 2. 购买包月

**POST** `/api/v1/purchase/monthly`

购买指定书籍的包月服务。

#### 请求参数

```json
{
  "userId": 123,
  "bookId": "book_456",
  "bookName": "测试书籍",
  "coinAmount": 30.00,
  "durationDays": 30
}
```

#### 响应示例

```json
{
  "code": 200,
  "message": "Purchase monthly successful",
  "data": {
    "orderId": "****************",
    "accountId": 1001,
    "userId": 123,
    "orderType": "monthly",
    "bookId": "book_456",
    "bookName": "测试书籍",
    "coinAmount": 30.00,
    "durationDays": 30,
    "startTime": "2023-12-01T12:00:01Z",
    "endTime": "2023-12-31T12:00:01Z",
    "status": 2,
    "createdAt": "2023-12-01T12:00:01Z"
  }
}
```

### 3. 购买VIP

**POST** `/api/v1/purchase/vip`

购买VIP服务。

#### 请求参数

```json
{
  "userId": 123,
  "coinAmount": 100.00,
  "durationDays": 30
}
```

#### 响应示例

```json
{
  "code": 200,
  "message": "Purchase VIP successful",
  "data": {
    "orderId": "****************",
    "accountId": 1001,
    "userId": 123,
    "orderType": "vip",
    "coinAmount": 100.00,
    "durationDays": 30,
    "startTime": "2023-12-01T12:00:01Z",
    "endTime": "2023-12-31T12:00:01Z",
    "status": 2,
    "createdAt": "2023-12-01T12:00:01Z"
  }
}
```

### 4. 获取购买订单列表

**GET** `/api/v1/purchase/orders`

获取用户的购买订单列表。

#### 请求参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| userId | uint64 | 是 | 用户ID |
| page | int32 | 否 | 页码，默认1 |
| pageSize | int32 | 否 | 每页数量，默认20 |
| orderType | string | 否 | 订单类型筛选：chapter, monthly, vip |

#### 响应示例

```json
{
  "code": 200,
  "message": "Get purchase orders successful",
  "data": {
    "orders": [
      {
        "orderId": "****************",
        "accountId": 1001,
        "userId": 123,
        "orderType": "chapter",
        "bookId": "book_456",
        "bookName": "测试书籍",
        "chapterId": "chapter_001",
        "chapterTitle": "第一章",
        "chapterOrder": 1,
        "coinAmount": 5.00,
        "status": 2,
        "createdAt": "2023-12-01T12:00:01Z",
        "updatedAt": "2023-12-01T12:00:01Z"
      }
    ],
    "total": 1
  }
}
```

### 5. 检查章节购买状态

**GET** `/api/v1/purchase/chapter/check`

检查指定章节是否已购买。

#### 请求参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| userId | uint64 | 是 | 用户ID |
| bookId | string | 是 | 书籍ID |
| chapterOrder | uint32 | 是 | 章节序号 |

#### 响应示例

```json
{
  "code": 200,
  "message": "Check chapter purchased successful",
  "data": {
    "isPurchased": true,
    "purchasedAt": "2023-12-01T12:00:01Z",
    "isMonthly": false,
    "orderId": "****************",
    "orderType": "chapter"
  }
}
```

### 6. 检查包月状态

**GET** `/api/v1/purchase/monthly/check`

检查指定书籍的包月状态。

#### 请求参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| userId | uint64 | 是 | 用户ID |
| bookId | string | 是 | 书籍ID |

#### 响应示例

```json
{
  "code": 200,
  "message": "Check monthly status successful",
  "data": {
    "isActive": true,
    "startTime": "2023-12-01T12:00:01Z",
    "endTime": "2023-12-31T12:00:01Z",
    "orderId": "****************"
  }
}
```

### 7. 获取已购买章节列表

**GET** `/api/v1/purchase/chapters`

获取用户已购买的章节列表。

#### 请求参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| userId | uint64 | 是 | 用户ID |
| bookId | string | 是 | 书籍ID |
| page | int32 | 否 | 页码，默认1 |
| pageSize | int32 | 否 | 每页数量，默认20 |

#### 响应示例

```json
{
  "code": 200,
  "message": "Get purchased chapters successful",
  "data": {
    "chapters": [
      {
        "orderId": "****************",
        "chapterId": "chapter_001",
        "chapterTitle": "第一章",
        "chapterOrder": 1,
        "coinAmount": 5.00,
        "purchasedAt": "2023-12-01T12:00:01Z",
        "isMonthly": false
      }
    ],
    "total": 1
  }
}
```

### 8. 健康检查

**GET** `/api/v1/purchase/ping`

检查服务健康状态。

#### 响应示例

```json
{
  "code": 200,
  "message": "pong"
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 500 | 服务器内部错误 |

## 错误响应格式

```json
{
  "code": 400,
  "message": "Invalid request parameters",
  "error": "详细错误信息"
}
```

## 使用示例

### cURL 示例

```bash
# 购买章节
curl -X POST http://localhost:8080/api/v1/purchase/chapter \
  -H "Content-Type: application/json" \
  -d '{
    "userId": 123,
    "bookId": "book_456",
    "chapterOrder": 1,
    "coinAmount": 5.00
  }'

# 检查章节购买状态
curl "http://localhost:8080/api/v1/purchase/chapter/check?userId=123&bookId=book_456&chapterOrder=1"

# 获取购买订单列表
curl "http://localhost:8080/api/v1/purchase/orders?userId=123&page=1&pageSize=10"
```

## 注意事项

1. 所有金额字段使用float64数字格式，保持精度
2. 时间字段使用ISO 8601格式
3. 用户ID使用uint64类型
4. 分页参数page从1开始
5. 购买操作需要确保用户有足够的书币余额
6. coinAmount字段在请求和响应中都是数字类型，不是字符串
