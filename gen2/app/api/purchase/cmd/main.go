package main

import (
	"context"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"creativematrix.com/beyondreading/gen2/app/api/purchase/api"
	"creativematrix.com/beyondreading/gen2/app/api/purchase/conf"
	purchaseHttp "creativematrix.com/beyondreading/gen2/app/api/purchase/http"
	"creativematrix.com/beyondreading/gen2/app/api/purchase/svc"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/tracer"
	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	config := conf.Load(api.App)

	// 初始化日志
	logger.InitLog(api.App, config.Log.Level)

	// 初始化链路追踪
	tracer.InitTracing(&config.Base, api.App)

	// 创建服务实例
	service := svc.Load(config)

	// 创建HTTP服务器
	server := setupHTTPServer(config, service)

	// 启动服务器
	go func() {
		logger.LogInfo("Starting HTTP server on %s", config.Port.HTTP)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.LogErrorf("Failed to start HTTP server: %v", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.LogInfo("Shutting down server...")

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		logger.LogErrorf("Server forced to shutdown: %v", err)
	}

	logger.LogInfo("Server exited")
}

// setupHTTPServer 设置HTTP服务器
func setupHTTPServer(config *conf.Config, service *svc.PurchaseSvc) *http.Server {
	// 设置Gin模式
	if config.Log.Level == "debug" {
		gin.SetMode(gin.DebugMode)
	} else {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建路由
	r := gin.New()

	// 添加中间件
	r.Use(gin.Logger())
	r.Use(gin.Recovery())

	// 添加CORS中间件
	r.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	})

	// 注册路由
	purchaseHandler := purchaseHttp.NewPurchaseHandler(service)
	purchaseHandler.RegisterRoutes(r)

	// 添加根路径健康检查
	r.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"service": "purchase-api",
			"version": "1.0.0",
			"status":  "running",
		})
	})

	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status": "healthy",
		})
	})

	// 创建HTTP服务器
	server := &http.Server{
		Addr:    config.Port.HTTP,
		Handler: r,
	}

	return server
}
