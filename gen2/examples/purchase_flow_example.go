package main

import (
	"context"
	"fmt"
	"log"

	"creativematrix.com/beyondreading/gen2/app/common/po"
	accountpb "creativematrix.com/beyondreading/gen2/proto/account"
	purchasepb "creativematrix.com/beyondreading/gen2/proto/purchase"
)

// 这是一个示例，展示完整的购买流程和用户等级更新
func main() {
	// 模拟客户端连接（实际使用时需要建立gRPC连接）
	// accountClient := accountpb.NewAccountClient(conn)
	// purchaseClient := purchasepb.NewPurchaseClient(conn)

	ctx := context.Background()
	userId := "user123"
	bookId := "book456"

	fmt.Println("=== 购买流程示例 ===")

	// 1. 获取用户当前账户信息
	fmt.Println("\n1. 获取用户当前账户信息")
	// accountResp, err := accountClient.GetAccount(ctx, &accountpb.GetAccountReq{
	//     UserId: userId,
	// })
	// 模拟响应
	fmt.Printf("用户ID: %s\n", userId)
	fmt.Printf("当前书币余额: 1000.00\n")
	fmt.Printf("总消费: 500.00\n")
	fmt.Printf("当前等级: %d (消费500书币)\n", po.GetUserLevel("500.00"))

	// 2. 购买章节
	fmt.Println("\n2. 购买章节")
	chapterOrder := uint32(1)
	coinAmount := "50.00"

	fmt.Printf("购买章节 %d，消费 %s 书币\n", chapterOrder, coinAmount)

	// purchaseResp, err := purchaseClient.PurchaseChapter(ctx, &purchasepb.PurchaseChapterReq{
	//     UserId:       userId,
	//     BookId:       bookId,
	//     ChapterOrder: chapterOrder,
	//     CoinAmount:   coinAmount,
	// })

	// 模拟购买成功后的状态
	newTotalConsumed := "550.00" // 500.00 + 50.00
	newBalance := "950.00"       // 1000.00 - 50.00
	newLevel := po.GetUserLevel(newTotalConsumed)

	fmt.Printf("购买成功！\n")
	fmt.Printf("新的书币余额: %s\n", newBalance)
	fmt.Printf("新的总消费: %s\n", newTotalConsumed)
	fmt.Printf("新的用户等级: %d\n", newLevel)

	// 3. 继续购买，达到升级条件
	fmt.Println("\n3. 继续购买多个章节")
	additionalPurchases := []string{"100.00", "200.00", "150.00"} // 总共450书币
	currentConsumed := 550.0

	for i, amount := range additionalPurchases {
		fmt.Printf("\n购买章节 %d，消费 %s 书币\n", i+2, amount)

		// 解析金额
		amountFloat, _ := po.ParseDecimalString(amount)
		currentConsumed += amountFloat

		newTotalConsumedStr := fmt.Sprintf("%.2f", currentConsumed)
		newLevel := po.GetUserLevel(newTotalConsumedStr)

		fmt.Printf("累计消费: %s 书币\n", newTotalConsumedStr)
		fmt.Printf("当前等级: %d\n", newLevel)

		// 检查是否升级
		if currentConsumed >= 1000.0 && currentConsumed < 1000.0+amountFloat {
			fmt.Printf("🎉 恭喜！用户升级到 %d 级！\n", newLevel)
		}
	}

	// 4. 展示等级计算逻辑
	fmt.Println("\n4. 用户等级计算规则演示")
	fmt.Println("规则：消费书币数量 / 1000 + 1")
	fmt.Printf("CoinsPerLevel = %d\n", po.CoinsPerLevel)

	testCases := []string{"0", "999", "1000", "1999", "2000", "5000", "10000"}
	for _, consumed := range testCases {
		level := po.GetUserLevel(consumed)
		fmt.Printf("消费 %s 书币 → %d 级\n", consumed, level)
	}

	// 5. 购买包月示例
	fmt.Println("\n5. 购买包月示例")
	monthlyPrice := "300.00"
	fmt.Printf("购买包月，消费 %s 书币\n", monthlyPrice)

	// 模拟包月购买
	monthlyAmount, _ := po.ParseDecimalString(monthlyPrice)
	currentConsumed += monthlyAmount
	finalTotalConsumed := fmt.Sprintf("%.2f", currentConsumed)
	finalLevel := po.GetUserLevel(finalTotalConsumed)

	fmt.Printf("包月购买成功！\n")
	fmt.Printf("最终总消费: %s 书币\n", finalTotalConsumed)
	fmt.Printf("最终用户等级: %d\n", finalLevel)

	fmt.Println("\n=== 购买流程完成 ===")
}

// 模拟数据库更新过程
func simulateDatabaseUpdate(userId, coinAmount, orderType, orderId, bookId, description string) {
	fmt.Printf("\n📝 数据库更新模拟:\n")
	fmt.Printf("  - 扣除用户 %s 的 %s 书币\n", userId, coinAmount)
	fmt.Printf("  - 更新 total_consumed 字段\n")
	fmt.Printf("  - 重新计算 user_level 字段\n")
	fmt.Printf("  - 插入 account_log 记录: %s\n", description)
	fmt.Printf("  - 插入 purchase_order 记录: %s\n", orderId)
}

// 展示Account表更新的SQL
func showAccountUpdateSQL() {
	fmt.Println("\n📊 Account表更新SQL示例:")
	fmt.Println(`
UPDATE account 
SET coin_balance = ?, 
    total_consumed = ?, 
    user_level = ?, 
    updated_at = ? 
WHERE account_id = ?
`)

	fmt.Println("\n📊 Account Log插入SQL示例:")
	fmt.Println(`
INSERT INTO account_log 
(account_id, user_id, transaction_type, amount, balance_before, balance_after, 
 order_id, book_id, chapter_id, description, created_at) 
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
`)
}
