#!/bin/bash

# 检查响应消息是否还有中文的脚本

echo "🔍 检查Account和Purchase服务中的响应消息..."

echo "📋 检查结果："

# 检查是否还有中文消息
echo "❌ 仍包含中文消息的文件："

# 检查Account服务
echo "=== Account服务 ==="
find gen2/app/base/account -name "*.go" -exec grep -l "Message.*[\u4e00-\u9fa5]" {} \; 2>/dev/null | while read file; do
    echo "文件: $file"
    grep "Message.*[\u4e00-\u9fa5]" "$file" 2>/dev/null | sed 's/^/  /'
done

# 检查Purchase服务
echo "=== Purchase服务 ==="
find gen2/app/base/purchase -name "*.go" -exec grep -l "Message.*[\u4e00-\u9fa5]" {} \; 2>/dev/null | while read file; do
    echo "文件: $file"
    grep "Message.*[\u4e00-\u9fa5]" "$file" 2>/dev/null | sed 's/^/  /'
done

echo ""
echo "✅ 英文消息示例："
echo "=== Account服务英文消息 ==="
grep -r "Message.*\".*\"" gen2/app/base/account/svc/ | head -5

echo ""
echo "=== Purchase服务英文消息 ==="
grep -r "Message.*\".*\"" gen2/app/base/purchase/svc/ | head -5

echo ""
echo "📊 统计信息："
echo "Account服务响应消息总数: $(grep -r "Message.*\".*\"" gen2/app/base/account/svc/ | wc -l)"
echo "Purchase服务响应消息总数: $(grep -r "Message.*\".*\"" gen2/app/base/purchase/svc/ | wc -l)"

echo ""
echo "✨ 检查完成！"
