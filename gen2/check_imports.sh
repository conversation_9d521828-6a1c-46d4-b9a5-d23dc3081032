#!/bin/bash

# 检查import路径是否正确的脚本

echo "🔍 检查gen2目录下的import路径..."

# 查找所有Go文件中的import路径
echo "📋 检查结果："

# 检查是否有遗漏gen2路径的import
echo "❌ 错误的import路径（缺少gen2）："
find gen2 -name "*.go" -exec grep -l "creativematrix.com/beyondreading/app/" {} \; | while read file; do
    echo "文件: $file"
    grep "creativematrix.com/beyondreading/app/" "$file" | sed 's/^/  /'
done

find gen2 -name "*.go" -exec grep -l "creativematrix.com/beyondreading/proto/" {} \; | while read file; do
    echo "文件: $file"
    grep "creativematrix.com/beyondreading/proto/" "$file" | sed 's/^/  /'
done

echo ""
echo "✅ 正确的import路径（包含gen2）："
find gen2 -name "*.go" -exec grep -l "creativematrix.com/beyondreading/gen2/" {} \; | wc -l | xargs echo "包含正确路径的文件数量:"

echo ""
echo "📊 统计信息："
echo "总Go文件数量: $(find gen2 -name "*.go" | wc -l)"
echo "包含gen2路径的文件数量: $(find gen2 -name "*.go" -exec grep -l "creativematrix.com/beyondreading/gen2/" {} \; | wc -l)"

echo ""
echo "🎯 proto文件go_package检查："
echo "account.proto: $(grep 'go_package' gen2/proto/account/account.proto)"
echo "purchase.proto: $(grep 'go_package' gen2/proto/purchase/purchase.proto)"

echo ""
echo "✨ 检查完成！"
