-- 数据库迁移脚本：将user_id字段从varchar改为bigint unsigned
-- 执行前请备份数据库！

USE beyondreading_test;

-- 开始事务
START TRANSACTION;

-- 1. 创建临时表用于数据转换
CREATE TABLE `account_temp` LIKE `account`;
ALTER TABLE `account_temp` MODIFY `user_id` bigint unsigned NOT NULL COMMENT '用户ID';

CREATE TABLE `account_log_temp` LIKE `account_log`;
ALTER TABLE `account_log_temp` MODIFY `user_id` bigint unsigned NOT NULL COMMENT '用户ID';

CREATE TABLE `recharge_order_temp` LIKE `recharge_order`;
ALTER TABLE `recharge_order_temp` MODIFY `user_id` bigint unsigned NOT NULL COMMENT '用户ID';

CREATE TABLE `purchase_order_temp` LIKE `purchase_order`;
ALTER TABLE `purchase_order_temp` MODIFY `user_id` bigint unsigned NOT NULL COMMENT '用户ID';

-- 2. 迁移数据（假设原来的user_id是数字字符串）
-- 注意：这里假设原来的user_id可以转换为数字，如果不能请手动处理

-- 迁移account表数据
INSERT INTO `account_temp` 
SELECT 
    account_id,
    CAST(user_id AS UNSIGNED) as user_id,
    coin_balance,
    total_recharged,
    total_consumed,
    user_type,
    user_level,
    vip_expire_time,
    monthly_expire_time,
    status,
    created_at,
    updated_at
FROM `account` 
WHERE user_id REGEXP '^[0-9]+$';  -- 只迁移纯数字的user_id

-- 迁移account_log表数据
INSERT INTO `account_log_temp`
SELECT 
    log_id,
    account_id,
    CAST(user_id AS UNSIGNED) as user_id,
    log_type,
    amount,
    balance_before,
    balance_after,
    order_type,
    order_id,
    book_id,
    chapter_id,
    description,
    created_at
FROM `account_log`
WHERE user_id REGEXP '^[0-9]+$';

-- 迁移recharge_order表数据
INSERT INTO `recharge_order_temp`
SELECT 
    order_id,
    account_id,
    CAST(user_id AS UNSIGNED) as user_id,
    amount,
    coin_amount,
    payment_method,
    payment_id,
    status,
    paid_at,
    created_at,
    updated_at
FROM `recharge_order`
WHERE user_id REGEXP '^[0-9]+$';

-- 迁移purchase_order表数据
INSERT INTO `purchase_order_temp`
SELECT 
    order_id,
    account_id,
    CAST(user_id AS UNSIGNED) as user_id,
    order_type,
    book_id,
    book_name,
    chapter_id,
    chapter_title,
    chapter_order,
    coin_amount,
    duration_days,
    start_time,
    end_time,
    status,
    created_at,
    updated_at
FROM `purchase_order`
WHERE user_id REGEXP '^[0-9]+$';

-- 3. 检查数据迁移结果
SELECT 'account表迁移结果' as table_name, 
       (SELECT COUNT(*) FROM account) as original_count,
       (SELECT COUNT(*) FROM account_temp) as migrated_count;

SELECT 'account_log表迁移结果' as table_name,
       (SELECT COUNT(*) FROM account_log) as original_count,
       (SELECT COUNT(*) FROM account_log_temp) as migrated_count;

SELECT 'recharge_order表迁移结果' as table_name,
       (SELECT COUNT(*) FROM recharge_order) as original_count,
       (SELECT COUNT(*) FROM recharge_order_temp) as migrated_count;

SELECT 'purchase_order表迁移结果' as table_name,
       (SELECT COUNT(*) FROM purchase_order) as original_count,
       (SELECT COUNT(*) FROM purchase_order_temp) as migrated_count;

-- 4. 如果数据检查无误，执行以下步骤替换原表
-- 注意：请先确认上面的数据检查结果正确！

-- 备份原表
RENAME TABLE `account` TO `account_backup`;
RENAME TABLE `account_log` TO `account_log_backup`;
RENAME TABLE `recharge_order` TO `recharge_order_backup`;
RENAME TABLE `purchase_order` TO `purchase_order_backup`;

-- 使用新表
RENAME TABLE `account_temp` TO `account`;
RENAME TABLE `account_log_temp` TO `account_log`;
RENAME TABLE `recharge_order_temp` TO `recharge_order`;
RENAME TABLE `purchase_order_temp` TO `purchase_order`;

-- 5. 重新创建索引（如果需要）
-- account表索引
ALTER TABLE `account` ADD UNIQUE KEY `uk_user_id` (`user_id`);
ALTER TABLE `account` ADD KEY `idx_user_type` (`user_type`);
ALTER TABLE `account` ADD KEY `idx_status` (`status`);
ALTER TABLE `account` ADD KEY `idx_created_at` (`created_at`);

-- account_log表索引
ALTER TABLE `account_log` ADD KEY `idx_account_id` (`account_id`);
ALTER TABLE `account_log` ADD KEY `idx_user_id` (`user_id`);
ALTER TABLE `account_log` ADD KEY `idx_log_type` (`log_type`);
ALTER TABLE `account_log` ADD KEY `idx_order_id` (`order_id`);
ALTER TABLE `account_log` ADD KEY `idx_created_at` (`created_at`);

-- recharge_order表索引
ALTER TABLE `recharge_order` ADD KEY `idx_account_id` (`account_id`);
ALTER TABLE `recharge_order` ADD KEY `idx_user_id` (`user_id`);
ALTER TABLE `recharge_order` ADD KEY `idx_status` (`status`);
ALTER TABLE `recharge_order` ADD KEY `idx_created_at` (`created_at`);

-- purchase_order表索引
ALTER TABLE `purchase_order` ADD KEY `idx_account_id` (`account_id`);
ALTER TABLE `purchase_order` ADD KEY `idx_user_id` (`user_id`);
ALTER TABLE `purchase_order` ADD KEY `idx_order_type` (`order_type`);
ALTER TABLE `purchase_order` ADD KEY `idx_book_id` (`book_id`);
ALTER TABLE `purchase_order` ADD KEY `idx_chapter_id` (`chapter_id`);
ALTER TABLE `purchase_order` ADD KEY `idx_status` (`status`);
ALTER TABLE `purchase_order` ADD KEY `idx_created_at` (`created_at`);
ALTER TABLE `purchase_order` ADD UNIQUE KEY `uk_user_book_chapter` (`user_id`, `book_id`, `chapter_order`);

-- 提交事务
COMMIT;

-- 6. 验证迁移结果
SELECT 'Migration completed successfully!' as status;

-- 显示新表结构
DESCRIBE `account`;
DESCRIBE `account_log`;
DESCRIBE `recharge_order`;
DESCRIBE `purchase_order`;

-- 显示数据样例
SELECT 'account表数据样例' as info;
SELECT * FROM `account` LIMIT 5;

SELECT 'account_log表数据样例' as info;
SELECT * FROM `account_log` LIMIT 5;

-- 注意事项：
-- 1. 执行前请备份数据库
-- 2. 确保原user_id字段的值都是有效的数字
-- 3. 如果有非数字的user_id，需要先处理这些数据
-- 4. 迁移完成后，可以删除备份表：
--    DROP TABLE account_backup, account_log_backup, recharge_order_backup, purchase_order_backup;
-- 5. 更新应用程序代码以使用新的user_id类型
