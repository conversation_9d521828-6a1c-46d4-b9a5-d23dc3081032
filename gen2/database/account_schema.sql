-- Account微服务数据库表结构

-- 账户表
DROP TABLE IF EXISTS `account`
CREATE TABLE `account` (
  `account_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `coin_balance` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '书币余额',
  `total_recharged` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '总充值金额',
  `total_consumed` decimal(15,2) NOT NULL DEFAULT '0.00' COMMENT '总消费金额',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '账户状态：1-正常，2-冻结，3-注销',
  `user_type` tinyint NOT NULL DEFAULT '1' COMMENT '用户类型：1-普通用户，2-VIP用户，3-包月用户',
  `user_level` int NOT NULL DEFAULT '1' COMMENT '用户等级（根据消费书币计算，1000书币一级）',
  `vip_expire_time` timestamp NULL DEFAULT NULL COMMENT 'VIP过期时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`account_id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='账户表';

-- 账户日志表
DROP TABLE IF EXISTS `account_log`
CREATE TABLE `account_log` (
  `log_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `account_id` bigint unsigned NOT NULL COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `transaction_type` varchar(32) NOT NULL COMMENT '交易类型：recharge, purchase_chapter, purchase_monthly, purchase_vip, refund',
  `amount` decimal(15,2) NOT NULL COMMENT '变动金额（正数为增加，负数为减少）',
  `balance_before` decimal(15,2) NOT NULL COMMENT '变动前余额',
  `balance_after` decimal(15,2) NOT NULL COMMENT '变动后余额',
  `order_id` varchar(64) DEFAULT NULL COMMENT '关联订单ID',
  `book_id` varchar(64) DEFAULT NULL COMMENT '书籍ID',
  `chapter_id` varchar(64) DEFAULT NULL COMMENT '章节ID',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `extra_data` json DEFAULT NULL COMMENT '额外数据（JSON格式）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_transaction_type` (`transaction_type`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_book_id` (`book_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='账户日志表';

-- 充值订单表
DROP TABLE IF EXISTS `recharge_order`
CREATE TABLE `recharge_order` (
  `order_id` varchar(64) NOT NULL COMMENT '订单ID',
  `account_id` bigint unsigned NOT NULL COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `amount` decimal(15,2) NOT NULL COMMENT '充值金额（人民币）',
  `coin_amount` decimal(15,2) NOT NULL COMMENT '获得书币数量',
  `exchange_rate` decimal(10,4) NOT NULL DEFAULT '1.0000' COMMENT '兑换比例',
  `payment_method` varchar(32) NOT NULL COMMENT '支付方式：alipay, wechat, apple, bank',
  `payment_order_id` varchar(128) DEFAULT NULL COMMENT '第三方支付订单ID',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '订单状态：1-待支付，2-支付成功，3-支付失败，4-已退款',
  `paid_at` timestamp NULL DEFAULT NULL COMMENT '支付时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_payment_method` (`payment_method`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='充值订单表';

-- 购买订单表
DROP TABLE IF EXISTS `purchase_order`
CREATE TABLE `purchase_order` (
  `order_id` varchar(64) NOT NULL COMMENT '订单ID',
  `account_id` bigint unsigned NOT NULL COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `order_type` varchar(32) NOT NULL COMMENT '订单类型：chapter, monthly, vip',
  `book_id` varchar(64) DEFAULT NULL COMMENT '书籍ID',
  `book_name` varchar(128) DEFAULT NULL COMMENT '书籍名称',
  `chapter_id` varchar(64) DEFAULT NULL COMMENT '章节ID',
  `chapter_title` varchar(128) DEFAULT NULL COMMENT '章节标题',
  `chapter_order` int unsigned DEFAULT NULL COMMENT '章节序号',
  `coin_amount` decimal(15,2) NOT NULL COMMENT '消费书币数量',
  `duration_days` int DEFAULT NULL COMMENT '有效期天数（包月、VIP使用）',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '订单状态：1-待支付，2-支付成功，3-支付失败',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`order_id`),
  KEY `idx_account_id` (`account_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_order_type` (`order_type`),
  KEY `idx_book_id` (`book_id`),
  KEY `idx_chapter_id` (`chapter_id`),
  KEY `idx_chapter_order` (`chapter_order`),
  KEY `idx_status` (`status`),
  KEY `idx_start_time` (`start_time`),
  KEY `idx_end_time` (`end_time`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_user_book_chapter` (`user_id`, `book_id`, `chapter_order`),
  KEY `idx_user_book_monthly` (`user_id`, `book_id`, `order_type`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='购买订单表';

-- 包月状态表（用于快速查询包月状态）
DROP TABLE IF EXISTS `monthly_status`
CREATE TABLE `monthly_status` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `book_id` varchar(64) NOT NULL COMMENT '书籍ID',
  `order_id` varchar(64) NOT NULL COMMENT '订单ID',
  `start_time` timestamp NOT NULL COMMENT '开始时间',
  `end_time` timestamp NOT NULL COMMENT '结束时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-有效，2-过期',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_book` (`user_id`, `book_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_end_time` (`end_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='包月状态表';

-- 初始化数据
INSERT INTO `account` (`user_id`, `coin_balance`, `total_recharged`, `total_consumed`, `status`, `user_type`, `user_level`, `created_at`, `updated_at`) VALUES
(123, '1000.00', '1000.00', '0.00', 1, 1, 1, NOW(), NOW()),
(456, '500.00', '500.00', '100.00', 1, 1, 1, NOW(), NOW());

-- 索引优化建议
-- 1. account表按user_id分片
-- 2. account_log表按user_id和时间分片
-- 3. purchase_order表按user_id分片
-- 4. 定期清理过期的日志数据
