
DROP TABLE IF EXISTS `orders00`;
CREATE TABLE `orders00` (
  `id` varchar(36) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '订单ID',
  `locationId` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0 其他地址',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '0未发出, 1等待接单， 2 超时， 3 已接单， 4 开始订单， 5 等待支付， 6 支付完成， 7 等待评论， 8 订单完成， 9 订单开始前取消 10 订单开始后取消',
  `orderType` tinyint NOT NULL DEFAULT '0' COMMENT '普通独享订单 0， 优享订单 1，到家接送订单 2， 共享订单 3，',
  `reserveTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '预约时间',
  `createTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `demanderId` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '需求发起方ID',
  `supplierId` varchar(64) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '服务提供方ID',
  `otherAddr` varchar(256) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL DEFAULT '' COMMENT '其他详细地址',
  `contactMobile` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '需求方联系电话',
  `emergencyMobile` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '需求方紧急联系电话',
  `supplierMobile` varchar(32) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NOT NULL COMMENT '服务方联系电话',
  `price` float NOT NULL DEFAULT '0.0' COMMENT '每小时服务单价',
  `serviceHours` float NOT NULL DEFAULT '0.0' COMMENT '服务小时数',
  `payType` tinyint(4) NOT NULL DEFAULT '0' COMMENT '支付方式 0 支付宝， 1 微信， 2 其他',
  PRIMARY KEY (`id`),
  KEY `idx_demanderId` (`demanderId`),
  KEY `idx_supplierId` (`supplierId`),
  KEY `idx_createTime` (`createTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='订单表';



DROP TABLE IF EXISTS `orders00`;
CREATE TABLE `orders00` (
  `orderId` bigint unsigned NOT NULL AUTO_INCREMENT,
  `accountId` bigint unsigned NOT NULL,
  `bookId` varchar(64) NOT NULL,
  `bookName` varchar(128) NOT NULL,
  `chapterId` varchar(64) NOT NULL,
  `chapterType` varchar(16) DEFAULT 'txt',
  `chapterOrder` int unsigned NOT NULL,
  `chapterTitle` varchar(128) NOT NULL,
  `price` float(10,2) NOT NULL,
  `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`orderId`),
  KEY `idx_accountId_orderId` (`accountId`,`orderId`),
  KEY `idx_accountId_bookId_orderId` (`accountId`,`bookId`,`orderId`),
  KEY `idx_accountId_bookId_chapterId` (`accountId`,`bookId`,`chapterId`),
  KEY `idx_accountId_bookId_chapterId_chapterOrder` (`accountId`,`bookId`,`chapterId`,`chapterOrder`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='订单表';

insert into orders00 (accountId, bookId, bookName, chapterId, chapterType, chapterOrder, chapterTitle, price)
values
(1, '68185f28582200008e003f72', '星际龙神', '68236d0f8c3e000052006d6c', 'txt', 1, '序幕', 66.0);


insert into orders00 (accountId, bookId, bookName, chapterId, chapterType, chapterOrder, chapterTitle, price)
values
(1, '68185f47582200008e003f73', '斗罗宇宙', '68236d0f8c3e000052006d6c', 'txt', '1', '旧识', 88.0);



DROP TABLE IF EXISTS `monthlyorders00`;
CREATE TABLE `monthlyorders00` (
  `orderId` bigint unsigned NOT NULL AUTO_INCREMENT,
  `accountId` bigint unsigned NOT NULL,
  `bookId` varchar(64) NOT NULL,
  `bookName` varchar(128) NOT NULL,
  `price` float(10,2) NOT NULL,
  `created` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `startTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `endTime` timestamp,
  PRIMARY KEY (`orderId`),
  KEY `idx_accountId_orderId` (`accountId`,`orderId`),
  KEY `idx_accountId_bookId_orderId` (`accountId`,`bookId`,`orderId`),
  KEY `idx_accountId_bookId_orderId_created` (`accountId`,`bookId`, `orderId`, `created`),
  KEY `idx_accountId_bookId_startTime_endTime` (`accountId`,`bookId`,`startTime`,`endTime`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='包月订单表';

CREATE TRIGGER setMonthlyEndTime
    BEFORE INSERT ON `monthlyorders00`
    FOR EACH ROW
    SET NEW.endTime = ADDDATE(curdate(), INTERVAL 30 DAY);

insert into monthlyorders00 (accountId, bookId, bookName, price)
values
(1, '6828a1f47f520000ff006092', '雨中菜刀行', 12000.0);

