-- Payment模块数据库初始化脚本
-- 注意：本项目使用MongoDB作为主要数据库，此SQL文件仅作为参考

-- 创建数据库
CREATE DATABASE IF NOT EXISTS beyondreading_payment DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE beyondreading_payment;

-- 支付订单表（参考结构，实际使用MongoDB）
CREATE TABLE IF NOT EXISTS payment_orders (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    orderId VARCHAR(64) NOT NULL UNIQUE COMMENT '订单ID',
    userId BIGINT NOT NULL COMMENT '用户ID',
    paymentMethod INT NOT NULL COMMENT '支付方式',
    paymentType INT NOT NULL COMMENT '支付类型',
    amount BIGINT NOT NULL COMMENT '支付金额（分）',
    currency VARCHAR(8) NOT NULL DEFAULT 'USD' COMMENT '货币类型',
    productId VARCHAR(64) NOT NULL COMMENT '产品ID',
    productName VARCHAR(255) NOT NULL COMMENT '产品名称',
    description TEXT COMMENT '订单描述',
    status INT NOT NULL DEFAULT 1 COMMENT '订单状态',
    transactionId VARCHAR(128) COMMENT '第三方交易ID',
    clientIp VARCHAR(45) COMMENT '客户端IP',
    userAgent TEXT COMMENT '用户代理',
    returnUrl VARCHAR(512) COMMENT '支付成功返回URL',
    cancelUrl VARCHAR(512) COMMENT '支付取消返回URL',
    paymentUrl VARCHAR(512) COMMENT '支付URL',
    paymentData TEXT COMMENT '支付数据',
    failureReason VARCHAR(512) COMMENT '失败原因',
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    paidAt TIMESTAMP NULL COMMENT '支付时间',
    expiredAt TIMESTAMP NOT NULL COMMENT '过期时间',
    
    INDEX idx_userId (userId),
    INDEX idx_paymentMethod (paymentMethod),
    INDEX idx_paymentType (paymentType),
    INDEX idx_status (status),
    INDEX idx_transactionId (transactionId),
    INDEX idx_createdAt (createdAt),
    INDEX idx_expiredAt (expiredAt),
    INDEX idx_userId_status (userId, status),
    INDEX idx_userId_createdAt (userId, createdAt)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付订单表';

-- 支付回调记录表（参考结构，实际使用MongoDB）
CREATE TABLE IF NOT EXISTS payment_callbacks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    orderId VARCHAR(64) NOT NULL COMMENT '订单ID',
    paymentMethod INT NOT NULL COMMENT '支付方式',
    transactionId VARCHAR(128) COMMENT '第三方交易ID',
    status INT NOT NULL COMMENT '支付状态',
    amount BIGINT NOT NULL COMMENT '支付金额（分）',
    currency VARCHAR(8) NOT NULL COMMENT '货币类型',
    callbackData JSON COMMENT '回调原始数据',
    signature VARCHAR(512) COMMENT '签名',
    processed BOOLEAN DEFAULT FALSE COMMENT '是否已处理',
    processedAt TIMESTAMP NULL COMMENT '处理时间',
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_orderId (orderId),
    INDEX idx_transactionId (transactionId),
    INDEX idx_paymentMethod (paymentMethod),
    INDEX idx_processed (processed),
    INDEX idx_createdAt (createdAt),
    INDEX idx_orderId_transactionId (orderId, transactionId),
    INDEX idx_processed_createdAt (processed, createdAt)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付回调记录表';

-- 退款记录表（参考结构，实际使用MongoDB）
CREATE TABLE IF NOT EXISTS payment_refunds (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    refundId VARCHAR(64) NOT NULL UNIQUE COMMENT '退款单号',
    orderId VARCHAR(64) NOT NULL COMMENT '订单ID',
    userId BIGINT NOT NULL COMMENT '用户ID',
    paymentMethod INT NOT NULL COMMENT '支付方式',
    refundAmount BIGINT NOT NULL COMMENT '退款金额（分）',
    currency VARCHAR(8) NOT NULL COMMENT '货币类型',
    reason VARCHAR(512) COMMENT '退款原因',
    status INT NOT NULL DEFAULT 1 COMMENT '退款状态',
    transactionId VARCHAR(128) COMMENT '第三方交易ID',
    refundData JSON COMMENT '退款数据',
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    processedAt TIMESTAMP NULL COMMENT '处理时间',
    
    INDEX idx_orderId (orderId),
    INDEX idx_userId (userId),
    INDEX idx_paymentMethod (paymentMethod),
    INDEX idx_status (status),
    INDEX idx_createdAt (createdAt),
    INDEX idx_userId_status (userId, status),
    INDEX idx_orderId_status (orderId, status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='退款记录表';

-- 支付方式配置表（参考结构，实际使用MongoDB）
CREATE TABLE IF NOT EXISTS payment_methods (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    method INT NOT NULL UNIQUE COMMENT '支付方式',
    name VARCHAR(64) NOT NULL COMMENT '支付方式名称',
    displayName VARCHAR(128) NOT NULL COMMENT '显示名称',
    icon VARCHAR(256) COMMENT '图标URL',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    supportedCurrencies JSON COMMENT '支持的货币',
    supportedPlatforms JSON COMMENT '支持的平台',
    supportedRegions JSON COMMENT '支持的地区',
    config JSON COMMENT '配置信息',
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_enabled (enabled),
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付方式配置表';

-- 支付产品配置表（参考结构，实际使用MongoDB）
CREATE TABLE IF NOT EXISTS payment_products (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    productId VARCHAR(64) NOT NULL UNIQUE COMMENT '产品ID',
    productName VARCHAR(255) NOT NULL COMMENT '产品名称',
    productType INT NOT NULL COMMENT '产品类型',
    amount BIGINT NOT NULL COMMENT '价格（分）',
    currency VARCHAR(8) NOT NULL DEFAULT 'USD' COMMENT '货币类型',
    coinAmount BIGINT DEFAULT 0 COMMENT '书币数量',
    vipDays INT DEFAULT 0 COMMENT 'VIP天数',
    monthlyDays INT DEFAULT 0 COMMENT '包月天数',
    description TEXT COMMENT '产品描述',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    sortOrder INT DEFAULT 0 COMMENT '排序',
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_productType (productType),
    INDEX idx_enabled (enabled),
    INDEX idx_sortOrder (sortOrder)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付产品配置表';

-- 支付日志表（参考结构，实际使用MongoDB）
CREATE TABLE IF NOT EXISTS payment_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    orderId VARCHAR(64) NOT NULL COMMENT '订单ID',
    userId BIGINT NOT NULL COMMENT '用户ID',
    action VARCHAR(64) NOT NULL COMMENT '操作',
    status VARCHAR(32) NOT NULL COMMENT '状态',
    message TEXT COMMENT '消息',
    data JSON COMMENT '数据',
    createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_orderId (orderId),
    INDEX idx_userId (userId),
    INDEX idx_action (action),
    INDEX idx_createdAt (createdAt),
    INDEX idx_orderId_createdAt (orderId, createdAt),
    INDEX idx_userId_createdAt (userId, createdAt)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='支付日志表';

-- 插入初始支付方式配置
INSERT INTO payment_methods (method, name, displayName, icon, enabled, supportedCurrencies, supportedPlatforms, supportedRegions, config) VALUES
(1, 'google_pay', 'Google Pay', '/icons/google_pay.png', TRUE, '["USD", "EUR", "GBP", "JPY"]', '["android", "web"]', '["US", "EU", "JP"]', '{}'),
(2, 'apple_pay', 'Apple Pay', '/icons/apple_pay.png', TRUE, '["USD", "EUR", "GBP", "JPY"]', '["ios", "web"]', '["US", "EU", "JP"]', '{}'),
(3, 'paypal', 'PayPal', '/icons/paypal.png', TRUE, '["USD", "EUR", "GBP", "JPY", "CAD", "AUD"]', '["ios", "android", "web"]', '["US", "EU", "JP", "CA", "AU"]', '{}'),
(4, 'alipay', '支付宝', '/icons/alipay.png', TRUE, '["CNY", "USD"]', '["ios", "android", "web"]', '["CN", "HK", "TW"]', '{}'),
(5, 'wechat_pay', '微信支付', '/icons/wechat_pay.png', TRUE, '["CNY"]', '["ios", "android", "web"]', '["CN"]', '{}');

-- 插入初始支付产品配置
INSERT INTO payment_products (productId, productName, productType, amount, currency, coinAmount, vipDays, monthlyDays, description, enabled, sortOrder) VALUES
('vip_monthly', 'VIP月卡', 1, 999, 'USD', 0, 30, 0, 'VIP月卡，享受30天VIP特权', TRUE, 1),
('vip_yearly', 'VIP年卡', 1, 9999, 'USD', 0, 365, 0, 'VIP年卡，享受365天VIP特权', TRUE, 2),
('monthly_subscription', '包月订阅', 2, 599, 'USD', 0, 0, 30, '包月订阅，自动续费', TRUE, 3),
('coins_100', '100书币', 3, 99, 'USD', 100, 0, 0, '充值100书币', TRUE, 4),
('coins_500', '500书币', 3, 499, 'USD', 500, 0, 0, '充值500书币', TRUE, 5),
('coins_1000', '1000书币', 3, 999, 'USD', 1000, 0, 0, '充值1000书币', TRUE, 6);
