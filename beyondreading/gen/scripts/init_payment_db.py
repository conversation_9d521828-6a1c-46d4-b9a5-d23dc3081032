#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Payment模块数据库初始化脚本
生成MongoDB集合和索引
"""

import pymongo
from datetime import datetime
import json

# MongoDB连接配置
MONGO_URI = "mongodb://localhost:27017"
DATABASE_NAME = "beyondreading_payment"

def init_mongodb():
    """初始化MongoDB数据库"""
    print("Initializing Payment MongoDB database...")
    
    # 连接MongoDB
    client = pymongo.MongoClient(MONGO_URI)
    db = client[DATABASE_NAME]
    
    # 创建集合和索引
    create_payment_orders_collection(db)
    create_payment_callbacks_collection(db)
    create_payment_refunds_collection(db)
    create_payment_methods_collection(db)
    create_payment_products_collection(db)
    create_payment_logs_collection(db)
    
    # 插入初始数据
    insert_initial_data(db)
    
    print("Payment MongoDB database initialized successfully!")
    client.close()

def create_payment_orders_collection(db):
    """创建支付订单集合"""
    collection = db.payment_orders
    
    # 创建索引
    collection.create_index("orderId", unique=True)
    collection.create_index("userId")
    collection.create_index("paymentMethod")
    collection.create_index("paymentType")
    collection.create_index("status")
    collection.create_index("transactionId")
    collection.create_index("createdAt")
    collection.create_index("expiredAt")
    collection.create_index([("userId", 1), ("status", 1)])
    collection.create_index([("userId", 1), ("createdAt", -1)])
    
    print("Created payment_orders collection with indexes")

def create_payment_callbacks_collection(db):
    """创建支付回调集合"""
    collection = db.payment_callbacks
    
    # 创建索引
    collection.create_index("orderId")
    collection.create_index("transactionId")
    collection.create_index("paymentMethod")
    collection.create_index("processed")
    collection.create_index("createdAt")
    collection.create_index([("orderId", 1), ("transactionId", 1)])
    collection.create_index([("processed", 1), ("createdAt", 1)])
    
    print("Created payment_callbacks collection with indexes")

def create_payment_refunds_collection(db):
    """创建退款记录集合"""
    collection = db.payment_refunds
    
    # 创建索引
    collection.create_index("refundId", unique=True)
    collection.create_index("orderId")
    collection.create_index("userId")
    collection.create_index("paymentMethod")
    collection.create_index("status")
    collection.create_index("createdAt")
    collection.create_index([("userId", 1), ("status", 1)])
    collection.create_index([("orderId", 1), ("status", 1)])
    
    print("Created payment_refunds collection with indexes")

def create_payment_methods_collection(db):
    """创建支付方式配置集合"""
    collection = db.payment_methods
    
    # 创建索引
    collection.create_index("method", unique=True)
    collection.create_index("enabled")
    collection.create_index("name")
    
    print("Created payment_methods collection with indexes")

def create_payment_products_collection(db):
    """创建支付产品配置集合"""
    collection = db.payment_products
    
    # 创建索引
    collection.create_index("productId", unique=True)
    collection.create_index("productType")
    collection.create_index("enabled")
    collection.create_index("sortOrder")
    
    print("Created payment_products collection with indexes")

def create_payment_logs_collection(db):
    """创建支付日志集合"""
    collection = db.payment_logs
    
    # 创建索引
    collection.create_index("orderId")
    collection.create_index("userId")
    collection.create_index("action")
    collection.create_index("createdAt")
    collection.create_index([("orderId", 1), ("createdAt", -1)])
    collection.create_index([("userId", 1), ("createdAt", -1)])
    
    # 设置TTL索引，日志保留30天
    collection.create_index("createdAt", expireAfterSeconds=30*24*60*60)
    
    print("Created payment_logs collection with indexes")

def insert_initial_data(db):
    """插入初始数据"""
    print("Inserting initial data...")
    
    # 插入支付方式配置
    payment_methods = [
        {
            "method": 1,
            "name": "google_pay",
            "displayName": "Google Pay",
            "icon": "/icons/google_pay.png",
            "enabled": True,
            "supportedCurrencies": ["USD", "EUR", "GBP", "JPY"],
            "supportedPlatforms": ["android", "web"],
            "supportedRegions": ["US", "EU", "JP"],
            "config": {},
            "createdAt": datetime.utcnow(),
            "updatedAt": datetime.utcnow()
        },
        {
            "method": 2,
            "name": "apple_pay",
            "displayName": "Apple Pay",
            "icon": "/icons/apple_pay.png",
            "enabled": True,
            "supportedCurrencies": ["USD", "EUR", "GBP", "JPY"],
            "supportedPlatforms": ["ios", "web"],
            "supportedRegions": ["US", "EU", "JP"],
            "config": {},
            "createdAt": datetime.utcnow(),
            "updatedAt": datetime.utcnow()
        },
        {
            "method": 3,
            "name": "paypal",
            "displayName": "PayPal",
            "icon": "/icons/paypal.png",
            "enabled": True,
            "supportedCurrencies": ["USD", "EUR", "GBP", "JPY", "CAD", "AUD"],
            "supportedPlatforms": ["ios", "android", "web"],
            "supportedRegions": ["US", "EU", "JP", "CA", "AU"],
            "config": {},
            "createdAt": datetime.utcnow(),
            "updatedAt": datetime.utcnow()
        },
        {
            "method": 4,
            "name": "alipay",
            "displayName": "支付宝",
            "icon": "/icons/alipay.png",
            "enabled": True,
            "supportedCurrencies": ["CNY", "USD"],
            "supportedPlatforms": ["ios", "android", "web"],
            "supportedRegions": ["CN", "HK", "TW"],
            "config": {},
            "createdAt": datetime.utcnow(),
            "updatedAt": datetime.utcnow()
        },
        {
            "method": 5,
            "name": "wechat_pay",
            "displayName": "微信支付",
            "icon": "/icons/wechat_pay.png",
            "enabled": True,
            "supportedCurrencies": ["CNY"],
            "supportedPlatforms": ["ios", "android", "web"],
            "supportedRegions": ["CN"],
            "config": {},
            "createdAt": datetime.utcnow(),
            "updatedAt": datetime.utcnow()
        }
    ]
    
    db.payment_methods.insert_many(payment_methods)
    print(f"Inserted {len(payment_methods)} payment methods")
    
    # 插入支付产品配置
    payment_products = [
        # VIP产品
        {
            "productId": "vip_monthly",
            "productName": "VIP月卡",
            "productType": 1,  # VIP_PURCHASE
            "amount": 999,  # 9.99 USD
            "currency": "USD",
            "coinAmount": 0,
            "vipDays": 30,
            "monthlyDays": 0,
            "description": "VIP月卡，享受30天VIP特权",
            "enabled": True,
            "sortOrder": 1,
            "createdAt": datetime.utcnow(),
            "updatedAt": datetime.utcnow()
        },
        {
            "productId": "vip_yearly",
            "productName": "VIP年卡",
            "productType": 1,  # VIP_PURCHASE
            "amount": 9999,  # 99.99 USD
            "currency": "USD",
            "coinAmount": 0,
            "vipDays": 365,
            "monthlyDays": 0,
            "description": "VIP年卡，享受365天VIP特权",
            "enabled": True,
            "sortOrder": 2,
            "createdAt": datetime.utcnow(),
            "updatedAt": datetime.utcnow()
        },
        # 包月产品
        {
            "productId": "monthly_subscription",
            "productName": "包月订阅",
            "productType": 2,  # MONTHLY_SUBSCRIPTION
            "amount": 599,  # 5.99 USD
            "currency": "USD",
            "coinAmount": 0,
            "vipDays": 0,
            "monthlyDays": 30,
            "description": "包月订阅，自动续费",
            "enabled": True,
            "sortOrder": 3,
            "createdAt": datetime.utcnow(),
            "updatedAt": datetime.utcnow()
        },
        # 书币充值产品
        {
            "productId": "coins_100",
            "productName": "100书币",
            "productType": 3,  # COIN_RECHARGE
            "amount": 99,  # 0.99 USD
            "currency": "USD",
            "coinAmount": 100,
            "vipDays": 0,
            "monthlyDays": 0,
            "description": "充值100书币",
            "enabled": True,
            "sortOrder": 4,
            "createdAt": datetime.utcnow(),
            "updatedAt": datetime.utcnow()
        },
        {
            "productId": "coins_500",
            "productName": "500书币",
            "productType": 3,  # COIN_RECHARGE
            "amount": 499,  # 4.99 USD
            "currency": "USD",
            "coinAmount": 500,
            "vipDays": 0,
            "monthlyDays": 0,
            "description": "充值500书币",
            "enabled": True,
            "sortOrder": 5,
            "createdAt": datetime.utcnow(),
            "updatedAt": datetime.utcnow()
        },
        {
            "productId": "coins_1000",
            "productName": "1000书币",
            "productType": 3,  # COIN_RECHARGE
            "amount": 999,  # 9.99 USD
            "currency": "USD",
            "coinAmount": 1000,
            "vipDays": 0,
            "monthlyDays": 0,
            "description": "充值1000书币",
            "enabled": True,
            "sortOrder": 6,
            "createdAt": datetime.utcnow(),
            "updatedAt": datetime.utcnow()
        }
    ]
    
    db.payment_products.insert_many(payment_products)
    print(f"Inserted {len(payment_products)} payment products")

if __name__ == "__main__":
    init_mongodb()
