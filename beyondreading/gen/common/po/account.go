package po

import (
	"time"
)

// Account 账户信息
type Account struct {
	AccountId         uint64     `db:"account_id" json:"account_id"`
	UserId            uint64     `db:"user_id" json:"user_id"`
	CoinBalance       float64    `db:"coin_balance" json:"coin_balance"`
	TotalRecharged    float64    `db:"total_recharged" json:"total_recharged"`
	TotalConsumed     float64    `db:"total_consumed" json:"total_consumed"`
	Status            int32      `db:"status" json:"status"`
	UserType          int32      `db:"user_type" json:"user_type"`
	UserLevel         int32      `db:"user_level" json:"user_level"`
	VipExpireTime     *time.Time `db:"vip_expire_time" json:"vip_expire_time"`
	MonthlyExpireTime *time.Time `db:"monthly_expire_time" json:"monthly_expire_time"`
	CreatedAt         time.Time  `db:"created_at" json:"created_at"`
	UpdatedAt         time.Time  `db:"updated_at" json:"updated_at"`
}

// AccountLog 账户日志
type AccountLog struct {
	LogId           uint64    `db:"log_id" json:"log_id"`
	AccountId       uint64    `db:"account_id" json:"account_id"`
	UserId          uint64    `db:"user_id" json:"user_id"`
	TransactionType string    `db:"transaction_type" json:"transaction_type"`
	Amount          float64   `db:"amount" json:"amount"`
	BalanceBefore   float64   `db:"balance_before" json:"balance_before"`
	BalanceAfter    float64   `db:"balance_after" json:"balance_after"`
	OrderId         string    `db:"order_id" json:"order_id"`
	BookId          string    `db:"book_id" json:"book_id"`
	ChapterId       string    `db:"chapter_id" json:"chapter_id"`
	Description     string    `db:"description" json:"description"`
	ExtraData       string    `db:"extra_data" json:"extra_data"`
	CreatedAt       time.Time `db:"created_at" json:"created_at"`
}

// RechargeOrder 充值订单
type RechargeOrder struct {
	OrderId        string     `db:"order_id" json:"order_id"`
	AccountId      uint64     `db:"account_id" json:"account_id"`
	UserId         uint64     `db:"user_id" json:"user_id"`
	Amount         float64    `db:"amount" json:"amount"`
	CoinAmount     float64    `db:"coin_amount" json:"coin_amount"`
	ExchangeRate   float32    `db:"exchange_rate" json:"exchange_rate"`
	PaymentMethod  string     `db:"payment_method" json:"payment_method"`
	PaymentOrderId string     `db:"payment_order_id" json:"payment_order_id"`
	Status         int32      `db:"status" json:"status"`
	PaidAt         *time.Time `db:"paid_at" json:"paid_at"`
	CreatedAt      time.Time  `db:"created_at" json:"created_at"`
	UpdatedAt      time.Time  `db:"updated_at" json:"updated_at"`
}

// PurchaseOrder 购买订单（章节）
type PurchaseOrder struct {
	OrderId      string    `db:"order_id" json:"order_id"`
	AccountId    uint64    `db:"account_id" json:"account_id"`
	UserId       uint64    `db:"user_id" json:"user_id"`
	OrderType    string    `db:"order_type" json:"order_type"`
	BookId       string    `db:"book_id" json:"book_id"`
	BookName     string    `db:"book_name" json:"book_name"`
	ChapterId    string    `db:"chapter_id" json:"chapter_id"`
	ChapterTitle string    `db:"chapter_title" json:"chapter_title"`
	ChapterOrder uint32    `db:"chapter_order" json:"chapter_order"`
	CoinAmount   float64   `db:"coin_amount" json:"coin_amount"`
	Status       int32     `db:"status" json:"status"`
	CreatedAt    time.Time `db:"created_at" json:"created_at"`
	UpdatedAt    time.Time `db:"updated_at" json:"updated_at"`
}

// VipMonthlyOrder VIP/包月订单
type VipMonthlyOrder struct {
	OrderId      string     `db:"order_id" json:"order_id"`
	AccountId    uint64     `db:"account_id" json:"account_id"`
	UserId       uint64     `db:"user_id" json:"user_id"`
	OrderType    string     `db:"order_type" json:"order_type"`
	CoinAmount   float64    `db:"coin_amount" json:"coin_amount"`
	DurationDays int32      `db:"duration_days" json:"duration_days"`
	StartTime    *time.Time `db:"start_time" json:"start_time"`
	EndTime      *time.Time `db:"end_time" json:"end_time"`
	Status       int32      `db:"status" json:"status"`
	CreatedAt    time.Time  `db:"created_at" json:"created_at"`
	UpdatedAt    time.Time  `db:"updated_at" json:"updated_at"`
}

// ChapterPurchaseInfo 章节购买信息
type ChapterPurchaseInfo struct {
	OrderId      string    `json:"order_id"`
	ChapterId    string    `json:"chapter_id"`
	ChapterTitle string    `json:"chapter_title"`
	ChapterOrder uint32    `json:"chapter_order"`
	CoinAmount   float64   `json:"coin_amount"`
	PurchasedAt  time.Time `json:"purchased_at"`
	IsMonthly    bool      `json:"is_monthly"`
	IsVip        bool      `json:"is_vip"`
}

// ChapterPurchaseItem 批量章节购买项
type ChapterPurchaseItem struct {
	ChapterOrder uint32  `json:"chapter_order"`
	CoinAmount   float64 `json:"coin_amount"`
}
