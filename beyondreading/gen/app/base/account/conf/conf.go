package conf

import (
	"fmt"

	"creativematrix.com/beyondreading/pkg/config"
)

// Config 配置结构
type Config struct {
	config.Base

	Log struct {
		Level string
	}

	MySQL struct {
		DSN string
	}

	Redis struct {
		Addr     string
		Password string
		DB       int
	}
}

// Load 加载配置
func Load(app string) *Config {
	var conf = new(Config)
	if err := config.Load(app, conf); err != nil {
		panic(fmt.Sprintf("config load failed: %v", err))
	}
	return conf
}
