package api

import (
	"creativematrix.com/beyondreading/pkg/config"
	"creativematrix.com/beyondreading/pkg/discovery"
	"creativematrix.com/beyondreading/pkg/logger"
	pb "creativematrix.com/beyondreading/gen/proto/account"
	"google.golang.org/grpc"
)

// App 应用名称
const App = "account"

// NewClient 创建Account客户端
func NewClient(c config.Base) (pb.AccountClient, error) {
	resolver := discovery.NewResolver(c.Etcd.Addrs, logger.Log)
	conn, err := grpc.Dial(
		resolver.Scheme()+"://"+App,
		grpc.WithInsecure(),
		grpc.WithResolvers(resolver),
		grpc.WithDefaultServiceConfig(`{"loadBalancingPolicy":"round_robin"}`),
	)
	if err != nil {
		return nil, err
	}

	return pb.NewAccountClient(conn), nil
}
