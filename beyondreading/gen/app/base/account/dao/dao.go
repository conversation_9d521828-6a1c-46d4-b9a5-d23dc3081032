package dao

import (
	"context"
	"fmt"
	"time"

	"creativematrix.com/beyondreading/gen/app/base/account/conf"
	"creativematrix.com/beyondreading/gen/common/po"
	"creativematrix.com/beyondreading/pkg/mysql"
	"creativematrix.com/beyondreading/pkg/redis"
	"github.com/jmoiron/sqlx"
)

// Dao 数据访问层
type Dao struct {
	msshard *mysql.MSShard
	redis   *redis.Redis
}

// New 创建新的DAO实例
func New(msshard *mysql.MSShard, redis *redis.Redis) *Dao {
	return &Dao{
		msshard: msshard,
		redis:   redis,
	}
}

// GetDB 获取数据库连接
func (d *Dao) GetDB(userId uint64) (*sqlx.DB, error) {
	userIdStr := fmt.Sprintf("%d", userId)
	db, err := d.msshard.DB(userIdStr)
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}
	return db, nil
}

// GetTableName 获取分表名称
func (d *Dao) GetTableName(userId uint64, tableName string) string {
	userIdStr := fmt.Sprintf("%d", userId)
	return d.msshard.Table(userIdStr, tableName)
}

// GetAccountByUserId 根据用户ID获取账户信息
func (d *Dao) GetAccountByUserId(ctx context.Context, userId uint64) (*po.Account, error) {
	db, err := d.GetDB(userId)
	if err != nil {
		return nil, err
	}

	var account po.Account
	query := `SELECT account_id, user_id, coin_balance, total_recharged, total_consumed, 
			  status, user_type, user_level, vip_expire_time, monthly_expire_time, 
			  created_at, updated_at FROM account WHERE user_id = ?`

	err = db.GetContext(ctx, &account, query, userId)
	if err != nil {
		return nil, fmt.Errorf("failed to get account: %w", err)
	}

	return &account, nil
}

// CreateAccount 创建账户
func (d *Dao) CreateAccount(ctx context.Context, userId uint64) (*po.Account, error) {
	db, err := d.GetDB(userId)
	if err != nil {
		return nil, err
	}

	now := time.Now()
	account := &po.Account{
		UserId:         userId,
		CoinBalance:    0.0,
		TotalRecharged: 0.0,
		TotalConsumed:  0.0,
		Status:         1, // 正常状态
		UserType:       1, // 普通用户
		UserLevel:      1, // 初始等级
		CreatedAt:      now,
		UpdatedAt:      now,
	}

	query := `INSERT INTO account (user_id, coin_balance, total_recharged, total_consumed, 
			  status, user_type, user_level, created_at, updated_at) 
			  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`

	result, err := db.ExecContext(ctx, query, account.UserId, account.CoinBalance,
		account.TotalRecharged, account.TotalConsumed, account.Status,
		account.UserType, account.UserLevel, account.CreatedAt, account.UpdatedAt)
	if err != nil {
		return nil, fmt.Errorf("failed to create account: %w", err)
	}

	accountId, err := result.LastInsertId()
	if err != nil {
		return nil, fmt.Errorf("failed to get account ID: %w", err)
	}

	account.AccountId = uint64(accountId)
	return account, nil
}

// UpdateAccountBalance 更新账户余额
func (d *Dao) UpdateAccountBalance(ctx context.Context, userId uint64, amount float64, transactionType string) (*po.Account, error) {
	db, err := d.GetDB(userId)
	if err != nil {
		return nil, err
	}

	// 开启事务
	tx, err := db.BeginTxx(ctx, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	// 获取当前账户信息
	var account po.Account
	query := `SELECT account_id, user_id, coin_balance, total_recharged, total_consumed, 
			  status, user_type, user_level, vip_expire_time, monthly_expire_time, 
			  created_at, updated_at FROM account WHERE user_id = ? FOR UPDATE`

	err = tx.GetContext(ctx, &account, query, userId)
	if err != nil {
		return nil, fmt.Errorf("failed to get account for update: %w", err)
	}

	// 计算新余额
	balanceBefore := account.CoinBalance
	balanceAfter := balanceBefore + amount

	// 检查余额是否足够（如果是扣款）
	if amount < 0 && balanceAfter < 0 {
		return nil, fmt.Errorf("insufficient balance: current=%.2f, required=%.2f", balanceBefore, -amount)
	}

	// 更新账户信息
	account.CoinBalance = balanceAfter
	if amount > 0 {
		account.TotalRecharged += amount
	} else {
		account.TotalConsumed += (-amount)
		// 更新用户等级（每1000书币一级）
		account.UserLevel = int32(account.TotalConsumed/1000) + 1
	}
	account.UpdatedAt = time.Now()

	// 更新账户表
	updateQuery := `UPDATE account SET coin_balance = ?, total_recharged = ?, total_consumed = ?, 
					user_level = ?, updated_at = ? WHERE user_id = ?`

	_, err = tx.ExecContext(ctx, updateQuery, account.CoinBalance, account.TotalRecharged,
		account.TotalConsumed, account.UserLevel, account.UpdatedAt, userId)
	if err != nil {
		return nil, fmt.Errorf("failed to update account: %w", err)
	}

	// 提交事务
	if err = tx.Commit(); err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	return &account, nil
}

// CreateAccountLog 创建账户日志
func (d *Dao) CreateAccountLog(ctx context.Context, log *po.AccountLog) error {
	db, err := d.GetDB(log.UserId)
	if err != nil {
		return err
	}

	query := `INSERT INTO account_log (account_id, user_id, transaction_type, amount, 
			  balance_before, balance_after, order_id, book_id, chapter_id, description, 
			  extra_data, created_at) 
			  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

	_, err = db.ExecContext(ctx, query, log.AccountId, log.UserId, log.TransactionType,
		log.Amount, log.BalanceBefore, log.BalanceAfter, log.OrderId, log.BookId,
		log.ChapterId, log.Description, log.ExtraData, log.CreatedAt)

	if err != nil {
		return fmt.Errorf("failed to create account log: %w", err)
	}

	return nil
}

// GetAccountLogs 获取账户日志
func (d *Dao) GetAccountLogs(ctx context.Context, userId uint64, page, pageSize int32, transactionType string) ([]*po.AccountLog, int64, error) {
	db, err := d.GetDB(userId)
	if err != nil {
		return nil, 0, err
	}

	// 构建查询条件
	whereClause := "WHERE user_id = ?"
	args := []interface{}{userId}

	if transactionType != "" {
		whereClause += " AND transaction_type = ?"
		args = append(args, transactionType)
	}

	// 获取总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM account_log %s", whereClause)
	var total int64
	err = db.GetContext(ctx, &total, countQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get account logs count: %w", err)
	}

	// 获取分页数据
	offset := (page - 1) * pageSize
	dataQuery := fmt.Sprintf(`SELECT log_id, account_id, user_id, transaction_type, amount, 
							  balance_before, balance_after, order_id, book_id, chapter_id, 
							  description, extra_data, created_at 
							  FROM account_log %s ORDER BY created_at DESC LIMIT ? OFFSET ?`, whereClause)
	args = append(args, pageSize, offset)

	var logs []*po.AccountLog
	err = db.SelectContext(ctx, &logs, dataQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get account logs: %w", err)
	}

	return logs, total, nil
}

// UpdateUserStatus 更新用户状态
func (d *Dao) UpdateUserStatus(ctx context.Context, userId uint64, userType int32, vipExpireTime, monthlyExpireTime *time.Time) (*po.Account, error) {
	db, err := d.GetDB(userId)
	if err != nil {
		return nil, err
	}

	query := `UPDATE account SET user_type = ?, vip_expire_time = ?, monthly_expire_time = ?, 
			  updated_at = ? WHERE user_id = ?`

	_, err = db.ExecContext(ctx, query, userType, vipExpireTime, monthlyExpireTime, time.Now(), userId)
	if err != nil {
		return nil, fmt.Errorf("failed to update user status: %w", err)
	}

	// 返回更新后的账户信息
	return d.GetAccountByUserId(ctx, userId)
}

// CheckUserStatus 检查用户状态
func (d *Dao) CheckUserStatus(ctx context.Context, userId uint64) (*po.Account, bool, bool, error) {
	account, err := d.GetAccountByUserId(ctx, userId)
	if err != nil {
		return nil, false, false, err
	}

	now := time.Now()
	hasVip := account.VipExpireTime != nil && account.VipExpireTime.After(now)
	hasMonthly := account.MonthlyExpireTime != nil && account.MonthlyExpireTime.After(now)

	return account, hasVip, hasMonthly, nil
}
