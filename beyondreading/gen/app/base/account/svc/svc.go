package svc

import (
	"context"
	"fmt"
	"time"

	"creativematrix.com/beyondreading/gen/app/base/account/dao"
	"creativematrix.com/beyondreading/gen/common/po"
	pb "creativematrix.com/beyondreading/gen/proto/account"
	"creativematrix.com/beyondreading/pkg/logger"
	"database/sql"
)

// AccountSvc 账户服务
type AccountSvc struct {
	dao *dao.Dao
}

// New 创建账户服务实例
func New(dao *dao.Dao) *AccountSvc {
	return &AccountSvc{
		dao: dao,
	}
}

// GetAccount 获取账户信息
func (s *AccountSvc) GetAccount(ctx context.Context, req *pb.GetAccountReq) (*pb.GetAccountResp, error) {
	account, err := s.dao.GetAccountByUserId(ctx, req.UserId)
	if err != nil {
		if err == sql.ErrNoRows {
			return &pb.GetAccountResp{
				Code:    404,
				Message: "Account not found",
			}, nil
		}
		logger.LogErrorf("Failed to get account: %v", err)
		return &pb.GetAccountResp{
			Code:    500,
			Message: "Internal server error",
		}, nil
	}

	return &pb.GetAccountResp{
		Code:    200,
		Message: "Success",
		Account: s.convertAccountToPB(account),
	}, nil
}

// CreateAccount 创建账户
func (s *AccountSvc) CreateAccount(ctx context.Context, req *pb.CreateAccountReq) (*pb.CreateAccountResp, error) {
	// 检查账户是否已存在
	existingAccount, err := s.dao.GetAccountByUserId(ctx, req.UserId)
	if err == nil && existingAccount != nil {
		return &pb.CreateAccountResp{
			Code:    200,
			Message: "Account already exists",
			Account: s.convertAccountToPB(existingAccount),
		}, nil
	}

	// 创建新账户
	account, err := s.dao.CreateAccount(ctx, req.UserId)
	if err != nil {
		logger.LogErrorf("Failed to create account: %v", err)
		return &pb.CreateAccountResp{
			Code:    500,
			Message: "Failed to create account",
		}, nil
	}

	return &pb.CreateAccountResp{
		Code:    200,
		Message: "Account created successfully",
		Account: s.convertAccountToPB(account),
	}, nil
}

// Recharge 充值
func (s *AccountSvc) Recharge(ctx context.Context, req *pb.RechargeReq) (*pb.RechargeResp, error) {
	// 计算书币数量
	coinAmount := req.Amount * float64(req.ExchangeRate)
	if req.ExchangeRate == 0 {
		coinAmount = req.Amount // 默认1:1兑换
	}

	// 更新账户余额
	account, err := s.dao.UpdateAccountBalance(ctx, req.UserId, coinAmount, "recharge")
	if err != nil {
		logger.LogErrorf("Failed to update account balance: %v", err)
		return &pb.RechargeResp{
			Code:    500,
			Message: "Failed to recharge",
		}, nil
	}

	// 创建充值订单（这里简化处理，实际应该先创建订单再更新余额）
	orderId := fmt.Sprintf("RO%d%02d%02d%02d%02d%02d",
		time.Now().Year(), time.Now().Month(), time.Now().Day(),
		time.Now().Hour(), time.Now().Minute(), time.Now().Second())

	// 创建账户日志
	log := &po.AccountLog{
		AccountId:       account.AccountId,
		UserId:          req.UserId,
		TransactionType: "recharge",
		Amount:          coinAmount,
		BalanceBefore:   account.CoinBalance - coinAmount,
		BalanceAfter:    account.CoinBalance,
		OrderId:         orderId,
		Description:     fmt.Sprintf("Recharge %.2f yuan, get %.2f coins", req.Amount, coinAmount),
		CreatedAt:       time.Now(),
	}

	err = s.dao.CreateAccountLog(ctx, log)
	if err != nil {
		logger.LogErrorf("Failed to create account log: %v", err)
	}

	// 构造充值订单响应
	order := &pb.RechargeOrder{
		OrderId:      orderId,
		AccountId:    account.AccountId,
		UserId:       req.UserId,
		Amount:       req.Amount,
		CoinAmount:   coinAmount,
		ExchangeRate: req.ExchangeRate,
		PaymentMethod: req.PaymentMethod,
		Status:       2, // 支付成功
		PaidAt:       time.Now().Unix(),
		CreatedAt:    time.Now().Unix(),
		UpdatedAt:    time.Now().Unix(),
	}

	return &pb.RechargeResp{
		Code:    200,
		Message: "Recharge successful",
		Order:   order,
	}, nil
}

// GetAccountLogs 获取账户日志
func (s *AccountSvc) GetAccountLogs(ctx context.Context, req *pb.GetAccountLogsReq) (*pb.GetAccountLogsResp, error) {
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	logs, total, err := s.dao.GetAccountLogs(ctx, req.UserId, req.Page, req.PageSize, req.TransactionType)
	if err != nil {
		logger.LogErrorf("Failed to get account logs: %v", err)
		return &pb.GetAccountLogsResp{
			Code:    500,
			Message: "Failed to get account logs",
		}, nil
	}

	var pbLogs []*pb.AccountLog
	for _, log := range logs {
		pbLogs = append(pbLogs, s.convertAccountLogToPB(log))
	}

	return &pb.GetAccountLogsResp{
		Code:    200,
		Message: "Success",
		Logs:    pbLogs,
		Total:   total,
	}, nil
}

// UpdateUserStatus 更新用户状态
func (s *AccountSvc) UpdateUserStatus(ctx context.Context, req *pb.UpdateUserStatusReq) (*pb.UpdateUserStatusResp, error) {
	var vipExpireTime, monthlyExpireTime *time.Time

	if req.VipExpireTime > 0 {
		t := time.Unix(req.VipExpireTime, 0)
		vipExpireTime = &t
	}

	if req.MonthlyExpireTime > 0 {
		t := time.Unix(req.MonthlyExpireTime, 0)
		monthlyExpireTime = &t
	}

	account, err := s.dao.UpdateUserStatus(ctx, req.UserId, req.UserType, vipExpireTime, monthlyExpireTime)
	if err != nil {
		logger.LogErrorf("Failed to update user status: %v", err)
		return &pb.UpdateUserStatusResp{
			Code:    500,
			Message: "Failed to update user status",
		}, nil
	}

	return &pb.UpdateUserStatusResp{
		Code:    200,
		Message: "User status updated successfully",
		Account: s.convertAccountToPB(account),
	}, nil
}

// DeductCoins 扣除书币
func (s *AccountSvc) DeductCoins(ctx context.Context, req *pb.DeductCoinsReq) (*pb.DeductCoinsResp, error) {
	// 更新账户余额（扣除书币）
	account, err := s.dao.UpdateAccountBalance(ctx, req.UserId, -req.Amount, req.TransactionType)
	if err != nil {
		logger.LogErrorf("Failed to deduct coins: %v", err)
		return &pb.DeductCoinsResp{
			Code:    500,
			Message: fmt.Sprintf("Failed to deduct coins: %v", err),
		}, nil
	}

	// 创建账户日志
	log := &po.AccountLog{
		AccountId:       account.AccountId,
		UserId:          req.UserId,
		TransactionType: req.TransactionType,
		Amount:          -req.Amount,
		BalanceBefore:   account.CoinBalance + req.Amount,
		BalanceAfter:    account.CoinBalance,
		OrderId:         req.OrderId,
		BookId:          req.BookId,
		ChapterId:       req.ChapterId,
		Description:     req.Description,
		CreatedAt:       time.Now(),
	}

	err = s.dao.CreateAccountLog(ctx, log)
	if err != nil {
		logger.LogErrorf("Failed to create account log: %v", err)
	}

	return &pb.DeductCoinsResp{
		Code:    200,
		Message: "Coins deducted successfully",
		Account: s.convertAccountToPB(account),
	}, nil
}

// CheckUserStatus 检查用户状态
func (s *AccountSvc) CheckUserStatus(ctx context.Context, req *pb.CheckUserStatusReq) (*pb.CheckUserStatusResp, error) {
	account, hasVip, hasMonthly, err := s.dao.CheckUserStatus(ctx, req.UserId)
	if err != nil {
		logger.LogErrorf("Failed to check user status: %v", err)
		return &pb.CheckUserStatusResp{
			Code:    500,
			Message: "Failed to check user status",
		}, nil
	}

	// 判断是否可以阅读指定书籍（这里简化处理，实际需要根据书籍类型判断）
	canReadBook := hasVip || hasMonthly || account.CoinBalance > 0

	return &pb.CheckUserStatusResp{
		Code:        200,
		Message:     "Success",
		Account:     s.convertAccountToPB(account),
		HasVip:      hasVip,
		HasMonthly:  hasMonthly,
		CanReadBook: canReadBook,
	}, nil
}

// convertAccountToPB 转换Account为protobuf格式
func (s *AccountSvc) convertAccountToPB(account *po.Account) *pb.AccountInfo {
	pbAccount := &pb.AccountInfo{
		AccountId:      account.AccountId,
		UserId:         account.UserId,
		CoinBalance:    account.CoinBalance,
		TotalRecharged: account.TotalRecharged,
		TotalConsumed:  account.TotalConsumed,
		Status:         account.Status,
		UserType:       account.UserType,
		UserLevel:      account.UserLevel,
		CreatedAt:      account.CreatedAt.Unix(),
		UpdatedAt:      account.UpdatedAt.Unix(),
	}

	if account.VipExpireTime != nil {
		pbAccount.VipExpireTime = account.VipExpireTime.Unix()
	}

	if account.MonthlyExpireTime != nil {
		pbAccount.MonthlyExpireTime = account.MonthlyExpireTime.Unix()
	}

	return pbAccount
}

// convertAccountLogToPB 转换AccountLog为protobuf格式
func (s *AccountSvc) convertAccountLogToPB(log *po.AccountLog) *pb.AccountLog {
	return &pb.AccountLog{
		LogId:           log.LogId,
		AccountId:       log.AccountId,
		UserId:          log.UserId,
		TransactionType: log.TransactionType,
		Amount:          log.Amount,
		BalanceBefore:   log.BalanceBefore,
		BalanceAfter:    log.BalanceAfter,
		OrderId:         log.OrderId,
		BookId:          log.BookId,
		ChapterId:       log.ChapterId,
		Description:     log.Description,
		ExtraData:       log.ExtraData,
		CreatedAt:       log.CreatedAt.Unix(),
	}
}
