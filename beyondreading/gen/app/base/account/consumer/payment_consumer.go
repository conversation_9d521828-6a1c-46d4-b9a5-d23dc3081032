package consumer

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"creativematrix.com/beyondreading/app/base/account/dao"
	"creativematrix.com/beyondreading/common/po"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/mq"
)

type PaymentConsumer struct {
	dao *dao.Dao
}

type PaymentMessage struct {
	Type      string  `json:"type"`
	UserId    uint64  `json:"userId"`
	OrderId   string  `json:"orderId"`
	Amount    int64   `json:"amount"`
	ProductId string  `json:"productId"`
}

func NewPaymentConsumer(dao *dao.Dao) *PaymentConsumer {
	return &PaymentConsumer{
		dao: dao,
	}
}

// HandleVipPurchase 处理VIP购买消息
func (c *PaymentConsumer) HandleVipPurchase(body []byte) error {
	var msg PaymentMessage
	if err := json.Unmarshal(body, &msg); err != nil {
		logger.LogErrorf("Failed to unmarshal VIP purchase message: %v", err)
		return err
	}

	logger.LogInfof("Processing VIP purchase: userId=%d, orderId=%s, productId=%s", 
		msg.UserId, msg.OrderId, msg.ProductId)

	// 获取用户账户
	ctx := context.Background()
	account, err := c.dao.GetAccountByUserId(ctx, msg.UserId)
	if err != nil {
		logger.LogErrorf("Failed to get account for VIP purchase: %v", err)
		return mq.RetryError // 重试
	}

	if account == nil {
		logger.LogErrorf("Account not found for user %d", msg.UserId)
		return fmt.Errorf("account not found")
	}

	// 根据产品ID确定VIP天数
	vipDays := c.getVipDays(msg.ProductId)
	if vipDays == 0 {
		logger.LogErrorf("Invalid VIP product: %s", msg.ProductId)
		return fmt.Errorf("invalid VIP product")
	}

	// 更新VIP状态
	now := time.Now()
	var newVipExpireTime time.Time
	
	if account.VipExpireTime != nil && account.VipExpireTime.After(now) {
		// 如果当前VIP未过期，在现有基础上延长
		newVipExpireTime = account.VipExpireTime.AddDate(0, 0, vipDays)
	} else {
		// 如果VIP已过期或首次购买，从现在开始计算
		newVipExpireTime = now.AddDate(0, 0, vipDays)
	}

	// 更新账户VIP状态
	err = c.dao.UpdateAccountVipStatus(ctx, msg.UserId, &newVipExpireTime)
	if err != nil {
		logger.LogErrorf("Failed to update VIP status: %v", err)
		return mq.RetryError // 重试
	}

	// 创建账户日志
	accountLog := &po.AccountLog{
		AccountId:       account.AccountId,
		UserId:          msg.UserId,
		TransactionType: "vip_purchase",
		Amount:          0, // VIP购买不涉及书币变动
		BalanceBefore:   account.CoinBalance,
		BalanceAfter:    account.CoinBalance,
		OrderId:         msg.OrderId,
		Description:     fmt.Sprintf("VIP purchase: %s", msg.ProductId),
		CreatedAt:       now,
	}

	err = c.dao.CreateAccountLog(ctx, accountLog)
	if err != nil {
		logger.LogErrorf("Failed to create account log for VIP purchase: %v", err)
		// 日志创建失败不影响主流程，只记录错误
	}

	logger.LogInfof("VIP purchase processed successfully: userId=%d, orderId=%s, expireTime=%s", 
		msg.UserId, msg.OrderId, newVipExpireTime.Format("2006-01-02 15:04:05"))

	return nil
}

// HandleMonthlySubscription 处理包月订阅消息
func (c *PaymentConsumer) HandleMonthlySubscription(body []byte) error {
	var msg PaymentMessage
	if err := json.Unmarshal(body, &msg); err != nil {
		logger.LogErrorf("Failed to unmarshal monthly subscription message: %v", err)
		return err
	}

	logger.LogInfof("Processing monthly subscription: userId=%d, orderId=%s, productId=%s", 
		msg.UserId, msg.OrderId, msg.ProductId)

	// 获取用户账户
	ctx := context.Background()
	account, err := c.dao.GetAccountByUserId(ctx, msg.UserId)
	if err != nil {
		logger.LogErrorf("Failed to get account for monthly subscription: %v", err)
		return mq.RetryError // 重试
	}

	if account == nil {
		logger.LogErrorf("Account not found for user %d", msg.UserId)
		return fmt.Errorf("account not found")
	}

	// 更新包月状态
	now := time.Now()
	var newMonthlyExpireTime time.Time
	
	if account.MonthlyExpireTime != nil && account.MonthlyExpireTime.After(now) {
		// 如果当前包月未过期，在现有基础上延长
		newMonthlyExpireTime = account.MonthlyExpireTime.AddDate(0, 1, 0)
	} else {
		// 如果包月已过期或首次购买，从现在开始计算
		newMonthlyExpireTime = now.AddDate(0, 1, 0)
	}

	// 更新账户包月状态
	err = c.dao.UpdateAccountMonthlyStatus(ctx, msg.UserId, &newMonthlyExpireTime)
	if err != nil {
		logger.LogErrorf("Failed to update monthly status: %v", err)
		return mq.RetryError // 重试
	}

	// 创建账户日志
	accountLog := &po.AccountLog{
		AccountId:       account.AccountId,
		UserId:          msg.UserId,
		TransactionType: "monthly_subscription",
		Amount:          0, // 包月订阅不涉及书币变动
		BalanceBefore:   account.CoinBalance,
		BalanceAfter:    account.CoinBalance,
		OrderId:         msg.OrderId,
		Description:     fmt.Sprintf("Monthly subscription: %s", msg.ProductId),
		CreatedAt:       now,
	}

	err = c.dao.CreateAccountLog(ctx, accountLog)
	if err != nil {
		logger.LogErrorf("Failed to create account log for monthly subscription: %v", err)
		// 日志创建失败不影响主流程，只记录错误
	}

	logger.LogInfof("Monthly subscription processed successfully: userId=%d, orderId=%s, expireTime=%s", 
		msg.UserId, msg.OrderId, newMonthlyExpireTime.Format("2006-01-02 15:04:05"))

	return nil
}

// HandleCoinRecharge 处理书币充值消息
func (c *PaymentConsumer) HandleCoinRecharge(body []byte) error {
	var msg PaymentMessage
	if err := json.Unmarshal(body, &msg); err != nil {
		logger.LogErrorf("Failed to unmarshal coin recharge message: %v", err)
		return err
	}

	logger.LogInfof("Processing coin recharge: userId=%d, orderId=%s, amount=%d", 
		msg.UserId, msg.OrderId, msg.Amount)

	// 根据产品ID获取书币数量
	coinAmount := c.getCoinAmount(msg.ProductId)
	if coinAmount == 0 {
		logger.LogErrorf("Invalid coin product: %s", msg.ProductId)
		return fmt.Errorf("invalid coin product")
	}

	// 更新账户余额
	ctx := context.Background()
	account, err := c.dao.UpdateAccountBalance(ctx, msg.UserId, coinAmount, "coin_recharge")
	if err != nil {
		logger.LogErrorf("Failed to update account balance for coin recharge: %v", err)
		return mq.RetryError // 重试
	}

	// 创建账户日志
	accountLog := &po.AccountLog{
		AccountId:       account.AccountId,
		UserId:          msg.UserId,
		TransactionType: "coin_recharge",
		Amount:          coinAmount,
		BalanceBefore:   account.CoinBalance - coinAmount,
		BalanceAfter:    account.CoinBalance,
		OrderId:         msg.OrderId,
		Description:     fmt.Sprintf("Coin recharge: %s", msg.ProductId),
		CreatedAt:       time.Now(),
	}

	err = c.dao.CreateAccountLog(ctx, accountLog)
	if err != nil {
		logger.LogErrorf("Failed to create account log for coin recharge: %v", err)
		// 日志创建失败不影响主流程，只记录错误
	}

	logger.LogInfof("Coin recharge processed successfully: userId=%d, orderId=%s, coinAmount=%.2f", 
		msg.UserId, msg.OrderId, coinAmount)

	return nil
}

// getVipDays 根据产品ID获取VIP天数
func (c *PaymentConsumer) getVipDays(productId string) int {
	switch productId {
	case "vip_monthly":
		return 30
	case "vip_yearly":
		return 365
	default:
		return 0
	}
}

// getCoinAmount 根据产品ID获取书币数量
func (c *PaymentConsumer) getCoinAmount(productId string) float64 {
	switch productId {
	case "coins_100":
		return 100.0
	case "coins_500":
		return 500.0
	case "coins_1000":
		return 1000.0
	default:
		return 0.0
	}
}
