package dao

import (
	"context"
	"fmt"
	"time"

	"creativematrix.com/beyondreading/gen/common/po"
	"creativematrix.com/beyondreading/pkg/mysql"
	"creativematrix.com/beyondreading/pkg/redis"
	"github.com/jmoiron/sqlx"
)

// Dao 数据访问层
type Dao struct {
	msshard *mysql.MSShard
	redis   *redis.Redis
}

// New 创建新的DAO实例
func New(msshard *mysql.MSShard, redis *redis.Redis) *Dao {
	return &Dao{
		msshard: msshard,
		redis:   redis,
	}
}

// GetDB 获取数据库连接
func (d *Dao) GetDB(userId uint64) (*sqlx.DB, error) {
	userIdStr := fmt.Sprintf("%d", userId)
	db, err := d.msshard.DB(userIdStr)
	if err != nil {
		return nil, fmt.Errorf("failed to get database connection: %w", err)
	}
	return db, nil
}

// GetTableName 获取分表名称
func (d *Dao) GetTableName(userId uint64, tableName string) string {
	userIdStr := fmt.Sprintf("%d", userId)
	return d.msshard.Table(userIdStr, tableName)
}

// PurchaseChapter 购买章节（支持批量购买）
func (d *Dao) PurchaseChapter(ctx context.Context, userId uint64, bookId string, chapterOrders []uint32, coinAmounts []float64) ([]*po.PurchaseOrder, error) {
	if len(chapterOrders) != len(coinAmounts) {
		return nil, fmt.Errorf("chapter orders and coin amounts length mismatch")
	}

	db, err := d.GetDB(userId)
	if err != nil {
		return nil, err
	}

	// 开启事务
	tx, err := db.BeginTxx(ctx, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	var orders []*po.PurchaseOrder
	now := time.Now()

	for i, chapterOrder := range chapterOrders {
		coinAmount := coinAmounts[i]

		// 生成订单ID
		orderId := fmt.Sprintf("PC%d%02d%02d%02d%02d%02d%03d",
			now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), now.Second(), i)

		// 创建购买订单
		order := &po.PurchaseOrder{
			OrderId:      orderId,
			UserId:       userId,
			OrderType:    "chapter",
			BookId:       bookId,
			ChapterOrder: chapterOrder,
			CoinAmount:   coinAmount,
			Status:       1, // 待支付
			CreatedAt:    now,
			UpdatedAt:    now,
		}

		// 插入订单
		query := `INSERT INTO purchase_order (order_id, user_id, order_type, book_id, 
				  chapter_order, coin_amount, status, created_at, updated_at) 
				  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`

		_, err = tx.ExecContext(ctx, query, order.OrderId, order.UserId, order.OrderType,
			order.BookId, order.ChapterOrder, order.CoinAmount, order.Status,
			order.CreatedAt, order.UpdatedAt)
		if err != nil {
			return nil, fmt.Errorf("failed to insert purchase order: %w", err)
		}

		orders = append(orders, order)
	}

	// 提交事务
	if err = tx.Commit(); err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	return orders, nil
}

// PurchaseVipMonthly 购买VIP或包月
func (d *Dao) PurchaseVipMonthly(ctx context.Context, userId uint64, orderType string, coinAmount float64, durationDays int32) (*po.VipMonthlyOrder, error) {
	db, err := d.GetDB(userId)
	if err != nil {
		return nil, err
	}

	now := time.Now()
	startTime := now
	endTime := now.AddDate(0, 0, int(durationDays))

	// 生成订单ID
	prefix := "PV"
	if orderType == "monthly" {
		prefix = "PM"
	}
	orderId := fmt.Sprintf("%s%d%02d%02d%02d%02d%02d",
		prefix, now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), now.Second())

	order := &po.VipMonthlyOrder{
		OrderId:      orderId,
		UserId:       userId,
		OrderType:    orderType,
		CoinAmount:   coinAmount,
		DurationDays: durationDays,
		StartTime:    &startTime,
		EndTime:      &endTime,
		Status:       1, // 待支付
		CreatedAt:    now,
		UpdatedAt:    now,
	}

	query := `INSERT INTO vipmonthly_order (order_id, user_id, order_type, coin_amount, 
			  duration_days, start_time, end_time, status, created_at, updated_at) 
			  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`

	_, err = db.ExecContext(ctx, query, order.OrderId, order.UserId, order.OrderType,
		order.CoinAmount, order.DurationDays, order.StartTime, order.EndTime,
		order.Status, order.CreatedAt, order.UpdatedAt)
	if err != nil {
		return nil, fmt.Errorf("failed to insert vip/monthly order: %w", err)
	}

	return order, nil
}

// UpdateOrderStatus 更新订单状态
func (d *Dao) UpdateOrderStatus(ctx context.Context, userId uint64, orderId string, status int32, orderType string) error {
	db, err := d.GetDB(userId)
	if err != nil {
		return err
	}

	var query string
	if orderType == "chapter" {
		query = `UPDATE purchase_order SET status = ?, updated_at = ? WHERE order_id = ? AND user_id = ?`
	} else {
		query = `UPDATE vipmonthly_order SET status = ?, updated_at = ? WHERE order_id = ? AND user_id = ?`
	}

	_, err = db.ExecContext(ctx, query, status, time.Now(), orderId, userId)
	if err != nil {
		return fmt.Errorf("failed to update order status: %w", err)
	}

	return nil
}

// CheckChapterPurchased 检查章节是否已购买
func (d *Dao) CheckChapterPurchased(ctx context.Context, userId uint64, bookId string, chapterOrder uint32) (bool, *time.Time, bool, bool, string, error) {
	db, err := d.GetDB(userId)
	if err != nil {
		return false, nil, false, false, "", err
	}

	// 检查直接购买的章节
	var order po.PurchaseOrder
	query := `SELECT order_id, created_at FROM purchase_order 
			  WHERE user_id = ? AND book_id = ? AND chapter_order = ? AND status = 2 
			  ORDER BY created_at DESC LIMIT 1`

	err = db.GetContext(ctx, &order, query, userId, bookId, chapterOrder)
	if err == nil {
		return true, &order.CreatedAt, false, false, order.OrderId, nil
	}

	// 检查VIP和包月状态
	now := time.Now()
	
	// 检查VIP状态
	var vipOrder po.VipMonthlyOrder
	vipQuery := `SELECT order_id, start_time, end_time FROM vipmonthly_order 
				 WHERE user_id = ? AND order_type = 'vip' AND status = 2 
				 AND start_time <= ? AND end_time >= ? 
				 ORDER BY created_at DESC LIMIT 1`

	err = db.GetContext(ctx, &vipOrder, vipQuery, userId, now, now)
	if err == nil {
		return true, vipOrder.StartTime, false, true, vipOrder.OrderId, nil
	}

	// 检查包月状态
	var monthlyOrder po.VipMonthlyOrder
	monthlyQuery := `SELECT order_id, start_time, end_time FROM vipmonthly_order 
					 WHERE user_id = ? AND order_type = 'monthly' AND status = 2 
					 AND start_time <= ? AND end_time >= ? 
					 ORDER BY created_at DESC LIMIT 1`

	err = db.GetContext(ctx, &monthlyOrder, monthlyQuery, userId, now, now)
	if err == nil {
		return true, monthlyOrder.StartTime, true, false, monthlyOrder.OrderId, nil
	}

	return false, nil, false, false, "", nil
}

// GetPurchaseOrders 获取购买订单列表
func (d *Dao) GetPurchaseOrders(ctx context.Context, userId uint64, page, pageSize int32, bookId string) ([]*po.PurchaseOrder, int64, error) {
	db, err := d.GetDB(userId)
	if err != nil {
		return nil, 0, err
	}

	// 构建查询条件
	whereClause := "WHERE user_id = ?"
	args := []interface{}{userId}

	if bookId != "" {
		whereClause += " AND book_id = ?"
		args = append(args, bookId)
	}

	// 获取总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM purchase_order %s", whereClause)
	var total int64
	err = db.GetContext(ctx, &total, countQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get purchase orders count: %w", err)
	}

	// 获取分页数据
	offset := (page - 1) * pageSize
	dataQuery := fmt.Sprintf(`SELECT order_id, account_id, user_id, order_type, book_id, book_name, 
							  chapter_id, chapter_title, chapter_order, coin_amount, status, 
							  created_at, updated_at 
							  FROM purchase_order %s ORDER BY created_at DESC LIMIT ? OFFSET ?`, whereClause)
	args = append(args, pageSize, offset)

	var orders []*po.PurchaseOrder
	err = db.SelectContext(ctx, &orders, dataQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get purchase orders: %w", err)
	}

	return orders, total, nil
}

// GetVipMonthlyOrders 获取VIP/包月订单列表
func (d *Dao) GetVipMonthlyOrders(ctx context.Context, userId uint64, page, pageSize int32, orderType string) ([]*po.VipMonthlyOrder, int64, error) {
	db, err := d.GetDB(userId)
	if err != nil {
		return nil, 0, err
	}

	// 构建查询条件
	whereClause := "WHERE user_id = ?"
	args := []interface{}{userId}

	if orderType != "" {
		whereClause += " AND order_type = ?"
		args = append(args, orderType)
	}

	// 获取总数
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM vipmonthly_order %s", whereClause)
	var total int64
	err = db.GetContext(ctx, &total, countQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get vip/monthly orders count: %w", err)
	}

	// 获取分页数据
	offset := (page - 1) * pageSize
	dataQuery := fmt.Sprintf(`SELECT order_id, account_id, user_id, order_type, coin_amount, 
							  duration_days, start_time, end_time, status, created_at, updated_at 
							  FROM vipmonthly_order %s ORDER BY created_at DESC LIMIT ? OFFSET ?`, whereClause)
	args = append(args, pageSize, offset)

	var orders []*po.VipMonthlyOrder
	err = db.SelectContext(ctx, &orders, dataQuery, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get vip/monthly orders: %w", err)
	}

	return orders, total, nil
}

// GetPurchasedChapters 获取已购买的章节列表
func (d *Dao) GetPurchasedChapters(ctx context.Context, userId uint64, bookId string, page, pageSize int32) ([]*po.ChapterPurchaseInfo, int64, error) {
	db, err := d.GetDB(userId)
	if err != nil {
		return nil, 0, err
	}

	// 获取直接购买的章节
	query := `SELECT order_id, chapter_id, chapter_title, chapter_order, coin_amount, created_at 
			  FROM purchase_order 
			  WHERE user_id = ? AND book_id = ? AND status = 2 
			  ORDER BY chapter_order`

	var purchaseOrders []struct {
		OrderId      string    `db:"order_id"`
		ChapterId    string    `db:"chapter_id"`
		ChapterTitle string    `db:"chapter_title"`
		ChapterOrder uint32    `db:"chapter_order"`
		CoinAmount   float64   `db:"coin_amount"`
		CreatedAt    time.Time `db:"created_at"`
	}

	err = db.SelectContext(ctx, &purchaseOrders, query, userId, bookId)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get purchased chapters: %w", err)
	}

	var chapters []*po.ChapterPurchaseInfo
	for _, order := range purchaseOrders {
		chapters = append(chapters, &po.ChapterPurchaseInfo{
			OrderId:      order.OrderId,
			ChapterId:    order.ChapterId,
			ChapterTitle: order.ChapterTitle,
			ChapterOrder: order.ChapterOrder,
			CoinAmount:   order.CoinAmount,
			PurchasedAt:  order.CreatedAt,
			IsMonthly:    false,
			IsVip:        false,
		})
	}

	return chapters, int64(len(chapters)), nil
}
