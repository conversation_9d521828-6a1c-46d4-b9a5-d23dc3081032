package svc

import (
	"context"
	"fmt"
	"time"

	"creativematrix.com/beyondreading/gen/app/base/purchase/dao"
	"creativematrix.com/beyondreading/gen/common/po"
	accountpb "creativematrix.com/beyondreading/gen/proto/account"
	pb "creativematrix.com/beyondreading/gen/proto/purchase"
	"creativematrix.com/beyondreading/pkg/logger"
)

// PurchaseSvc 购买服务
type PurchaseSvc struct {
	dao           *dao.Dao
	accountClient accountpb.AccountClient
}

// New 创建购买服务实例
func New(dao *dao.Dao, accountClient accountpb.AccountClient) *PurchaseSvc {
	return &PurchaseSvc{
		dao:           dao,
		accountClient: accountClient,
	}
}

// PurchaseChapter 购买章节（支持批量购买）
func (s *PurchaseSvc) PurchaseChapter(ctx context.Context, req *pb.PurchaseChapterReq) (*pb.PurchaseChapterResp, error) {
	if len(req.Chapters) == 0 {
		return &pb.PurchaseChapterResp{
			Code:    400,
			Message: "No chapters to purchase",
		}, nil
	}

	// 提取章节序号和金额
	var chapterOrders []uint32
	var coinAmounts []float64
	totalAmount := 0.0

	for _, chapter := range req.Chapters {
		chapterOrders = append(chapterOrders, chapter.ChapterOrder)
		coinAmounts = append(coinAmounts, chapter.CoinAmount)
		totalAmount += chapter.CoinAmount
	}

	// 检查用户余额
	accountResp, err := s.accountClient.GetAccount(ctx, &accountpb.GetAccountReq{
		UserId: req.UserId,
	})
	if err != nil {
		logger.LogErrorf("Failed to get account: %v", err)
		return &pb.PurchaseChapterResp{
			Code:    500,
			Message: "Failed to get account information",
		}, nil
	}

	if accountResp.Code != 200 {
		return &pb.PurchaseChapterResp{
			Code:    accountResp.Code,
			Message: accountResp.Message,
		}, nil
	}

	if accountResp.Account.CoinBalance < totalAmount {
		return &pb.PurchaseChapterResp{
			Code:    400,
			Message: fmt.Sprintf("Insufficient balance: required=%.2f, available=%.2f", totalAmount, accountResp.Account.CoinBalance),
		}, nil
	}

	// 创建购买订单
	orders, err := s.dao.PurchaseChapter(ctx, req.UserId, req.BookId, chapterOrders, coinAmounts)
	if err != nil {
		logger.LogErrorf("Failed to create purchase orders: %v", err)
		return &pb.PurchaseChapterResp{
			Code:    500,
			Message: "Failed to create purchase orders",
		}, nil
	}

	// 扣除书币
	for i, order := range orders {
		deductReq := &accountpb.DeductCoinsReq{
			UserId:          req.UserId,
			Amount:          coinAmounts[i],
			OrderId:         order.OrderId,
			BookId:          req.BookId,
			ChapterId:       fmt.Sprintf("chapter_%03d", chapterOrders[i]),
			TransactionType: "purchase_chapter",
			Description:     fmt.Sprintf("Purchase chapter %d of book %s", chapterOrders[i], req.BookId),
		}

		deductResp, err := s.accountClient.DeductCoins(ctx, deductReq)
		if err != nil {
			logger.LogErrorf("Failed to deduct coins: %v", err)
			return &pb.PurchaseChapterResp{
				Code:    500,
				Message: "Failed to deduct coins",
			}, nil
		}

		if deductResp.Code != 200 {
			return &pb.PurchaseChapterResp{
				Code:    deductResp.Code,
				Message: deductResp.Message,
			}, nil
		}

		// 更新订单状态为支付成功
		err = s.dao.UpdateOrderStatus(ctx, req.UserId, order.OrderId, 2, "chapter")
		if err != nil {
			logger.LogErrorf("Failed to update order status: %v", err)
		}
	}

	// 转换为protobuf格式
	var pbOrders []*pb.PurchaseOrder
	for _, order := range orders {
		pbOrders = append(pbOrders, s.convertPurchaseOrderToPB(order))
	}

	return &pb.PurchaseChapterResp{
		Code:    200,
		Message: "Purchase successful",
		Orders:  pbOrders,
	}, nil
}

// PurchaseMonthly 购买包月
func (s *PurchaseSvc) PurchaseMonthly(ctx context.Context, req *pb.PurchaseMonthlyReq) (*pb.PurchaseMonthlyResp, error) {
	if req.DurationDays <= 0 {
		req.DurationDays = 30 // 默认30天
	}

	// 检查用户余额
	accountResp, err := s.accountClient.GetAccount(ctx, &accountpb.GetAccountReq{
		UserId: req.UserId,
	})
	if err != nil {
		logger.LogErrorf("Failed to get account: %v", err)
		return &pb.PurchaseMonthlyResp{
			Code:    500,
			Message: "Failed to get account information",
		}, nil
	}

	if accountResp.Code != 200 {
		return &pb.PurchaseMonthlyResp{
			Code:    accountResp.Code,
			Message: accountResp.Message,
		}, nil
	}

	if accountResp.Account.CoinBalance < req.CoinAmount {
		return &pb.PurchaseMonthlyResp{
			Code:    400,
			Message: fmt.Sprintf("Insufficient balance: required=%.2f, available=%.2f", req.CoinAmount, accountResp.Account.CoinBalance),
		}, nil
	}

	// 创建包月订单
	order, err := s.dao.PurchaseVipMonthly(ctx, req.UserId, "monthly", req.CoinAmount, req.DurationDays)
	if err != nil {
		logger.LogErrorf("Failed to create monthly order: %v", err)
		return &pb.PurchaseMonthlyResp{
			Code:    500,
			Message: "Failed to create monthly order",
		}, nil
	}

	// 扣除书币
	deductReq := &accountpb.DeductCoinsReq{
		UserId:          req.UserId,
		Amount:          req.CoinAmount,
		OrderId:         order.OrderId,
		TransactionType: "purchase_monthly",
		Description:     fmt.Sprintf("Purchase monthly subscription for %d days", req.DurationDays),
	}

	deductResp, err := s.accountClient.DeductCoins(ctx, deductReq)
	if err != nil {
		logger.LogErrorf("Failed to deduct coins: %v", err)
		return &pb.PurchaseMonthlyResp{
			Code:    500,
			Message: "Failed to deduct coins",
		}, nil
	}

	if deductResp.Code != 200 {
		return &pb.PurchaseMonthlyResp{
			Code:    deductResp.Code,
			Message: deductResp.Message,
		}, nil
	}

	// 更新订单状态为支付成功
	err = s.dao.UpdateOrderStatus(ctx, req.UserId, order.OrderId, 2, "monthly")
	if err != nil {
		logger.LogErrorf("Failed to update order status: %v", err)
	}

	// 更新用户状态
	updateReq := &accountpb.UpdateUserStatusReq{
		UserId:            req.UserId,
		UserType:          3, // 包月用户
		MonthlyExpireTime: order.EndTime.Unix(),
	}

	_, err = s.accountClient.UpdateUserStatus(ctx, updateReq)
	if err != nil {
		logger.LogErrorf("Failed to update user status: %v", err)
	}

	return &pb.PurchaseMonthlyResp{
		Code:    200,
		Message: "Monthly subscription purchased successfully",
		Order:   s.convertVipMonthlyOrderToPB(order),
	}, nil
}

// PurchaseVip 购买VIP
func (s *PurchaseSvc) PurchaseVip(ctx context.Context, req *pb.PurchaseVipReq) (*pb.PurchaseVipResp, error) {
	if req.DurationDays <= 0 {
		req.DurationDays = 30 // 默认30天
	}

	// 检查用户余额
	accountResp, err := s.accountClient.GetAccount(ctx, &accountpb.GetAccountReq{
		UserId: req.UserId,
	})
	if err != nil {
		logger.LogErrorf("Failed to get account: %v", err)
		return &pb.PurchaseVipResp{
			Code:    500,
			Message: "Failed to get account information",
		}, nil
	}

	if accountResp.Code != 200 {
		return &pb.PurchaseVipResp{
			Code:    accountResp.Code,
			Message: accountResp.Message,
		}, nil
	}

	if accountResp.Account.CoinBalance < req.CoinAmount {
		return &pb.PurchaseVipResp{
			Code:    400,
			Message: fmt.Sprintf("Insufficient balance: required=%.2f, available=%.2f", req.CoinAmount, accountResp.Account.CoinBalance),
		}, nil
	}

	// 创建VIP订单
	order, err := s.dao.PurchaseVipMonthly(ctx, req.UserId, "vip", req.CoinAmount, req.DurationDays)
	if err != nil {
		logger.LogErrorf("Failed to create VIP order: %v", err)
		return &pb.PurchaseVipResp{
			Code:    500,
			Message: "Failed to create VIP order",
		}, nil
	}

	// 扣除书币
	deductReq := &accountpb.DeductCoinsReq{
		UserId:          req.UserId,
		Amount:          req.CoinAmount,
		OrderId:         order.OrderId,
		TransactionType: "purchase_vip",
		Description:     fmt.Sprintf("Purchase VIP subscription for %d days", req.DurationDays),
	}

	deductResp, err := s.accountClient.DeductCoins(ctx, deductReq)
	if err != nil {
		logger.LogErrorf("Failed to deduct coins: %v", err)
		return &pb.PurchaseVipResp{
			Code:    500,
			Message: "Failed to deduct coins",
		}, nil
	}

	if deductResp.Code != 200 {
		return &pb.PurchaseVipResp{
			Code:    deductResp.Code,
			Message: deductResp.Message,
		}, nil
	}

	// 更新订单状态为支付成功
	err = s.dao.UpdateOrderStatus(ctx, req.UserId, order.OrderId, 2, "vip")
	if err != nil {
		logger.LogErrorf("Failed to update order status: %v", err)
	}

	// 更新用户状态
	updateReq := &accountpb.UpdateUserStatusReq{
		UserId:        req.UserId,
		UserType:      2, // VIP用户
		VipExpireTime: order.EndTime.Unix(),
	}

	_, err = s.accountClient.UpdateUserStatus(ctx, updateReq)
	if err != nil {
		logger.LogErrorf("Failed to update user status: %v", err)
	}

	return &pb.PurchaseVipResp{
		Code:    200,
		Message: "VIP subscription purchased successfully",
		Order:   s.convertVipMonthlyOrderToPB(order),
	}, nil
}

// GetPurchaseOrders 获取购买订单列表
func (s *PurchaseSvc) GetPurchaseOrders(ctx context.Context, req *pb.GetPurchaseOrdersReq) (*pb.GetPurchaseOrdersResp, error) {
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	orders, total, err := s.dao.GetPurchaseOrders(ctx, req.UserId, req.Page, req.PageSize, req.BookId)
	if err != nil {
		logger.LogErrorf("Failed to get purchase orders: %v", err)
		return &pb.GetPurchaseOrdersResp{
			Code:    500,
			Message: "Failed to get purchase orders",
		}, nil
	}

	var pbOrders []*pb.PurchaseOrder
	for _, order := range orders {
		pbOrders = append(pbOrders, s.convertPurchaseOrderToPB(order))
	}

	return &pb.GetPurchaseOrdersResp{
		Code:    200,
		Message: "Success",
		Orders:  pbOrders,
		Total:   total,
	}, nil
}

// GetVipMonthlyOrders 获取VIP/包月订单列表
func (s *PurchaseSvc) GetVipMonthlyOrders(ctx context.Context, req *pb.GetVipMonthlyOrdersReq) (*pb.GetVipMonthlyOrdersResp, error) {
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	orders, total, err := s.dao.GetVipMonthlyOrders(ctx, req.UserId, req.Page, req.PageSize, req.OrderType)
	if err != nil {
		logger.LogErrorf("Failed to get vip/monthly orders: %v", err)
		return &pb.GetVipMonthlyOrdersResp{
			Code:    500,
			Message: "Failed to get vip/monthly orders",
		}, nil
	}

	var pbOrders []*pb.VipMonthlyOrder
	for _, order := range orders {
		pbOrders = append(pbOrders, s.convertVipMonthlyOrderToPB(order))
	}

	return &pb.GetVipMonthlyOrdersResp{
		Code:    200,
		Message: "Success",
		Orders:  pbOrders,
		Total:   total,
	}, nil
}

// CheckChapterPurchased 检查章节购买状态
func (s *PurchaseSvc) CheckChapterPurchased(ctx context.Context, req *pb.CheckChapterPurchasedReq) (*pb.CheckChapterPurchasedResp, error) {
	isPurchased, purchasedAt, isMonthly, isVip, orderId, err := s.dao.CheckChapterPurchased(ctx, req.UserId, req.BookId, req.ChapterOrder)
	if err != nil {
		logger.LogErrorf("Failed to check chapter purchased: %v", err)
		return &pb.CheckChapterPurchasedResp{
			Code:    500,
			Message: "Failed to check chapter purchased",
		}, nil
	}

	resp := &pb.CheckChapterPurchasedResp{
		Code:        200,
		Message:     "Success",
		IsPurchased: isPurchased,
		IsMonthly:   isMonthly,
		IsVip:       isVip,
		OrderId:     orderId,
	}

	if purchasedAt != nil {
		resp.PurchasedAt = purchasedAt.Unix()
	}

	return resp, nil
}

// CheckVipStatus 检查VIP状态
func (s *PurchaseSvc) CheckVipStatus(ctx context.Context, req *pb.CheckVipStatusReq) (*pb.CheckVipStatusResp, error) {
	// 通过账户服务检查VIP状态
	statusResp, err := s.accountClient.CheckUserStatus(ctx, &accountpb.CheckUserStatusReq{
		UserId: req.UserId,
	})
	if err != nil {
		logger.LogErrorf("Failed to check VIP status: %v", err)
		return &pb.CheckVipStatusResp{
			Code:    500,
			Message: "Failed to check VIP status",
		}, nil
	}

	if statusResp.Code != 200 {
		return &pb.CheckVipStatusResp{
			Code:    statusResp.Code,
			Message: statusResp.Message,
		}, nil
	}

	resp := &pb.CheckVipStatusResp{
		Code:     200,
		Message:  "Success",
		IsActive: statusResp.HasVip,
	}

	if statusResp.Account.VipExpireTime > 0 {
		// 这里简化处理，实际应该从VIP订单中获取开始时间
		resp.StartTime = statusResp.Account.VipExpireTime - 30*24*3600 // 假设30天前开始
		resp.EndTime = statusResp.Account.VipExpireTime
	}

	return resp, nil
}

// CheckMonthlyStatus 检查包月状态
func (s *PurchaseSvc) CheckMonthlyStatus(ctx context.Context, req *pb.CheckMonthlyStatusReq) (*pb.CheckMonthlyStatusResp, error) {
	// 通过账户服务检查包月状态
	statusResp, err := s.accountClient.CheckUserStatus(ctx, &accountpb.CheckUserStatusReq{
		UserId: req.UserId,
	})
	if err != nil {
		logger.LogErrorf("Failed to check monthly status: %v", err)
		return &pb.CheckMonthlyStatusResp{
			Code:    500,
			Message: "Failed to check monthly status",
		}, nil
	}

	if statusResp.Code != 200 {
		return &pb.CheckMonthlyStatusResp{
			Code:    statusResp.Code,
			Message: statusResp.Message,
		}, nil
	}

	resp := &pb.CheckMonthlyStatusResp{
		Code:     200,
		Message:  "Success",
		IsActive: statusResp.HasMonthly,
	}

	if statusResp.Account.MonthlyExpireTime > 0 {
		// 这里简化处理，实际应该从包月订单中获取开始时间
		resp.StartTime = statusResp.Account.MonthlyExpireTime - 30*24*3600 // 假设30天前开始
		resp.EndTime = statusResp.Account.MonthlyExpireTime
	}

	return resp, nil
}

// GetPurchasedChapters 获取已购买的章节列表
func (s *PurchaseSvc) GetPurchasedChapters(ctx context.Context, req *pb.GetPurchasedChaptersReq) (*pb.GetPurchasedChaptersResp, error) {
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	chapters, total, err := s.dao.GetPurchasedChapters(ctx, req.UserId, req.BookId, req.Page, req.PageSize)
	if err != nil {
		logger.LogErrorf("Failed to get purchased chapters: %v", err)
		return &pb.GetPurchasedChaptersResp{
			Code:    500,
			Message: "Failed to get purchased chapters",
		}, nil
	}

	var pbChapters []*pb.ChapterPurchaseInfo
	for _, chapter := range chapters {
		pbChapters = append(pbChapters, s.convertChapterPurchaseInfoToPB(chapter))
	}

	return &pb.GetPurchasedChaptersResp{
		Code:     200,
		Message:  "Success",
		Chapters: pbChapters,
		Total:    total,
	}, nil
}

// convertPurchaseOrderToPB 转换PurchaseOrder为protobuf格式
func (s *PurchaseSvc) convertPurchaseOrderToPB(order *po.PurchaseOrder) *pb.PurchaseOrder {
	return &pb.PurchaseOrder{
		OrderId:      order.OrderId,
		AccountId:    order.AccountId,
		UserId:       order.UserId,
		OrderType:    order.OrderType,
		BookId:       order.BookId,
		BookName:     order.BookName,
		ChapterId:    order.ChapterId,
		ChapterTitle: order.ChapterTitle,
		ChapterOrder: order.ChapterOrder,
		CoinAmount:   order.CoinAmount,
		Status:       order.Status,
		CreatedAt:    order.CreatedAt.Unix(),
		UpdatedAt:    order.UpdatedAt.Unix(),
	}
}

// convertVipMonthlyOrderToPB 转换VipMonthlyOrder为protobuf格式
func (s *PurchaseSvc) convertVipMonthlyOrderToPB(order *po.VipMonthlyOrder) *pb.VipMonthlyOrder {
	pbOrder := &pb.VipMonthlyOrder{
		OrderId:      order.OrderId,
		AccountId:    order.AccountId,
		UserId:       order.UserId,
		OrderType:    order.OrderType,
		CoinAmount:   order.CoinAmount,
		DurationDays: order.DurationDays,
		Status:       order.Status,
		CreatedAt:    order.CreatedAt.Unix(),
		UpdatedAt:    order.UpdatedAt.Unix(),
	}

	if order.StartTime != nil {
		pbOrder.StartTime = order.StartTime.Unix()
	}

	if order.EndTime != nil {
		pbOrder.EndTime = order.EndTime.Unix()
	}

	return pbOrder
}

// convertChapterPurchaseInfoToPB 转换ChapterPurchaseInfo为protobuf格式
func (s *PurchaseSvc) convertChapterPurchaseInfoToPB(info *po.ChapterPurchaseInfo) *pb.ChapterPurchaseInfo {
	return &pb.ChapterPurchaseInfo{
		OrderId:      info.OrderId,
		ChapterId:    info.ChapterId,
		ChapterTitle: info.ChapterTitle,
		ChapterOrder: info.ChapterOrder,
		CoinAmount:   info.CoinAmount,
		PurchasedAt:  info.PurchasedAt.Unix(),
		IsMonthly:    info.IsMonthly,
		IsVip:        info.IsVip,
	}
}
