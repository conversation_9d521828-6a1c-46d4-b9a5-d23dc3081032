package grpc

import (
	"net"
	"time"

	"creativematrix.com/beyondreading/gen/app/base/payment/conf"
	"creativematrix.com/beyondreading/gen/app/base/payment/svc"
	pb "creativematrix.com/beyondreading/gen/proto/payment"
	"creativematrix.com/beyondreading/pkg/discovery"
	"creativematrix.com/beyondreading/pkg/gm"
	"creativematrix.com/beyondreading/pkg/logger"
	"google.golang.org/grpc"
	"google.golang.org/grpc/keepalive"
)

const App = "base-payment"

func Start(c *conf.Config, s *svc.PaymentSvc) (*grpc.Server, error) {
	lis, err := net.Listen("tcp", c.GRPC.Addr)
	if err != nil {
		return nil, err
	}

	server := grpc.NewServer(
		grpc.KeepaliveParams(keepalive.ServerParameters{
			MaxConnectionIdle:     time.Minute * 5,
			MaxConnectionAge:      time.Hour,
			MaxConnectionAgeGrace: time.Minute * 5,
			Time:                  time.Minute * 10,
			Timeout:               time.Second * 3,
		}),
		grpc.UnaryInterceptor(gm.UnaryServerInterceptor()),
	)

	pb.RegisterPaymentServer(server, s)

	dis := discovery.New(c.Etcd.Addrs, logger.Log)
	defer dis.Close()
	if err := dis.Register(App, c.GRPC.Addr, 10); err != nil {
		panic(err)
	}

	go func() {
		if err := server.Serve(lis); err != nil {
			panic(err)
		}
	}()

	return server, nil
}
