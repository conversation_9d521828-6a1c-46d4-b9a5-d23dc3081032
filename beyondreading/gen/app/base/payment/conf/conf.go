package conf

import (
	"fmt"
	"creativematrix.com/beyondreading/pkg/config"
)

type Config struct {
	config.Base

	Log struct {
		Level string `toml:"level"`
	} `toml:"log"`

	MongoDB struct {
		URI      string `toml:"uri"`
		Database string `toml:"database"`
	} `toml:"mongodb"`

	MySQL struct {
		DSN string `toml:"dsn"`
	} `toml:"mysql"`

	RabbitMQ struct {
		URL      string `toml:"url"`
		Exchange string `toml:"exchange"`
	} `toml:"rabbitmq"`

	Redis struct {
		Addr     string `toml:"addr"`
		Password string `toml:"password"`
		DB       int    `toml:"db"`
	} `toml:"redis"`

	GooglePay struct {
		PackageName   string `toml:"package_name"`
		ServiceAccount string `toml:"service_account"`
		PrivateKey    string `toml:"private_key"`
	} `toml:"google_pay"`

	ApplePay struct {
		BundleId    string `toml:"bundle_id"`
		Environment string `toml:"environment"` // sandbox or production
		SharedSecret string `toml:"shared_secret"`
	} `toml:"apple_pay"`

	PayPal struct {
		ClientId     string `toml:"client_id"`
		ClientSecret string `toml:"client_secret"`
		Environment  string `toml:"environment"` // sandbox or live
		WebhookId    string `toml:"webhook_id"`
	} `toml:"paypal"`

	Alipay struct {
		AppId      string `toml:"app_id"`
		PrivateKey string `toml:"private_key"`
		PublicKey  string `toml:"public_key"`
		NotifyUrl  string `toml:"notify_url"`
		ReturnUrl  string `toml:"return_url"`
		Gateway    string `toml:"gateway"`
	} `toml:"alipay"`

	WechatPay struct {
		AppId     string `toml:"app_id"`
		MchId     string `toml:"mch_id"`
		ApiKey    string `toml:"api_key"`
		NotifyUrl string `toml:"notify_url"`
		Gateway   string `toml:"gateway"`
	} `toml:"wechat_pay"`

	Payment struct {
		OrderExpireMinutes int32  `toml:"order_expire_minutes"`
		CallbackRetryTimes int32  `toml:"callback_retry_times"`
		RefundTimeoutDays  int32  `toml:"refund_timeout_days"`
		DefaultCurrency    string `toml:"default_currency"`
	} `toml:"payment"`
}

func Load(app string) *Config {
	var conf = new(Config)
	if err := config.Load(app, conf); err != nil {
		panic(fmt.Sprintf("config load failed: %v", err))
	}
	return conf
}
