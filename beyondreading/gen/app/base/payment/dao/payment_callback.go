package dao

import (
	"context"
	"fmt"
	"time"

	"creativematrix.com/beyondreading/gen/common/po"
	"creativematrix.com/beyondreading/pkg/logger"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// CreatePaymentCallback 创建支付回调记录
func (d *Dao) CreatePaymentCallback(ctx context.Context, callback *po.PaymentCallback) error {
	callback.CreatedAt = time.Now()
	callback.Processed = false

	model := d.schema["payment_callbacks"]
	_, err := model.InsertOne(ctx, callback)
	if err != nil {
		logger.LogErrorf("Failed to create payment callback: %v", err)
		return fmt.Errorf("failed to create payment callback: %w", err)
	}

	return nil
}

// GetPaymentCallback 根据订单ID和交易ID获取回调记录
func (d *Dao) GetPaymentCallback(ctx context.Context, orderId, transactionId string) (*po.PaymentCallback, error) {
	model := d.schema["payment_callbacks"]
	filter := bson.M{
		"orderId":       orderId,
		"transactionId": transactionId,
	}

	var result po.PaymentCallback
	err := model.FindOne(ctx, filter).Decode(&result)
	if err != nil {
		if err.Error() == "mongo: no documents in result" {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get payment callback: %w", err)
	}

	return &result, nil
}

// GetUnprocessedCallbacks 获取未处理的回调记录
func (d *Dao) GetUnprocessedCallbacks(ctx context.Context, limit int32) ([]*po.PaymentCallback, error) {
	model := d.schema["payment_callbacks"]

	filter := bson.M{"processed": false}
	opts := options.Find().
		SetLimit(int64(limit)).
		SetSort(bson.D{{"createdAt", 1}})

	cursor, err := model.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to find unprocessed callbacks: %w", err)
	}
	defer cursor.Close(ctx)

	var callbacks []*po.PaymentCallback
	for cursor.Next(ctx) {
		var callback po.PaymentCallback
		if err := cursor.Decode(&callback); err != nil {
			logger.LogErrorf("Failed to decode payment callback: %v", err)
			continue
		}
		callbacks = append(callbacks, &callback)
	}

	return callbacks, nil
}

// MarkCallbackProcessed 标记回调为已处理
func (d *Dao) MarkCallbackProcessed(ctx context.Context, callbackId string) error {
	model := d.schema["payment_callbacks"]

	filter := bson.M{"_id": callbackId}
	update := bson.M{
		"$set": bson.M{
			"processed":   true,
			"processedAt": time.Now(),
		},
	}

	result, err := model.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to mark callback processed: %w", err)
	}

	if result.MatchedCount == 0 {
		return fmt.Errorf("callback not found: %s", callbackId)
	}

	return nil
}

// GetCallbacksByOrderId 根据订单ID获取所有回调记录
func (d *Dao) GetCallbacksByOrderId(ctx context.Context, orderId string) ([]*po.PaymentCallback, error) {
	model := d.schema["payment_callbacks"]

	filter := bson.M{"orderId": orderId}
	opts := options.Find().SetSort(bson.D{{"createdAt", -1}})

	cursor, err := model.Find(ctx, filter, opts)
	if err != nil {
		return nil, fmt.Errorf("failed to find callbacks by order id: %w", err)
	}
	defer cursor.Close(ctx)

	var callbacks []*po.PaymentCallback
	for cursor.Next(ctx) {
		var callback po.PaymentCallback
		if err := cursor.Decode(&callback); err != nil {
			logger.LogErrorf("Failed to decode payment callback: %v", err)
			continue
		}
		callbacks = append(callbacks, &callback)
	}

	return callbacks, nil
}

// CleanOldCallbacks 清理旧的回调记录
func (d *Dao) CleanOldCallbacks(ctx context.Context, days int32) error {
	model := d.schema["payment_callbacks"]

	cutoffTime := time.Now().AddDate(0, 0, -int(days))
	filter := bson.M{
		"createdAt": bson.M{"$lt": cutoffTime},
		"processed": true,
	}

	result, err := model.DeleteMany(ctx, filter)
	if err != nil {
		return fmt.Errorf("failed to clean old callbacks: %w", err)
	}

	logger.LogInfof("Cleaned %d old payment callbacks", result.DeletedCount)
	return nil
}
