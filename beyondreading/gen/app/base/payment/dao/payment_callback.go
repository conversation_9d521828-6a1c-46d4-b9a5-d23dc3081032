package dao

import (
	"context"
	"time"

	"creativematrix.com/beyondreading/gen/app/base/payment/model"
	"creativematrix.com/beyondreading/gen/common/po"
	"creativematrix.com/beyondreading/pkg/ecode"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/mongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// CreatePaymentCallback 创建支付回调记录
func (d *Dao) CreatePaymentCallback(ctx context.Context, callback *po.PaymentCallback) error {
	callback.CreatedAt = time.Now()
	callback.Processed = false

	insertedId, err := d.schema[model.TablePaymentCallbacks].Insert(callback, mongo.Ctx(ctx))
	if err != nil {
		logger.LogErrorf("Failed to create payment callback: %v", err)
		return err
	}

	// 获取插入的ID
	if oid, ok := insertedId.(primitive.ObjectID); ok {
		callback.ID = oid
	}

	return nil
}

// GetPaymentCallback 根据订单ID和交易ID获取回调记录
func (d *Dao) GetPaymentCallback(ctx context.Context, orderId, transactionId string) (*po.PaymentCallback, error) {
	filter := bson.M{
		"orderId":       orderId,
		"transactionId": transactionId,
	}

	var result po.PaymentCallback
	err := d.schema[model.TablePaymentCallbacks].FindOne(filter, &result, mongo.Ctx(ctx))
	if err != nil {
		if mongo.IsErrNoDocuments(err) {
			return nil, nil
		}
		logger.LogErrorf("Failed to get payment callback: %v", err)
		return nil, err
	}

	return &result, nil
}

// GetUnprocessedCallbacks 获取未处理的回调记录
func (d *Dao) GetUnprocessedCallbacks(ctx context.Context, limit int32) ([]*po.PaymentCallback, error) {
	filter := bson.M{"processed": false}

	var callbacks []*po.PaymentCallback
	err := d.schema[model.TablePaymentCallbacks].Find(filter, &callbacks, mongo.Ctx(ctx), mongo.Limit(int(limit)), mongo.Sort(bson.M{"createdAt": 1}))
	if err != nil {
		logger.LogErrorf("Failed to find unprocessed callbacks: %v", err)
		return nil, err
	}

	return callbacks, nil
}

// MarkCallbackProcessed 标记回调为已处理
func (d *Dao) MarkCallbackProcessed(ctx context.Context, callbackId string) error {
	// 转换callbackId为ObjectID
	oid, err := primitive.ObjectIDFromHex(callbackId)
	if err != nil {
		return err
	}

	filter := bson.M{"_id": oid}
	update := bson.M{
		"$set": bson.M{
			"processed":   true,
			"processedAt": time.Now(),
		},
	}

	err = d.schema[model.TablePaymentCallbacks].Update(filter, update, mongo.Ctx(ctx))
	if err != nil {
		logger.LogErrorf("Failed to mark callback processed: %v", err)
		return err
	}

	return nil
}

// GetCallbacksByOrderId 根据订单ID获取所有回调记录
func (d *Dao) GetCallbacksByOrderId(ctx context.Context, orderId string) ([]*po.PaymentCallback, error) {
	filter := bson.M{"orderId": orderId}

	var callbacks []*po.PaymentCallback
	err := d.schema[model.TablePaymentCallbacks].Find(filter, &callbacks, mongo.Ctx(ctx), mongo.Sort(bson.M{"createdAt": -1}))
	if err != nil {
		logger.LogErrorf("Failed to find callbacks by order id: %v", err)
		return nil, err
	}

	return callbacks, nil
}

// CleanOldCallbacks 清理旧的回调记录
func (d *Dao) CleanOldCallbacks(ctx context.Context, days int32) error {
	cutoffTime := time.Now().AddDate(0, 0, -int(days))
	filter := bson.M{
		"createdAt": bson.M{"$lt": cutoffTime},
		"processed": true,
	}

	deletedCount, err := d.schema[model.TablePaymentCallbacks].RemoveAll(filter, mongo.Ctx(ctx))
	if err != nil {
		logger.LogErrorf("Failed to clean old callbacks: %v", err)
		return err
	}

	logger.LogInfof("Cleaned %d old payment callbacks", deletedCount)
	return nil
}
