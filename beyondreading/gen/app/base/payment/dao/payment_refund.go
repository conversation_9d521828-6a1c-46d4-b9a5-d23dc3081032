package dao

import (
	"context"
	"time"

	"creativematrix.com/beyondreading/gen/app/base/payment/ecode"
	"creativematrix.com/beyondreading/gen/app/base/payment/model"
	"creativematrix.com/beyondreading/gen/common/po"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/mongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// CreatePaymentRefund 创建退款记录
func (d *Dao) CreatePaymentRefund(ctx context.Context, refund *po.PaymentRefund) error {
	refund.CreatedAt = time.Now()
	refund.UpdatedAt = refund.CreatedAt

	insertedId, err := d.schema[model.TablePaymentRefunds].Insert(refund, mongo.Ctx(ctx))
	if err != nil {
		logger.LogErrorf("Failed to create payment refund: %v", err)
		return err
	}

	// 获取插入的ID
	if oid, ok := insertedId.(primitive.ObjectID); ok {
		refund.ID = oid
	}

	return nil
}

// GetPaymentRefundById 根据退款ID获取退款记录
func (d *Dao) GetPaymentRefundById(ctx context.Context, refundId string) (*po.PaymentRefund, error) {
	filter := bson.M{"refundId": refundId}

	var result po.PaymentRefund
	err := d.schema[model.TablePaymentRefunds].FindOne(filter, &result, mongo.Ctx(ctx))
	if err != nil {
		if mongo.IsErrNoDocuments(err) {
			return nil, nil
		}
		logger.LogErrorf("Failed to get payment refund: %v", err)
		return nil, err
	}

	return &result, nil
}

// GetPaymentRefundsByOrderId 根据订单ID获取退款记录列表
func (d *Dao) GetPaymentRefundsByOrderId(ctx context.Context, orderId string) ([]*po.PaymentRefund, error) {
	filter := bson.M{"orderId": orderId}

	var refunds []*po.PaymentRefund
	err := d.schema[model.TablePaymentRefunds].Find(filter, &refunds, mongo.Ctx(ctx), mongo.Sort(bson.M{"createdAt": -1}))
	if err != nil {
		logger.LogErrorf("Failed to find refunds by order id: %v", err)
		return nil, err
	}

	return refunds, nil
}

// GetPaymentRefundsByUserId 根据用户ID获取退款记录列表
func (d *Dao) GetPaymentRefundsByUserId(ctx context.Context, userId uint64, page, pageSize int32) ([]*po.PaymentRefund, int64, error) {
	filter := bson.M{"userId": userId}

	// 获取总数
	total, err := d.schema[model.TablePaymentRefunds].Count(filter, mongo.Ctx(ctx))
	if err != nil {
		logger.LogErrorf("Failed to count payment refunds: %v", err)
		return nil, 0, err
	}

	// 分页查询
	skip := (page - 1) * pageSize
	var refunds []*po.PaymentRefund
	err = d.schema[model.TablePaymentRefunds].Find(filter, &refunds, mongo.Ctx(ctx), mongo.Skip(int(skip)), mongo.Limit(int(pageSize)), mongo.Sort(bson.M{"createdAt": -1}))
	if err != nil {
		logger.LogErrorf("Failed to find payment refunds: %v", err)
		return nil, 0, err
	}

	return refunds, total, nil
}

// UpdatePaymentRefund 更新退款记录
func (d *Dao) UpdatePaymentRefund(ctx context.Context, refundId string, updates bson.M) error {
	updates["updatedAt"] = time.Now()
	filter := bson.M{"refundId": refundId}
	update := bson.M{"$set": updates}

	err := d.schema[model.TablePaymentRefunds].Update(filter, update, mongo.Ctx(ctx))
	if err != nil {
		logger.LogErrorf("Failed to update payment refund: %v", err)
		return err
	}

	return nil
}

// UpdatePaymentRefundStatus 更新退款状态
func (d *Dao) UpdatePaymentRefundStatus(ctx context.Context, refundId string, status int32) error {
	updates := bson.M{
		"status": status,
	}

	if status == po.RefundStatusSuccess || status == po.RefundStatusFailed {
		updates["processedAt"] = time.Now()
	}

	return d.UpdatePaymentRefund(ctx, refundId, updates)
}
