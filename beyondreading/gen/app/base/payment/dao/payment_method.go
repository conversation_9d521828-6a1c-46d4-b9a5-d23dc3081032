package dao

import (
	"context"
	"encoding/json"
	"fmt"

	"creativematrix.com/beyondreading/gen/app/base/payment/ecode"
	"creativematrix.com/beyondreading/gen/app/base/payment/model"
	"creativematrix.com/beyondreading/gen/common/po"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/mongo"
	"go.mongodb.org/mongo-driver/bson"
)

// GetPaymentMethods 获取支付方式列表
func (d *Dao) GetPaymentMethods(ctx context.Context, enabled *bool) ([]*po.PaymentMethodConfig, error) {
	// 先从缓存获取
	if enabled == nil || *enabled {
		methods, err := d.getPaymentMethodsFromCache(ctx)
		if err == nil && methods != nil {
			return methods, nil
		}
	}

	filter := bson.M{}
	if enabled != nil {
		filter["enabled"] = *enabled
	}

	var methods []*po.PaymentMethodConfig
	err := d.schema[model.TablePaymentMethods].Find(filter, &methods, mongo.Ctx(ctx), mongo.Sort(bson.M{"method": 1}))
	if err != nil {
		logger.LogErrorf("Failed to find payment methods: %v", err)
		return nil, err
	}

	// 设置缓存（仅缓存启用的支付方式）
	if enabled == nil || *enabled {
		err = d.setPaymentMethodsCache(ctx, methods)
		if err != nil {
			logger.LogErrorf("Failed to set payment methods cache: %v", err)
		}
	}

	return methods, nil
}

// GetPaymentMethodByMethod 根据支付方式获取配置
func (d *Dao) GetPaymentMethodByMethod(ctx context.Context, method int32) (*po.PaymentMethodConfig, error) {
	// 先从缓存获取
	methodConfig, err := d.getPaymentMethodFromCache(ctx, method)
	if err == nil && methodConfig != nil {
		return methodConfig, nil
	}

	filter := bson.M{"method": method}

	var result po.PaymentMethodConfig
	err = d.schema[model.TablePaymentMethods].FindOne(filter, &result, mongo.Ctx(ctx))
	if err != nil {
		if mongo.IsErrNoDocuments(err) {
			return nil, nil
		}
		logger.LogErrorf("Failed to get payment method: %v", err)
		return nil, err
	}

	// 设置缓存
	err = d.setPaymentMethodCache(ctx, &result)
	if err != nil {
		logger.LogErrorf("Failed to set payment method cache: %v", err)
	}

	return &result, nil
}

// GetPaymentProducts 获取支付产品列表
func (d *Dao) GetPaymentProducts(ctx context.Context, productType *int32, enabled *bool) ([]*po.PaymentProduct, error) {
	filter := bson.M{}

	if productType != nil {
		filter["productType"] = *productType
	}
	if enabled != nil {
		filter["enabled"] = *enabled
	}

	var products []*po.PaymentProduct
	err := d.schema[model.TablePaymentProducts].Find(filter, &products, mongo.Ctx(ctx), mongo.Sort(bson.M{"sortOrder": 1, "createdAt": 1}))
	if err != nil {
		logger.LogErrorf("Failed to find payment products: %v", err)
		return nil, err
	}

	return products, nil
}

// GetPaymentProductById 根据产品ID获取支付产品
func (d *Dao) GetPaymentProductById(ctx context.Context, productId string) (*po.PaymentProduct, error) {
	// 先从缓存获取
	product, err := d.getPaymentProductFromCache(ctx, productId)
	if err == nil && product != nil {
		return product, nil
	}

	filter := bson.M{"productId": productId}

	var result po.PaymentProduct
	err = d.schema[model.TablePaymentProducts].FindOne(filter, &result, mongo.Ctx(ctx))
	if err != nil {
		if mongo.IsErrNoDocuments(err) {
			return nil, nil
		}
		logger.LogErrorf("Failed to get payment product: %v", err)
		return nil, err
	}

	// 设置缓存
	err = d.setPaymentProductCache(ctx, &result)
	if err != nil {
		logger.LogErrorf("Failed to set payment product cache: %v", err)
	}

	return &result, nil
}

// 缓存相关方法
func (d *Dao) getPaymentMethodsFromCache(ctx context.Context) ([]*po.PaymentMethodConfig, error) {
	key := "payment:methods:enabled"
	data, err := d.cache.RGetString(ctx, key)
	if err != nil {
		return nil, err
	}

	var methods []*po.PaymentMethodConfig
	err = json.Unmarshal([]byte(data), &methods)
	if err != nil {
		return nil, err
	}

	return methods, nil
}

func (d *Dao) setPaymentMethodsCache(ctx context.Context, methods []*po.PaymentMethodConfig) error {
	key := "payment:methods:enabled"
	data, err := json.Marshal(methods)
	if err != nil {
		return err
	}

	_, err = d.cache.RSet(ctx, key, string(data), model.CachePaymentMethodExpire)
	return err
}

func (d *Dao) getPaymentMethodFromCache(ctx context.Context, method int32) (*po.PaymentMethodConfig, error) {
	key := fmt.Sprintf(model.RedisPaymentMethodId, method)
	data, err := d.cache.RGetString(ctx, key)
	if err != nil {
		return nil, err
	}

	var methodConfig po.PaymentMethodConfig
	err = json.Unmarshal([]byte(data), &methodConfig)
	if err != nil {
		return nil, err
	}

	return &methodConfig, nil
}

func (d *Dao) setPaymentMethodCache(ctx context.Context, methodConfig *po.PaymentMethodConfig) error {
	key := fmt.Sprintf(model.RedisPaymentMethodId, methodConfig.Method)
	data, err := json.Marshal(methodConfig)
	if err != nil {
		return err
	}

	_, err = d.cache.RSet(ctx, key, string(data), model.CachePaymentMethodExpire)
	return err
}

func (d *Dao) getPaymentProductFromCache(ctx context.Context, productId string) (*po.PaymentProduct, error) {
	key := fmt.Sprintf(model.RedisPaymentProductId, productId)
	data, err := d.cache.RGetString(ctx, key)
	if err != nil {
		return nil, err
	}

	var product po.PaymentProduct
	err = json.Unmarshal([]byte(data), &product)
	if err != nil {
		return nil, err
	}

	return &product, nil
}

func (d *Dao) setPaymentProductCache(ctx context.Context, product *po.PaymentProduct) error {
	key := fmt.Sprintf(model.RedisPaymentProductId, product.ProductId)
	data, err := json.Marshal(product)
	if err != nil {
		return err
	}

	_, err = d.cache.RSet(ctx, key, string(data), model.CachePaymentProductExpire)
	return err
}
