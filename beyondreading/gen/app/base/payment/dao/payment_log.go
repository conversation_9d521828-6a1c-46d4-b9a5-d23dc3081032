package dao

import (
	"context"
	"time"

	"creativematrix.com/beyondreading/gen/app/base/payment/model"
	"creativematrix.com/beyondreading/gen/common/po"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/mongo"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// CreatePaymentLog 创建支付日志
func (d *Dao) CreatePaymentLog(ctx context.Context, log *po.PaymentLog) error {
	log.CreatedAt = time.Now()

	insertedId, err := d.schema[model.TablePaymentLogs].Insert(log, mongo.Ctx(ctx))
	if err != nil {
		logger.LogErrorf("Failed to create payment log: %v", err)
		return err
	}

	// 获取插入的ID
	if oid, ok := insertedId.(primitive.ObjectID); ok {
		log.ID = oid
	}

	return nil
}

// GetPaymentLogsByOrderId 根据订单ID获取支付日志
func (d *Dao) GetPaymentLogsByOrderId(ctx context.Context, orderId string, page, pageSize int32) ([]*po.PaymentLog, int64, error) {
	filter := bson.M{"orderId": orderId}

	// 获取总数
	total, err := d.schema[model.TablePaymentLogs].Count(filter, mongo.Ctx(ctx))
	if err != nil {
		logger.LogErrorf("Failed to count payment logs: %v", err)
		return nil, 0, err
	}

	// 分页查询
	skip := (page - 1) * pageSize
	var logs []*po.PaymentLog
	err = d.schema[model.TablePaymentLogs].Find(filter, &logs, mongo.Ctx(ctx), mongo.Skip(int(skip)), mongo.Limit(int(pageSize)), mongo.Sort(bson.M{"createdAt": -1}))
	if err != nil {
		logger.LogErrorf("Failed to find payment logs: %v", err)
		return nil, 0, err
	}

	return logs, total, nil
}

// GetPaymentLogsByUserId 根据用户ID获取支付日志
func (d *Dao) GetPaymentLogsByUserId(ctx context.Context, userId uint64, page, pageSize int32) ([]*po.PaymentLog, int64, error) {
	filter := bson.M{"userId": userId}

	// 获取总数
	total, err := d.schema[model.TablePaymentLogs].Count(filter, mongo.Ctx(ctx))
	if err != nil {
		logger.LogErrorf("Failed to count payment logs: %v", err)
		return nil, 0, err
	}

	// 分页查询
	skip := (page - 1) * pageSize
	var logs []*po.PaymentLog
	err = d.schema[model.TablePaymentLogs].Find(filter, &logs, mongo.Ctx(ctx), mongo.Skip(int(skip)), mongo.Limit(int(pageSize)), mongo.Sort(bson.M{"createdAt": -1}))
	if err != nil {
		logger.LogErrorf("Failed to find payment logs: %v", err)
		return nil, 0, err
	}

	return logs, total, nil
}

// CleanOldPaymentLogs 清理旧的支付日志
func (d *Dao) CleanOldPaymentLogs(ctx context.Context, days int32) error {
	cutoffTime := time.Now().AddDate(0, 0, -int(days))
	filter := bson.M{
		"createdAt": bson.M{"$lt": cutoffTime},
	}

	deletedCount, err := d.schema[model.TablePaymentLogs].RemoveAll(filter, mongo.Ctx(ctx))
	if err != nil {
		logger.LogErrorf("Failed to clean old payment logs: %v", err)
		return err
	}

	logger.LogInfof("Cleaned %d old payment logs", deletedCount)
	return nil
}

// LogPaymentAction 记录支付操作日志
func (d *Dao) LogPaymentAction(ctx context.Context, orderId string, userId uint64, action, status, message string, data map[string]string) error {
	log := &po.PaymentLog{
		OrderId: orderId,
		UserId:  userId,
		Action:  action,
		Status:  status,
		Message: message,
		Data:    data,
	}

	return d.CreatePaymentLog(ctx, log)
}
