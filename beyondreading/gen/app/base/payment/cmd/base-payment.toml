# Payment Base服务配置文件

[log]
level = "info"

[grpc]
addr = ":9004"
timeout = 30

[etcd]
addrs = ["localhost:2379"]
timeout = 5

[mongodb]
uri = "mongodb://localhost:27017"
database = "beyondreading_payment"

[mysql]
dsn = [
    "root:password@tcp(localhost:3306)/beyondreading_payment?charset=utf8mb4&parseTime=True&loc=Local"
]

[rabbitmq]
url = "amqp://guest:guest@localhost:5672/"
exchange = "payment"

[redis]
addr = "localhost:6379"
password = ""
db = 2

[google_pay]
package_name = "com.beyondreading.app"
service_account = "path/to/service-account.json"
private_key = "path/to/private-key.p8"

[apple_pay]
bundle_id = "com.beyondreading.app"
environment = "sandbox"
shared_secret = "your_apple_shared_secret"

[paypal]
client_id = "your_paypal_client_id"
client_secret = "your_paypal_client_secret"
environment = "sandbox"
webhook_id = "your_paypal_webhook_id"

[alipay]
app_id = "your_alipay_app_id"
private_key = "your_alipay_private_key"
public_key = "your_alipay_public_key"
notify_url = "https://api.beyondreading.com/payment/alipay/notify"
return_url = "https://app.beyondreading.com/payment/success"
gateway = "https://openapi.alipay.com/gateway.do"

[wechat_pay]
app_id = "your_wechat_app_id"
mch_id = "your_wechat_mch_id"
api_key = "your_wechat_api_key"
notify_url = "https://api.beyondreading.com/payment/wechat/notify"
gateway = "https://api.mch.weixin.qq.com"

[payment]
order_expire_minutes = 30
callback_retry_times = 3
refund_timeout_days = 7
default_currency = "USD"
