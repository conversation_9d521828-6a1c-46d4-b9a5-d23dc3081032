package api

import (
	"creativematrix.com/beyondreading/pkg/config"
	"creativematrix.com/beyondreading/pkg/discovery"
	"creativematrix.com/beyondreading/pkg/gm"
	"creativematrix.com/beyondreading/pkg/logger"
	pb "creativematrix.com/beyondreading/gen/proto/payment"
	"fmt"
	"google.golang.org/grpc"
	"google.golang.org/grpc/balancer/roundrobin"
	"google.golang.org/grpc/resolver"
)

const App = "base-payment"

func NewClient(c config.Base) (pb.PaymentClient, error) {
	options := []grpc.DialOption{
		grpc.WithInsecure(),
		grpc.WithDefaultServiceConfig(fmt.Sprintf(`{"LoadBalancingPolicy": "%s"}`, roundrobin.Name)),
		grpc.WithUnaryInterceptor(gm.UnaryClientInterceptor()),
	}

	r := discovery.NewResolver(c.Etcd.Addrs, logger.Log)
	resolver.Register(r)

	conn, err := grpc.NewClient("etcd:///"+App, options...)
	if err != nil {
		return nil, err
	}
	return pb.NewPaymentClient(conn), nil
}
