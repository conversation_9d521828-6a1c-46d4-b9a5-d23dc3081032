package service

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"creativematrix.com/beyondreading/gen/app/base/payment/conf"
	"creativematrix.com/beyondreading/gen/common/po"
	"creativematrix.com/beyondreading/pkg/logger"
)

type GooglePayService struct {
	config *conf.Config
	client *http.Client
}

type GooglePlayPurchase struct {
	PurchaseToken    string `json:"purchaseToken"`
	ProductId        string `json:"productId"`
	PurchaseTime     int64  `json:"purchaseTime"`
	PurchaseState    int    `json:"purchaseState"`
	ConsumptionState int    `json:"consumptionState"`
	DeveloperPayload string `json:"developerPayload"`
	OrderId          string `json:"orderId"`
	AcknowledgmentState int `json:"acknowledgmentState"`
}

type GooglePlaySubscription struct {
	StartTimeMillis          string `json:"startTimeMillis"`
	ExpiryTimeMillis         string `json:"expiryTimeMillis"`
	AutoRenewing             bool   `json:"autoRenewing"`
	PriceCurrencyCode        string `json:"priceCurrencyCode"`
	PriceAmountMicros        string `json:"priceAmountMicros"`
	PaymentState             int    `json:"paymentState"`
	CancelReason             int    `json:"cancelReason"`
	UserCancellationTimeMillis string `json:"userCancellationTimeMillis"`
	OrderId                  string `json:"orderId"`
	PurchaseToken            string `json:"purchaseToken"`
}

func NewGooglePayService(config *conf.Config) *GooglePayService {
	return &GooglePayService{
		config: config,
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

// CreatePayment 创建Google Pay支付
func (g *GooglePayService) CreatePayment(ctx context.Context, order *po.PaymentOrder) (*PaymentResult, error) {
	// Google Play支付通常在客户端完成，这里主要是生成支付数据
	paymentData := map[string]interface{}{
		"productId":   order.ProductId,
		"orderId":     order.OrderId,
		"packageName": g.config.GooglePay.PackageName,
		"amount":      order.Amount,
		"currency":    order.Currency,
	}

	paymentDataJson, err := json.Marshal(paymentData)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal payment data: %w", err)
	}

	return &PaymentResult{
		Success:     true,
		PaymentUrl:  "", // Google Play不需要支付URL
		PaymentData: string(paymentDataJson),
		Message:     "Payment data generated successfully",
		Metadata: map[string]string{
			"packageName": g.config.GooglePay.PackageName,
			"productId":   order.ProductId,
		},
	}, nil
}

// VerifyCallback 验证Google Play支付回调
func (g *GooglePayService) VerifyCallback(ctx context.Context, callback *po.PaymentCallback) (*CallbackResult, error) {
	purchaseToken, exists := callback.CallbackData["purchaseToken"]
	if !exists {
		return &CallbackResult{
			Valid:   false,
			Message: "Missing purchase token",
		}, nil
	}

	productId, exists := callback.CallbackData["productId"]
	if !exists {
		return &CallbackResult{
			Valid:   false,
			Message: "Missing product ID",
		}, nil
	}

	// 验证购买凭证
	isValid, purchaseInfo, err := g.verifyPurchase(ctx, productId, purchaseToken)
	if err != nil {
		logger.LogErrorf("Failed to verify Google Play purchase: %v", err)
		return &CallbackResult{
			Valid:   false,
			Message: fmt.Sprintf("Verification failed: %v", err),
		}, nil
	}

	if !isValid {
		return &CallbackResult{
			Valid:   false,
			Message: "Invalid purchase token",
		}, nil
	}

	// 确定支付状态
	status := po.OrderStatusFailed
	if purchaseInfo.PurchaseState == 1 { // 1 = Purchased
		status = po.OrderStatusPaid
	}

	return &CallbackResult{
		Valid:         true,
		Status:        status,
		TransactionId: purchaseInfo.OrderId,
		Amount:        callback.Amount,
		Currency:      callback.Currency,
		Message:       "Verification successful",
		Metadata: map[string]string{
			"purchaseToken":      purchaseToken,
			"productId":          productId,
			"purchaseTime":       fmt.Sprintf("%d", purchaseInfo.PurchaseTime),
			"consumptionState":   fmt.Sprintf("%d", purchaseInfo.ConsumptionState),
			"acknowledgmentState": fmt.Sprintf("%d", purchaseInfo.AcknowledgmentState),
		},
	}, nil
}

// ProcessRefund 处理Google Play退款
func (g *GooglePayService) ProcessRefund(ctx context.Context, refund *po.PaymentRefund) (*RefundResult, error) {
	// Google Play退款通常通过Google Play Console手动处理
	// 这里可以记录退款请求，但实际退款需要手动操作
	
	logger.LogInfof("Google Play refund request: orderId=%s, refundId=%s, amount=%d", 
		refund.OrderId, refund.RefundId, refund.RefundAmount)

	return &RefundResult{
		Success:  false, // Google Play不支持自动退款
		RefundId: refund.RefundId,
		Status:   po.RefundStatusPending,
		Message:  "Google Play refunds must be processed manually through Google Play Console",
		Metadata: map[string]string{
			"note": "Manual refund required",
		},
	}, nil
}

// verifyPurchase 验证Google Play购买
func (g *GooglePayService) verifyPurchase(ctx context.Context, productId, purchaseToken string) (bool, *GooglePlayPurchase, error) {
	// 构建Google Play Developer API请求
	url := fmt.Sprintf("https://androidpublisher.googleapis.com/androidpublisher/v3/applications/%s/purchases/products/%s/tokens/%s",
		g.config.GooglePay.PackageName, productId, purchaseToken)

	// 获取访问令牌
	accessToken, err := g.getAccessToken(ctx)
	if err != nil {
		return false, nil, fmt.Errorf("failed to get access token: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return false, nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+accessToken)
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := g.client.Do(req)
	if err != nil {
		return false, nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return false, nil, fmt.Errorf("API request failed with status: %d", resp.StatusCode)
	}

	// 解析响应
	var purchase GooglePlayPurchase
	if err := json.NewDecoder(resp.Body).Decode(&purchase); err != nil {
		return false, nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return true, &purchase, nil
}

// getAccessToken 获取Google API访问令牌
func (g *GooglePayService) getAccessToken(ctx context.Context) (string, error) {
	// 这里应该使用Google服务账户凭据获取访问令牌
	// 实际实现需要使用google.golang.org/api/oauth2/v2包
	
	// 示例实现（实际应该使用JWT和服务账户密钥）
	// 这里返回一个占位符，实际实现需要完整的OAuth2流程
	
	logger.LogWarnf("Google API access token not implemented, using placeholder")
	return "placeholder_access_token", nil
}

// verifySubscription 验证Google Play订阅
func (g *GooglePayService) verifySubscription(ctx context.Context, subscriptionId, purchaseToken string) (bool, *GooglePlaySubscription, error) {
	// 构建Google Play Developer API请求
	url := fmt.Sprintf("https://androidpublisher.googleapis.com/androidpublisher/v3/applications/%s/purchases/subscriptions/%s/tokens/%s",
		g.config.GooglePay.PackageName, subscriptionId, purchaseToken)

	// 获取访问令牌
	accessToken, err := g.getAccessToken(ctx)
	if err != nil {
		return false, nil, fmt.Errorf("failed to get access token: %w", err)
	}

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		return false, nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Authorization", "Bearer "+accessToken)
	req.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := g.client.Do(req)
	if err != nil {
		return false, nil, fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return false, nil, fmt.Errorf("API request failed with status: %d", resp.StatusCode)
	}

	// 解析响应
	var subscription GooglePlaySubscription
	if err := json.NewDecoder(resp.Body).Decode(&subscription); err != nil {
		return false, nil, fmt.Errorf("failed to decode response: %w", err)
	}

	return true, &subscription, nil
}
