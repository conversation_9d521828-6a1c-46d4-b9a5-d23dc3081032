package svc

import (
	"context"
	"fmt"

	"creativematrix.com/beyondreading/gen/app/api/payment/model/vo"
	pb "creativematrix.com/beyondreading/gen/proto/payment"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/utils"
)

// CreatePaymentOrder 创建支付订单
func (s *PaymentSvc) CreatePaymentOrder(ctx context.Context, userId uint64, param *vo.CreatePaymentOrderReq) (*vo.CreatePaymentOrderResp, error) {
	req := &pb.CreatePaymentOrderReq{
		UserId:        userId,
		PaymentMethod: pb.PaymentMethod(param.PaymentMethod),
		PaymentType:   pb.PaymentType(param.PaymentType),
		Amount:        param.Amount,
		Currency:      param.Currency,
		ProductId:     param.ProductId,
		ProductName:   param.ProductName,
		Description:   param.Description,
		Metadata:      param.Metadata,
		ClientIp:      param.ClientIp,
		UserAgent:     param.UserAgent,
		ReturnUrl:     param.ReturnUrl,
		CancelUrl:     param.CancelUrl,
	}

	resp, err := s.dao.CreatePaymentOrder(ctx, req)
	if err != nil {
		logger.LogErrorf("Failed to create payment order: %v", err)
		return nil, fmt.Errorf("failed to create payment order: %w", err)
	}

	if resp.Code != 200 {
		return nil, fmt.Errorf("create payment order failed: %s", resp.Message)
	}

	return &vo.CreatePaymentOrderResp{
		Order:       s.convertOrderFromPB(resp.Order),
		PaymentUrl:  resp.PaymentUrl,
		PaymentData: resp.PaymentData,
	}, nil
}

// ProcessPaymentCallback 处理支付回调
func (s *PaymentSvc) ProcessPaymentCallback(ctx context.Context, param *vo.PaymentCallbackReq) (*vo.PaymentCallbackResp, error) {
	req := &pb.ProcessPaymentCallbackReq{
		PaymentMethod: pb.PaymentMethod(param.PaymentMethod),
		OrderId:       param.OrderId,
		TransactionId: param.TransactionId,
		Status:        pb.OrderStatus(param.Status),
		Amount:        param.Amount,
		Currency:      param.Currency,
		CallbackData:  param.CallbackData,
		Signature:     param.Signature,
	}

	resp, err := s.dao.ProcessPaymentCallback(ctx, req)
	if err != nil {
		logger.LogErrorf("Failed to process payment callback: %v", err)
		return nil, fmt.Errorf("failed to process payment callback: %w", err)
	}

	return &vo.PaymentCallbackResp{
		Success: resp.Success,
		Message: resp.Message,
	}, nil
}

// GetPaymentOrder 获取支付订单
func (s *PaymentSvc) GetPaymentOrder(ctx context.Context, userId uint64, param *vo.GetPaymentOrderReq) (*vo.GetPaymentOrderResp, error) {
	req := &pb.GetPaymentOrderReq{
		OrderId: param.OrderId,
		UserId:  userId,
	}

	resp, err := s.dao.GetPaymentOrder(ctx, req)
	if err != nil {
		logger.LogErrorf("Failed to get payment order: %v", err)
		return nil, fmt.Errorf("failed to get payment order: %w", err)
	}

	if resp.Code != 200 {
		return nil, fmt.Errorf("get payment order failed: %s", resp.Message)
	}

	return &vo.GetPaymentOrderResp{
		Order: s.convertOrderFromPB(resp.Order),
	}, nil
}

// GetPaymentOrders 获取支付订单列表
func (s *PaymentSvc) GetPaymentOrders(ctx context.Context, userId uint64, param *vo.GetPaymentOrdersReq) (*vo.GetPaymentOrdersResp, error) {
	req := &pb.GetPaymentOrdersReq{
		UserId:        userId,
		PaymentMethod: pb.PaymentMethod(param.PaymentMethod),
		PaymentType:   pb.PaymentType(param.PaymentType),
		Status:        pb.OrderStatus(param.Status),
		Page:          param.Page,
		PageSize:      param.PageSize,
		StartTime:     param.StartTime,
		EndTime:       param.EndTime,
	}

	resp, err := s.dao.GetPaymentOrders(ctx, req)
	if err != nil {
		logger.LogErrorf("Failed to get payment orders: %v", err)
		return nil, fmt.Errorf("failed to get payment orders: %w", err)
	}

	if resp.Code != 200 {
		return nil, fmt.Errorf("get payment orders failed: %s", resp.Message)
	}

	var orders []*vo.PaymentOrderInfo
	for _, order := range resp.Orders {
		orders = append(orders, s.convertOrderFromPB(order))
	}

	return &vo.GetPaymentOrdersResp{
		Orders: orders,
		Total:  resp.Total,
	}, nil
}

// CancelPaymentOrder 取消支付订单
func (s *PaymentSvc) CancelPaymentOrder(ctx context.Context, userId uint64, param *vo.CancelPaymentOrderReq) (*vo.CancelPaymentOrderResp, error) {
	req := &pb.CancelPaymentOrderReq{
		OrderId: param.OrderId,
		UserId:  userId,
		Reason:  param.Reason,
	}

	resp, err := s.dao.CancelPaymentOrder(ctx, req)
	if err != nil {
		logger.LogErrorf("Failed to cancel payment order: %v", err)
		return nil, fmt.Errorf("failed to cancel payment order: %w", err)
	}

	return &vo.CancelPaymentOrderResp{
		Success: resp.Code == 200,
		Message: resp.Message,
	}, nil
}

// RefundPayment 退款
func (s *PaymentSvc) RefundPayment(ctx context.Context, userId uint64, param *vo.RefundPaymentReq) (*vo.RefundPaymentResp, error) {
	refundId := param.RefundId
	if refundId == "" {
		refundId = utils.GenerateOrderId("REF")
	}

	req := &pb.RefundPaymentReq{
		OrderId:      param.OrderId,
		UserId:       userId,
		RefundAmount: param.RefundAmount,
		Reason:       param.Reason,
		RefundId:     refundId,
	}

	resp, err := s.dao.RefundPayment(ctx, req)
	if err != nil {
		logger.LogErrorf("Failed to refund payment: %v", err)
		return nil, fmt.Errorf("failed to refund payment: %w", err)
	}

	return &vo.RefundPaymentResp{
		RefundId: resp.RefundId,
		Success:  resp.Success,
		Message:  resp.Message,
	}, nil
}

// GetPaymentMethods 获取支付方式列表
func (s *PaymentSvc) GetPaymentMethods(ctx context.Context, param *vo.GetPaymentMethodsReq) (*vo.GetPaymentMethodsResp, error) {
	req := &pb.GetPaymentMethodsReq{
		Platform: param.Platform,
		Region:   param.Region,
	}

	resp, err := s.dao.GetPaymentMethods(ctx, req)
	if err != nil {
		logger.LogErrorf("Failed to get payment methods: %v", err)
		return nil, fmt.Errorf("failed to get payment methods: %w", err)
	}

	if resp.Code != 200 {
		return nil, fmt.Errorf("get payment methods failed: %s", resp.Message)
	}

	var methods []*vo.PaymentMethodInfo
	for _, method := range resp.Methods {
		methods = append(methods, &vo.PaymentMethodInfo{
			Method:              int32(method.Method),
			Name:                method.Name,
			DisplayName:         method.DisplayName,
			Icon:                method.Icon,
			Enabled:             method.Enabled,
			SupportedCurrencies: method.SupportedCurrencies,
			Config:              method.Config,
		})
	}

	return &vo.GetPaymentMethodsResp{
		Methods: methods,
	}, nil
}
