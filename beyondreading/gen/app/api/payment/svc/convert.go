package svc

import (
	"time"

	"creativematrix.com/beyondreading/gen/app/api/payment/model/vo"
	pb "creativematrix.com/beyondreading/gen/proto/payment"
)

// convertOrderFromPB 从protobuf转换支付订单
func (s *PaymentSvc) convertOrderFromPB(order *pb.PaymentOrderInfo) *vo.PaymentOrderInfo {
	if order == nil {
		return nil
	}

	voOrder := &vo.PaymentOrderInfo{
		OrderId:       order.OrderId,
		UserId:        order.UserId,
		PaymentMethod: int32(order.PaymentMethod),
		PaymentType:   int32(order.PaymentType),
		Amount:        order.Amount,
		Currency:      order.Currency,
		ProductId:     order.ProductId,
		ProductName:   order.ProductName,
		Description:   order.Description,
		Status:        int32(order.Status),
		TransactionId: order.TransactionId,
		Metadata:      order.Metadata,
		CreatedAt:     time.Unix(order.CreatedAt, 0),
		UpdatedAt:     time.Unix(order.UpdatedAt, 0),
		ExpiredAt:     time.Unix(order.ExpiredAt, 0),
		FailureReason: order.FailureReason,
	}

	if order.PaidAt > 0 {
		paidAt := time.Unix(order.PaidAt, 0)
		voOrder.PaidAt = &paidAt
	}

	return voOrder
}
