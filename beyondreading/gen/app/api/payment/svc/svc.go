package svc

import (
	"context"

	"creativematrix.com/beyondreading/gen/app/api/payment/conf"
	"creativematrix.com/beyondreading/gen/app/api/payment/dao"
)

type PaymentSvc struct {
	conf *conf.Config
	dao  *dao.Dao
}

func Load(c *conf.Config) *PaymentSvc {
	svc := &PaymentSvc{
		conf: c,
		dao:  dao.Load(c),
	}

	return svc
}

func (s *PaymentSvc) Ping(ctx context.Context) error {
	return s.dao.Ping(ctx)
}

func (s *PaymentSvc) Close() {
	s.dao.Close()
}
