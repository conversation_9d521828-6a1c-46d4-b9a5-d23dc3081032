package http

import (
	"errors"
	"strconv"

	"creativematrix.com/beyondreading/gen/app/api/payment/model/vo"
	"creativematrix.com/beyondreading/pkg/ecode"
	"creativematrix.com/beyondreading/pkg/logger"
	"github.com/gin-gonic/gin"
)

// createPaymentOrder 创建支付订单
func createPaymentOrder(c *gin.Context) {
	var param vo.CreatePaymentOrderReq
	if err := c.ShouldBindJSON(&param); err != nil {
		ecode.Back(c).Failure(errors.New("invalid parameter"))
		return
	}

	// 获取用户ID
	userId, exists := c.Get("userId")
	if !exists {
		ecode.Back(c).Failure(errors.New("user not authenticated"))
		return
	}

	// 自动获取客户端信息
	param.ClientIp = c.ClientIP()
	param.UserAgent = c.GetHeader("User-Agent")

	result, err := service.CreatePaymentOrder(c.Request.Context(), userId.(uint64), &param)
	if err != nil {
		logger.LogErrorf("Failed to create payment order: %v", err)
		ecode.Back(c).Failure(errors.New("failed to create payment order"))
		return
	}

	ecode.Back(c).SetData(result).Success()
}

// processPaymentCallback 处理支付回调
func processPaymentCallback(c *gin.Context) {
	var param vo.PaymentCallbackReq
	if err := c.ShouldBindJSON(&param); err != nil {
		ecode.Back(c).Failure(errors.New("invalid parameter"))
		return
	}

	result, err := service.ProcessPaymentCallback(c.Request.Context(), &param)
	if err != nil {
		logger.LogErrorf("Failed to process payment callback: %v", err)
		ecode.Back(c).Failure(errors.New("failed to process payment callback"))
		return
	}

	ecode.Back(c).SetData(result).Success()
}

// getPaymentOrder 获取支付订单
func getPaymentOrder(c *gin.Context) {
	orderId := c.Param("orderId")
	if orderId == "" {
		ecode.Back(c).Failure(errors.New("order id is required"))
		return
	}

	// 获取用户ID
	userId, exists := c.Get("userId")
	if !exists {
		ecode.Back(c).Failure(errors.New("user not authenticated"))
		return
	}

	param := &vo.GetPaymentOrderReq{
		OrderId: orderId,
	}

	result, err := service.GetPaymentOrder(c.Request.Context(), userId.(uint64), param)
	if err != nil {
		logger.LogErrorf("Failed to get payment order: %v", err)
		ecode.Back(c).Failure(errors.New("failed to get payment order"))
		return
	}

	ecode.Back(c).SetData(result).Success()
}

// getPaymentOrders 获取支付订单列表
func getPaymentOrders(c *gin.Context) {
	// 获取用户ID
	userId, exists := c.Get("userId")
	if !exists {
		ecode.Back(c).Failure(errors.New("user not authenticated"))
		return
	}

	var param vo.GetPaymentOrdersReq

	// 解析查询参数
	if paymentMethod := c.Query("paymentMethod"); paymentMethod != "" {
		if pm, err := strconv.Atoi(paymentMethod); err == nil {
			param.PaymentMethod = int32(pm)
		}
	}

	if paymentType := c.Query("paymentType"); paymentType != "" {
		if pt, err := strconv.Atoi(paymentType); err == nil {
			param.PaymentType = int32(pt)
		}
	}

	if status := c.Query("status"); status != "" {
		if s, err := strconv.Atoi(status); err == nil {
			param.Status = int32(s)
		}
	}

	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil {
			param.Page = int32(p)
		}
	}

	if pageSize := c.Query("pageSize"); pageSize != "" {
		if ps, err := strconv.Atoi(pageSize); err == nil {
			param.PageSize = int32(ps)
		}
	}

	if startTime := c.Query("startTime"); startTime != "" {
		if st, err := strconv.ParseInt(startTime, 10, 64); err == nil {
			param.StartTime = st
		}
	}

	if endTime := c.Query("endTime"); endTime != "" {
		if et, err := strconv.ParseInt(endTime, 10, 64); err == nil {
			param.EndTime = et
		}
	}

	result, err := service.GetPaymentOrders(c.Request.Context(), userId.(uint64), &param)
	if err != nil {
		logger.LogErrorf("Failed to get payment orders: %v", err)
		ecode.Back(c).Failure(errors.New("failed to get payment orders"))
		return
	}

	ecode.Back(c).SetData(result).Success()
}

// cancelPaymentOrder 取消支付订单
func cancelPaymentOrder(c *gin.Context) {
	orderId := c.Param("orderId")
	if orderId == "" {
		ecode.Back(c).Failure(errors.New("order id is required"))
		return
	}

	// 获取用户ID
	userId, exists := c.Get("userId")
	if !exists {
		ecode.Back(c).Failure(errors.New("user not authenticated"))
		return
	}

	var param vo.CancelPaymentOrderReq
	if err := c.ShouldBindJSON(&param); err != nil {
		// 如果没有body，只设置orderId
		param.OrderId = orderId
	} else {
		param.OrderId = orderId
	}

	result, err := service.CancelPaymentOrder(c.Request.Context(), userId.(uint64), &param)
	if err != nil {
		logger.LogErrorf("Failed to cancel payment order: %v", err)
		ecode.Back(c).Failure(errors.New("failed to cancel payment order"))
		return
	}

	ecode.Back(c).SetData(result).Success()
}

// refundPayment 退款
func refundPayment(c *gin.Context) {
	var param vo.RefundPaymentReq
	if err := c.ShouldBindJSON(&param); err != nil {
		ecode.Back(c).Failure(errors.New("invalid parameter"))
		return
	}

	// 获取用户ID
	userId, exists := c.Get("userId")
	if !exists {
		ecode.Back(c).Failure(errors.New("user not authenticated"))
		return
	}

	result, err := service.RefundPayment(c.Request.Context(), userId.(uint64), &param)
	if err != nil {
		logger.LogErrorf("Failed to refund payment: %v", err)
		ecode.Back(c).Failure(errors.New("failed to refund payment"))
		return
	}

	ecode.Back(c).SetData(result).Success()
}

// getPaymentMethods 获取支付方式列表
func getPaymentMethods(c *gin.Context) {
	param := &vo.GetPaymentMethodsReq{
		Platform: c.Query("platform"),
		Region:   c.Query("region"),
	}

	result, err := service.GetPaymentMethods(c.Request.Context(), param)
	if err != nil {
		logger.LogErrorf("Failed to get payment methods: %v", err)
		ecode.Back(c).Failure(errors.New("failed to get payment methods"))
		return
	}

	ecode.Back(c).SetData(result).Success()
}

// processGooglePayCallback 处理Google Pay回调
func processGooglePayCallback(c *gin.Context) {
	var callbackData map[string]string
	if err := c.ShouldBindJSON(&callbackData); err != nil {
		ecode.Back(c).Failure(errors.New("invalid callback data"))
		return
	}

	param := &vo.PaymentCallbackReq{
		PaymentMethod: 1, // Google Pay
		CallbackData:  callbackData,
	}

	// 从回调数据中提取必要信息
	if orderId, exists := callbackData["orderId"]; exists {
		param.OrderId = orderId
	}
	if transactionId, exists := callbackData["transactionId"]; exists {
		param.TransactionId = transactionId
	}

	result, err := service.ProcessPaymentCallback(c.Request.Context(), param)
	if err != nil {
		logger.LogErrorf("Failed to process Google Pay callback: %v", err)
		ecode.Back(c).Failure(errors.New("failed to process callback"))
		return
	}

	ecode.Back(c).SetData(result).Success()
}

// processApplePayCallback 处理Apple Pay回调
func processApplePayCallback(c *gin.Context) {
	var callbackData map[string]string
	if err := c.ShouldBindJSON(&callbackData); err != nil {
		ecode.Back(c).Failure(errors.New("invalid callback data"))
		return
	}

	param := &vo.PaymentCallbackReq{
		PaymentMethod: 2, // Apple Pay
		CallbackData:  callbackData,
	}

	// 从回调数据中提取必要信息
	if orderId, exists := callbackData["orderId"]; exists {
		param.OrderId = orderId
	}
	if transactionId, exists := callbackData["transactionId"]; exists {
		param.TransactionId = transactionId
	}

	result, err := service.ProcessPaymentCallback(c.Request.Context(), param)
	if err != nil {
		logger.LogErrorf("Failed to process Apple Pay callback: %v", err)
		ecode.Back(c).Failure(errors.New("failed to process callback"))
		return
	}

	ecode.Back(c).SetData(result).Success()
}

// processPayPalCallback 处理PayPal回调
func processPayPalCallback(c *gin.Context) {
	var callbackData map[string]string
	if err := c.ShouldBindJSON(&callbackData); err != nil {
		ecode.Back(c).Failure(errors.New("invalid callback data"))
		return
	}

	param := &vo.PaymentCallbackReq{
		PaymentMethod: 3, // PayPal
		CallbackData:  callbackData,
	}

	// 从回调数据中提取必要信息
	if orderId, exists := callbackData["orderId"]; exists {
		param.OrderId = orderId
	}
	if transactionId, exists := callbackData["transactionId"]; exists {
		param.TransactionId = transactionId
	}

	result, err := service.ProcessPaymentCallback(c.Request.Context(), param)
	if err != nil {
		logger.LogErrorf("Failed to process PayPal callback: %v", err)
		ecode.Back(c).Failure(errors.New("failed to process callback"))
		return
	}

	ecode.Back(c).SetData(result).Success()
}

// processAlipayCallback 处理支付宝回调
func processAlipayCallback(c *gin.Context) {
	// 支付宝回调通常是表单数据
	callbackData := make(map[string]string)
	for key, values := range c.Request.Form {
		if len(values) > 0 {
			callbackData[key] = values[0]
		}
	}

	param := &vo.PaymentCallbackReq{
		PaymentMethod: 4, // Alipay
		CallbackData:  callbackData,
	}

	// 从回调数据中提取必要信息
	if orderId, exists := callbackData["out_trade_no"]; exists {
		param.OrderId = orderId
	}
	if transactionId, exists := callbackData["trade_no"]; exists {
		param.TransactionId = transactionId
	}

	result, err := service.ProcessPaymentCallback(c.Request.Context(), param)
	if err != nil {
		logger.LogErrorf("Failed to process Alipay callback: %v", err)
		c.String(200, "FAIL")
		return
	}

	if result.Success {
		c.String(200, "SUCCESS")
	} else {
		c.String(400, "FAIL")
	}
}

// processWechatPayCallback 处理微信支付回调
func processWechatPayCallback(c *gin.Context) {
	// 微信支付回调是XML格式
	body, err := c.Request.GetBody()
	if err != nil {
		logger.LogErrorf("Failed to read WeChat Pay callback body: %v", err)
		c.XML(400, gin.H{"return_code": "FAIL", "return_msg": "读取数据失败"})
		return
	}

	// 解析XML数据为map
	callbackData := make(map[string]string)
	// 这里需要实现XML解析逻辑，简化处理
	callbackData["xml_data"] = "wechat_callback_data"

	param := &vo.PaymentCallbackReq{
		PaymentMethod: 5, // WeChat Pay
		CallbackData:  callbackData,
	}

	result, err := service.ProcessPaymentCallback(c.Request.Context(), param)
	if err != nil {
		logger.LogErrorf("Failed to process WeChat Pay callback: %v", err)
		c.XML(500, gin.H{"return_code": "FAIL", "return_msg": "处理失败"})
		return
	}

	if result.Success {
		c.XML(200, gin.H{"return_code": "SUCCESS", "return_msg": "OK"})
	} else {
		c.XML(400, gin.H{"return_code": "FAIL", "return_msg": "验证失败"})
	}
}
