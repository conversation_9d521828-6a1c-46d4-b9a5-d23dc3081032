package dao

import (
	"context"

	"creativematrix.com/beyondreading/gen/app/api/payment/conf"
	"creativematrix.com/beyondreading/gen/app/base/payment/api"
	pb "creativematrix.com/beyondreading/gen/proto/payment"
)

type Dao struct {
	conf          *conf.Config
	paymentClient pb.PaymentClient
}

func Load(c *conf.Config) *Dao {
	paymentClient, err := api.NewClient(c.Base)
	if err != nil {
		panic(err)
	}

	return &Dao{
		conf:          c,
		paymentClient: paymentClient,
	}
}

func (d *Dao) Ping(ctx context.Context) error {
	return nil
}

func (d *Dao) Close() {
	// 关闭连接
}

// Payment相关方法
func (d *Dao) CreatePaymentOrder(ctx context.Context, req *pb.CreatePaymentOrderReq) (*pb.CreatePaymentOrderResp, error) {
	return d.paymentClient.CreatePaymentOrder(ctx, req)
}

func (d *Dao) ProcessPaymentCallback(ctx context.Context, req *pb.ProcessPaymentCallbackReq) (*pb.ProcessPaymentCallbackResp, error) {
	return d.paymentClient.ProcessPaymentCallback(ctx, req)
}

func (d *Dao) GetPaymentOrder(ctx context.Context, req *pb.GetPaymentOrderReq) (*pb.GetPaymentOrderResp, error) {
	return d.paymentClient.GetPaymentOrder(ctx, req)
}

func (d *Dao) GetPaymentOrders(ctx context.Context, req *pb.GetPaymentOrdersReq) (*pb.GetPaymentOrdersResp, error) {
	return d.paymentClient.GetPaymentOrders(ctx, req)
}

func (d *Dao) CancelPaymentOrder(ctx context.Context, req *pb.CancelPaymentOrderReq) (*pb.CancelPaymentOrderResp, error) {
	return d.paymentClient.CancelPaymentOrder(ctx, req)
}

func (d *Dao) RefundPayment(ctx context.Context, req *pb.RefundPaymentReq) (*pb.RefundPaymentResp, error) {
	return d.paymentClient.RefundPayment(ctx, req)
}

func (d *Dao) GetPaymentMethods(ctx context.Context, req *pb.GetPaymentMethodsReq) (*pb.GetPaymentMethodsResp, error) {
	return d.paymentClient.GetPaymentMethods(ctx, req)
}
