# Account & Purchase 微服务重新设计总结

## 概述

根据新的业务需求，重新设计了Account和Purchase微服务，主要变化包括：

1. **VIP/包月购买模式变更**: VIP和包月是针对用户账户的，用户购买后可以在指定时间内阅读符合权限的书籍
2. **批量章节购买**: PurchaseChapter支持同时购买多个章节
3. **新增vipmonthly_order表**: 专门记录VIP和包月购买订单

## 主要变更

### 1. 业务模式变更

#### **修改前**
- 包月是针对特定书籍的
- VIP是针对特定书籍的
- 每本书需要单独购买包月/VIP

#### **修改后**
- 包月是针对用户账户的，购买后可以阅读所有支持包月的书籍
- VIP是针对用户账户的，购买后可以阅读所有支持VIP的书籍
- 用户状态包含VIP和包月过期时间

### 2. 数据库设计变更

#### **新增表结构**
```sql
-- VIP/包月订单表
CREATE TABLE `vipmonthly_order` (
  `order_id` varchar(64) NOT NULL COMMENT '订单ID',
  `account_id` bigint unsigned NOT NULL COMMENT '账户ID',
  `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
  `order_type` varchar(32) NOT NULL COMMENT '订单类型：monthly-包月，vip-VIP',
  `coin_amount` decimal(15,2) NOT NULL COMMENT '消费书币数量',
  `duration_days` int NOT NULL COMMENT '有效期天数',
  `start_time` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '订单状态：1-待支付，2-支付成功，3-支付失败',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`order_id`)
);
```

#### **Account表增强**
```sql
-- 账户表新增字段
ALTER TABLE `account` ADD COLUMN `vip_expire_time` timestamp NULL DEFAULT NULL COMMENT 'VIP过期时间';
ALTER TABLE `account` ADD COLUMN `monthly_expire_time` timestamp NULL DEFAULT NULL COMMENT '包月过期时间';
```

### 3. API接口变更

#### **Purchase服务新增接口**
```protobuf
service Purchase {
  // 购买章节（支持批量购买）
  rpc PurchaseChapter(PurchaseChapterReq) returns (PurchaseChapterResp);
  
  // 购买包月（针对用户账户）
  rpc PurchaseMonthly(PurchaseMonthlyReq) returns (PurchaseMonthlyResp);
  
  // 购买VIP（针对用户账户）
  rpc PurchaseVip(PurchaseVipReq) returns (PurchaseVipResp);
  
  // 获取VIP/包月订单列表
  rpc GetVipMonthlyOrders(GetVipMonthlyOrdersReq) returns (GetVipMonthlyOrdersResp);
  
  // 检查VIP状态
  rpc CheckVipStatus(CheckVipStatusReq) returns (CheckVipStatusResp);
  
  // 检查包月状态
  rpc CheckMonthlyStatus(CheckMonthlyStatusReq) returns (CheckMonthlyStatusResp);
}
```

#### **批量章节购买接口**
```protobuf
message ChapterPurchaseItem {
  uint32 chapter_order = 1;
  double coin_amount = 2;
}

message PurchaseChapterReq {
  uint64 user_id = 1;
  string book_id = 2;
  repeated ChapterPurchaseItem chapters = 3;  // 支持批量购买
}

message PurchaseChapterResp {
  int32 code = 1;
  string message = 2;
  repeated PurchaseOrder orders = 3;          // 返回多个订单
}
```

### 4. DAO层实现

#### **批量章节购买实现**
```go
func (d *Dao) PurchaseChapter(ctx context.Context, userId uint64, bookId string, chapterOrders []uint32, coinAmounts []float64) ([]*po.PurchaseOrder, error) {
    // 支持批量购买多个章节
    // 在事务中创建多个订单
    // 返回所有创建的订单
}
```

#### **VIP/包月购买实现**
```go
func (d *Dao) PurchaseVipMonthly(ctx context.Context, userId uint64, orderType string, coinAmount float64, durationDays int32) (*po.VipMonthlyOrder, error) {
    // 创建VIP或包月订单
    // 设置开始时间和结束时间
    // 插入vipmonthly_order表
}
```

### 5. 服务层业务逻辑

#### **购买流程**
1. **章节购买**:
   - 检查用户余额
   - 批量创建章节订单
   - 逐个扣除书币
   - 更新订单状态

2. **VIP/包月购买**:
   - 检查用户余额
   - 创建VIP/包月订单
   - 扣除书币
   - 更新用户状态（设置过期时间）
   - 更新订单状态

#### **权限检查逻辑**
```go
func (d *Dao) CheckChapterPurchased(ctx context.Context, userId uint64, bookId string, chapterOrder uint32) (bool, *time.Time, bool, bool, string, error) {
    // 1. 检查直接购买的章节
    // 2. 检查VIP状态（如果有效，可以阅读所有章节）
    // 3. 检查包月状态（如果有效，可以阅读包月范围的章节）
    // 返回: 是否已购买, 购买时间, 是否通过包月, 是否通过VIP, 订单ID
}
```

## 新的业务流程

### 1. 用户购买VIP
```
用户请求购买VIP → 检查余额 → 创建VIP订单 → 扣除书币 → 更新用户VIP状态 → 返回订单信息
```

### 2. 用户购买包月
```
用户请求购买包月 → 检查余额 → 创建包月订单 → 扣除书币 → 更新用户包月状态 → 返回订单信息
```

### 3. 用户阅读章节权限检查
```
用户请求阅读章节 → 检查是否直接购买 → 检查VIP状态 → 检查包月状态 → 返回权限结果
```

### 4. 批量购买章节
```
用户选择多个章节 → 计算总金额 → 检查余额 → 批量创建订单 → 逐个扣除书币 → 返回所有订单
```

## 文件结构

```
beyondreading/gen/
├── proto/
│   ├── account/
│   │   └── account.proto          # Account服务Proto定义
│   └── purchase/
│       └── purchase.proto         # Purchase服务Proto定义
├── database/
│   └── account_schema.sql         # 数据库表结构
├── common/po/
│   └── account.go                 # PO模型定义
├── app/base/account/
│   ├── api/api.go                 # Account API客户端
│   ├── dao/dao.go                 # Account DAO层
│   └── svc/svc.go                 # Account服务层
├── app/base/purchase/
│   ├── api/api.go                 # Purchase API客户端
│   ├── dao/dao.go                 # Purchase DAO层
│   └── svc/svc.go                 # Purchase服务层
└── docs/
    └── redesign_summary.md        # 重设计总结文档
```

## 关键特性

### 1. 批量章节购买
- 支持一次购买多个章节
- 事务保证数据一致性
- 返回所有创建的订单

### 2. 账户级VIP/包月
- VIP用户可以阅读所有支持VIP的内容
- 包月用户可以阅读所有支持包月的内容
- 用户状态包含过期时间

### 3. 灵活的权限检查
- 优先检查直接购买
- 其次检查VIP权限
- 最后检查包月权限

### 4. 完整的订单管理
- 章节订单存储在purchase_order表
- VIP/包月订单存储在vipmonthly_order表
- 支持订单状态跟踪

## 使用示例

### 批量购买章节
```go
// DAO层调用
orders, err := dao.PurchaseChapter(ctx, 123, "book_456", []uint32{1, 2, 3}, []float64{5.0, 5.0, 5.0})
```

### 购买VIP
```go
// 服务层调用
resp, err := svc.PurchaseVip(ctx, &pb.PurchaseVipReq{
    UserId: 123,
    CoinAmount: 100.0,
    DurationDays: 30,
})
```

### 检查章节权限
```go
// 检查用户是否可以阅读某章节
isPurchased, purchasedAt, isMonthly, isVip, orderId, err := dao.CheckChapterPurchased(ctx, 123, "book_456", 1)
```

这个重新设计的架构更好地支持了新的业务需求，提供了更灵活的购买和权限管理机制。
