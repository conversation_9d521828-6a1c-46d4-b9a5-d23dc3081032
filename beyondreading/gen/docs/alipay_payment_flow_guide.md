# 支付宝充值支付完整流程指南

## 流程概述

支付宝充值支付流程包含10个主要步骤，涉及客户端、Payment API、Payment Base、支付宝网关、Account服务等多个组件的协作。

## 详细API调用流程

### 1. 获取支付产品列表

#### **客户端调用**
```http
GET /api/payment/methods?platform=android&region=CN
Authorization: Bearer {access_token}
```

#### **响应示例**
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "methods": [
      {
        "method": 4,
        "name": "Alipay",
        "displayName": "支付宝",
        "enabled": true,
        "supportedCurrencies": ["CNY", "USD"]
      }
    ],
    "products": [
      {
        "productId": "coins_100",
        "productName": "100书币",
        "productType": 3,
        "price": 99,
        "currency": "CNY",
        "coinAmount": 100
      },
      {
        "productId": "coins_500", 
        "productName": "500书币",
        "productType": 3,
        "price": 499,
        "currency": "CNY",
        "coinAmount": 500
      }
    ]
  }
}
```

### 2. 创建支付订单

#### **客户端调用**
```http
POST /api/payment/orders
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "paymentMethod": 4,
  "paymentType": 3,
  "productId": "coins_500",
  "productName": "500书币",
  "amount": 499,
  "currency": "CNY",
  "description": "充值500书币",
  "returnUrl": "beyondreading://payment/success",
  "cancelUrl": "beyondreading://payment/cancel"
}
```

#### **gRPC调用示例**
```go
req := &pb.CreatePaymentOrderReq{
    UserId:        12345,                           // 从JWT token获取
    PaymentMethod: pb.PaymentMethod_ALIPAY,         // 支付宝
    PaymentType:   pb.PaymentType_COIN_RECHARGE,    // 书币充值
    Amount:        499,                             // 4.99元 = 499分
    Currency:      "CNY",                           // 人民币
    ProductId:     "coins_500",                     // 产品ID
    ProductName:   "500书币",                       // 产品名称
    Description:   "充值500书币用于阅读",             // 订单描述
    Metadata: map[string]string{                    // 额外信息
        "platform":     "android",
        "coin_amount":  "500",
        "app_version":  "1.0.0",
    },
    ClientIp:   "*************",                   // 自动获取
    UserAgent:  "BeyondReading/1.0.0 (Android 12)", // 自动获取
    ReturnUrl:  "beyondreading://payment/success",  // 支付成功回调
    CancelUrl:  "beyondreading://payment/cancel",   // 支付取消回调
}

got, err := paymentSvc.CreatePaymentOrder(context.Background(), req)
if err != nil {
    logger.LogErrorf("Failed to create payment order: %v", err)
    return
}
```

#### **响应示例**
```json
{
  "code": 200,
  "message": "Payment order created successfully",
  "data": {
    "order": {
      "orderId": "PAY12345coins_500**********789",
      "userId": 12345,
      "paymentMethod": 4,
      "paymentType": 3,
      "amount": 499,
      "currency": "CNY",
      "productId": "coins_500",
      "status": 1,
      "createdAt": **********
    },
    "paymentUrl": "https://openapi.alipay.com/gateway.do?app_id=xxx&method=alipay.trade.create&charset=utf-8&sign_type=RSA2&timestamp=2023-12-21%2010:30:56&version=1.0&biz_content=%7B%22out_trade_no%22%3A%22PAY12345coins_500**********789%22%2C%22total_amount%22%3A%224.99%22%2C%22subject%22%3A%22500%E4%B9%A6%E5%B8%81%22%7D&sign=xxx",
    "paymentData": "PAY12345coins_500**********789"
  }
}
```

### 3. 客户端处理支付

#### **Android客户端示例**
```kotlin
// 获取支付URL后，打开支付宝支付
val paymentUrl = response.data.paymentUrl
val intent = Intent(Intent.ACTION_VIEW, Uri.parse(paymentUrl))
startActivity(intent)

// 或者使用支付宝SDK
val alipay = Alipay(this)
val payResult = alipay.payV2(paymentUrl, true)
```

#### **iOS客户端示例**
```swift
// 使用支付宝SDK
let alipay = AlipaySDK.defaultService()
alipay?.payOrder(paymentUrl, fromScheme: "beyondreading") { result in
    // 处理支付结果
}
```

#### **Web客户端示例**
```javascript
// 直接跳转到支付宝支付页面
window.location.href = paymentUrl;

// 或者在新窗口打开
window.open(paymentUrl, '_blank');
```

### 4. 支付宝回调处理

#### **支付宝异步通知**
```http
POST /api/payment/callback/alipay
Content-Type: application/x-www-form-urlencoded

out_trade_no=PAY12345coins_500**********789&
trade_no=2023122122001234567890123456&
trade_status=TRADE_SUCCESS&
total_amount=4.99&
receipt_amount=4.99&
buyer_pay_amount=4.99&
gmt_payment=2023-12-21 10:35:20&
sign=xxx
```

#### **回调验证流程**
```go
// 1. 解析回调数据
callbackData := make(map[string]string)
for key, values := range c.Request.Form {
    if len(values) > 0 {
        callbackData[key] = values[0]
    }
}

// 2. 创建回调记录
callback := &po.PaymentCallback{
    OrderId:       callbackData["out_trade_no"],
    PaymentMethod: 4, // Alipay
    TransactionId: callbackData["trade_no"],
    Status:        getStatusFromTradeStatus(callbackData["trade_status"]),
    CallbackData:  callbackData,
}

// 3. 验证签名
isValid := alipayService.VerifyCallback(ctx, callback)
if !isValid {
    c.String(400, "FAIL")
    return
}

// 4. 更新订单状态
if callbackData["trade_status"] == "TRADE_SUCCESS" {
    dao.UpdatePaymentOrderStatus(ctx, callback.OrderId, po.OrderStatusPaid, callback.TransactionId, "")
}

// 5. 发布消息到RabbitMQ
message := map[string]interface{}{
    "type":      "coin_recharge",
    "userId":    order.UserId,
    "orderId":   order.OrderId,
    "productId": order.ProductId,
}
dao.PublishMessage(ctx, "payment.coin.recharged", message)

// 6. 返回成功响应
c.String(200, "SUCCESS")
```

### 5. Account服务处理充值

#### **RabbitMQ消息消费**
```go
// Account消费者处理书币充值消息
func (c *PaymentConsumer) HandleCoinRecharge(body []byte) error {
    var msg PaymentMessage
    if err := json.Unmarshal(body, &msg); err != nil {
        return err
    }

    // 根据产品ID计算书币数量
    coinAmount := c.getCoinAmount(msg.ProductId) // coins_500 -> 500.0

    // 更新用户账户余额
    account, err := c.dao.UpdateAccountBalance(ctx, msg.UserId, coinAmount, "coin_recharge")
    if err != nil {
        return mq.RetryError // 重试
    }

    // 创建账户日志
    accountLog := &po.AccountLog{
        AccountId:       account.AccountId,
        UserId:          msg.UserId,
        TransactionType: "coin_recharge",
        Amount:          coinAmount,
        BalanceBefore:   account.CoinBalance - coinAmount,
        BalanceAfter:    account.CoinBalance,
        OrderId:         msg.OrderId,
        Description:     fmt.Sprintf("书币充值: %s", msg.ProductId),
        CreatedAt:       time.Now(),
    }

    return c.dao.CreateAccountLog(ctx, accountLog)
}
```

### 6. 客户端查询订单状态

#### **查询订单API**
```http
GET /api/payment/orders/PAY12345coins_500**********789
Authorization: Bearer {access_token}
```

#### **响应示例**
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "order": {
      "orderId": "PAY12345coins_500**********789",
      "userId": 12345,
      "paymentMethod": 4,
      "paymentType": 3,
      "amount": 499,
      "currency": "CNY",
      "productId": "coins_500",
      "status": 2,
      "transactionId": "2023122122001234567890123456",
      "paidAt": **********,
      "createdAt": **********
    }
  }
}
```

### 7. 客户端查询用户余额

#### **查询余额API**
```http
GET /api/account/balance
Authorization: Bearer {access_token}
```

#### **响应示例**
```json
{
  "code": 200,
  "message": "Success", 
  "data": {
    "accountId": "ACC12345",
    "userId": 12345,
    "coinBalance": 1500.0,
    "totalConsumed": 2000.0,
    "userLevel": 3
  }
}
```

## 错误处理

### 1. **支付创建失败**
```json
{
  "code": 500,
  "message": "Failed to create payment order",
  "data": null
}
```

### 2. **签名验证失败**
```http
HTTP/1.1 400 Bad Request
Content-Type: text/plain

FAIL
```

### 3. **订单不存在**
```json
{
  "code": 404,
  "message": "Payment order not found",
  "data": null
}
```

## 状态码说明

### **订单状态**
- `1` - PENDING (待支付)
- `2` - PAID (已支付)
- `3` - FAILED (支付失败)
- `4` - CANCELLED (已取消)
- `5` - REFUNDED (已退款)

### **支付方式**
- `4` - ALIPAY (支付宝)

### **支付类型**
- `3` - COIN_RECHARGE (书币充值)

## 安全注意事项

### 1. **签名验证**
- 必须验证支付宝回调的RSA2签名
- 使用支付宝提供的公钥进行验证

### 2. **幂等性处理**
- 同一订单的多次回调只处理一次
- 使用订单ID和交易ID确保唯一性

### 3. **金额校验**
- 回调中的金额必须与订单金额一致
- 防止金额篡改攻击

### 4. **超时处理**
- 设置合理的订单过期时间
- 定期清理过期订单

这个完整的流程确保了支付宝充值的安全性、可靠性和用户体验！
