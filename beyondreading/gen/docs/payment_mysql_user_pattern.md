# Payment MySQL操作按User模块模式重构总结

## 重构目标

参照 `app/base/user/dao/user.go` 中操作MySQL的方式，重写payment_methods和payment_products相关的代码，使用相同的模式和最佳实践。

## User模块MySQL操作模式分析

### 1. **核心模式特点**

#### **使用msshard.Mysqler**
```go
// user/dao/dao.go
type Dao struct {
    conf    *conf.Config
    cache   redis.Redis
    msshard msshard.Mysqler  // 使用msshard包
}

func Load(c *conf.Config) *Dao {
    return &Dao{
        msshard: msshard.Load(c.MySQL),  // 加载msshard
    }
}
```

#### **数据库连接获取**
```go
// user/dao/user.go
db, err := d.msshard.DB(strconv.Itoa(int(userId % common.ShardNum)))
if err != nil {
    return nil, fmt.Errorf("failed to get database connection: %w", err)
}
```

#### **使用pkg/json包**
```go
import js "creativematrix.com/beyondreading/pkg/json"

// 序列化
data, err := js.Marshal(user)

// 反序列化  
err = js.Unmarshal([]byte(data), &user)
```

## Payment模块重构实现

### 1. **DAO结构重构** ✅

#### **修改前（错误）**
```go
type Dao struct {
    mysql mysql.Mysqler  // ❌ 直接使用mysql包
}

func Load(c *conf.Config) *Dao {
    mysqlConn := mysql.Load(c.MySQL)  // ❌ 错误的加载方式
}
```

#### **修改后（正确）**
```go
type Dao struct {
    mysql msshard.Mysqler  // ✅ 使用msshard包
}

func Load(c *conf.Config) *Dao {
    mysqlConn := msshard.Load(c.MySQL)  // ✅ 正确的加载方式
}
```

### 2. **数据库连接获取** ✅

#### **配置表不分片的连接方式**
```go
// payment_methods和payment_products是配置表，不需要分片
// 使用固定的"0"作为连接标识
db, err := d.mysql.DB("0")
if err != nil {
    return nil, fmt.Errorf("failed to get database connection: %w", err)
}
```

### 3. **查询操作重构** ✅

#### **GetPaymentMethods重构**
```go
// 修改前
rows, err := d.mysql.QueryContext(ctx, query, args...)

// 修改后
db, err := d.mysql.DB("0")
if err != nil {
    return nil, fmt.Errorf("failed to get database connection: %w", err)
}
rows, err := db.QueryContext(ctx, query, args...)
```

#### **GetPaymentMethodByMethod重构**
```go
// 修改前
err = d.mysql.QueryRowContext(ctx, query, method).Scan(...)

// 修改后
db, err := d.mysql.DB("0")
if err != nil {
    return nil, fmt.Errorf("failed to get database connection: %w", err)
}
err = db.QueryRowContext(ctx, query, method).Scan(...)
```

### 4. **JSON序列化重构** ✅

#### **缓存操作**
```go
// 修改前
import "encoding/json"
err = json.Unmarshal([]byte(data), &methods)

// 修改后
import js "creativematrix.com/beyondreading/pkg/json"
err = js.Unmarshal([]byte(data), &methods)
```

#### **配置字段处理**
```go
// 支付方式配置JSON字段处理
if configStr != "" {
    if err := json.Unmarshal([]byte(configStr), &method.Config); err != nil {
        logger.LogErrorf("Failed to unmarshal method config: %v", err)
        method.Config = make(map[string]string)
    }
} else {
    method.Config = make(map[string]string)
}
```

### 5. **配置文件重构** ✅

#### **修改前（单个DSN）**
```toml
[mysql]
dsn = "root:password@tcp(localhost:3306)/beyondreading_payment?charset=utf8mb4&parseTime=True&loc=Local"
```

#### **修改后（DSN数组）**
```toml
[mysql]
dsn = [
    "root:password@tcp(localhost:3306)/beyondreading_payment?charset=utf8mb4&parseTime=True&loc=Local"
]
```

#### **配置结构体**
```go
// 修改前
MySQL struct {
    DSN string `toml:"dsn"`
} `toml:"mysql"`

// 修改后
MySQL struct {
    DSN []string `toml:"dsn"`  // 支持多个DSN
} `toml:"mysql"`
```

### 6. **新增管理方法** ✅

#### **CreatePaymentMethod**
```go
func (d *Dao) CreatePaymentMethod(ctx context.Context, method *po.PaymentMethodConfig) error {
    query := `INSERT INTO payment_methods (method, name, enabled, config, sort_order, created_at, updated_at) 
              VALUES (?, ?, ?, ?, ?, NOW(), NOW())`
    
    // 序列化配置
    configStr, err := js.Marshal(method.Config)
    if err != nil {
        return fmt.Errorf("failed to marshal config: %w", err)
    }

    // 获取数据库连接
    db, err := d.mysql.DB("0")
    if err != nil {
        return fmt.Errorf("failed to get database connection: %w", err)
    }

    _, err = db.ExecContext(ctx, query, method.Method, method.Name, method.Enabled, 
        string(configStr), method.SortOrder)
    return err
}
```

#### **UpdatePaymentMethod**
```go
func (d *Dao) UpdatePaymentMethod(ctx context.Context, method int32, updates map[string]interface{}) error {
    // 动态构建更新语句
    setParts := []string{}
    args := []interface{}{}
    
    for field, value := range updates {
        if field == "config" {
            // 配置字段需要序列化
            if configMap, ok := value.(map[string]string); ok {
                configStr, err := js.Marshal(configMap)
                if err != nil {
                    return fmt.Errorf("failed to marshal config: %w", err)
                }
                setParts = append(setParts, "config = ?")
                args = append(args, string(configStr))
            }
        } else {
            setParts = append(setParts, fmt.Sprintf("%s = ?", field))
            args = append(args, value)
        }
    }
    
    // 执行更新并清除缓存
    // ...
}
```

## 重构后的优势

### 1. **一致性** ✅
- **统一的数据库访问模式** - 与user模块保持一致
- **统一的错误处理方式** - 使用相同的错误包装
- **统一的JSON处理** - 使用项目标准的json包

### 2. **可维护性** ✅
- **标准化的代码结构** - 易于理解和维护
- **统一的配置管理** - 支持多数据源配置
- **一致的缓存策略** - 与其他模块保持一致

### 3. **扩展性** ✅
- **支持分片扩展** - 虽然配置表不分片，但架构支持
- **支持多数据源** - 配置支持多个MySQL实例
- **灵活的配置管理** - JSON配置字段支持复杂配置

### 4. **性能优化** ✅
- **连接池管理** - msshard提供更好的连接管理
- **缓存机制** - 与user模块相同的缓存策略
- **查询优化** - 使用相同的查询模式

## 文件修改清单

### 1. **核心文件修改** ✅
```
beyondreading/gen/app/base/payment/dao/
├── dao.go                              # 使用msshard.Mysqler
├── payment_method.go                   # 重构所有MySQL操作
└── (其他文件保持不变)
```

### 2. **配置文件修改** ✅
```
beyondreading/gen/app/base/payment/
├── conf/conf.go                        # MySQL配置结构改为数组
└── cmd/base-payment.toml               # DSN配置改为数组格式
```

### 3. **新增功能** ✅
```
payment_method.go新增方法：
├── CreatePaymentMethod()               # 创建支付方式
├── UpdatePaymentMethod()               # 更新支付方式
├── CreatePaymentProduct()              # 创建支付产品
└── UpdatePaymentProduct()              # 更新支付产品
```

## 使用示例

### 1. **查询支付方式**
```go
// 获取所有启用的支付方式
methods, err := dao.GetPaymentMethods(ctx, &enabled)

// 获取特定支付方式
method, err := dao.GetPaymentMethodByMethod(ctx, 1) // Google Pay
```

### 2. **管理支付方式**
```go
// 创建新的支付方式
method := &po.PaymentMethodConfig{
    Method: 6,
    Name: "Stripe",
    Enabled: true,
    Config: map[string]string{
        "api_key": "sk_test_...",
        "webhook_secret": "whsec_...",
    },
    SortOrder: 6,
}
err := dao.CreatePaymentMethod(ctx, method)

// 更新支付方式
updates := map[string]interface{}{
    "enabled": false,
    "config": map[string]string{
        "api_key": "sk_live_...",
    },
}
err := dao.UpdatePaymentMethod(ctx, 6, updates)
```

### 3. **查询支付产品**
```go
// 获取VIP产品
vipType := int32(1)
enabled := true
products, err := dao.GetPaymentProducts(ctx, &vipType, &enabled)

// 获取特定产品
product, err := dao.GetPaymentProductById(ctx, "vip_monthly")
```

## 总结

通过参照user模块的MySQL操作模式，payment模块现在：

1. **✅ 使用标准的msshard.Mysqler** - 与项目其他模块保持一致
2. **✅ 统一的数据库连接管理** - 支持连接池和多数据源
3. **✅ 标准的JSON序列化** - 使用项目统一的json包
4. **✅ 完整的CRUD操作** - 支持创建、查询、更新配置
5. **✅ 一致的错误处理** - 使用相同的错误包装模式
6. **✅ 优化的缓存策略** - 与其他模块保持一致

现在payment模块的MySQL操作完全符合项目规范，与user模块保持一致的代码风格和架构模式！
