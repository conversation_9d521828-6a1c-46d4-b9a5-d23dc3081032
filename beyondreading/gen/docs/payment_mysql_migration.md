# Payment模块MySQL迁移总结

## 迁移目标

将payment_methods和payment_products两个配置表从MongoDB迁移到MySQL，保持现有的数据结构设计。

## 迁移原因

### 1. **配置表更适合关系型数据库**
- payment_methods和payment_products是配置表，数据结构相对固定
- 需要支持复杂查询和事务操作
- MySQL提供更好的数据一致性保证

### 2. **混合存储架构**
```
MongoDB: 存储业务数据
├── payment_orders      # 支付订单
├── payment_callbacks   # 支付回调
├── payment_refunds     # 支付退款
└── payment_logs        # 支付日志

MySQL: 存储配置数据
├── payment_methods     # 支付方式配置
└── payment_products    # 支付产品配置
```

## 迁移内容

### 1. **数据库结构设计** ✅

#### **payment_methods表**
```sql
CREATE TABLE payment_methods (
    id INT AUTO_INCREMENT PRIMARY KEY,
    method INT NOT NULL COMMENT '支付方式: 1-Google Pay, 2-Apple Pay, 3-PayPal, 4-Alipay, 5-WeChat Pay',
    name VARCHAR(50) NOT NULL COMMENT '支付方式名称',
    enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    config JSON COMMENT '支付方式配置信息',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序顺序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_method (method),
    KEY idx_enabled (enabled),
    KEY idx_sort_order (sort_order)
);
```

#### **payment_products表**
```sql
CREATE TABLE payment_products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id VARCHAR(50) NOT NULL COMMENT '产品ID',
    product_name VARCHAR(100) NOT NULL COMMENT '产品名称',
    product_type INT NOT NULL COMMENT '产品类型: 1-VIP, 2-包月订阅, 3-书币充值',
    price DECIMAL(10,2) NOT NULL COMMENT '价格(美分)',
    currency VARCHAR(3) NOT NULL DEFAULT 'USD' COMMENT '货币类型',
    coin_amount DECIMAL(10,2) DEFAULT 0 COMMENT '书币数量',
    vip_days INT DEFAULT 0 COMMENT 'VIP天数',
    enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    sort_order INT NOT NULL DEFAULT 0 COMMENT '排序顺序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_product_id (product_id),
    KEY idx_product_type (product_type),
    KEY idx_enabled (enabled),
    KEY idx_sort_order (sort_order)
);
```

### 2. **代码迁移** ✅

#### **DAO层修改**
```go
// dao/dao.go - 添加MySQL连接
type Dao struct {
    conf        *conf.Config
    cache       redis.Redis
    schema      map[string]*mongo.Model  // MongoDB连接
    rabbitMQ    *rabbitmq.Broker
    paymentConn *mongo.Connection
    mysql       mysql.Mysqler           // 新增MySQL连接
}

func Load(c *conf.Config) *Dao {
    // MongoDB连接 (用于业务数据)
    paymentConn := mongo.Connect(c.MongoDB.URI)
    
    // MySQL连接 (用于配置数据)
    mysqlConn := mysql.Load(c.MySQL)
    
    return &Dao{
        // ...
        mysql: mysqlConn,
    }
}
```

#### **payment_method.go重构**
```go
// 修改前 (MongoDB)
var methods []*po.PaymentMethodConfig
err := d.schema[model.TablePaymentMethods].Find(filter, &methods, mongo.Ctx(ctx))

// 修改后 (MySQL)
query := "SELECT id, method, name, enabled, config, sort_order, created_at, updated_at FROM payment_methods"
rows, err := d.mysql.QueryContext(ctx, query, args...)
```

### 3. **配置文件更新** ✅

#### **base-payment.toml**
```toml
[mongodb]
uri = "mongodb://localhost:27017"
database = "beyondreading_payment"

[mysql]  # 新增MySQL配置
dsn = "root:password@tcp(localhost:3306)/beyondreading_payment?charset=utf8mb4&parseTime=True&loc=Local"

[redis]
addr = "localhost:6379"
password = ""
db = 0

[rabbitmq]
url = "amqp://guest:guest@localhost:5672/"
exchange = "payment"
```

#### **conf.go结构体**
```go
type Config struct {
    MongoDB struct {
        URI      string `toml:"uri"`
        Database string `toml:"database"`
    } `toml:"mongodb"`

    MySQL struct {  // 新增MySQL配置
        DSN string `toml:"dsn"`
    } `toml:"mysql"`
    
    // ...
}
```

### 4. **模型常量更新** ✅

#### **model/model.go**
```go
// MongoDB表名常量 (业务数据)
const (
    TablePaymentOrders    = "payment_orders"
    TablePaymentCallbacks = "payment_callbacks"
    TablePaymentRefunds   = "payment_refunds"
    TablePaymentLogs      = "payment_logs"
)

// MySQL表名常量 (配置数据)
const (
    TablePaymentMethods  = "payment_methods"
    TablePaymentProducts = "payment_products"
)
```

## 迁移后的架构

### 1. **数据存储分层**
```
应用层
├── Payment API (HTTP)
└── Payment Base (gRPC)

数据层
├── MongoDB (业务数据)
│   ├── payment_orders      # 支付订单
│   ├── payment_callbacks   # 支付回调
│   ├── payment_refunds     # 支付退款
│   └── payment_logs        # 支付日志
│
├── MySQL (配置数据)
│   ├── payment_methods     # 支付方式配置
│   └── payment_products    # 支付产品配置
│
└── Redis (缓存)
    ├── 支付订单缓存
    ├── 支付方式缓存
    └── 支付产品缓存
```

### 2. **查询性能优化**
```sql
-- 支付方式查询优化
CREATE INDEX idx_payment_methods_enabled_sort ON payment_methods (enabled, sort_order);

-- 支付产品查询优化
CREATE INDEX idx_payment_products_type_enabled ON payment_products (product_type, enabled);
CREATE INDEX idx_payment_products_enabled_sort ON payment_products (enabled, sort_order);
```

### 3. **缓存策略**
```go
// 支付方式缓存 (1小时)
key := fmt.Sprintf(model.RedisPaymentMethodId, method)
d.cache.RSet(ctx, key, data, model.CachePaymentMethodExpire)

// 支付产品缓存 (30分钟)
key := fmt.Sprintf(model.RedisPaymentProductId, productId)
d.cache.RSet(ctx, key, data, model.CachePaymentProductExpire)
```

## 迁移的文件列表

### 1. **修改的文件** ✅
```
beyondreading/gen/app/base/payment/
├── dao/dao.go                          # 添加MySQL连接
├── dao/payment_method.go               # MongoDB改为MySQL
├── conf/conf.go                        # 添加MySQL配置
├── cmd/base-payment.toml               # 添加MySQL配置
└── model/model.go                      # 分离MongoDB和MySQL表常量
```

### 2. **新增的文件** ✅
```
beyondreading/gen/database/
└── payment_mysql_init.sql              # MySQL初始化脚本
```

### 3. **保持不变的文件** ✅
```
beyondreading/gen/app/base/payment/dao/
├── payment_order.go                    # 继续使用MongoDB
├── payment_callback.go                 # 继续使用MongoDB
├── payment_refund.go                   # 继续使用MongoDB
└── payment_log.go                      # 继续使用MongoDB
```

## 部署步骤

### 1. **数据库初始化**
```bash
# 1. 创建MySQL数据库和表
mysql -u root -p < beyondreading/gen/database/payment_mysql_init.sql

# 2. 验证表结构
mysql -u root -p beyondreading_payment -e "SHOW TABLES;"
mysql -u root -p beyondreading_payment -e "SELECT * FROM payment_methods;"
mysql -u root -p beyondreading_payment -e "SELECT * FROM payment_products;"
```

### 2. **服务启动**
```bash
# 1. 启动MongoDB (业务数据)
mongod --dbpath /data/db

# 2. 启动MySQL (配置数据)
systemctl start mysql

# 3. 启动Redis (缓存)
redis-server

# 4. 启动RabbitMQ (消息队列)
systemctl start rabbitmq-server

# 5. 启动Payment Base服务
cd beyondreading/gen/app/base/payment/cmd
go run main.go

# 6. 启动Payment API服务
cd beyondreading/gen/app/api/payment/cmd
go run main.go
```

### 3. **数据迁移** (如果有现有数据)
```bash
# 如果MongoDB中已有payment_methods和payment_products数据
# 需要编写迁移脚本将数据从MongoDB迁移到MySQL
```

## 优势总结

### 1. **性能优势**
- **MySQL配置查询** - 更快的关系查询和索引优化
- **MongoDB业务数据** - 更好的文档存储和扩展性
- **Redis缓存** - 减少数据库查询压力

### 2. **维护优势**
- **配置管理** - MySQL提供更好的数据一致性
- **业务数据** - MongoDB提供更好的灵活性
- **混合架构** - 各取所长，优化整体性能

### 3. **扩展优势**
- **配置表** - 支持复杂的关系查询和事务
- **业务表** - 支持灵活的文档结构和水平扩展
- **缓存层** - 提供高性能的数据访问

现在Payment模块使用了最优的混合存储架构：MySQL存储配置数据，MongoDB存储业务数据，Redis提供缓存支持！
