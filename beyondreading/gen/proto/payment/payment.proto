syntax = "proto3";
package payment;
option go_package = "creativematrix.com/beyondreading/gen/proto/payment";

service Payment {
  // 创建支付订单
  rpc CreatePaymentOrder(CreatePaymentOrderReq) returns (CreatePaymentOrderResp);
  
  // 处理支付回调
  rpc ProcessPaymentCallback(ProcessPaymentCallbackReq) returns (ProcessPaymentCallbackResp);
  
  // 查询支付订单状态
  rpc GetPaymentOrder(GetPaymentOrderReq) returns (GetPaymentOrderResp);
  
  // 获取支付订单列表
  rpc GetPaymentOrders(GetPaymentOrdersReq) returns (GetPaymentOrdersResp);
  
  // 取消支付订单
  rpc CancelPaymentOrder(CancelPaymentOrderReq) returns (CancelPaymentOrderResp);
  
  // 退款
  rpc RefundPayment(RefundPaymentReq) returns (RefundPaymentResp);
  
  // 获取支付方式列表
  rpc GetPaymentMethods(GetPaymentMethodsReq) returns (GetPaymentMethodsResp);
}

// 支付方式枚举
enum PaymentMethod {
  UNKNOWN_PAYMENT = 0;
  GOOGLE_PAY = 1;
  APPLE_PAY = 2;
  PAYPAL = 3;
  ALIPAY = 4;
  WECHAT_PAY = 5;
}

// 支付类型枚举
enum PaymentType {
  UNKNOWN_TYPE = 0;
  VIP_PURCHASE = 1;        // VIP购买
  MONTHLY_SUBSCRIPTION = 2; // 包月订阅
  COIN_RECHARGE = 3;       // 书币充值
}

// 订单状态枚举
enum OrderStatus {
  UNKNOWN_STATUS = 0;
  PENDING = 1;    // 待支付
  PAID = 2;       // 已支付
  CANCELLED = 3;  // 已取消
  REFUNDED = 4;   // 已退款
  FAILED = 5;     // 支付失败
}

// 创建支付订单请求
message CreatePaymentOrderReq {
  uint64 userId = 1;
  PaymentMethod paymentMethod = 2;
  PaymentType paymentType = 3;
  int64 amount = 4;           // 支付金额（分）
  string currency = 5;        // 货币类型
  string productId = 6;       // 产品ID
  string productName = 7;     // 产品名称
  string description = 8;     // 订单描述
  map<string, string> metadata = 9; // 额外元数据
  string clientIp = 10;       // 客户端IP
  string userAgent = 11;      // 用户代理
  string returnUrl = 12;      // 支付成功返回URL
  string cancelUrl = 13;      // 支付取消返回URL
}

// 创建支付订单响应
message CreatePaymentOrderResp {
  int32 code = 1;
  string message = 2;
  PaymentOrderInfo order = 3;
  string paymentUrl = 4;      // 支付URL
  string paymentData = 5;     // 支付数据（如移动端SDK需要的数据）
}

// 处理支付回调请求
message ProcessPaymentCallbackReq {
  PaymentMethod paymentMethod = 1;
  string orderId = 2;
  string transactionId = 3;   // 第三方交易ID
  OrderStatus status = 4;
  int64 amount = 5;
  string currency = 6;
  map<string, string> callbackData = 7; // 回调原始数据
  string signature = 8;       // 签名
}

// 处理支付回调响应
message ProcessPaymentCallbackResp {
  int32 code = 1;
  string message = 2;
  bool success = 3;
}

// 获取支付订单请求
message GetPaymentOrderReq {
  string orderId = 1;
  uint64 userId = 2;
}

// 获取支付订单响应
message GetPaymentOrderResp {
  int32 code = 1;
  string message = 2;
  PaymentOrderInfo order = 3;
}

// 获取支付订单列表请求
message GetPaymentOrdersReq {
  uint64 userId = 1;
  PaymentMethod paymentMethod = 2;
  PaymentType paymentType = 3;
  OrderStatus status = 4;
  int32 page = 5;
  int32 pageSize = 6;
  int64 startTime = 7;
  int64 endTime = 8;
}

// 获取支付订单列表响应
message GetPaymentOrdersResp {
  int32 code = 1;
  string message = 2;
  repeated PaymentOrderInfo orders = 3;
  int64 total = 4;
}

// 取消支付订单请求
message CancelPaymentOrderReq {
  string orderId = 1;
  uint64 userId = 2;
  string reason = 3;
}

// 取消支付订单响应
message CancelPaymentOrderResp {
  int32 code = 1;
  string message = 2;
}

// 退款请求
message RefundPaymentReq {
  string orderId = 1;
  uint64 userId = 2;
  int64 refundAmount = 3;     // 退款金额（分）
  string reason = 4;          // 退款原因
  string refundId = 5;        // 退款单号
}

// 退款响应
message RefundPaymentResp {
  int32 code = 1;
  string message = 2;
  string refundId = 3;
  bool success = 4;
}

// 获取支付方式列表请求
message GetPaymentMethodsReq {
  string platform = 1;       // 平台：ios, android, web
  string region = 2;          // 地区
}

// 获取支付方式列表响应
message GetPaymentMethodsResp {
  int32 code = 1;
  string message = 2;
  repeated PaymentMethodInfo methods = 3;
}

// 支付订单信息
message PaymentOrderInfo {
  string orderId = 1;
  uint64 userId = 2;
  PaymentMethod paymentMethod = 3;
  PaymentType paymentType = 4;
  int64 amount = 5;
  string currency = 6;
  string productId = 7;
  string productName = 8;
  string description = 9;
  OrderStatus status = 10;
  string transactionId = 11;  // 第三方交易ID
  map<string, string> metadata = 12;
  int64 createdAt = 13;
  int64 updatedAt = 14;
  int64 paidAt = 15;
  int64 expiredAt = 16;
  string failureReason = 17;
}

// 支付方式信息
message PaymentMethodInfo {
  PaymentMethod method = 1;
  string name = 2;
  string displayName = 3;
  string icon = 4;
  bool enabled = 5;
  repeated string supportedCurrencies = 6;
  map<string, string> config = 7;
}
