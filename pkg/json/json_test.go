package json

import (
	"fmt"
	"testing"
)

type JsonTest struct {
	Id   int64  `json:"id"`
	Name string `json:"name"`
}

func Test_JSON(t *testing.T) {
	a := &JsonTest{
		Id:   10,
		Name: "Bob",
	}

	b, err := JSON.MarshalToString(a)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("MarshalToString:", b)

	var c *JsonTest
	if err = JSON.UnmarshalFromString(b, &c); err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("UnmarshalFromString:", c)
}
