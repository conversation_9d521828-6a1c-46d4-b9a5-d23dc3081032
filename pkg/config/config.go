package config

import (
	"fmt"
	"os"
	"reflect"
	"strings"

	"creativematrix.com/beyondreading/pkg/wangsu"

	"github.com/BurntSushi/toml"

	"creativematrix.com/beyondreading/pkg/elastic"
	"creativematrix.com/beyondreading/pkg/mysql"
	"creativematrix.com/beyondreading/pkg/redis"
)

type Base struct {
	Env string

	IsWinDebug bool

	Port struct {
		HTTP  string
		DEBUG string
		GRPC  string
	}
	Mysql *mysql.Config

	Cache *redis.Config

	Jaeger struct {
		Collector string
		Disabled  bool
	}

	Etcd struct {
		Addrs []string
	}
	ElasticSearch *elastic.Config

	UserTokenSecret string // token加密串

	AesKey string //返回消息md5 key
	Md5Key string //请求消息md5 key

	WebAesKey string //web AesKey
	WebMd5Key string // web md5Key

	PcAesKey string //pc AesKey
	PcMd5Key string // pc md5Key

	AesSwitch bool //加密是否开启

	Md5Switch bool // 是否开启md5加密

	LightSwitch bool //是否返回明文

	InnerKey string //内部接口调用加密

	WhiteList []string //回调白名单

	Wangsu *wangsu.Config // 网宿配置

	BaseImgUrl string
}

func rC(port string, name string, config interface{}) {
	reflect.ValueOf(config).Elem().FieldByName("Port").FieldByName(name).SetString(port)
}

func path(app string) (string, string) {
	basePath := "./app/base.toml"

	strarr := strings.SplitN(app, "-", 2)

	var i = 2
	strarr = append(strarr[:i], append([]string{"cmd"}, strarr[i:]...)...)

	// strarr := []string{app, "cmd"}
	apiPath := fmt.Sprintf("./app/%s/%s.toml", strings.Join(strarr, "/"), app)

	env := os.Getenv("SUMMER_ENV")
	if env == "production" {
		basePath = "/etc/config/base.toml"
		apiPath = "/etc/config/" + app + ".toml"
	} else if env == "gray" {
		basePath = "./summer-ops/config/base.toml"
		apiPath = "./summer-ops/config/" + app + ".toml"
	} else if strings.HasPrefix(env, "test") {
		basePath = fmt.Sprintf("./summer-config-test/%s/base.toml", env)
		apiPath = fmt.Sprintf("./summer-config-test/%s/%s.toml", env, app)
	}

	return basePath, apiPath
}

func Load(app string, config interface{}) (err error) {
	basePath, apiPath := path(app)
	if _, err = toml.DecodeFile(basePath, config); err != nil {
		return
	}
	if _, err = toml.DecodeFile(apiPath, config); err != nil {
		return
	}

	if httpP := os.Getenv("HTTP_PORT"); httpP != "" {
		rC(httpP, "HTTP", config)
	}

	if debugP := os.Getenv("DEBUG_PORT"); debugP != "" {
		fmt.Println(debugP)
		rC(debugP, "DEBUG", config)
	}

	if grpcP := os.Getenv("GRPC_PORT"); grpcP != "" {
		rC(grpcP, "GRPC", config)
	}

	return
}

func pathPlus(app string) (string, string) {
	basePath := "./app/base.toml"

	strarr := strings.SplitN(app, "-", -1)

	// strarr := []string{app, "cmd"}
	apiPath := fmt.Sprintf("./app/%s/%s.toml", strings.Join(strarr, "/"), app)

	env := os.Getenv("SUMMER_ENV")
	if env == "production" {
		basePath = "/etc/config/base.toml"
		apiPath = "/etc/config/" + app + ".toml"
	} else if env == "gray" {
		basePath = "./summer-ops/config/base.toml"
		apiPath = "./summer-ops/config/" + app + ".toml"
	} else if strings.HasPrefix(env, "test") {
		basePath = fmt.Sprintf("./summer-config-test/%s/base.toml", env)
		apiPath = fmt.Sprintf("./summer-config-test/%s/%s.toml", env, app)
	}

	return basePath, apiPath
}

func LoadPlus(app string, config interface{}) (err error) {
	basePath, apiPath := pathPlus(app)
	if _, err = toml.DecodeFile(basePath, config); err != nil {
		return
	}
	if _, err = toml.DecodeFile(apiPath, config); err != nil {
		return
	}

	if httpP := os.Getenv("HTTP_PORT"); httpP != "" {
		rC(httpP, "HTTP", config)
	}

	if debugP := os.Getenv("DEBUG_PORT"); debugP != "" {
		fmt.Println(debugP)
		rC(debugP, "DEBUG", config)
	}

	if grpcP := os.Getenv("GRPC_PORT"); grpcP != "" {
		rC(grpcP, "GRPC", config)
	}

	return
}
