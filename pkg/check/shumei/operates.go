package shumei

import (
	"bytes"
	"context"
	"encoding/json"
	"io/ioutil"

	"creativematrix.com/beyondreading/pkg/ecode"
	"creativematrix.com/beyondreading/pkg/http"
)

type sm struct {
	conf SMConfig
}

func SM(conf SMConfig) ISM {
	return &sm{
		conf: conf,
	}
}

type ISM interface {
	//图片
	Img(ctx context.Context, request ImgRequest, response ReviewValidInterface) error

	//文本
	Msg(ctx context.Context, request MsgRequest, response ReviewValidInterface) error

	//音频
	AudioFile(ctx context.Context, request AudioFileRequest, response ReviewValidInterface) error

	//视频文件
	VideoFile(ctx context.Context, request VideoFileRequest, response ReviewValidInterface) error
}

func (s *sm) Img(ctx context.Context, request ImgRequest, response ReviewValidInterface) error {
	request.AccessKey = s.conf.AccessKey

	request.Type = "POLITICS_PORN_AD_BEHAVIOR"
	request.AppId = "default"

	b, _ := json.Marshal(request)
	resp, err := http.NewDefaultHttpClient().Post(s.conf.ImgUrl, "application/json", bytes.NewBuffer(b))

	if err != nil {
		return err
	}
	if resp != nil {
		respBytes, _ := ioutil.ReadAll(resp.Body)
		err = json.Unmarshal(respBytes, &response)
		if err != nil {
			return err
		}
	}
	return nil
}

func (s *sm) Msg(ctx context.Context, request MsgRequest, response ReviewValidInterface) error {

	request.AccessKey = s.conf.AccessKey
	request.Type = "ALL"
	request.AppId = "default"

	if request.EventId == "" {
		request.EventId = Message
	}

	b, _ := json.Marshal(request)

	resp, err := http.NewDefaultHttpClient().Post(s.conf.TxtUrl, "application/json", bytes.NewBuffer(b))

	if err != nil {
		return err
	}

	if resp != nil {
		respBytes, _ := ioutil.ReadAll(resp.Body)
		return json.Unmarshal(respBytes, response)
	}
	return nil
}

func (s *sm) AudioFile(ctx context.Context, request AudioFileRequest, response ReviewValidInterface) error {
	request.AccessKey = s.conf.AccessKey

	if request.Type == "" {
		request.Type = "AD_PORN_POLITICAL_MOAN"
	}
	if request.AppId == "" {
		request.AppId = "default"
	}

	if request.Callback == "" {
		request.Callback = s.conf.AudioCallback
	}

	b, _ := json.Marshal(request)
	resp, err := http.NewDefaultHttpClient().Post(s.conf.AudioUrl, "application/json", bytes.NewBuffer(b))

	if err != nil {
		return err
	}

	if resp != nil {
		respBytes, _ := ioutil.ReadAll(resp.Body)
		return json.Unmarshal(respBytes, &response)
	}
	return nil
}

func (s *sm) VideoFile(ctx context.Context, request VideoFileRequest, response ReviewValidInterface) error {
	if request.ImgType == "" {
		request.ImgType = "POLITICS_PORN_AD"
	}

	request.AccessKey = s.conf.AccessKey

	if request.AudioType == "" {
		request.AudioType = "NONE"
	}

	if request.AppId == "" {
		request.AppId = "default"
	}

	b, _ := json.Marshal(request)
	resp, err := http.NewDefaultHttpClient().Post(s.conf.VideoUrl, "application/json", bytes.NewBuffer(b))
	if err != nil {
		return err
	}

	if resp != nil {
		respBytes, _ := ioutil.ReadAll(resp.Body)
		return json.Unmarshal(respBytes, response)
	}
	return nil
}

// Msg
func (msg *MsgResponse) Pass() error {
	if msg.Code != 1100 {
		return ecode.SmCodeError
	}

	if msg.RiskLevel == "PASS" {
		return nil
	}
	if msg.RiskLevel == "REVIEW" {
		return ecode.SmReviewError
	}
	return ecode.SmRejectError
}
func (msg *MsgResponse) Review() error {
	if msg.Code != 1100 {
		return ecode.SmCodeError
	}

	if msg.RiskLevel == "PASS" || msg.RiskLevel == "REVIEW" {
		return nil
	}

	return ecode.SmRejectError

}

func (msg *MsgResponse) Label() string {
	return msg.RiskDescription
}

func (msg *MsgResponse) GetRequestId() string {
	return msg.RequestId
}

// Img
func (img *ImgResponse) Pass() error {
	if img.Code != 1100 {
		return ecode.SmCodeError
	}
	if img.RiskLevel == "PASS" {
		return nil
	}
	if img.RiskLevel == "REVIEW" {
		return ecode.SmReviewError
	}
	return ecode.SmRejectError
}

func (img *ImgResponse) Label() string {
	return img.Detail.Description
}
func (img *ImgResponse) Review() error {
	if img.Code != 1100 {
		return ecode.SmCodeError
	}
	if img.RiskLevel == "PASS" {
		return nil
	}
	return ecode.SmRejectError
}

func (img *ImgResponse) GetRequestId() string {
	return img.RequestId
}

// Audio
func (a *AudioFileAyncRequest) Pass() error {
	if a.Code != 1100 {
		return ecode.SmCodeError
	}
	if a.RiskLevel == "PASS" {
		return nil
	}
	if a.RiskLevel == "REVIEW" {
		return ecode.SmReviewError
	}
	return ecode.SmRejectError
}

func (a *AudioFileAyncRequest) Review() error {
	if a.Code != 1100 {
		return ecode.SmCodeError
	}

	if a.RiskLevel == "PASS" || a.RiskLevel == "REVIEW" {
		return nil
	}

	return ecode.SmRejectError
}

func (a *AudioFileAyncRequest) Label() string {
	return a.Labels
}

func (a *AudioFileAyncRequest) GetRequestId() string {
	return a.RequestId
}
func (a *AudioFileAyncRequest) Status() string {
	return a.RiskLevel
}

func (a *AudioFileResponse) Review() error {
	if a.Code != 1100 {
		return ecode.SmCodeError
	}
	return nil
}
func (a *AudioFileResponse) GetRequestId() string {
	return a.RequestId
}
func (a *AudioFileResponse) Label() string {
	return ""
}

/*
VideoFile
*/
func (v *VideoFileAyncRequest) Review() error {
	if v.Code != 1100 {
		return ecode.SmCodeError
	}

	if v.RiskLevel == "PASS" || v.RiskLevel == "REVIEW" {
		return nil
	}

	return ecode.SmRejectError
}

func (v *VideoFileAyncRequest) Label() string {
	return v.Labels
}

func (v *VideoFileAyncRequest) GetRequestId() string {
	return v.RequestId
}

func (v *VideoFileAyncRequest) Status() string {
	return v.RiskLevel
}
func (v *VideoFileAyncRequest) Pass() error {
	if v.Code != 1100 {
		return ecode.SmCodeError
	}
	if v.RiskLevel == "PASS" {
		return nil
	}
	if v.RiskLevel == "REVIEW" {
		return ecode.SmReviewError
	}
	return ecode.SmRejectError
}

func (v *VideoFileResponse) Review() error {
	if v.Code != 1100 {
		return ecode.SmCodeError
	}
	return nil
}

func (v *VideoFileResponse) GetRequestId() string {
	return v.RequestId
}
func (v *VideoFileResponse) Label() string {
	return ""
}
