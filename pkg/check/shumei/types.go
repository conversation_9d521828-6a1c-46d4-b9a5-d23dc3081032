package shumei

type EventId string

const (
	Nickname       EventId = "nickname"       //昵称
	Message        EventId = "message"        //私聊
	GroupChat      EventId = "groupChat"      //群聊
	Title          EventId = "title"          //标题
	Notice         EventId = "notice"         //公告
	Article        EventId = "article"        //帖子
	Comment        EventId = "comment"        //评论
	Barrage        EventId = "barrage"        //弹幕
	Search         EventId = "search"         //搜索栏
	RoomName       EventId = "roomName"       //房间名
	Profile        EventId = "profile"        //个人简介
	ProductName    EventId = "productName"    //商品名称
	ProductInfo    EventId = "productInfo"    //商品介绍
	ProductReviews EventId = "productReviews" //商品评价
	InternalChat   EventId = "internalChat"   //内部交流
	OutsideWork    EventId = "outsideWork"    //外部合作
)

type BaseValidInterface interface {
	ReviewValidInterface
	RequestIdInterface
	Label() string
}

type PassValidInterface interface {
	Pass() error
}

type ReviewValidInterface interface {
	Review() error
}

type RequestIdInterface interface {
	GetRequestId() string
}

type MsgInterface interface {
	PassValidInterface
	BaseValidInterface
}

type ImgInterface interface {
	PassValidInterface
	BaseValidInterface
}

type AudioInterface interface {
	BaseValidInterface
}

type AudioReqInterface interface {
	BaseValidInterface
	PassValidInterface
	Status() string
}

type VideoInterface interface {
	BaseValidInterface
}

type VideoReqInterface interface {
	BaseValidInterface
	PassValidInterface
	Status() string
}

/*
	===============>文本<===============
*/
//文本消息请求结构体
type MsgRequest struct {
	AccessKey string `json:"accessKey"`
	/*
		应用标识：
		用于区分相同公司的不同应用数据
		默认应用值：default
		传递其他值时需联系数美服务协助开通
	*/
	AppId   string  `json:"appId"`
	EventId EventId `json:"eventId"`
	/*
		检测风险类型
		可选值如下：
		识别所有风险类型：ALL
	*/
	Type string `json:"type"`
	/*
		请求的数据内容
		最长1MB
	*/
	Data *MsgData `json:"data"`
}

type MsgData struct {
	/*
		要检测的文本内容
		文本字数上限2000字，200字内效果最佳
		若传递nickname字段，则会同时校验文本+昵称内容。
		注意：有超过2000字以上文本内容建议联系数美协商
	*/
	Text string `json:"text"`
	/*
				建议使用贵司用户UID（可加密）自行生成 ,
				标识用户唯一身份用作灌水和广告等行为维度风控。
				如无用户uid的场景建议使用唯一的数据标识传值
		/
	*/
	TokenId string `json:"tokenId"`
	//用户昵称
	Nickname string `json:"nickname,omitempty"`
	/*
		发送该文本的的用户公网ipv4地址
		该参数用于IP维度的用户行为分析，强烈要求传入
	*/
	Ip string `json:"ip,omitempty"`
	//于辅助文本检测的相关信息，提升拦截效果
	Extra *MsgExtra `json:"extra,omitempty"`
	//数美设备指纹标识
	DeviceId string `json:"deviceId,omitempty"`
}

type MsgExtra struct {
	ReceiveTokenId string `json:"receiveTokenId,omitempty"`
	Topic          string `json:"topic,omitempty"`
	Room           string `json:"room,omitempty"`
	AtId           string `json:"atId,omitempty"`
	Level          int    `json:"level,omitempty"`
	/*
		用户角色
		对不同角色可配置不同策略。
		直播领域可取值：
		房管：ADMIN
		主播：HOST
		系统角色：SYSTEM
		游戏领域可取值：
		管理员：ADMIN
		普通用户：USER
	*/
	Role string `json:"role,omitempty"`
	/*
		用户性别，可选值：
		0：男性
		1：女性
		2：性别不明
	*/
	Sex int `json:"sex,omitempty"`
	/*
		区分不同appId下的相同应用，可选值：
		0：不区分
		1：区分
		不传该字段默认值为0
	*/
	IsTokenSeparate int `json:"isTokenSeparate,omitempty"`
}

type MsgResponse struct {
	/*
		1100：成功
		1901：QPS超限
		1902：参数不合法
		1903：服务失败
		9100：余额不足
		9101：无权限操作
	*/
	Code      int    `json:"code"`
	Message   string `json:"message"`   //返回码详情描述
	RequestId string `json:"requestId"` //强烈建议保存 请求唯一标识，后续可用于数据查询调优
	/*
		风险级别
		可能返回值：
		PASS：正常内容，建议直接放行
		REVIEW：可疑内容，建议人工审核
		REJECT：违规内容，建议直接拦截
	*/
	RiskLevel string `json:"riskLevel"`
	/*
		一级风险标签:当riskLevel为PASS时返回normal
		可能取值：
		涉政 ：politics
		暴恐 ：violence
		违禁 ：ban
		色情 ：porn
		辱骂：abuse
		广告 ：ad
		灌水 ：spam
		广告法 ：ad_law
		黑名单 ：blacklist
		无意义 ：meaningless
		未成年人 ：minor
		其他：other，未包含在以上标签中的其他标签，兼容使用，防止标签迭代产生的影响
	*/
	RiskLabel1 string `json:"riskLabel1"`
	//一级风险标签:当riskLevel为PASS时返回为空字符串""
	RiskLabel2 string `json:"riskLabel2"`
	//一级风险标签:当riskLevel为PASS时返回为空字符串""
	RiskLabel3 string `json:"riskLabel3"`
	//风险原因：当riskLevel为PASS时为正常
	RiskDescription string `json:"riskDescription"`
	//风险详情：仅在命中关键词时有值
	RiskDetail struct {
	} `json:"riskDetail"`
	//辅助信息：返回命中的所有风险标签，无风险时返回为空，注意：此字段用于辅助人工审核展示，与riskLevel结果没有1v1对应关系
	AllLabels []struct {
		//置信度：可选值在【0-1】之间，值越大，可信度越高
		Probability float64 `json:"probability"`
		//风险原因：当riskLevel为PASS时为正常
		RiskDescription string `json:"riskDescription"`
		//风险详情：格式与上层riskDetail结构相同
		RiskDetail struct {
			//辅助信息：高风险内容片段检测文本包含涉政、暴恐、违禁、广告法等风险内容的时候存在
			RiskSegments []struct {
				Position []int  `json:"position"` //高风险内容片段所在位置
				Segment  string `json:"segment"`  //辅助信息：高风险内容片段
			} `json:"riskSegments,omitempty"`

			//辅助信息：展示命中的客户自定义名单列表
			MatchedLists []struct {
				Word     string `json:"word"`     //命中敏感词
				Position string `json:"position"` //命中敏感词位置
			} `json:"matchedLists"`
		} `json:"riskDetail"`
		//一级风险标签:当riskLevel为PASS时返回normal
		RiskLabel1 string `json:"riskLabel1"`
		//一级风险标签:当riskLevel为PASS时返回为空字符串""
		RiskLabel2 string `json:"riskLabel2"`
		//一级风险标签:当riskLevel为PASS时返回为空字符串""
		RiskLabel3 string `json:"riskLabel3"`
	} `json:"allLabels"`
	//辅助信息：预留扩展字段，必返，返回值为空
	AuxInfo interface{} `json:"auxInfo"`
	//	账号风险画像信息：仅在命中时有值，数据接口见详情
	TokenLabels struct {
		//辅助信息：展示UGC内容账号相关风险
		UgcAccountRisk struct {
			//辅助信息：展示色情账号风险分取值区间[0-1]
			Sexy_risk_tokenid float64 `json:"sexy_risk_tokenid"`
		} `json:"UGC_account_risk"`
	} `json:"tokenLabels"`
}

/*
	===============>图片<===============
*/

type ImgRequest struct {
	AccessKey interface{} `json:"accessKey"`
	Type      string      `json:"type"`
	/*
		应用标识
		用于区分相同公司的不同应用
		该参数传递值可与数美服务协商用于区分应用
		默认应用值：default
	*/
	AppId string `json:"appId"`
	//请求数据内容，最长10MB
	Data *ImgData `json:"data"`
}

type ImgData struct {
	/*
		要检测的图片
		可使用图片的base64编码或者图片的url链接
		支持格式：jpg，jpeg，jp2，png，webp，gif，bmp，tiff，tif，dib，ppm，pgm，pbm，hdr，pic
		建议图片像素不小于256*256
	*/
	Img string `json:"img"`
	/*
		客户端用户唯一标识
		用于用户行为分析，建议传入用户UID
		注：不同用户务必传入不同的tokenId对其进行唯一标识
	*/
	TokenId string `json:"tokenId"`

	/*
		用户的性别，可选值：
		女性：0
		男性：1
	*/
	Sex int `json:"sex,omitempty"`
	/*
		用户的年龄，可选值：
		青年（大约18-45岁）：0
		中年（大约45-60岁）：1
		老年（大于60岁）：2
	*/
	Age int `json:"age,omitempty"`

	/*
		用户角色
		对不同角色可配置不同策略。
		直播领域可取值：
		房管：ADMIN
		主播：HOST
		系统角色：SYSTEM
		游戏领域可取值：
		管理员：ADMIN
		普通用户：USER
		缺失或者默认普通用户：USER
	*/
	Role string
}

type ImgResponse struct {
	/*
		返回码，详见常见错误码
		除message和requestId之外的字段，只有当code为1100时才会存在
	*/
	Code int `json:"code"`
	/*
		返回码详情描述
	*/
	Message string `json:"message"`
	/*
		请求唯一标识
		后续可用于数据查询
	*/
	RequestId string `json:"requestId"`
	/*
		任务编号
		唯一标识该次图片审核任务
	*/
	TaskId string `json:"taskId"`
	/*
		风险分数
		取值范围[0,1000]，分数越高风险越大
	*/
	Score int `json:"score"`
	/*
		风险级别
		可能返回值：
		PASS：正常内容，建议直接放行
		REVIEW：可疑内容，建议人工审核
		REJECT：违规内容，建议直接拦截
	*/
	RiskLevel string `json:"riskLevel"`
	//风险详情
	Detail struct {
		Description   string `json:"description"`
		DescriptionV2 string `json:"descriptionV2"`
		Hits          []struct {
			/*
				拦截的风险原因解释
				仅供人了解风险原因时作为参考，程序请勿依赖该参数的值做逻辑处理
			*/
			Description string `json:"description"`
			/*
				新版策略规则风险原因描述
				注：该参数为新版API返回参数，过渡阶段只有新策略才会返回
			*/
			DescriptionV2 string `json:"descriptionV2"`
			/*
				策略规则标识
				用来标识命中的策略规则
				注：该参数为旧版API返回参数，兼容保留，后续版本会取消，请勿依赖此参数，仅供参考
			*/
			Model string `json:"model"`
			/*
				风险级别
				可能返回值：
				PASS：正常内容，建议直接放行
				REVIEW：可疑内容，建议人工审核
				REJECT：违规内容，建议直接拦截
			*/
			RiskLevel string `json:"riskLevel"`
			/*
				标识风险类型，可能取值
				正常：0
				涉政：100
				色情：200
				性感：210
				广告：300
				二维码：310
				水印：320
				暴恐：400
				违规：500
				不良场景 ：510
				黑名单：700
				白名单：710
				高危账号：800
				自定义：900
			*/
			RiskType int `json:"riskType"`
			Score    int `json:"score"`
		} `json:"hits"`

		/*
			策略规则标识
			用来标识命中的策略规则
			注：该参数为旧版API返回参数，兼容保留，后续版本会取消，请勿依赖此参数，仅供参考
		*/
		Model string `json:"model"`
		/*
			标识风险类型，可能取值
			正常：0
			涉政：100
			色情：200
			性感：210
			广告：300
			二维码：310
			水印：320
			暴恐：400
			违规：500
			不良场景 ：510
			黑名单：700
			白名单：710
			高危账号：800
			自定义：900
		*/
		RiskType int `json:"riskType"`
	} `json:"detail"`
	/*
		提示服务是否超时
		正常：0
		超时： 501
	*/
	Status int `json:"status"`
}

/*
	===============>音频<=================
*/

type AudioFileRequest struct {
	AccessKey interface{}           `json:"accessKey"`
	Type      string                `json:"type"`
	AppId     string                `json:"appId"`
	BtId      interface{}           `json:"btId"`
	Callback  string                `json:"callback"`
	Data      *AudioFileRequestData `json:"data"`
}

type AudioFileRequestData struct {
	Url     string `json:"url"`
	TokenId string `json:"tokenId"` //用户账号标识
}

type AudioFileResponse struct {
	Code      int    `json:"code"`
	Message   string `json:"message"`
	RequestId string `json:"requestId"`
	BtId      string `json:"btId"`
}

type AudioFileAyncRequest struct {
	AudioText     string `json:"audioText"`
	AudioTime     int    `json:"audioTime"`
	BtId          string `json:"btId"`
	CallbackParam struct {
	} `json:"callbackParam"`
	Code   int `json:"code"`
	Detail []struct {
		AudioEndtime   int    `json:"audioEndtime"`
		AudioModel     string `json:"audioModel"`
		AudioStarttime int    `json:"audioStarttime"`
		AudioText      string `json:"audioText"`
		AudioUrl       string `json:"audioUrl"`
		Description    string `json:"description"`
		RequestId      string `json:"requestId"`
		RiskLevel      string `json:"riskLevel"`
		RiskType       int    `json:"riskType"`
	} `json:"detail"`
	Labels    string `json:"labels"`
	Message   string `json:"message"`
	RequestId string `json:"requestId"`
	RiskLevel string `json:"riskLevel"`
	Tags      []struct {
		Confidence int    `json:"confidence"`
		Label      string `json:"label"`
	} `json:"tags"`
}

/*
	===============>视频<===============
*/

type VideoFileRequest struct {
	AccessKey string                `json:"accessKey"`
	ImgType   string                `json:"imgType"`
	AudioType string                `json:"audioType"`
	AppId     string                `json:"appId"`
	BtId      string                `json:"btId"`
	Callback  string                `json:"callback"`
	Data      *VideoFileRequestData `json:"data"`
}
type VideoFileRequestData struct {
	Url string `json:"url"`
}

type VideoFileResponse struct {
	Code      int    `json:"code"`
	Message   string `json:"message"`
	RequestId string `json:"requestId"`
	BtId      string `json:"btId"`
}

type VideoFileAyncRequest struct {
	Code      int    `json:"code"`
	Message   string `json:"message"`
	RequestId string `json:"requestId"`
	BtId      string `json:"btId"`
	Labels    string `json:"labels"`
	AuxInfo   struct {
		FrameCount int `json:"frameCount"`
		Time       int `json:"time"`
	} `json:"auxInfo"`
	RiskLevel string `json:"riskLevel"`
}

type AyncBaseRequest struct {
	Checksum string `json:"checksum"`
	Result   string `json:"result"`
}
