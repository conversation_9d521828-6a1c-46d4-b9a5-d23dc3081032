package etcdv3

import (
	"fmt"
	"time"

	clientv3 "go.etcd.io/etcd/client/v3"
)

var cli *clientv3.Client

type Config struct {
	EtcdAddrs   []string
	DialTimeout int
}

func InitEtcd(cfg *Config) (*clientv3.Client, error) {
	cli, err := clientv3.New(clientv3.Config{
		Endpoints:   cfg.EtcdAddrs,
		DialTimeout: time.Duration(cfg.DialTimeout) * time.Second,
	})

	if err != nil {
		return nil, err
	}

	return cli, err
}

func Close() {
	err := cli.Close()
	if err != nil {
		fmt.Printf("err")
	}
}
