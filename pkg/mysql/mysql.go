package mysql

import (
	"strconv"
	"time"

	"creativematrix.com/beyondreading/pkg/logger"
	"github.com/jmoiron/sqlx"

	_ "github.com/go-sql-driver/mysql"
)

type Config struct {
	OpenMax        int
	IdleMax        int
	LifeTime       int
	ShardTableSize int
	DSN            []string
}
type msshard struct {
	shardTableSize int
	list           []*sqlx.DB
}

type Mysqler interface {
	DB(id string) (*sqlx.DB, error)
	Table(id string, name string) string
	All() []*sqlx.DB
}

func connect(c *Config, dsn string) *sqlx.DB {
	db := sqlx.MustConnect("mysql", dsn)
	db.SetMaxOpenConns(c.OpenMax)
	db.SetMaxIdleConns(c.IdleMax)
	db.SetConnMaxLifetime(time.Second * time.Duration(c.LifeTime))

	return db
}

func New(c *Config) Mysqler {
	list := make([]*sqlx.DB, 0, len(c.<PERSON>))
	for _, d := range c.DSN {
		list = append(list, connect(c, d))
		logger.LogInfo("connect to mysql:", d)
	}

	if c.ShardTableSize == 0 {
		c.ShardTableSize = 1
	}

	return &msshard{list: list, shardTableSize: c.ShardTableSize}
}

func (s *msshard) DB(id string) (*sqlx.DB, error) {
	return s.list[0], nil
}

func (s *msshard) Table(id string, name string) string {
	key, _ := strconv.ParseInt(id[6:8], 16, 0)
	return name + "_" + strconv.Itoa(int(key)%s.shardTableSize)
}

func (s *msshard) All() []*sqlx.DB {
	return s.list
}

func Transact(db *sqlx.DB, txFunc func(*sqlx.Tx) error) (err error) {
	tx, err := db.Beginx()
	if err != nil {
		return
	}
	defer func() {
		if p := recover(); p != nil {
			tx.Rollback()
			panic(p)
		} else if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit()
		}
	}()
	err = txFunc(tx)
	return err
}
