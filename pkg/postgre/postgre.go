package postgre

import (
	"strconv"
	"time"

	"github.com/jmoiron/sqlx"
	_ "github.com/lib/pq"

	"creativematrix.com/beyondreading/pkg/logger"
)

type Config struct {
	OpenMax        int
	IdleMax        int
	MaxLifetime    int
	ShardTableSize int
	DSN            []string
}

type pgshard struct {
	shardTableSize int
	list           []*sqlx.DB
}

type Postgrer interface {
	DB(id string) (*sqlx.DB, error)
	Table(id string, name string) string
	All() []*sqlx.DB
}

func connect(c *Config, dsn string) *sqlx.DB {
	db := sqlx.MustConnect("postgres", dsn)
	db.SetMaxOpenConns(c.OpenMax)
	db.SetMaxIdleConns(c.IdleMax)
	db.SetConnMaxLifetime(time.Second * time.Duration(c.MaxLifetime))
	return db
}

func New(c *Config) Postgrer {
	list := make([]*sqlx.DB, 0, len(c.DSN))
	for _, d := range c.DSN {
		list = append(list, connect(c, d))
		logger.LogInfo("connect to postgre:", d)
	}

	if c.ShardTableSize == 0 {
		c.ShardTableSize = 1
	}

	return &pgshard{list: list, shardTableSize: c.ShardTableSize}
}

func (s *pgshard) DB(id string) (*sqlx.DB, error) {
	return s.list[0], nil
}

func (s *pgshard) Table(id string, name string) string {
	key, _ := strconv.ParseInt(id[6:8], 16, 0)
	return name + "_" + strconv.Itoa(int(key)%s.shardTableSize)
}

func (s *pgshard) All() []*sqlx.DB {
	return s.list
}

func Transact(db *sqlx.DB, txFunc func(*sqlx.Tx) error) (err error) {
	tx, err := db.Beginx()

	defer func() {
		if p := recover(); p != nil {
			tx.Rollback()
			panic(p)
		} else if err != nil {
			tx.Rollback()
		} else {
			err = tx.Commit()
		}
	}()
	err = txFunc(tx)
	return err
}
