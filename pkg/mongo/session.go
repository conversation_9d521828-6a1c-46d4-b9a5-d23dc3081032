package mongo

import (
	"context"
	"reflect"

	"creativematrix.com/beyondreading/pkg/ecode"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"
)

type Session struct {
	sessCtx    mongo.SessionContext
	session    mongo.Session
	collection *mongo.Database
}

type SessionConn struct {
	collection   *mongo.Collection
	SessionModel *Session
}

func (c *Connection) NewSession(ctx context.Context) (*Session, error) {
	//从主节点读取
	sessionOpt := options.Session().SetDefaultReadPreference(
		readpref.Primary())

	session, err := c.client.StartSession(sessionOpt)
	if err != nil {
		return nil, err
	}
	//defer session.EndSession(ctx)
	sessCtx := mongo.NewSessionContext(ctx, session)

	if err = session.StartTransaction(); err != nil {
		return nil, err
	}
	return &Session{
		sessCtx:    sessCtx,
		session:    session,
		collection: c.Db,
	}, nil
}

func (c *Connection) Transaction(ctx context.Context, fn func(sessCtx mongo.SessionContext) (interface{}, error),
	opts ...*options.TransactionOptions) (interface{}, error) {
	//从主节点读取

	sessionOpt := options.Session().SetDefaultReadPreference(
		readpref.Primary())

	session, err := c.client.StartSession(sessionOpt)
	if err != nil {
		return nil, err
	}

	return session.WithTransaction(ctx, fn)
}

func (s *Session) Table(name string, opts ...*options.CollectionOptions) *SessionConn {
	return &SessionConn{
		SessionModel: s,
		collection:   s.collection.Collection(name, opts...),
	}
}

func (s *Session) Rollback() error {
	return s.session.AbortTransaction(s.sessCtx)
}

func (s *Session) Commit() error {
	return s.session.CommitTransaction(s.sessCtx)
}

func (s *Session) Close() {
	s.session.EndSession(s.sessCtx)
}
func (m *SessionConn) FindOne(result, query interface{}, opts ...Opts) error {
	if m.collection == nil {
		return ecode.CollectionInvalid
	}

	ps := optsArgs(opts)

	err := m.collection.FindOne(m.SessionModel.sessCtx, query, &options.FindOneOptions{
		Sort:       ps.sort,
		Projection: ps.projection,
	}).Decode(result)

	if err == mongo.ErrNoDocuments {
		return ecode.NothingFound
	}
	return err
}

func (m *SessionConn) Find(result, query interface{}, opts ...Opts) error {
	if m.collection == nil {
		return ecode.CollectionInvalid
	}

	ps := optsArgs(opts)

	resultv := reflect.ValueOf(result)
	if resultv.Kind() != reflect.Ptr || resultv.Elem().Kind() != reflect.Slice {
		panic("result argument must be a slice address")
	}
	slicev := resultv.Elem()
	elemt := slicev.Type().Elem()

	size := int32(100)

	cursor, err := m.collection.Find(m.SessionModel.sessCtx, query, &options.FindOptions{
		BatchSize:  &size,
		Sort:       ps.sort,
		Skip:       ps.skip,
		Limit:      ps.limit,
		Projection: ps.projection,
	})
	if err != nil {
		return err
	}
	defer cursor.Close(m.SessionModel.sessCtx)

	for cursor.Next(m.SessionModel.sessCtx) {
		elemp := reflect.New(elemt)
		if err = cursor.Decode(elemp.Interface()); err != nil {
			return err
		}
		slicev = reflect.Append(slicev, elemp.Elem())
	}
	if err = cursor.Err(); err != nil {
		return err
	}

	resultv.Elem().Set(slicev)
	return nil
}

func (m *SessionConn) Insert(doc interface{}, opts ...Opts) (interface{}, error) {
	if m.collection == nil {
		return nil, ecode.CollectionInvalid
	}

	result, err := m.collection.InsertOne(m.SessionModel.sessCtx, doc)
	if err != nil {
		return nil, err
	}
	return result.InsertedID, nil
}

func (m *SessionConn) InsertMany(doc []interface{}, opts ...Opts) (interface{}, error) {
	if m.collection == nil {
		return nil, ecode.CollectionInvalid
	}

	result, err := m.collection.InsertMany(m.SessionModel.sessCtx, doc)
	if err != nil {
		return nil, err
	}
	return result.InsertedIDs, nil
}

func (m *SessionConn) Update(filter, update interface{}, opts ...Opts) (*mongo.UpdateResult, error) {
	if m.collection == nil {
		return nil, ecode.CollectionInvalid
	}

	ps := optsArgs(opts)
	if ps.multi {
		updateRes, err := m.collection.UpdateMany(m.SessionModel.sessCtx, filter, update, &options.UpdateOptions{Upsert: ps.upsert})
		return updateRes, err
	}

	updateRes, err := m.collection.UpdateOne(m.SessionModel.sessCtx, filter, update, &options.UpdateOptions{Upsert: ps.upsert})
	return updateRes, err
}

func (m *SessionConn) FindOneAndUpdate(filter, update interface{}, opts ...Opts) (*mongo.SingleResult, error) {
	if m.collection == nil {
		return nil, ecode.CollectionInvalid
	}
	ps := optsArgs(opts)
	singleResult := m.collection.FindOneAndUpdate(m.SessionModel.sessCtx, filter, update, &options.FindOneAndUpdateOptions{Upsert: ps.upsert})
	if singleResult.Err() != nil {
		return nil, singleResult.Err()
	}
	return singleResult, nil
}

func (m *SessionConn) Delete(filter interface{}, opts ...Opts) error {
	if m.collection == nil {
		return ecode.CollectionInvalid
	}

	_, err := m.collection.DeleteMany(m.SessionModel.sessCtx, filter)
	return err
}

func (m *SessionConn) Count(query interface{}, opts ...Opts) int64 {
	if m.collection == nil {
		return 0
	}
	count, _ := m.collection.CountDocuments(m.SessionModel.sessCtx, query)
	return count
}

func (m *SessionConn) Aggregate(pipeline interface{}, opts ...*options.AggregateOptions) (*mongo.Cursor, context.Context, error) {
	if m.collection == nil {
		return nil, nil, ecode.CollectionInvalid
	}
	cursor, err := m.collection.Aggregate(m.SessionModel.sessCtx, pipeline, opts...)
	return cursor, m.SessionModel.sessCtx, err
}

func (m *SessionConn) NewOrderedBufferedBulkInserter(docLimit int) *BufferedBulkInserter {
	return NewOrderedBufferedBulkInserter(m.collection, docLimit)
}

func (m *SessionConn) NewUnorderedBufferedBulkInserter(docLimit int) *BufferedBulkInserter {
	return NewUnorderedBufferedBulkInserter(m.collection, docLimit)
}

func (m *SessionConn) UpdateManyByIDs(docLimit int, ids []string, update bson.D) error {
	if m.collection == nil {
		return ecode.CollectionInvalid
	}

	bb := m.NewUnorderedBufferedBulkInserter(docLimit)
	for _, item := range ids {
		idObj, err := primitive.ObjectIDFromHex(item)
		if err != nil {
			continue
		}
		selector := bson.D{
			{Key: "_id", Value: idObj},
		}
		_, _ = bb.Update(selector, update)
	}
	_, _ = bb.Flush()
	return nil
}

func (m *SessionConn) RemoveManyByObjIDFiled(docLimit int, field string, objIDs []string) error {
	bb := m.NewUnorderedBufferedBulkInserter(300)
	for _, item := range objIDs {
		idObj, err := primitive.ObjectIDFromHex(item)
		if err != nil {
			continue
		}
		selector := bson.D{
			{Key: field, Value: idObj},
		}
		_, _ = bb.Delete(selector)
	}
	_, _ = bb.Flush()
	return nil
}

func (m *SessionConn) Bulk(models []mongo.WriteModel, opts ...*options.BulkWriteOptions) (*mongo.BulkWriteResult, error) {
	return m.collection.BulkWrite(m.SessionModel.sessCtx, models, opts...)
}
