package mongo

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/readpref"
	"go.mongodb.org/mongo-driver/x/mongo/driver/connstring"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type Connection struct {
	client *mongo.Client
	Db     *mongo.Database
}

func Connect(uri string) *Connection {
	fmt.Println("mongodb url:", uri)
	conn := &Connection{}

	cp, err := connstring.Parse(uri)
	if err != nil {
		panic(err)
	}
	dbName := cp.Database

	opt := options.Client()
	opt.ApplyURI(uri)
	opt.SetReadPreference(readpref.SecondaryPreferred())
	//opt.SetMonitor(&event.CommandMonitor{})
	//opt.SetPoolMonitor(&event.PoolMonitor{})

	client, err := mongo.Connect(context.Background(), opt)
	if err != nil {
		panic(fmt.Sprintf("mongodb %s connect failed: %v", uri, err))
	}
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	if err := client.Ping(ctx, readpref.Primary()); err != nil {
		panic(fmt.Sprintf("mongodb %s ping failed: %v", uri, err))
	}

	conn.client = client
	conn.Db = client.Database(dbName)
	return conn
}

func (c *Connection) Collection(name string, opts ...*options.CollectionOptions) *Collection {
	collection := c.Db.Collection(name, opts...)
	return &Collection{
		collection: collection,
	}
}

func (c *Connection) Model(name string) *Model {
	return &Model{
		collection: c.Db.Collection(name),
	}
}

func (c *Connection) HealthCheck() error {
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()
	if err := c.client.Ping(ctx, readpref.Primary()); err != nil {
		return err
	}
	return nil
}

type opts struct {
	sort       interface{}
	skip       *int64
	limit      *int64
	multi      bool
	upsert     *bool
	projection interface{}
	ctx        context.Context
	cancel     context.CancelFunc
}
type Opts func(*opts)

func optsArgs(arg []Opts) *opts {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	op := &opts{
		ctx:    ctx,
		cancel: cancel,
	}
	for _, a := range arg {
		a(op)
	}
	return op
}

func Context(ctx context.Context) Opts {
	return func(o *opts) {
		o.ctx = ctx
	}
}

func Sort(sort interface{}) Opts {
	return func(o *opts) {
		o.sort = sort
	}
}

func Skip(skip int64) Opts {
	return func(o *opts) {
		o.skip = &skip
	}
}

func Limit(limit int64) Opts {
	return func(o *opts) {
		o.limit = &limit
	}
}

func Multi() Opts {
	return func(o *opts) {
		o.multi = true
	}
}

func Upsert() Opts {
	return func(o *opts) {
		u := true
		o.upsert = &u
	}
}

func Projection(projection interface{}) Opts {
	return func(o *opts) {
		o.projection = projection
	}
}

func Select(fields []string) Opts {
	return func(o *opts) {
		pj := bson.M{}
		for _, f := range fields {
			pj[f] = true
		}
		o.projection = pj
	}
}

func Exclude(fields []string) Opts {
	return func(o *opts) {
		pj := bson.M{}
		for _, f := range fields {
			pj[f] = false
		}
		o.projection = pj
	}
}
