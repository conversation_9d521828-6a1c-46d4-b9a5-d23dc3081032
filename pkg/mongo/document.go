package mongo

import (
	"go.mongodb.org/mongo-driver/bson"
	"reflect"
	"strings"
)

func GetUpdateFields(doc interface{}) (*bson.M, error) {
	updateFields := bson.M{}

	// 使用反射来遍历结构体字段
	v := reflect.ValueOf(doc)
	t := v.Type()
	for i := 0; i < v.NumField(); i++ {
		field := t.Field(i)
		value := v.Field(i)

		// 跳过 Created 字段和零值字段
		if value.IsZero() {
			continue
		}

		// 使用 bson 标签名（如果存在）
		bsonNames := strings.Split(field.Tag.Get("bson"), ",")
		if len(bsonNames) > 1 && bsonNames[1] == "validInCondition" {
			continue
		}
		bsonName := bsonNames[0]

		if bsonName == "" || bsonName == "-" {
			bsonName = field.Name
		}

		updateFields[bsonName] = value.Interface()
	}

	return &updateFields, nil
}
