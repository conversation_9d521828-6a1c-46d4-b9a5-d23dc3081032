package mongo

import (
	"context"
	"reflect"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"creativematrix.com/beyondreading/pkg/ecode"
)

type Collection struct {
	collection *mongo.Collection
}

func (c *Collection) FindOne(result, query interface{}, opts ...Opts) error {
	if c.collection == nil {
		return ecode.CollectionInvalid
	}

	ps := optsArgs(opts)

	err := c.collection.FindOne(ps.ctx, query, &options.FindOneOptions{
		Sort:       ps.sort,
		Projection: ps.projection,
	}).Decode(result)

	if err == mongo.ErrNoDocuments {
		return ecode.NothingFound
	}
	return err
}

func (c *Collection) Find(result, query interface{}, opts ...Opts) error {
	var err error
	if c.collection == nil {
		return ecode.CollectionInvalid
	}

	ps := optsArgs(opts)

	resultv := reflect.ValueOf(result)
	if resultv.Kind() != reflect.Ptr || resultv.Elem().Kind() != reflect.Slice {
		panic("result argument must be a slice address")
	}
	slicev := resultv.Elem()
	elemt := slicev.Type().Elem()

	size := int32(100)

	cursor, err := c.collection.Find(ps.ctx, query, &options.FindOptions{
		BatchSize:  &size,
		Sort:       ps.sort,
		Skip:       ps.skip,
		Limit:      ps.limit,
		Projection: ps.projection,
	})
	if err != nil {
		return err
	}
	defer cursor.Close(ps.ctx)

	for cursor.Next(ps.ctx) {
		elemp := reflect.New(elemt)
		if err = cursor.Decode(elemp.Interface()); err != nil {
			return err
		}
		slicev = reflect.Append(slicev, elemp.Elem())
	}
	if err = cursor.Err(); err != nil {
		return err
	}

	resultv.Elem().Set(slicev)
	return nil
}

func (c *Collection) Cursor(query interface{}, opts ...Opts) (*mongo.Cursor, error) {
	var err error
	if c.collection == nil {
		return nil, ecode.CollectionInvalid
	}
	size := int32(100)
	ps := optsArgs(opts)
	//从主节点读取数据
	cursor, err := c.collection.Find(ps.ctx, query, &options.FindOptions{
		BatchSize:  &size,
		Sort:       ps.sort,
		Skip:       ps.skip,
		Limit:      ps.limit,
		Projection: ps.projection,
	})
	if err != nil {
		return nil, err
	}
	return cursor, nil
}

func (c *Collection) Insert(doc interface{}, opts ...Opts) (interface{}, error) {
	if c.collection == nil {
		return nil, ecode.CollectionInvalid
	}

	ps := optsArgs(opts)
	result, err := c.collection.InsertOne(ps.ctx, doc)
	if err != nil {
		return nil, err
	}
	return result.InsertedID, nil
}

func (c *Collection) InsertMany(doc []interface{}, opts ...Opts) (interface{}, error) {
	if c.collection == nil {
		return nil, ecode.CollectionInvalid
	}

	ps := optsArgs(opts)
	result, err := c.collection.InsertMany(ps.ctx, doc)
	if err != nil {
		return nil, err
	}
	return result.InsertedIDs, nil
}

func (c *Collection) Update(filter, update interface{}, opts ...Opts) (*mongo.UpdateResult, error) {
	if c.collection == nil {
		return nil, ecode.CollectionInvalid
	}

	ps := optsArgs(opts)

	if ps.multi {
		updateRes, err := c.collection.UpdateMany(ps.ctx, filter, update, &options.UpdateOptions{Upsert: ps.upsert})
		return updateRes, err
	}

	updateRes, err := c.collection.UpdateOne(ps.ctx, filter, update, &options.UpdateOptions{Upsert: ps.upsert})
	return updateRes, err
}

func (c *Collection) FindOneAndUpdate(filter, update interface{}, opts ...Opts) (*mongo.SingleResult, error) {
	if c.collection == nil {
		return nil, ecode.CollectionInvalid
	}
	ps := optsArgs(opts)

	singleResult := c.collection.FindOneAndUpdate(ps.ctx, filter, update, &options.FindOneAndUpdateOptions{Upsert: ps.upsert})
	if singleResult.Err() != nil {
		return nil, singleResult.Err()
	}
	return singleResult, nil
}

func (c *Collection) Delete(filter interface{}, opts ...Opts) error {
	if c.collection == nil {
		return ecode.CollectionInvalid
	}

	ps := optsArgs(opts)

	_, err := c.collection.DeleteMany(ps.ctx, filter)
	return err
}

func (c *Collection) Count(query interface{}, opts ...Opts) int64 {
	if c.collection == nil {
		return 0
	}
	ps := optsArgs(opts)
	count, _ := c.collection.CountDocuments(ps.ctx, query)
	return count
}

func (c *Collection) Aggregate(pipeline interface{}, opts ...*options.AggregateOptions) (*mongo.Cursor, context.Context, error) {
	if c.collection == nil {
		return nil, nil, ecode.CollectionInvalid
	}
	ps := optsArgs(nil)
	cursor, err := c.collection.Aggregate(ps.ctx, pipeline, opts...)
	return cursor, ps.ctx, err
}

func (c *Collection) NewOrderedBufferedBulkInserter(docLimit int) *BufferedBulkInserter {
	return NewOrderedBufferedBulkInserter(c.collection, docLimit)
}

func (c *Collection) NewUnorderedBufferedBulkInserter(docLimit int) *BufferedBulkInserter {
	return NewUnorderedBufferedBulkInserter(c.collection, docLimit)
}

func (c *Collection) UpdateManyByIDs(docLimit int, ids []string, update bson.D) error {
	if c.collection == nil {
		return ecode.CollectionInvalid
	}

	bb := c.NewUnorderedBufferedBulkInserter(docLimit)
	for _, item := range ids {
		idObj, err := primitive.ObjectIDFromHex(item)
		if err != nil {
			continue
		}
		selector := bson.D{
			{Key: "_id", Value: idObj},
		}
		_, _ = bb.Update(selector, update)
	}
	_, _ = bb.Flush()
	return nil
}

func (c *Collection) RemoveManyByObjIDFiled(docLimit int, field string, objIDs []string) error {
	bb := c.NewUnorderedBufferedBulkInserter(300)
	for _, item := range objIDs {
		idObj, err := primitive.ObjectIDFromHex(item)
		if err != nil {
			continue
		}
		selector := bson.D{
			{Key: field, Value: idObj},
		}
		_, _ = bb.Delete(selector)
	}
	_, _ = bb.Flush()
	return nil
}

func (c *Collection) Bulk(ctx context.Context, models []mongo.WriteModel, opts ...*options.BulkWriteOptions) (*mongo.BulkWriteResult, error) {
	return c.collection.BulkWrite(ctx, models, opts...)
}
