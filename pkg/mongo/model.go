package mongo

import (
	"context"
	"encoding/binary"
	"reflect"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"creativematrix.com/beyondreading/pkg/ecode"
)

type Model struct {
	collection *mongo.Collection
}

var (
	NoopModel = &Model{}
)

func (m *Model) FindOne(result, query interface{}, opts ...Opts) error {
	if m.collection == nil {
		return ecode.CollectionInvalid
	}

	ps := optsArgs(opts)

	err := m.collection.FindOne(ps.ctx, query, &options.FindOneOptions{
		Sort:       ps.sort,
		Projection: ps.projection,
	}).Decode(result)

	if err == mongo.ErrNoDocuments {
		return ecode.NothingFound
	}
	return err
}

func (m *Model) Find(result, query interface{}, opts ...Opts) error {
	var err error
	if m.collection == nil {
		return ecode.CollectionInvalid
	}

	ps := optsArgs(opts)

	resultv := reflect.ValueOf(result)
	if resultv.Kind() != reflect.Ptr || resultv.Elem().Kind() != reflect.Slice {
		panic("result argument must be a slice address")
	}
	slicev := resultv.Elem()
	elemt := slicev.Type().Elem()

	size := int32(100)

	cursor, err := m.collection.Find(ps.ctx, query, &options.FindOptions{
		BatchSize:  &size,
		Sort:       ps.sort,
		Skip:       ps.skip,
		Limit:      ps.limit,
		Projection: ps.projection,
	})
	if err != nil {
		return err
	}
	defer cursor.Close(ps.ctx)

	for cursor.Next(ps.ctx) {
		elemp := reflect.New(elemt)
		if err = cursor.Decode(elemp.Interface()); err != nil {
			return err
		}
		slicev = reflect.Append(slicev, elemp.Elem())
	}
	if err = cursor.Err(); err != nil {
		return err
	}

	resultv.Elem().Set(slicev)
	return nil
}

//func (m *Model) Cursor(query interface{}, opts ...Opts) (*mongo.Cursor, error) {
//	var err error
//	if m.collectionName == "" {
//		return nil, ecode.CollectionInvalid
//	}
//	if m.database == nil {
//		return nil, ecode.DatabaseInvalid
//	}
//	size := int32(100)
//	ps := optsArgs(opts)
//	//从主节点读取数据
//	cursor, err := m.database.Collection(m.collectionName, &options.CollectionOptions{
//		ReadPreference: readpref.Primary(),
//	}).Find(ps.ctx, query, &options.FindOptions{
//		BatchSize:  &size,
//		Sort:       ps.sort,
//		Skip:       ps.skip,
//		Limit:      ps.limit,
//		Projection: ps.projection,
//	})
//	if err != nil {
//		return nil, err
//	}
//	return cursor, nil
//}

func (m *Model) Insert(doc interface{}, opts ...Opts) (interface{}, error) {
	if m.collection == nil {
		return nil, ecode.CollectionInvalid
	}

	ps := optsArgs(opts)
	result, err := m.collection.InsertOne(ps.ctx, doc)
	if err != nil {
		if mgErr, ok := err.(mongo.WriteException); ok {
			if mgErr.WriteConcernError != nil {
				return nil, ecode.ContentErr
			}
			for _, wE := range mgErr.WriteErrors {
				if wE.Code == 11000 {
					return nil, ecode.UniqueErr
				}
			}
		}
		return nil, err
	}
	return result.InsertedID, nil
}

func (m *Model) InsertMany(doc []interface{}, opts ...Opts) (interface{}, error) {
	if m.collection == nil {
		return nil, ecode.CollectionInvalid
	}

	ps := optsArgs(opts)
	result, err := m.collection.InsertMany(ps.ctx, doc)
	if err != nil {
		return nil, err
	}
	return result.InsertedIDs, nil
}

func (m *Model) Update(filter, update interface{}, opts ...Opts) (*mongo.UpdateResult, error) {
	if m.collection == nil {
		return nil, ecode.CollectionInvalid
	}

	ps := optsArgs(opts)

	if ps.multi {
		updateRes, err := m.collection.UpdateMany(ps.ctx, filter, update, &options.UpdateOptions{Upsert: ps.upsert})
		return updateRes, err
	}

	updateRes, err := m.collection.UpdateOne(ps.ctx, filter, update, &options.UpdateOptions{Upsert: ps.upsert})
	return updateRes, err
}

func (m *Model) FindOneAndUpdate(filter, update interface{}, opts ...Opts) (*mongo.SingleResult, error) {
	if m.collection == nil {
		return nil, ecode.CollectionInvalid
	}
	ps := optsArgs(opts)

	singleResult := m.collection.FindOneAndUpdate(ps.ctx, filter, update, &options.FindOneAndUpdateOptions{Upsert: ps.upsert})
	if singleResult.Err() != nil {
		return nil, singleResult.Err()
	}
	return singleResult, nil
}

func (m *Model) Delete(filter interface{}, opts ...Opts) error {
	if m.collection == nil {
		return ecode.CollectionInvalid
	}

	ps := optsArgs(opts)

	_, err := m.collection.DeleteMany(ps.ctx, filter)
	return err
}

func (m *Model) Count(query interface{}, opts ...Opts) int64 {
	if m.collection == nil {
		return 0
	}
	ps := optsArgs(opts)
	count, _ := m.collection.CountDocuments(ps.ctx, query)
	return count
}

func (m *Model) Aggregate(pipeline interface{}, opts ...*options.AggregateOptions) (*mongo.Cursor, context.Context, error) {
	if m.collection == nil {
		return nil, nil, ecode.CollectionInvalid
	}
	ps := optsArgs(nil)
	cursor, err := m.collection.Aggregate(ps.ctx, pipeline, opts...)
	return cursor, ps.ctx, err
}

func (m *Model) NewOrderedBufferedBulkInserter(docLimit int) *BufferedBulkInserter {
	return NewOrderedBufferedBulkInserter(m.collection, docLimit)
}

func (m *Model) NewUnorderedBufferedBulkInserter(docLimit int) *BufferedBulkInserter {
	return NewUnorderedBufferedBulkInserter(m.collection, docLimit)
}

func (m *Model) UpdateManyByIDs(docLimit int, ids []string, update bson.D) error {
	if m.collection == nil {
		return ecode.CollectionInvalid
	}

	bb := m.NewUnorderedBufferedBulkInserter(docLimit)
	for _, item := range ids {
		idObj, err := primitive.ObjectIDFromHex(item)
		if err != nil {
			continue
		}
		selector := bson.D{
			{Key: "_id", Value: idObj},
		}
		_, _ = bb.Update(selector, update)
	}
	_, _ = bb.Flush()
	return nil
}

func (m *Model) RemoveManyByObjIDFiled(docLimit int, field string, objIDs []string) error {
	bb := m.NewUnorderedBufferedBulkInserter(300)
	for _, item := range objIDs {
		idObj, err := primitive.ObjectIDFromHex(item)
		if err != nil {
			continue
		}
		selector := bson.D{
			{Key: field, Value: idObj},
		}
		_, _ = bb.Delete(selector)
	}
	_, _ = bb.Flush()
	return nil
}

func ObjectIDToTime(id primitive.ObjectID) time.Time {
	secs := int64(binary.BigEndian.Uint32(id[0:4]))
	return time.Unix(secs, 0)
}

func (m *Model) Bulk(ctx context.Context, models []mongo.WriteModel, opts ...*options.BulkWriteOptions) (*mongo.BulkWriteResult, error) {
	return m.collection.BulkWrite(ctx, models, opts...)
}
