package elastic

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"

	"github.com/olivere/elastic/v7"

	"creativematrix.com/beyondreading/pkg/logger"
)

// Config is es.go config.
type Config struct {
	Es map[string][]string // ["esCluster1"]["addr1","addr2"]
	// EsHost   string
	Username string
	Password string
}

// Elastic .
type Elastic struct {
	C    *Config
	Pool map[string]*elastic.Client
}

// NewEsPool cluster
func NewEsPool(c *Config) (es *Elastic, err error) {
	es = &Elastic{
		C:    c,
		Pool: make(map[string]*elastic.Client),
	}

	for esName, e := range c.Es {
		client, err := elastic.NewClient(elastic.SetSniff(false), elastic.SetURL(e...), elastic.SetBasicAuth(c.Username, c.Password))
		if err != nil {
			return es, err
		}
		es.Pool[esName] = client
	}
	return
}

// Ping health of db.
func (e *Elastic) Ping(c context.Context) (err error) {
	if err = e.pingESCluster(c); err != nil {
		logger.LogErrorw("Ping err %v", err)
	}

	return
}

// pingESCluster ping es.go cluster
func (e *Elastic) pingESCluster(ctx context.Context) (err error) {
	for name := range e.C.Es {
		client, ok := e.Pool[name]
		if !ok {
			continue
		}
		for _, addr := range e.C.Es[name] {
			_, _, err = client.Ping(addr).Do(ctx)
			if err != nil {
				return
			}
		}
	}
	return
}

// Close all es.go instances.
func (e *Elastic) Close() {
	for name := range e.C.Es {
		client, ok := e.Pool[name]
		if !ok {
			continue
		}
		client.Stop()
	}
}

func (e *Elastic) Scan(data *elastic.SearchResult, array interface{}) error {
	arrayT := reflect.TypeOf(array)
	resultv := reflect.ValueOf(array)
	arrayV := resultv
	if resultv.Kind() != reflect.Ptr || resultv.Elem().Kind() != reflect.Slice {
		return fmt.Errorf("result argument must be a slice address")
	}
	arrayT = arrayT.Elem()
	arrayV = arrayV.Elem()

	innerObjT := arrayT.Elem()
	if innerObjT.Kind() == reflect.Ptr {
		innerObjT = innerObjT.Elem()
	}
	for _, hit := range data.Hits.Hits {
		row := reflect.New(innerObjT)
		v := row.Elem()
		if hit.Source == nil {
			arrayV = reflect.Append(arrayV, v)
			continue
		}
		if err := json.Unmarshal(hit.Source, v.Addr().Interface()); err == nil {
			arrayV = reflect.Append(arrayV, v)
		}
	}
	resultv.Elem().Set(arrayV)
	return nil
}
