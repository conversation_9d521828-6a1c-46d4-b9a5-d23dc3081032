package elastic

import (
	"context"
	"fmt"
	"testing"

	"github.com/olivere/elastic/v7"
)

func TestEs(t *testing.T) {
	config := &Config{
		Es: map[string][]string{
			"local": []string{
				"http://es-cn-i7m2e01bz000xh996.elasticsearch.aliyuncs.com:9200/",
			},
		},
		Username: "",
		Password: "",
	}
	es, err := NewEsPool(config)
	if err != nil {
		t.Log(err)
		t.FailNow()
	}
	if err := es.Ping(context.Background()); err != nil {
		t.Log(err)
		t.FailNow()
	}
	for _, client := range es.Pool {
		exists, err := client.IndexExists("custom").Do(context.Background())
		if err != nil {
			t.Log(err)
			t.FailNow()
		}
		if !exists {
			t.Log("index not exists")
		}
	}
}

func TestEsQuery(t *testing.T) {
	config := &Config{
		Es: map[string][]string{
			"local": []string{
				"http://192.168.3.62:9200",
			},
		},
		Username: "",
		Password: "",
	}
	es, err := NewEsPool(config)
	if err != nil {
		t.Log(err)
		t.FailNow()
	}

	client := es.Pool["local"]

	res, err := client.Search("usernew").Query(elastic.NewTermQuery("noId", "2021410")).Do(context.Background())
	if err != nil {
		fmt.Println(err.Error())
		return
	}
	fmt.Println(string(res.Hits.Hits[0].Source))
}
