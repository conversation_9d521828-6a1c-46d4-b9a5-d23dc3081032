package memory

import (
	"errors"
	"runtime"
	"sync"
	"time"
)

var (
	DefaultCache                    = NewCache()
	DefaultExpiration time.Duration = 0

	ErrItemExpired = errors.New("item has expired")
	ErrKeyNotFound = errors.New("key not found in memory")
)

type Cache struct {
	*memCache
}

type memCache struct {
	opts Options
	sync.RWMutex

	items map[string]Item

	janitor *janitor
}

type Item struct {
	Value      interface{}
	Expiration int64
}

func (i *Item) Expired() bool {
	if i.Expiration == 0 {
		return false
	}

	return time.Now().UnixNano() > i.Expiration
}

// Get The key is encrypted using md5
func (c *memCache) Get(key string) (interface{}, time.Time, error) {
	c.RWMutex.RLock()
	defer c.RWMutex.RUnlock()

	item, found := c.items[key]
	if !found {
		return nil, time.Time{}, ErrKeyNotFound
	}
	if item.Expired() {
		return nil, time.Time{}, ErrItemExpired
	}

	return item.Value, time.Unix(0, item.Expiration), nil
}

// Put The key is encrypted using md5
func (c *memCache) Put(key string, val interface{}, d time.Duration) error {
	var e int64
	if d == DefaultExpiration {
		d = c.opts.Expiration
	}
	if d > 0 {
		e = time.Now().Add(d).UnixNano()
	}

	c.RWMutex.Lock()
	defer c.RWMutex.Unlock()

	c.items[key] = Item{
		Value:      val,
		Expiration: e,
	}

	return nil
}

// Delete delete items with key.
func (c *memCache) Delete(key string) error {
	c.RWMutex.Lock()
	defer c.RWMutex.Unlock()

	_, found := c.items[key]
	if !found {
		return ErrKeyNotFound
	}

	c.delete(key)
	return nil
}

func (c *memCache) delete(key string) {
	delete(c.items, key)
}

// Flush delete all items from the memory.
func (c *memCache) Flush() {
	c.RWMutex.Lock()
	defer c.RWMutex.Unlock()
	c.items = map[string]Item{}
}

// delete all expired items from the memory.
func (c *memCache) deleteExpired() {
	c.RWMutex.Lock()
	defer c.RWMutex.Unlock()

	for k, v := range c.items {
		if v.Expired() {
			c.delete(k)
		}
	}
}

func NewCache(opts ...Option) *memCache {
	options := NewOptions(opts...)
	items := make(map[string]Item)

	if len(options.Items) > 0 {
		items = options.Items
	}
	return &memCache{
		opts:  options,
		items: items,
	}
}

// NewCacheWithClearExpire 初始化带自动清除过期数据缓存
// clearInterval: 自动清理过期数据的间隔时间 如：10*time.Minute 代表每隔10分钟会清除一次过期数据
func NewCacheWithClearExpire(clearInterval time.Duration, opts ...Option) *Cache {
	c := NewCache(opts...)

	C := &Cache{c}
	if clearInterval > 0 {
		runJanitor(c, clearInterval)
		runtime.SetFinalizer(C, stopJanitor)
	}

	return C
}
