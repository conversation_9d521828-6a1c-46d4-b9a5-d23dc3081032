package memory

import "time"

type janitor struct {
	interval time.Duration
	stop     chan bool
}

func (j *janitor) Run(c *memCache) {
	ticker := time.NewTicker(j.interval)
	for {
		select {
		case <-ticker.C:
			c.deleteExpired()
		case <-j.stop:
			ticker.Stop()
			return
		}
	}
}

func stopJanitor(c *Cache) {
	c.janitor.stop <- true
}

func runJanitor(c *memCache, ci time.Duration) {
	j := &janitor{
		interval: ci,
		stop:     make(chan bool),
	}
	c.janitor = j
	go j.Run(c)
}
