package memory

import (
	"testing"
	"time"

	"gotest.tools/assert"
)

var (
	key             = "test"
	val interface{} = "test-value"
)

func TestMemoryCache(t *testing.T) {
	t.Run("key not found", func(t *testing.T) {
		v, tt, err := DefaultCache.Get(key)
		assert.NilError(t, err)
		t.Log(v, tt)
	})

	t.Run("set no expire", func(t *testing.T) {
		var (
			err error
			c   = NewCache()
		)

		err = c.Put(key, val, 0)
		assert.NilError(t, err)

		v, tt, err := c.Get(key)
		assert.NilError(t, err)
		t.Log(v, tt)
	})

	t.Run("set with expire", func(t *testing.T) {
		var (
			err error
			c   = NewCache()
			d   = time.Second
		)

		err = c.Put(key, val, d)
		assert.NilError(t, err)

		<-time.After(2 * time.Second)

		v, tt, err := c.<PERSON>(key)
		assert.NilError(t, err)
		t.Log(v, tt)
	})

	t.Run("", func(t *testing.T) {
		var (
			err error
			d   = time.Second
			c   = NewCacheWithClearExpire(time.Second)
		)

		err = c.Put(key, val, d)
		assert.NilError(t, err)

		<-time.After(2 * time.Second)

		v, tt, err := c.Get(key)
		assert.NilError(t, err)
		t.Log(v, tt)
	})
}
