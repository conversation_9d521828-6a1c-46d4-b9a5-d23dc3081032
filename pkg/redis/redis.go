package redis

import (
	"context"
	"time"

	rds "github.com/gomodule/redigo/redis"
	"github.com/opentracing/opentracing-go"
	"github.com/opentracing/opentracing-go/ext"
)

type Redis interface {
	RDo(ctx context.Context, commandName string, args ...interface{}) (reply interface{}, err error)
	RClose() error
	RConn() rds.Conn
	RStats() rds.PoolStats

	Pipeline(ctx context.Context) Pipeline

	GetString(ctx context.Context, key string) (string, error)
	//批量获取多个字符串
	GetStrings(ctx context.Context, keys []interface{}) ([][]byte, error)
	Set(ctx context.Context, key string, val interface{}, expire int64) (interface{}, error)
	GetBit(ctx context.Context, key string, offset int) (bool, error)
	SetBit(ctx context.Context, key string, i int, offset int) (bool, error)
	SetLock(ctx context.Context, key string, value interface{}, TTL int64) (int64, error)
	TTL(ctx context.Context, key string) (int64, error)
	Expire(ctx context.Context, key string, TTL int64) (int64, error)
	ExistKey(ctx context.Context, key string) (bool, error)
	DelKey(ctx context.Context, key string) error
	DelKeys(ctx context.Context, keys []interface{}) error
	DecrKey(ctx context.Context, key string) error
	DecrByKey(ctx context.Context, key string, value int64) (int64, error)
	IncrKey(ctx context.Context, key string) error
	IncrByKey(ctx context.Context, key string, value int64) (total int64, err error)
	IncrByKeyV2(ctx context.Context, key string, value int64, TTL int64) (total int64, err error)
	AddList(ctx context.Context, key string, ids []int64, TTL int64) error
	Lpush(ctx context.Context, key string, vals interface{}, TTL int64) error
	Ltrim(ctx context.Context, key string, start, stop int64) error
	RPush(ctx context.Context, key string, vals interface{}, TTL int64) error
	RPopCount(ctx context.Context, key string, count int64) ([]string, error)
	PopList(ctx context.Context, key string) (int64, error)
	RPop(ctx context.Context, key string) (res string, err error)
	LPop(ctx context.Context, key string) (res string, err error)
	LLenList(ctx context.Context, key string) (int64, error)
	LRange(ctx context.Context, key string, start, end int64) ([]string, error)
	LIndex(ctx context.Context, key string, index int64) (string, error)
	LRem(ctx context.Context, key string, count int64, val interface{}) (int64, error)
	HSet(ctx context.Context, key, field, value string) error
	HSetnx(ctx context.Context, key, field, value string) error
	HSetNx(ctx context.Context, key, field, value string) (int64, error)
	HIncrBy(ctx context.Context, key, field string, amount int64) error
	HIncrByInt(ctx context.Context, key, field string, amount int64, TTL int64) (int64, error)
	HIncrByFloat(ctx context.Context, key, field string, amount float64, TTL int64) (float64, error)
	HIncrByExp(ctx context.Context, key, field string, amount int64, TTL int64) error
	HGet(ctx context.Context, key string, field string) (string, error)
	HDel(ctx context.Context, key, field string) error
	HMGet(ctx context.Context, key string, fields []string) ([]string, error)
	//删除hash
	//HDel(ctx context.Context, key string, fields []interface{}) (err error)
	HGetAll(ctx context.Context, key string) ([][]byte, error)
	HGetAllStrings(ctx context.Context, key string) (resp []string, err error)
	MultiHMGet(ctx context.Context, keys []string, nodes []string) (map[string][][]byte, error)
	MultiHGetAllPl(keys []string) (map[string][][]byte, error)
	MultiHMGetPipeline(keys []string, nodes []string) (map[string][][]byte, error)
	MultiZcardZsetPipeline(ctx context.Context, nodes []interface{}) ([]int64, error)
	MultiHMSet(ctx context.Context, data map[string]map[string][]byte) (bool, error)
	/*
		@HMSetTTL 新增Hash
		@Params:key Hash Key
		@Params:TTL 有效期  <=0 不设置有效期
		@Params:cols field value
	*/
	HMSetTTL(ctx context.Context, key string, TTL float64, cols ...interface{}) error
	/*
		@HMSet 设置指定字段Hash
		@Params:key Hash Key
		@Params:cols field value
	*/
	HMSet(ctx context.Context, key string, cols ...interface{}) error
	AddZSetElement(ctx context.Context, key string, member interface{}, scores float64, TTL int64) error
	AddsZSetElement(ctx context.Context, key string, member []interface{}, score []interface{}, TTL int64) (err error)
	GetZSetLength(ctx context.Context, key string) (int64, error)
	RemZSetElement(ctx context.Context, key string, member interface{}) error
	RemZSetElements(ctx context.Context, key string, members []string) error
	ZScore(ctx context.Context, key string, member interface{}) (float64, error)
	ZRank(ctx context.Context, key string, member interface{}) (int64, error)
	ZRevRank(ctx context.Context, key string, member interface{}) (rank int64, err error)
	ZRange(ctx context.Context, key string, start, end int64) (members []string, err error)
	ZRemRange(ctx context.Context, key string, start, end int64) (int, error)
	ZRangeWithScore(ctx context.Context, key string, start, end int64) ([]string, error)
	ZRevRangeUpToDown(ctx context.Context, key string, start, end int64) (members []string, err error)
	ZRangeByScore(ctx context.Context, key string, min, max, offset, count int64) ([]string, error)
	ZRevRangeWithScoreUpToDown(ctx context.Context, key string, start, end int64) (members []string, err error)
	ZIncrby(ctx context.Context, key string, member, increment interface{}, ttl int) (total float64, err error)
	SAdd(ctx context.Context, key string, member interface{}) error
	SPop(ctx context.Context, key string) (string, error)
	SAdds(ctx context.Context, key interface{}, member ...interface{}) error
	SRem(ctx context.Context, key string, members ...interface{}) error
	SGetStrings(ctx context.Context, key interface{}) (list []string, err error)
	SCard(ctx context.Context, key interface{}) (num int64, err error)
	ZCard(ctx context.Context, key interface{}) (num int64, err error)
	SMembers(ctx context.Context, key interface{}) (list [][]byte, err error)
	SisMember(ctx context.Context, key string, member interface{}) (bool, error)
	SRandMember(ctx context.Context, key string, count int64) (res []string, err error)
	GEOPOS(ctx context.Context, key string, member interface{}) ([]string, error)
	FuncString(ctx context.Context, key string, fu func() (string, error), expire int64) (string, error)
	FuncZRevRange(ctx context.Context, key string, start, end, ttl int64, fn func() ([]string, error)) ([]string, error)
}

type Pipeline interface {
	Send(command string, args ...interface{})
	Receive() (replies []interface{}, err error)
}

type Config struct {
	Address   string
	Password  string
	MaxIdle   int
	MaxActive int
}

type redis struct {
	pool *rds.Pool
}

func Load(cfg *Config) Redis {
	pool := &rds.Pool{
		MaxIdle:     cfg.MaxIdle,
		MaxActive:   cfg.MaxActive,
		IdleTimeout: 240 * time.Second,
		Dial: func() (rds.Conn, error) {
			return rds.Dial("tcp", cfg.Address, rds.DialPassword(cfg.Password))
		},
	}
	return &redis{pool}
}

func (rd *redis) RDo(ctx context.Context, commandName string, args ...interface{}) (reply interface{}, err error) {
	connect, err := rd.pool.GetContext(ctx)

	parentSpanContext := ctx.Value("ParentSpanContext")
	if parentSpanContext != nil {
		span := opentracing.StartSpan(
			commandName,
			opentracing.ChildOf(parentSpanContext.(opentracing.SpanContext)),
			opentracing.Tag{Key: string(ext.Component), Value: "net/http"},
		)

		defer span.Finish()
	}

	if err != nil {
		return nil, err
	}
	defer connect.Close()

	return connect.Do(commandName, args...)
}
func (rd *redis) RClose() error {
	return rd.pool.Close()
}

func (rd *redis) RConn() rds.Conn {
	return rd.pool.Get()
}

func (rd *redis) RStats() rds.PoolStats {
	return rd.pool.Stats()
}
