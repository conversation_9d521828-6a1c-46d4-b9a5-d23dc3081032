package redis

import (
	"context"
	"encoding/json"
	"fmt"

	rds "github.com/gomodule/redigo/redis"
)

type Operates interface {
}

func (rd *redis) GetString(ctx context.Context, key string) (string, error) {
	return rds.String(rd.RDo(ctx, "GET", key))
}

func (rd *redis) GetStrings(ctx context.Context, keys []interface{}) ([][]byte, error) {
	return rds.ByteSlices(rd.RDo(ctx, "MGET", keys...))
}
func (rd *redis) GetBit(ctx context.Context, key string, offset int) (bool, error) {
	return rds.Bool(rd.RDo(ctx, "GETBIT", key, offset))
}

func (rd *redis) SetBit(ctx context.Context, key string, i int, offset int) (bool, error) {
	return rds.Bool(rd.RDo(ctx, "SETBIT", key, i, offset))
}

func (rd *redis) Set(ctx context.Context, key string, val interface{}, expire int64) (interface{}, error) {
	var value interface{}
	switch v := val.(type) {
	case string, int, uint, int8, int16, int32, int64, float32, float64, bool:
		value = v
	default:
		b, err := json.Marshal(v)
		if err != nil {
			return nil, err
		}
		value = string(b)
	}
	if expire > 0 {
		value, err := rd.RDo(ctx, "SETEX", key, expire, value)
		return value, err
	} else {
		value, err := rd.RDo(ctx, "SET", key, value)
		return value, err
	}
}

func (rd *redis) SetLock(ctx context.Context, key string, value interface{}, TTL int64) (res int64, err error) {
	if res, err = rds.Int64(rd.RDo(ctx, "SETNX", key, value)); err != nil {
		return 0, err
	}

	if res == 1 && TTL > 0 {
		_, _ = rd.RDo(ctx, "EXPIRE", key, TTL)
	}

	return
}

func (rd *redis) TTL(ctx context.Context, key string) (int64, error) {
	return rds.Int64(rd.RDo(ctx, "TTL", key))
}

func (rd *redis) Expire(ctx context.Context, key string, timeout int64) (int64, error) {
	return rds.Int64(rd.RDo(ctx, "EXPIRE", key, timeout))
}

func (rd *redis) ExistKey(ctx context.Context, key string) (isExist bool, err error) {
	count, err := rds.Int64(rd.RDo(ctx, "EXISTS", key))
	if err != nil {
		return false, err
	}

	if count == 1 {
		isExist = true
	}

	return
}

func (rd *redis) DelKey(ctx context.Context, key string) error {
	if _, err := rd.RDo(ctx, "DEL", key); err != nil {
		return err
	}
	return nil
}

func (rd *redis) DelKeys(ctx context.Context, keys []interface{}) error {
	if _, err := rd.RDo(ctx, "DEL", keys...); err != nil {
		return err
	}

	return nil
}

func (rd *redis) DecrKey(ctx context.Context, key string) error {
	if _, err := rd.RDo(ctx, "DECR", key); err != nil {
		return err
	}

	return nil
}

func (rd *redis) DecrByKey(ctx context.Context, key string, value int64) (int64, error) {
	var (
		ret int64
		err error
	)

	if ret, err = rds.Int64(rd.RDo(ctx, "DECRBY", key, value)); err != nil {
		return 0, err
	}

	return ret, nil
}

func (rd *redis) IncrKey(ctx context.Context, key string) error {
	if _, err := rd.RDo(ctx, "INCR", key); err != nil {
		return err
	}

	return nil
}

func (rd *redis) IncrByKey(ctx context.Context, key string, value int64) (total int64, err error) {
	if total, err = rds.Int64(rd.RDo(ctx, "INCRBY", key, value)); err != nil {
		return 0, err
	}

	return total, nil
}

func (rd *redis) IncrByKeyV2(ctx context.Context, key string, value int64, TTL int64) (total int64, err error) {
	if total, err = rds.Int64(rd.RDo(ctx, "INCRBY", key, value)); err != nil {
		return 0, err
	}

	if TTL > 0 {
		_, _ = rd.RDo(ctx, "EXPIRE", key, TTL)
	}
	return total, nil
}

func (rd *redis) AddList(ctx context.Context, key string, ids []int64, TTL int64) error {
	var err error
	args := rds.Args{
		key,
	}
	for _, id := range ids {
		args = append(args, id)
	}
	if _, err = rd.RDo(ctx, "LPUSH", args...); err != nil {
		return err
	}

	if TTL > 0 {
		_, _ = rd.RDo(ctx, "EXPIRE", key, TTL)
	}

	return nil
}

func (rd *redis) RPush(ctx context.Context, key string, vals interface{}, TTL int64) error {
	var err error
	args := rds.Args{
		key,
	}
	switch v := vals.(type) {
	case []string:
		for _, id := range v {
			args = append(args, id)
		}
	case []int64:
		for _, id := range v {
			args = append(args, id)
		}
	case []int32:
		for _, id := range v {
			args = append(args, id)
		}
	case []interface{}:
		for _, id := range v {
			args = append(args, id)
		}
	default:
		return fmt.Errorf("invalid Type")
	}
	if _, err = rd.RDo(ctx, "RPUSH", args...); err != nil {
		return err
	}

	if TTL > 0 {
		_, _ = rd.RDo(ctx, "EXPIRE", key, TTL)
	}

	return nil
}

func (rd *redis) RPopCount(ctx context.Context, key string, count int64) ([]string, error) {
	args := rds.Args{
		key,
		count,
	}

	ret, err := rds.Strings(rd.RDo(ctx, "RPOP", args...))
	if err != nil {
		return nil, err
	}

	return ret, nil
}

func (rd *redis) Lpush(ctx context.Context, key string, vals interface{}, TTL int64) error {
	var err error
	args := rds.Args{
		key,
	}
	switch v := vals.(type) {
	case []string:
		for _, id := range v {
			args = append(args, id)
		}
	case []int64:
		for _, id := range v {
			args = append(args, id)
		}
	case []int32:
		for _, id := range v {
			args = append(args, id)
		}
	case []interface{}:
		for _, id := range v {
			args = append(args, id)
		}
	default:
		return fmt.Errorf("invalid Type")
	}
	if _, err = rd.RDo(ctx, "LPUSH", args...); err != nil {
		return err
	}

	if TTL > 0 {
		_, _ = rd.RDo(ctx, "EXPIRE", key, TTL)
	}

	return nil
}

func (rd *redis) Ltrim(ctx context.Context, key string, start, stop int64) error {
	_, err := rd.RDo(ctx, "LTRIM", key, start, stop)
	return err
}

func (rd *redis) PopList(ctx context.Context, key string) (res int64, err error) {
	if res, err = rds.Int64(rd.RDo(ctx, "RPOP", key)); err != nil {
		if err == rds.ErrNil {
			return 0, nil
		}
		return 0, err
	}

	return
}

func (rd *redis) RPop(ctx context.Context, key string) (res string, err error) {
	if res, err = rds.String(rd.RDo(ctx, "RPOP", key)); err != nil {
		if err == rds.ErrNil {
			return "", nil
		}
		return "", err
	}

	return
}

func (rd *redis) LPop(ctx context.Context, key string) (res string, err error) {
	if res, err = rds.String(rd.RDo(ctx, "LPOP", key)); err != nil {
		if err == rds.ErrNil {
			return "", nil
		}
		return "", err
	}

	return
}

func (rd *redis) LRange(ctx context.Context, key string, start, end int64) ([]string, error) {
	return rds.Strings(rd.RDo(ctx, "LRANGE", []interface{}{key, start, end}...))
}

func (rd *redis) LIndex(ctx context.Context, key string, index int64) (string, error) {
	return rds.String(rd.RDo(ctx, "LINDEX", key, index))
}

func (rd *redis) LRem(ctx context.Context, key string, count int64, val interface{}) (int64, error) {
	return rds.Int64(rd.RDo(ctx, "LREM", key, count, val))
}

func (rd *redis) LLenList(ctx context.Context, key string) (length int64, err error) {
	if length, err = rds.Int64(rd.RDo(ctx, "LLEN", key)); err != nil {
		return 0, err
	}
	return
}

func (rd *redis) MultiHGetAllPl(keys []string) (map[string][][]byte, error) {
	resp := make(map[string][][]byte)
	var (
		err   error
		reply [][]byte
	)
	connect := rd.RConn()
	defer connect.Close()
	for _, key := range keys {
		err = connect.Send("HGETALL", key)
		if err != nil {
			return nil, err
		}
	}
	err = connect.Flush()
	if err != nil {
		return nil, err
	}
	for _, key := range keys {
		if reply, err = rds.ByteSlices(connect.Receive()); err != nil {
			return nil, err
		}
		resp[key] = reply
	}
	return resp, nil
}

func (rd *redis) MultiHMGetPipeline(keys []string, nodes []string) (map[string][][]byte, error) {
	resp := make(map[string][][]byte)
	var (
		err   error
		reply [][]byte
	)
	connect := rd.RConn()
	defer connect.Close()
	for _, key := range keys {
		args := rds.Args{key}
		for _, v := range nodes {
			args = args.Add(v)
		}
		err = connect.Send("HMGET", args...)
		if err != nil {
			return nil, err
		}
	}
	err = connect.Flush()
	if err != nil {
		return nil, err
	}
	for _, key := range keys {
		if reply, err = rds.ByteSlices(connect.Receive()); err != nil {
			return nil, err
		}
		resp[key] = reply
	}
	return resp, nil
}

// 批量获取无序集合指定key的成员数量
func (rd *redis) MultiZcardZsetPipeline(ctx context.Context, nodes []interface{}) ([]int64, error) {
	resp := make([]int64, 0)
	var (
		err   error
		count int64
	)
	connect := rd.RConn()
	defer connect.Close()
	for _, node := range nodes {
		err = connect.Send("ZCARD", node)
		if err != nil {
			return nil, err
		}
	}
	err = connect.Flush()
	if err != nil {
		return nil, err
	}
	for i := 0; i < len(nodes); i++ {
		if count, err = rds.Int64(connect.Receive()); err != nil {
			return nil, err
		}
		resp = append(resp, count)
	}
	return resp, nil
}

func (rd *redis) HSet(ctx context.Context, key, field, value string) error {
	args := rds.Args{key, field, value}
	_, err := rd.RDo(ctx, "HSET", args...)
	return err
}

func (rd *redis) HSetnx(ctx context.Context, key, field, value string) error {
	args := rds.Args{key, field, value}
	_, err := rd.RDo(ctx, "HSETNX", args...)
	return err
}

func (rd *redis) HSetNx(ctx context.Context, key, field, value string) (int64, error) {
	args := rds.Args{key, field, value}
	rsp, err := rds.Int64(rd.RDo(ctx, "HSETNX", args...))
	return rsp, err
}

func (rd *redis) HIncrBy(ctx context.Context, key, field string, amount int64) error {
	args := rds.Args{key, field, amount}
	_, err := rd.RDo(ctx, "HINCRBY", args...)
	return err
}

func (rd *redis) HIncrByFloat(ctx context.Context, key, field string, amount float64, TTL int64) (float64, error) {
	args := rds.Args{key, field, amount}
	num, err := rds.Float64(rd.RDo(ctx, "HINCRBYFLOAT", args...))
	if err != nil {
		return 0, err
	}
	if TTL > 0 {
		_, err = rd.Expire(ctx, key, TTL)
		if err != nil {
			return 0, err
		}
	}
	return num, nil
}

func (rd *redis) HIncrByInt(ctx context.Context, key, field string, amount int64, TTL int64) (int64, error) {
	args := rds.Args{key, field, amount}
	num, err := rds.Int64(rd.RDo(ctx, "HINCRBY", args...))
	if err != nil {
		return 0, err
	}
	if TTL > 0 {
		_, err = rd.Expire(ctx, key, TTL)
		if err != nil {
			return 0, err
		}
	}
	return num, nil
}

func (rd *redis) HIncrByExp(ctx context.Context, key, field string, amount int64, TTL int64) error {
	args := rds.Args{key, field, amount}
	_, err := rd.RDo(ctx, "HINCRBY", args...)
	if err != nil {
		return err
	}
	if TTL > 0 {
		_, _ = rd.RDo(ctx, "EXPIRE", key, TTL)
	}
	return err
}

func (rd *redis) HDel(ctx context.Context, key, field string) error {
	args := rds.Args{key, field}
	_, err := rd.RDo(ctx, "HDEL", args...)
	return err
}

func (rd *redis) HGet(ctx context.Context, key string, field string) (string, error) {
	return rds.String(rd.RDo(ctx, "HGET", key, field))
}

func (rd *redis) HMGet(ctx context.Context, key string, fields []string) (resp []string, err error) {
	args := rds.Args{key}
	for _, v := range fields {
		args = args.Add(v)
	}
	resp, err = rds.Strings(rd.RDo(ctx, "HMGET", args...))
	return
}

func (rd *redis) HGetAll(ctx context.Context, key string) (resp [][]byte, err error) {
	args := rds.Args{key}

	if resp, err = rds.ByteSlices(rd.RDo(ctx, "HGETALL", args...)); err != nil {
		return nil, err
	}

	return resp, nil
}

func (rd *redis) HGetAllStrings(ctx context.Context, key string) (resp []string, err error) {
	args := rds.Args{key}

	if resp, err = rds.Strings(rd.RDo(ctx, "HGETALL", args...)); err != nil {
		return nil, err
	}

	return resp, nil
}

func (rd *redis) MultiHMGet(ctx context.Context, keys []string, nodes []string) (map[string][][]byte, error) {
	resp := make(map[string][][]byte)
	var (
		err   error
		reply [][]byte
	)

	for _, key := range keys {
		args := rds.Args{key}
		for _, v := range nodes {
			args = args.Add(v)
		}

		if reply, err = rds.ByteSlices(rd.RDo(ctx, "HMGET", args...)); err != nil {
			return nil, err
		}
		resp[key] = reply
	}
	return resp, nil
}

func (rd *redis) MultiHMSet(ctx context.Context, data map[string]map[string][]byte) (bool, error) {
	var (
		err error
	)

	for key, hMap := range data {
		args := rds.Args{key}
		for hKey, hVal := range hMap {
			args = args.Add(hKey, hVal)
		}
		if _, err = rd.RDo(ctx, "HMSET", args...); err != nil {
			return false, err
		}
	}

	return true, nil
}

func (rd *redis) HMSetTTL(ctx context.Context, key string, TTL float64, cols ...interface{}) error {
	var (
		err error
	)
	args := rds.Args{key}
	for i := 0; i < len(cols); i++ {
		if i%2 == 0 {
			//Bin modified to fix bug
			args = args.Add(cols[i], cols[i+1])
		}
	}
	_, err = rd.RDo(ctx, "HMSET", args...)
	if err != nil {
		return err
	}
	if TTL >= 0 {
		//Bin modified to fix TTL bug
		_, err = rd.RDo(ctx, "EXPIRE", key, TTL)
	}
	return err
}

func (rd *redis) HMSet(ctx context.Context, key string, cols ...interface{}) error {
	var (
		err error
	)
	args := rds.Args{key}
	for i := 0; i < len(cols); i++ {
		if i%2 == 0 {
			args = args.Add(cols[i])
			args = args.Add(cols[i+1])
		}
	}
	_, err = rd.RDo(ctx, "HMSET", args...)
	if err != nil {
		return err
	}
	return nil
}

func (rd *redis) AddZSetElement(ctx context.Context, key string, member interface{}, score float64, TTL int64) (err error) {
	if _, err = rd.RDo(ctx, "ZADD", key, score, member); err != nil {
		return err
	}

	if TTL > 0 {
		_, _ = rd.RDo(ctx, "EXPIRE", key, TTL)
	}

	return nil
}

// 批量插入有序集合
func (rd *redis) AddsZSetElement(ctx context.Context, key string, member []interface{}, score []interface{}, TTL int64) (err error) {
	args := rds.Args{key}
	for i := 0; i < len(member); i++ {
		args = args.Add(score[i], member[i])
	}
	if _, err = rd.RDo(ctx, "ZADD", args...); err != nil {
		return err
	}

	if TTL > 0 {
		_, _ = rd.RDo(ctx, "EXPIRE", key, TTL)
	}

	return nil
}

func (rd *redis) GetZSetLength(ctx context.Context, key string) (length int64, err error) {
	if length, err = rds.Int64(rd.RDo(ctx, "ZCARD", key)); err != nil {
		return 0, err
	}

	return
}

func (rd *redis) RemZSetElement(ctx context.Context, key string, member interface{}) (err error) {
	if _, err = rd.RDo(ctx, "ZREM", key, member); err != nil {
		return err
	}
	return nil
}

func (rd *redis) RemZSetElements(ctx context.Context, key string, members []string) (err error) {
	args := rds.Args{key}
	for _, v := range members {
		args = append(args, v)
	}
	if _, err = rd.RDo(ctx, "ZREM", args...); err != nil {
		return err
	}
	return nil
}

func (rd *redis) ZScore(ctx context.Context, key string, member interface{}) (score float64, err error) {
	if score, err = rds.Float64(rd.RDo(ctx, "ZSCORE", key, member)); err != nil {
		return 0, err
	}

	return
}

func (rd *redis) ZRank(ctx context.Context, key string, member interface{}) (rank int64, err error) {
	if rank, err = rds.Int64(rd.RDo(ctx, "ZRANK", key, member)); err != nil {
		return 0, err
	}

	return
}

func (rd *redis) ZRevRank(ctx context.Context, key string, member interface{}) (rank int64, err error) {
	if rank, err = rds.Int64(rd.RDo(ctx, "ZREVRANK", key, member)); err != nil {
		return 0, err
	}

	return
}

func (rd *redis) ZRangeByScore(ctx context.Context, key string, min, max, offset, count int64) (members []string, err error) {
	if members, err = rds.Strings(rd.RDo(ctx, "ZRANGEBYSCORE", key, min, max, "LIMIT", offset, count)); err != nil {
		return nil, err
	}

	return
}

// 从小到大
func (rd *redis) ZRangeWithScore(ctx context.Context, key string, start, end int64) (members []string, err error) {
	if members, err = rds.Strings(rd.RDo(ctx, "ZRANGE", key, start, end, "WITHSCORES")); err != nil {
		return nil, err
	}
	return
}

// 从小到大
func (rd *redis) ZRange(ctx context.Context, key string, start, end int64) (members []string, err error) {
	if members, err = rds.Strings(rd.RDo(ctx, "ZRANGE", key, start, end)); err != nil {
		return nil, err
	}
	return
}

func (rd *redis) ZRemRange(ctx context.Context, key string, start, end int64) (int, error) {
	return rds.Int(rd.RDo(ctx, "ZREMRANGEBYRANK", key, start, end))
}

// 从大到小
func (rd *redis) ZRevRangeWithScoreUpToDown(ctx context.Context, key string, start, end int64) (members []string, err error) {
	if members, err = rds.Strings(rd.RDo(ctx, "ZREVRANGE", key, start, end, "WITHSCORES")); err != nil {
		return nil, err
	}
	return
}

// ZRevRangeUpToDown 从大到小
func (rd *redis) ZRevRangeUpToDown(ctx context.Context, key string, start, end int64) (members []string, err error) {
	if members, err = rds.Strings(rd.RDo(ctx, "ZREVRANGE", key, start, end)); err != nil {
		return nil, err
	}
	return
}

func (rd *redis) ZIncrby(ctx context.Context, key string, member, increment interface{}, ttl int) (total float64, err error) {
	if total, err = rds.Float64(rd.RDo(ctx, "ZINCRBY", key, increment, member)); err != nil {
		return 0, err
	}

	if ttl > 0 {
		rd.RDo(ctx, "EXPIRE", key, ttl)
	}

	return total, nil
}

func (rd *redis) SAdd(ctx context.Context, key string, member interface{}) error {
	_, err := rd.RDo(ctx, "SADD", key, member)
	return err
}

func (rd *redis) SPop(ctx context.Context, key string) (string, error) {
	return rds.String(rd.RDo(ctx, "SPOP", key))
}

func (rd *redis) SRem(ctx context.Context, key string, members ...interface{}) error {
	args := rds.Args{key}
	for _, v := range members {
		args = append(args, v)
	}
	_, err := rd.RDo(ctx, "SREM", args...)
	return err
}

func (rd *redis) SAdds(ctx context.Context, key interface{}, member ...interface{}) error {
	_, err := rd.RDo(ctx, "SADD", append(append([]interface{}{}, key), member...)...)
	return err
}

func (rd *redis) SGetStrings(ctx context.Context, key interface{}) (list []string, err error) {
	list, err = rds.Strings(rd.RDo(ctx, "SMEMBERS", key))
	return
}

func (rd *redis) SCard(ctx context.Context, key interface{}) (num int64, err error) {
	num, err = rds.Int64(rd.RDo(ctx, "SCARD", key))
	return
}

func (rd *redis) ZCard(ctx context.Context, key interface{}) (num int64, err error) {
	num, err = rds.Int64(rd.RDo(ctx, "ZCARD", key))
	return
}

func (rd *redis) SMembers(ctx context.Context, key interface{}) (list [][]byte, err error) {
	list, err = rds.ByteSlices(rd.RDo(ctx, "SMEMBERS", key))
	return
}

func (rd *redis) SisMember(ctx context.Context, key string, member interface{}) (exists bool, err error) {
	res, err := rds.Int64(rd.RDo(ctx, "SISMEMBER", key, member))
	if err != nil {
		return false, err
	}

	return res == 1, nil
}

func (rd *redis) SRandMember(ctx context.Context, key string, count int64) (res []string, err error) {
	res, err = rds.Strings(rd.RDo(ctx, "SRANDMEMBER", key, count))
	if err != nil {
		return nil, err
	}

	if len(res) > 0 {
		return res, nil
	}

	return nil, nil
}

func (rd *redis) GEOPOS(ctx context.Context, key string, member interface{}) ([]string, error) {
	res, err := rds.Strings(rd.RDo(ctx, "GEOPOS", key, member))
	if err != nil {
		return nil, err
	}
	if len(res) > 0 {
		return res, nil
	}
	return nil, nil
}

type pipeline struct {
	conn rds.Conn
	num  int
}

func (r *redis) Pipeline(ctx context.Context) Pipeline {
	return &pipeline{
		conn: r.pool.Get(),
	}
}

func (p *pipeline) Send(command string, args ...interface{}) {
	p.conn.Send(command, args...)
	p.num++
}

func (p *pipeline) Receive() (replies []interface{}, err error) {
	defer p.conn.Close()

	if err = p.conn.Flush(); err != nil {
		return
	}

	for i := 0; i < p.num; i++ {
		reply, err := p.conn.Receive()
		if err != nil {
			return nil, err
		}
		replies = append(replies, reply)
	}
	return
}

func (rd *redis) FuncString(ctx context.Context, key string, fu func() (string, error), expire int64) (string, error) {
	v, err := rd.GetString(ctx, key)
	if err != nil && err != rds.ErrNil {
		return "", err
	}
	if v != "" {
		return v, nil
	}

	v, err = fu()
	if err != nil {
		return v, err
	}
	rd.Set(ctx, key, v, expire)
	return v, nil
}

func (rd *redis) FuncZRevRange(ctx context.Context, key string, start, end, ttl int64, fn func() ([]string, error)) ([]string, error) {
	ret, err := rd.ZRevRangeWithScoreUpToDown(ctx, key, start, end)
	if err != nil && err != rds.ErrNil {
		return nil, err
	}
	if len(ret) > 0 {
		return ret, nil
	}

	ret, err = fn()
	if err != nil {
		return ret, err
	}
	if len(ret) > 1 && len(ret)%2 == 0 {
		members := make([]interface{}, 0)
		scores := make([]interface{}, 0)
		for i := 0; i < len(ret); i += 2 {
			members = append(members, ret[i])
			scores = append(scores, ret[i+1])
		}
		rd.AddsZSetElement(ctx, key, members, scores, ttl)
		return ret, nil
	}

	return nil, nil
}
