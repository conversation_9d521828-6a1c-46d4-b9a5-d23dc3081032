package redis

import (
	"context"
	"fmt"
	"testing"
	"time"
)

func TestRedis_RDo(t *testing.T) {
	c := &Config{
		Address:   "127.0.0.1:6379",
		Password:  "123456",
		MaxIdle:   5,
		MaxActive: 5,
	}

	ctx := context.Background()
	rds := Load(c)

	res, err := rds.Set(ctx, "test:key:1", "hahaha", 10)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("[Set] res:", res)

	if res, err = rds.GetString(ctx, "test:key:1"); err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("[GetString] res:", res)

	args := []interface{}{
		"test:key:2",
		"123456",
	}
	if res, err = rds.RDo(ctx, "SETNX", args...); err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("[RDo] res:", res)
}

func TestRedis_List(t *testing.T) {
	c := &Config{
		Address:   "127.0.0.1:6379",
		Password:  "123456",
		MaxIdle:   5,
		MaxActive: 5,
	}

	var err error
	ctx := context.Background()
	rds := Load(c)
	key := "test:key:list:1"

	if err = rds.AddList(ctx, key, []int64{
		1,
		2,
	}, 100); err != nil {
		fmt.Println(err)
		return
	}

	length, err := rds.LLenList(ctx, key)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("[LLenList] len:", length)

	//pop,err := rds.PopList(ctx, key)
	//if err != nil {
	//	fmt.Println(err)
	//	return
	//}
	//fmt.Println("[LLenList] pop:", pop)
}

// type Student int

// func (s Student) Name() string {
// 	return "java.Student"
// }

// type Class struct {
// 	Level   int     `redis:"level"`
// 	Student Student `json:"student" redis:"student"`
// }

// func (c Class) Name() string {
// 	return "java.Class"
// }

func TestRedis_MGETArray(t *testing.T) {
	c := &Config{
		Address:   "127.0.0.1:6379",
		Password:  "123456",
		MaxIdle:   5,
		MaxActive: 5,
	}
	var err error

	ctx := context.Background()
	rds := Load(c)
	key := "test:class11111"

	ls, err := rds.HMGet(ctx, key, []string{"1"})
	if err != nil {
		fmt.Println("err get:", err.Error())
		return
	}
	fmt.Println("ls:", len(ls))
	fmt.Println("ls:", ls)
}

func TestRedis_LRangeList(t *testing.T) {
	c := &Config{
		Address:   "127.0.0.1:6379",
		Password:  "123456",
		MaxIdle:   5,
		MaxActive: 5,
	}
	var err error

	ctx := context.Background()
	rds := Load(c)
	key := "test1"

	res, err := rds.LRange(ctx, key, 0, -1)
	if err != nil {
		fmt.Println("err:", err)
		return
	}
	fmt.Println("res:", res)
	fmt.Println("res1:", len(res))
}

func TestRedis_SetLock(t *testing.T) {
	c := &Config{
		Address:   "127.0.0.1:6379",
		Password:  "123456",
		MaxIdle:   5,
		MaxActive: 5,
	}

	var err error
	ctx := context.Background()
	rds := Load(c)
	key := "alex"

	length, err := rds.SetLock(ctx, key, 0, 10)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("[GetZSetLength] len:", length)
}
func TestRedis_ZSET(t *testing.T) {
	c := &Config{
		Address:   "127.0.0.1:6379",
		Password:  "123456",
		MaxIdle:   5,
		MaxActive: 5,
	}

	var err error
	ctx := context.Background()
	rds := Load(c)
	key := "test:key:zset:1"

	if err = rds.AddZSetElement(ctx, key, "kobe", 123456, 10); err != nil {
		fmt.Println(err)
		return
	}

	length, err := rds.GetZSetLength(ctx, key)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println("[GetZSetLength] len:", length)
}

func TestRedis_HGetAll(t *testing.T) {
	c := &Config{
		Address:   "************:6381",
		Password:  "redis1234",
		MaxIdle:   5,
		MaxActive: 5,
	}

	ctx := context.Background()
	rds := Load(c)
	key := "\"gift:all:hash\""

	resp, err := rds.HGetAll(ctx, key)
	if err != nil {
		fmt.Println(err)
		return
	}

	for k, v := range resp {
		fmt.Println(k, string(v))
	}
}

func TestRedis_Adds(t *testing.T) {
	c := &Config{
		Address:   "127.0.0.1:6379",
		Password:  "123456",
		MaxIdle:   5,
		MaxActive: 5,
	}

	ctx := context.Background()
	rds := Load(c)
	m := []interface{}{"22", "33", "55"}
	err := rds.SAdds(ctx, "myset", m...)
	if err != nil {
		fmt.Println("err:", err.Error())
	}

}

func TestRedis_Hdel(t *testing.T) {
	c := &Config{
		Address:   "127.0.0.1:6379",
		Password:  "123456",
		MaxIdle:   5,
		MaxActive: 5,
	}
	ctx := context.Background()
	rds := Load(c)
	err := rds.HDel(ctx, "student", "name")
	if err != nil {
		fmt.Println("err:", err.Error())
	}

}

func TestRedis_SGet(t *testing.T) {
	c := &Config{
		Address:   "127.0.0.1:6379",
		Password:  "123456",
		MaxIdle:   5,
		MaxActive: 5,
	}
	ctx := context.Background()
	rds := Load(c)
	list, err := rds.SGetStrings(ctx, "student")
	if err != nil {
		fmt.Println("err:", err.Error())
		return
	}
	fmt.Println("list:", list)
	//for _,val:=range list{
	//	fmt.Printf("val:%s\n",val)
	//}

}

func TestRedis_MGet(t *testing.T) {
	c := &Config{
		Address:   "127.0.0.1:6379",
		Password:  "123456",
		MaxIdle:   5,
		MaxActive: 5,
	}
	ctx := context.Background()
	rds := Load(c)
	list, err := rds.GetStrings(ctx, []interface{}{"hello", "hello1", "1"})
	if err != nil {
		fmt.Println("err:", err.Error())
		return
	}
	fmt.Println("list:", list)
	for _, val := range list {
		fmt.Printf("val:%s\n", val)
	}

}

func TestRedis_HGetAllUser(t *testing.T) {
	c := &Config{
		Address:   "**************:6379",
		Password:  "123456",
		MaxIdle:   5,
		MaxActive: 5,
	}
	ctx := context.Background()
	rds := Load(c)
	list, err := rds.HGetAll(ctx, "\"user:user:userid:1082039\"")
	if err != nil {
		fmt.Println("err:", err.Error())
		return
	}
	//java.GetJsonFromJavaStr(string(list))
	for _, v := range list {
		//fmt.Println(string(java.GetJsonFromJavaStr(string(v))))
		fmt.Println(string(string(v)))
	}
	fmt.Println(time.Now().Unix())
	//for _,val:=range list{
	//	fmt.Printf("val:%s\n",val)
	//}

}

func TestRedis_ZScore(t *testing.T) {
	c := &Config{
		Address:   "************:6381",
		Password:  "redis1234",
		MaxIdle:   5,
		MaxActive: 5,
	}
	ctx := context.Background()
	rds := Load(c)
	score, err := rds.ZRangeWithScore(ctx, "\"gift:wall:anchor:stars\"", 0, 98)
	if err != nil {
		fmt.Println("err:", err.Error())
		return
	}

	fmt.Println("score:", score)
}

func TestMultiHMGetPipeline(t *testing.T) {
	c := &Config{
		Address:   "**************:7000",
		Password:  "redis1234",
		MaxIdle:   5,
		MaxActive: 5,
	}
	redisKey := []string{
		"\"user:user:userid:1\"",
	}
	//ctx := context.Background()
	rds := Load(c)
	data, err := rds.MultiHMGetPipeline(redisKey, []string{"\"id\""})
	if err != nil {
		fmt.Println("err:", err.Error())
	}
	fmt.Println("data:", data)
}

func TestMultiHIncr(t *testing.T) {
	c := &Config{
		Address:   "127.0.0.1:6379",
		Password:  "123456",
		MaxIdle:   5,
		MaxActive: 5,
	}

	ctx := context.Background()
	rds := Load(c)
	num, err := rds.HIncrByInt(ctx, "test", "age", 3, 0)
	if err != nil {
		fmt.Println("err:", err)
		return
	}
	fmt.Println("num:", num)
}

func TestZRevRange(t *testing.T) {
	c := &Config{
		Address:   "127.0.0.1:6379",
		Password:  "123456",
		MaxIdle:   5,
		MaxActive: 5,
	}

	ctx := context.Background()
	rds := Load(c)
	num, err := rds.ZRevRangeWithScoreUpToDown(ctx, "test", 0, 2)
	if err != nil {
		fmt.Println("err:", err)
		return
	}
	fmt.Println("num:", num)
}

func TestGetBit(t *testing.T) {
	c := &Config{
		Address:   "127.0.0.1:6379",
		Password:  "123456",
		MaxIdle:   5,
		MaxActive: 5,
	}

	ctx := context.Background()
	rds := Load(c)
	num, err := rds.GetBit(ctx, "test", 1)
	if err != nil {
		fmt.Println("err:", err)
		return
	}
	fmt.Println("num:", num)
}

func TestZsetCount(t *testing.T) {
	c := &Config{
		Address:   "127.0.0.1:6379",
		Password:  "123456",
		MaxIdle:   5,
		MaxActive: 5,
	}

	ctx := context.Background()
	rds := Load(c)
	nums, err := rds.MultiZcardZsetPipeline(ctx, []interface{}{"test", "test1", "test3"})
	if err != nil {
		fmt.Println("err:", err)
		return
	}
	fmt.Println("nums:", nums)
}

func TestZsetZadds(t *testing.T) {
	c := &Config{
		Address:   "127.0.0.1:6379",
		Password:  "123456",
		MaxIdle:   5,
		MaxActive: 5,
	}

	ctx := context.Background()
	rds := Load(c)
	err := rds.AddsZSetElement(ctx, "test", []interface{}{"alex", "owen"}, []interface{}{100, 200}, -1)
	if err != nil {
		fmt.Println("err:", err)
		return
	}

}

func TestZsetHMset(t *testing.T) {
	c := &Config{
		Address:   "**************:6379",
		Password:  "123456",
		MaxIdle:   5,
		MaxActive: 5,
	}

	ctx := context.Background()
	rds := Load(c)
	err := rds.HMSet(ctx, "test", "hello", "eowld")
	if err != nil {
		fmt.Println("err:", err)
		return
	}

}
func TestTTL(t *testing.T) {
	c := &Config{
		Address:   "**************:6379",
		Password:  "123456",
		MaxIdle:   5,
		MaxActive: 5,
	}

	ctx := context.Background()
	rds := Load(c)
	num, err := rds.TTL(ctx, "chat:intimacy:string:60111f62fcb34ba3f95e95031d1_888980012")
	if err != nil {
		fmt.Println("err:", err)
		return
	}
	fmt.Println("num:", num)
}

func TestRemZSetElement(t *testing.T) {
	c := &Config{
		Address:   "**************:6379",
		Password:  "123456",
		MaxIdle:   5,
		MaxActive: 5,
	}

	ctx := context.Background()
	rds := Load(c)
	err := rds.RemZSetElement(ctx, "pppppppppppp", "111")
	if err != nil {
		panic(err)
	}
}

func TestLIndex(t *testing.T) {
	c := &Config{
		Address:   "**************:6379",
		Password:  "123456",
		MaxIdle:   5,
		MaxActive: 5,
	}

	ctx := context.Background()
	rds := Load(c)

	//err := rds.Lpush(ctx, "index_teat", []interface{}{1, 2}, -1)
	//if err != nil {
	//	panic(err)
	//}

	row, err := rds.LIndex(ctx, "chat:accost:push:list:888980012", 10)
	if err != nil {
		panic(err)
	}
	fmt.Println("row:", row)
}
