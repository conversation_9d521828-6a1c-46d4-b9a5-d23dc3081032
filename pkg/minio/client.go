//Description minio文件服务器
//Date        2021/9/1
//User        cl

package minio

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"time"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
)

type Config struct {
	EndPoint  string
	Domain    string
	AccessKey string
	SecretKey string
	UseSSL    bool
}

type Minio struct {
	Client *minio.Client
}

func NewMinio(c *Config) (*Minio, error) {
	client, err := minio.New(c.EndPoint, &minio.Options{
		Creds:  credentials.NewStaticV4(c.<PERSON>, <PERSON><PERSON>, ""),
		Secure: c.UseSSL,
	})
	if err != nil {
		return nil, err
	}

	return &Minio{Client: client}, nil
}

func (m *Minio) MakeBucket(ctx context.Context, bucket string) error {
	location := "us-east-1"

	exists, err := m.Client.BucketExists(ctx, bucket)
	if err != nil {
		return err
	}
	if exists {
		return nil
	} else {
		err = m.Client.MakeBucket(ctx, bucket, minio.MakeBucketOptions{Region: location, ObjectLocking: false})
		if err != nil {
			return err
		}
	}

	return nil
}

func (m *Minio) UploadFileBytes(ctx context.Context, bucketName, objectName string, bts io.Reader, size int64) (minio.UploadInfo, error) {
	info, err := m.Client.PutObject(ctx, bucketName, objectName, bts, size, minio.PutObjectOptions{})
	return info, err
}

func (m *Minio) UploadFileBytesExpire(ctx context.Context, bucketName, objectName string, bts io.Reader, size int64, expire time.Duration) (minio.UploadInfo, error) {
	info, err := m.Client.PutObject(ctx, bucketName, objectName, bts, size, minio.PutObjectOptions{
		RetainUntilDate: time.Now().Add(expire),
	})
	return info, err
}

func (m *Minio) GetExpireFileUrl(ctx context.Context, bucketName, objectName string, expire time.Duration) (string, error) {
	ob, err := m.Client.PresignedGetObject(ctx, bucketName, objectName, expire, url.Values{})
	return fmt.Sprintf("%s://%s%s", ob.Scheme, ob.Host, ob.RequestURI()), err
}

func (m *Minio) UploadFile(ctx context.Context, bucket, filename, filepath string) error {
	b := make([]byte, 512)
	f, err := os.Open(filepath)
	if err != nil {
		return err
	}
	_, err = f.Read(b)
	if err != nil {
		return err
	}

	_, err = m.Client.FPutObject(ctx, bucket, filename, filepath, minio.PutObjectOptions{
		ContentType: http.DetectContentType(b),
	})
	if err != nil {
		return err
	}
	return nil
}

func (m *Minio) DownloadFile(ctx context.Context, bucket, filename, downloadPath string) error {
	return m.Client.FGetObject(ctx, bucket, filename, downloadPath, minio.GetObjectOptions{})
}
