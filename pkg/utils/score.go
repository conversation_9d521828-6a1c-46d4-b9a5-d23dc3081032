//Description 获取score区间
//Date        2021/8/12
//User        cl

package utils

import "fmt"

var Male = []int{-10, -9, -8, -6, -5, -4, -3, -1, 1, 3, 4, 5}
var FeMale = []int{-5, -4, -3, -1, 1, 3, 4, 5, 6, 8, 9, 10}

func GetScoreKey(target int, arr []int) string {
	if target == 0 {
		return "0"
	}

	if target < arr[0] {
		return ""
	}

	for i := 0; i < len(arr); i++ {
		if arr[i] == target {

			if i%2 == 0 {
				return fmt.Sprintf("%d,%d", arr[i], arr[i+1])
			} else {
				return fmt.Sprintf("%d,%d", arr[i-1], arr[i])
			}

		} else if arr[i] > target {
			return fmt.Sprintf("%d,%d", arr[i-1], arr[i])
		}
	}
	return ""
}
