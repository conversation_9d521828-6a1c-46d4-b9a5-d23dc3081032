package utils

import (
	"fmt"
	"net"
	"net/http"
	"os"
	"regexp"
)

// InternalIP get internal ip.
func InternalIP() string {
	if ip := os.Getenv("MY_POD_IP"); ip != "" {
		return ip
	}

	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return ""
	}
	for _, a := range addrs {
		if ipnet, ok := a.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipnet.IP.To4() != nil {
				return ipnet.IP.String()
			}
		}
	}
	return ""
}

func InternalWindowsIP() string {
	conn, error := net.Dial("udp", "*******:80")
	if error != nil {
		fmt.Println(error)

	}

	defer conn.Close()
	ipAddress := conn.LocalAddr().(*net.UDPAddr)
	//fmt.Println("get ip", ipAddress)

	return ipAddress.IP.String()
}

func ClientIP(r *http.Request) (sbIP string) {
	var ip string

	env := os.Getenv("SUMMER_ENV")
	if env != "production" {
		ip = r.Header.Get("x-forwarded-for") // 增加waf之后 ip被阿里云串改了
	} else {
		ip = r.Header.Get("x-original-forwarded-for")
	}

	if ip == "" {
		ip = r.RemoteAddr
	}
	re, _ := regexp.Compile(`(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d).(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d).(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d).(25[0-5]|2[0-4]\d|[0-1]\d{2}|[1-9]?\d)`)
	return re.FindString(ip)
}
