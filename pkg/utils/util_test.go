package utils

import (
	"fmt"
	"sort"
	"testing"
)

func TestRemoveRepeatedElementInt64(t *testing.T) {
	list := []int64{
		2, 33, 1, 2, 33, 44,
	}
	fmt.Println("list:", RemoveRepeatedElementInt64(list))
}

func TestRemoveRepeatedElementStr(t *testing.T) {
	list := []string{
		"2", "33", "1", "2", "33", "44",
	}
	//fmt.Println("list:",RemoveRepeatedElementStr(list))

	sort.Slice(list, func(i, j int) bool {
		return list[i] > list[j]
	})
	fmt.Println("list:", list)
}

func TestStripSlashes(t *testing.T) {
	a := "\\\"注册登录昵称签名关注回复\\\""
	fmt.Println(StripSlashes(a))
}

func TestStripSlashesPlus(t *testing.T) {
	s := `"{\"avtivityId\":0,\"createTime\":1548462177000,\"details\":\"1.普通用户可进行购买\\n2.商品服务包含进房座驾特效\\n3.商品的有效期为一个月\\n4.请保持座驾的有效期，若过座驾的有效期则不提供商品服务\\n5.只要成功购买即兑换即承诺遵守反广告机制，若昵称涉广告涉黄嫌疑，则我司有权暂停或永久拒绝对其提供本商品服务\\n\",\"feeData\":\"{\\\"buy1\\\":18800,\\\"buy3\\\":30800,\\\"buy6\\\":45800,\\\"renew1\\\":6800,\\\"renew3\\\":18800,\\\"renew6\\\":31800}\",\"id\":102,\"isRenew\":0,\"isSpecial\":0,\"modifyTime\":1548463573000,\"name\":\"保时捷\",\"sortId\":2,\"status\":0,\"type\":0,\"urlAnimation\":\"http://img.17kuxiu.com/gift/cartoon/cartoon20190126082248.svga\",\"urlIcon\":\"http://img.17kuxiu.com/gift/icon/icon20190126082244.png\"}"`
	fmt.Println(StripSlashesPlus(s))
}

func TestMd5String(t *testing.T) {
	s := "Q`W1E2R3T4Kuxiu2018~!@#$"
	fmt.Println(Md5String(s))
}

func TestRangeRandom(t *testing.T) {
	fmt.Println(RangeRandom(0, 2))
}

func TestAddSymbol(t *testing.T) {
	fmt.Println(AddSymbol(`{"name":"alex"}`))
}

func TestNewUnion(t *testing.T) {
	fmt.Println(NewUnion("888980012", "888980013"))
	fmt.Println(NewUnion("888980013", "888980012"))
}

func TestRandRangeNum(t *testing.T) {
	fmt.Println(RandRangeNum(10, 8))
}

func TestRandBorn(t *testing.T) {
	for i := 0; i < 100000; i++ {
		if GetAgeByDate(RandBorn()) < 18 {
			panic(GetAgeByDate(RandBorn()))
		}
	}
	//fmt.Println()
	//fmt.Println(RandWealth(15))
	//fmt.Println(mrand.Intn(1))
	//fmt.Println(GetWealthLv(174))
}

func TestImMd5(t *testing.T) {
	fmt.Println(ImMd5([]byte("1"), []byte("2"), []byte("3")))
}

func TestRelativePath(t *testing.T) {
	//fmt.Println(RelativePath("https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimg.zynews.cn%2Fattachement%2Fjpg%2Fsite2%2F20130809%2F00219761fd20136e981a1e.jpg&refer=http%3A%2F%2Fimg.zynews.cn&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=jpeg?sec=1630723183&t=0f7647305049b779be7c39aa63795beb"))
	//fmt.Println(AbPath("http://devimg.91quliao.com", "https://gimg2.baidu.com/image_search"))
	//fmt.Println(AbPath("http://devimg.91quliao.com", "image_search"))
	//fmt.Println(AbPath("http://devimg.91quliao.com", "/image_search"))
	//fmt.Println(RelativePath("chat/get"))

	fmt.Println(AbPath("http://devimg.91quliao.com", "http://devimg.91quliao11.com/chat/2021/10/4ec6628125feeb0d3846d4838431ea31.amr"))
}

func TestGetFamilyLv(t *testing.T) {
	fmt.Println(GetFamilyLv(10000))
}
