package utils

import (
	"errors"
	"fmt"
	"reflect"
	"strings"
)

/*
	根据结构体生成es script
*/
func EsScript(obj interface{}, cols ...string) (string, error) {
	if len(cols) == 0 {
		return "", fmt.<PERSON>rrorf("empty cols")
	}
	t := reflect.TypeOf(obj)
	v := reflect.ValueOf(obj)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
		v = v.Elem()
	}
	if t.Kind() != reflect.Struct {
		return "", fmt.Errorf("obj need struct")
	}
	colsMap := make(map[string]string)
	for _, c := range cols {
		colsMap[c] = c
	}

	resultStr := make([]string, 0)
	for i := 0; i < t.NumField(); i++ {
		t := t.Field(i).Tag.Get("json")
		if colsMap[t] != "" {
			resultStr = append(resultStr, fmt.Sprintf("ctx._source.%s =%v", t, v.Field(i).Interface()))
		}
	}
	return strings.Join(resultStr, ";"), nil
}

func RedisHashKV(obj interface{}, cols ...string) ([]interface{}, error) {
	t := reflect.TypeOf(obj)
	v := reflect.Indirect(reflect.ValueOf(obj))
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}
	if t.Kind() != reflect.Struct {
		return nil, fmt.Errorf("Invalid Object")
	}
	colMap := make(map[string]string)
	if len(cols) != 0 {
		for _, v := range cols {
			colMap[v] = v
		}
	}
	array := make([]interface{}, 0)
	for i := 0; i < t.NumField(); i++ {
		tag := t.Field(i).Tag.Get("redis")
		if tag != "" {
			if len(colMap) != 0 {
				if colMap[tag] == "" {
					continue
				}
			}
			array = append(array, v.Field(i).Interface())
		}
	}
	return array, nil
}

func CopyProperties(dst, src interface{}) (err error) {
	dstType, dstValue := reflect.TypeOf(dst), reflect.ValueOf(dst)
	srcType, srcValue := reflect.TypeOf(src), reflect.ValueOf(src)

	// dst必须结构体指针类型
	if dstType.Kind() != reflect.Ptr || dstType.Elem().Kind() != reflect.Struct {
		return errors.New("dst type should be a struct pointer")
	}

	// src必须为结构体或者结构体指针
	if srcType.Kind() == reflect.Ptr {
		srcType, srcValue = srcType.Elem(), srcValue.Elem()
	}
	if srcType.Kind() != reflect.Struct {
		return errors.New("src type should be a struct or a struct pointer")
	}

	// 取具体内容
	dstType, dstValue = dstType.Elem(), dstValue.Elem()

	// 属性个数
	propertyNums := dstType.NumField()

	scrTag := make(map[string]string)
	for i := 0; i < srcType.NumField(); i++ {
		filed := srcType.Field(i)
		scrTag[filed.Tag.Get("json")] = filed.Name
	}

	for i := 0; i < propertyNums; i++ {
		// 属性
		property := dstType.Field(i)
		tagValue := property.Tag.Get("json")
		// 待填充属性值
		propertyValue := srcValue.FieldByName(scrTag[tagValue])

		// 无效，说明src没有这个属性 || 属性同名但类型不同
		if !propertyValue.IsValid() || property.Type != propertyValue.Type() {
			continue
		}

		if dstValue.Field(i).CanSet() {
			dstValue.Field(i).Set(propertyValue)
		}
	}

	return nil
}
