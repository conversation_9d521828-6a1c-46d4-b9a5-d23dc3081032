package utils

import (
	"bufio"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"strings"
)

func ReadFile(filePath string) string {
	f, err := os.Open(filePath)
	if err != nil {
		fmt.Println(err)
		panic(err)
	}
	defer f.Close()

	fd, err := ioutil.ReadAll(f)
	if err != nil {
		panic(err)
	}

	return string(fd)
}

func ReadAllBytes(read io.Reader, size int) ([]byte, error) {
	bts := make([]byte, 4096)
	rs := make([]byte, size)
	bf := bufio.NewReader(read)
	var count int
	for {
		n, err := bf.Read(bts)
		if err == io.EOF {
			return rs, nil
		}
		if err != nil {
			return nil, err
		}
		if n == 0 {
			return rs, nil
		}
		for i := 0; i < n; i++ {
			rs[count] = bts[i]
			count++
		}
	}
}

func FileFormat(fileName string) string {
	return fileName[strings.LastIndex(fileName, ".")+1:]
}
