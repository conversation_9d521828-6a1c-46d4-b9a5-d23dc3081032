//Description 结构体转json
//Date 2021/4/20 15:59
//Author cl

package utils

import (
	"encoding/json"
	"strings"

	js "creativematrix.com/beyondreading/pkg/json"
)

func JsonString(v interface{}) string {
	content, err := js.JSON.MarshalToString(v)
	if err != nil {
		return ""
	}
	return content
}

func JsonByte(v interface{}) []byte {
	p, err := json.Marshal(v)
	if err != nil {
		return []byte{}
	}
	return p
}

// FormatJson 与现有redis存的json保持一致格式， json中的" 替换为 \", 首位加 "
func FormatJson(j string) string {
	if j == "" {
		return j
	}
	j = strings.ReplaceAll(j, `"`, `\"`)
	return "\"" + j + "\""
}

func JsonCopy(src interface{}, dst interface{}) error {
	p, err := json.Marshal(src)
	if err != nil {
		return err
	}
	return json.Unmarshal(p, dst)
}

func ParseData(data interface{}, dest interface{}) error {
	dataBytes, err := js.JSO<PERSON>.Marshal(data)
	if err != nil {
		return err
	}

	if err = js.JSON.Unmarshal(dataBytes, dest); err != nil {
		return err
	}

	return nil
}

func UnmarshalFromString(src string, dest interface{}) error {
	return js.JSON.UnmarshalFromString(src, &dest)
}
