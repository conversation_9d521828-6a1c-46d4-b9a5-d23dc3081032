package utils

import (
	"bytes"
	"crypto/hmac"
	"crypto/md5"
	"crypto/rand"
	"crypto/sha1"
	"encoding/binary"
	"encoding/gob"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"math/big"
	mrand "math/rand"
	"net/url"
	"path"
	"reflect"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"
	"unsafe"

	"go.mongodb.org/mongo-driver/bson"

	uuid "github.com/satori/go.uuid"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"creativematrix.com/beyondreading/pkg/logger"
)

const (
	A_YEAR_MINUTE   = 60 * 24 * 365
	A_MONTH_MINUTE  = 60 * 24 * 30
	A_WEEK_MINUTE   = 60 * 24 * 7
	A_DAY_MINUTE    = 60 * 24
	AN_HOUR_MINUTE  = 60
	A_SECOND_MILLIS = 1000
)

type (
	FamilyConf struct {
		Score     int64
		Member    int64 // 成员数量
		Assistant int64 // 副族长数量
		Elder     int64 // 长老数量
	}
)

func ObjectIDToTimeUnix(_id primitive.ObjectID) time.Time {
	secs := int64(binary.BigEndian.Uint32(_id[0:4]))
	return time.Unix(secs, 0)
}

// BinarySearch 查找整数在切片中命中范围的位置
func BinarySearch(exp int64, array []int64, beginIndex, endIndex int64) int64 {
	midIndex := beginIndex + (endIndex-beginIndex)/2
	if exp < array[beginIndex] {
		if beginIndex > 0 && exp >= array[beginIndex-1] {
			return beginIndex - 1
		}
		if beginIndex == 0 {
			return beginIndex
		}
		return -1
	}

	if exp >= array[endIndex] {
		if endIndex < int64(len(array)-1) && exp < array[endIndex+1] {
			return endIndex
		}
		if endIndex == int64(len(array)-1) {
			return endIndex
		}
		return -1
	}

	if midIndex <= beginIndex {
		return beginIndex
	}

	if exp > array[midIndex] {
		return BinarySearch(exp, array, midIndex+1, endIndex)
	} else {
		if exp == array[midIndex] {
			return midIndex
		}
		return BinarySearch(exp, array, beginIndex, midIndex-1)
	}
}

/*
单引号（'）、双引号（"）、反斜线（\）去转义
*/
func StripSlashes(source string) string {
	if source == "" {
		return source
	}

	target := strings.Replace(source, `\'`, `'`, -1)
	target = strings.Replace(target, `\"`, `"`, -1)
	target = strings.Replace(target, `\\`, `\`, -1)

	return target
}

func GenUUID() string {
	return uuid.Must(uuid.NewV4(), nil).String()
}

// Md5String 大写
func Md5String(value string) string {
	hexDigits := []string{"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C", "D", "E", "F"}
	data := []byte(value)
	h := md5.New()
	h.Write(data)
	mdByte := h.Sum(nil)
	str := make([]string, len(mdByte)*2)
	k := 0
	for i := 0; i < len(mdByte); i++ {
		temp := mdByte[i]
		str[k] = hexDigits[uint32(temp)>>4&0xf]
		k = k + 1
		str[k] = hexDigits[uint32(temp)&0xf]
		k = k + 1
	}
	return strings.Join(str, "")
}

func RemoveRepeatedElementInt64(array []int64) (arrayNew []int64) {
	cache := make(map[int64]int64)
	for _, v := range array {
		cache[v] = v
	}
	for _, v := range cache {
		arrayNew = append(arrayNew, v)
	}
	return
}

func RemoveRepeatedElementInt(array []int) (arrayNew []int) {
	cache := make(map[int]int)
	for _, v := range array {
		cache[v] = v
	}
	for _, v := range cache {
		arrayNew = append(arrayNew, v)
	}
	return
}

func RemoveRepeatedElementInt32(array []int32) (arrayNew []int32) {
	cache := make(map[int32]int32)
	for _, v := range array {
		cache[v] = v
	}
	for _, v := range cache {
		arrayNew = append(arrayNew, v)
	}
	return
}

func RemoveRepeatedElementStr(array []string) (arrayNew []string) {
	cache := make(map[string]string)
	for _, v := range array {
		cache[v] = v
	}
	for _, v := range cache {
		arrayNew = append(arrayNew, v)
	}
	return
}

func RemoveRepeatedElementBsonA(array bson.A) (arrayNew bson.A) {
	cache := make(map[interface{}]interface{})
	for _, v := range array {
		cache[v] = v
	}
	for _, v := range cache {
		arrayNew = append(arrayNew, v)
	}
	return
}

func RemoveRepeatedElementInterface(a interface{}) (ret []interface{}) {
	va := reflect.ValueOf(a)
	m := make(map[interface{}]interface{})
	for i := 0; i < va.Len(); i++ {
		m[va.Index(i).Interface()] = va.Index(i).Interface()
	}
	for _, v := range m {
		ret = append(ret, v)
	}
	return ret
}

// StripSlashesPlus 去掉前后冒号 / // //
func StripSlashesPlus(source string) string {
	if source == "" {
		return source
	}
	var target = strings.Trim(source, "\"")
	target = strings.ReplaceAll(target, `\"`, `"`)
	target = strings.ReplaceAll(target, `\\"`, `\"`)
	return target
}

func Colon(s string) string {
	return fmt.Sprintf("\"%s\"", s)
}

func AddSymbol(s string) string {
	s = strings.ReplaceAll(s, `"`, `\"`)
	return fmt.Sprintf("\"%s\"", s)
}

// DeepCopy 深拷贝
func DeepCopy(dst, src interface{}) {
	var buf bytes.Buffer
	gob.NewEncoder(&buf).Encode(src)
	gob.NewDecoder(bytes.NewBuffer(buf.Bytes())).Decode(dst)
}

// GetCurrMonth 获取当前年月
func GetCurrMonth() string {
	return time.Now().Format("200601")
}

// CheckMonth 是否是最近的三个月
func CheckMonth(month string) bool {
	monthM := make(map[string]string)

	now := time.Now()
	monthM[now.Format("200601")] = ""
	monthM[now.AddDate(0, -1, 0).Format("200601")] = ""
	monthM[now.AddDate(0, -2, 0).Format("200601")] = ""

	if _, ok := monthM[month]; ok {
		return true
	}

	return false
}

// WeekStart 获取本周第一天 yyyyMMdd
func WeekStart() string {
	now := time.Now()

	offset := int(time.Monday - now.Weekday())
	if offset > 0 {
		offset = -6
	}

	weekStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, time.Local).AddDate(0, 0, offset)

	return weekStart.Format("20060102")
}

// GenRandom 生成随机数
func GenRandom() int64 {
	return RangeRandom(0, math.MaxInt32)
}

// RangeRandom 生成区间[-m, n]的安全随机数
func RangeRandom(min, max int64) int64 {
	if min > max {
		panic("the min is greater than max!")
	}

	if min < 0 {
		f64Min := math.Abs(float64(min))
		i64Min := int64(f64Min)
		result, _ := rand.Int(rand.Reader, big.NewInt(max+1+i64Min))

		return result.Int64() - i64Min
	} else {
		result, _ := rand.Int(rand.Reader, big.NewInt(max-min+1))
		return min + result.Int64()
	}
}

// MongoProjection 获取mongo对象需要返回的字段
func MongoProjection(m interface{}) bson.M {
	t := reflect.TypeOf(m)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}

	col := make(bson.M)
	for i := 0; i < t.NumField(); i++ {
		tag := t.Field(i).Tag.Get("bson")
		if tag != "" {
			col[tag] = 1
		}
	}

	return col
}

func MongoColumns(m interface{}) []string {
	cols := make([]string, 0)
	t := reflect.TypeOf(m)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}

	for i := 0; i < t.NumField(); i++ {
		tag := t.Field(i).Tag.Get("bson")
		if tag != "" {
			cols = append(cols, tag)
		}
	}

	return cols
}

func GetStructTag(m interface{}, tagName string) bson.M {
	t := reflect.TypeOf(m)
	v := reflect.ValueOf(m)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
		v = v.Elem()
	}
	col := make(bson.M)
	for i := 0; i < t.NumField(); i++ {
		tag := t.Field(i).Tag.Get(tagName)
		if tag != "" && !v.Field(i).IsZero() {
			col[tag] = v.Field(i).Interface()
		}
	}
	return col
}

var WealthExp = []int64{
	0, 100, 1000, 2000, 3000, 4000, 5000, 6000, 8000, 10000, 15000, 20000, 25000, 30000, 40000, 50000, 60000, 80000,
	100000, 200000, 300000, 500000, 700000, 1000000, 1400000, 1800000, 2200000, 2600000, 3000000, 3600000, 4200000,
	4800000, 5400000, 6000000, 6800000, 7600000, 8400000, 9200000, 10000000, 11000000, 12000000, 13000000, 14000000,
	15000000, 16200000, 17400000, 18600000, 19800000, 21000000, 22500000, 24000000, 25500000, 27000000, 28500000,
	30500000, 32500000, 34500000, 36500000, 38500000, 41000000, 43500000, 46000000, 48500000, 51000000, 54000000,
	57000000, 60000000, 63000000, 66000000, 69500000, 73000000, 76500000, 80000000, 83500000, 87500000, 91500000,
	95500000, 99500000, 103500000, 108000000, 112500000, 117000000, 121500000, 126000000, 130500000, 135000000,
	139500000, 144000000, 148500000, 153500000, 158500000, 163500000, 168500000, 173500000, 178500000, 183500000,
	188500000, 193500000, 198500000, 208500000, 218500000,
	228500000, 238500000, 248500000, 258500000, 268500000, 278500000, 288500000, 298500000, 308500000, 318500000,
	331500000, 344500000, 357500000, 370500000, 383500000, 396500000, 409500000, 422500000, 435500000, 448500000,
	463500000, 478500000, 493500000, 508500000, 523500000, 538500000, 553500000, 568500000, 583500000, 598500000,
	615500000, 632500000, 649500000, 666500000, 683500000, 700500000, 717500000, 734500000, 751500000, 768500000,
	800000000, 820000000, 840000000, 860000000, 880000000, 900000000, 920000000, 940000000, 960000000, 980000000,
	1000000000, 1030000000, 1060000000, 1090000000, 1120000000, 1150000000, 1180000000, 1210000000, 1240000000, 1270000000,
	1310000000, 1350000000, 1390000000, 1430000000, 1470000000, 1510000000, 1550000000, 1590000000, 1630000000,
}

var CharmExp = []int64{
	0, 1000, 10000, 20000, 30000, 80000, 130000, 180000, 230000, 280000, 380000, 480000, 580000, 680000, 780000, 1280000,
	1780000, 2280000, 2780000, 3280000, 4280000, 5280000, 6280000, 7280000, 8280000, 13280000, 18280000, 23280000, 28280000,
	33280000, 43280000, 53280000, 63280000, 73280000, 83280000, 98280000, 113280000, 128280000, 143280000, 158280000, 178280000,
	198280000, 218280000, 238280000, 258280000, 283280000, 308280000, 333280000, 358280000, 383280000, 418280000, 453280000,
	488280000, 523280000, 558280000, 603280000, 648280000, 693280000, 738280000, 783280000, 838280000, 893280000, 948280000,
	1003280000, 1058280000, 1118280000, 1178280000, 1238280000, 1298280000, 1358280000, 1423280000, 1488280000, 1553280000,
	1618280000, 1683280000, 1753280000, 1823280000, 1893280000, 1963280000, 2033280000, 2108280000, 2183280000, 2258280000,
	2333280000, 2408280000, 2488280000, 2568280000, 2648280000, 2728280000, 2808280000, 2903280000, 2998280000, 3093280000,
	3188280000, 3283280000, 3383280000, 3483280000, 3583280000, 3683280000, 3783280000, 3983280000,
}

var FamilyLevel = []FamilyConf{
	{Score: 0, Member: 50, Assistant: 1, Elder: 2},            // Lv1
	{Score: 100000, Member: 100, Assistant: 1, Elder: 2},      // Lv2
	{Score: 1000000, Member: 150, Assistant: 2, Elder: 3},     // Lv3
	{Score: 4000000, Member: 200, Assistant: 2, Elder: 3},     // Lv4
	{Score: 10000000, Member: 240, Assistant: 3, Elder: 4},    // Lv5
	{Score: 20000000, Member: 280, Assistant: 3, Elder: 4},    // Lv6
	{Score: 50000000, Member: 320, Assistant: 3, Elder: 4},    // Lv7
	{Score: 100000000, Member: 360, Assistant: 4, Elder: 5},   // Lv8
	{Score: 200000000, Member: 400, Assistant: 4, Elder: 5},   // Lv9
	{Score: 360000000, Member: 440, Assistant: 4, Elder: 5},   // Lv10
	{Score: 600000000, Member: 480, Assistant: 5, Elder: 6},   // Lv11
	{Score: 1000000000, Member: 520, Assistant: 5, Elder: 6},  // Lv12
	{Score: 1600000000, Member: 550, Assistant: 5, Elder: 6},  // Lv13
	{Score: 2400000000, Member: 580, Assistant: 6, Elder: 7},  // Lv14
	{Score: 3600000000, Member: 620, Assistant: 6, Elder: 7},  // Lv15
	{Score: 5200000000, Member: 650, Assistant: 6, Elder: 7},  // Lv16
	{Score: 7300000000, Member: 680, Assistant: 7, Elder: 8},  // Lv17
	{Score: 10000000000, Member: 720, Assistant: 7, Elder: 8}, // Lv18
	{Score: 13140000000, Member: 760, Assistant: 7, Elder: 8}, // Lv19
	{Score: 21000000000, Member: 800, Assistant: 8, Elder: 9}, // Lv20
}

func GetFamilyLvConf(score int64) (lv, nl int64, cc, nc FamilyConf) {
	var exps []int64
	for _, v := range FamilyLevel {
		exps = append(exps, v.Score)
	}
	i := BinarySearch(score, exps, 0, int64(len(exps)-1))
	if int(i) == len(FamilyLevel)-1 {
		lv = i + 1
		nl = i + 1
		cc = FamilyLevel[i]
		nc = FamilyLevel[i]
		return
	}
	lv = i + 1
	nl = i + 2
	cc = FamilyLevel[i]
	nc = FamilyLevel[i+1]
	return
}

func GetFamilyLv(score int64) int64 {
	lv, _, _, _ := GetFamilyLvConf(score)
	return lv
}

// GetWealthLv 计算财富等级
func GetWealthLv(wealth int64) int64 {
	endIndex := int64(len(WealthExp) - 1)
	return BinarySearch(wealth, WealthExp, 0, endIndex)
}

// GetCharmLv 计算魅力等级
func GetCharmLv(charm int64) int64 {
	endIndex := int64(len(CharmExp) - 1)
	return BinarySearch(charm, CharmExp, 0, endIndex)
}

// FormatDistance 单位m，返回字符串
func FormatDistance(d float64) string {
	if d < 1000 {
		return "<1km"
	} else if d > 500000 {
		return ""
	} else {
		return fmt.Sprintf("%.1fkm", d/1000)
	}
}

const R = 6378137

func rad(d float64) float64 {
	return d * math.Pi / 180.0
}

func deg(d float64) float64 {
	return d * 180 / math.Pi
}

// GetLonLat 已知经纬度，角度，距离求 另一个经纬度
func GetLonLat(lng, lat, brng, distance float64) (float64, float64) {
	var a = 6378137.0
	var b = 6356752.3142
	var f = 1 / 298.257223563

	var lon1 = lng
	var lat1 = lat
	var s = distance * 1000
	var alpha1 = rad(brng)
	var sinAlpha1 = math.Sin(alpha1)
	var cosAlpha1 = math.Cos(alpha1)

	var tanU1 = (1 - f) * math.Tan(rad(lat1))
	cosU1 := 1 / math.Sqrt(1+tanU1*tanU1)
	sinU1 := tanU1 * cosU1
	var sigma1 = math.Atan2(tanU1, cosAlpha1)
	var sinAlpha = cosU1 * sinAlpha1
	var cosSqAlpha = 1 - sinAlpha*sinAlpha
	var uSq = cosSqAlpha * (a*a - b*b) / (b * b)
	var A = 1 + uSq/16384*(4096+uSq*(-768+uSq*(320-175*uSq)))
	var B = uSq / 1024 * (256 + uSq*(-128+uSq*(74-47*uSq)))

	var sigma = s / (b * A)
	sigmaP := 2 * math.Pi
	var sinSigma, cosSigma, cos2SigmaM, deltaSigma float64
	for math.Abs(sigma-sigmaP) > 1e-12 {
		cos2SigmaM = math.Cos(2*sigma1 + sigma)
		sinSigma = math.Sin(sigma)
		cosSigma = math.Cos(sigma)
		deltaSigma = B * sinSigma * (cos2SigmaM + B/4*(cosSigma*(-1+2*cos2SigmaM*cos2SigmaM)-
			B/6*cos2SigmaM*(-3+4*sinSigma*sinSigma)*(-3+4*cos2SigmaM*cos2SigmaM)))
		sigmaP = sigma
		sigma = s/(b*A) + deltaSigma
	}
	var tmp = sinU1*sinSigma - cosU1*cosSigma*cosAlpha1
	var lat2 = math.Atan2(sinU1*cosSigma+cosU1*sinSigma*cosAlpha1,
		(1-f)*math.Sqrt(sinAlpha*sinAlpha+tmp*tmp))
	var lambda = math.Atan2(sinSigma*sinAlpha1, cosU1*cosSigma-sinU1*sinSigma*cosAlpha1)
	var C = f / 16 * cosSqAlpha * (4 + f*(4-3*cosSqAlpha))
	var L = lambda - (1-C)*f*sinAlpha*
		(sigma+C*sinSigma*(cos2SigmaM+C*cosSigma*(-1+2*cos2SigmaM*cos2SigmaM)))

	return lon1 + deg(L), deg(lat2)
}

func GetDistance(latA, lonA, latB, lonB float64) float64 {
	var pk = 180 / math.Pi
	var a1 = latA / pk
	var a2 = lonA / pk
	var b1 = latB / pk
	var b2 = lonB / pk
	var t1 = math.Cos(a1) * math.Cos(a2) * math.Cos(b1) * math.Cos(b2)
	var t2 = math.Cos(a1) * math.Sin(a2) * math.Cos(b1) * math.Sin(b2)
	var t3 = math.Sin(a1) * math.Sin(b1)
	var tt = math.Acos(t1 + t2 + t3)
	return R * tt
}

var gap = []int{20, 19, 21, 20, 21, 22, 23, 23, 23, 24, 23, 22}
var star = []string{"摩羯座", "水瓶座", "双鱼座", "白羊座", "金牛座", "双子座", "巨蟹座", "狮子座", "处女座", "天秤座", "天蝎座", "射手座", "摩羯座"}

// GetStarSign 获取星座
func GetStarSign(t time.Time) string {
	month := t.Month()
	if t.Day() < gap[month-1] {
		return star[month-1]
	} else {
		return star[month%12]
	}
}

func Contains(slice []string, item string) bool {
	set := make(map[string]struct{}, len(slice))
	for _, s := range slice {
		set[s] = struct{}{}
	}
	_, ok := set[item]
	return ok
}

var idNumPattern = "^[1-9]\\d{7}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}$|^[1-9]\\d{5}[1-9]\\d{3}((0\\d)|(1[0-2]))(([0|1|2]\\d)|3[0-1])\\d{3}([0-9]|X)$"

// IdNumPattern 校验身份证号格式
func IdNumPattern(idNum string) bool {
	reg, _ := regexp.Compile(idNumPattern)
	if reg.MatchString(idNum) {
		//校验身份证号第18位
		idLen := len(idNum) - 1
		var count int
		for i := 0; i < idLen; i++ {
			a, _ := strconv.Atoi(string(idNum[i]))
			count += a * (int(math.Pow(2, float64(17-i))) % 11)
		}
		var idLast string
		switch count % 11 {
		case 0:
			idLast = "1"
		case 1:
			idLast = "0"
		case 2:
			idLast = "X"
		default:
			idLast = strconv.Itoa(12 - (count % 11))
		}
		return string(idNum[idLen]) == idLast
	}
	return false
}

// AgeByIDCard 根据身份证号返回age
func AgeByIDCard(idNum string) int {
	t := idNum[6:14]
	d, err := time.Parse("20060102", t)
	if err != nil {
		return 0
	}
	today := time.Now()

	age := today.Year() - d.Year()
	if today.Month() == d.Month() {
		if today.Day() < d.Day() {
			age--
		}
	} else if today.Month() < d.Month() {
		age--
	}

	return age
}

func NewUnion(from, to string) string {
	if from > to {
		return fmt.Sprintf("%s_%s", to, from)
	}
	return fmt.Sprintf("%s_%s", from, to)
}

func Uuid() string {
	return strings.Replace(uuid.NewV4().String(), "-", "", -1)
}

func RandNum(n int) int {
	switch n {
	case 0:
		return -1
	case 1:
		return 0
	default:
		return mrand.Intn(n)
	}
}

// num 数字  n 需要在范围内提取的数字数量
func RandRangeNum(num, n int) []int {
	var array []int
	if num <= n {
		for i := 0; i < num; i++ {
			array = append(array, i)
		}
		return array
	}

	randPool := make([]int, 0)
	for i := 0; i < num; i++ {
		randPool = append(randPool, i)
	}
	l := len(randPool)
	mrand.Seed(time.Now().Unix())
	for i := 0; i < n; i++ {
		index := mrand.Intn(l)
		array = append(array, randPool[index])
		l -= 1
		randPool = append(randPool[:index], randPool[index+1:]...)
	}

	return array
}

func HmacSha1(plainText, key string) []byte {
	hash := hmac.New(sha1.New, []byte(key))
	hash.Write([]byte(plainText))
	return hash.Sum(nil)
}

func Sha1(str string) string {
	hash := sha1.New()
	_, _ = io.WriteString(hash, str)

	return fmt.Sprintf("%x", hash.Sum(nil))
}

func Transform(src []byte, dst interface{}) error {
	return json.Unmarshal(src, dst)
}

func TraceTime(info string, t time.Time) {
	logger.LogInfof("%s执行消耗时间:%s", info, time.Since(t).String())
}

func GetAgeByDate(date string) int {
	t, err := time.Parse("2006-01-02", date)
	if err != nil {
		return 0
	}

	return GetAge(t.Year(), int(t.Month()), t.Day())
}

func GetAge(year, month, day int) int {
	now := time.Now()
	thisYear := now.Year()
	thisMonth := int(now.Month())
	today := now.Day()

	y := thisYear - year
	if month < thisMonth {
		return y
	} else if month > thisMonth {
		return y - 1
	} else {
		if day <= today {
			return y
		} else {
			return y - 1
		}
	}
}

func ValidTitle(title string, maxLen int) bool {
	reg, _ := regexp.Compile(fmt.Sprintf(`^[\p{Han}A-Za-z0-9_]{1,%d}$`, maxLen))
	return reg.MatchString(title)
}

// WeekByDate 判断时间是当年的第几周
func WeekByDate(day string) int64 {
	t, _ := time.ParseInLocation(dateformat, day, time.Local)
	yearDay := t.YearDay()
	yearFirstDay := t.AddDate(0, 0, -yearDay+1)
	firstDayInWeek := int(yearFirstDay.Weekday())

	//今年第一周有几天
	firstWeekDays := 1
	if firstDayInWeek != 0 {
		firstWeekDays = 7 - firstDayInWeek + 1
	}
	var week int
	if yearDay <= firstWeekDays {
		week = 1
	} else {
		mod := (yearDay - firstWeekDays) / 7
		div := (yearDay - firstWeekDays) % 7

		if div > 0 {
			week = mod + 2
		} else {
			week = mod + 1
		}
	}

	resStr := strconv.FormatInt(int64(t.Year()), 10) + strconv.FormatInt(int64(week), 10)
	res, _ := strconv.ParseInt(resStr, 10, 0)
	return res
}

// MonthByDate 获取年月
func MonthByDate(day string) int64 {
	t, _ := time.ParseInLocation(dateformat, day, time.Local)
	month, _ := strconv.ParseInt(t.Format("200601"), 10, 64)
	return month
}

// GenStringsInCond 生成mysql字符类型字段数组IN条件
func GenStringsInCond(source []string) string {
	target := make([]string, 0)
	for _, v := range source {
		target = append(target, "'"+v+"'")
	}

	return strings.Trim(strings.Join(strings.Fields(fmt.Sprint(target)), ","), "[]")
}

const dateformat = "20060102"

func FormatDay() string {
	return time.Now().Format(dateformat)
}

func FormatTimeDay(t time.Time) string {
	return t.Format(dateformat)
}

func RiskLevel(score int, isOld bool) string {
	if isOld {
		return oldRiskLevel(score)
	} else {
		return newRiskLevel(score)
	}
}

func oldRiskLevel(score int) string {
	if score < 20 {
		return "R"
	} else if score >= 20 && score < 40 {
		return "C"
	} else if score >= 40 && score < 65 {
		return "B"
	} else if score >= 65 && score < 85 {
		return "A"
	} else {
		return "S"
	}
}

func newRiskLevel(score int) string {
	if score < 20 {
		return "R"
	} else if score >= 20 && score < 40 {
		return "C"
	} else if score >= 40 && score < 60 {
		return "B"
	} else if score >= 60 && score < 80 {
		return "A"
	} else {
		return "S"
	}
}

func RandHeight(gender string) int {
	mrand.Seed(time.Now().UnixNano())

	if gender == "male" {
		return mrand.Intn(20) + 170
	} else {
		return mrand.Intn(20) + 150
	}
}

func RandWeight(gender string) int {
	mrand.Seed(time.Now().UnixNano())
	if gender == "male" {
		return mrand.Intn(20) + 65
	} else {
		return mrand.Intn(13) + 40
	}
}

func RandWealth(min, max int64) int64 {
	mrand.Seed(time.Now().UnixNano())

	return mrand.Int63n(max-min) + min
}

func WealthLevelToRandWealth(level int) int64 {
	mrand.Seed(time.Now().UnixNano())

	if level == 0 {
		return 0
	}

	min := WealthExp[level-1]
	max := WealthExp[level]

	return mrand.Int63n(max-min) + min
}

func RandBorn() string {
	mrand.Seed(time.Now().UnixNano())
	age := int64(mrand.Intn(12) + 19)
	n := time.Hour.Nanoseconds() * age * 24 * 365
	return time.Now().Add(-time.Duration(n)).Format("2006-01-02")
}

func AgeToBorn(age int) string {
	n := time.Hour.Nanoseconds() * int64(age) * 24 * 365

	return time.Now().Add(-time.Duration(n)).Format("2006-01-02")
}

func RandStarSign() string {
	mrand.Seed(time.Now().UnixNano())
	return star[mrand.Intn(len(star))]
}

var city = []string{"上海市", "北京市", "广州市", "深圳市", "天津市", "重庆市", "成都市", "武汉市"}

func RandCity() string {
	mrand.Seed(time.Now().UnixNano())
	return city[mrand.Intn(len(city))]
}

func RanomNum() int64 {
	mrand.Seed(time.Now().UnixNano())
	return mrand.Int63n(100000000)
}

func ImMd5(content, data, key []byte) string {
	m := md5.New()
	m.Write(content)
	m.Write(data)
	m.Write(key)
	return fmt.Sprintf("%x", m.Sum(nil))
}

func RspMd5(code, ts, data, msg, key []byte) string {
	m := md5.New()
	m.Write(code)
	m.Write(ts)
	m.Write(data)
	m.Write(msg)
	m.Write(key)
	return fmt.Sprintf("%x", m.Sum(nil))
}

func RelativePath(urlPath string) string {
	u, err := url.Parse(urlPath)
	if err != nil {
		return ""
	}

	relativePath := u.Path

	relativePath = strings.TrimPrefix(relativePath, "/")
	return relativePath
}

func AbPath(basePath string, urlPath string) (relativePath string, abPath string) {
	u, err := url.Parse(basePath)
	if err != nil {
		return "", ""
	}
	relativePath = RelativePath(urlPath)

	u.Path = path.Join(u.Path, RelativePath(urlPath))
	return relativePath, u.String()
}

func MapScan(obj interface{}, array *[]string) {
	if obj == nil {
		return
	}

	t := reflect.TypeOf(obj)
	v := reflect.ValueOf(obj)

	switch t.Kind() {
	case reflect.String:
		*array = append(*array, v.String())
	case reflect.Int, reflect.Int8, reflect.Int32, reflect.Int16, reflect.Int64:
		*array = append(*array, fmt.Sprintf("%d", v.Int()))
	case reflect.Float64, reflect.Float32:
		*array = append(*array, fmt.Sprintf("%.2f", v.Float()))
	case reflect.Bool:
		*array = append(*array, strconv.FormatBool(v.Bool()))
	case reflect.Map:
		for _, key := range v.MapKeys() {
			*array = append(*array, key.String())
			MapScan(v.MapIndex(key).Interface(), array)
		}
	case reflect.Slice:
		for i := 0; i < v.Len(); i++ {
			MapScan(v.Index(i).Interface(), array)
		}
	}
}

func ObjectToArray(obj interface{}) []string {
	array := make([]string, 0)
	data := StructToMap(obj)
	MapScan(data, &array)
	return array
}

func StructToMap(obj interface{}) map[string]interface{} {
	obj1 := reflect.TypeOf(obj)
	obj2 := reflect.ValueOf(obj)

	var data = make(map[string]interface{})
	for i := 0; i < obj1.NumField(); i++ {
		data[obj1.Field(i).Name] = obj2.Field(i).Interface()
	}
	return data
}

func ArrayToMd5(array []string, key string) string {
	str1 := strings.Join(array, "")

	array1 := strings.Split(str1, "")
	sort.Strings(array1)
	str := strings.Join(array1, "")

	m := md5.New()
	m.Write([]byte(str))
	m.Write([]byte(key))

	return fmt.Sprintf("%x", m.Sum(nil))
}

func RandNumStr(l int) string {
	numStr := ""
	for i := 0; i < l; {
		numStr += strconv.Itoa(mrand.Intn(9))
		i++
	}
	return numStr
}

func AmrToMp3(path string) string {
	if strings.HasSuffix(path, ".amr") {
		path = path[:len(path)-4] + ".mp3"
	}
	return path
}

func IsNum(s string) bool {
	_, err := strconv.ParseFloat(s, 64)
	return err == nil
}

func ConvertObjectIdsToString(ids []primitive.ObjectID) []string {

	strIds := make([]string, 0, len(ids))
	for _, id := range ids {
		strIds = append(strIds, id.Hex())
	}

	return strIds
}

func ConvertStringIdsToObject(ids []string) ([]primitive.ObjectID, error) {
	objectIds := make([]primitive.ObjectID, 0, len(ids))
	for _, id := range ids {
		objId, err := primitive.ObjectIDFromHex(id)
		if err != nil {
			return nil, err
		}
		objectIds = append(objectIds, objId)
	}
	return objectIds, nil
}

// ParseJSONTimestamp return 0 if err
func ParseJSONTimestamp(timestamp int64) *time.Time {
	if timestamp <= 0 {
		return nil
	}
	t := time.Unix(0, timestamp*int64(time.Millisecond))
	return &t
}

// FormatJSONTimestamp take care of error time format
func FormatJSONTimestamp(t *time.Time) int64 {
	if t == nil {
		return 0
	}
	timestamp := t.UnixNano() / int64(time.Millisecond)
	if timestamp < 0 {
		return 0
	}
	return timestamp
}

type JsTime time.Time

// UnmarshalJSON : UnmarshalJSON 从json->JSTime
func (j *JsTime) UnmarshalJSON(data []byte) error {
	t := parseJSONTime(string(data[1 : len(data)-1]))
	if t == nil {
		return nil
	}
	jtime := JsTime(*t)
	*j = jtime
	return nil
}
func (j *JsTime) ToJsTimeStamp() int64 {
	return FormatJSONTimestamp(j.ToStdTime())
}

// MarshalJSON : JSTime到 json
func (j JsTime) MarshalJSON() (data []byte, err error) {

	var buf bytes.Buffer
	t := time.Time(j)
	str := formatJSONTime(&t)
	buf.WriteRune('"')
	buf.WriteString(str)
	buf.WriteRune('"')
	return buf.Bytes(), nil
}

// ParseJSONTime return 1970-01-01T00:00:00.000Z if err
func parseJSONTime(str string) *time.Time {
	jsFormat := "2006-01-02T15:04:05.999Z"
	if str == "0001-01-01T00:00:00Z" {
		return nil
	}
	t, err := time.Parse(jsFormat, str)
	if err != nil {
		return nil
	}
	return &t
}

// FormatJSONTime take care of error time format
func formatJSONTime(t *time.Time) string {
	if t == nil {
		return ""
	}
	h, _ := time.ParseDuration("-1h")

	jsFormat := "2006-01-02T15:04:05.999Z"
	str := t.Add(8 * h).Format(jsFormat)
	if str == "0001-01-01T00:00:00Z" {
		return ""
	}
	return str
}

func ConvertToJSTime(t *time.Time) *JsTime {
	return (*JsTime)(unsafe.Pointer(t))
}

func (j *JsTime) ToStdTime() *time.Time {
	return (*time.Time)(unsafe.Pointer(j))
}

func GetDefaultTrue(b *bool) bool {
	if b == nil {
		return true
	}
	return *b
}

func GetWithDefaultInt64(num *int64, def int64) int64 {
	if num == nil {
		return def
	}
	return *num
}
func GetDefaultString(b *string, def string) string {
	if b == nil {
		return def
	}
	return *b
}

func GetNow() *time.Time {
	now := time.Now()
	return &now
}
