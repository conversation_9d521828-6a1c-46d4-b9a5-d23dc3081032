package utils

import (
	"strconv"
	"time"
)

const (
	MonthFormat = "200601"
	TimeFormat  = "2006-01-02 15:04:05"
	TimeFormat2 = "20060102"
	TimeFormat3 = "2006-01-02"
)

func TodayRemainSecond() int64 {
	todayLast := time.Now().Format("2006-01-02") + " 23:59:59"

	todayLastTime, _ := time.ParseInLocation("2006-01-02 15:04:05", todayLast, time.Local)

	return todayLastTime.Unix() - time.Now().Local().Unix()
}

func MidNight(t time.Time) time.Time {
	night := t.Format("2006-01-02") + " 23:59:59"
	nightTime, _ := time.ParseInLocation("2006-01-02 15:04:05", night, time.Local)
	return nightTime
}

func NexMonth(t time.Time) string {
	t = t.AddDate(0, 1, 0)
	return t.Format(MonthFormat)
}

func StrToTime(s, format string) time.Time {
	t, _ := time.Parse(format, s)
	return t
}

func FormatUnix(unix int64) string {
	var f string
	t := time.Unix(unix, 0)
	sec := t.Second()
	if sec > 0 {
		f = strconv.Itoa(sec) + "秒"
	}
	min := t.Minute()
	if min > 0 {
		f = strconv.Itoa(min) + "分" + f
	}
	hour := unix / 3600
	if hour > 0 {
		f = strconv.FormatInt(hour, 10) + "时" + f
	}
	return f
}

//GetFirstUnixOfMonth 获取传入月份(202109)的第一天0点的unix时间
func GetFirstUnixOfMonth(m string) int64 {
	t, _ := time.ParseInLocation(MonthFormat, m, time.Local)
	return GetZeroTime(t).Unix()
}

//GetLastUnixOfMonth 获取传入月份(202109)的最后一天23:59:59点的unix时间
func GetLastUnixOfMonth(m string) int64 {
	t, _ := time.ParseInLocation(MonthFormat, m, time.Local)
	return GetZeroTime(t.AddDate(0, 1, 0)).Unix() - 1
}

func GetZeroTime(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
}

func TodayRemainMilliSecond() int64 {
	d := time.Now()

	n := d.UnixNano() / 1e6
	t := GetZeroTime(d.AddDate(0, 0, 1)).UnixNano() / 1e6

	return t - n
}
