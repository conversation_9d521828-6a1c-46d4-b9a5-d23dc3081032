package utils

//import (
//	"fmt"
//	"testing"
//
//	es "creativematrix.com/beyondreading/app/api/search/model"
//)
//
//func TestEsScript(t *testing.T) {
//	user := es.EUser{
//		NickName: "alex",
//		//IsOpen:    false,
//		//AvatarUrl: "http://111.com",
//		//Type:      1,
//	}
//	doc, err := EsScript(&user, "nickName", "isOpen", "type")
//	if err != nil {
//		panic(err)
//	}
//	fmt.Println(doc)
//}
