package typeconvert

import (
	"reflect"
	"testing"
)

func TestToString(t *testing.T) {
	if r := ToString(1); r != "1" {
		t.<PERSON>al("should equal", r)
	}

	if r := ToString(3.14); r != "3.14" {
		t.<PERSON><PERSON>("should equal", r)
	}

	a1 := []string{"a", "b", "c"}
	if r := ToString(a1); r != "a,b,c" {
		t.<PERSON><PERSON>("should equal", r)
	}

	a2 := []int64{1, 2, 3}
	if r := ToString(a2); r != "1,2,3" {
		t.<PERSON><PERSON>("should equal", r)
	}

	a3 := []float64{3.14, 3.15}
	if r := ToString(a3); r != "3.14,3.15" {
		t.<PERSON><PERSON>("should equal", r)
	}

	var a4 []float64
	if r := ToString(a4); r != "" {
		t.Fatal("should equal", r)
	}
}

func TestToStringSlice(t *testing.T) {
	if r := ToStringSlice("a,b,c"); !reflect.DeepEqual(r, []string{"a", "b", "c"}) {
		t.Fatal("should equal", r)
	}

	var x []interface{}
	x = append(x, "a")
	x = append(x, 1)

	if r := ToStringSlice(x); !reflect.DeepEqual(r, []string{"a", "1"}) {
		t.Fatal("should equal", r)
	}
}
