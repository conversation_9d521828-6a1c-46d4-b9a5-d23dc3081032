package wangsu

import (
	"creativematrix.com/beyondreading/pkg/utils"
	"encoding/base64"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"

	"github.com/Wangsu-Cloud-Storage/wcs-go-sdk/src/lib/core"
	"github.com/Wangsu-Cloud-Storage/wcs-go-sdk/src/lib/utility"

	js "creativematrix.com/beyondreading/pkg/json"
	"creativematrix.com/beyondreading/pkg/typeconvert"
)

const (
	expire     = 3600
	returnBody = "$(url)"
)

type PutPolicy struct {
	Scope      string `json:"scope"`
	Deadline   string `json:"deadline"`
	ReturnBody string `json:"returnBody"`
}
type Config struct {
	AK        string
	SK        string
	UpDomain  string
	MgrDomain string
	Bucket    string
	LogBucket string
	EndPoint  string
	CdnDomain string
	PullUrl   []string
}

type Wangsu struct {
	Auth *utility.Auth
	Conf *Config
}

func NewWangsu(c *Config) *Wangsu {
	ws := &Wangsu{
		Conf: c,
	}
	ws.Auth = utility.NewAuth(c.AK, c.SK)

	return ws
}

func (w *Wangsu) GenUpToken(bucket string) string {
	policy := &PutPolicy{
		Scope:      bucket,
		Deadline:   typeconvert.Int64ToString(time.Now().Add(time.Second*expire).Unix() * 1000),
		ReturnBody: returnBody,
	}
	policyStr, _ := js.JSON.MarshalToString(&policy)

	return w.Auth.CreateUploadToken(policyStr)
}

func (w *Wangsu) SimpleUpload(filePath, fileName string) (fileUrl string, err error) {
	config := core.NewConfig(false, w.Conf.UpDomain, w.Conf.MgrDomain)

	policy := &PutPolicy{
		Scope:      w.Conf.Bucket,
		Deadline:   typeconvert.Int64ToString(time.Now().Add(time.Second*expire).Unix() * 1000),
		ReturnBody: returnBody,
	}
	policyStr, _ := js.JSON.MarshalToString(&policy)

	su := core.NewSimpleUpload(w.Auth, config, nil)
	res, err := su.UploadFile(filePath, policyStr, fileName, nil)
	if err != nil {
		fmt.Println(err)
		return "", err
	}

	body, _ := ioutil.ReadAll(res.Body)
	if http.StatusOK == res.StatusCode {
		dst, _ := base64.StdEncoding.DecodeString(string(body))
		fmt.Println(string(dst))
		return string(dst), nil
	}
	fmt.Println("Failed, StatusCode =", res.StatusCode)
	fmt.Println(string(body))
	return "", fmt.Errorf("FailedStatusCode =%d", res.StatusCode)

}

// FopsAudio 音频转码 key网宿相对路径 saveAs转码后存储相对路径/*
func (w *Wangsu) FopsAudio(key, saveAs string) error {
	config := core.NewConfig(false, w.Conf.UpDomain, w.Conf.MgrDomain)

	fops := "avthumb/mp3/acodec/libmp3lame/ab/64k|saveas/%s"
	query := fmt.Sprintf("bucket=%s&key=%s&fops=%s&force=1&separate=1",
		utils.Base64Str(w.Conf.Bucket),
		utils.Base64Str(key),
		utils.Base64Str(fmt.Sprintf(fops, utils.Base64Str(w.Conf.Bucket+":"+saveAs))),
	)
	response, err := core.FOps(w.Auth, config, nil, query)
	if nil != err {
		fmt.Println("FopsAudio failed:", err)
		return err
	}

	body, _ := ioutil.ReadAll(response.Body)
	if response.StatusCode != http.StatusOK {
		return fmt.Errorf(string(body))
	}

	return nil
}
