package wangsu

import (
	"fmt"
	"os"
	"path"
	"testing"
	"time"
)

var (
	/*ak = "WldrUfsKX8n2dFjMbqSNNtt5qLBbUQZ6Ovnv"
	sk = "HPsil0HyiTwSBmEOTPlfWfOt0kMDc8VsJTWImYOP5GoWI7LAb6T4g4zsjppsuB0u"
	upDomain = "zhuita01.up27.v1.wcsapi.com"
	mgrDomain = "zhuita01.mgr27.v1.wcsapi.com"
	bucket = "zhuitagolangdev"
	endPoint = "s3-cn-east-1.wcsapi.com"
	cdnDomain = "http://devimg.91quliao.com"*/

	//c = &Config{
	//	AK:        "WldrUfsKX8n2dFjMbqSNNtt5qLBbUQZ6Ovnv",
	//	SK:        "HPsil0HyiTwSBmEOTPlfWfOt0kMDc8VsJTWImYOP5GoWI7LAb6T4g4zsjppsuB0u",
	//	UpDomain:  "zhuita01.up27.v1.wcsapi.com",
	//	MgrDomain: "zhuita01.mgr27.v1.wcsapi.com",
	//	Bucket:    "zhuitagolangdev",
	//	EndPoint:  "s3-cn-east-1.wcsapi.com",
	//}

	//线上
	c = &Config{
		AK:        "WldrUfsKX8n2dFjMbqSNNtt5qLBbUQZ6Ovnv",
		SK:        "HPsil0HyiTwSBmEOTPlfWfOt0kMDc8VsJTWImYOP5GoWI7LAb6T4g4zsjppsuB0u",
		UpDomain:  "zhuita01.up27.v1.wcsapi.com",
		MgrDomain: "zhuita01.mgr27.v1.wcsapi.com",
		Bucket:    "zt-static2",
		EndPoint:  "s3-cn-east-1.wcsapi.com",
	}
)

func TestWangsu_GenUpToken(t *testing.T) {
	w := NewWangsu(c)
	t.Log(w.GenUpToken(c.Bucket))
}

func TestWangsu_SimpleUpload(t *testing.T) {
	w := NewWangsu(c)

	fileUrl, err := w.SimpleUpload("/Users/<USER>/Documents/20210608131250.jpg", "999.jpg")
	if err != nil {
		t.Fatal(err)
		return
	}
	t.Log(fileUrl)
}

func TestWangsu_Fops(t *testing.T) {
	w := NewWangsu(c)

	fmt.Println(w.FopsAudio("uservoice/2021/10/d114fa49cb00ed112137bc9b81dc7e97.amr", "hhh1.mp3"))
}

func TestWangsu_SimpleUploadMany(t *testing.T) {
	w := NewWangsu(c)

	date := time.Now().Format("20060102")
	//date = date + "6666" //1 女
	date = date + "7777" //1 男
	femaleStartNum := 0

	baseUrl := "/Users/<USER>/Desktop/男女图片/男"

	array := readDir(w, baseUrl, date, &femaleStartNum)

	fmt.Println("femaleStartNum", femaleStartNum)
	fmt.Println("===================================")
	fmt.Println("读取总图片数:", len(array))
	for index, v := range array {
		if index != 0 {
			fmt.Println(v)
		}
	}

}

func readDir(w *Wangsu, basePath string, date string, num *int) (array []string) {
	files, err := os.ReadDir(basePath)
	if err != nil {
		panic(err)
	}

	for _, v := range files {
		if v.IsDir() {
			fmt.Println("[Dir]:", path.Join(basePath, v.Name()))
			array = append(array, readDir(w, path.Join(basePath, v.Name()), date, num)...)
			continue
		}
		n := time.Now()

		*num += 1
		name := fmt.Sprintf("%s%d", date, *num)

		jpgPath := fmt.Sprintf("usericon/%d/%d/%s.jpg", n.Year(), n.Month(), name)
		//fmt.Println("[file]:", path.Join(basePath, v.Name()))
		fmt.Println(jpgPath)
		_, err = w.SimpleUpload(path.Join(basePath, v.Name()), jpgPath)
		if err != nil {
			panic(err)
		}
		//t.Log(fileUrl)
		array = append(array, jpgPath)
	}

	return array
}
