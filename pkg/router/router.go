package router

import (
	"bytes"
	"creativematrix.com/beyondreading/pkg/crypto"
	"creativematrix.com/beyondreading/pkg/ecode"
	"creativematrix.com/beyondreading/pkg/utils"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/ajg/form"
	"github.com/gin-gonic/gin"
	"io/ioutil"
	"net/http"
	"strings"
	"time"

	userpb "creativematrix.com/beyondreading/app/base/user/api"
	"creativematrix.com/beyondreading/pkg/config"
	mid "creativematrix.com/beyondreading/pkg/middlewares"
)

const (
	UserKey       = "userId"
	MsgId         = "msgId"
	NoId          = "noId"
	Gender        = "gender"
	Status        = "status"
	Platform      = "platform"
	Channel       = "channel"
	AppVersion    = "appVersion"
	ForbidVersion = "1.2.4"
	ParamLon      = "lon"
	ParamLat      = "lat"
	ParamLonType  = "type"
	SwitchLonLat  = "switch"
	NextErr       = "nextErr"
	TokenErr      = "-1"
	DeviceErr     = "-2"
	IPErr         = "-3"
	ReqId         = "reqId"
)

type Router struct {
	*gin.Engine
	userpb.UserClient
	conf config.Base
}

func Start(conf config.Base) *Router {

	user, err := userpb.NewClient(conf)
	if err != nil {
		panic(err)
	}

	gin.SetMode(gin.ReleaseMode)
	e := gin.New()
	// Bin modified temporary remove request validating
	//e.Use(mid.Cors(), mid.RequestValidate(), mid.Logger(), mid.Monitor(), mid.Tracing(), mid.Recovery())
	e.Use(mid.Cors(), mid.Logger(), mid.Monitor(), mid.Tracing(), mid.Recovery())

	e.Use(func(c *gin.Context) {
		c.Next()

		res := make(map[string]interface{})
		if err := c.Errors.Last(); err != nil {
			if c.Writer.Size() != 0 {
				return
			}
			status := http.StatusOK
			c.JSON(status, res)
		}
	})
	return &Router{Engine: e, UserClient: user, conf: conf}
}

func (r *Router) GetIds(c *gin.Context) (userId, noId, msgId, gender string) {
	token := c.GetHeader("token")

	if token == "" && c.Request.Method == "GET" { // get token from query
		token = c.Query("token")
	}

	if token == "" && c.Request.Method != "GET" { // get token from body
		var body []byte
		if c.Request.Body != nil {
			body, _ = ioutil.ReadAll(c.Request.Body)
		}
		c.Request.Body = ioutil.NopCloser(bytes.NewBuffer(body))

		var data map[string]interface{}
		if err := decodeBody(&data, body, c.ContentType()); err == nil {
			if t, ok := data["token"].(string); ok && token == "" {
				token = t
			}
		}
	}

	res, err := r.TokenToID(c, &userpb.TokenReq{Token: token})
	if err != nil {
		return
	}
	userId = res.Id

	// 解析noId，msgId
	noId, msgId, gender = decodeToken(r.conf.UserTokenSecret, token)

	return
}

func decodeToken(key, token string) (string, string, string) {
	decodeStr, err := crypto.Decrypt(token, []byte(key))
	if err != nil {
		fmt.Println(err)
		return "", "", ""
	}
	if decodeStr == "" {
		return "", "", ""
	}
	if strings.Contains(decodeStr, "_") { // 用户ID noId msgId gender以下划线隔开
		tokenArr := strings.Split(decodeStr, "_")
		if len(tokenArr) == 4 {
			return tokenArr[1], tokenArr[2], tokenArr[3]
		}
	}

	return "", "", ""
}

func decodeBody(v interface{}, body []byte, contentType string) error {
	if strings.Contains(contentType, "application/json") {
		return json.Unmarshal(body, v)
	} else if strings.Contains(contentType, "application/x-www-form-urlencoded") {
		return form.DecodeString(v, string(body))
	}
	return errors.New("error content type")
}

func (r *Router) AuthWithGuest(c *gin.Context) {
	userId, noId, msgId, gender := r.GetIds(c)
	if userId == "-1" || userId == "" || noId == "" || msgId == "" {
		return
	}

	c.Set(UserKey, userId)
	c.Set(NoId, noId)
	c.Set(MsgId, msgId)
	c.Set(Gender, gender)
}

func (r *Router) AuthUser(c *gin.Context) {
	userId, noId, msgId, gender := r.GetIds(c)
	if userId == "-1" {
		c.AbortWithStatusJSON(http.StatusOK, gin.H{
			"code": ecode.TOKENERROR.Code(),
			"msg":  ecode.TOKENMulti.Message(),
			"ts":   time.Now().UnixNano() / 1e6,
			"data": nil,
		})
		return
	}
	if userId == "" || noId == "" || msgId == "" {
		c.AbortWithStatusJSON(http.StatusOK, gin.H{
			"code": -11,
			"msg":  "TOKEN_ERROR",
			"ts":   time.Now().UnixNano() / 1e6,
			"data": nil,
		})
		return
	}

	c.Set(UserKey, userId)
	c.Set(NoId, noId)
	c.Set(MsgId, msgId)
	c.Set(Gender, gender)
}

func (r *Router) CheckGender(c *gin.Context) {
	gender := c.GetString(Gender)
	if gender == "" || gender == "unkown" { // 查询一次
		userId := c.GetString(UserKey)
		rsp, err := r.GetUsersByMongoIds(c, &userpb.MongoIdsReq{
			Ids:  []string{userId},
			Cols: []string{"gender"},
		})
		if err != nil {
			return
		}
		if rsp != nil && len(rsp.Users) == 1 {
			c.Set(Gender, rsp.Users[0].Gender)
		}
	}
}

func (r *Router) SetUser(c *gin.Context) {
	userId, noId, msgId, gender := r.GetIds(c)

	if userId == "" || noId == "" || msgId == "" {
		c.AbortWithStatusJSON(http.StatusOK, gin.H{
			"code": -11,
			"msg":  "TOKEN_ERROR",
			"ts":   time.Now().UnixNano() / 1e6,
			"data": nil,
		})
		return
	}
	c.Set(UserKey, userId)
	c.Set(NoId, noId)
	c.Set(MsgId, msgId)
	c.Set(Gender, gender)
}

// 获取用户 NoId MsgId
func (r *Router) UserIds(c *gin.Context) {
	userId := c.GetString(UserKey)
	rsp, err := r.GetUsersByMongoIds(c, &userpb.MongoIdsReq{
		Ids:  []string{userId},
		Cols: []string{"_id", "noId", "msgId"},
	})
	if err != nil {
		return
	}
	c.Set(MsgId, rsp.Users[0].MsgId)
	c.Set(NoId, rsp.Users[0].NoId)
}

func (r *Router) ValidHead(c *gin.Context) {
	var lon, lat float64
	var lonLatType string
	if c.Request.Method != "GET" {
		var body []byte
		if c.Request.Body != nil {
			body, _ = ioutil.ReadAll(c.Request.Body)
		}
		c.Request.Body = ioutil.NopCloser(bytes.NewBuffer(body))

		var data map[string]interface{}
		if err := decodeBody(&data, body, c.ContentType()); err == nil {
			if t, ok := data[ParamLonType].(string); ok {
				lonLatType = t
			}
			if t, ok := data[ParamLon].(float64); ok {
				lon = t
			}
			if t, ok := data[ParamLat].(float64); ok {
				lat = t
			}
		}
	}
	if SwitchLonLat == lonLatType {
		return
	}
	req := &userpb.ValidHeadReq{
		NoId: c.GetString(NoId),
		Imei: c.GetHeader("imei"),
		Ip:   utils.ClientIP(c.Request),
		Lon:  float32(lon),
		Lat:  float32(lat),
		Url:  c.FullPath(),
	}
	rsp, err := r.ValidHeader(c, req)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusOK, gin.H{
			"code": ecode.ServerErr.Code(),
			"msg":  ecode.ServerErr.Message(),
			"ts":   time.Now().UnixNano() / 1e6,
			"data": nil,
		})
		return
	}

	if int(rsp.Code) == ecode.ForbidDeviceErr.Code() {
		c.AbortWithStatusJSON(http.StatusOK, gin.H{
			"code": ecode.TokenExpiredError.Code(),
			"msg":  ecode.ForbidDeviceErr.Message(),
			"ts":   time.Now().UnixNano() / 1e6,
			"data": nil,
		})
		return
	}
	if int(rsp.Code) == ecode.ForbidIPErr.Code() {
		c.AbortWithStatusJSON(http.StatusOK, gin.H{
			"code": ecode.TokenForbid.Code(),
			"msg":  ecode.ForbidIPErr.Message(),
			"ts":   time.Now().UnixNano() / 1e6,
			"data": nil,
		})
		return
	}
}

func (r *Router) AfterAuth(c *gin.Context) {
	userId := c.GetString(UserKey)
	code := ecode.TOKENERROR.Code()
	msg := ecode.TOKENERROR.Message()
	isErr := false
	if userId == DeviceErr {
		isErr = true
		if c.GetString(AppVersion) >= ForbidVersion {
			code = ecode.TokenExpiredError.Code()
			msg = ecode.TokenForbid.Message()
		}
	} else if userId == IPErr {
		isErr = true
		if c.GetString(AppVersion) >= ForbidVersion {
			code = ecode.TokenForbid.Code()
			msg = ecode.ForbidIPErr.Message()
		}
	}

	if isErr {
		c.AbortWithStatusJSON(http.StatusOK, gin.H{
			"code": code,
			"msg":  msg,
			"ts":   time.Now().UnixNano() / 1e6,
			"data": nil,
		})
		return
	}
}

func (r *Router) ValidImei(c *gin.Context) {
	req := &userpb.ValidHeadReq{
		Imei: c.GetHeader("imei"),
	}

	rsp, err := r.ValidByImei(c, req)
	if err != nil {
		c.AbortWithStatusJSON(http.StatusOK, gin.H{
			"code": ecode.ServerErr.Code(),
			"msg":  ecode.ServerErr.Message(),
			"ts":   time.Now().UnixNano() / 1e6,
			"data": nil,
		})
		return
	}

	if int(rsp.Code) == ecode.ForbidDeviceErr.Code() {
		c.AbortWithStatusJSON(http.StatusOK, gin.H{
			"code": ecode.TokenExpiredError.Code(),
			"msg":  ecode.ForbidDeviceErr.Message(),
			"ts":   time.Now().UnixNano() / 1e6,
			"data": nil,
		})
		return
	}
}

func (r *Router) ValidIP(c *gin.Context) {
	req := &userpb.ValidHeadReq{
		NoId:   c.GetString(NoId),
		Ip:     utils.ClientIP(c.Request),
		Status: c.GetString(Status),
	}

	rsp, err := r.ValidByIP(c, req)
	if err != nil {
		c.Set(NextErr, ecode.ServerErr.Code())
		c.AbortWithStatusJSON(http.StatusOK, gin.H{
			"code": ecode.ServerErr.Code(),
			"msg":  ecode.ServerErr.Message(),
			"ts":   time.Now().UnixNano() / 1e6,
			"data": nil,
		})
		return
	}

	if int(rsp.Code) == ecode.ForbidIPErr.Code() {
		c.Set(NextErr, rsp.Code)
		c.AbortWithStatusJSON(http.StatusOK, gin.H{
			"code": ecode.TokenForbid.Code(),
			"msg":  ecode.ForbidIPErr.Message(),
			"ts":   time.Now().UnixNano() / 1e6,
			"data": nil,
		})
		return
	}
}
