package crypto

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/hex"
	"errors"
	"io"
)

func Encrypt(rawData, key []byte) (string, error) {
	data, err := AesCBCEncrypt(rawData, key)
	if err != nil {
		return "", err
	}
	return hex.EncodeToString(data), nil
}

func Decrypt(rawData string, key []byte) (string, error) {
	data, err := hex.DecodeString(rawData)
	if err != nil {
		return "", err
	}
	deData, err := AesCBCDecrypt(data, key)
	if err != nil {
		return "", err
	}
	return string(deData), nil
}

func AesCBCEncrypt(rawData, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	//填充原文
	blockSize := block.BlockSize()
	rawData = PKCS7Padding(rawData, blockSize)
	//初始向量IV必须是唯一，但不需要保密
	cipherText := make([]byte, blockSize+len(rawData))
	//block大小 16
	iv := cipherText[:blockSize]

	if _, err := io.ReadFull(rand.Reader, iv); err != nil {
		return nil, err
	}

	//block大小和初始向量大小一定要一致
	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(cipherText[blockSize:], rawData)

	return cipherText, nil
}

func AesCBCDecrypt(encryptData, key []byte) ([]byte, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}

	blockSize := block.BlockSize()

	if len(encryptData) < blockSize {
		return nil, err
	}
	iv := encryptData[:blockSize]
	encryptData = encryptData[blockSize:]

	// CBC mode always works in whole blocks.
	if len(encryptData)%blockSize != 0 {
		return nil, err
	}

	mode := cipher.NewCBCDecrypter(block, iv)

	// CryptBlocks can work in-place if the two arguments are the same.
	mode.CryptBlocks(encryptData, encryptData)
	encryptData = PKCS7UnPadding(encryptData)
	return encryptData, nil
}

func PKCS7Padding(ciphertext []byte, blockSize int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padtext...)
}

func PKCS7UnPadding(origData []byte) []byte {
	length := len(origData)
	unpadding := int(origData[length-1])
	return origData[:(length - unpadding)]
}

//填充明文为块大小的整数倍
func fillSrc(src []byte, blockSize int) []byte {
	unFillSize := blockSize - len(src)%blockSize

	addBytes := bytes.Repeat([]byte{byte(unFillSize)}, unFillSize)

	newBytes := append(src, addBytes...)

	return newBytes
}

//删除多余的字节
func delSrc(src []byte) []byte {
	delSize := int(src[len(src)-1])

	return src[:len(src)-delSize]
}

//加密
func AesEncrypt(src []byte, key []byte) []byte {
	//1.创建cipher.Block
	block, _ := aes.NewCipher(key)
	//2.明文填充
	src = fillSrc(src, block.BlockSize())
	//3.创建cbc模式的 blockMode接口
	iv := key[:16]
	cbcMode := cipher.NewCBCEncrypter(block, iv)
	//4.加密
	dst := make([]byte, len(src))
	cbcMode.CryptBlocks(dst, src)
	return dst
}

//解密
func AesDecrypt(dst []byte, key []byte) []byte {
	//1.创建block
	block, _ := aes.NewCipher(key)
	//2.创建block.Mode
	iv := key[:16]
	decrypter := cipher.NewCBCDecrypter(block, iv)
	//3.解密
	src := make([]byte, len(dst))
	decrypter.CryptBlocks(src, dst)
	//4.去掉填充数据
	return delSrc(src)
}

//pkcs7Padding 填充
func pkcs7Padding(data []byte, blockSize int) []byte {
	//判断缺少几位长度。最少1，最多 blockSize
	padding := blockSize - len(data)%blockSize
	//补足位数。把切片[]byte{byte(padding)}复制padding个
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(data, padText...)
}

//pkcs7UnPadding 填充的反向操作
func pkcs7UnPadding(data []byte) ([]byte, error) {
	length := len(data)
	if length == 0 {
		return nil, errors.New("加密字符串错误！")
	}
	//获取填充的个数
	unPadding := int(data[length-1])
	return data[:(length - unPadding)], nil
}

//AesEncrypt 加密
func AesEncryptJS(data, key []byte) ([]byte, error) {
	// key := []byte(sKey)
	//创建加密实例
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	//判断加密块的大小
	blockSize := block.BlockSize()
	//填充
	encryptBytes := pkcs7Padding(data, blockSize)
	//初始化加密数据接收切片
	crypted := make([]byte, len(encryptBytes))
	//使用cbc加密模式
	blockMode := cipher.NewCBCEncrypter(block, key[:blockSize])
	//执行加密
	blockMode.CryptBlocks(crypted, encryptBytes)
	return crypted, nil
}

//AesDecrypt 解密
func AesDecryptJS(data, key []byte) ([]byte, error) {
	// key := []byte(sKey)
	//创建实例
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	//获取块的大小
	blockSize := block.BlockSize()
	//使用cbc
	blockMode := cipher.NewCBCDecrypter(block, key[:blockSize])
	//初始化解密数据接收切片
	crypted := make([]byte, len(data))
	//执行解密
	blockMode.CryptBlocks(crypted, data)
	//去除填充
	crypted, err = pkcs7UnPadding(crypted)
	if err != nil {
		return nil, err
	}
	return crypted, nil
}
