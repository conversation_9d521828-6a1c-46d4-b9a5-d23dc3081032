//Description aes测试
//Date        2021/9/2
//User        cl

package crypto

import (
	"encoding/base64"
	"fmt"
	"testing"
)

func Test_Decrypt(t *testing.T) {
	key := []byte("N7j8yySsgps8nAsP")
	str := "3fc8670005a4062a9d09d7e7eb0b0f4164cd993acdcee3e79ac802d00547414c10529765b93d7802c240af03db04259d"
	raw, err := Decrypt(str, key)
	if err != nil {
		fmt.Println(err.Error())
		t.FailNow()
	}

	fmt.Println(raw)
}

func TestCBC(t *testing.T) {
	info := []byte("")
	key := []byte("N7j8yySsgps8nAsP")

	raw, err := Encrypt(info, key)
	if err != nil {
		fmt.Println(err.<PERSON>r())
		t.<PERSON>ail<PERSON>ow()
	}

	fmt.Println(raw)
	fmt.Println(len(raw))

	str, err := Decrypt(raw,
		key)
	if err != nil {
		panic(err)
	}

	fmt.Println(str)
}

func TestCBCBase64(t *testing.T) {
	key := []byte("lAEhdjtiRbuq2owpi0g5Lm2z6iLbcfbm")
	raw := "OTJjNzQ4ZDA1YzE0Mzg2Mjc3YjljM2JkMzQwMDE2MTA1N2QwZGQ5ZWE2MjQ5YTJkZTZkYmExYzQ5OTliMjI5ZmEyNWM1ODM0OTU5NjViODc5OWUzNDY2NDU3OGY0NTQ5N2NlODE4ZWRkYzhhNDFiNzRmMjI2ZGY1OTg1YjEwNjA5MzdmOWY3MmUzYjBjOGNiMmNjYjQ5MjJhZTY0NDdlYWVhM2ZhNDQzOTQxZDNjNjk4ZTBlYTI0Yjg5OWZjYzJiY2MzZGRlNzQ3Yjk3NzNmOTUxMmMzOWRmYjEzNDM2ODU="
	rawByte, _ := base64.StdEncoding.DecodeString(raw)
	str, err := Decrypt(string(rawByte),
		key)
	if err != nil {
		panic(err)
	}

	fmt.Println(str)
}

func TestAesEncryptJS(t *testing.T) {
	s := []byte("javascript")
	key := []byte("VfilO8phjuGjO7Ch")
	ss, err := AesEncryptJS(s, key)
	if err != nil {
		t.Fatal(err)
	}
	mw := base64.StdEncoding.EncodeToString(ss)
	t.Logf("加密后: %s\n", mw)

	mwb, _ := base64.StdEncoding.DecodeString(mw)
	s, err = AesDecryptJS(mwb, key)
	if err != nil {
		t.Fatal(err)
	}
	t.Logf("解密后: %s\n", s)
}
