package crypto

import (
	"strings"
	"testing"
)

var (
	str  = strings.Join([]string{"/a/b/c", "a=1&c=2"}, "|")
	hash = "97b9d5797fd142a7da8009c78ca5bf5f"
)

func TestHash(t *testing.T) {
	if v := Hash(str); v != hash {
		t.<PERSON><PERSON><PERSON>("hash not match hash: %s, current: %s", hash, v)
	}
}

func BenchmarkHash(b *testing.B) {
	for i := 0; i < b.N; i++ {
		if v := Hash(str); v != hash {
			b.<PERSON><PERSON><PERSON>("hash not match")
		}
	}
}
