//Description tencent人脸
//Date        2021/8/9
//User        cl

package fc

import (
	"crypto/md5"
	"crypto/sha1"
	"encoding/hex"
	"errors"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"

	"creativematrix.com/beyondreading/pkg/logger"
)

type AccessToken struct {
	Code        string
	Msg         string
	AccessToken string `json:"access_token"`
}

type Tickets struct {
	Code    string
	Msg     string
	Tickets []struct {
		Value string
	}
}

type FaceIdReq struct {
	WebankAppId     string `json:"webankAppId"`
	OrderNo         string `json:"orderNo"`
	Name            string `json:"name"`
	IdNo            string `json:"idNo"`
	UserId          string `json:"userId"`
	SourcePhotoStr  string `json:"sourcePhotoStr"`
	SourcePhotoType string `json:"sourcePhotoType"`
	Version         string `json:"version"`
	Sign            string `json:"sign"`
	Nonce           string `json:"nonce"`
}

type FaceIdRsp struct {
	Code     string `json:"code"`
	Msg      string `json:"msg"`
	BizSeqNo string `json:"bizSeqNo"`
	Result   struct {
		BizSeqNo        string `json:"bizSeqNo"`
		TransactionTime string `json:"transactionTime"`
		OrderNo         string `json:"orderNo"`
		FaceId          string `json:"faceId"`
		Success         bool   `json:"success"`
	}
}

func (face *TencentClient) init() {
	face.getAccessToken()
	go func() {
		ticker := time.NewTicker(time.Minute * 20)
		for t := range ticker.C {
			face.getAccessToken()
			face.getSignTicket()
			logger.LogInfoF("%s: accessToken=%s , signTicket=%s", t.String(), face.accessToken, face.signTicket)
		}
	}()
}

func (face *TencentClient) GetToken(reqParam *ReqToken) (*RspToken, error) {
	orderId := reqParam.UserId + strconv.FormatInt(time.Now().Unix(), 10)
	nonce := face.genNonce(orderId)

	reqUrl := "https://miniprogram-kyc.tencentcloudapi.com/api/server/getfaceid?orderNo=" + orderId
	param := &FaceIdReq{
		WebankAppId: face.conf.AppId,
		OrderNo:     orderId,
		UserId:      reqParam.UserId,
		Version:     "1.0.0",
		Sign:        face.genSign(reqParam.UserId, nonce, face.signTicket),
		Nonce:       nonce,
	}
	if reqParam.ComparisonType == CompareFace {
		//高清正脸照
		param.SourcePhotoType = "2"
		param.SourcePhotoStr = ""
	} else {
		param.IdNo = reqParam.IdCardNum
		param.Name = reqParam.IdCardName
	}

	rsp := new(FaceIdRsp)
	_, err := resty.New().R().SetBody(param).SetResult(rsp).Post(reqUrl)

	if err != nil {
		logger.LogErrorw("tencent getFaceID err:", err.Error())
		return nil, err
	}

	nonceTicket := face.getNonceTicket(reqParam.UserId)
	return &RspToken{
		Token: rsp.Result.FaceId,
		Order: orderId,
		Nonce: nonce,
		Sign:  face.genSign(reqParam.UserId, nonce, nonceTicket),
	}, nil
}

func (face *TencentClient) Verify(compareType, bizToken, megData string) (*RspVerify, error) {

	return nil, nil
}

func (face *TencentClient) getAccessToken() {
	reqUrl := "https://miniprogram-kyc.tencentcloudapi.com/api/oauth2/access_token"
	param := make(url.Values)
	param.Add("app_id", face.conf.AppId)
	param.Add("secret", face.conf.Secret)
	param.Add("grant_type", "client_credential")
	param.Add("version", "1.0.0")

	rsp := new(AccessToken)
	_, err := resty.New().R().SetQueryParamsFromValues(param).SetResult(rsp).Get(reqUrl)
	if err != nil {
		logger.LogErrorw("tencent getAccessToken:", err.Error())
		return
	}
	if rsp.Code != "0" {
		logger.LogErrorw("tencent getAccessToken:", rsp.Msg)
		return
	}

	face.mutex.Lock()
	defer face.mutex.Unlock()
	face.accessToken = rsp.AccessToken
}

func (face *TencentClient) getTicket(ticketType, userId string) (string, error) {
	reqUrl := "https://miniprogram-kyc.tencentcloudapi.com/api/oauth2/api_ticket"
	param := make(url.Values)
	param.Add("app_id", face.conf.AppId)
	param.Add("access_token", face.accessToken)
	param.Add("type", ticketType)
	param.Add("version", "1.0.0")
	if userId != "" {
		param.Add("user_id", userId)
	}

	rsp := new(Tickets)
	_, err := resty.New().R().SetQueryParamsFromValues(param).SetResult(rsp).Get(reqUrl)

	if err != nil {
		return "", err
	}
	if rsp.Code != "0" {
		return "", errors.New(rsp.Msg)
	}

	if len(rsp.Tickets) > 0 {
		return rsp.Tickets[0].Value, nil
	}

	return "", errors.New("ticket null")
}

func (face *TencentClient) getSignTicket() {
	ticket, err := face.getTicket("SIGN", "")
	if err != nil {
		logger.LogErrorw("tencent getSignTicket:", "err", err.Error())
		return
	}
	face.mutex.Lock()
	defer face.mutex.Unlock()
	face.signTicket = ticket
}

func (face *TencentClient) getNonceTicket(userId string) string {
	ticket, err := face.getTicket("NONCE", userId)
	if err != nil {
		logger.LogErrorw("tencent getNonceTicket:", "err", err.Error())
		return ""
	}
	return ticket
}

// 32位随机字符
func (face *TencentClient) genNonce(orderId string) string {
	sum := md5.Sum([]byte(orderId))
	return hex.EncodeToString(sum[:])
}

func (face *TencentClient) genSign(userId, nonce, ticket string) string {
	data := []string{face.conf.AppId, userId, "1.0.0", nonce, ticket}
	sort.Slice(data, func(i, j int) bool {
		return data[i] < data[j]
	})
	sign := strings.Join(data, "")
	sum := sha1.Sum([]byte(sign))
	return hex.EncodeToString(sum[:])
}
