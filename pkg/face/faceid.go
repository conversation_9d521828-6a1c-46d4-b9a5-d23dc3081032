//Description faceID人脸
//Date        2021/8/9
//User        cl

package fc

import (
	"bytes"
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"math/rand"
	"regexp"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"

	"creativematrix.com/beyondreading/pkg/ecode"
	"creativematrix.com/beyondreading/pkg/utils"
)

type RspBizToken struct {
	RequestId string `json:"request_id"`
	TimeUsed  int    `json:"time_used"`
	BizToken  string `json:"biz_token"` //biz_token唯一且只能使用一次，且有效期为1小时
	Error     string `json:"error"`
}

// RspVerify 验证结果
type RspVerify struct {
	RequestId    string       `json:"request_id"`
	ResultCode   int          `json:"result_code"`
	Error        string       `json:"error"`
	Images       FaceImage    `json:"images"`
	Verification Verification `json:"verification"`
}

type Verification struct {
	Ref1 RefImg `json:"ref1"`
}

type RefImg struct {
	Confidence float64 `json:"confidence"`
}

type FaceImage struct {
	ImageBest string `json:"image_best"`
}

func (face *FaceIdClient) GetToken(reqParam *ReqToken) (*RspToken, error) {

	result := new(RspBizToken)
	req := resty.New().R().SetResult(result).SetError(result)

	param := make(map[string]string)
	param["sign"] = face.getFaceIDSign(reqParam.ComparisonType)
	param["sign_version"] = SignVersion
	param["liveness_type"] = reqParam.LiveNessType
	param["comparison_type"] = reqParam.ComparisonType

	if reqParam.LiveNessType == RawImage {
		req.SetFile("image", reqParam.Image)
		param["fail_when_multiple_faces"] = "0"
	}

	if reqParam.ComparisonType == CompareKYC {
		param["idcard_name"] = reqParam.IdCardName
		param["idcard_number"] = reqParam.IdCardNum
	} else {
		param["uuid"] = reqParam.UserId
		req.SetFile("image_ref1", reqParam.ImageRef)
	}

	_, err := req.SetMultipartFormData(param).Post(face.conf.BizTokenUrl)
	if err != nil {
		return nil, err
	}

	if result.Error != "" {
		if strings.Contains(result.Error, "NO_FACE_FOUND") {
			return nil, ecode.NeedRealAvatarErr
		}
		if strings.Contains(result.Error, "idcard_name") || strings.Contains(result.Error, "idcard_number") {
			return nil, ecode.CardNumErr
		}
		return nil, errors.New(result.Error)
	}

	return &RspToken{Token: result.BizToken}, nil
}

func (face *FaceIdClient) Verify(compareType, bizToken, megData string) (*RspVerify, error) {
	reqParam := make(map[string]string)
	reqParam["sign"] = face.getFaceIDSign(compareType)
	reqParam["sign_version"] = SignVersion
	reqParam["biz_token"] = bizToken

	result := new(RspVerify)
	req := resty.New().R().SetMultipartFormData(reqParam).SetResult(result)
	if megData != "" {
		req = req.SetFile("meglive_data", megData)
	}

	_, err := req.Post(face.conf.VerifyUrl)
	if err != nil {
		return nil, err
	}

	return result, nil
}

// FaceIDVerify 人脸验证
// megData 为客户端提供的视频数据
func (face *FaceIdClient) FaceIDVerify(ctx context.Context, compareType, bizToken, megData string) (*RspVerify, error) {
	reqParam := make(map[string]string)
	reqParam["sign"] = face.getFaceIDSign(compareType)
	reqParam["sign_version"] = SignVersion
	reqParam["biz_token"] = bizToken

	result := new(RspVerify)
	_, err := resty.New().R().SetMultipartFormData(reqParam).SetFile("meglive_data", megData).
		SetResult(result).Post(face.conf.VerifyUrl)

	if err != nil {
		return nil, err
	}

	return result, nil
}

// FaceID人脸参数签名
func (face *FaceIdClient) getFaceIDSign(comparisonType string) string {
	var appId, secret string
	if comparisonType == CompareFace {
		//人脸比对使用无源key
		appId = face.conf.AppIdWY
		secret = face.conf.SecretWY
	} else {
		//KYC验证使用有源key
		appId = face.conf.AppIdYY
		secret = face.conf.SecretYY
	}
	t := time.Now()

	plainText := fmt.Sprintf("a=%s&b=%d&c=%d&d=%d", appId, t.Unix()+360, t.Unix(), rand.Int31())

	buf := bytes.Join([][]byte{utils.HmacSha1(plainText, secret), []byte(plainText)}, []byte{})

	reg, _ := regexp.Compile("[\\s*\t\n\r]")

	return reg.ReplaceAllString(base64.StdEncoding.EncodeToString(buf), "")
}
