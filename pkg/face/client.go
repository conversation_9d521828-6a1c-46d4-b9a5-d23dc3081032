//Description 人脸封装
//Date        2021/8/9
//User        cl

package fc

import "sync"

type FaceType string

const (
	FaceID  FaceType = "FaceID"
	Tencent FaceType = "Tencent"

	CompareFace   = "0"         //人脸比对类型
	CompareKYC    = "1"         //KYC比对类型
	SignVersion   = "hmac_sha1" //FaceID 签名加密方法
	FaceIDSuccess = 1000        //FaceID 验证成功code

	MegLive  = "meglive"
	RawImage = "raw_image"
)

type IFace interface {
	GetToken(reqParam *ReqToken) (*RspToken, error)
	Verify(compareType, bizToken, megData string) (*RspVerify, error)
}

func FaceClient(t FaceType, conf *FaceConf) IFace {
	switch t {
	case FaceID:
		return &FaceIdClient{conf: conf.FaceID}
	case Tencent:
		tencent := &TencentClient{conf: conf.Tencent}
		tencent.init()
		return tencent
	default:
		return nil
	}
}

type FaceConf struct {
	FaceID  *FaceIdConf
	Tencent *TencentConf
}

type FaceIdConf struct {
	BizTokenUrl string
	VerifyUrl   string
	OcrUrl      string
	AppIdYY     string
	SecretYY    string
	AppIdWY     string
	SecretWY    string
}

type TencentConf struct {
	AppId  string
	Secret string
}

type FaceIdClient struct {
	conf *FaceIdConf
}

type TencentClient struct {
	mutex       sync.Mutex
	conf        *TencentConf
	accessToken string
	signTicket  string
}

type ReqToken struct {
	UserId         string
	ComparisonType string
	LiveNessType   string
	IdCardNum      string
	IdCardName     string
	ImageRef       string
	Image          string
}

type RspToken struct {
	Token string
	Order string
	Nonce string
	Sign  string
}
