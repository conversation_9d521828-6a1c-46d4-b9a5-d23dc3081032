package restify

import (
	"creativematrix.com/beyondreading/pkg/restify/hook"
	"creativematrix.com/beyondreading/pkg/restify/param"
)

type Handler func(data *param.Param) error

type ChainHandler []Handler

type CallBacks struct {
	processors map[string]*processor
}

func (c *CallBacks) Create() *processor {
	return c.processors["create"]
}

func (c *CallBacks) Update() *processor {
	return c.processors["update"]
}

func (c *CallBacks) Query() *processor {
	return c.processors["query"]
}

func (c *CallBacks) QueryOne() *processor {
	return c.processors["queryOne"]
}

func (c *CallBacks) Delete() *processor {
	return c.processors["delete"]
}

func (c *CallBacks) RegisterDefaultCallbacks() {
	create := c.Create()
	create.Register(hook.BeforeCreate)
	create.Register(hook.Create)
	create.Register(hook.AfterCreate)

	update := c.Update()
	update.Register(hook.BeforeUpdate)
	update.Register(hook.Update)
	update.Register(hook.AfterUpdate)

	query := c.Query()
	query.Register(hook.BeforeQuery)
	query.Register(hook.Query)
	query.Register(hook.AfterQuery)

	queryOne := c.QueryOne()
	queryOne.Register(hook.BeforeQueryOne)
	queryOne.Register(hook.QueryOne)
	queryOne.Register(hook.AfterQueryOne)

	delete := c.Delete()
	delete.Register(hook.BeforeDelete)
	delete.Register(hook.Delete)
	delete.Register(hook.AfterDelete)
}

func InitializeCallbacks() *CallBacks {
	return &CallBacks{
		processors: map[string]*processor{
			"create":   {},
			"query":    {},
			"queryOne": {},
			"update":   {},
			"delete":   {},
		},
	}
}

type processor struct {
	fns ChainHandler
}

func (p *processor) Register(fn Handler) {
	p.fns = append(p.fns, fn)
}

func (p *processor) Execute(data *param.Param) error {
	for _, f := range p.fns {
		if err := f(data); err != nil {
			return err
		}
	}
	return nil
}
