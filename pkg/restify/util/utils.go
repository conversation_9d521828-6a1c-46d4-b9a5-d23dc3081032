package util

import (
	"reflect"
	"strconv"
	"strings"
	"sync"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"creativematrix.com/beyondreading/pkg/ecode"
)

var mutex sync.Mutex

func GetOrderBy(query map[string]interface{}) string {
	mutex.Lock()
	defer mutex.Unlock()

	order := ""
	if val, ok := query["orderBy"]; ok {
		order = val.(string)
		delete(query, "orderBy")
	}

	return order
}

func Assignment(data interface{}) {
	resultV := reflect.ValueOf(data).Elem()
	if resultV.FieldByName("ID").Type() == reflect.TypeOf(primitive.ObjectID{}) {
		resultV.FieldByName("ID").Set(reflect.ValueOf(primitive.NewObjectID()))
	}
}

func GetQueryMap(data interface{}) map[string]interface{} {
	queryMap := make(map[string]interface{})
	//resultT := reflect.TypeOf(data).Elem()
	resultV := reflect.ValueOf(data).Elem()
	switch resultV.Type().Kind() {
	case reflect.Struct:
		for i := 0; i < resultV.NumField(); i++ {
			fieldT := resultV.Type().Field(i)
			field := resultV.Field(i)
			query := fieldT.Tag.Get("form")
			orderBy := fieldT.Tag.Get("orderBy")

			if orderBy != "" {
				if _, ok := queryMap["orderBy"]; !ok {
					queryMap["orderBy"] = map[string]string{
						orderBy: "",
					}
				} else {
					queryMap["orderBy"].(map[string]string)[orderBy] = ""
				}
			}

			if query != "" {
				kind := getKind(field)
				switch kind {
				case reflect.String:
					if field.IsValid() && field.Len() != 0 {
						queryMap[query] = field.Interface()
					}
				case reflect.Ptr, reflect.Interface:
					if field.IsNil() {
						queryMap[query] = field.Interface()
					}
				case reflect.Array:
					if v, ok := field.Interface().(primitive.ObjectID); ok && !v.IsZero() {
						queryMap[query] = field.Interface()
					}
				case reflect.Int, reflect.Int64, reflect.Int32:
					queryMap[query] = field.Interface()
				case reflect.Float32:
					queryMap[query] = field.Interface()
				}
			}
		}
	}
	return queryMap
}

func StringToKind(val string, valRef reflect.Value) (reflect.Value, error) {
	kind := getKind(valRef)
	switch kind {
	case reflect.Int:
		if val == "" {
			val = "0"
		}
		v, err := strconv.Atoi(val)
		if err != nil { //可能会有""空字符串的情况，当做默认值处理
			return reflect.Value{}, err
		}
		return reflect.ValueOf(v), nil
	case reflect.Int64:
		if val == "" {
			val = "0"
		}
		v, err := strconv.ParseInt(val, 10, 64)
		if err != nil {
			return reflect.Value{}, err
		}
		return reflect.ValueOf(v), nil
	case reflect.Int32:
		if val == "" {
			val = "0"
		}
		v, err := strconv.ParseInt(val, 10, 32)
		if err != nil {
			return reflect.Value{}, err
		}
		return reflect.ValueOf(v), nil
	case reflect.Float32:
		if val == "" {
			val = "0"
		}
		v, err := strconv.ParseFloat(val, 32)
		if err != nil {
			return reflect.Value{}, err
		}
		return reflect.ValueOf(v), nil
	case reflect.Array:
		obj, err := primitive.ObjectIDFromHex(val)
		if err != nil {
			return reflect.ValueOf(val), nil
		}
		return reflect.ValueOf(obj), nil
	case reflect.String:
		return reflect.ValueOf(val), nil
	default:
		return reflect.Value{}, ecode.UnKnownKindType
	}
}

func getKind(val reflect.Value) reflect.Kind {
	kind := val.Kind()

	switch {
	//case kind >= reflect.Int && kind <= reflect.Int64:
	//	return reflect.Int
	case kind >= reflect.Uint && kind <= reflect.Uint64:
		return reflect.Uint
	case kind >= reflect.Float32 && kind <= reflect.Float64:
		return reflect.Float32
	default:
		return kind
	}
}

func RouteValid(route string) string {
	if !strings.HasPrefix(route, "/") {
		route = "/" + route
	}

	if strings.HasSuffix(route, "/") {
		route = route[:len(route)-1]
	}
	return route
}
