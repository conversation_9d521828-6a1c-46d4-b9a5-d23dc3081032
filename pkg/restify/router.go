package restify

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"reflect"
	"strings"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"creativematrix.com/beyondreading/pkg/ecode"
	"creativematrix.com/beyondreading/pkg/restify/util"

	param2 "creativematrix.com/beyondreading/pkg/restify/param"
)

func (r *Restify) Router(router *gin.RouterGroup, route string, table string, key string, limiter gin.HandlerFunc) {

	param := &param2.Param{
		Table: table,
		Dao:   r.dao,
	}

	route = util.RouteValid(route)
	routeById := util.RouteValid(fmt.Sprintf("%s/:id", route))

	router.POST(route, limiter, func(c *gin.Context) {
		data := r.pools[key].Get()
		if err := c.BindJSON(data); err != nil {
			ecode.Back(c).Failure(err)
			return
		}

		param.Data = data

		if err := r.callbacks.Create().Execute(param); err != nil {
			ecode.Back(c).Failure(err)
			return
		}
		ecode.Back(c).SetData(true).Success()
	})

	router.PUT(routeById, limiter, func(c *gin.Context) {

		data := r.pools[key].Get()
		update := map[string]interface{}{}

		requestBody := c.Request.Body
		body, err := ioutil.ReadAll(requestBody)
		if err != nil {
			ecode.Back(c).Failure(err)
			return
		}

		if err := json.Unmarshal(body, &update); err != nil {
			ecode.Back(c).Failure(err)
			return
		}
		param.Update.Data = update
		param.Update.DataBody = body

		id, err := primitive.ObjectIDFromHex(c.Param("id"))
		if err != nil {
			ecode.Back(c).Failure(ecode.ObjectIDInvalid)
			return
		}

		param.Data = data
		param.Id = id
		if err := r.callbacks.Update().Execute(param); err != nil {
			ecode.Back(c).Failure(err)
			return
		}
		ecode.Back(c).SetData(true).Success()
	})

	router.DELETE(routeById, limiter, func(c *gin.Context) {
		data := r.pools[key].Get()

		id, err := primitive.ObjectIDFromHex(c.Param("id"))
		if err != nil {
			ecode.Back(c).Failure(ecode.ObjectIDInvalid)
			return
		}
		param.Id = id
		param.Data = data

		if err = r.callbacks.Delete().Execute(param); err != nil {
			ecode.Back(c).Failure(err)
			return
		}

		ecode.Back(c).SetData(true).Success()
	})

	router.GET(route, func(c *gin.Context) {
		data := r.pools[key].Get()
		// 分页需要的字段
		var params param2.BaseListParam
		if err := c.BindQuery(&params); err != nil {
			ecode.Back(c).Failure(err)
			return
		}
		param.Query = param2.Query{}
		if err := c.Request.ParseForm(); err != nil {
			ecode.Back(c).Failure(err)
			return
		}

		if len(c.Request.Form) > 0 {
			for k, v := range c.Request.Form {
				resultv := reflect.ValueOf(data)
				resultv = resultv.Elem()
				k1 := strings.Title(k)
				field := resultv.FieldByName(k1)
				if !field.CanAddr() && !field.CanSet() {
					continue
				}
				val, err := util.StringToKind(v[0], field)
				if err != nil {
					if err == ecode.UnKnownKindType {
						continue
					}
					return
				}
				fieldT, _ := resultv.Type().FieldByName(k1)

				if fieldT.Tag.Get("jump") == "true" {
					continue
				}

				field.Set(val)
			}
			param.Data = data
			param.Query.IsSearch = true
			param.Query.FormValue = c.Request.Form
		}

		param.Query.Page = params.Page
		param.Query.PageSize = params.PageSize

		if err := r.callbacks.Query().Execute(param); err != nil {
			ecode.Back(c).Failure(err)
			return
		}

		result := make(map[string]interface{})
		result["list"] = param.Query.Result
		result["count"] = param.Query.Count

		ecode.Back(c).SetData(result).Success()
	})

	router.GET(routeById, func(c *gin.Context) {
		id, err := primitive.ObjectIDFromHex(c.Param("id"))
		if err != nil {
			ecode.Back(c).Failure(ecode.ObjectIDInvalid)
			return
		}
		param.Id = id

		if err := r.callbacks.QueryOne().Execute(param); err != nil {
			ecode.Back(c).Failure(err)
			return
		}

		ecode.Back(c).SetData(param.Query.Result).Success()
	})
}
