package restify

import (
	"sync"

	"creativematrix.com/beyondreading/pkg/restify/conf"
	"creativematrix.com/beyondreading/pkg/restify/param"
)

type Restify struct {
	callbacks *CallBacks
	conf      *conf.Config
	pools     map[string]*sync.Pool
	dao       *param.Dao
}

func New(c *conf.Config) *Restify {
	callbacks := InitializeCallbacks()
	callbacks.RegisterDefaultCallbacks()
	restify := &Restify{
		callbacks: callbacks,
		conf:      c,
		pools:     make(map[string]*sync.Pool),
		dao:       param.NewDao(c),
	}

	return restify
}

func (r *Restify) NewPool(key string) *sync.Pool {
	r.pools[key] = &sync.Pool{}
	return r.pools[key]
}
