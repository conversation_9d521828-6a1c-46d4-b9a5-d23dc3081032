package param

import (
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"creativematrix.com/beyondreading/pkg/mongo"
	"creativematrix.com/beyondreading/pkg/redis"
	"creativematrix.com/beyondreading/pkg/restify/conf"
)

type Param struct {
	Dao    *Dao
	Table  string
	Data   interface{}
	Id     primitive.ObjectID
	Query  Query
	Update Update
}

type Update struct {
	Data     map[string]interface{}
	DataBody []byte
}

type Query struct {
	IsSearch  bool
	Condition bson.M
	FormValue map[string][]string
	Page      int
	PageSize  int
	OrderBy   []*OrderByArray
	Result    interface{}
	Count     int64
}

type OrderByArray struct {
	Column string
	Sort   int
}

type BaseListParam struct {
	PageSize int `form:"pageSize" binding:"required"`
	Page     int `form:"page" binding:"required"`
}

type Dao struct {
	schema        map[string]*mongo.Model
	cache         redis.Redis
	activityCache redis.Redis
}

func createModel(c *conf.Config) map[string]*mongo.Model {
	summer := mongo.Connect(c.Summer)
	tableMap := map[string]*mongo.Model{}
	for _, name := range c.Tables {
		tableMap[name] = summer.Model(name)
	}

	return tableMap
}

func NewDao(c *conf.Config) *Dao {
	dao := &Dao{
		schema: createModel(c),
	}
	if c.Cache != nil {
		dao.cache = redis.Load(c.Cache)
	}
	if c.ActivityCache != nil {
		dao.activityCache = redis.Load(c.ActivityCache)
	}

	return dao
}

func (p *Param) Schema(table string) *mongo.Model {
	return p.Dao.schema[table]
}

func (p *Param) Cache() redis.Redis {
	return p.Dao.cache
}

func (p *Param) ActivityCache() redis.Redis {
	return p.Dao.activityCache
}
