package hook

import (
	"encoding/json"

	"creativematrix.com/beyondreading/pkg/restify/param"
	"go.mongodb.org/mongo-driver/bson"
)

func BeforeUpdate(r *param.Param) error {
	if err := r.<PERSON>a(r.Table).FindOne(r.Data, bson.M{"_id": r.Id}); err != nil {
		return err
	}
	if err := json.Unmarshal(r.Update.DataBody, r.Data); err != nil {
		return err
	}
	if i, ok := r.Data.(BeforeUpdateInterface); ok {
		if err := i.BeforeUpdate(r); err != nil {
			return err
		}
	}

	return nil
}

func Update(r *param.Param) error {
	_, err := r.Schema(r.Table).Update(bson.M{"_id": r.Id}, bson.M{"$set": r.Data})
	return err
}

func AfterUpdate(r *param.Param) error {
	if i, ok := r.Data.(AfterUpdateInterface); ok {
		if err := i.AfterUpdate(r); err != nil {
			return err
		}
	}
	return nil
}
