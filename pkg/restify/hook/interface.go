package hook

import "creativematrix.com/beyondreading/pkg/restify/param"

type BeforeCreateInterface interface {
	BeforeCreate(*param.Param) error
}

type AfterCreateInterface interface {
	AfterCreate(*param.Param) error
}

type BeforeUpdateInterface interface {
	BeforeUpdate(*param.Param) error
}

type AfterUpdateInterface interface {
	AfterUpdate(*param.Param) error
}

type BeforeQueryInterface interface {
	BeforeQuery(*param.Param) error
}

type AfterQueryInterface interface {
	AfterQuery(*param.Param) error
}

type BeforeQueryOneInterface interface {
	BeforeQueryOne(*param.Param) error
}

type AfterQueryOneInterface interface {
	AfterQueryOne(*param.Param) error
}

type BeforeDeleteInterface interface {
	BeforeDelete(*param.Param) error
}

type AfterDeleteInterface interface {
	AfterDelete(*param.Param) error
}

type AfterFindInterface interface {
	AfterFind(*param.Param) error
}
