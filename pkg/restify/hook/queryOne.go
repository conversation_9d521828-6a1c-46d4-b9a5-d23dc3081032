package hook

import (
	"creativematrix.com/beyondreading/pkg/restify/param"
	"go.mongodb.org/mongo-driver/bson"
)

func BeforeQueryOne(r *param.Param) error {
	if i, ok := r.Data.(BeforeQueryOneInterface); ok {
		if err := i.BeforeQueryOne(r); err != nil {
			return err
		}
	}

	return nil
}

func QueryOne(r *param.Param) error {
	result := map[string]interface{}{}
	err := r.<PERSON>(r.Table).FindOne(&result, bson.M{"_id": r.Id})

	r.Query.Result = result
	return err
}

func AfterQueryOne(r *param.Param) error {
	if i, ok := r.Data.(AfterQueryOneInterface); ok {
		if err := i.AfterQueryOne(r); err != nil {
			return err
		}
	}
	return nil
}
