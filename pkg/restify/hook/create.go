package hook

import (
	"creativematrix.com/beyondreading/pkg/restify/param"
	"creativematrix.com/beyondreading/pkg/restify/util"
)

func BeforeCreate(r *param.Param) error {
	util.Assignment(r.Data)
	if i, ok := r.Data.(BeforeCreateInterface); ok {
		if err := i.BeforeCreate(r); err != nil {
			return err
		}
	}
	return nil
}

func Create(r *param.Param) error {
	_, err := r.<PERSON>(r.Table).Insert(r.Data)
	return err
}

func AfterCreate(r *param.Param) error {
	if i, ok := r.Data.(AfterCreateInterface); ok {
		if err := i.AfterCreate(r); err != nil {
			return err
		}
	}
	return nil
}
