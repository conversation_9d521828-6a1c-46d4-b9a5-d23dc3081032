package hook

import (
	"strings"

	"creativematrix.com/beyondreading/pkg/mongo"
	"creativematrix.com/beyondreading/pkg/restify/param"
	"creativematrix.com/beyondreading/pkg/restify/util"
	"go.mongodb.org/mongo-driver/bson"
)

func BeforeQuery(r *param.Param) error {
	query := bson.M{}
	if r.Query.IsSearch {
		queryMap := util.GetQueryMap(r.Data)
		r.Query.OrderBy = GetOrderBy(r, queryMap)

		for k, v := range queryMap {
			if k == "orderBy" {
				continue
			}
			if _, ok := r.Query.FormValue[k]; ok {
				query[k] = v
			}
		}
		r.Query.Condition = query
	}
	if i, ok := r.Data.(BeforeQueryInterface); ok {
		if err := i.BeforeQuery(r); err != nil {
			return err
		}
	}

	return nil
}

func GetOrderBy(r *param.Param, query map[string]interface{}) []*param.OrderByArray {
	if query["orderBy"] == nil {
		return nil
	}

	orderMap := query["orderBy"].(map[string]string)
	if len(orderMap) > 0 {
		orderBy, ok := r.Query.FormValue["orderBy"]
		if ok {
			if len(orderBy) > 0 {
				ret := make([]*param.OrderByArray, 0)
				for _, order := range orderBy {
					// 解析 格式如sort.asc或sort.desc
					var sort int
					t := strings.Split(order, ".")
					if len(t) == 1 { // 默认补全降序
						t = append(t, "desc")
					}
					if _, has := orderMap[t[0]]; has {
						if t[1] == "asc" {
							sort = 1
						} else if t[1] == "desc" {
							sort = -1
						}
						ret = append(ret, &param.OrderByArray{
							Column: t[0],
							Sort:   sort,
						})
					}
				}
				return ret
			}
		}
	}

	return nil
}

func Query(r *param.Param) error {
	result := []map[string]interface{}{}

	p := make([]mongo.Opts, 0)
	p = append(p, mongo.Limit(int64(r.Query.PageSize)))
	p = append(p, mongo.Skip(int64((r.Query.Page-1)*r.Query.PageSize)))
	if len(r.Query.OrderBy) > 0 {
		sort := bson.D{}

		for _, v := range r.Query.OrderBy {
			sort = append(sort, bson.E{Key: v.Column, Value: v.Sort})
		}
		p = append(p, mongo.Sort(sort))
	} else {
		p = append(p, mongo.Sort(bson.M{"_id": -1}))
	}

	r.Query.Count = r.Schema(r.Table).Count(r.Query.Condition)
	if r.Query.Count > 0 {
		if err := r.Schema(r.Table).Find(&result, r.Query.Condition, p...); err != nil {
			return err
		}
	}

	r.Query.Result = result
	return nil
}

func AfterQuery(r *param.Param) error {
	if i, ok := r.Data.(AfterQueryInterface); ok {
		if err := i.AfterQuery(r); err != nil {
			return err
		}
	}
	return nil
}
