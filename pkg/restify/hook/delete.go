package hook

import (
	"creativematrix.com/beyondreading/pkg/restify/param"
	"go.mongodb.org/mongo-driver/bson"
)

func BeforeDelete(r *param.Param) error {
	if err := r.Schema(r.Table).FindOne(r.Data, bson.M{"_id": r.Id}); err != nil {
		return err
	}
	if i, ok := r.Data.(BeforeDeleteInterface); ok {
		if err := i.BeforeDelete(r); err != nil {
			return err
		}
	}

	return nil
}

func Delete(r *param.Param) error {
	return r.Schema(r.Table).Delete(bson.M{"_id": r.Id})
}

func AfterDelete(r *param.Param) error {
	if i, ok := r.Data.(AfterDeleteInterface); ok {
		if err := i.AfterDelete(r); err != nil {
			return err
		}
	}
	return nil
}
