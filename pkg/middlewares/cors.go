package middlewares

import (
	"net/http"
	"os"

	"github.com/gin-gonic/gin"
)

var domains = map[string]bool{
	"http://91quliao.com":             true,
	"http://www.91quliao.com":         true,
	"http://webadmin.91quliao.com":    true,
	"http://webunion.91quliao.com":    true,
	"http://h5.91quliao.com":          true,
	"http://webh5.91quliao.com":       true,
	"http://devwebadmin.91quliao.com": true,
	"http://devwebunion.91quliao.com": true,
	"http://devh5.91quliao.com":       true,
	"http://webgame.91quliao.com":     true,

	"https://91quliao.com":             true,
	"https://www.91quliao.com":         true,
	"https://webadmin.91quliao.com":    true,
	"https://webunion.91quliao.com":    true,
	"https://h5.91quliao.com":          true,
	"https://webh5.91quliao.com":       true,
	"https://devwebadmin.91quliao.com": true,
	"https://devwebunion.91quliao.com": true,
	"https://devh5.91quliao.com":       true,
	"https://webgame.91quliao.com":     true,

	"http://imkela.com":             true,
	"http://www.imkela.com":         true,
	"http://webadmin.imkela.com":    true,
	"http://webunion.imkela.com":    true,
	"http://h5.imkela.com":          true,
	"http://webh5.imkela.com":       true,
	"http://devwebadmin.imkela.com": true,
	"http://devwebunion.imkela.com": true,
	"http://devh5.imkela.com":       true,
	"http://webgame.imkela.com":     true,

	"https://imkela.com":             true,
	"https://www.imkela.com":         true,
	"https://webadmin.imkela.com":    true,
	"https://webunion.imkela.com":    true,
	"https://h5.imkela.com":          true,
	"https://webh5.imkela.com":       true,
	"https://devwebadmin.imkela.com": true,
	"https://devwebunion.imkela.com": true,
	"https://devh5.imkela.com":       true,
	"https://webgame.imkela.com":     true,

	"http://17youni.com":             true,
	"http://www.17youni.com":         true,
	"http://webadmin.17youni.com":    true,
	"http://webunion.17youni.com":    true,
	"http://h5.17youni.com":          true,
	"http://webh5.17youni.com":       true,
	"http://devwebadmin.17youni.com": true,
	"http://devwebunion.17youni.com": true,
	"http://devh5.17youni.com":       true,
	"http://webgame.17youni.com":     true,

	"https://17youni.com":             true,
	"https://www.17youni.com":         true,
	"https://webadmin.17youni.com":    true,
	"https://webunion.17youni.com":    true,
	"https://h5.17youni.com":          true,
	"https://webh5.17youni.com":       true,
	"https://devwebadmin.17youni.com": true,
	"https://devwebunion.17youni.com": true,
	"https://devh5.17youni.com":       true,
	"https://webgame.17youni.com":     true,
}

func Cors() gin.HandlerFunc {
	return func(c *gin.Context) {

		if os.Getenv("SUMMER_ENV") == "production" {
			origin := c.Request.Header.Get("Origin")
			if domains[origin] {
				c.Writer.Header().Set("Access-Control-Allow-Origin", origin)
			}
		} else {
			c.Header("Access-Control-Allow-Origin", "*")
		}

		c.Header("Access-Control-Allow-Methods", "POST, GET, OPTIONS, PUT, DELETE, UPDATE")
		c.Header("Access-Control-Allow-Headers", "Origin, token, webDev, X-Requested-With, Content-Type, Content-Length, sign, platform, Accept, Authorization,union,appVersion")
		c.Header("Access-Control-Allow-Credentials", "true")
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}
		c.Next()
	}
}
