package middlewares

import (
	"bytes"
	"creativematrix.com/beyondreading/pkg/typeconvert"
	"crypto/md5"
	"fmt"
	"io/ioutil"
	"net/http"
	"sort"
	"strings"
	"time"

	"creativematrix.com/beyondreading/pkg/config"
	"creativematrix.com/beyondreading/pkg/ecode"
	"creativematrix.com/beyondreading/pkg/logger"
	"github.com/gin-gonic/gin"
)

var (
	md5Key       string
	webMd5Key    string
	pcMd5Key     string
	whiteListMap map[string]string
)

func Init(c *config.Base) {
	whiteListMap = make(map[string]string)
	for _, v := range c.WhiteList {
		whiteListMap[v] = v
	}

	if c.Md5Switch {
		md5Key = c.Md5Key
		webMd5Key = c.WebMd5Key
		pcMd5Key = c.PcMd5Key
	}
}

func RequestValidate() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("reqId", genReqId()) // 生成请求ID

		if whiteListMap[c.Request.URL.Path] != "" {
			c.Next()
			return
		}

		platform := c.GetHeader("platform")
		c.Set("platform", platform)

		method := c.Request.Method

		md5Val := c.GetHeader("sign")
		var (
			md5Right string
			key      string
		)

		if platform == "web" {
			key = webMd5Key
		} else if platform == "pc" {
			key = pcMd5Key
		} else {
			key = md5Key
		}

		switch method {
		case http.MethodGet:
			md5Right = queryMd5(c, key)

		case http.MethodPost:
			md5Right = bodyMd5(c, key)

		case http.MethodPut:
			md5Right = putMd5(c, key)

		case http.MethodDelete:
			md5Right = deleteMd5(c, key)

		default:

		}

		logger.LogWarnw("md5", "md5Right", md5Right, "md5Val", md5Val)
		if md5Right != "" && md5Right != md5Val {
			logger.LogWarnw("md5", "md5Right", md5Right, "md5Val", md5Val)
			c.AbortWithStatusJSON(http.StatusOK, gin.H{
				"code": ecode.InvalidArgument.Code(),
				"msg":  ecode.InvalidArgument.Message(),
				"ts":   time.Now().UnixNano() / 1e6,
				"data": nil,
			})
			return
		}

		c.Next()
	}
}

func queryMd5(c *gin.Context, key string) string {
	if key == "" {
		return ""
	}

	queryVal := c.Request.URL.Query()

	var md5Str string
	for k, vals := range queryVal {
		for _, val := range vals {
			md5Str += k
			md5Str += val
		}
	}

	if md5Str == "" {
		return ""
	}
	fmt.Println("md5Str:", md5Str)

	md5Str = strings.ToLower(md5Str)

	splitList := strings.Split(md5Str, "")

	sort.Strings(splitList)
	str := strings.Join(splitList, "")

	fmt.Println("str:", str)
	m := md5.New()
	m.Write([]byte(str))
	m.Write([]byte(key))

	return fmt.Sprintf("%x", m.Sum(nil))
}

func bodyMd5(c *gin.Context, key string) string {
	if key == "" {
		return ""
	}

	contentByte, err := c.GetRawData()
	if err != nil {
		return ""
	}

	c.Request.Body = ioutil.NopCloser(bytes.NewBuffer(contentByte))

	if len(contentByte) == 0 {
		return ""
	}

	var content string

	switch c.GetHeader("Content-Type") {
	case "application/json":
		content = string(contentByte)
	default:
		return ""
	}

	if content == "" {
		return ""
	}

	bodyFormatStr := strings.ToLower(string(content))

	splitList := strings.Split(bodyFormatStr, "")

	sort.Strings(splitList)
	str := strings.Join(splitList, "")

	m := md5.New()
	m.Write([]byte(str))
	m.Write([]byte(key))

	return fmt.Sprintf("%x", m.Sum(nil))
}

func deleteMd5(c *gin.Context, key string) string {
	if key == "" {
		return ""
	}

	bodyFormatStr := c.Param("id")
	if bodyFormatStr == "" {
		return ""
	}

	splitList := strings.Split(strings.ToLower(bodyFormatStr), "")

	sort.Strings(splitList)
	str := strings.Join(splitList, "")
	fmt.Println(str)

	m := md5.New()
	m.Write([]byte(str))
	m.Write([]byte(key))

	return fmt.Sprintf("%x", m.Sum(nil))
}

func putMd5(c *gin.Context, key string) string {
	return bodyMd5(c, key)
}

func genReqId() string {
	return typeconvert.Int64ToString(time.Now().UnixNano())
}
