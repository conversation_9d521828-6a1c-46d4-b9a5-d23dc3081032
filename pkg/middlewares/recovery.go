package middlewares

import (
	"creativematrix.com/beyondreading/pkg/logger"
	"fmt"
	"github.com/gin-gonic/gin"
	"runtime/debug"
)

func Recovery() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := recover(); err != nil {
				data := []interface{}{
					"error", fmt.Sprintf("%s", err),
					"stacktrace", string(debug.Stack()),
				}
				logger.LogErrorw("panic recovered:", data...)
				c.JSON(500, gin.H{"ok": false, "result": "SERVER_ERROR"})
				c.Abort()
			}
		}()
		c.Next()
	}
}

func RecoveryBiz(opts ...interface{}) {
	if err := recover(); err != nil {
		logger.LogErrorf("RecoveryBiz opts(%v) error(%v) stack(%s) ", opts, err, string(debug.Stack()))
	}
}
