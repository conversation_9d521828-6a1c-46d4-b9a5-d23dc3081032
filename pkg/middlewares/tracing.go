package middlewares

import (
	"context"
	"github.com/gin-gonic/gin"
	"github.com/opentracing/opentracing-go"
	"github.com/opentracing/opentracing-go/ext"
	"net/http"
)

func spanContext(tracer opentracing.Tracer, r *http.Request) (context.Context, opentracing.Span) {
	ctx, _ := tracer.Extract(opentracing.HTTPHeaders, opentracing.HTTPHeadersCarrier(r.<PERSON>er))
	sSpan := tracer.StartSpan( r.Method+" "+ r.URL.Path, ext.RPCServerOption(ctx))

	ext.HTTPMethod.Set(sSpan,  r.Method)
	ext.HTTPUrl.Set(sSpan,  r.URL.String())
	ext.Component.Set(sSpan, "net/http")

	return opentracing.ContextWithSpan(r.Context(), sSpan), sSpan
}

func Tracing() gin.HandlerFunc {
	return func(c *gin.Context){
		tracer := opentracing.GlobalTracer()
		ctx, span := spanContext(tracer, c.Request)

		c.Set("Tracer", tracer)
		c.Set("ParentSpanContext", span.Context())

		c.Request = c.Request.WithContext(ctx)
		c.Next()

		ext.HTTPStatusCode.Set(span, uint16(c.Writer.Status()))
		span.Finish()
	}
}

