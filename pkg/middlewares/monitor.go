package middlewares

import (
	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
	"net/http"
	"strconv"
	"strings"
	"time"
)

func Monitor() gin.HandlerFunc {
	durationVec := prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Namespace: "kuxiu",
		Subsystem: "web",
		Name:      "request_duration_seconds",
		Help:      "http Request duration in seconds.",
		Buckets:   prometheus.LinearBuckets(0, 100, 5),
	}, []string{"method", "url", "code"})

	prometheus.MustRegister(durationVec)

	return func(c *gin.Context) {
		path := c.Request.URL.Path
		for _, param := range c.Params {
			path = strings.Replace(path, param.Value, ":"+param.Key, 1)
		}
		now := time.Now()
		c.Next()

		code := c.Writer.Status()

		if err := c.Errors.Last(); err != nil && code == http.StatusOK {
			code = http.StatusBadRequest
		}

		if code != 404 {
			durationVec.With(prometheus.Labels{
				"method": c.Request.Method,
				"url":    path,
				"code":   strconv.Itoa(code),
			}).Observe(float64(time.Since(now).Seconds()))
		}
	}
}