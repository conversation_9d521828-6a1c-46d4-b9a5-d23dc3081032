package middlewares

import (
	"bytes"
	"net/http/httputil"
	"os"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/utils"
)

func getBody(c *gin.Context) string {
	var body string
	if rd, err := httputil.DumpRequest(c.Request, true); err == nil {
		message := strings.Split(string(rd), "\r\n\r\n")
		if len(message) > 1 {
			body = message[1]
		}
	}
	return body
}

type bodyLogWriter struct {
	gin.ResponseWriter
	body *bytes.Buffer
}

func (w *bodyLogWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

func Logger() gin.HandlerFunc {
	return func(c *gin.Context) {
		blw := &bodyLogWriter{body: bytes.NewBufferString(""), ResponseWriter: c.<PERSON>}
		c.Writer = blw

		now := time.Now()
		body := getBody(c)
		c.Next()

		token := c.GetHeader("token")
		if os.Getenv("SUMMER_ENV") == "production" {
			if token != "" && len(token) > 10 {
				token = token[10:]
			}
		}
		resTime := time.Since(now)
		result := []interface{}{
			"reqId", c.GetString("reqId"),
			"ip", utils.ClientIP(c.Request),
			"status", c.Writer.Status(),
			"method", c.Request.Method,
			"url", c.Request.URL.Path,
			"host", c.Request.Host,
			"query", c.Request.URL.RawQuery,
			"token", token,
			"sign", c.GetHeader("sign"),
			"userId", c.GetString("userId"),
			"noId", c.GetString("noId"),
			"appVersion", c.GetHeader("AppVersion"),
			"model", c.GetHeader("Model"),
			"platform", c.GetHeader("Platform"),
			"os", c.GetHeader("OS"),
			"adminUser", c.GetString("ctxAUserId") + "-" + c.GetString("ctxAUserName"),
			"unionUser", c.GetString("ctxUserId") + "-" + c.GetString("ctxUserMobile") + "-" + c.GetString("ctxUserType"),
			"body", body,
			"resTime", resTime.Milliseconds(),
			"err", c.Errors.Errors(),
			"openDataLogger", c.GetString("openDataLogger"),
		}

		log := logger.LogInfow
		if resTime.Seconds() > 0.5 {
			log = logger.LogWarnw
		}
		if err := c.Errors.Last(); err != nil {
			log = logger.LogWarnw
			result = append(result, "res", blw.body.String())
		}

		result = append(result, "res", blw.body.String())

		if c.Writer.Status() >= 500 {
			log = logger.LogErrorw
		}

		log("logging http", result...)
	}
}
