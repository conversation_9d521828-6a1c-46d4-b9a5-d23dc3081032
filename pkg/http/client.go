package http

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"io"
	"io/ioutil"
	"net/http"
	nurl "net/url"
	"strings"
	"time"

	"golang.org/x/net/http2"

	"creativematrix.com/beyondreading/pkg/logger"
)

var (
	// HTTPNoKeepAliveClient is http client without keep alive
	HTTPNoKeepAliveClient = &http.Client{
		Transport: &http.Transport{
			DisableKeepAlives: true,
		},
	}
	defaultHTTPClient = &http.Client{
		Transport: &http.Transport{
			MaxIdleConnsPerHost: 2048,
			IdleConnTimeout:     time.Minute * 5,
		},
	}
	H2Client = &http.Client{
		Transport: &http2.Transport{
			AllowHTTP: true, //充许非加密的链接
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}
	defaultTimeout    = 500
	defaultRetryCount = 2
)

func NewDefaultHttpClient() *http.Client {
	return defaultHTTPClient
}

// PostRaw PostRaw
func PostRaw(client *http.Client, url string, header http.Header, reqBody interface{}, params ...int) ([]byte, error) {
	var (
		data []byte
		err  error
	)
	timeOut, retryCount := genDefaultParams(params...)
	for i := 0; i < retryCount; i++ {
		data, err = do(client, http.MethodPost, url, header, reqBody, timeOut)
		if err == nil {
			break
		}
	}

	return data, err
}

// PostWithUnmarshal do http get with unmarshal
func PostWithUnmarshal(ctx context.Context, client *http.Client, urlpath string, header http.Header, reqBody interface{}, resp interface{}, params ...int) error {
	var (
		data []byte
		err  error
	)

	data, err = PostRaw(client, urlpath, header, reqBody, params...)
	if err != nil {
		return err
	}
	// for no resp needed request.
	if resp == nil {
		return nil
	}
	// for big int
	decoder := json.NewDecoder(bytes.NewBuffer(data))
	decoder.UseNumber()
	err = decoder.Decode(resp)
	reqBodyJs, _ := json.Marshal(reqBody)
	if err != nil {
		logger.LogErrorw("err:%s url:%s reqBody:%s  params:%v respData:%s", err, urlpath, reqBodyJs, params, string(data))
	}
	return err
}

// GetRaw get http raw
func Raw(method, url string, header http.Header, reqBody interface{}, params ...int) (resp *http.Response, err error) {
	timeOut, retryCount := genDefaultParams(params...)
	for i := 0; i < retryCount; i++ {
		resp, err = doHttp(nil, method, url, header, reqBody, timeOut)
		if err == nil {
			break
		}
	}
	return
}

// GetRaw get http raw
func GetRaw(client *http.Client, url string, header http.Header, reqBody interface{}, params ...int) ([]byte, error) {
	var (
		data []byte
		err  error
	)
	timeOut, retryCount := genDefaultParams(params...)
	for i := 0; i < retryCount; i++ {
		data, err = do(client, http.MethodGet, url, header, reqBody, timeOut)
		if err == nil {
			break
		}
	}

	return data, err
}

// GetWithUnmarshal do http get with unmarshal
func GetWithUnmarshal(client *http.Client, url string, header http.Header, reqBody interface{}, resp interface{}, params ...int) error {
	data, err := GetRaw(client, url, header, reqBody, params...)
	if err != nil {
		return err
	}
	// for no resp needed request.
	if resp == nil {
		return nil
	}
	// for big int
	decoder := json.NewDecoder(bytes.NewBuffer(data))
	decoder.UseNumber()
	err = decoder.Decode(resp)

	return err
}

func genDefaultParams(params ...int) (int, int) {
	timeOut, retryCount := defaultTimeout, defaultRetryCount
	switch {
	case len(params) >= 2:
		timeOut, retryCount = params[0], params[1]
	case len(params) >= 1:
		timeOut = params[0]
	}
	return timeOut, retryCount
}

func doHttp(client *http.Client, method string, url string, header http.Header, reqBody interface{}, timeOut int) (*http.Response, error) {
	if client == nil {
		client = defaultHTTPClient
	}
	var reader io.Reader
	switch v := reqBody.(type) {
	case nurl.Values:
		reader = strings.NewReader(v.Encode())
	case []byte:
		reader = bytes.NewBuffer(v)
	case string:
		reader = strings.NewReader(v)
	case io.Reader:
		reader = v
	default:
		buff := &bytes.Buffer{}
		err := json.NewEncoder(buff).Encode(v)
		if err != nil {
			return nil, err
		}
		reader = buff
	}
	req, err := http.NewRequest(method, url, reader)
	if err != nil {
		return nil, err
	}
	if header != nil {
		req.Header = header
	}
	ctx, cancelFunc := context.WithTimeout(context.Background(), time.Millisecond*time.Duration(timeOut))
	defer cancelFunc()
	req = req.WithContext(ctx)
	return client.Do(req)
}

func do(client *http.Client, method string, url string, header http.Header, reqBody interface{}, timeOut int) ([]byte, error) {
	resp, err := doHttp(client, method, url, header, reqBody, timeOut)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	data, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	return data, nil
}
