package logger

import (
	"go.uber.org/zap"
)

var Log *zap.Logger
var sugar *zap.SugaredLogger

func init() {

	Log = zap.NewNop()
	sugar = Log.Sugar()
}

func LogInfo(args ...interface{}) {
	sugar.Info(args...)
}

func LogInfoF(template string, args ...interface{}) {
	sugar.Infof(template, args...)
}

func LogInfow(msg string, keysAndValues ...interface{}) {
	sugar.Infow(msg, keysAndValues...)
}

func LogWarnw(msg string, keysAndValues ...interface{}) {
	sugar.Warnw(msg, keysAndValues...)
}
func LogWarnf(template string, args ...interface{}) {
	sugar.Warnf(template, args...)
}

func LogErrorw(msg string, keysAndValues ...interface{}) {
	sugar.Errorw(msg, keysAndValues...)
}
func LogErrorf(template string, args ...interface{}) {
	sugar.Errorf(template, args...)
}

func InitLog(appName string, level string) {
	conf := zap.NewProductionConfig()
	_ = conf.Level.UnmarshalText([]byte(level))

	Log, _ = conf.Build()
	Log = Log.Named(appName)
	// 输出调用堆栈
	Log = Log.WithOptions(zap.AddCallerSkip(1), zap.AddStacktrace(zap.ErrorLevel+1))
	sugar = Log.Sugar()
}
