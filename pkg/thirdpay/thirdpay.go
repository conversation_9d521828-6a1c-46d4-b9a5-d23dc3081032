package thirdpay

import "context"

type IThirdPay interface {
	Name() string
	PayWrapper(context.Context, *PayParam) (*PayRsp, error)
	CallbackHandle(context.Context, string) (*CallbackRsp, error)
}

const (
	Institution_Duliday = "duliday"
	Institution_Xinxin  = "xinxin"
)

type PayParam struct {
	BatchNo      string // 批次号
	OutOrderNo   string // 商户业务订单号
	IdCard       string // 身份证
	Channel      string // 打款渠道
	Phone        string // 手机
	PayeeAccount string // 收款账户
	Amount       string // 金额
	RealName     string // 真实姓名
}

type PayRsp struct {
	OrderNo string
}

type CallbackRsp struct {
	Success      bool
	OutOrderNo   string
	ThirdOrderNo string
	PaidTime     string
	Detail       string
}
