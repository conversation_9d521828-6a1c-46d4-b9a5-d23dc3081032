package thirdpay

import (
	"context"
	"creativematrix.com/beyondreading/pkg/encrypt"
	irsa "creativematrix.com/beyondreading/pkg/rsa"
	"creativematrix.com/beyondreading/pkg/typeconvert"
	"creativematrix.com/beyondreading/pkg/utils"
	"crypto"
	"encoding/base64"
	"fmt"
	"github.com/go-resty/resty/v2"
	"time"
)

const (
	FreeLancerSignUpV1      = "/freelancer/v1/signUp"      // 自由职业者签约接口
	FreeLancerQuerySignUpV1 = "/freelancer/v1/querySignUp" // 自由职业者签约信息查询接口
	FreeLancerSignUpV2      = "/freelancer/v2/signUp"      // 自由职业者二要素签约接口
	SettlePayV1             = "/settle/v1/pay"             // 批量付款接口 V1.0
	SettlePayQuery          = "/settle/v2/payQuery"        // 付款查询接口
	SettleQueryEleReceipt   = "/settle/v1/queryEleReceipt" // 查询电子回单接口 V1.0
	AccountPayBalance       = "/account/v1/payBalance"     // 账户余额查询接口
)

type XinXin struct {
	MerchantNo string // 商户唯一编号
	BaseUrl    string // API地址
	RsaObj     *irsa.Rsa
	Key        string
	Callback   string
}

func NewXinXin(merchantNo, baseUrl, publicKeyData, privateKeyData, key, callback string) *XinXin {
	pubKeyData, err := base64.StdEncoding.DecodeString(publicKeyData)
	if err != nil {
		panic(err)
	}
	priKeyData, err := base64.StdEncoding.DecodeString(privateKeyData)
	if err != nil {
		panic(err)
	}

	return &XinXin{
		MerchantNo: merchantNo,
		BaseUrl:    baseUrl,
		RsaObj:     irsa.NewRsa(string(pubKeyData), string(priKeyData)),
		Key:        key,
		Callback:   callback,
	}
}

func (x *XinXin) Name() string {
	return Institution_Xinxin
}

func (x *XinXin) FreelancerSignUpV2(name, idCard string, mobile int64) {
	// 1.构造请求体
	req := &FreelancerSignUpV2Req{
		MerchantNo: x.MerchantNo,
		Name:       name,
		IdCardNo:   idCard,
		Mobile:     mobile,
	}

	baseReq, err := x.genBaseReq(utils.JsonString(req))
	if err != nil {
		panic(err)
	}
	fmt.Println(utils.JsonString(baseReq))

	rsp := &XinXinRsp{}
	postUrl := x.BaseUrl + FreeLancerSignUpV2
	if _, err = resty.New().R().SetResult(&rsp).SetHeader("Content-Type", "application/json").SetBody(baseReq).Post(postUrl); err != nil {
		panic(err)
	}
	fmt.Println(utils.JsonString(rsp))
	fmt.Println(x.parseRsp(rsp))
}

func (x *XinXin) FreeLancerQuerySignUpV1(name, idCard string) (bool, error) {
	// 1.构造请求体
	req := &FreeLancerQuerySignUpV1Req{
		MerchantNo: x.MerchantNo,
		Name:       name,
		IdCardNo:   idCard,
	}

	baseReq, err := x.genBaseReq(utils.JsonString(req))
	if err != nil {
		panic(err)
	}
	fmt.Println(utils.JsonString(baseReq))

	rsp := &XinXinRsp{}
	postUrl := x.BaseUrl + FreeLancerQuerySignUpV1
	if _, err = resty.New().R().SetResult(&rsp).SetHeader("Content-Type", "application/json").SetBody(baseReq).Post(postUrl); err != nil {
		return false, err
	}
	fmt.Println(utils.JsonString(rsp))

	rspStr, err := x.parseRsp(rsp)
	if err != nil {
		return false, err
	}

	var querySignUpV1Rsp *FreeLancerQuerySignUpV1Rsp
	if err = utils.UnmarshalFromString(rspStr, &querySignUpV1Rsp); err != nil {
		return false, err
	}

	return querySignUpV1Rsp.SignStatus == "1", nil
}

func (x *XinXin) PayWrapper(ctx context.Context, param *PayParam) (*PayRsp, error) {
	var (
		paymentType int64
		channelNo   string
	)
	if param.Channel == "bank_card" {
		paymentType = 0
	} else if param.Channel == "alipay" {
		paymentType = 1
		channelNo = "2000"
	}
	fmt.Println(paymentType)

	isSign, err := x.FreeLancerQuerySignUpV1(param.RealName, param.IdCard)
	if err != nil {
		return nil, err
	}
	if !isSign {
		x.FreelancerSignUpV2(param.RealName, param.IdCard, typeconvert.StringToInt64(param.Phone))
	}

	orderNo, err := x.SettlePay(param.BatchNo, channelNo, param.OutOrderNo, param.RealName, param.PayeeAccount,
		param.IdCard, typeconvert.StringToFloat64(param.Amount), typeconvert.StringToInt64(param.Phone), paymentType)
	if err != nil {
		return nil, err
	}

	return &PayRsp{OrderNo: orderNo}, nil
}

func (x *XinXin) CallbackHandle(ctx context.Context, data string) (*CallbackRsp, error) {
	var (
		rsp  = &CallbackRsp{}
		body *CallbackThirdXinxin
		err  error
	)

	dataStr, err := x.parseCallback(data)
	if err != nil {
		return nil, err
	}
	fmt.Println("xinxin callback", dataStr)

	if err = utils.UnmarshalFromString(dataStr, &body); err != nil {
		fmt.Println(err)
		return nil, err
	}

	rsp.OutOrderNo = body.MerOrderNum
	rsp.ThirdOrderNo = body.TransOrderNum

	if body.TradeState != "2" {
		rsp.Detail = body.TransMsg
		return rsp, nil
	}

	rsp.PaidTime = time.Unix(0, body.OrderCompleteTime*1e6).Format("2006-01-02 15:04:05")
	rsp.Success = true
	return rsp, nil
}

func (x *XinXin) SettlePay(batchNo, channelNo, orderNo, payeeName, payeeAcc, idCard string, amount float64, mobile, paymentType int64) (string, error) {
	payItem := &PayItems{
		OrderNumber:    orderNo,
		PayAmount:      amount,
		PayeeName:      payeeName,
		PayeeAcc:       payeeAcc,
		IdCard:         idCard,
		Mobile:         mobile,
		BankBranchNo:   0,
		BankBranchName: "",
		Province:       "",
		City:           "",
		Memo:           "",
		PayType:        0,
		PaymentType:    paymentType,
		AccType:        1,
	}

	req := &SettlePayReq{
		MerchantNo:     x.MerchantNo,
		TotalCount:     1,
		TotalAmt:       amount,
		BatchNo:        batchNo,
		ChannelNo:      channelNo,
		WechatSubAppId: "",
		PayItems:       []*PayItems{payItem},
		NotifyUrl:      x.Callback,
		ProjectCode:    "",
	}
	fmt.Println("薪信明文REQ", utils.JsonString(req))

	baseReq, err := x.genBaseReq(utils.JsonString(req))
	if err != nil {
		return "", err
	}
	fmt.Println(utils.JsonString(baseReq))

	rsp := &XinXinRsp{}
	postUrl := x.BaseUrl + SettlePayV1
	if _, err = resty.New().R().SetResult(&rsp).SetHeader("Content-Type", "application/json").SetBody(baseReq).Post(postUrl); err != nil {
		return "", err
	}
	fmt.Println(utils.JsonString(rsp))

	rspStr, err := x.parseRsp(rsp)
	if err != nil {
		return "", err
	}
	var settlePayRsp *SettlePayRsp
	if err = utils.UnmarshalFromString(rspStr, &settlePayRsp); err != nil {
		return "", err
	}

	if settlePayRsp.ResCode != "0000" {
		return "", fmt.Errorf("薪信单笔支付错误 code[%s] msg[%s]", settlePayRsp.ResCode, settlePayRsp.ResMsg)
	}
	if len(settlePayRsp.PayResultList) == 1 {
		if settlePayRsp.PayResultList[0].ResCode != "0000" {
			return "", fmt.Errorf("薪信单笔支付错误 code[%s] msg[%s]", settlePayRsp.PayResultList[0].ResCode, settlePayRsp.PayResultList[0].ResMsg)
		}
		return settlePayRsp.PayResultList[0].TransOrderNum, nil
	}

	return "", nil
}

func (x *XinXin) SettlePayQuery() {

}

func (x *XinXin) genBaseReq(reqData string) (*XinXinReq, error) {
	sign, err := x.sign(reqData)
	if err != nil {
		return nil, err
	}

	baseReq := &XinXinReq{
		MerchantNo:  x.MerchantNo,
		EncryptKey:  x.encryptKey(),
		EncryptData: x.encryptData(reqData),
		Sign:        sign,
	}
	return baseReq, nil
}

func (x *XinXin) parseRsp(rsp *XinXinRsp) (string, error) {
	var (
		rspData = &XinXinReq{}
		err     error
	)

	if rsp.Code == "0000" {
		if err = utils.UnmarshalFromString(rsp.Data, &rspData); err != nil {
			return "", err
		}
		fmt.Println(utils.JsonString(rspData))

		ekBytes, err := base64.StdEncoding.DecodeString(rspData.EncryptKey)
		if err != nil {
			fmt.Println(err)
			return "", err
		}
		ek, err := x.RsaObj.Decrypt(ekBytes)
		if err != nil {
			fmt.Println(err)
			return "", err
		}
		fmt.Println("encryptKey:", string(ek))

		edBytes, err := base64.StdEncoding.DecodeString(rspData.EncryptData)
		if err != nil {
			fmt.Println(err)
			return "", err
		}
		edDecryptBytes := encrypt.AesDecrypt(edBytes, ek)
		fmt.Println("encryptData:", string(edDecryptBytes))

		signBytes, err := base64.StdEncoding.DecodeString(rspData.Sign)
		if err != nil {
			fmt.Println(err)
			return "", err
		}

		checkSign := x.RsaObj.Verify(edDecryptBytes, signBytes, crypto.SHA1)
		fmt.Println("验签结果：", checkSign)
		if checkSign {
			return string(edDecryptBytes), nil
		}
	}

	return "", fmt.Errorf("解析返回值错误 code[%s] msg[%s]", rsp.Code, rsp.Message)
}

func (x *XinXin) parseCallback(body string) (string, error) {
	var (
		callbackData *XinXinCallback
		err          error
	)

	if err = utils.UnmarshalFromString(body, &callbackData); err != nil {
		return "", err
	}

	ekBytes, err := base64.StdEncoding.DecodeString(callbackData.EncryptKey)
	if err != nil {
		fmt.Println(err)
		return "", err
	}
	ek, err := x.RsaObj.Decrypt(ekBytes)
	if err != nil {
		fmt.Println(err)
		return "", err
	}
	fmt.Println("回调encryptKey:", string(ek))

	edBytes, err := base64.StdEncoding.DecodeString(callbackData.EncryptData)
	if err != nil {
		fmt.Println(err)
		return "", err
	}
	edDecryptBytes := encrypt.AesDecrypt(edBytes, ek)
	fmt.Println("回调encryptData:", string(edDecryptBytes))

	return string(edDecryptBytes), nil
}

func (x *XinXin) encryptKey() string {
	keyBytes, _ := x.RsaObj.Encrypt([]byte(x.Key))
	return base64.StdEncoding.EncodeToString(keyBytes)
}

func (x *XinXin) encryptData(src string) string {
	return base64.StdEncoding.EncodeToString(encrypt.AesEncrypt(src, x.Key))
}

func (x *XinXin) sign(src string) (string, error) {
	signBytes, err := x.RsaObj.Sign([]byte(src), crypto.SHA1)
	if err != nil {
		return "", err
	}

	return base64.StdEncoding.EncodeToString(signBytes), nil
}
