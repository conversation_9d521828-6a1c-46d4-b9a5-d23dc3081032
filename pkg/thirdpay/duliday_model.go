package thirdpay

type DulidayReqBase struct {
	AppId  string `json:"appId"`  // 企业应用分配给独立日的唯一身份标识
	Method string `json:"method"` // 接口名称（contract.doc）
	Nonce  string `json:"nonce"`  // 随机数，用于防止重放
	Sign   string `json:"sign"`   // 企业请求参数的签名串
	//SignType  string      `json:"signType"`  // 固定值：RSA2
	Timestamp string      `json:"timestamp"` // 发送请求的时间，格式"yyyy-MM-dd HH:mm:ss"
	Version   string      `json:"version"`   // 版本号 1.0
	Data      interface{} `json:"data"`
}

type DulidayRsp struct {
	Code string      `json:"code"`
	Msg  string      `json:"msg"`
	Sign string      `json:"sign"`
	Data interface{} `json:"data"`
}

type ContractDocRsp struct {
	ImgUrl string `json:"imgUrl"` // 合同IMG链接
	PdfUrl string `json:"pdfUrl"` // 合同PDF链接
}

type ContractSignReq struct {
	BackIdentityKey  string `json:"backIdentityKey"`  // 身份证证件照URL：国徽面图片地址 （确保图片大小不超过2M）
	FrontIdentityKey string `json:"frontIdentityKey"` // 身份证证件照URL：人像面图片地址（确保图片大小不超过2M）
	FullName         string `json:"fullName"`         // 真实姓名
	IdentityCard     string `json:"identityCard"`     // 证件号
	IdentityType     string `json:"identityType"`     // 证件号类型，1.身份证（目前唯一类型）
	Phone            string `json:"phone"`            // 手机号
	SignatureKey     string `json:"signatureKey"`     // 手写签名图片地址(分辨率470*345). 特别说明：实际使用时为必填，因特殊原因文档中展示为非必填
	//TaxAddressId     string `json:"taxAddressId"`     // 税优地ID
	Attach  string `json:"attach"`  // 扩展字段
	SignUid int64  `json:"signUid"` // 签约配置id
}

type ContractSignDetailReq struct {
	IdentityCard string `json:"identityCard"` // 身份证号
	TaxAddressId string `json:"taxAddressId"` // 税优地ID
	Attach       string `json:"attach"`       // 扩展字段
	SignUid      int64  `json:"signUid"`      // 签约配置id
}

type ContractSignDetailRsp struct {
	BackIdentityKey  string `json:"backIdentityKey"`  // 证件照反面
	ContractImgUrl   string `json:"contractImgUrl"`   // 签署的协议图片地址
	ContractPDFUrl   string `json:"contractPdfUrl"`   // 签署的协议PDF地址
	FrontIdentityKey string `json:"frontIdentityKey"` // 证件照正面
	FullName         string `json:"fullName"`         // 真实姓名
	IdentityCard     string `json:"identityCard"`     // 证件号
	IdentityType     string `json:"identityType"`     // 证件号类型，1.身份证（目前唯一类型）
	Phone            string `json:"phone"`            // 手机号
	SignStatus       int64  `json:"signStatus"`       // 签约状态，10:未签约 20:签约中 30 签约成功 40 签约失败
	SignatureKey     string `json:"signatureKey"`     // 手写签名
	TaxAddressId     string `json:"taxAddressId"`     // 税优地id
	ErrCode          string `json:"errCode"`          // 错误码
	ErrMsg           string `json:"errMsg"`           // 错误提示
}

type ContractUploadImageReq struct {
	ImageBase64  string `json:"imageBase64"`  // 图片base64,转文件不超过4M
	IdentityCard string `json:"identityCard"` // 身份证号
	FileName     string `json:"fileName"`     // 文件名
}

type PaySingleOrderReq struct {
	OutOrderNo   string `json:"outOrderNo"`   // 商户系统内部订单号 只能为字符或字母或-
	Identity     string `json:"identity"`     // 身份证号
	Channel      string `json:"channel"`      // 支付渠道，BANK:银行卡,ALIPAY:支付宝
	Phone        string `json:"phone"`        // 手机号码
	PayeeAccount string `json:"payeeAccount"` // 收款方账户
	Amount       string `json:"amount"`       // 实际支付金额(不含个人所得税)，单位：元。 只支持2位小数，小数点前最大支持13位，金额必须大于等于0.1元。  最大转账金额以实际签约的限额为准。
	//PayerShowName string `json:"payerShowName"` // 付款方姓名
	PayeeRealName string `json:"payeeRealName"` // 收款方真实姓名（需要用于要素验证）
	//Remark        string `json:"remark"`        // 转账备注（支持200个英文/100个汉字）
	//Attach        string `json:"attach"`        // 附加数据，在调用支付接口时原样返回，可作为自定义参数使用
	//AccountType   string `json:"accountType"`   // 账号类型：主master、子sub
	//SubAccountNo  string `json:"subAccountNo"`  // 子账号唯一标识
	CallbackUrl string `json:"callbackUrl"` // 回调地址
	SignUid     int64  `json:"signUid"`     // 签约配置id
}

type PaySingleOrderRsp struct {
	ReqNo      string `json:"reqNo"`      // 请求流水号
	OrderNo    string `json:"orderNo"`    // 独立日平台订单号
	OutOrderNo string `json:"outOrderNo"` // 商户系统内部订单号
	Attach     string `json:"attach"`     // 附加数据，在查询和异步通知接口中原样返回，可作为自定义参数使用
}

type PayQueryReq struct {
	//ReqNo      string `json:"reqNo"`      // 下单时生成的请求流水号
	OutOrderNo string `json:"outOrderNo"` // 企业系统内部订单号
}

type PayQueryRsp struct {
	Code          string `json:"code"`          // 支付结果 详情支付结果状态码
	Msg           string `json:"msg"`           // 支付结果描述
	ExceptionCode string `json:"exceptionCode"` // 支付异常代码，支付结果状态码为支付失败（40）状态时，通过该字段表示具体的异常
	ReqNo         string `json:"reqNo"`         // 下单时生成的请求流水号
	OrderNo       string `json:"orderNo"`       // 独立日平台订单号
	OutOrderNo    string `json:"outOrderNo"`    // 商户系统内部订单号
}

type CallbackThirdDuliday struct {
	AppId     string `json:"appId"`     // 企业应用分配给独立日的唯一身份标识
	Method    string `json:"method"`    // 接口名称（contract.doc）
	Nonce     string `json:"nonce"`     // 随机数，用于防止重放
	Sign      string `json:"sign"`      // 企业请求参数的签名串
	SignType  string `json:"signType"`  // 固定值：RSA2
	Timestamp string `json:"timestamp"` // 发送请求的时间，格式"yyyy-MM-dd HH:mm:ss"
	Version   string `json:"version"`   // 版本号 1.0
	Data      struct {
		Code          string `json:"code"`
		ExceptionCode string `json:"exceptionCode"`
		Msg           string `json:"msg"`
		OrderNo       string `json:"orderNo"`
		ReqNo         string `json:"reqNo"`
		OutOrderNo    string `json:"outOrderNo"`
	} `json:"data"`
}
