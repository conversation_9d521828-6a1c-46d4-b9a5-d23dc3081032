package thirdpay

import (
	"context"
	cHttp "creativematrix.com/beyondreading/pkg/http"
	"creativematrix.com/beyondreading/pkg/logger"
	irsa "creativematrix.com/beyondreading/pkg/rsa"
	"creativematrix.com/beyondreading/pkg/typeconvert"
	"creativematrix.com/beyondreading/pkg/utils"
	"crypto"
	"encoding/base64"
	"fmt"
	"github.com/go-resty/resty/v2"
	"net/http"
	"time"
)

const (
	ContractDoc         = "/assignment/contract/document"     // 查询协议信息
	ContractSign        = "/assignment/contract/sign"         // 签约接口
	ContractSignDetail  = "/assignment/contract/signDetail"   // 查询签约结果
	ContractUploadImage = "/assignment/contract/upload/image" // 上传文件，如身份证正反面、手写签名等图片，支持png|jpg|jpeg

	PaySingleOrder   = "/pay/singleOrder"   // 单笔支付（异步）
	PayQuery         = "/pay/query"         // 单笔支付结果主动查询
	PayPauseOrders   = "/pay/pauseOrders"   // 暂停支付
	PayRestartOrders = "/pay/restartOrders" // 重启支付
	PayAmountQuery   = "/pay/amount/query"  // 查询余额

	ATTACH = "xingxuan" // 签约配置用
)

type Duliday struct {
	AppId             string
	BaseUrl           string
	SignUid           int64
	SinglePayCallback string
	RsaObj            *irsa.Rsa
}

func NewDuliday(appId, baseUrl, singlePayCallback, publicKeyData, privateKeyData string, signUid int64) *Duliday {
	return &Duliday{
		AppId:             appId,
		BaseUrl:           baseUrl,
		SignUid:           signUid,
		SinglePayCallback: singlePayCallback,
		RsaObj:            irsa.NewRsa(publicKeyData, privateKeyData),
	}
}

func (d *Duliday) Name() string {
	return Institution_Duliday
}

func (d *Duliday) QueryContractDoc(ctx context.Context) (*ContractDocRsp, error) {
	postUrl := d.BaseUrl + ContractDoc

	header := http.Header{}
	header.Add("Content-Type", "application/json")

	req := &DulidayReqBase{
		AppId:     d.AppId,
		Method:    ContractDoc,
		Nonce:     typeconvert.Int64ToString(utils.GenRandom()),
		Sign:      "",
		Timestamp: time.Now().Format("2006-01-02 15:04:05"),
		Version:   "1.0",
		Data:      nil,
	}
	rsp := &DulidayRsp{
		Code: "",
		Msg:  "",
		Sign: "",
		Data: &ContractDocRsp{},
	}

	if err := cHttp.PostWithUnmarshal(ctx, nil, postUrl, header, req, rsp, 1000); err != nil {
		return nil, err
	}

	if rsp.Code != "0000" {
		return nil, fmt.Errorf("查询协议信息错误 code[%s] msg[%s]", rsp.Code, rsp.Msg)
	}

	return rsp.Data.(*ContractDocRsp), nil
}

func (d *Duliday) ContractSign(ctx context.Context, signReq *ContractSignReq) (interface{}, error) {
	postUrl := d.BaseUrl + ContractSign

	header := http.Header{}
	header.Add("Content-Type", "application/json")

	signReq.Attach = ATTACH
	signReq.SignUid = d.SignUid
	req := &DulidayReqBase{
		AppId:     d.AppId,
		Method:    "contract.sign",
		Nonce:     typeconvert.Int64ToString(utils.GenRandom()),
		Sign:      "",
		Timestamp: time.Now().Format("2006-01-02 15:04:05"),
		Version:   "1.0",
		Data:      signReq,
	}
	// 签名
	signSource := fmt.Sprintf("appId=%s&data={attach=%s&backIdentityKey=%s&frontIdentityKey=%s&fullName=%s&identityCard=%s&identityType=%s&phone=%s&signUid=%d&signatureKey=%s}&method=%s&nonce=%s&timestamp=%s&version=1.0",
		req.AppId, signReq.Attach, signReq.BackIdentityKey, signReq.FrontIdentityKey, signReq.FullName, signReq.IdentityCard, signReq.IdentityType, signReq.Phone, signReq.SignUid, signReq.SignatureKey, req.Method, req.Nonce, req.Timestamp)
	sign, err := d.genSign(signSource)
	if err != nil {
		return nil, err
	}
	fmt.Println("signSource", signSource)
	fmt.Println("sign", sign)
	req.Sign = sign

	rsp := &DulidayRsp{
		Code: "",
		Msg:  "",
		Sign: "",
		Data: nil,
	}

	if _, err = resty.New().R().SetResult(&rsp).SetHeader("Content-Type", "application/json").SetBody(req).Post(postUrl); err != nil {
		return nil, err
	}
	fmt.Println(rsp)

	if rsp.Code != "0000" {
		logger.LogErrorf("ContractSign failed req(%s) sign(%s) rsp(%s)", utils.JsonString(req), sign, utils.JsonString(rsp))
		return nil, fmt.Errorf("签约失败[%s]", rsp.Msg)
	}

	return rsp.Data, nil
}

func (d *Duliday) ContractSignDetail(ctx context.Context, idCard string) (*ContractSignDetailRsp, error) {
	postUrl := d.BaseUrl + ContractSignDetail

	header := http.Header{}
	header.Add("Content-Type", "application/json;charset=UTF-8")

	req := &DulidayReqBase{
		AppId:     d.AppId,
		Method:    "contract.detail",
		Nonce:     typeconvert.Int64ToString(utils.GenRandom()),
		Sign:      "",
		Timestamp: time.Now().Format("2006-01-02 15:04:05"),
		Version:   "1.0",
		Data: &ContractSignDetailReq{
			IdentityCard: idCard,
			Attach:       ATTACH,
			SignUid:      d.SignUid,
		},
	}
	// 签名
	signSource := fmt.Sprintf("appId=%s&data={attach=%s&identityCard=%s&signUid=%d}&method=%s&nonce=%s&timestamp=%s&version=1.0", req.AppId, ATTACH, idCard, d.SignUid, req.Method, req.Nonce, req.Timestamp)
	sign, err := d.genSign(signSource)
	if err != nil {
		return nil, err
	}
	req.Sign = sign

	rsp := &DulidayRsp{
		Code: "",
		Msg:  "",
		Sign: "",
		Data: &ContractSignDetailRsp{},
	}

	if _, err = resty.New().R().SetResult(&rsp).SetHeader("Content-Type", "application/json").SetBody(req).Post(postUrl); err != nil {
		return nil, err
	}

	if rsp.Code != "0000" {
		return nil, fmt.Errorf("查询签约结果错误 code[%s] msg[%s]", rsp.Code, rsp.Msg)
	}

	return rsp.Data.(*ContractSignDetailRsp), nil
}

func (d *Duliday) ContractUploadImage(ctx context.Context, imageBase64, idCard, fileName string) (string, error) {
	postUrl := d.BaseUrl + ContractUploadImage

	header := http.Header{}
	header.Add("Content-Type", "application/json")

	req := &DulidayReqBase{
		AppId:     d.AppId,
		Method:    "contract.image",
		Nonce:     typeconvert.Int64ToString(utils.GenRandom() + time.Now().UnixNano()/1e6),
		Sign:      "",
		Timestamp: time.Now().Format("2006-01-02 15:04:05"),
		Version:   "1.0",
		Data: &ContractUploadImageReq{
			ImageBase64:  imageBase64,
			IdentityCard: idCard,
			FileName:     fileName,
		},
	}
	// 签名
	signSource := fmt.Sprintf("appId=%s&data={fileName=%s&identityCard=%s&imageBase64=%s}&method=%s&nonce=%s&timestamp=%s&version=1.0", req.AppId, fileName, idCard, imageBase64, req.Method, req.Nonce, req.Timestamp)
	sign, err := d.genSign(signSource)
	if err != nil {
		return "", err
	}
	req.Sign = sign

	rsp := &DulidayRsp{
		Code: "",
		Msg:  "",
		Sign: "",
		Data: "",
	}

	if _, err = resty.New().R().SetResult(&rsp).SetHeader("Content-Type", "application/json").SetBody(req).Post(postUrl); err != nil {
		return "", err
	}

	if rsp.Code != "0000" {
		logger.LogErrorf("ContractUploadImage failed sign(%s) rsp(%s)", sign, utils.JsonString(rsp))
		return "", fmt.Errorf("上传文件失败[%s]", rsp.Msg)
	}

	return rsp.Data.(string), nil
}

func (d *Duliday) PayWrapper(ctx context.Context, param *PayParam) (*PayRsp, error) {
	var channel string
	if param.Channel == "bank_card" {
		channel = "BANK"
	} else if param.Channel == "alipay" {
		channel = "ALIPAY"
	}

	orderReq := &PaySingleOrderReq{
		OutOrderNo:    param.OutOrderNo,
		Identity:      param.IdCard,
		Channel:       channel,
		Phone:         param.Phone,
		PayeeAccount:  param.PayeeAccount,
		Amount:        param.Amount,
		PayeeRealName: param.RealName,
		CallbackUrl:   d.SinglePayCallback,
	}
	rsp, err := d.PaySingleOrder(ctx, orderReq)
	if err != nil {
		return nil, err
	}

	payRsp := &PayRsp{
		OrderNo: rsp.OrderNo,
	}
	return payRsp, nil
}

func (d *Duliday) CallbackHandle(ctx context.Context, data string) (*CallbackRsp, error) {
	var (
		rsp  = &CallbackRsp{}
		body *CallbackThirdDuliday
		err  error
	)

	if err = utils.UnmarshalFromString(data, &body); err != nil {
		return nil, err
	}

	rsp.OutOrderNo = body.Data.OutOrderNo
	rsp.ThirdOrderNo = body.Data.OrderNo

	if body.Data.Code != "30" {
		rsp.Detail = body.Data.Msg
		return rsp, nil
	}

	rsp.PaidTime = body.Timestamp
	rsp.Success = true
	return rsp, nil
}

func (d *Duliday) PaySingleOrder(ctx context.Context, orderReq *PaySingleOrderReq) (*PaySingleOrderRsp, error) {
	postUrl := d.BaseUrl + PaySingleOrder

	header := http.Header{}
	header.Add("Content-Type", "application/json")

	orderReq.SignUid = d.SignUid
	req := &DulidayReqBase{
		AppId:     d.AppId,
		Method:    "pay.singleOrder",
		Nonce:     typeconvert.Int64ToString(utils.GenRandom()),
		Sign:      "",
		Timestamp: time.Now().Format("2006-01-02 15:04:05"),
		Version:   "1.0",
		Data:      orderReq,
	}
	// 签名
	signSource := fmt.Sprintf("appId=%s&data={amount=%s&callbackUrl=%s&channel=%s&identity=%s&outOrderNo=%s&payeeAccount=%s&payeeRealName=%s&phone=%s&signUid=%d}&method=%s&nonce=%s&timestamp=%s&version=1.0",
		req.AppId, orderReq.Amount, orderReq.CallbackUrl, orderReq.Channel, orderReq.Identity, orderReq.OutOrderNo,
		orderReq.PayeeAccount, orderReq.PayeeRealName, orderReq.Phone, orderReq.SignUid, req.Method, req.Nonce, req.Timestamp)
	sign, err := d.genSign(signSource)
	if err != nil {
		return nil, err
	}
	req.Sign = sign

	rsp := &DulidayRsp{
		Code: "",
		Msg:  "",
		Sign: "",
		Data: &PaySingleOrderRsp{},
	}

	if _, err = resty.New().R().SetResult(&rsp).SetHeader("Content-Type", "application/json").SetBody(req).Post(postUrl); err != nil {
		return nil, err
	}

	if rsp.Code != "0000" {
		return nil, fmt.Errorf("独立日单笔支付错误 code[%s] msg[%s]", rsp.Code, rsp.Msg)
	}

	return rsp.Data.(*PaySingleOrderRsp), nil
}

func (d *Duliday) PayQuery(ctx context.Context, queryReq *PayQueryReq) (*PayQueryRsp, error) {
	postUrl := d.BaseUrl + PayQuery

	header := http.Header{}
	header.Add("Content-Type", "application/json")

	req := &DulidayReqBase{
		AppId:     d.AppId,
		Method:    "pay.query",
		Nonce:     typeconvert.Int64ToString(utils.GenRandom()),
		Sign:      "",
		Timestamp: time.Now().Format("2006-01-02 15:04:05"),
		Version:   "1.0",
		Data:      queryReq,
	}
	// 签名
	signSource := fmt.Sprintf("appId=%s&data={outOrderNo=%s}&method=%s&nonce=%s&timestamp=%s&version=1.0", req.AppId, queryReq.OutOrderNo, req.Method, req.Nonce, req.Timestamp)
	sign, err := d.genSign(signSource)
	if err != nil {
		return nil, err
	}
	req.Sign = sign

	rsp := &DulidayRsp{
		Code: "",
		Msg:  "",
		Sign: "",
		Data: &PayQueryRsp{},
	}

	if err = cHttp.PostWithUnmarshal(ctx, nil, postUrl, header, req, rsp, 1000); err != nil {
		return nil, err
	}

	if rsp.Code != "0000" {
		return nil, fmt.Errorf("单笔支付结果查询错误 code[%s] msg[%s]", rsp.Code, rsp.Msg)
	}

	return rsp.Data.(*PayQueryRsp), nil
}

func (d *Duliday) PayPauseOrders(ctx context.Context) (bool, error) {
	postUrl := d.BaseUrl + PayPauseOrders

	header := http.Header{}
	header.Add("Content-Type", "application/json")

	req := &DulidayReqBase{
		AppId:     d.AppId,
		Method:    "pay.pauseOrders",
		Nonce:     typeconvert.Int64ToString(utils.GenRandom()),
		Sign:      "",
		Timestamp: time.Now().Format("2006-01-02 15:04:05"),
		Version:   "1.0",
	}
	// 签名
	signSource := fmt.Sprintf("appId=%s&method=%s&nonce=%s&timestamp=%s&version=1.0", req.AppId, req.Method, req.Nonce, req.Timestamp)
	sign, err := d.genSign(signSource)
	if err != nil {
		return false, err
	}
	req.Sign = sign

	rsp := &DulidayRsp{
		Code: "",
		Msg:  "",
		Sign: "",
	}

	if err = cHttp.PostWithUnmarshal(ctx, nil, postUrl, header, req, rsp, 1000); err != nil {
		return false, err
	}

	if rsp.Code != "0000" {
		return false, fmt.Errorf("暂停支付错误 code[%s] msg[%s]", rsp.Code, rsp.Msg)
	}

	return true, nil
}

func (d *Duliday) PayRestartOrders(ctx context.Context) (bool, error) {
	postUrl := d.BaseUrl + PayRestartOrders

	header := http.Header{}
	header.Add("Content-Type", "application/json")

	req := &DulidayReqBase{
		AppId:     d.AppId,
		Method:    "pay.restartOrders",
		Nonce:     typeconvert.Int64ToString(utils.GenRandom()),
		Sign:      "",
		Timestamp: time.Now().Format("2006-01-02 15:04:05"),
		Version:   "1.0",
	}
	// 签名
	signSource := fmt.Sprintf("appId=%s&method=%s&nonce=%s&timestamp=%s&version=1.0", req.AppId, req.Method, req.Nonce, req.Timestamp)
	sign, err := d.genSign(signSource)
	if err != nil {
		return false, err
	}
	req.Sign = sign

	rsp := &DulidayRsp{
		Code: "",
		Msg:  "",
		Sign: "",
	}

	if err = cHttp.PostWithUnmarshal(ctx, nil, postUrl, header, req, rsp, 1000); err != nil {
		return false, err
	}

	if rsp.Code != "0000" {
		return false, fmt.Errorf("重启支付错误 code[%s] msg[%s]", rsp.Code, rsp.Msg)
	}

	return true, nil
}

func (d *Duliday) genSign(signSource string) (string, error) {
	signBytes, err := d.RsaObj.Sign([]byte(signSource), crypto.SHA256)
	if err != nil {
		return "", err
	}

	return base64.StdEncoding.EncodeToString(signBytes), nil
}
