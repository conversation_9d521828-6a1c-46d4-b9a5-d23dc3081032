package thirdpay

import (
	"context"
	"creativematrix.com/beyondreading/pkg/utils"
	"fmt"
	"testing"
)

//var (
//	// 测试商户配置
//	merchantNo = "MI278377174086815744"
//	baseUrl = "http://taxpay.yzxxcloud.com/api"
//	key = "iagkok2ld2ncpsot"
//	callback = "http://purchase.testonegolang.91quliao.com/callback/third/xinxin"
//	pubKeyDataStr = "TUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUFwRWQ1bmlkMVAvalF6TG9UdW5rbjB3RmwxYU1xRS9ZVzVwV2pPdHRDd0dvRTdtSng1NHJac3ZQWGY0cUJBYzJmZFZWQWpLbk9MdXhuaVR0alV5Ly9tQ2ZJSzRZRFZ5YnJCcjNwZHNRSHVTNkFWcHM0VXJzcVk0Nzh1alAyWG1NbUhjbDIxZlBpbnJEMDNUK0NMcFhramFJOTFyRHFlWGNFS0dmN0pMSVBkSXVvMisrNUx1SkMrSldoQU1JUVM5WXhsRHI3V3FBMGFxYlhXNXBhdmNuSEkwWFd4ZXZrVmxrZTAzNGpLV3N2TnhWdnk3akNNNDRLTno2YXFXU1V5ZUtUUXptdVU1WDZPd3BXcG8yc2I0V04xMERzTzl4NW9zNmdEcVprRkJkQjlSYjRzaEFmcmpieXFtdExpNGlxUmt3bm42NS9HQ0dTaWRUZ1FZTjRtUXdxL1FJREFRQUI="
//	priKeyDataStr = "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"
//)

var (
	// 生产商户配置
	merchantNo    = "MI302620729919508480"
	baseUrl       = "http://taxpay.yzxxcloud.com/api"
	key           = "2ofbb224ujuzbpe2"
	callback      = "http://purchase.testtwogolang.91quliao.com/callback/third/xinxin"
	pubKeyDataStr = "TUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUFwRWQ1bmlkMVAvalF6TG9UdW5rbjB3RmwxYU1xRS9ZVzVwV2pPdHRDd0dvRTdtSng1NHJac3ZQWGY0cUJBYzJmZFZWQWpLbk9MdXhuaVR0alV5Ly9tQ2ZJSzRZRFZ5YnJCcjNwZHNRSHVTNkFWcHM0VXJzcVk0Nzh1alAyWG1NbUhjbDIxZlBpbnJEMDNUK0NMcFhramFJOTFyRHFlWGNFS0dmN0pMSVBkSXVvMisrNUx1SkMrSldoQU1JUVM5WXhsRHI3V3FBMGFxYlhXNXBhdmNuSEkwWFd4ZXZrVmxrZTAzNGpLV3N2TnhWdnk3akNNNDRLTno2YXFXU1V5ZUtUUXptdVU1WDZPd3BXcG8yc2I0V04xMERzTzl4NW9zNmdEcVprRkJkQjlSYjRzaEFmcmpieXFtdExpNGlxUmt3bm42NS9HQ0dTaWRUZ1FZTjRtUXdxL1FJREFRQUI="
	priKeyDataStr = "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"
)

func newXinXin() *XinXin {
	x := NewXinXin(merchantNo, baseUrl, pubKeyDataStr, priKeyDataStr, key, callback)
	return x
}

func TestXinXin_New(t *testing.T) {
	fmt.Println(newXinXin())
}

func TestXinXin_FreelancerSignUpV2(t *testing.T) {
	x := newXinXin()
	x.FreelancerSignUpV2("卞阮兵", "342623199210227114", ***********)
}

func TestXinXin_FreeLancerQuerySignUpV1(t *testing.T) {
	x := newXinXin()
	fmt.Println(x.FreeLancerQuerySignUpV1("卞阮兵", "342623199210227114"))
}

func TestXinXin_SettlePayBankCard(t *testing.T) {
	x := newXinXin()
	orderNo, err := x.SettlePay(utils.GenUUID(), "1000", utils.Uuid(), "卞阮兵", "6228482001136468715", "342623199210227114", 0.01, ***********,
		0)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(orderNo)
}

func TestXinXin_SettlePayAli(t *testing.T) {
	x := newXinXin()
	orderNo, err := x.SettlePay(utils.GenUUID(), "2000", utils.Uuid(), "卞阮兵", "***********", "342623199210227114", 0.01, ***********,
		1)
	if err != nil {
		fmt.Println(err)
		return
	}
	fmt.Println(orderNo)
}

func TestXinXin_CallbackHandle(t *testing.T) {
	x := newXinXin()
	//data := `{"encryptData":"xtwcKLEKa/3qYSUhNd2XuB+1K6m/wbjoQz4kjoaAvt9i4qQz6eJdbZiYz6Br1Z8iEU+zEyFEtg5Pbs+qiX4lcMeveOYzMyHmsv0hv3EqLhcZtcdEod6/sajkdERnJFhEa93SIRKeoGMv/iT5cUaOPbz02V+9gOb5T5/ijEwsqYOh1TxST0H+ZpKfoq2ENRiWcAG1NnL+wYmvJbAPtiCbry1hrglxgwyl8cAixdCBm3OyXNYJtvND62vkBCNLmORJSSKBd3VNHvc5KiciEoqOLudpDTadda3X+K22/eqDLvSpruXVf7OnJYx/jVpELPRfHP1w+fyGTtVAQISfn3WDR8T6fL3uVY6GW46FtccCMz/nBGu+DV/VjMoSp5WiLT0VnuwHp0nlNFXQZH7lYWaB7qFrm4ap5eoFVDZiC8yIM64RLF4Q8MbR4yPmpXy4kLcn3St5WBmYFUuJ1y8vAahnl5IsgDOQi2Kp7tV+Zzu3v0e/8Bw4jOzOPb919gQwQ9UpWH0z9UfXxvsDJ808L/6AaSxTJJU8uAn+u0pcaPDOaa3Q/56Gm1MA78oXPwfI31pFl2/MQcyW0/Bkr5V+AH7KhguWmBBn1ClYgmyvXIEeqf74Ah48ri3fIbjnz8zbtei/","encryptKey":"D8GB/bu8QffcxN0W6zwJxZjrrJCRCJqI3Y/0WlTgaPmcBBZ3QnmRj/ptk0DW56sJhH0rX/xSc9Lnbp4daa2Wa1KZ06hU2vzIv77o9BNoaYGK+a9mlI/3Bt+VUFle7qgizM8FkFtCjgOVXTMaNGl5gXsoC6/BDc0EJ6QFSBKD2PgGq1otK+XH1KDTMJI2FgrRD0qI4s2BhIWHcs6qaR5q8cJOaOHLVHdZsCjAi7PMw/Cm4zHRXog5mPiMuL3bwG2DaSldIuGQQU6LTfGrYffmF2S6/gBVKoXxiK0gmlHNNN4nyoMxNvm/j2e9+Xqz6ZqsaKweg23eml1/uBrJqMjL/A==","merchantNo":"MI278377174086815744","notifyType":"2","sign":"WnCne/Ulk8CuxCDzRhrFwiVl876tIb8pu16r0fj+XMUBrqTxvI7kjYc/1muhZFMS8gUoHkVSzSaqdAw/kCCs/P4nNw6OZ9/wE1tK55TAn/329Of1m+STvgcEbMuWGnMPYEOdzEZJXWRiVTpIw0oVXQw9ZYzfrfaWVoQhmMgJ8UPaBQcG46i8+butYGMmNYg1fvt1nTpYBhSqMykRg174ETQnS5t6aBf5H3Srm2DzSGaNm/zICyrBFPnzTHoD9U76k8wyKcVvSjQNeFGCGBLJgHSbm1+1Fp4C+5odTjMx66/iBxOpI8JbAGbYJJEvd3lvay7DXjPO/F0TgD0irEicMg=="}`

	data := `{"encryptData":"53FSgokQ0d4Ghk5S0vV3wdRuAiyA7x0aQ8PwUuZLABZ2gIYWjyBZu2kJtBqDUyu6UsRM2k0+O7euIK0S0H/a6qvmxomH9zSkMe97xewewIcjtCdAcSkiMXDIlbM5/sevCLHCaDvNhCGJC+7z7eBP7tgMVgfQ52H8obI/LSwzJAS+0g9lIgdQZc69/qygdO8KTyp4JgENxItfL8y+5F+vgoLOgeSoS+SRGpE2T+7SQCTq2xW9kO8fbqRNkaWmGL6LXNvNv0LUaw+mIV5Fem9v3KKk8fOymUv5KLwMTRsM1UGtFTVsmTnJ1p8XY+axBud3FwUooxWuYb8EjDMoisesB5i7VwrTUD3jCy1ku13ne/yaU5lUSAItFLiizCukO1EG8WmzqhBInv3DCWF7++Ejg1PJdEZd+/+/KS4ioVjH7ESixZNfDF423JtttyWLC1JNLUT1Fz0jU61Ttp5GzagX7vaWFbkd3cmKFGJZNgiv+MA2cTGOUfjk8Hr4RwKywXikW5sReack8uBB3bYW0//0DZnf/3Rs5c+rRBncady3EOOw0QfmCb8Dzo9+VmbDiNS3evMu/YFys63tVmAgiAKlKnvC0TiVtu9keoyVNDLBMSgJ8yOWQVH1dAVTGuDqqu4P","encryptKey":"V8qEhw1ZVfBRDRjMcnJ9Im7faZg83JOTen1TEKTbYCJyCq3d9P0828GZVTWLKFAnWaIA8GadhfR4atQC0gxbE/YejCrPnqYNURcGzoyT2UvAe1JBFq/UTUV1IkfSqowf3K1zb9tvD02Y8h3hhkigdhFE/Ifch1vTgbMP6S+rxQvGnfnC38iSvDN8XwFUYC6G9BiopESTiS8JCPuM0tGBHojGDseURyY+2FZk4cWQOWNfWrMweWyP5dWhbJo1KjuSUlQQ8IT8l1151ETsv3fG9tb4eJVLaFYFh6Ao5UIoHNi+nCpaN45d4FsKWP2wDpmmeJZdrsV4MLleqkumos4vfA==","merchantNo":"MI302620729919508480","notifyType":"2","sign":"bTKcYDpC7iCclyAAJtQiuMBuU2YkKZ8r1j5dOS2giTZ8+UuhBpwlCjF3PlHj60t3c4eIyrVWB9pAxAYlrURBeAZTFg1qxumMte0JoiNqO3SYwMhopFECCmnMWRR1+xkaqpfY88qiqCOYZ+EaCnPt15ylhcwDLEvV+iV6ugrldh5H2C4QZcZWM98EmpSuljlobAciLiFhjCRwxmJNUYzeB2IQszi6dbCVxLou6TUtfNWO7W/JBYbLzjc9OBJ67EK2jUUZwmrHJ8BDC4Kq8p5hwwGO6/QPiSzAF82KjBi4gT4jPrv4o7MJRW13mf/529dDhcg8G3GILMrHJ/Uw/2sf3Q=="}`

	x.CallbackHandle(context.Background(), data)
}
