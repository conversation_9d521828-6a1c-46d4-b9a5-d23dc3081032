package thirdpay

type XinXinReq struct {
	MerchantNo  string `json:"merchantNo"`  // 合作商户编号 系统分配，唯一标识
	EncryptKey  string `json:"encryptKey"`  // 加密后的 AES 秘钥 公钥加密(RSA/ECB/PKCS1Padding)，加密结 果采用 base64 编码
	EncryptData string `json:"encryptData"` // 加密后的请求/应答 报文 AES 加密(AES/ECB/PKCS5Padding)，加密结 果采用 base64 编码
	Sign        string `json:"sign"`        // 签名 对 encryptData 对应的明文进行签名(SHA1WithRSA)，签名结果采用 base64 编码
}

type XinXinRsp struct {
	Code    string `json:"code"`    // 请求返回码 请求返回码 0000 请求成功 其他为失败
	Message string `json:"message"` // 请求返回描述 请求返回描述
	Data    string `json:"data"`    // 请求返回值 上述信息数据格式
}

type XinXinCallback struct {
	MerchantNo  string `json:"merchantNo"`  // 合作商户编号 系统分配，唯一标识
	NotifyType  string `json:"notifyType"`  // 通知类型 结算:2
	EncryptKey  string `json:"encryptKey"`  // 加密后的 AES 秘钥 公钥加密(RSA/ECB/PKCS1Padding)，加密结 果采用 base64 编码
	EncryptData string `json:"encryptData"` // 加密后的请求/应答 报文 AES 加密(AES/ECB/PKCS5Padding)，加密结 果采用 base64 编码
	Sign        string `json:"sign"`        // 签名 对 encryptData 对应的明文进行签名(SHA1WithRSA)，签名结果采用 base64 编码
}

type FreelancerSignUpV2Req struct {
	MerchantNo string `json:"merchantNo"`
	Name       string `json:"name"`
	IdCardNo   string `json:"idCardNo"`
	Mobile     int64  `json:"mobile"`
}

type FreelancerSignUpV2Rsp struct {
	MerchantNo string `json:"merchantNo"`
	ResCode    string `json:"resCode"`
	ResMsg     string `json:"resMsg"`
}

type FreeLancerQuerySignUpV1Req struct {
	MerchantNo string `json:"merchantNo"`
	Name       string `json:"name"`
	IdCardNo   string `json:"idCardNo"`
}

type FreeLancerQuerySignUpV1Rsp struct {
	MerchantNo string `json:"merchantNo"`
	Name       string `json:"name"`
	IdCardNo   string `json:"idCardNo"`
	SignStatus string `json:"signStatus"`
	ResCode    string `json:"resCode"`
	ResMsg     string `json:"resMsg"`
}

type SettlePayReq struct {
	MerchantNo     string      `json:"merchantNo"`
	TotalCount     int64       `json:"totalCount"`     // 付款总笔数
	TotalAmt       float64     `json:"totalAmt"`       // 付款总金额
	BatchNo        string      `json:"batchNo"`        // 商户批次号
	ChannelNo      string      `json:"channelNo"`      // 通道编码，由我司人员提供
	WechatSubAppId string      `json:"wechatSubAppId"` // 微信 appid
	PayItems       []*PayItems `json:"payItems"`       // 付款数据，见下面 payItems 属性说明
	NotifyUrl      string      `json:"notifyUrl"`      // 结算回调 url (也可以登录商户后台配置)
	ProjectCode    string      `json:"projectCode"`    // 项目编号
}

type PayItems struct {
	OrderNumber    string  `json:"orderNumber"`    // 商户订单号
	PayAmount      float64 `json:"payAmount"`      // 金额
	PayeeName      string  `json:"payeeName"`      // 收款人名称
	PayeeAcc       string  `json:"payeeAcc"`       // 收款人账号 银行卡号/支付宝账号/微信号
	IdCard         string  `json:"idCard"`         // 身份证号
	Mobile         int64   `json:"mobile"`         // 手机号
	BankBranchNo   int64   `json:"bankBranchNo"`   // 银联号 非必填
	BankBranchName string  `json:"bankBranchName"` // 支行名称 非必填
	Province       string  `json:"province"`       // 省名称 非必填
	City           string  `json:"city"`           // 市名称 非必填
	Memo           string  `json:"memo"`           // 备注 非必填
	PayType        int64   `json:"payType"`        // 代付类型 0：实时
	PaymentType    int64   `json:"paymentType"`    // 代付方式 0：银行卡，1：支付宝，2：微信
	AccType        int64   `json:"accType"`        // 结算银行卡账号类型 1：对私（目前只支持对私）
}

type SettlePayRsp struct {
	TotalCount    int64        `json:"totalCount"`    // 总笔数
	TotalAmt      float64      `json:"totalAmt"`      // 总金额
	SuccessNum    int64        `json:"successNum"`    // 成功数
	SuccessAmount float64      `json:"successAmount"` // 成功金额
	BatchNo       string       `json:"batchNo"`       // 商户批次号
	MerchantNo    string       `json:"merchantNo"`    // 商户号，我司分配给客户的唯一编号
	ResCode       string       `json:"resCode"`       // 返回码 0000 请求成功
	ResMsg        string       `json:"resMsg"`        // 返回信息
	PayResultList []*PayResult `json:"payResultList"` // 付款返回数据，见下面 payResultList 属性说明
}

type PayResult struct {
	OrderNumber   string  `json:"orderNumber"`   // 商户订单号
	TransOrderNum string  `json:"transOrderNum"` // 订单流水号
	PayAmount     float64 `json:"payAmount"`     // 付款金额
	SplitFlag     int64   `json:"splitFlag"`     // 订单拆分标识 0：未拆分，1：已拆分
	ResCode       string  `json:"resCode"`       // 返回码
	ResMsg        string  `json:"resMsg"`        // 返回信息
}

type SettleQueryReq struct {
	MerchantNo string        `json:"merchantNo"` // 商户号，我司分配给客户的唯一编号
	BatchNo    string        `json:"batchNo"`    // 商户批次号
	QueryItems []*QueryItems `json:"queryItems"` // 查询数据，见下面 queryItems 属性说明
}

type QueryItems struct {
	OrderNumber   string `json:"orderNumber"`   // 商户订单号 与订单流水号二者必须传一个
	TransOrderNum int64  `json:"transOrderNum"` // 订单流水号 与商户订单号二者必须传一个
}

type CallbackThirdXinxin struct {
	MerchantNo        string  `json:"merchantNo"`        // 商户号
	NotifyType        string  `json:"notifyType"`        // 通知类型 (2:结算)
	BatchNum          string  `json:"batchNum"`          // 批次号
	ChannelNo         string  `json:"channelNo"`         // 通道编号
	MerOrderNum       string  `json:"merOrderNum"`       // 商户订单号
	PayeeAcc          string  `json:"payeeAcc"`          // 收款人卡号
	PayeeIdCard       string  `json:"payeeIdCard"`       // 收款人证件号
	PayeeName         string  `json:"payeeName"`         // 收款人姓名
	Telephone         string  `json:"telephone"`         // 收款人电话
	SettMoney         string  `json:"settMoney"`         // 结算金额
	PlatFee           float64 `json:"platFee"`           // 服务费
	SplitFlag         string  `json:"splitFlag"`         // 是否拆单
	TradeState        string  `json:"tradeState"`        // 交易状态 1：未处理 2：出款成功 3： 出款失败 4：处理中 5： 已取消(退票)
	TransMsg          string  `json:"transMsg"`          // 5结5算：返回信息
	TransOrderNum     string  `json:"transOrderNum"`     // 交易订单号
	OrderCompleteTime int64   `json:"orderCompleteTime"` // 订单完成时间
}
