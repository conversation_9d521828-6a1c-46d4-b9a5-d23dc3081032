package rateLimit

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"
	"time"

	"github.com/ajg/form"
	"github.com/gin-gonic/gin"

	"creativematrix.com/beyondreading/pkg/ecode"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/redis"
	"creativematrix.com/beyondreading/pkg/router"
	"creativematrix.com/beyondreading/pkg/utils"
)

const (
	DefaultExpireTime = 10 // 秒
	DefaultMaxThreads = 10
	DefaultPrefix     = "rlg"
)

type LimitClient struct {
	rateLimit redis.Redis
}

func New(conf *redis.Config) *LimitClient {
	return &LimitClient{
		rateLimit: redis.Load(conf),
	}
}

func (p *LimitClient) RateLimiter(param ...Param) gin.HandlerFunc {
	return func(c *gin.Context) {
		ps := evaluateParam(param)

		validAndAssignInput(c, ps)
		pipe := p.rateLimit.Pipeline(c)
		pipe.Send("INCR", ps.Key)
		pipe.Send("TTL", ps.Key)

		replies, err := pipe.Receive()
		if err != nil {
			logger.LogErrorw("pipe filed", "message", ps.Key, "err", err)
			c.AbortWithStatusJSON(http.StatusOK, gin.H{
				"code": ecode.RateLimitInvalid,
				"msg":  ps.CustomMsg,
				"ts":   time.Now().UnixNano() / 1e6,
				"data": nil,
			})
			return
		}

		var (
			current = replies[0].(int64)
			ttl     = replies[1].(int64)
		)

		if current == int64(1) || ttl == int64(-1) {
			_, _ = p.rateLimit.Expire(c, ps.Key, ps.ExpireTime)
		}

		if current > ps.MaxThreads {
			c.AbortWithStatusJSON(http.StatusOK, gin.H{
				"code": ecode.RateLimitInvalid,
				"msg":  ps.CustomMsg,
				"ts":   time.Now().UnixNano() / 1e6,
				"data": nil,
			})
			return
		}
		c.Next()

		if !ps.IsLimitTime {
			defer p.rateLimit.DelKey(c, ps.Key)
		}
	}
}

func validAndAssignInput(c *gin.Context, p *params) {
	keyItem := utils.ClientIP(c.Request)
	userID, exists := c.Get(router.UserKey)
	if exists && userID != "" {
		keyItem = userID.(string)
	}

	if p.ExpireTime == 0 {
		p.ExpireTime = DefaultExpireTime
	}

	if p.MaxThreads == 0 {
		p.MaxThreads = DefaultMaxThreads
	}

	if p.Key == "" {
		// 格式 rlg:60:POST:gold:/gold/issueGold:*************
		p.Key = fmt.Sprintf("%s:%s:%s:%s", DefaultPrefix, c.Request.Method, c.Request.URL.Path, keyItem)
	}

	if len(p.BodyParam) > 0 {
		if c.Request.Method != "GET" { // get token from body
			var body []byte
			if c.Request.Body != nil {
				body, _ = ioutil.ReadAll(c.Request.Body)
			}
			c.Request.Body = ioutil.NopCloser(bytes.NewBuffer(body))

			var data map[string]interface{}
			if err := decodeBody(&data, body, c.ContentType()); err == nil {
				for _, bp := range p.BodyParam {
					if t, ok := data[bp].(string); ok {
						p.Key = p.Key + ":" + t
					}
				}
			}
		}
	}
}

func decodeBody(v interface{}, body []byte, contentType string) error {
	if strings.Contains(contentType, "application/json") {
		return json.Unmarshal(body, v)
	} else if strings.Contains(contentType, "application/x-www-form-urlencoded") {
		return form.DecodeString(v, string(body))
	}
	return errors.New("error content type")
}
