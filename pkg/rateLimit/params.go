package rateLimit

import "creativematrix.com/beyondreading/pkg/ecode"

type params struct {
	Key         string   `json:"key"`         // redis key
	MaxThreads  int64    `json:"maxThreads"`  // 最大的线程数 默认10
	ExpireTime  int64    `json:"expireTime"`  // 到期时间，秒 默认10
	IsLimitTime bool     `json:"isLimitTime"` // 是否在一定时间内限速 默认false 进行删除
	BodyParam   []string `json:"bodyParam"`   // 针对 body参数 设定特殊的处理
	CustomMsg   string   `json:"customMsg"`   // 针对 body参数 设定特殊的处理
}

type Param func(*params)

func evaluateParam(param []Param) *params {
	ps := &params{
		CustomMsg: ecode.DefaultRateMsg.Message(),
	}
	for _, p := range param {
		p(ps)
	}
	return ps
}

func Key(key string) Param {
	return func(o *params) {
		o.Key = key
	}
}

func MaxThreads(maxThreads int64) Param {
	return func(o *params) {
		o.MaxThreads = maxThreads
	}
}

func ExpireTime(expireTime int64) Param {
	return func(o *params) {
		o.ExpireTime = expireTime
	}
}

func IsLimitTime(limit bool) Param {
	return func(o *params) {
		o.IsLimitTime = limit
	}
}

func BodyParam(bp []string) Param {
	return func(o *params) {
		o.BodyParam = bp
	}
}

func MsgParam(msg string) Param {
	return func(o *params) {
		o.CustomMsg = msg
	}
}
