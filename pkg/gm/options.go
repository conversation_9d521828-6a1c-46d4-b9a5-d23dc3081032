package gm

import (
	"creativematrix.com/beyondreading/pkg/logger"
	opentracing "github.com/opentracing/opentracing-go"
	"go.uber.org/zap"
	"golang.org/x/time/rate"
	"google.golang.org/grpc"
)

type options struct {
	logger             *zap.Logger
	tracer             opentracing.Tracer
	clientInterceptors []grpc.UnaryClientInterceptor
	serverInterceptors []grpc.UnaryServerInterceptor
	limiter            *rate.Limiter
	buckets            []float64
}

type Option func(*options)

var buckets = []float64{.005, .01, .025, .05, .1, .25, .5}

func evaluateOption(opts []Option) *options {
	opt := &options{
		logger:  logger.Log,
		tracer:  opentracing.GlobalTracer(),
		buckets: buckets,
	}
	for _, o := range opts {
		o(opt)
	}
	return opt
}

func WithClientInterceptors(interceptors ...grpc.UnaryClientInterceptor) Option {
	return func(o *options) {
		o.clientInterceptors = interceptors
	}
}

func WithServerInterceptors(interceptors ...grpc.UnaryServerInterceptor) Option {
	return func(o *options) {
		o.serverInterceptors = interceptors
	}
}

// WithRateLimiter  r:最大QPS b:桶容量
func WithRateLimiter(r float64, b int) Option {
	return func(o *options) {
		o.limiter = rate.NewLimiter(rate.Limit(r), b)
	}
}
