package gm

import (
	"context"
	"encoding/json"
	"fmt"
	"runtime/debug"
	"strconv"
	"time"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"golang.org/x/time/rate"

	"creativematrix.com/beyondreading/pkg/ecode"
	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	grpc_ctxtags "github.com/grpc-ecosystem/go-grpc-middleware/tags"
	grpc_opentracing "github.com/grpc-ecosystem/go-grpc-middleware/tracing/opentracing"
	grpc_validator "github.com/grpc-ecosystem/go-grpc-middleware/validator"
	grpc_prometheus "github.com/grpc-ecosystem/go-grpc-prometheus"
	"google.golang.org/grpc"
)

func UnaryServerInterceptor(opts ...Option) grpc.UnaryServerInterceptor {

	opt := evaluateOption(opts)

	grpc_prometheus.EnableHandlingTimeHistogram(
		grpc_prometheus.WithHistogramBuckets(opt.buckets),
	)

	tagOpts := []grpc_ctxtags.Option{
		// grpc_ctxtags.WithFieldExtractor(grpc_ctxtags.TagBasedRequestFieldExtractor("log_fields")),
	}

	opt.logger = opt.logger.WithOptions(zap.AddStacktrace(zap.ErrorLevel + 1))

	defaultInterceptors := []grpc.UnaryServerInterceptor{
		unaryServerLog(opt.logger),
		unaryServerRateLimit(opt.limiter),
		grpc_validator.UnaryServerInterceptor(),
		grpc_ctxtags.UnaryServerInterceptor(tagOpts...),
		grpc_prometheus.UnaryServerInterceptor,
		grpc_opentracing.UnaryServerInterceptor(grpc_opentracing.WithTracer(opt.tracer)),
		unaryServerRecover(opt.logger),
	}

	interceptors := append(defaultInterceptors, opt.serverInterceptors...)
	return grpc_middleware.ChainUnaryServer(interceptors...)
}

func unaryServerRateLimit(limiter *rate.Limiter) grpc.UnaryServerInterceptor {

	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (_ interface{}, err error) {
		if limiter == nil {
			return handler(ctx, req)
		}

		if err := limiter.Wait(ctx); err != nil {
			return nil, status.Errorf(codes.ResourceExhausted, "%v", err)
		}

		return handler(ctx, req)
	}
}

func unaryServerLog(logger *zap.Logger) grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		startTime := time.Now()

		resp, err := handler(ctx, req)

		code := status.Code(err)

		l := logger.Warn
		if code == codes.OK {
			l = logger.Info
		} else if code == codes.Unknown {
			l = logger.Error
		}

		l("grpc unary server",
			zap.String("code", code.String()),
			zap.String("method", info.FullMethod),
			zap.String("resTime", time.Since(startTime).String()),
			zap.Any("request", req),
			zap.Any("err", err),
		)

		return resp, err
	}
}

func unaryServerRecover(logger *zap.Logger) grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (_ interface{}, err error) {
		defer func() {
			if r := recover(); r != nil {
				logger.Error("stacktrace from panic",
					zap.String("err", fmt.Sprintf("%s", r)),
					zap.Any("request", req),
					zap.String("method", info.FullMethod),
					zap.String("stacktrace", string(debug.Stack())),
				)
			}
		}()

		resp, err := handler(ctx, req)
		if err != nil {
			logger.Error("rpc error", zap.String("err", err.Error()), zap.String("method", info.FullMethod))
			// code := ecode.Cause(err).Code()
			// if code < 0 {
			// 	code = -code
			// }
			// return nil, status.Errorf(codes.Code(code), "%s", err.Error())

			code, _ := strconv.Atoi(err.Error())
			if code < 0 {
				tErr := &ecode.TransError{
					Code: code,
					Msg:  ecode.Int(code).Message(),
				}
				eb, _ := json.Marshal(tErr)
				return nil, status.Error(codes.Unknown, string(eb))
			}

			return nil, err
		}
		return resp, nil
	}
}
