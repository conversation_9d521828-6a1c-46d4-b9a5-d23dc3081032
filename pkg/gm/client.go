package gm

import (
	"context"
	"time"

	"github.com/afex/hystrix-go/hystrix"
	"go.uber.org/zap"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	grpc_opentracing "github.com/grpc-ecosystem/go-grpc-middleware/tracing/opentracing"
	grpc_prometheus "github.com/grpc-ecosystem/go-grpc-prometheus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/status"
)

func UnaryClientInterceptor(opts ...Option) grpc.UnaryClientInterceptor {

	opt := evaluateOption(opts)

	grpc_prometheus.EnableClientHandlingTimeHistogram(
		grpc_prometheus.WithHistogramBuckets(opt.buckets),
	)

	defaultInterceptors := []grpc.UnaryClientInterceptor{
		unaryClientLog(opt.logger),
		unaryClientDeadline(),
		grpc_prometheus.UnaryClientInterceptor,
		grpc_opentracing.UnaryClientInterceptor(grpc_opentracing.WithTracer(opt.tracer)),
		//unaryClientHystrix(),
	}

	interceptors := append(defaultInterceptors, opt.clientInterceptors...)
	return grpc_middleware.ChainUnaryClient(interceptors...)
}

func unaryClientDeadline() grpc.UnaryClientInterceptor {
	return func(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
		newCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
		defer cancel()
		err := invoker(newCtx, method, req, reply, cc, opts...)
		return err
	}
}

func UnaryClientHystrix() grpc.UnaryClientInterceptor {
	hystrix.DefaultMaxConcurrent = 1000
	hystrix.DefaultTimeout = 12000

	return func(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
		err := hystrix.Do(method, func() error {
			return invoker(ctx, method, req, reply, cc, opts...)
		}, nil)

		// if err == hystrix.ErrCircuitOpen || err == hystrix.ErrTimeout || err == hystrix.ErrMaxConcurrency {
		// 	return ecode.ServerErr
		// }

		return err
	}
}

func unaryClientLog(logger *zap.Logger) grpc.UnaryClientInterceptor {
	logger = logger.WithOptions(zap.AddStacktrace(zap.ErrorLevel + 1))
	return func(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
		startTime := time.Now()
		err := invoker(ctx, method, req, reply, cc, opts...)

		code := status.Code(err)

		if err != nil {
			logger.Error("grpc unary client",
				zap.String("code", code.String()),
				zap.String("method", method),
				zap.String("reqTime", time.Since(startTime).String()),
				zap.Any("request", req),
				zap.Any("err", err),
			)
		}
		return err
	}
}
