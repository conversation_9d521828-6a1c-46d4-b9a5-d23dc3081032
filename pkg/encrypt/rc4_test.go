package encrypt

import (
	"fmt"
	"testing"
)

func TestEecryptRC4(t *testing.T) {
	//userId := "60de7c3590e19257e578d362"
	//noId := "888880011"
	//msgId := "1ac6843e60ed136e43"

	userId := "6110d420059d8e6a1b3e756a"
	noId := "1"
	msgId := "13"

	jStr := userId + "_" + noId + "_" + msgId

	a := EecryptRC4(SummerEncryptKey, []byte(jStr))
	fmt.Println("加密后", a)

	b := DecryptRC4(SummerEncryptKey, "d4187db468ee23c370de64c54b49064c734b2f9427f6ea0fd26f4c9a6d12882bed3c33293772027c6e3091bd766263e98e0bbf8c36")
	fmt.Println("解密后", b)
}
