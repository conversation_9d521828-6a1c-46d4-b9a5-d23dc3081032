package encrypt

import (
	"encoding/hex"
	"fmt"
	"testing"
)

func TestEncryptAES(t *testing.T) {
	x := []byte("88888888")
	key := []byte("hgfedcba87654321")
	x1 := EncryptAES(x, key)
	fmt.Println("x1:", x1)
	info := hex.EncodeToString(x1)
	fmt.Println(info)
	c, err := hex.DecodeString(info)
	if err != nil {
		panic(err)
	}
	fmt.Println("c:", c)
	x2 := DecryptAES(c, key)
	fmt.Print(string(x2))

}

func TestEDecryptRC4(t *testing.T) {
	key := "hello"
	val := "888888888"
	p := []byte(key)
	vp := []byte(val)
	info := EecryptRC4(p, vp)
	fmt.Println("info:", info)

	fmt.Println(DecryptRC4(p, info))
}
