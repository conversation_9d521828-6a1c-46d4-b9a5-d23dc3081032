package encrypt

import (
	"crypto/rc4"
	"encoding/hex"
)

var SummerEncryptKey = []byte("jkvyEOs5")

//加密/解密
func EecryptRC4(key []byte, val []byte) string {
	cipher, err := rc4.NewCipher(key)
	if err != nil {
		return ""
	}
	p := make([]byte, len(val))
	cipher.XORKeyStream(p, val)
	return hex.EncodeToString(p)
}

//加密/解密
func DecryptRC4(key []byte, val string) string {
	vByte, err := hex.DecodeString(val)
	if err != nil {
		return ""
	}
	cipher, err := rc4.NewCipher(key)
	if err != nil {
		return ""
	}
	p := make([]byte, len(vByte))
	cipher.XORKeyStream(p, vByte)
	return string(p)
}
