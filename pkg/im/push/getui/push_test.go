package getui

import (
	"encoding/json"
	"gotest.tools/assert"
	"testing"

	"creativematrix.com/beyondreading/pkg/im/push"
	"creativematrix.com/beyondreading/pkg/utils"
)

// 单次推送ByCid 厂商通道+纯透模板
func TestPushSingleByCidA(t *testing.T) {
	iosChannel := push.IosChannel{
		Type: "",
		Aps: &push.Aps{
			Alert: &push.Alert{
				Title: "卡是谁？",
				Body:  "为什么我们每天都要打 TA ？",
			},
			ContentAvailable: 0,
		},
		AutoBadge:      "+1",
		PayLoad:        "",
		Multimedia:     nil,
		ApnsCollapseId: "",
	}
	stringIos, _ := json.Marshal(iosChannel)

	param := push.PushSingleByCidParam{
		RequestId: utils.Uuid(),
		Audience: &push.Audience{ // 目标用户
			Cid:           []string{_cid}, // cid推送数组
			Alias:         nil,            // 别名送数组
			Tag:           nil,            // 推送条件
			FastCustomTag: "",             // 使用用户标签筛选目标用户
		},
		Settings: &push.Settings{ // 推送条件设置
			TTL: 3600000, // 默认一小时，消息离线时间设置，单位毫秒
			Strategy: &push.Strategy{ // 厂商通道策略
				Default: 1,
				Ios:     4,
				St:      1,
				Hw:      1,
				Xm:      1,
				Vv:      1,
				Mz:      1,
				Op:      1,
			},
			Speed:        0, // 推送速度，设置100表示：100条/秒左右，0表示不限速
			ScheduleTime: 0, // 定时推送时间，必须是7天内的时间，格式：毫秒时间戳
		},
		PushMessage: &push.PushMessage{
			Duration:     "", // 手机端通知展示时间段
			Notification: nil,
			Transmission: string(stringIos),
			Revoke:       nil,
		},
		PushChannel: &push.PushChannel{
			Ios: &iosChannel,
			Android: &push.AndroidChannel{Ups: &push.Ups{
				Notification: nil,
				TransMission: string(stringIos), // 透传消息内容，与notification 二选一
			}},
		},
	}
	err := gt.PushSingleByCid(ctx, param)
	assert.NilError(t, err)

}

// 测试-单次推送ByCid 厂商通道普通模板+个推通道普通模板
func TestPushSingleByCidB(t *testing.T) {
	ms := make([]*push.Multimedia, 0)
	ms = append(ms, &push.Multimedia{
		Url:  "http://devimg.91quliao.com/admin/2022/01/890c34c1c930e792e5a6e278a1bc1d44.png",
		Type: 1,
	})
	iosChannel := push.IosChannel{
		Type: "notify",
		Aps: &push.Aps{
			Alert: &push.Alert{
				Title: "卡是谁啊？",
				Body:  "为什么我们每天都要打 TA ？",
			},
			ContentAvailable: 0,
		},
		AutoBadge:      "+1",
		PayLoad:        "",
		Multimedia:     ms,
		ApnsCollapseId: "",
	}
	notification := push.Notification{
		Title:       "卡是谁啊？",
		Body:        "为什么我们每天都要打 TA ？",
		ClickType:   "startapp", // 打开应用首页
		BadgeAddNum: 1,
	}

	singleParam := push.PushSingleByAliasParam{
		RequestId: utils.Uuid(), // 请求唯一标识号
		Audience: &push.Audience{ // 目标用户
			Alias:         []string{_alias}, // 别名送数组
			Tag:           nil,              // 推送条件
			FastCustomTag: "",               // 使用用户标签筛选目标用户
		},
		Settings: &push.Settings{ // 推送条件设置
			TTL: 3600000, // 默认一小时，消息离线时间设置，单位毫秒
			Strategy: &push.Strategy{ // 厂商通道策略，具体看public_struct.go
				Default: 1,
				Ios:     4,
				St:      4,
				Hw:      4,
				Xm:      4,
				Vv:      4,
				Mz:      4,
				Op:      4,
			},
			Speed:        100, // 推送速度，设置100表示：100条/秒左右，0表示不限速
			ScheduleTime: 0,   // 定时推送时间，必须是7天内的时间，格式：毫秒时间戳
		},
		PushMessage: &push.PushMessage{
			Notification: &notification,
		},
		PushChannel: &push.PushChannel{
			Ios: &iosChannel,
			Android: &push.AndroidChannel{Ups: &push.Ups{
				Notification: &notification,
			}},
		},
	}
	err := gt.PushSingleByAlias(ctx, singleParam)
	assert.NilError(t, err)
}

func TestGeTui_PushListByCid(t *testing.T) {
	param := push.PushListByCidParam{
		ToList: push.ToList{
			Audience: &push.Audience{
				Cid: _cids,
			},
			IsAsync: false,
			TaskId:  _taskId,
		},
	}
	err := gt.PushListByCid(ctx, param)
	assert.NilError(t, err)
}

func TestGeTui_CreateMessage(t *testing.T) {
	iosChannel := push.IosChannel{
		Type: "",
		Aps: &push.Aps{
			Alert: &push.Alert{
				Title: "卡是谁？",
				Body:  "为什么我们每天都要打 TA ？",
			},
			ContentAvailable: 0,
		},
		AutoBadge:      "+1",
		PayLoad:        "",
		Multimedia:     nil,
		ApnsCollapseId: "",
	}
	stringIos, _ := json.Marshal(iosChannel)

	param := push.CreateMessageParam{
		RequestId: utils.Uuid(),
		GroupName: "",
		Settings: &push.Settings{ // 推送条件设置
			TTL: 3600000, // 默认一小时，消息离线时间设置，单位毫秒
			Strategy: &push.Strategy{ // 厂商通道策略
				Default: 1,
				Ios:     4,
				St:      1,
				Hw:      1,
				Xm:      1,
				Vv:      1,
				Mz:      1,
				Op:      1,
			},
			Speed:        0, // 推送速度，设置100表示：100条/秒左右，0表示不限速
			ScheduleTime: 0, // 定时推送时间，必须是7天内的时间，格式：毫秒时间戳
		},
		PushMessage: &push.PushMessage{
			Duration:     "", // 手机端通知展示时间段
			Notification: nil,
			Transmission: string(stringIos),
			Revoke:       nil,
		},
		PushChannel: &push.PushChannel{
			Ios: &iosChannel,
			Android: &push.AndroidChannel{Ups: &push.Ups{
				Notification: nil,
				TransMission: string(stringIos), // 透传消息内容，与notification 二选一
			}},
		},
	}

	tid, err := gt.CreateMessage(ctx, param)
	assert.NilError(t, err)
	_taskId = tid
	t.Logf("taskId: %s", _taskId)
}

func TestGeTui_QueryScheduleTask(t *testing.T) {
	task, err := gt.QueryScheduleTask(ctx, _cid, _taskId)
	assert.NilError(t, err)
	for _, dt := range task.Detail {
		t.Logf("%s %s", dt.Time, dt.Event)
	}
}

func TestGeTui_DeleteScheduleTask(t *testing.T) {
	err := gt.DeleteScheduleTask(ctx, _taskId)
	assert.NilError(t, err)
}
