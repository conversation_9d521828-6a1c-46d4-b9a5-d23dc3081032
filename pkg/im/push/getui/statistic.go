package getui

import (
	"context"
	"creativematrix.com/beyondreading/pkg/im/push"
)

func (gt *GeTui) QueryPushResultByTaskIds(ctx context.Context, taskId ...string) (*push.PushResult, error) {
	go gt.checkAndReacquireToken(ctx)
	url := BaseUrl + gt.conf.AppId + "/report/push/task/" + gt.Join(taskId...)

	result := &push.QueryPushResultByTaskIdsResult{}
	if err := gt.handleHttp(nil, result, url, GetMethod, gt.token); err != nil {
		return nil, err
	}

	if err := gt.handleResult(ctx, result.HttpResult); err != nil {
		return nil, err
	}

	return result.Data, nil

}
