package getui

import (
	"context"
	"fmt"
)

var (
	conf = Config{
		AppId:        "12312312312312",
		AppSecret:    "12312312312312",
		AppKey:       "12312312312312",
		MasterSecret: "12312312312312",
	}
	gt      *GeTui
	ctx     = context.Background()
	_cid    = "1"
	_cids   = []string{"1", "2"}
	_alias  = "test_user"
	_taskId = ""
)

func init() {
	getui, err := InitGeTui(conf.AppId, conf.AppKey, conf.AppSecret, conf.MasterSecret)
	if err != nil {
		_ = fmt.<PERSON><PERSON>rf("%s", err.<PERSON>rror())
	}
	gt = getui
	fmt.Printf("init token: %s\n", gt.token)
}
