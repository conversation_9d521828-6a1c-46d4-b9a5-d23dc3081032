package getui

import (
	"context"
	"creativematrix.com/beyondreading/pkg/im/push"
)

func (gt *GeTui) PushSingleByCid(ctx context.Context, param push.PushSingleByCidParam) error {
	go gt.checkAndReacquireToken(ctx)
	url := BaseUrl + gt.conf.AppId + "/push/single/cid"

	result := &push.HttpResult{}
	if err := gt.handleHttp(param, result, url, PostMethod, gt.token); err != nil {
		return err
	}

	return gt.handleResult(ctx, result)
}

func (gt *GeTui) PushSingleByAlias(ctx context.Context, param push.PushSingleByAliasParam) error {
	go gt.checkAndReacquireToken(ctx)
	url := BaseUrl + gt.conf.AppId + "/push/single/alias"

	result := &push.HttpResult{}
	if err := gt.handleHttp(param, result, url, PostMethod, gt.token); err != nil {
		return err
	}

	return gt.handleResult(ctx, result)
}

func (gt *GeTui) PushBatchByCid(ctx context.Context, param push.PushBatchByCidParam) error {
	go gt.checkAndReacquireToken(ctx)
	url := BaseUrl + gt.conf.AppId + "/push/single/batch/cid"

	result := &push.HttpResult{}
	if err := gt.handleHttp(param, result, url, PostMethod, gt.token); err != nil {
		return err
	}

	return gt.handleResult(ctx, result)
}

func (gt *GeTui) PushBatchByAlias(ctx context.Context, param push.PushBatchByAliasParam) error {
	go gt.checkAndReacquireToken(ctx)
	url := BaseUrl + gt.conf.AppId + "/push/single/batch/alias"

	result := &push.HttpResult{}
	if err := gt.handleHttp(param, result, url, PostMethod, gt.token); err != nil {
		return err
	}

	return gt.handleResult(ctx, result)
}

func (gt *GeTui) CreateMessage(ctx context.Context, param push.CreateMessageParam) (string, error) {
	go gt.checkAndReacquireToken(ctx)
	url := BaseUrl + gt.conf.AppId + "/push/list/message"

	result := &push.CreateMessageResult{}
	if err := gt.handleHttp(param, result, url, PostMethod, gt.token); err != nil {
		return "", err
	}

	if err := gt.handleResult(ctx, result.HttpResult); err != nil {
		return "", err
	}
	return result.Data.TaskId, nil
}

func (gt *GeTui) PushListByCid(ctx context.Context, param push.PushListByCidParam) error {
	go gt.checkAndReacquireToken(ctx)
	url := BaseUrl + gt.conf.AppId + "/push/list/cid"

	result := &push.HttpResult{}
	if err := gt.handleHttp(param, result, url, PostMethod, gt.token); err != nil {
		return err
	}

	return gt.handleResult(ctx, result)
}

func (gt *GeTui) PushListByAlias(ctx context.Context, param push.PushListByAliasParam) error {
	go gt.checkAndReacquireToken(ctx)
	url := BaseUrl + gt.conf.AppId + "/push/list/alias"

	result := &push.HttpResult{}
	if err := gt.handleHttp(param, result, url, PostMethod, gt.token); err != nil {
		return err
	}

	return gt.handleResult(ctx, result)
}

func (gt *GeTui) PushAll(ctx context.Context, param push.PushAllParam) error {
	go gt.checkAndReacquireToken(ctx)
	url := BaseUrl + gt.conf.AppId + "/push/all"

	result := &push.HttpResult{}
	if err := gt.handleHttp(param, result, url, PostMethod, gt.token); err != nil {
		return err
	}

	return gt.handleResult(ctx, result)
}

func (gt *GeTui) PushByTag(ctx context.Context, param push.PushByTagParam) error {
	go gt.checkAndReacquireToken(ctx)
	url := BaseUrl + gt.conf.AppId + "/push/tag"

	result := &push.HttpResult{}
	if err := gt.handleHttp(param, result, url, PostMethod, gt.token); err != nil {
		return err
	}

	return gt.handleResult(ctx, result)
}

func (gt *GeTui) PushByFastCustomTag(ctx context.Context, param push.PushByFastCustomTagParam) error {
	go gt.checkAndReacquireToken(ctx)
	url := BaseUrl + gt.conf.AppId + "/push/fast_custom_tag"

	result := &push.HttpResult{}
	if err := gt.handleHttp(param, result, url, PostMethod, gt.token); err != nil {
		return err
	}

	return gt.handleResult(ctx, result)
}

func (gt *GeTui) StopPush(ctx context.Context, taskId string) error {
	go gt.checkAndReacquireToken(ctx)
	url := BaseUrl + gt.conf.AppId + "/task/" + taskId

	result := &push.HttpResult{}
	if err := gt.handleHttp(nil, result, url, DeleteMethod, gt.token); err != nil {
		return err
	}

	return gt.handleResult(ctx, result)
}

func (gt *GeTui) DeleteScheduleTask(ctx context.Context, taskId string) error {
	go gt.checkAndReacquireToken(ctx)
	url := BaseUrl + gt.conf.AppId + "/task/schedule/" + taskId

	result := &push.HttpResult{}
	if err := gt.handleHttp(nil, result, url, DeleteMethod, gt.token); err != nil {
		return err
	}

	return gt.handleResult(ctx, result)
}

func (gt *GeTui) QueryScheduleTask(ctx context.Context, cid, taskId string) (*push.ScheduleDetail, error) {
	go gt.checkAndReacquireToken(ctx)
	url := BaseUrl + gt.conf.AppId + "/task/detail/" + cid + "/" + taskId

	result := &push.QueryScheduleTaskResult{}
	if err := gt.handleHttp(nil, result, url, GetMethod, gt.token); err != nil {
		return nil, err
	}

	if err := gt.handleResult(ctx, result.HttpResult); err != nil {
		return nil, err
	}

	return result.Data, nil
}
