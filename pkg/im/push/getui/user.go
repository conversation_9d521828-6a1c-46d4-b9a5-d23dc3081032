package getui

import (
	"context"
	"creativematrix.com/beyondreading/pkg/im/push"
)

func (gt *GeTui) BindAlias(ctx context.Context, param push.BindAliasParam) error {
	go gt.checkAndReacquireToken(ctx)
	url := BaseUrl + gt.conf.AppId + "/user/alias"

	result := &push.HttpResult{}
	if err := gt.handleHttp(param, result, url, PostMethod, gt.token); err != nil {
		return err
	}

	return gt.handleResult(ctx, result)
}

func (gt *GeTui) QueryAliasByCid(ctx context.Context, cid string) (string, error) {
	go gt.checkAndReacquireToken(ctx)
	url := BaseUrl + gt.conf.AppId + "/user/alias/cid/" + cid

	result := &push.QueryAliasByCidResult{}
	if err := gt.handleHttp(nil, result, url, GetMethod, gt.token); err != nil {
		return "", err
	}

	if err := gt.handleResult(ctx, result.HttpResult); err != nil {
		return "", err
	}

	return result.Data.Alias, nil
}

func (gt *GeTui) QueryCidByAlias(ctx context.Context, alias string) ([]string, error) {
	go gt.checkAndReacquireToken(ctx)
	url := BaseUrl + gt.conf.AppId + "/user/cid/alias/" + alias

	result := &push.QueryCidByAliasResult{}
	if err := gt.handleHttp(nil, result, url, GetMethod, gt.token); err != nil {
		return []string{}, err
	}

	if err := gt.handleResult(ctx, result.HttpResult); err != nil {
		return []string{}, err
	}

	return result.Data.Cid, nil
}

func (gt *GeTui) BatchUnbindAlias(ctx context.Context, param push.BatchUnbindAliasParam) error {
	go gt.checkAndReacquireToken(ctx)
	url := BaseUrl + gt.conf.AppId + "/user/alias"

	result := &push.HttpResult{}
	if err := gt.handleHttp(param, result, url, DeleteMethod, gt.token); err != nil {
		return err
	}

	if err := gt.handleResult(ctx, result); err != nil {
		return err
	}

	return nil
}

func (gt *GeTui) UnbindAllAlias(ctx context.Context, alias string) error {
	go gt.checkAndReacquireToken(ctx)
	url := BaseUrl + gt.conf.AppId + "/user/alias/" + alias

	result := &push.HttpResult{}
	if err := gt.handleHttp(nil, result, url, DeleteMethod, gt.token); err != nil {
		return err
	}

	return gt.handleResult(ctx, result)
}

func (gt *GeTui) UserBindTags(ctx context.Context, cid string, tags []string) error {
	go gt.checkAndReacquireToken(ctx)
	url := BaseUrl + gt.conf.AppId + "/user/custom_tag/cid/" + cid
	param := &push.MultiTag{
		CustomTag: tags,
	}

	result := &push.HttpResult{}
	if err := gt.handleHttp(param, result, url, PostMethod, gt.token); err != nil {
		return err
	}

	return gt.handleResult(ctx, result)
}

func (gt *GeTui) UsersBindTag(ctx context.Context, tag string, cids []string) error {
	go gt.checkAndReacquireToken(ctx)
	url := BaseUrl + gt.conf.AppId + "/user/custom_tag/batch/" + tag
	param := &push.MultiCid{
		Cid: cids,
	}

	result := &push.HttpResult{}
	if err := gt.handleHttp(param, result, url, PostMethod, gt.token); err != nil {
		return err
	}

	return gt.handleResult(ctx, result)
}

func (gt *GeTui) UnBindUsersTag(ctx context.Context, tag string, cids []string) error {
	go gt.checkAndReacquireToken(ctx)
	url := BaseUrl + gt.conf.AppId + "/user/custom_tag/batch/" + tag
	param := &push.MultiCid{
		Cid: cids,
	}

	result := &push.HttpResult{}
	if err := gt.handleHttp(param, result, url, DeleteMethod, gt.token); err != nil {
		return err
	}

	return gt.handleResult(ctx, result)
}

func (gt *GeTui) QueryUserTags(ctx context.Context, cid string) ([]string, error) {
	go gt.checkAndReacquireToken(ctx)
	url := BaseUrl + gt.conf.AppId + "/user/custom_tag/cid/" + cid

	result := &push.QueryUserTagsResult{}
	if err := gt.handleHttp(nil, result, url, GetMethod, gt.token); err != nil {
		return []string{}, err
	}

	if err := gt.handleResult(ctx, result.HttpResult); err != nil {
		return []string{}, err
	}

	return result.Data[cid], nil
}

func (gt *GeTui) AddBlackList(ctx context.Context, cid ...string) error {
	go gt.checkAndReacquireToken(ctx)

	url := BaseUrl + gt.conf.AppId + "/user/black/cid/" + gt.Join(cid...)

	result := &push.HttpResult{}
	if err := gt.handleHttp(nil, result, url, PostMethod, gt.token); err != nil {
		return err
	}

	return gt.handleResult(ctx, result)
}

func (gt *GeTui) RemoveBlackList(ctx context.Context, cid ...string) error {
	go gt.checkAndReacquireToken(ctx)

	url := BaseUrl + gt.conf.AppId + "/user/black/cid/" + gt.Join(cid...)

	result := &push.HttpResult{}
	if err := gt.handleHttp(nil, result, url, DeleteMethod, gt.token); err != nil {
		return err
	}

	return gt.handleResult(ctx, result)
}

func (gt *GeTui) QueryUserStatus(ctx context.Context, cid ...string) (map[string]*push.UserStatus, error) {
	go gt.checkAndReacquireToken(ctx)
	url := BaseUrl + gt.conf.AppId + "/user/status/" + gt.Join(cid...)

	result := &push.QueryUserStatusResult{}
	if err := gt.handleHttp(nil, result, url, GetMethod, gt.token); err != nil {
		return nil, err
	}

	if err := gt.handleResult(ctx, result.HttpResult); err != nil {
		return nil, err
	}

	return result.Data, nil
}

func (gt *GeTui) QueryDeviceStatus(ctx context.Context, cid ...string) (map[string]*push.DeviceStatus, error) {
	go gt.checkAndReacquireToken(ctx)
	url := BaseUrl + gt.conf.AppId + "/user/deviceStatus/" + gt.Join(cid...)

	result := &push.QueryDeviceStatusResult{}
	if err := gt.handleHttp(nil, result, url, GetMethod, gt.token); err != nil {
		return nil, err
	}

	if err := gt.handleResult(ctx, result.HttpResult); err != nil {
		return nil, err
	}

	return result.Data, nil
}

func (gt *GeTui) QueryUserDetail(ctx context.Context, cid ...string) (*push.UserDetail, error) {
	go gt.checkAndReacquireToken(ctx)
	url := BaseUrl + gt.conf.AppId + "/user/detail/" + gt.Join(cid...)

	result := &push.QueryUserDetailResult{}
	if err := gt.handleHttp(nil, result, url, GetMethod, gt.token); err != nil {
		return nil, err
	}

	if err := gt.handleResult(ctx, result.HttpResult); err != nil {
		return nil, err
	}

	return result.Data, nil
}
