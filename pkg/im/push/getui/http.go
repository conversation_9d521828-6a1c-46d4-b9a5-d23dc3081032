package getui

import (
	"bytes"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"strconv"
	"time"
)

const (
	BaseUrl         = "https://restapi.getui.com/v2/" // 个推开放平台接口前缀(BaseUrl)
	SuccessCode     = 0                               // 成功
	TokenExpireCode = 10001                           // token错误/失效
	PostMethod      = "POST"
	GetMethod       = "GET"
	DeleteMethod    = "DELETE"
)

// Signature 获取加密后的签名
func Signature(appKey string, masterSecret string) (string, string) {
	timestamp := strconv.FormatInt(time.Now().UnixNano()/1000000, 10) //签名开始生成毫秒时间
	original := appKey + timestamp + masterSecret
	hash := sha256.New()
	hash.Write([]byte(original))
	sum := hash.Sum(nil)
	return fmt.Sprintf("%x", sum), timestamp
}

// Request 接口请求
// url：请求的完整url
// method：请求方法，如：POST GET DELETE PUT
func Request(bodyByte []byte, url, method, token string) (string, error) {
	// 创建客户端实例
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	body := bytes.NewBuffer(bodyByte)

	// 创建请求实例
	req, err := http.NewRequest(method, url, body)
	if err != nil {
		return "", err
	}

	req.Header.Add("token", token)
	req.Header.Add("Content-Type", "application/json;charset=utf-8")

	// 发起请求
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}

	defer resp.Body.Close()

	// 读取响应
	result, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	return string(result), nil
}

// MakeReqBody 生成请求参数对应的JSON
func MakeReqBody(param interface{}) ([]byte, error) {
	if param == nil {
		return []byte{}, nil
	}
	body, err := json.Marshal(param)
	if err != nil {
		return nil, err
	}

	return body, nil
}
