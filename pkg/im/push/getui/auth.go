package getui

import (
	"context"
	"creativematrix.com/beyondreading/pkg/im/push"
)

func (gt *GeTui) Auth(ctx context.Context) error {
	// 获取加密字符串和时间戳
	signStr, timestamp := Signature(gt.conf.App<PERSON>ey, gt.conf.MasterSecret)
	param := &push.TokenParam{
		Sign:      signStr,
		Timestamp: timestamp,
		AppKey:    gt.conf.AppKey,
	}

	url := BaseUrl + gt.conf.AppId + "/auth"

	result := &push.TokenResult{}
	if err := gt.handleHttp(param, result, url, PostMethod, ""); err != nil {
		return err
	}

	// 重置token信息
	gt.token = result.Data.Token
	gt.tokenExpire = result.Data.ExpireTime

	return gt.handleResult(ctx, result.HttpResult)
}

func (gt *GeTui) Close(ctx context.Context) error {
	url := BaseUrl + gt.conf.AppId + "/auth/" + gt.token

	result := &push.HttpResult{}
	if err := gt.handleHttp(nil, result, url, DeleteMethod, ""); err != nil {
		return err
	}

	if err := gt.handleResult(ctx, result); err != nil {
		return err
	}

	gt.token = ""
	gt.tokenExpire = ""
	return nil
}
