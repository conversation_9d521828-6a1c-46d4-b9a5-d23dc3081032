package getui

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"creativematrix.com/beyondreading/pkg/im/push"
)

type Config struct {
	AppId        string
	AppKey       string
	AppSecret    string
	MasterSecret string
}

type GeTui struct {
	conf        Config
	token       string
	tokenExpire string
}

func InitGeTui(appId, appKey, appSecret, masterSecret string) (*GeTui, error) {
	gt := &GeTui{conf: Config{
		AppId:        appId,
		AppKey:       appKey,
		AppSecret:    appSecret,
		MasterSecret: masterSecret,
	}}

	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	if err := gt.Auth(ctx); err != nil {
		return gt, err
	}

	return gt, nil
}

// 校验token是否过期，设定token有效期剩1小时重新获取
func (gt *GeTui) checkAndReacquireToken(ctx context.Context) {
	expire, _ := strconv.ParseInt(gt.tokenExpire, 10, 64)
	expireUnix := expire
	ntUnix := time.Now().UnixNano() / 1000
	// token失效 或 仅剩1小时过期时，重新获取token
	if len(gt.token) == 0 || expireUnix-ntUnix <= 3600000 {
		_ = gt.Auth(ctx)
	}
}

func (gt *GeTui) handleResult(ctx context.Context, ret *push.HttpResult) error {
	if ret.Code == SuccessCode {
		return nil
	}
	if ret.Code == TokenExpireCode {
		gt.checkAndReacquireToken(ctx)
	}

	return fmt.Errorf("%d:%s", ret.Code, ret.Msg)
}

func (gt *GeTui) handleHttp(param, result interface{}, url, method, token string) error {
	bodyByte, err := MakeReqBody(param)
	if err != nil {
		return err
	}

	data, err := Request(bodyByte, url, method, token)
	if err != nil {
		return err
	}

	if err = json.Unmarshal([]byte(data), result); err != nil {
		return err
	}

	return nil
}

func (gt *GeTui) Join(ss ...string) string {
	var _ss []string
	_ss = append(_ss, ss...)
	return strings.Join(_ss, ",")
}
