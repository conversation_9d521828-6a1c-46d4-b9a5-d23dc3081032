package push

import (
	"context"
)

type Push interface {
	// Auth 获取token，全局唯一接口调用凭据
	Auth(ctx context.Context) error
	// Close 关闭鉴权token 为防止token被滥用或泄露，可以调用此接口主动使token失效
	Close(ctx context.Context) error

	// PushSingleByCid 根据cid向单个用户推送消息
	PushSingleByCid(ctx context.Context, param PushSingleByCidParam) error
	// PushSingleByAlias 通过别名推送消息
	PushSingleByAlias(ctx context.Context, param PushSingleByAliasParam) error
	// PushBatchByCid 根据cid批量推送，每个cid用户的推送内容都不同的情况下，使用此接口，可提升推送效率
	PushBatchByCid(ctx context.Context, param PushBatchByCidParam) error
	// PushBatchByAlias 根据别名批量推送，在给每个别名用户的推送内容都不同的情况下，可以使用此接口
	PushBatchByAlias(ctx context.Context, param PushBatchByAliasParam) error
	// CreateMessage 创建消息体，并返回taskid，批量推的前置步骤
	CreateMessage(ctx context.Context, param CreateMessageParam) (string, error)
	// PushListByCid 根据cid批量推送，对列表中所有cid进行消息推送。调用此接口前需调用创建消息接口设置消息内容
	PushListByCid(ctx context.Context, param PushListByCidParam) error
	// PushListByAlias 执行别名批量推,对列表中所有别名进行消息推送。调用此接口前需调用创建消息接口设置消息内容
	PushListByAlias(ctx context.Context, param PushListByAliasParam) error
	// PushAll 群推，对所有用户群发推送消息
	PushAll(ctx context.Context, param PushAllParam) error
	// PushByTag 根据条件筛选用户推送，对符合筛选条件的用户群发推送消息
	PushByTag(ctx context.Context, param PushByTagParam) error
	// PushByFastCustomTag 使用标签快速推送
	PushByFastCustomTag(ctx context.Context, param PushByFastCustomTagParam) error
	// StopPush 停止任务，对正处于推送状态，或者未接收的消息停止下发（只支持批量推和群推任务）
	StopPush(ctx context.Context, taskId string) error
	// DeleteScheduleTask 删除定时任务，用来删除还未下发的任务，删除后定时任务不再触发(距离下发还有一分钟的任务，将无法删除，后续可以调用停止任务接口。)
	DeleteScheduleTask(ctx context.Context, taskId string) error
	// QueryScheduleTask 查询消息明细，调用此接口可以查询某任务下某cid的具体实时推送路径情况
	QueryScheduleTask(ctx context.Context, cid, taskId string) (*ScheduleDetail, error)

	// BindAlias 绑定别名，一个cid只能绑定一个别名，若已绑定过别名的cid再次绑定新别名，则前一个别名会自动解绑，并绑定新别名
	BindAlias(ctx context.Context, param BindAliasParam) error
	// QueryAliasByCid 根据cid查询别名
	QueryAliasByCid(ctx context.Context, cid string) (string, error)
	// QueryCidByAlias 根据别名查询cid
	QueryCidByAlias(ctx context.Context, alias string) ([]string, error)
	// BatchUnbindAlias 批量解绑别名
	BatchUnbindAlias(ctx context.Context, param BatchUnbindAliasParam) error
	// UnbindAllAlias 解绑所有与该别名绑定的cid
	UnbindAllAlias(ctx context.Context, alias string) error
	// UserBindTags 一个用户绑定一批标签，此操作为覆盖操作，会删除历史绑定的标签
	// 此接口对单个cid有频控限制，每天只能修改一次，最多设置100个标签；单个标签长度最大为32字符，标签总长度最大为512个字符
	UserBindTags(ctx context.Context, cid string, tags []string) error
	// UsersBindTag 一批用户绑定一个标签，此接口为增量，此接口有频次控制(每分钟最多100次，每天最多10000次)
	UsersBindTag(ctx context.Context, tag string, cids []string) error
	// UnBindUsersTag 一批用户解绑一个标签，此接口有频次控制(每分钟最多100次，每天最多10000次)
	UnBindUsersTag(ctx context.Context, tag string, cids []string) error
	// QueryUserTags 根据cid查询用户标签列表
	QueryUserTags(ctx context.Context, cid string) ([]string, error)
	// AddBlackList 将单个或多个用户加入黑名单，对于黑名单用户在推送过程中会被过滤掉
	AddBlackList(ctx context.Context, cid ...string) error
	// RemoveBlackList 将单个cid或多个cid用户移出黑名单
	RemoveBlackList(ctx context.Context, cid ...string) error
	// QueryUserStatus 查询用户的状态
	QueryUserStatus(ctx context.Context, cid ...string) (map[string]*UserStatus, error)
	// QueryDeviceStatus 查询设备的状态
	QueryDeviceStatus(ctx context.Context, cid ...string) (map[string]*DeviceStatus, error)
	// QueryUserDetail 查询用户的信息
	QueryUserDetail(ctx context.Context, cid ...string) (*UserDetail, error)

	// QueryPushResultByTaskIds 获取推送结果，可查询消息可下发数、下发数，接收数、展示数、点击数等结果。支持单个taskId查询和多个taskId查询。
	// 此接口调用，仅可以查询toList或toApp的推送结果数据；不能查询toSingle的推送结果数据。
	QueryPushResultByTaskIds(ctx context.Context, taskId ...string) (*PushResult, error)
}
