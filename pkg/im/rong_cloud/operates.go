package rong_cloud

import (
	"context"
	"crypto/sha1"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"math/rand"
	"net/http"
	"net/url"
	"strconv"
	"time"

	mtp "creativematrix.com/beyondreading/pkg/http"
	"github.com/rongcloud/server-sdk-go/v3/sdk"
)

var (
	InvalidUserError = fmt.Errorf("invalid userID")
	InvalidParam     = fmt.Errorf("invalid Param")

	KeepLiveQueryUrl = "http://api-cn.ronghub.com/chatroom/keepalive/query.json"
	ChatRoomSendUrl  = "http://api-cn.ronghub.com/message/chatroom/publish.json?"
)

/*
	ahthor:cwl
	date:2021-05-08
	info:融云方法实现
*/

// 私聊
type privateChat struct {
	rc               *sdk.RongCloud
	senderID         string
	targetID         []string
	objectName       string
	pushContent      string
	pushData         string
	count            int
	verifyBlacklist  int
	isPersisted      int
	isIncludeSender  int
	contentAvailable int
	options          []sdk.MsgOption
}

func (p *privateChat) From(userID string) IPrivate {
	p.senderID = userID
	return p
}

func (p *privateChat) To(toUserID string) IPrivate {
	p.targetID = []string{toUserID}
	return p
}

func (p *privateChat) ObjectName(name string) IPrivate {
	p.objectName = name
	return p
}

func (p *privateChat) PushContent(content string) IPrivate {
	p.pushContent = content
	return p
}

func (p *privateChat) PushData(data string) IPrivate {
	p.pushData = data
	return p
}

func (p *privateChat) Count(count int) IPrivate {
	p.count = count
	return p
}

func (p *privateChat) VerifyBlacklist(vb int) IPrivate {
	p.verifyBlacklist = vb
	return p
}

func (p *privateChat) IsPersisted(b int) IPrivate {
	p.isPersisted = b
	return p
}

func (p *privateChat) IsIncludeSender(rcv int) IPrivate {
	p.isIncludeSender = rcv
	return p
}

func (p *privateChat) ContentAvailable(open int) IPrivate {
	p.contentAvailable = open
	return p
}

func (p *privateChat) Options(options ...sdk.MsgOption) IPrivate {
	p.options = options
	return p
}

func (p *privateChat) PrivateSend(ctx context.Context, msg Msg) error {
	if len(p.senderID) == 0 || len(p.targetID) == 0 {
		return InvalidUserError
	}
	return p.rc.PrivateSend(
		p.senderID,
		p.targetID,
		p.objectName,
		msg,
		p.pushContent,
		p.pushData,
		p.count,
		p.verifyBlacklist,
		p.isPersisted,
		p.isIncludeSender,
		p.contentAvailable,
		p.options...,
	)
}

func (p *privateChat) PrivateRecall(ctx context.Context, uID string, sentTime int) error {
	if len(p.senderID) == 0 || len(p.targetID) == 0 {
		return InvalidUserError
	}
	return p.rc.GroupRecall(p.senderID, p.targetID[0], uID, sentTime)
}

// 群聊
type groupChat struct {
	rc *sdk.RongCloud
	//发送人用户 ID 。
	senderID string
	//接收群ID.
	targetID []string
	//消息类型。
	objectName string
	//群定向消群定向消息功能，向群中指定的一个或多个用户发送消息，群中其他用户无法收到该消息，当 targetID 为一个群组时此参数有效。注：如果开通了“单群聊消息云存储”功能，群定向消息不会存储到云端，向群中部分用户发送消息阅读状态回执时可使用此功能。（可选）
	userID []string
	//定义显示的 Push 内容，如果 objectName 为融云内置消息类型时，则发送后用户一定会收到 Push 信息. 如果为自定义消息，则 pushContent 为自定义消息显示的 Push 内容，如果不传则用户不会收到 Push 通知。
	pushContent string
	//针对 iOS 平台为 Push 通知时附加到 payload 中，Android 客户端收到推送消息时对应字段名为 pushData。
	pushData string
	//当前版本有新的自定义消息，而老版本没有该自定义消息时，老版本客户端收到消息后是否进行存储，0 表示为不存储、 1 表示为存储，默认为 1 存储消息。
	isPersisted int
	//是否过滤发送人黑名单列表，0 表示为不过滤、 1 表示为过滤，默认为 0 不过滤
	verifyBlacklist int
	//发送用户自已是否接收消息，0 表示为不接收，1 表示为接收，默认为 0 不接收。
	isIncludeSender int
	//发送消息需要用的其他扩展参数
	options []sdk.MsgOption
	//isMentioned:是否为 @消息，0 表示为普通消息，1 表示为 @消息，默认为 0。当为 1 时 content 参数中必须携带 mentionedInfo @消息的详细内容。为 0 时则不需要携带 mentionedInfo。当指定了 toUserId 时，则 @ 的用户必须为 toUserId 中的用户。
	isMentioned int
	//contentAvailable:针对 iOS 平台，对 SDK 处于后台暂停状态时为静默推送，是 iOS7 之后推出的一种推送方式。 允许应用在收到通知后在后台运行一段代码，且能够马上执行，查看详细。1 表示为开启，0 表示为关闭，默认为 0
	contentAvailable int
}

func (g *groupChat) From(userID string) IGroup {
	g.senderID = userID
	return g
}

func (g *groupChat) GroupID(groupIds []string) IGroup {
	g.targetID = groupIds
	return g
}

func (g *groupChat) ObjectName(name string) IGroup {
	g.objectName = name
	return g
}

func (g *groupChat) ToUser(userIds []string) IGroup {
	g.userID = userIds
	return g
}

func (g *groupChat) PushContent(content string) IGroup {
	g.pushContent = content
	return g
}

func (g *groupChat) PushData(data string) IGroup {
	g.pushData = data
	return g
}

func (g *groupChat) IsPersisted(b int) IGroup {
	g.isPersisted = b
	return g
}

func (g *groupChat) VerifyBlacklist(v int) IGroup {
	g.verifyBlacklist = v
	return g
}

func (g *groupChat) IsIncludeSender(rcv int) IGroup {
	g.isIncludeSender = rcv
	return g
}

func (g *groupChat) Options(options ...sdk.MsgOption) IGroup {
	g.options = options
	return g
}

func (g *groupChat) GroupSend(ctx context.Context, msg Msg) error {
	if len(g.senderID) == 0 {
		return fmt.Errorf("invalid userID")
	}
	return g.rc.GroupSend(
		g.senderID,
		g.targetID,
		g.userID,
		g.objectName,
		msg,
		g.pushContent,
		g.pushData,
		g.isPersisted,
		g.isIncludeSender,
		g.options...,
	)
}

func (g *groupChat) GroupStatusSend(ctx context.Context, msg Msg) error {
	if len(g.senderID) == 0 {
		return fmt.Errorf("invalid userID")
	}
	return g.rc.GroupStatusSend(
		g.senderID,
		g.targetID,
		g.objectName,
		msg,
		g.verifyBlacklist,
		g.isIncludeSender,
		g.options...,
	)
}

func (g *groupChat) IsMentioned(m int) IGroup {
	g.isMentioned = m
	return g
}

func (g *groupChat) ContentAvailable(ca int) IGroup {
	g.contentAvailable = ca
	return g
}

func (g *groupChat) GroupSendMention(ctx context.Context, msg sdk.MentionMsgContent) error {
	if len(g.senderID) == 0 {
		return InvalidUserError
	}
	return g.rc.GroupSendMention(
		g.senderID,
		g.targetID,
		g.objectName,
		msg,
		g.pushContent,
		g.pushData,
		g.isPersisted,
		g.isIncludeSender,
		g.isMentioned,
		g.contentAvailable,
	)
}

// uID:消息的唯一标识，各端 SDK 发送消息成功后会返回 uID。
// sentTime:消息的发送时间，各端 SDK 发送消息成功后会返回 sentTime。
func (g *groupChat) GroupRecall(ctx context.Context, uID string, sentTime int) error {
	if len(g.senderID) == 0 || len(g.targetID) == 0 {
		return InvalidParam
	}
	return g.rc.GroupRecall(g.senderID, g.targetID[0], uID, sentTime)
}

type api struct {
	conf *RcouldConfig
}

func (a *api) ChatRoomKeepAliveGetList() ([]string, error) {
	nonce, timestamp, signature := getSignature(a.conf.AppSecret)

	request, err := http.NewRequest("POST", KeepLiveQueryUrl, nil)
	if err != nil {
		return nil, err
	}

	request.Header.Set("Host", " api-cn.ronghub.com")
	request.Header.Set("App-Key", a.conf.AppKey)
	request.Header.Set("Nonce", nonce)
	request.Header.Set("Timestamp", timestamp)
	request.Header.Set("Signature", signature)
	request.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	response, err := mtp.NewDefaultHttpClient().Do(request)
	if err != nil {
		return nil, err
	}

	defer response.Body.Close()

	body, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return nil, err
	}

	var rsp *sdk.ChatRoomResult

	err = json.Unmarshal(body, &rsp)
	if err != nil {
		return nil, err
	}

	return rsp.ChatRoomIDs, nil
}

func getSignature(appSecret string) (nonce, timestamp, signature string) {
	nonceInt := rand.Int()
	nonce = strconv.Itoa(nonceInt)
	timeInt64 := time.Now().Unix()
	timestamp = strconv.FormatInt(timeInt64, 10)
	h := sha1.New()
	_, _ = io.WriteString(h, appSecret+nonce+timestamp)
	signature = fmt.Sprintf("%x", h.Sum(nil))
	return
}

type ChatRomm struct {
	conf *RcouldConfig

	values url.Values

	isPersisted     int
	isIncludeSender int
}

type Reponse struct {
	Code int `json:"code"`
}

func (c *ChatRomm) ChatRoomSend(fromUserId string, chatRoomIds []string, objectName string, msg Msg) error {
	nonce, timestamp, signature := getSignature(c.conf.AppSecret)
	content, err := msg.ToString()
	if err != nil {
		return err
	}

	c.values.Add("fromUserId", fromUserId)
	if len(chatRoomIds) == 0 {
		return nil
	}

	for _, id := range chatRoomIds {
		c.values.Add("toChatroomId", id)
	}

	c.values.Add("objectName", objectName)
	c.values.Add("content", content)
	c.values.Add("isIncludeSender", fmt.Sprintf("%d", c.isIncludeSender))
	c.values.Add("isPersisted", fmt.Sprintf("%d", c.isPersisted))

	path := fmt.Sprintf("%s%s", ChatRoomSendUrl, c.values.Encode())

	request, err := http.NewRequest("POST", path, nil)
	if err != nil {
		return err
	}

	request.Header.Set("Host", " api-cn.ronghub.com")
	request.Header.Set("App-Key", c.conf.AppKey)
	request.Header.Set("Nonce", nonce)
	request.Header.Set("Timestamp", timestamp)
	request.Header.Set("Signature", signature)
	request.Header.Set("Content-Type", "application/x-www-form-urlencoded")

	response, err := mtp.NewDefaultHttpClient().Do(request)
	if err != nil {
		return err
	}

	defer response.Body.Close()
	r, err := ioutil.ReadAll(response.Body)
	if err != nil {
		panic(err)
	}
	var rsp Reponse
	err = json.Unmarshal(r, &rsp)
	if err != nil {
		return err
	}

	if rsp.Code != 200 {
		return model.RcChatRoomSendError
	}
	return nil
}

// //当前版本有新的自定义消息，而老版本没有该自定义消息时，老版本客户端收到消息后是否进行存储，0 表示为不存储、 1 表示为存储，默认为 1 存储消息。
// isPersisted int
func (c *ChatRomm) IsPersisted(b int) ChatRoomer {
	c.isPersisted = b
	return c
}

// //发送用户自已是否接收消息，0 表示为不接收，1 表示为接收，默认为 0 不接收。
// isIncludeSender int
func (c *ChatRomm) IsIncludeSender(rcv int) ChatRoomer {
	c.isIncludeSender = rcv
	return c
}
