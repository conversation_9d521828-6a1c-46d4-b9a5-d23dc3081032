package rong_cloud

import "time"

const (
	//内容类消息
	TxtMsg       = "RC:TxtMsg"       //文字消息
	VcMsg        = "RC:VcMsg"        //语音消息	旧消息类型
	HQVCMsg      = "RC:HQVCMsg"      //语音消息 新消息类型
	ImgMsg       = "RC:ImgMsg"       //图片消息
	GIFMsg       = "RC:GIFMsg"       //  GIF	图片消息
	ImgTextMsg   = "RC:ImgTextMsg"   // 图文消息
	FileMsg      = "RC:FileMsg"      // 文件消息
	LBSMsg       = "RC:LBSMsg"       // 位置消息
	SightMsg     = "RC:SightMsg"     // 小视频消息
	ReferenceMsg = "RC:ReferenceMsg" // 引用消息
	CombineMsg   = "RC:CombineMsg"   // 合并转发消息

	//命令消息
	CmdMsg        = "RC:CmdMsg"        //命令消息
	ContactNtf    = "RC:ContactNtf"    //联系人 (好友)通知消息
	ProfileNtf    = "RC:ProfileNtf"    //资料通知消息
	InfoNtf       = "RC:InfoNtf"       //提示条通知消息
	GrpNtf        = "RC:GrpNtf"        //群组通知消息
	ChrmKVNotiMsg = "RC:chrmKVNotiMsg" //聊天室属性通知消息

	//状态类消息
	TypSts   = "RC:TypSts"   //对方正在输入状态消息
	ReadNtf  = "RC:ReadNtf"  //单聊已读通知消息
	RRReqMsg = "RC:RRReqMsg" //群已读状态请求消息
	RRRspMsg = "RC:RRRspMsg" //群已读通知消息
	SRSMsg   = "RC:SRSMsg"   //多端已读状态同步消息

	//音视频信令消息
	VCAccept      = "RC:VCAccept"      //实时音视频接受
	VCHangup      = "RC:VCHangup"      //实时音视频挂断
	VCInvite      = "RC:VCInvite"      //实时音视频邀请
	VCModifyMedia = "RC:VCModifyMedia" //实时音视频切换
	VCModifyMem   = "RC:VCModifyMem"   //实时音视频成员变化
	VCRinging     = "RC:VCRinging"     //实时音视频响铃

	ZtWhiteTxtMsg = "ZtWhite:TxtMsg" //白名单
	ZtLowTxtMsg   = "ZtLow:TxtMsg"   //低级别

	ZtCmdMsg = "Zt:CmdMsg"
)

type RcouldConfig struct {
	AppKey    string
	AppSecret string
	TimeOut   time.Duration
}
