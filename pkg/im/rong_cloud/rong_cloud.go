package rong_cloud

import (
	"context"
	"net/url"
	"time"

	"github.com/rongcloud/server-sdk-go/v3/sdk"
)

const PRIVATE_RONGCLOUDURI = "http://api-zhuita.ronghub.com"

type Msg interface {
	ToString() (string, error)
}

type rcloud struct {
	rc   *sdk.RongCloud
	conf *RcouldConfig
}

type IRcloud interface {
	//sdk原始方法
	Origin() *sdk.RongCloud
	//私聊(一对一)
	PrivateChat() IPrivate
	//群聊(一对多)
	GroupChat() IGroup

	API() IAPI

	ChatRoom() ChatRoomer
}

func (r *rcloud) Origin() *sdk.RongCloud {
	return r.rc
}

func (r *rcloud) ChatRoom() ChatRoomer {
	return &ChatRomm{
		conf:   r.conf,
		values: make(url.Values),
	}
}

func (r *rcloud) PrivateChat() IPrivate {
	return &privateChat{rc: r.rc}
}

//私聊
type IPrivate interface {
	//发送者
	From(userID string) IPrivate
	//接受者
	To(toUserID string) IPrivate
	//消息类型
	ObjectName(name string) IPrivate
	//PushContent:定义显示的 Push 内容，如果 objectName 为融云内置消息类型时，则发送后用户一定会收到 Push 信息。如果为自定义消息，则 pushContent 为自定义消息显示的 Push 内容，如果不传则用户不会收到 Push 通知。
	PushContent(content string) IPrivate
	//PushData:针对 iOS 平台为 Push 通知时附加到 payload 中，Android 客户端收到推送消息时对应字段名为 pushData。
	PushData(data string) IPrivate
	//Count:针对 iOS 平台，Push 时用来控制未读消息显示数，只有在 toUserId 为一个用户 Id 的时候有效。
	Count(count int) IPrivate
	//VerifyBlacklist:是否过滤发送人黑名单列表，0 表示为不过滤、 1 表示为过滤，默认为 0 不过滤。
	VerifyBlacklist(vb int) IPrivate
	//IsPersisted:当前版本有新的自定义消息，而老版本没有该自定义消息时，老版本客户端收到消息后是否进行存储，0 表示为不存储、 1 表示为存储，默认为 1 存储消息。
	IsPersisted(s int) IPrivate
	//IsIncludeSender:发送用户自已是否接收消息，0 表示为不接收，1 表示为接收，默认为 0 不接收。
	IsIncludeSender(rcv int) IPrivate
	//ContentAvailable:针对 iOS 平台，对 SDK 处于后台暂停状态时为静默推送，是 iOS7 之后推出的一种推送方式。 允许应用在收到通知后在后台运行一段代码，且能够马上执行，查看详细。1 表示为开启，0 表示为关闭，默认为 0。
	ContentAvailable(open int) IPrivate
	//Options 发送消息需要用的其他扩展参数
	Options(options ...sdk.MsgOption) IPrivate
	//PrivateSend 发送消息
	PrivateSend(ctx context.Context, msg Msg) error

	//param  uID:消息的唯一标识，各端 SDK 发送消息成功后会返回 uID。
	//param  sentTime:消息的发送时间，各端 SDK 发送消息成功后会返回 sentTime。 ex:1543566558208
	PrivateRecall(ctx context.Context, uID string, sentTime int) error
}

func (r *rcloud) GroupChat() IGroup {
	return &groupChat{rc: r.rc}
}

func (r *rcloud) API() IAPI {
	return &api{conf: r.conf}
}

type IAPI interface {
	ChatRoomKeepAliveGetList() ([]string, error)
}

//群聊
type IGroup interface {
	//senderID:发送人用户 ID 。
	From(userID string) IGroup
	//targetID:接收群ID.
	GroupID(groupID []string) IGroup
	//objectName:消息类型。
	ObjectName(name string) IGroup
	//userID:群定向消群定向消息功能，向群中指定的一个或多个用户发送消息，群中其他用户无法收到该消息，当 targetID 为一个群组时此参数有效。注：如果开通了“单群聊消息云存储”功能，群定向消息不会存储到云端，向群中部分用户发送消息阅读状态回执时可使用此功能。（可选）
	ToUser(userIds []string) IGroup
	//pushContent:定义显示的 Push 内容，如果 objectName 为融云内置消息类型时，则发送后用户一定会收到 Push 信息. 如果为自定义消息，则 pushContent 为自定义消息显示的 Push 内容，如果不传则用户不会收到 Push 通知。
	PushContent(content string) IGroup
	//pushData:针对 iOS 平台为 Push 通知时附加到 payload 中，Android 客户端收到推送消息时对应字段名为 pushData。
	PushData(data string) IGroup
	//isPersisted:当前版本有新的自定义消息，而老版本没有该自定义消息时，老版本客户端收到消息后是否进行存储，0 表示为不存储、 1 表示为存储，默认为 1 存储消息。
	IsPersisted(b int) IGroup
	//verifyBlacklist是否过滤发送人黑名单列表，0 表示为不过滤、 1 表示为过滤，默认为 0 不过滤
	VerifyBlacklist(v int) IGroup
	//isIncludeSender:发送用户自已是否接收消息，0 表示为不接收，1 表示为接收，默认为 0 不接收。
	IsIncludeSender(rcv int) IGroup
	//options 发送消息需要用的其他扩展参数
	Options(options ...sdk.MsgOption) IGroup
	//GroupSend 发送群组消息
	GroupSend(ctx context.Context, msg Msg) error
	//GroupStatusSend 发送群组状态消息
	GroupStatusSend(ctx context.Context, msg Msg) error

	//isMentioned:是否为 @消息，0 表示为普通消息，1 表示为 @消息，默认为 0。当为 1 时 content 参数中必须携带 mentionedInfo @消息的详细内容。为 0 时则不需要携带 mentionedInfo。当指定了 toUserId 时，则 @ 的用户必须为 toUserId 中的用户。
	IsMentioned(m int) IGroup

	//contentAvailable:针对 iOS 平台，对 SDK 处于后台暂停状态时为静默推送，是 iOS7 之后推出的一种推送方式。 允许应用在收到通知后在后台运行一段代码，且能够马上执行，查看详细。1 表示为开启，0 表示为关闭，默认为 0
	ContentAvailable(ca int) IGroup
	//发送群组 @ 消息
	GroupSendMention(ctx context.Context, msg sdk.MentionMsgContent) error

	//uID:消息的唯一标识，各端 SDK 发送消息成功后会返回 uID。
	//sentTime:消息的发送时间，各端 SDK 发送消息成功后会返回 sentTime。  ex:1543566558208
	GroupRecall(ctx context.Context, uID string, sentTime int) error
}

type ChatRoomer interface {
	ChatRoomSend(fromUserId string, chatRoomIds []string, objectName string, msg Msg) error
	IsPersisted(b int) ChatRoomer
	IsIncludeSender(rcv int) ChatRoomer
}

func RCloud(conf RcouldConfig) IRcloud {
	if conf.TimeOut == 0 {
		conf.TimeOut = time.Second * 3
	}
	client := sdk.NewRongCloud(conf.AppKey, conf.AppSecret, sdk.WithRongCloudURI(PRIVATE_RONGCLOUDURI), sdk.WithTimeout(conf.TimeOut))
	return &rcloud{
		rc:   client,
		conf: &conf,
	}
}
