package ecode

var (
	BindError         = New(-3, "body bind 错误")
	TOKENERROR        = New(-11, "TOKEN_ERROR")
	TOKENMulti        = New(-12, "账号已在其他设备登录")
	TokenExpiredError = New(-13, "token已过期")
	TokenForbid       = New(-14, "该设备已被关进小黑屋")
	ErrorArgument     = New(-100000, "参数异常")

	ObjectIDInvalid   = New(-201, "无效的 objectId")
	CollectionInvalid = New(-202, "无效的 collection")
	RateLimitInvalid  = New(-203, "刷新频率太高了")
	DatabaseInvalid   = New(-204, "无效的 database")

	RequestErr   = New(-400, "请求错误")
	Unauthorized = New(-401, "未认证")
	AccessDenied = New(-403, "访问权限不足")
	NothingFound = New(-404, "未找到")
	UserLogout   = New(-405, "发送失败，该用户已注销")

	ServerErr       = New(-500, "服务器错误")
	ForbidDeviceErr = New(-15001, "该设备已被关进小黑屋")
	ForbidIPErr     = New(-15002, "服务器暂时飞到外太空去了")

	UnKnownKindType = New(-700, "未知类型")
	UniqueErr       = New(-701, "唯一性索引错误")

	ContentErr = New(-702, "内容异常")
	InvalidMsg = New(-800, "消息不合法")

	AccountNotFound = New(-1000, "账户不存在")

	DefaultRateMsg = New(-10007, "刷新频率太高了")

	InvalidArgument = New(-100002, "无效请求参数")

	CardNumErr        = New(-13036, "身份信息不匹配，请重新输入")
	NeedRealAvatarErr = New(-13037, "请先上传真人头像")
)

// 数美
var (
	SmCodeError   = New(-31001, "无效Code")
	SmReviewError = New(-31002, "可疑内容，建议人工审核")
	SmRejectError = New(-31005, "违规内容，建议直接拦截")
)

var (
	BindingRequired = New(-50400, "必须存在的字段为空")
	HeadFieldError  = New(-50401, "表头异常")
	ParamValueError = New(-50402, "字段数值不合法，无法解析")
)

var (
	AppUpgradeError     = New(-50500, "获取最新版本失败")
	UnKnowVersionError  = New(-50501, "未知版本")
	UnKnowChannelError  = New(-50502, "未知渠道")
	ServerResponseError = New(-50503, "网络连接出现错误，请稍后重试")
	TooManyHystrixError = New(-50504, "操作过于频繁，请稍后再试")
)

var (
	AdminMobileRepeatError   = New(-50700, "手机号已存在")
	AdminCodeIsExistError    = New(-50701, "Code已存在")
	AdminNameIsExistError    = New(-50702, "名称已存在")
	ResourceRouteRepeatError = New(-50703, "路由重复")
	ResourceNotExist         = New(-50704, "资源不存在")
	AdminUserNotExistError   = New(-50705, "用户不存在")
	PasswordWrongError       = New(-50706, "密码错误")
)
