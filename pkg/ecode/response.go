package ecode

import (
	"encoding/json"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/render"
	gcode "google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"creativematrix.com/beyondreading/pkg/config"
	"creativematrix.com/beyondreading/pkg/crypto"
	"creativematrix.com/beyondreading/pkg/logger"
	"creativematrix.com/beyondreading/pkg/utils"
)

var (
	aesKey      string
	webAesKey   string
	pcAesKey    string
	lightSwitch bool
)

func Init(c *config.Base) {
	if c.LightSwitch {
		lightSwitch = c.LightSwitch
	}

	if c.AesSwitch {
		aesKey = c.Aes<PERSON>ey
		webAesKey = c.WebAesKey
		pcAesKey = c.PcAesKey
	}

}

type Response struct {
	Code  int         `json:"code"`
	Msg   string      `json:"msg"`
	Ts    int64       `json:"ts"`
	Data  interface{} `json:"data"`
	Ext   interface{} `json:"ext"`
	Total int64       `json:"total,omitempty"`
	Sign  string      `json:"sign,omitempty"`

	OpenData interface{} `json:"openData,omitempty"`
}

type EncData struct {
	Data interface{} `json:"encData"`
}

type response struct {
	ctx   *gin.Context
	code  int
	msg   string
	ts    int64
	data  interface{}
	ext   interface{}
	total int64
}

func Back(ctx *gin.Context) IResponse {
	return &response{
		ctx: ctx,
		ts:  time.Now().UnixNano() / 1e6,
	}
}

type IResponse interface {
	Echo() IResponse
	Success() IResponse
	Failure(i interface{}) IResponse
	SetData(interface{}) IResponse
	SetExt(interface{}) IResponse
	Code(code int) IResponse
	Message(msg string) IResponse
	SetTotal(count int64) IResponse
}

func (r *response) newResponse() interface{} {
	rsp := &Response{
		Code:  r.code,
		Msg:   r.msg,
		Ts:    r.ts,
		Data:  r.data,
		Ext:   r.ext,
		Total: r.total,
	}

	if lightSwitch {
		rsp.OpenData = r.data
	}

	if webAesKey != "" && r.ctx.GetString("platform") == "web" {
		data := crypto.AesEncrypt(utils.JsonByte(EncData{
			Data: rsp.Data,
		}), []byte(webAesKey))

		rsp.Data = data

	} else if pcAesKey != "" && r.ctx.GetString("platform") == "pc" {
		data := crypto.AesEncrypt(utils.JsonByte(EncData{
			Data: rsp.Data,
		}), []byte(pcAesKey))

		rsp.Data = data

	} else if aesKey != "" {
		data := crypto.AesEncrypt(utils.JsonByte(EncData{
			Data: rsp.Data,
		}), []byte(aesKey))

		rsp.Data = data
	}

	return rsp
}

func (r *response) Echo() IResponse {
	r.code = 200
	r.ctx.Render(http.StatusOK, render.JSON{Data: r.newResponse()})
	return r
}

func (r *response) Success() IResponse {
	r.code = 200
	if r.msg == "" {
		r.msg = "执行成功!"
	}
	r.ctx.Render(http.StatusOK, render.JSON{Data: r.newResponse()})
	return r
}
func (r *response) Message(msg string) IResponse {
	r.msg = msg
	return r
}
func (r *response) Code(code int) IResponse {
	r.code = code
	return r
}
func (r *response) SetTotal(total int64) IResponse {
	r.total = total
	return r
}

func (r *response) Failure(i interface{}) IResponse {
	r.set(i)
	r.ctx.Abort()
	r.ctx.Render(http.StatusOK, render.JSON{Data: r.newResponse()})
	return r
}

func (r *response) set(i interface{}) {
	defer func() {
		logger.LogErrorw("Response error", "err", i)
	}()

	switch err := i.(type) {
	case Error:
		r.code = err.Code()
		r.msg = err.Message()
	case error:
		tErr := ConvertError(err)
		r.code = tErr.Code
		r.msg = tErr.Msg
	default:
		// 针对中间件返回的字符串类型错误，统一返回友好提示
		r.code = ServerResponseError.Code()
		r.msg = ServerResponseError.Message()
	}
}

func (r *response) SetData(data interface{}) IResponse {
	r.data = data
	openDataLogger, _ := json.Marshal(data)
	r.ctx.Set("openDataLogger", string(openDataLogger))
	return r
}

func (r *response) SetExt(ext interface{}) IResponse {
	r.ext = ext
	return r
}

func ConvertError(gErr error) *TransError {
	e := new(TransError)
	statusConvert := status.Convert(gErr)
	switch statusConvert.Code() {
	case gcode.OK:
		e.Code = OK.Code()
	case gcode.Unknown:
		e = ParseTransError(statusConvert.Message())
	case gcode.InvalidArgument:
		e.Code = ErrorArgument.Code()
		e.Msg = ErrorArgument.Message()
	}
	return e
}
