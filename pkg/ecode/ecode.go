package ecode

import (
	"encoding/json"
	"strconv"
)

// Common var
var (
	codes = map[string]map[int]string{}

	serviceName = "summer"
	OK          = New(0, "ok")
	FAILED      = New(-1, "failed")
)

// Error .
type Error interface {
	error
	Success() bool
	Code() int
	Message() string

	GMessasge() string
}

// New code msg
func New(code int, msg string) Error {
	cs, ok := codes[serviceName]
	if ok {
		cs[code] = msg
		codes[serviceName] = cs
	} else {
		codes[serviceName] = map[int]string{
			code: msg,
		}
	}
	return Int(code)
}

type ecode int

func (e ecode) Error() string {
	return strconv.FormatInt(int64(e), 10)
}

func (e ecode) Success() bool {
	return e.Code() == 200
}

func (e ecode) Code() int {
	return int(e)
}
func (e ecode) GMessasge() string {
	return codes[serviceName][int(e)]
}

func (e ecode) Message() string {
	if v, ok := codes[serviceName]; ok {
		if msg, ok1 := v[e.Code()]; ok1 {
			return msg
		}
	}
	return e.Error()
}

// Int parse code int to error.
func Int(i int) Error {
	return ecode(i)
}

// String parse code string to error.
func String(e string) Error {
	if e == "" {
		return OK
	}
	i, err := strconv.Atoi(e)
	if err != nil {
		return FAILED
	}
	return ecode(i)
}

func ErrorMsg(err error) string {
	switch err := err.(type) {
	case Error:
		return err.Message()
	case error:
		return err.Error()
	default:
		return err.Error()
	}
}

type TransError struct {
	Code int
	Msg  string
}

func ParseTransError(errors string) *TransError {
	e := new(TransError)
	pErr := json.Unmarshal([]byte(errors), e)
	if pErr == nil {
		return e
	}

	// 非RPC转换类型error
	code, _ := strconv.Atoi(errors)
	if code < 0 {
		sErr := Int(code)
		e.Code = sErr.Code()
		e.Msg = sErr.Message()
	} else {
		e.Code = ServerResponseError.Code()
		e.Msg = ServerResponseError.Message()
	}
	return e
}
