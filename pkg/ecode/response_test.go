package ecode

import (
	"encoding/json"
	"fmt"
	"testing"

	gcode "google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

func Test_response_set(t *testing.T) {
	type args struct {
		i interface{}
	}

	tests := []struct {
		name string
		args args
		want Error
	}{
		{"", args{nil}, ServerResponseError},
		{"", args{""}, ServerResponseError},
		{"", args{"test error"}, ServerResponseError},
		{"", args{fmt.Errorf("test error")}, ServerResponseError},
		{"", args{status.Error(gcode.Unknown, "test code")}, ServerResponseError},
		{"", args{New(-1, "test error")}, New(-1, "test error")},
		{"", args{status.Error(gcode.Unknown, genTransError())}, New(-1, "test error")},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &response{}
			r.set(tt.args.i)
			if r.code != tt.want.Code() || r.msg != tt.want.Message() {
				t.Errorf("ValidateError.Error() code= %v, msg= %v, want code= %v, msg= %v", r.code, r.msg, tt.want.Code(), tt.want.Message())
			}
		})
	}
}

func genTransError() string {
	transError := &TransError{
		Code: -1,
		Msg:  "test error",
	}
	by, _ := json.Marshal(transError)
	return string(by)
}
