package debug

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/pprof"

	"creativematrix.com/beyondreading/pkg/config"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

type Service interface {
	Ping(context.Context) error
}

func Start(conf config.Base, service Service) {
	dMux := http.NewServeMux()
	dMux.Handle("/metrics", promhttp.Handler())
	dMux.Handle("/healthz", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		res := map[string]string{
			"status": "UP",
		}
		statusCode := http.StatusOK
		if err := service.Ping(r.Context()); err != nil {
			statusCode = http.StatusInternalServerError
			res["status"] = "DOWN"
			res["err"] = err.Error()
		}

		w.Header().Set("Content-Type", "application/json; charset=utf-8")
		w.WriteHeader(statusCode)
		_ = json.NewEncoder(w).Encode(res)
	}))

	dMux.HandleFunc("/debug/pprof/", pprof.Index)
	dMux.HandleFunc("/debug/pprof/cmdline", pprof.Cmdline)
	dMux.HandleFunc("/debug/pprof/profile", pprof.Profile)
	dMux.HandleFunc("/debug/pprof/symbol", pprof.Symbol)
	dMux.HandleFunc("/debug/pprof/trace", pprof.Trace)

	go func() {
		if err := http.ListenAndServe(conf.Port.DEBUG, dMux); err != nil {
			panic(err)
		}
	}()
}
