package tracer

import (
	"fmt"
	"io"

	"creativematrix.com/beyondreading/pkg/config"
	"github.com/uber/jaeger-client-go"
	jaegercfg "github.com/uber/jaeger-client-go/config"
)

var closer io.Closer

func InitTracing(conf config.Base, serviceName string) {
	cfg := jaegercfg.Configuration{
		ServiceName: serviceName,
		Disabled:    conf.Jaeger.Disabled,
		Sampler: &jaegercfg.SamplerConfig{
			Type:  jaeger.SamplerTypeRateLimiting,
			Param: 0.5,
		},
		Reporter: &jaegercfg.ReporterConfig{
			CollectorEndpoint: conf.Jaeger.Collector,
			LogSpans:          false,
		},
	}
	closer, _ = cfg.InitGlobalTracer(serviceName)
}

func Close() {
	err := closer.Close()
	if err != nil {
		fmt.Print(err)
	}
}
