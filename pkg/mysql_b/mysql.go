package mysql_b

import (
	"context"
	"database/sql"
	"errors"
	"sync/atomic"
	"time"

	"github.com/go-sql-driver/mysql"
	"github.com/jmoiron/sqlx"

	"creativematrix.com/beyondreading/pkg/logger"
)

var (
	// ErrStmtNil prepared stmt error
	ErrStmtNil = errors.New("prepare failed and stmt nil")
	// ErrNoRows is returned by Scan when QueryRow doesn't return a row.
	// In such a case, QueryRow returns a placeholder *Row value that defers
	// this error until a Scan.
	ErrNoRows = sql.ErrNoRows
	// ErrTxDone transaction done.
	ErrTxDone = sql.ErrTxDone
)

type Config struct {
	Driver       string        `toml:"driver" json:"driver"`
	DSN          []string      `toml:"dsn" json:"dsn"`
	MaxOpen      int           `toml:"maxOpen" json:"maxOpen"`
	MaxIdle      int           `toml:"maxIdle" json:"maxIdle"`
	MaxIdleTime  time.Duration `toml:"maxIdleTime" json:"maxIdleTime"`
	MaxLifetime  time.Duration `toml:"maxLifetime" json:"maxLifetime"`
	QueryTimeout time.Duration `toml:"queryTimeout" json:"queryTimeout"` // query sql timeout
	ExecTimeout  time.Duration `toml:"execTimeout" json:"execTimeout"`   // execute sql timeout
	TranTimeout  time.Duration `toml:"tranTimeout" json:"tranTimeout"`   // transaction sql timeout
}

// DB database connection
type DB struct {
	conf *Config
	conn *sqlx.DB
}

func NewMySQL(c *Config) (dbs map[string]*DB, err error) {
	dbs = make(map[string]*DB)

	for _, v := range c.DSN {
		dbName, db, errConn := connect(c, v)
		if errConn != nil {
			return nil, errConn
		}
		dbs[dbName] = &DB{
			conf: c,
			conn: db,
		}
	}

	return dbs, nil
}

func connect(c *Config, dsn string) (string, *sqlx.DB, error) {
	cfg, err := mysql.ParseDSN(dsn)
	if err != nil {
		return "", nil, err
	}

	if !cfg.InterpolateParams {
		cfg.InterpolateParams = true
	}
	if cfg.Timeout == 0 {
		cfg.Timeout = 1 * time.Second
	}
	if cfg.ReadTimeout == 0 {
		cfg.ReadTimeout = 1 * time.Second
	}
	if cfg.WriteTimeout == 0 {
		cfg.WriteTimeout = 1 * time.Second
	}

	if c.Driver == "" {
		c.Driver = "mysql"
	}

	db, err := sqlx.Connect(c.Driver, dsn)
	if err != nil {
		return "", nil, err
	}

	db.SetMaxIdleConns(c.MaxIdle)
	db.SetMaxOpenConns(c.MaxOpen)
	db.SetConnMaxLifetime(c.MaxLifetime * time.Second)
	db.SetConnMaxIdleTime(c.MaxIdleTime * time.Second)

	return cfg.DBName, db, nil
}

// Tx transaction.
type Tx struct {
	db     *DB
	tx     *sqlx.Tx
	c      context.Context
	cancel func()
}

// Row row.
type Row struct {
	err error
	*sql.Row
	db     *DB
	query  string
	args   []interface{}
	cancel func()
}

// Rows rows.
type Rows struct {
	*sql.Rows
	cancel func()
}

// Stmt prepared stmt.
type Stmt struct {
	db    *DB
	tx    bool
	query string
	stmt  atomic.Value
}

// Begin begin tx
func (db *DB) Begin(c context.Context) (tx *Tx, err error) {
	c, cancel := context.WithTimeout(c, time.Duration(db.conf.TranTimeout*time.Second))
	rtx, err := db.conn.BeginTxx(c, nil)
	if err != nil {
		logger.LogInfo("BeginTx err:%v", err)
		cancel()
		return
	}
	tx = &Tx{db: db, tx: rtx, c: c, cancel: cancel}
	return
}

// Exec exec
func (db *DB) Exec(c context.Context, query string, args ...interface{}) (res sql.Result, err error) {
	c, cancel := context.WithTimeout(c, time.Duration(db.conf.ExecTimeout*time.Second))
	res, err = db.conn.ExecContext(c, query, args...)
	cancel()
	if err != nil {
		logger.LogInfo("SQL: %s, Args: %v, Exec err: %v,", query, args, err)
	}
	return
}

// Ping for check mysql health
func (db *DB) Ping(c context.Context) (err error) {
	c, cancel := context.WithTimeout(c, time.Duration(db.conf.ExecTimeout*time.Second))
	err = db.conn.PingContext(c)
	cancel()
	if err != nil {
		logger.LogInfo("Ping err:%v", err)
	}
	return
}

// Prepare prepare
func (db *DB) Prepare(query string) (*Stmt, error) {
	stmt, err := db.conn.PrepareContext(context.Background(), query)
	if err != nil {
		logger.LogInfo("Prepare err:%v", err)
		return nil, err
	}
	st := &Stmt{query: query, db: db}
	st.stmt.Store(stmt)
	return st, nil
}

// Prepared Prepared
func (db *DB) Prepared(query string) (stmt *Stmt) {
	stmt = &Stmt{query: query, db: db}
	s, err := db.conn.PrepareContext(context.Background(), query)
	if err == nil {
		stmt.stmt.Store(s)
		return
	}
	go func() {
		for {
			s, err := db.conn.PrepareContext(context.Background(), query)
			if err != nil {
				time.Sleep(time.Second)
				continue
			}
			stmt.stmt.Store(s)
			return
		}
	}()
	return
}

// Query query
func (db *DB) Query(c context.Context, query string, args ...interface{}) (rows *Rows, err error) {
	_, cancel := context.WithTimeout(c, db.conf.QueryTimeout*time.Second)
	rs, err := db.conn.Query(query, args...)
	if err != nil {
		logger.LogInfo("Query err:%v", err)
		cancel()
		return
	}
	rows = &Rows{Rows: rs, cancel: cancel}
	return
}

func (db *DB) Select(dest interface{}, query string, args ...interface{}) (err error) {
	return db.conn.Select(dest, query, args...)
}

func (db *DB) Get(dest interface{}, query string, args ...interface{}) (err error) {
	return db.conn.Get(dest, query, args...)
}

// QueryRow QueryRow
func (db *DB) QueryRow(c context.Context, query string, args ...interface{}) *Row {
	c, cancel := context.WithTimeout(c, time.Duration(db.conf.QueryTimeout*time.Second))
	r := db.conn.QueryRowContext(c, query, args...)
	return &Row{db: db, Row: r, query: query, args: args, cancel: cancel}
}

// Close Close.
func (db *DB) Close() error {
	return db.conn.Close()
}

// Commit commits the transaction.
func (tx *Tx) Commit() (err error) {
	err = tx.tx.Commit()
	tx.cancel()
	if err != nil {
		logger.LogErrorw("Commit err:%v", err)
	}
	return
}

func (tx *Tx) Select(dest interface{}, query string, args ...interface{}) (err error) {
	return tx.db.Select(dest, query, args...)
}

// Rollback aborts the transaction.
func (tx *Tx) Rollback() (err error) {
	err = tx.tx.Rollback()
	tx.cancel()
	if err != nil && err != ErrTxDone {
		logger.LogErrorw("Rollback err:%", err)
	}
	return
}

// Exec executes a query that doesn't return rows. For example: an INSERT and UPDATE.
func (tx *Tx) Exec(query string, args ...interface{}) (res sql.Result, err error) {
	res, err = tx.tx.ExecContext(tx.c, query, args...)
	if err != nil {
		logger.LogErrorw("Exec err", "error", err)
		return
	}
	return
}

// Query executes a query that returns rows, typically a SELECT.
func (tx *Tx) Query(query string, args ...interface{}) (rows *Rows, err error) {
	rs, err := tx.tx.QueryContext(tx.c, query, args...)
	if err == nil {
		rows = &Rows{Rows: rs}
	} else {
		logger.LogErrorw("Query, err:%v", err)
	}
	return
}

// QueryRow executes a query that is expected to return at most one row.
// QueryRow always returns a non-nil value. Errors are deferred until Row's
// Scan method is called.
func (tx *Tx) QueryRow(query string, args ...interface{}) *Row {
	r := tx.tx.QueryRowContext(tx.c, query, args...)
	return &Row{Row: r, db: tx.db, query: query, args: args}
}

// Stmt returns a transaction-specific prepared statement from an existing statement.
func (tx *Tx) Stmt(stmt *Stmt) *Stmt {
	as, ok := stmt.stmt.Load().(*sql.Stmt)
	if !ok {
		return nil
	}
	ts := tx.tx.StmtContext(tx.c, as)
	st := &Stmt{query: stmt.query, tx: true, db: tx.db}
	st.stmt.Store(ts)
	return st
}

// Prepare creates a prepared statement for use within a transaction.
// The returned statement operates within the transaction and can no longer be
// used once the transaction has been committed or rolled back.
// To use an existing prepared statement on this transaction, see Tx.Stmt.
func (tx *Tx) Prepare(query string) (*Stmt, error) {
	stmt, err := tx.tx.Prepare(query)
	if err != nil {
		logger.LogErrorw("Prepare, err:%v", err)
		return nil, err
	}
	st := &Stmt{query: query, tx: true, db: tx.db}
	st.stmt.Store(stmt)
	return st, nil
}

// Scan copies the columns from the matched row into the values pointed at by dest.
func (r *Row) Scan(dest ...interface{}) (err error) {
	if r.err != nil {
		err = r.err
	} else if r.Row == nil {
		err = ErrStmtNil
	}
	if err != nil {
		logger.LogErrorw("Scan err:%v", err)
		return
	}
	err = r.Row.Scan(dest...)
	if r.cancel != nil {
		r.cancel()
	}
	return
}

// Close closes the Rows, preventing further enumeration. If Next is called
// and returns false and there are no further result sets,
// the Rows are closed automatically and it will suffice to check the
// result of Err. Close is idempotent and does not affect the result of Err.
func (rs *Rows) Close() (err error) {
	err = rs.Rows.Close()
	if rs.cancel != nil {
		rs.cancel()
	}
	return
}

// Exec executes a prepared statement with the given arguments and returns a
// Result summarizing the effect of the statement.
func (s *Stmt) Exec(c context.Context, args ...interface{}) (res sql.Result, err error) {
	stmt, ok := s.stmt.Load().(*sql.Stmt)
	if !ok {
		err = ErrStmtNil
		return
	}
	c, cancel := context.WithTimeout(c, time.Duration(s.db.conf.ExecTimeout*time.Second))
	res, err = stmt.ExecContext(c, args...)
	cancel()
	if err != nil {
		logger.LogErrorw("Exec err:%v", err)
	}
	return
}

// Query executes a prepared query statement with the given arguments and
// returns the query results as a *Rows.
func (s *Stmt) Query(c context.Context, args ...interface{}) (rows *Rows, err error) {
	stmt, ok := s.stmt.Load().(*sql.Stmt)
	if !ok {
		err = ErrStmtNil
		return
	}
	c, cancel := context.WithTimeout(c, time.Duration(s.db.conf.QueryTimeout*time.Second))
	rs, err := stmt.QueryContext(c, args...)
	if err != nil {
		cancel()
		return
	}
	rows = &Rows{Rows: rs, cancel: cancel}
	return
}

// QueryRow executes a prepared query statement with the given arguments.
// If an error occurs during the execution of the statement, that error will
// be returned by a call to Scan on the returned *Row, which is always non-nil.
// If the query selects no rows, the *Row's Scan will return ErrNoRows.
// Otherwise, the *Row's Scan scans the first selected row and discards the rest.
func (s *Stmt) QueryRow(c context.Context, args ...interface{}) (row *Row) {
	row = &Row{db: s.db, query: s.query, args: args}
	stmt, ok := s.stmt.Load().(*sql.Stmt)
	if !ok {
		return
	}
	c, cancel := context.WithTimeout(c, time.Duration(s.db.conf.QueryTimeout*time.Second))
	row.Row = stmt.QueryRowContext(c, args...)
	row.cancel = cancel
	return
}

// Close closes the statement.
func (s *Stmt) Close() (err error) {
	stmt, ok := s.stmt.Load().(*sql.Stmt)
	if ok {
		err = stmt.Close()
	}
	return
}
