package mysql_b

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"

	"creativematrix.com/beyondreading/pkg/logger"
)

func TestDB_Exec(t *testing.T) {
	config := &Config{
		Driver:       "mysql",
		DSN:          []string{"root:123456@tcp(127.0.0.1:3306)/kuxiu?interpolateParams=true&readTimeout=3s&timeout=3s&writeTimeout=3s"},
		MaxOpen:      5,
		MaxIdle:      5,
		MaxIdleTime:  5,
		MaxLifetime:  5,
		QueryTimeout: 3,
		ExecTimeout:  3,
		TranTimeout:  3,
	}

	dbs, err := NewMySQL(
		config,
	)
	if err != nil {
		fmt.Println(err)
		return
	}
	userDb := dbs["kuxiu"]
	AddUserInfoSQL := "INSERT INTO user_info(name) VALUES(?)"
	args := []interface{}{
		"KongZi",
	}
	result, err := userDb.Exec(context.Background(), AddUserInfoSQL, args...)
	if err != nil {
		logger.LogInfo("%v", err)
		return
	}

	fmt.Println(result.RowsAffected())
}

// type UserInfo struct {
// 	Id         int64
// 	Name       string
// 	CreateTime string
// 	UpdateTime string
// }

type TGiftConsumeTotal struct {
	Id          int64
	UserId      int64
	TotalAmount float64
	CreateTime  string
	ModifyTime  string
}

func TestDB_Query(t *testing.T) {
	config := &Config{
		Driver:       "mysql",
		DSN:          []string{"root:123456@tcp(172.19.245.138:3306)/kuxiu_gift?interpolateParams=true&readTimeout=3s&timeout=3s&writeTimeout=3s"},
		MaxOpen:      5,
		MaxIdle:      5,
		MaxIdleTime:  5,
		MaxLifetime:  5,
		QueryTimeout: 3,
		ExecTimeout:  3,
		TranTimeout:  3,
	}

	dbs, err := NewMySQL(
		config,
	)
	if err != nil {
		fmt.Println(err)
		return
	}
	userDb := dbs["kuxiu_gift"]

	qsql := "SELECT id,user_id,total_amount,create_time,modify_time FROM t_gift_consume_total ORDER BY create_time ASC LIMIT 1"
	rows, err := userDb.Query(context.Background(), qsql)
	if err != nil {
		fmt.Println(err)
		return
	}
	defer rows.Close()

	userInfos := make([]*TGiftConsumeTotal, 0)
	for rows.Next() {
		ui := &TGiftConsumeTotal{}
		if err = rows.Scan(&ui.Id, &ui.UserId, &ui.TotalAmount, &ui.CreateTime, &ui.ModifyTime); err != nil {
			fmt.Println(err)
			return
		}
		userInfos = append(userInfos, ui)
	}

	uBytes, _ := json.Marshal(userInfos)
	fmt.Println(string(uBytes))
}

func TestDB_Transaction(t *testing.T) {
	config := &Config{
		Driver:       "mysql",
		DSN:          []string{"root:123456@tcp(127.0.0.1:3306)/kuxiu?interpolateParams=true&readTimeout=3s&timeout=3s&writeTimeout=3s"},
		MaxOpen:      5,
		MaxIdle:      5,
		MaxIdleTime:  5,
		MaxLifetime:  5,
		QueryTimeout: 3,
		ExecTimeout:  3,
		TranTimeout:  3,
	}

	dbs, err := NewMySQL(
		config,
	)
	if err != nil {
		fmt.Println(err)
		return
	}
	userDb := dbs["kuxiu"]

	ctx := context.Background()
	tx, err := userDb.Begin(ctx)
	if err != nil {
		fmt.Println(err)
		return
	}

	AddUserInfoSQL := "INSERT INTO user_info(name) VALUES(?)"
	args := []interface{}{
		"James",
	}
	result, err := tx.Exec(AddUserInfoSQL, args...)
	if err != nil {
		tx.Rollback()
	} else {
		tx.Commit()
	}

	fmt.Println(result.LastInsertId())
}
