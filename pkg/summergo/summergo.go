package summergo

import (
	"context"
	"creativematrix.com/beyondreading/pkg/middlewares"
	"sync"
)

func SummerGo(f func()) {
	go do(f)
}

func do(f func()) {
	defer middlewares.RecoveryBiz()
	f()
}

type SummerGroup struct {
	cancel func()

	wg sync.WaitGroup

	errOnce sync.Once
	err     error
}

// WithContext returns a new Group and an associated Context derived from ctx.
//
// The derived Context is canceled the first time a function passed to Go
// returns a non-nil error or the first time Wait returns, whichever occurs
// first.
func WithContext(ctx context.Context) (*SummerGroup, context.Context) {
	ctx, cancel := context.WithCancel(ctx)
	return &SummerGroup{cancel: cancel}, ctx
}

// Wait blocks until all function calls from the Go method have returned, then
// returns the first non-nil error (if any) from them.
func (g *SummerGroup) Wait() error {
	g.wg.Wait()
	if g.cancel != nil {
		g.cancel()
	}
	return g.err
}

// Go calls the given function in a new goroutine.
//
// The first call to return a non-nil error cancels the group; its error will be
// returned by Wait.
func (g *SummerGroup) Go(f func() error) {
	g.wg.Add(1)

	SummerGo(func() {
		defer g.wg.Done()

		if err := f(); err != nil {
			g.errOnce.Do(func() {
				g.err = err
				if g.cancel != nil {
					g.cancel()
				}
			})
		}
	})
}
